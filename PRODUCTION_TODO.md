# MrEngineer – Production Readiness Plan and TODOs

This document consolidates all required tasks to take MrEngineer to a production-ready state. It reflects the current codebase (Backend: Node/Express/Prisma/Postgres; Frontend: Next.js/TypeScript/Tailwind; Realtime: Socket.IO) and prioritizes work based on risk and impact.

Priorities:

- P0: Must fix before production
- P1: High priority, next sprint
- P2: Important, can follow
- P3: Nice-to-have

---

## Priority Rollup (Actionable Summary)

P0 – Must fix before production
- [ ] AppError pattern uniformly used; consistent response shapes via sendError/sendResponse
- [ ] Request validation added to remaining routes (beyond battle/support/RBAC)
- [ ] Supabase auth integration validated end-to-end (authMiddleware flow)
- [ ] Socket auth on handshake token; document event contracts (1.6)

P1 – High priority next
- [ ] Redis cache service: connection, TTL tuning, invalidation correctness (1.7)
- [ ] Pagination/projections across list endpoints (1.7)
- [ ] N+1 query audit and indexes for heavy tables (1.7/1.8)
- [ ] Swagger docs across modules (1.5)
- [ ] Backend unit tests: repositories/services (Prism<PERSON> mocking) (5.1)
- [ ] Frontend unit tests (5.1); Integration tests and E2E flows (5.2)
- [ ] Database migrations on deploy; backup/restore plan (6.3)
- [ ] Monitoring/alerts: Prometheus/Sentry alert pipelines (6.2)

Backlog (P2/P3)
- [ ] Performance/load testing; coverage targets (5.3/5.4)
- [ ] Frontend UX polish, performance, accessibility (3.3/3.4)
- [ ] Data retention/archival policies (7.2)

---

## 1) Backend – Stabilization and Refactoring

### 1.1 Critical Routing and Module Wiring (P0)

- [x] Fix double mounting of battle routes
  - Current: AppRoutes mounts `/battles` and BattleRoutes mounts `/battles` again, resulting in `/battles/battles/*`
  - Action: In `Backend/src/routes/battleRoutes.ts`, remove inner prefix so endpoints resolve under `/api/v1/battles/*`
- [x] Unify route registration to a single composition path (`src/index.ts` vs `src/app.ts/server.ts`)
  - Current: Two server entry styles exist (App class in `index.ts` and `initializeApp` in `app.ts` with Sentry/Socket setup). `package.json` start uses `dist/index.js` which does NOT initialize websockets or Swagger.
  - Action: Choose one entrypoint. Recommended: migrate to `index.ts` and embed Socket/Sentry/Swagger setup there; remove `server.ts` and `app.ts` duplication. Ensure `socketService.initialize(server)` is called only once.

### 1.2 Battle Module Migration Coherence (P0)

- [x] Fix imports and missing dependencies in new battle module
  - `modules/battle/battle.routes.ts` imports `@/middleware/auth.middleware` (file doesn’t exist); should use `@/middlewares/authMiddleware`
  - `modules/battle/controllers/battle.controller.ts` uses `createAppError` but does not import it; imports `sendResponse` but never uses it; verify repo method names (e.g., `createBattle`) exist
- [x] Ensure DTO mapping matches DB schema
  - Controller’s `createBattle` uses fields (`createdBy`, `maxParticipants`, `duration`) that must map to Prisma `user_id`, `max_participants`, `time_per_question`/`length` accordingly.
- [x] Align enums and types (Prisma vs TS)
  - Prisma `Length` uses lowercase values (`short|medium|long`), but TS typings may differ. Normalize mapping layer.

### 1.3 Error Handling and Response Consistency (P0)

- [x] Standardize error handling middleware usage
  - Present both `errorMiddleware.ts` and `errorHandler.ts`. Consolidate to one (prefer the structured one returning typed responses). Update all imports.
- [ ] Ensure `AppError` pattern is used uniformly and return shapes are consistent (`{ status, message, data }`).
- [ ] Add request validation for all routes (express-validator/Joi) with common base validator; already present in battle module – replicate pattern across controllers.

### 1.4 Authentication/Authorization (P0)

- [ ] Validate Supabase integration
  - Confirm `SUPABASE_URL` and `SUPABASE_ANON_KEY` envs; test `authMiddleware` end-to-end.
- [ ] Complete and apply RBAC consistently
  - Verify role/permission checks for Admin/Moderator routes; implement `authorizeRoles`/`authorizePermission` across routers.

### 1.5 Swagger/OpenAPI (P1)

- [x] Fix Swagger bootstrap
  - `app.ts` reads `swagger/index.json` that isn’t checked in; generate OpenAPI and serve via `/api-docs` from the single chosen server entry.
- [ ] Document all endpoints including battle module and core modules (users, roadmaps, challenges, resources, discussions, quiz, placement, leaderboard, etc.).

### 1.6 Socket.IO Integration (P0)

- [x] Ensure WebSocket server starts with the actual production entry
  - Move `socketService.initialize(server)` into the chosen server entrypoint (`index.ts`).
- [x] Auth for sockets
  - `socketService` expects JWT verify via `verifyToken`; confirm frontend sends valid token in `socket.handshake.auth.token`.
- [x] Define and document socket event contracts (battle state, score, chat, timer sync) – keep parity with frontend hooks.

### 1.7 Performance and Caching (P1)

- [ ] Validate Redis connection for `cacheService` and tune TTLs; ensure invalidation is correct (battle list/details invalidations use wildcard delete – adjust to key scanning/prefix delete).
- [ ] Add pagination and projections to all list endpoints (ensure not over-selecting heavy payloads).
- [ ] Audit N+1 queries; prefer batched selects, indexes for heavy tables (participants, answers, leaderboard).

### 1.8 Database Schema/Indexes (P1)

- [ ] Confirm Prisma migrations are applied; review indexes for:
  - `Battle`: `status`, `type`, `user_id`, `topic_id`, `created_at`
  - `BattleParticipant`: `battle_id`, `user_id`, `rank`
  - `BattleAnswer`: `question_id, user_id` (unique) – already present
  - `Challenge`, `Quiz`, `Roadmap`, `Topic` – indexes used in filters
- [ ] Ensure lengths of varchars align with UI constraints
- [ ] Validate cascading rules (onDelete Cascade used appropriately)

### 1.9 Logging, Auditing, and Monitoring (P1)

- [x] Standardize Winston logger usage; include correlation IDs and user IDs.
- [x] Add basic Prometheus metrics (HTTP latencies, throughput, error rates) via `prom-client`.
- [x] Sentry (or alternative) error reporting from unified entrypoint.

---

## 2) Backend – Feature Completion

### 2.1 Battle Zone (P0/P1)

- [x] Complete CRUD: create/update/delete battles with schema-aligned DTO(s)
- [x] Finalize question selection and association flows
  - Ensure `selectQuestionsForBattle` aligns with topic/quiz structures and difficulty mapping
- [x] Implement participant lifecycle
  - join/leave, progress/state updates, answer submission, anti-cheat validations (middleware exists), leaderboard updates, finalization
- [x] Battle statistics endpoint `/battles/statistics` – implement real values using `BattleRepository.getBattleZoneStatistics`
- [x] Leaderboard endpoint `/battles/:id/leaderboard` – wire to `BattleRepository.getBattleLeaderboard`
- [ ] Websocket event emissions during lifecycle (state, timer sync, join/leave, score update, completion)
- [x] Scheduled jobs
  - Validate node-schedule jobs for start/end and notifications; add persistence/recovery upon restarts

### 2.2 Content/Learning Modules (P1)

- [ ] Roadmap Management (see ROADMAP_MANAGEMENT_TODO.md) – ensure parity with schema already present (many models exist)
- [ ] Quizzes and challenges – verify submission lifecycle, metrics, hints, boilerplates, shared solutions
- [ ] Community features: discussions, moderation, votes/flags – ensure permission checks
- [ ] Placement: jobs, company-specific prep endpoints – confirm minimal MVP scope

### 2.3 Admin/Moderator tooling (P1)

- [ ] Admin APIs for approving content, moderations, feature flags, system configs
- [ ] Audit logs, security audit logs – ensure endpoints and UI parity

---

## 3) Frontend – Stabilization and UX

### 3.1 API Integration Consistency (P0)

- [x] Ensure Axios base URL is `/api/v1` and all services use it consistently
- [ ] Fix battle endpoints mapping
  - Example: `useBattleApi` and `battleService` must hit `/api/v1/battles/*`; verify templated routes use `{ replacements }` correctly
- [ ] Ensure pagination parameters match backend (`page, limit, sort_by, sort_order`)

### 3.2 WebSocket Parity (P0)

- [ ] Validate `NEXT_PUBLIC_WS_URL` and Socket path; align event names with backend `SocketEvents`
- [x] Attach auth token to handshake (`socket.io` auth); ensure 401s are handled with re-auth flows
- [ ] Normalize event payload shapes (state updates, score updates, participants)

### 3.3 Battle Zone UX (P1)

- [ ] Complete screens: battle list, details, lobby, live view, leaderboard, statistics
- [ ] Add states for loading/empty/error
- [ ] Add toasts and retry flows
- [ ] Accessibility of interactive components (keyboard focus, aria roles)

### 3.4 Global UI/UX (P2)

- [ ] Responsive layouts – validate key pages on mobile (roadmaps, challenges, battle zone)
- [ ] Performance – code-splitting of large routes, dynamic import heavy components
- [ ] Forms – unify form patterns and validation messages

---

## 4) Security Hardening

### 4.1 API Security (P0)

- [ ] Enforce auth on protected routes; block anonymous writes
- [ ] RBAC permission checks on Admin/Moderator endpoints
- [ ] Rate limiting (global and route-level); ensure `rateLimiterMiddleware` applied to sensitive endpoints
- [ ] Input sanitization (XSS, SQLi) – validate usage of `xss-clean` and validation middlewares

### 4.2 HTTP Security (P1)

- [ ] CORS tightened to known origins
- [ ] Helmet policies tuned for production (CSP/COEP/COOP as suitable)
- [ ] CSRF protection for state-changing requests (if cookies used)

### 4.3 Secrets and Config (P0)

- [ ] Verify `.env` completeness in both Frontend and Backend
- [ ] Rotate keys for production; store secrets in secure vault (Vercel/Infra provider)

---

## 5) Testing Strategy

### 5.1 Unit Tests (P1)

- [ ] Backend: repositories/services (Prisma mocking)
- [x] Backend: utilities
- [ ] Frontend: hooks and components logic (React Testing Library)

### 5.2 Integration and E2E (P1/P2)

- [ ] Backend integration tests for key routes (Auth, Battle join/submit, Leaderboard, Roadmaps)
- [ ] E2E with Cypress/Playwright for critical flows: login -> join battle -> answer -> see leaderboard

### 5.3 Performance/Load (P2)

- [ ] k6/Gatling for high-traffic endpoints (battle join, leaderboard)

### 5.4 Coverage Targets (P2)

- [ ] Aim 70%+ initially, reach 80%+ for critical modules

---

## 6) Observability & Ops

### 6.1 Logging and Metrics (P1)

- [x] Ensure structured logs with request IDs; capture user IDs where applicable
- [x] Prometheus metrics endpoint exposed (/metrics) and HTTP duration histogram
- [ ] Alerts for error rates/latency configured (Prometheus Alertmanager or Sentry alert rules)

### 6.2 Monitoring/Alerts (P1)

- [ ] Sentry/Alerting pipelines for exceptions and downtimes

### 6.3 CI/CD (P1)

- [x] GitHub Actions pipeline: lint, typecheck, test, build
- [ ] Database migrations on deploy; backup/restore plan

---

## 7) Data and Migrations

### 7.1 Migrations (P0)

- [x] Validate Prisma schema in CI
- [ ] Ensure seeders run in CI; check for dangling models, data integrity
- [ ] Battle sample data seeding – ensure ready for demo and testing

### 7.2 Data Retention/Archival (P2)

- [ ] Policies for logs, metrics, completed battle data

---

## 8) Documentation

### 8.1 Developer Docs (P1)

- [ ] Clear local setup guides for Backend/Frontend
- [ ] Architecture doc – modules, data model, and sequence for battle lifecycle

### 8.2 API Docs (P1)

- [ ] Swagger/OpenAPI definitions linked from the repo and deployed API `/api-docs`

### 8.3 User/Admin Docs (P2)

- [ ] Battle management guide (admin), moderator workflows

---

## 9) Acceptance Criteria & Readiness Checklist

- [ ] All P0 issues closed (routing, auth, sockets, errors, core battle flows)
- [ ] Battle Zone MVP flows verified:
  - [ ] Create battle -> select questions -> join -> start -> answer -> leaderboard -> complete
  - [ ] Statistics & leaderboard endpoints functional
  - [ ] Websocket events in sync with UI
- [ ] Admin/Moderator CRUDs secured by RBAC
- [ ] 70%+ unit test coverage on battle services/controllers; smoke E2E for critical flows
- [ ] Swagger docs published and accurate
- [ ] CI pipeline green; deploy to staging with migrations
- [ ] Logging/metrics/alerts configured

---

## 10) Phased Timeline

- Phase 0 (Week 1): P0 fixes (routing, sockets, auth, controller/schema mismatch), Swagger scaffolding
- Phase 1 (Weeks 2-3): Battle flows end-to-end, caching/indexing, logging/metrics, RBAC enforcement
- Phase 2 (Weeks 4-5): Frontend UX polish, accessibility, performance, admin tooling, E2E tests
- Phase 3 (Week 6+): Scaling, optimization, extended features (placement, community expansions)

---

## 11) Notable Code Issues Discovered (Traceback Summary)

- Double route prefix for battles: `/api/v1/battles/battles/*` due to nested mounting
- New battle module imports mismatch (`@/middleware/auth.middleware` vs `@/middlewares/authMiddleware`)
- Missing import of `createAppError` in new battle controller; DTO/schema field mismatches
- Two server boot paths (`index.ts` vs `app.ts/server.ts`) causing confusion; only `index.ts` is used in prod, which currently does not initialize Socket.IO or Swagger
- Swagger file read from non-existent `swagger/index.json`
- Websocket backend exists but not initialized in the active entrypoint; frontend expects Socket.IO at `NEXT_PUBLIC_WS_URL`
- Some repositories/controllers contain TODO placeholders for battle statistics/leaderboard endpoints (placeholders to be replaced with real handlers)

---

This document supersedes and complements existing TODOs. Cross-reference with `TODO.md` and `ROADMAP_MANAGEMENT_TODO.md` for module-specific details.
