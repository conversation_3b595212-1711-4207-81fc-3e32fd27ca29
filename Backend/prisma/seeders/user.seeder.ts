import { createClient } from '@supabase/supabase-js';

import { PrismaClient, RoleType } from '@prisma/client';

const prisma = new PrismaClient();

// Supabase client initialization
const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

const defaultUsers = [
  {
    email: '<EMAIL>',
    username: 'admin',
    password: 'Admin@123',
    full_name: 'Admin User',
    role: 'ADMIN',
    is_verified: true,
  },
  {
    email: '<EMAIL>',
    username: 'moderator',
    password: 'Mod@123',
    full_name: 'Moderator User',
    role: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    is_verified: true,
  },
  {
    email: '<EMAIL>',
    username: 'shailesh',
    password: '<PERSON><PERSON><PERSON>@123',
    full_name: '<PERSON><PERSON><PERSON>',
    role: 'ADMIN',
    is_verified: true,
  },
  // Additional sample users
  {
    email: '<EMAIL>',
    username: 'joh<PERSON><PERSON>',
    password: 'Password@123',
    full_name: '<PERSON> <PERSON>e',
    role: 'USER',
    is_verified: true,
  },
  {
    email: '<EMAIL>',
    username: 'janedoe',
    password: 'Password@123',
    full_name: 'Jane Doe',
    role: 'USER',
    is_verified: true,
  },
  {
    email: '<EMAIL>',
    username: 'alexsmith',
    password: 'Password@123',
    full_name: 'Alex Smith',
    role: 'USER',
    is_verified: true,
  },
  {
    email: '<EMAIL>',
    username: 'sarahjohnson',
    password: 'Password@123',
    full_name: 'Sarah Johnson',
    role: 'USER',
    is_verified: true,
  },
  {
    email: '<EMAIL>',
    username: 'michaelbrown',
    password: 'Password@123',
    full_name: 'Michael Brown',
    role: 'USER',
    is_verified: true,
  },
];

async function createUserInSupabase(userData: (typeof defaultUsers)[0]) {
  try {
    // First check if user already exists in Supabase
    const { data: existingUsers, error: fetchError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userData.email);

    if (!fetchError && existingUsers && existingUsers.length > 0) {
      console.log(`⚠️ User ${userData.email} already exists in Supabase`);
      return { id: existingUsers[0].id };
    }

    // Create new user if doesn't exist
    const { data: supabaseUser, error } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      user_metadata: {
        full_name: userData.full_name,
        username: userData.username,
      },
      app_metadata: {
        roles: [userData.role], // Ensure the role is an array as per Supabase expectations
      },
    });

    if (error) {
      // Check if it's the specific error for email already exists
      if (error.code === 'email_exists') {
        console.log(`⚠️ User ${userData.email} already exists in Supabase`);
        // Try to get the user ID
        const { data: existingUser } = await supabase
          .from('users')
          .select('id')
          .eq('email', userData.email)
          .single();

        if (existingUser) {
          return { id: existingUser.id };
        } else {
          // If we can't get the ID, generate a placeholder
          return {
            id: `existing-${Math.random().toString(36).substring(2, 15)}`,
          };
        }
      } else {
        throw error;
      }
    }

    return supabaseUser.user;
  } catch (error) {
    // Only log the error but don't throw it if it's an email_exists error
    if (error && (error as any).code === 'email_exists') {
      console.log(`⚠️ User ${userData.email} already exists in Supabase`);
      // Generate a placeholder ID
      return { id: `existing-${Math.random().toString(36).substring(2, 15)}` };
    }

    console.error(
      `Failed to create Supabase user for ${userData.email}:`,
      error,
    );
    throw error;
  }
}

async function createUserInDatabase(
  userData: (typeof defaultUsers)[0],
  supabaseId: string,
) {
  try {
    // Find the role by type instead of name, using the new RoleType enum
    const roleType = userData.role as RoleType;
    const role = await prisma.role.findFirst({
      where: { type: roleType },
    });

    if (!role) {
      throw new Error(
        `Role ${userData.role} not found. Please run role seeder first.`,
      );
    }

    // Create or update the user
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {
        supabase_id: supabaseId,
        username: userData.username,
        full_name: userData.full_name,
        is_verified: userData.is_verified,
      },
      create: {
        email: userData.email,
        supabase_id: supabaseId,
        username: userData.username,
        full_name: userData.full_name,
        is_verified: userData.is_verified,
      },
    });

    // Check if user already has this role
    const existingUserRole = await prisma.userRole.findFirst({
      where: {
        user_id: user.id,
        role_id: role.id,
      },
    });

    // If user doesn't have this role, assign it
    if (!existingUserRole) {
      await prisma.userRole.create({
        data: {
          user_id: user.id,
          role_id: role.id,
        },
      });
      console.log(
        `✅ Assigned ${userData.role} role to user: ${userData.email}`,
      );
    }

    return user;
  } catch (error) {
    console.error(
      `Failed to create database user for ${userData.email}:`,
      error,
    );
    throw error;
  }
}

export async function seedUsers() {
  try {
    console.log('\n==================================================');
    console.log('🌱 USER SEEDER STARTED');
    console.log('==================================================');

    let createdCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    // Check if Supabase credentials are available
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error(
        '❌ Supabase credentials not found in environment variables',
      );
      console.log('⚠️ Creating users in database only (without Supabase auth)');

      // Create users directly in the database without Supabase
      for (const userData of defaultUsers) {
        try {
          // Get role
          const role = await prisma.role.findUnique({
            where: { name: userData.role },
          });

          if (!role) {
            console.error(
              `❌ Role ${userData.role} not found. Please run the role seeder first.`,
            );
            continue;
          }

          // Create or update user in database
          const dbUser = await prisma.user.upsert({
            where: { email: userData.email },
            update: {
              username: userData.username,
              full_name: userData.full_name,
              is_verified: userData.is_verified,
              role_id: role.id,
            },
            create: {
              email: userData.email,
              username: userData.username,
              full_name: userData.full_name,
              is_verified: userData.is_verified,
              role_id: role.id,
              supabase_id: `local-${Math.random().toString(36).substring(2, 15)}`, // Generate a fake ID
            },
          });

          // Create user streak if it doesn't exist
          try {
            await prisma.userStreak.upsert({
              where: { user_id: dbUser.id },
              update: {},
              create: {
                user_id: dbUser.id,
                current_streak: Math.floor(Math.random() * 20),
                longest_streak: Math.floor(Math.random() * 30) + 20,
                last_activity_date: new Date(),
              },
            });
          } catch (error) {
            console.log('⚠️ Error creating user streak:', error);
          }

          console.log(`✅ Created/updated user: ${userData.email}`);
          createdCount++;
        } catch (error) {
          console.error(`❌ Error creating user ${userData.email}:`, error);
          errorCount++;
        }
      }
    } else {
      // Create users with Supabase auth
      for (const userData of defaultUsers) {
        try {
          // Check if user already exists in database
          const existingUser = await prisma.user.findUnique({
            where: { email: userData.email },
          });

          // Check if user already exists in Supabase
          const { data: existingSupabaseUser, error: supabaseError } =
            await supabase
              .from('users')
              .select('id')
              .eq('email', userData.email)
              .single();

          let supabaseUser;

          if (existingUser && existingSupabaseUser) {
            console.log(
              `⚠️ User ${userData.email} already exists in both database and Supabase`,
            );
            supabaseUser = existingSupabaseUser;
            updatedCount++;
          } else if (supabaseError || !existingSupabaseUser) {
            // Create user in Supabase
            supabaseUser = await createUserInSupabase(userData);
            console.log(`✅ Created Supabase user: ${userData.email}`);
          } else {
            console.log(`⚠️ User ${userData.email} already exists in Supabase`);
            supabaseUser = existingSupabaseUser;
            updatedCount++;
          }

          // Create or update user in database
          const dbUser = await createUserInDatabase(userData, supabaseUser.id);

          // Create user streak if it doesn't exist
          try {
            await prisma.userStreak.upsert({
              where: { user_id: dbUser.id },
              update: {},
              create: {
                user_id: dbUser.id,
                current_streak: Math.floor(Math.random() * 20),
                longest_streak: Math.floor(Math.random() * 30) + 20,
                last_activity_date: new Date(),
              },
            });
          } catch (error) {
            console.log('⚠️ Error creating user streak:', error);
          }

          console.log(
            `✅ Successfully created/updated user: ${userData.email}`,
          );
          createdCount++;
        } catch (error) {
          console.error(`❌ Error creating user ${userData.email}:`, error);
          errorCount++;
        }
      }
    }

    console.log('\n✅ USER SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created/updated ${createdCount} users`);
    console.log(`⚠️ Skipped ${updatedCount} users (already exist)`);
    console.log(`❌ Encountered ${errorCount} errors`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding users:', error);
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedUsers()
    .then(() => {
      console.log('User seeder completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error running user seeder:', error);
      process.exit(1);
    });
}
