import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

import { quizzes } from '../../resources/quizzes';

const prisma = new PrismaClient();

async function seedQuizzes() {
  console.log('\n==================================================');
  console.log('🌱 QUIZ SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    const topics = await prisma.topic.findMany();

    if (topics.length === 0) {
      console.error('❌ No topics found. Please run the topic seeder first.');
      return;
    }

    console.log(`ℹ️  Found ${topics.length} topics for quizzes`);
    console.log(`ℹ️  Processing ${quizzes.length} quizzes`);

    for (const quiz of quizzes) {
      try {
        const existingQuiz = await prisma.quiz.findFirst({
          where: { title: quiz.title },
        });

        if (existingQuiz) {
          await prisma.quiz.update({
            where: { id: existingQuiz.id },
            data: { ...quiz, updated_at: new Date() },
          });
          updatedCount++;
          console.log(`🔄 Updated quiz: ${quiz.title}`);
        } else {
          await prisma.quiz.create({
            data: {
              id: uuidv4(),
              ...quiz,
              created_at: new Date(),
              updated_at: new Date(),
            },
          });
          createdCount++;
          console.log(`✅ Created quiz: ${quiz.title}`);
        }
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to process quiz ${quiz.title}:`, error);
      }
    }

    console.log('\n✅ QUIZ SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding quizzes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  seedQuizzes().catch((error) => {
    console.error('Failed to seed quizzes:', error);
    process.exit(1);
  });
}

export { seedQuizzes };
