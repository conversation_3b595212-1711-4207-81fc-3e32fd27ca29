import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

import { topics } from '../../resources/topics';

const prisma = new PrismaClient();

const seedTopics = async () => {
  try {
    console.log('\n==================================================');
    console.log('🌱 TOPIC SEEDER STARTED');
    console.log('==================================================');

    let createdCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;

    for (const topicData of topics) {
      try {
        // Find subject by title instead of name
        const subject = await prisma.subject.findFirst({
          where: { title: topicData.subject },
        });

        if (subject) {
          // Check if topic exists
          const existingTopic = await prisma.topic.findFirst({
            where: { title: topicData.name },
          });

          if (existingTopic) {
            // Update existing topic
            await prisma.topic.update({
              where: { id: existingTopic.id },
              data: {
                description: topicData.description,
              },
            });

            updatedCount++;
            console.log(`✅ Updated topic: ${topicData.name}`);
          } else {
            // Create new topic
            const newTopic = await prisma.topic.create({
              data: {
                id: uuidv4(),
                title: topicData.name,
                description: topicData.description,
                order: topicData.order || 0,
              },
            });

            // Link topic to subject
            await prisma.subjectTopic.create({
              data: {
                id: uuidv4(),
                subject_id: subject.id,
                topic_id: newTopic.id,
                order: 0, // Default order
              },
            });

            createdCount++;
            console.log(`✅ Created topic: ${topicData.name}`);
          }
        } else {
          console.error(
            `Subject "${topicData.subject}" not found for topic "${topicData.name}"`,
          );
          skippedCount++;
        }
      } catch (error) {
        console.error(`❌ Error processing topic "${topicData.name}":`, error);
        skippedCount++;
      }
    }

    console.log('\n✅ TOPIC SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created ${createdCount} new topics`);
    console.log(`✅ Updated ${updatedCount} existing topics`);
    console.log(
      `⚠️ Skipped ${skippedCount} topics (subject not found or error)`,
    );
    console.log('==================================================');
  } catch (error) {
    console.error('Error seeding topics:', error);
  } finally {
    await prisma.$disconnect();
  }
};

seedTopics();
