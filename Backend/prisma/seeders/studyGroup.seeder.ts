/**
 * @file studyGroup.seeder.ts
 * @description Seeder for creating study groups with members, resources, and meeting schedules
 */
import { v4 as uuidv4 } from 'uuid';

import { PrismaClient, StudyGroupMemberRole } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds the database with study groups
 */
export async function seedStudyGroups() {
  console.log('🌱 Seeding study groups...');

  try {
    // Check if study groups already exist
    const existingGroups = await prisma.studyGroup.count();

    if (existingGroups > 0) {
      console.log(
        `⚠️ ${existingGroups} study groups already exist. Skipping study group seeding.`,
      );
      return;
    }

    // Get users to add to study groups
    const users = await prisma.user.findMany({
      take: 50, // Limit to 50 users to avoid creating too much data
    });

    if (users.length === 0) {
      console.error('❌ No users found. Please run the user seeder first.');
      return;
    }

    // We need at least 10 users to create meaningful study groups
    if (users.length < 10) {
      console.error(
        '❌ Not enough users found. Need at least 10 users to create study groups.',
      );
      return;
    }

    // Get subjects to associate with study groups
    const subjects = await prisma.subject.findMany();

    if (subjects.length === 0) {
      console.error(
        '❌ No subjects found. Please run the subject seeder first.',
      );
      return;
    }

    console.log(
      `✅ Found ${users.length} users and ${subjects.length} subjects for creating study groups`,
    );

    /**
     * Generate a random date within a range
     * @param start The start date
     * @param end The end date
     * @returns A random date between start and end
     */
    const randomDate = (start: Date, end: Date): Date => {
      return new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime()),
      );
    };

    /**
     * Generate a study group name based on a subject
     * @param subject The subject to generate a name for
     * @returns A study group name
     */
    const generateGroupName = (subject: any): string => {
      const prefixes = [
        'The',
        'Elite',
        'Advanced',
        'Master',
        'Pro',
        'Expert',
        'Ultimate',
        'Core',
        'Global',
        'Dedicated',
      ];

      const suffixes = [
        'Study Circle',
        'Learning Group',
        'Study Squad',
        'Knowledge Hub',
        'Learning Community',
        'Study Team',
        'Learners Alliance',
        'Study Collective',
        'Learning Network',
        'Study Crew',
      ];

      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
      const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];

      return `${prefix} ${subject.name} ${suffix}`;
    };

    /**
     * Generate a study group description based on a subject
     * @param subject The subject to generate a description for
     * @returns A study group description
     */
    const generateGroupDescription = (subject: any): string => {
      const descriptions = [
        `A collaborative study group focused on mastering ${subject.name}. We meet regularly to discuss concepts, solve problems, and share resources.`,
        `Join our ${subject.name} study group to accelerate your learning through peer collaboration. We welcome learners of all levels who are passionate about this subject.`,
        `This study group is dedicated to exploring advanced topics in ${subject.name}. We work together on projects, share insights, and help each other overcome challenges.`,
        `A supportive community for anyone interested in ${subject.name}. We organize regular study sessions, share learning materials, and provide feedback on each other's work.`,
        `Our ${subject.name} study group combines structured learning with collaborative problem-solving. We focus on practical applications and real-world examples.`,
      ];

      return descriptions[Math.floor(Math.random() * descriptions.length)];
    };

    /**
     * Generate a meeting schedule description
     * @returns A meeting schedule description
     */
    const generateMeetingSchedule = (): string => {
      const days = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
      ];
      const frequencies = ['weekly', 'bi-weekly', 'monthly'];
      const times = ['morning', 'afternoon', 'evening'];

      const day = days[Math.floor(Math.random() * days.length)];
      const frequency =
        frequencies[Math.floor(Math.random() * frequencies.length)];
      const time = times[Math.floor(Math.random() * times.length)];

      return `We meet ${frequency} on ${day} ${time}s.`;
    };

    /**
     * Generate a resource title
     * @param subject The subject to generate a resource title for
     * @returns A resource title
     */
    const generateResourceTitle = (subject: any): string => {
      const titles = [
        `Essential ${subject.name} Cheat Sheet`,
        `${subject.name} Quick Reference Guide`,
        `Comprehensive ${subject.name} Tutorial`,
        `${subject.name} Practice Problems`,
        `Advanced ${subject.name} Concepts`,
        `${subject.name} Study Notes`,
        `Beginner's Guide to ${subject.name}`,
        `${subject.name} Project Ideas`,
        `${subject.name} Learning Roadmap`,
        `${subject.name} Best Practices`,
      ];

      return titles[Math.floor(Math.random() * titles.length)];
    };

    /**
     * Generate a resource URL
     * @param title The resource title
     * @returns A resource URL
     */
    const generateResourceUrl = (title: string): string => {
      // Convert title to URL-friendly format
      const urlTitle = title.toLowerCase().replace(/[^a-z0-9]+/g, '-');

      const domains = [
        'github.com',
        'notion.so',
        'docs.google.com',
        'medium.com',
        'dev.to',
        'hackernoon.com',
        'freecodecamp.org',
        'youtube.com',
        'coursera.org',
        'udemy.com',
      ];

      const domain = domains[Math.floor(Math.random() * domains.length)];

      return `https://${domain}/${urlTitle}`;
    };

    // Create study groups
    console.log('🌱 Creating study groups...');

    // Calculate date ranges
    const now = new Date();
    const sixMonthsAgo = new Date(now);
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    // Determine how many study groups to create (5-10)
    const numGroups = Math.floor(Math.random() * 6) + 5;

    // Randomly select subjects for study groups
    const shuffledSubjects = [...subjects].sort(() => 0.5 - Math.random());
    const selectedSubjects = shuffledSubjects.slice(0, numGroups);

    let groupCount = 0;
    let memberCount = 0;
    let resourceCount = 0;
    let meetingCount = 0;

    // Create study groups
    for (const subject of selectedSubjects) {
      try {
        // Generate group name and description
        const name = generateGroupName(subject);
        const description = generateGroupDescription(subject);

        // Generate a random creation date (within the last 6 months)
        const creationDate = randomDate(sixMonthsAgo, now);

        // Create the study group
        const studyGroup = await prisma.studyGroup.create({
          data: {
            id: uuidv4(),
            name,
            description,
            subject_id: subject.id,
            created_at: creationDate,
            updated_at: creationDate,
          },
        });

        groupCount++;

        // Determine how many members to add to this group (5-15)
        const numMembers = Math.floor(Math.random() * 11) + 5;

        // Randomly select users for this group
        const shuffledUsers = [...users].sort(() => 0.5 - Math.random());
        const selectedUsers = shuffledUsers.slice(0, numMembers);

        // First user is the admin
        const admin = selectedUsers[0];

        // Add members to the group
        for (let i = 0; i < selectedUsers.length; i++) {
          try {
            const user = selectedUsers[i];

            // Determine the role for this member
            // First user is admin, 20% are moderators, rest are members
            let role: StudyGroupMemberRole;
            if (i === 0) {
              role = StudyGroupMemberRole.ADMIN;
            } else if (i < Math.ceil(selectedUsers.length * 0.2)) {
              role = StudyGroupMemberRole.MODERATOR;
            } else {
              role = StudyGroupMemberRole.MEMBER;
            }

            // Generate a random join date (after group creation, before now)
            const joinDate = randomDate(creationDate, now);

            // Create the group member
            await prisma.studyGroupMember.create({
              data: {
                id: uuidv4(),
                study_group_id: studyGroup.id,
                user_id: user.id,
                role,
                joined_at: joinDate,
                created_at: joinDate,
                updated_at: joinDate,
              },
            });

            memberCount++;
          } catch (error) {
            console.error(
              `❌ Error adding member to study group "${name}":`,
              error,
            );
          }
        }

        // Add resources to the group
        // Determine how many resources to add (2-5)
        const numResources = Math.floor(Math.random() * 4) + 2;

        for (let i = 0; i < numResources; i++) {
          try {
            // Generate a resource title and URL
            const title = generateResourceTitle(subject);
            const url = generateResourceUrl(title);

            // Generate a random date for the resource (after group creation, before now)
            const resourceDate = randomDate(creationDate, now);

            // Create the resource
            await prisma.studyGroupResource.create({
              data: {
                id: uuidv4(),
                study_group_id: studyGroup.id,
                title,
                url,
                added_by_id: admin.id,
                created_at: resourceDate,
                updated_at: resourceDate,
              },
            });

            resourceCount++;
          } catch (error) {
            console.error(
              `❌ Error adding resource to study group "${name}":`,
              error,
            );
          }
        }

        // Add meeting schedules to the group
        // Determine how many meeting schedules to add (1-3)
        const numMeetings = Math.floor(Math.random() * 3) + 1;

        for (let i = 0; i < numMeetings; i++) {
          try {
            // Generate a meeting schedule
            const schedule = generateMeetingSchedule();

            // Generate a random date for the meeting schedule (after group creation, before now)
            const scheduleDate = randomDate(creationDate, now);

            // Create the meeting schedule
            await prisma.studyGroupMeetingSchedule.create({
              data: {
                id: uuidv4(),
                study_group_id: studyGroup.id,
                schedule,
                created_by_id: admin.id,
                created_at: scheduleDate,
                updated_at: scheduleDate,
              },
            });

            meetingCount++;
          } catch (error) {
            console.error(
              `❌ Error adding meeting schedule to study group "${name}":`,
              error,
            );
          }
        }
      } catch (error) {
        console.error(
          `❌ Error creating study group for subject "${subject.name}":`,
          error,
        );
      }
    }

    console.log(
      `✅ Successfully created ${groupCount} study groups with ${memberCount} members, ${resourceCount} resources, and ${meetingCount} meeting schedules`,
    );
  } catch (error) {
    console.error('❌ Error seeding study groups:', error);
  }
}
