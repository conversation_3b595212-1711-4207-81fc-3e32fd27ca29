/**
 * @file mentorship.seeder.ts
 * @description Seeder for creating mentor-mentee relationships and mentorship sessions
 */
import { v4 as uuidv4 } from 'uuid';

import { MentorshipStatus, PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds the database with mentorships and sessions
 */
export async function seedMentorships() {
  console.log('🌱 Seeding mentorships...');

  try {
    // Check if mentorships already exist
    const existingMentorships = await prisma.mentorship.count();

    if (existingMentorships > 0) {
      console.log(
        `⚠️ ${existingMentorships} mentorships already exist. Skipping mentorship seeding.`,
      );
      return;
    }

    // Get users to create mentorships for
    const users = await prisma.user.findMany();

    if (users.length === 0) {
      console.error('❌ No users found. Please run the user seeder first.');
      return;
    }

    // We need at least 10 users to create meaningful mentorships
    if (users.length < 10) {
      console.error(
        '❌ Not enough users found. Need at least 10 users to create mentorships.',
      );
      return;
    }

    console.log(`✅ Found ${users.length} users for creating mentorships`);

    /**
     * Generate a random date within a range
     * @param start The start date
     * @param end The end date
     * @returns A random date between start and end
     */
    const randomDate = (start: Date, end: Date): Date => {
      return new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime()),
      );
    };

    /**
     * Generate a mentorship topic
     * @returns A random mentorship topic
     */
    const generateMentorshipTopic = (): string => {
      const topics = [
        'Web Development',
        'Mobile App Development',
        'Data Science',
        'Machine Learning',
        'Cloud Computing',
        'DevOps',
        'Cybersecurity',
        'Blockchain',
        'Game Development',
        'UI/UX Design',
        'Frontend Development',
        'Backend Development',
        'Full Stack Development',
        'Database Management',
        'Network Administration',
        'Software Architecture',
        'Artificial Intelligence',
        'IoT Development',
        'Embedded Systems',
        'Career Guidance',
      ];

      return topics[Math.floor(Math.random() * topics.length)];
    };

    /**
     * Generate a mentorship goal
     * @param topic The mentorship topic
     * @returns A random mentorship goal
     */
    const generateMentorshipGoal = (topic: string): string => {
      const goals = [
        `Improve ${topic} skills and knowledge`,
        `Build a portfolio project in ${topic}`,
        `Prepare for interviews in ${topic}`,
        `Learn best practices in ${topic}`,
        `Transition to a career in ${topic}`,
        `Stay updated with the latest trends in ${topic}`,
        `Overcome challenges in ${topic} projects`,
        `Develop expertise in specific ${topic} technologies`,
        `Understand industry standards for ${topic}`,
        `Gain practical experience in ${topic}`,
      ];

      return goals[Math.floor(Math.random() * goals.length)];
    };

    /**
     * Generate a session note
     * @param sessionNumber The session number
     * @param topic The mentorship topic
     * @returns A random session note
     */
    const generateSessionNote = (
      sessionNumber: number,
      topic: string,
    ): string => {
      const introNotes = [
        `Initial meeting to discuss goals and expectations for the ${topic} mentorship.`,
        `First session focused on understanding the mentee's background and experience in ${topic}.`,
        `Introductory session to establish a roadmap for the mentorship journey in ${topic}.`,
      ];

      const middleNotes = [
        `Reviewed progress on the ${topic} project. Discussed challenges and potential solutions.`,
        `Deep dive into advanced ${topic} concepts. Provided resources for further learning.`,
        `Worked through specific ${topic} problems together. Mentee showing good progress.`,
        `Discussed industry best practices for ${topic}. Shared real-world examples and case studies.`,
        `Focused on practical applications of ${topic}. Assigned exercises for next session.`,
      ];

      const finalNotes = [
        `Wrap-up session reviewing overall progress in ${topic}. Discussed next steps for continued growth.`,
        `Final mentorship session. Celebrated achievements and set long-term goals for ${topic}.`,
        `Concluding session with reflection on the mentorship journey and growth in ${topic} skills.`,
      ];

      if (sessionNumber === 1) {
        return introNotes[Math.floor(Math.random() * introNotes.length)];
      } else if (sessionNumber < 5) {
        return middleNotes[Math.floor(Math.random() * middleNotes.length)];
      } else {
        return finalNotes[Math.floor(Math.random() * finalNotes.length)];
      }
    };

    // Create mentorships
    console.log('🌱 Creating mentorships...');

    // Divide users into potential mentors and mentees
    // We'll use the first 30% of users as potential mentors
    const mentorCount = Math.floor(users.length * 0.3);
    const potentialMentors = users.slice(0, mentorCount);
    const potentialMentees = users.slice(mentorCount);

    // Calculate date ranges
    const now = new Date();
    const sixMonthsAgo = new Date(now);
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    let mentorshipCount = 0;
    let sessionCount = 0;

    // Create mentorships (each mentor can have multiple mentees)
    for (const mentor of potentialMentors) {
      try {
        // Determine how many mentees this mentor will have (1-3)
        const numMentees = Math.floor(Math.random() * 3) + 1;

        // Randomly select mentees for this mentor
        const shuffledMentees = [...potentialMentees].sort(
          () => 0.5 - Math.random(),
        );
        const selectedMentees = shuffledMentees.slice(0, numMentees);

        for (const mentee of selectedMentees) {
          try {
            // Generate a random topic and goal
            const topic = generateMentorshipTopic();
            const goal = generateMentorshipGoal(topic);

            // Generate a random start date (within the last 6 months)
            const startDate = randomDate(sixMonthsAgo, now);

            // Determine the status of this mentorship
            // 60% active, 20% completed, 20% pending
            let status: MentorshipStatus;
            let endDate: Date | null = null;

            const random = Math.random();
            if (random < 0.6) {
              status = MentorshipStatus.ACTIVE;
            } else if (random < 0.8) {
              status = MentorshipStatus.COMPLETED;
              // End date should be after start date and before now
              endDate = randomDate(
                new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000),
                now,
              ); // At least 30 days after start
            } else {
              status = MentorshipStatus.PENDING;
            }

            // Create the mentorship
            const mentorship = await prisma.mentorship.create({
              data: {
                id: uuidv4(),
                mentor_id: mentor.id,
                mentee_id: mentee.id,
                topic,
                goal,
                status,
                start_date: startDate,
                end_date: endDate,
                created_at: startDate,
                updated_at: now,
              },
            });

            mentorshipCount++;

            // Create sessions for active and completed mentorships
            if (
              status === MentorshipStatus.ACTIVE ||
              status === MentorshipStatus.COMPLETED
            ) {
              // Determine how many sessions to create
              // Completed mentorships have 5-8 sessions, active have 1-4
              const numSessions =
                status === MentorshipStatus.COMPLETED
                  ? Math.floor(Math.random() * 4) + 5 // 5-8 sessions
                  : Math.floor(Math.random() * 4) + 1; // 1-4 sessions

              for (let i = 1; i <= numSessions; i++) {
                try {
                  // Session date should be after start date
                  // For completed mentorships, all sessions should be before end date
                  // For active mentorships, all sessions should be before now
                  const sessionEndDate =
                    status === MentorshipStatus.COMPLETED ? endDate! : now;

                  // Calculate a date range for this session based on its position in the sequence
                  const sessionStartDate = new Date(startDate);
                  sessionStartDate.setDate(startDate.getDate() + (i - 1) * 7); // Roughly one session per week

                  const sessionEndDateAdjusted = new Date(sessionEndDate);
                  if (
                    i === numSessions &&
                    status === MentorshipStatus.COMPLETED
                  ) {
                    // Last session of completed mentorship should be close to the end date
                    sessionEndDateAdjusted.setDate(
                      sessionEndDate.getDate() - 3,
                    );
                  }

                  // Ensure the session date range is valid
                  const validSessionEndDate =
                    sessionStartDate < sessionEndDateAdjusted
                      ? sessionEndDateAdjusted
                      : new Date(
                          sessionStartDate.getTime() + 24 * 60 * 60 * 1000,
                        ); // At least 1 day after start

                  const sessionDate = randomDate(
                    sessionStartDate,
                    validSessionEndDate,
                  );

                  // Generate a note for this session
                  const note = generateSessionNote(i, topic);

                  // Create the session
                  await prisma.mentorshipSession.create({
                    data: {
                      id: uuidv4(),
                      mentorship_id: mentorship.id,
                      date: sessionDate,
                      duration_minutes: Math.floor(Math.random() * 31) + 30, // 30-60 minutes
                      notes: note,
                      created_at: sessionDate,
                      updated_at: sessionDate,
                    },
                  });

                  sessionCount++;
                } catch (error) {
                  console.error(
                    `❌ Error creating session for mentorship between ${mentor.email} and ${mentee.email}:`,
                    error,
                  );
                }
              }
            }
          } catch (error) {
            console.error(
              `❌ Error creating mentorship between ${mentor.email} and ${mentee.email}:`,
              error,
            );
          }
        }
      } catch (error) {
        console.error(`❌ Error processing mentor ${mentor.email}:`, error);
      }
    }

    console.log(
      `✅ Successfully created ${mentorshipCount} mentorships with ${sessionCount} sessions`,
    );
  } catch (error) {
    console.error('❌ Error seeding mentorships:', error);
  }
}
