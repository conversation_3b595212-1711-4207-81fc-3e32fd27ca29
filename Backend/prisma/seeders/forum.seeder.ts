/**
 * @file forum.seeder.ts
 * @description Seeder for creating forum categories, threads, and replies
 */
import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds the database with forum data
 */
export async function seedForums() {
  console.log('🌱 Seeding forums...');

  try {
    // Check if forum categories already exist
    const existingCategories = await prisma.forumCategory.count();

    if (existingCategories > 0) {
      console.log(
        `⚠️ ${existingCategories} forum categories already exist. Skipping forum seeding.`,
      );
      return;
    }

    // Get users to create forum content for
    const users = await prisma.user.findMany({
      take: 30, // Limit to 30 users to avoid creating too much data
    });

    if (users.length === 0) {
      console.error('❌ No users found. Please run the user seeder first.');
      return;
    }

    console.log(`✅ Found ${users.length} users for creating forum content`);

    /**
     * Generate a random date within a range
     * @param start The start date
     * @param end The end date
     * @returns A random date between start and end
     */
    const randomDate = (start: Date, end: Date): Date => {
      return new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime()),
      );
    };

    /**
     * Forum category data
     */
    const forumCategories = [
      {
        name: 'General Discussion',
        description:
          'General discussions about programming, technology, and career development.',
        subcategories: [
          {
            name: 'Introductions',
            description: 'Introduce yourself to the community.',
          },
          {
            name: 'Career Advice',
            description:
              'Discussions about career paths, job hunting, and professional development.',
          },
          {
            name: 'Tech News',
            description:
              'Share and discuss the latest technology news and trends.',
          },
        ],
      },
      {
        name: 'Programming Languages',
        description: 'Discussions about various programming languages.',
        subcategories: [
          {
            name: 'JavaScript',
            description:
              'Discussions about JavaScript, frameworks, and libraries.',
          },
          {
            name: 'Python',
            description: 'Discussions about Python, frameworks, and libraries.',
          },
          {
            name: 'Java',
            description: 'Discussions about Java, frameworks, and libraries.',
          },
          {
            name: 'C/C++',
            description: 'Discussions about C and C++ programming.',
          },
          {
            name: 'Other Languages',
            description: 'Discussions about other programming languages.',
          },
        ],
      },
      {
        name: 'Web Development',
        description:
          'Discussions about web development technologies and practices.',
        subcategories: [
          {
            name: 'Frontend',
            description:
              'Discussions about frontend technologies, frameworks, and best practices.',
          },
          {
            name: 'Backend',
            description:
              'Discussions about backend technologies, frameworks, and best practices.',
          },
          {
            name: 'Full Stack',
            description: 'Discussions about full stack development.',
          },
          {
            name: 'Web Design',
            description:
              'Discussions about web design, UI/UX, and accessibility.',
          },
        ],
      },
      {
        name: 'Mobile Development',
        description: 'Discussions about mobile app development.',
        subcategories: [
          {
            name: 'Android',
            description: 'Discussions about Android development.',
          },
          {
            name: 'iOS',
            description: 'Discussions about iOS development.',
          },
          {
            name: 'Cross-Platform',
            description:
              'Discussions about cross-platform mobile development frameworks.',
          },
        ],
      },
      {
        name: 'Data Science & AI',
        description:
          'Discussions about data science, machine learning, and artificial intelligence.',
        subcategories: [
          {
            name: 'Machine Learning',
            description:
              'Discussions about machine learning algorithms and applications.',
          },
          {
            name: 'Data Analysis',
            description:
              'Discussions about data analysis techniques and tools.',
          },
          {
            name: 'AI Ethics',
            description:
              'Discussions about ethical considerations in AI development.',
          },
        ],
      },
      {
        name: 'DevOps & Cloud',
        description:
          'Discussions about DevOps practices and cloud technologies.',
        subcategories: [
          {
            name: 'CI/CD',
            description:
              'Discussions about continuous integration and continuous deployment.',
          },
          {
            name: 'Cloud Platforms',
            description:
              'Discussions about AWS, Azure, GCP, and other cloud platforms.',
          },
          {
            name: 'Containerization',
            description:
              'Discussions about Docker, Kubernetes, and other containerization technologies.',
          },
        ],
      },
      {
        name: 'Platform Help',
        description: 'Get help with using the platform.',
        subcategories: [
          {
            name: 'Bug Reports',
            description: 'Report bugs and issues with the platform.',
          },
          {
            name: 'Feature Requests',
            description: 'Suggest new features for the platform.',
          },
          {
            name: 'General Help',
            description: 'Get help with using the platform.',
          },
        ],
      },
    ];

    /**
     * Generate a thread title based on a subcategory
     * @param subcategory The subcategory to generate a title for
     * @returns A thread title
     */
    const generateThreadTitle = (subcategory: any): string => {
      const generalTitles = [
        `Question about ${subcategory.name}`,
        `Need help with ${subcategory.name}`,
        `Discussion: ${subcategory.name} best practices`,
        `${subcategory.name} resources for beginners`,
        `Advanced ${subcategory.name} techniques`,
        `${subcategory.name} vs alternatives`,
        `How to get started with ${subcategory.name}`,
        `${subcategory.name} career opportunities`,
        `Latest trends in ${subcategory.name}`,
        `${subcategory.name} project ideas`,
      ];

      // Add some specific titles based on subcategory name
      let specificTitles: string[] = [];

      if (subcategory.name === 'JavaScript') {
        specificTitles = [
          'Understanding closures in JavaScript',
          'React vs Vue vs Angular: Which one to choose?',
          'How to optimize JavaScript performance',
          'ES6 features every developer should know',
          'Async/await vs Promises in JavaScript',
        ];
      } else if (subcategory.name === 'Python') {
        specificTitles = [
          'Python for data science: Getting started',
          'Django vs Flask: Which framework to use?',
          'Python decorators explained',
          'Best practices for Python virtual environments',
          'How to use Python generators effectively',
        ];
      } else if (subcategory.name === 'Frontend') {
        specificTitles = [
          'CSS Grid vs Flexbox: When to use each',
          'Responsive design best practices',
          'State management in modern frontend frameworks',
          'Web accessibility guidelines for developers',
          'How to optimize website loading speed',
        ];
      } else if (subcategory.name === 'Backend') {
        specificTitles = [
          'RESTful API design principles',
          'Microservices vs Monolithic architecture',
          'Database selection for different use cases',
          'Authentication and authorization strategies',
          'Handling high traffic on your backend',
        ];
      } else if (subcategory.name === 'Machine Learning') {
        specificTitles = [
          'Getting started with TensorFlow',
          'Supervised vs Unsupervised learning',
          'Feature engineering techniques',
          'Neural networks explained simply',
          'Ethical considerations in ML model development',
        ];
      }

      // Combine general and specific titles
      const allTitles = [...generalTitles, ...specificTitles];

      return allTitles[Math.floor(Math.random() * allTitles.length)];
    };

    /**
     * Generate thread content
     * @returns Thread content
     */
    const generateThreadContent = (): string => {
      const shortContents = [
        "I'm new to this topic and would appreciate any guidance or resources to help me get started. Thanks in advance!",

        "Has anyone here worked with this technology professionally? I'd love to hear about your experiences and any advice you might have.",

        "I'm trying to decide between several options for my next project. What are the pros and cons of each approach?",

        "I've been struggling with this problem for days. Here's what I've tried so far... Any suggestions would be greatly appreciated.",

        'What are the best resources for learning this topic? Books, courses, tutorials, or anything else that helped you.',
      ];

      const mediumContents = [
        "I've been working on a project that involves this technology, and I've run into some challenges. Specifically, I'm trying to implement a feature that allows users to [feature description]. My current approach is [approach description], but I'm running into [problem description]. Has anyone dealt with something similar? Any suggestions on how to overcome this?\n\nThanks for your help!",

        "I'm comparing different approaches for my upcoming project. Option A offers [advantages of A], but has [disadvantages of A]. Option B, on the other hand, provides [advantages of B], though it comes with [disadvantages of B]. Based on your experience, which would you recommend for a project that prioritizes [project priorities]? Are there other alternatives I should consider?\n\nI appreciate any insights you can share!",

        "I'm relatively new to this field and I'm trying to create a learning roadmap for myself. So far, I've identified these key areas to focus on: [list of areas]. Is this a comprehensive list? Are there important topics I'm missing? And what order would you recommend tackling these in?\n\nAlso, if you have recommendations for specific resources (books, courses, tutorials) for any of these topics, I'd be very grateful!",

        "I've noticed an interesting pattern in my work with this technology. When [situation description], I've found that [observation]. This seems counterintuitive to me, as I would expect [expected behavior]. Is this a known phenomenon? Does anyone have an explanation for why this happens?\n\nI'm curious to hear if others have observed the same thing and what your thoughts are on this.",

        "I'm working on optimizing the performance of my application. Currently, it [current performance description]. I've already tried [attempted optimizations], which resulted in [results of attempts]. I'm wondering if there are other approaches I haven't considered yet.\n\nHas anyone successfully optimized a similar system? What strategies worked best for you?",
      ];

      const longContents = [
        "I've been working in this field for several years now, and I wanted to share some insights I've gained that might help others, especially those just starting out.\n\nWhen I first began, I made several mistakes that I now see are common among newcomers. First, [mistake description and lesson learned]. Second, [another mistake and lesson]. These experiences taught me that [key insight].\n\nOver time, I've developed a methodology that has served me well: [detailed methodology description]. This approach has helped me tackle complex problems by breaking them down into [process description].\n\nOne particularly challenging project taught me the importance of [important principle]. We were tasked with [project description], and initially struggled with [challenges faced]. By applying [solution approach], we were able to [positive outcome].\n\nFor those looking to advance in this area, I recommend focusing on these key skills:\n1. [Skill 1] - This is fundamental because [explanation]\n2. [Skill 2] - Often overlooked, but crucial for [reason]\n3. [Skill 3] - This will set you apart because [explanation]\n\nI'm curious to hear about others' experiences and whether these insights resonate with you. What lessons have you learned that you wish you'd known earlier?",

        "I've been researching different approaches to solving [complex problem] and wanted to share my findings and get feedback from the community.\n\nThe problem, as many of you know, involves [detailed problem description]. The conventional wisdom suggests [traditional approach], but this has limitations including [limitations list].\n\nI've explored several alternative approaches:\n\nApproach 1: [Detailed description]\nPros: [List of advantages]\nCons: [List of disadvantages]\nPerformance characteristics: [Performance details]\n\nApproach 2: [Detailed description]\nPros: [List of advantages]\nCons: [List of disadvantages]\nPerformance characteristics: [Performance details]\n\nApproach 3: [Detailed description]\nPros: [List of advantages]\nCons: [List of disadvantages]\nPerformance characteristics: [Performance details]\n\nBased on my testing, Approach 2 seems most promising for my specific use case because [reasoning]. However, I'm concerned about [specific concern] and wondering if anyone has experience addressing this issue.\n\nHas anyone implemented any of these approaches at scale? Are there other approaches I've overlooked? I'd appreciate any insights or experiences you can share.",

        "I've been developing a framework to address [specific need] in [technology area], and I'd like to share my progress and get feedback from the community.\n\nThe motivation behind this project came from my experience with [problem scenario]. Existing solutions like [existing solution 1] and [existing solution 2] didn't quite meet my needs because [limitations of existing solutions].\n\nMy framework, which I'm calling [framework name], takes a different approach by [key differentiating factor]. The core architecture consists of:\n\n1. [Component 1] - Responsible for [functionality]\n2. [Component 2] - Handles [functionality]\n3. [Component 3] - Manages [functionality]\n\nThese components interact through [interaction mechanism], which provides [benefits of this approach].\n\nI've implemented a prototype and tested it on [test scenarios]. The results have been promising, showing [positive results]. However, I've also encountered challenges with [challenges faced].\n\nI'm particularly interested in feedback on:\n- The overall architecture - does it make sense? Are there obvious improvements?\n- The approach to [specific aspect] - is there a better way to handle this?\n- Potential use cases I might not have considered\n\nThe code is available at [repository link] if anyone wants to take a closer look. I welcome contributions and suggestions for improvement.\n\nThank you for your time and any insights you can provide!",
      ];

      // Determine the length of the content (short: 60%, medium: 30%, long: 10%)
      const random = Math.random();
      if (random < 0.6) {
        return shortContents[Math.floor(Math.random() * shortContents.length)];
      } else if (random < 0.9) {
        return mediumContents[
          Math.floor(Math.random() * mediumContents.length)
        ];
      } else {
        return longContents[Math.floor(Math.random() * longContents.length)];
      }
    };

    /**
     * Generate a reply to a thread
     * @returns A reply content
     */
    const generateReplyContent = (): string => {
      const shortReplies = [
        "Great question! I've been wondering about this too.",
        "Thanks for sharing this. It's very helpful.",
        'I agree with your approach. It worked well for me too.',
        'Have you tried using [alternative approach]? It might help with your issue.',
        "I encountered a similar problem. Here's how I solved it...",
        "Interesting perspective. I hadn't thought about it that way.",
        'Could you provide more details about your implementation?',
        'This is exactly what I needed. Thank you!',
        "I'd recommend checking out [resource]. It covers this topic in depth.",
        "Based on my experience, I'd suggest focusing on [specific aspect] first.",
      ];

      const mediumReplies = [
        "I've worked with this technology for a few years now, and I've found that the approach you're considering has both strengths and limitations. The main advantages are [advantages], which make it great for [use cases]. However, be aware of [limitations], especially if you're working with [specific scenario].\n\nIn your situation, I'd probably go with [recommendation] because [reasoning]. Hope this helps!",

        "Thanks for bringing up this topic! I've been experimenting with different solutions for this problem, and I've found that a combination of [approach 1] and [approach 2] works best. The key is to [key insight].\n\nHere's a quick example of how I implemented it:\n[brief example]\n\nLet me know if you have any questions about this approach.",

        "I recently completed a project that faced similar challenges. What worked for us was [solution approach]. The main benefits were [benefits], though we did have to deal with [challenges].\n\nOne thing I wish we'd known from the start is [insight]. It would have saved us a lot of time and effort.\n\nFeel free to reach out if you want more specific details about our implementation.",

        'This is a common issue that many developers face. From my experience, there are a few ways to address it:\n\n1. [Option 1] - Best for [scenario], but has [limitation]\n2. [Option 2] - More flexible, though requires [requirement]\n3. [Option 3] - My personal favorite for [reason]\n\nConsider your specific requirements before choosing an approach. What works best often depends on factors like [factors to consider].',

        "I've been researching this topic extensively for my current project. The most up-to-date resources I've found are:\n\n- [Resource 1]: Great for [aspect]\n- [Resource 2]: Covers [aspect] in depth\n- [Resource 3]: Practical examples of [aspect]\n\nThe landscape is changing quickly, but these should give you a solid foundation. Hope this helps with your learning journey!",
      ];

      // Determine the length of the reply (short: 70%, medium: 30%)
      const random = Math.random();
      if (random < 0.7) {
        return shortReplies[Math.floor(Math.random() * shortReplies.length)];
      } else {
        return mediumReplies[Math.floor(Math.random() * mediumReplies.length)];
      }
    };

    /**
     * Generate a random number of replies for a thread
     * @returns A number between 0 and 10
     */
    const generateReplyCount = (): number => {
      // 20% chance of no replies, 30% chance of 1-2 replies, 30% chance of 3-5 replies, 20% chance of 6-10 replies
      const random = Math.random();
      if (random < 0.2) {
        return 0;
      } else if (random < 0.5) {
        return Math.floor(Math.random() * 2) + 1; // 1-2 replies
      } else if (random < 0.8) {
        return Math.floor(Math.random() * 3) + 3; // 3-5 replies
      } else {
        return Math.floor(Math.random() * 5) + 6; // 6-10 replies
      }
    };

    // Create forum categories and subcategories
    console.log('🌱 Creating forum categories and subcategories...');

    // Calculate date ranges
    const now = new Date();
    const sixMonthsAgo = new Date(now);
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    let categoryCount = 0;
    let subcategoryCount = 0;
    let threadCount = 0;
    let replyCount = 0;

    // Create categories and subcategories
    for (const category of forumCategories) {
      try {
        // Create the category
        const createdCategory = await prisma.forumCategory.create({
          data: {
            id: uuidv4(),
            name: category.name,
            description: category.description,
            created_at: now,
            updated_at: now,
          },
        });

        categoryCount++;

        // Create subcategories for this category
        for (const subcategory of category.subcategories) {
          try {
            // Create the subcategory
            const createdSubcategory = await prisma.forumSubcategory.create({
              data: {
                id: uuidv4(),
                category_id: createdCategory.id,
                name: subcategory.name,
                description: subcategory.description,
                created_at: now,
                updated_at: now,
              },
            });

            subcategoryCount++;

            // Create threads for this subcategory
            // Determine how many threads to create (3-8)
            const numThreads = Math.floor(Math.random() * 6) + 3;

            for (let i = 0; i < numThreads; i++) {
              try {
                // Randomly select a user to create the thread
                const userIndex = Math.floor(Math.random() * users.length);
                const user = users[userIndex];

                // Generate thread title and content
                const title = generateThreadTitle(subcategory);
                const content = generateThreadContent();

                // Generate a random date for the thread
                const threadDate = randomDate(sixMonthsAgo, now);

                // Create the thread
                const thread = await prisma.forumThread.create({
                  data: {
                    id: uuidv4(),
                    subcategory_id: createdSubcategory.id,
                    user_id: user.id,
                    title,
                    content,
                    created_at: threadDate,
                    updated_at: threadDate,
                  },
                });

                threadCount++;

                // Determine if this thread should have replies
                const numReplies = generateReplyCount();

                // Create replies for this thread
                if (numReplies > 0) {
                  // Replies should be after the thread date
                  const replyStartDate = new Date(threadDate);
                  replyStartDate.setHours(replyStartDate.getHours() + 1); // At least 1 hour after thread

                  for (let j = 0; j < numReplies; j++) {
                    try {
                      // Randomly select a user to create the reply (different from the thread creator)
                      let replyUserIndex;
                      do {
                        replyUserIndex = Math.floor(
                          Math.random() * users.length,
                        );
                      } while (users[replyUserIndex].id === user.id);

                      const replyUser = users[replyUserIndex];

                      // Generate reply content
                      const replyContent = generateReplyContent();

                      // Generate a random date for the reply (after thread, before now)
                      const replyDate = randomDate(replyStartDate, now);

                      // Create the reply
                      await prisma.forumReply.create({
                        data: {
                          id: uuidv4(),
                          thread_id: thread.id,
                          user_id: replyUser.id,
                          content: replyContent,
                          created_at: replyDate,
                          updated_at: replyDate,
                        },
                      });

                      replyCount++;
                    } catch (error) {
                      console.error(
                        `❌ Error creating reply for thread "${title}":`,
                        error,
                      );
                    }
                  }
                }
              } catch (error) {
                console.error(
                  `❌ Error creating thread for subcategory "${subcategory.name}":`,
                  error,
                );
              }
            }
          } catch (error) {
            console.error(
              `❌ Error creating subcategory "${subcategory.name}":`,
              error,
            );
          }
        }
      } catch (error) {
        console.error(`❌ Error creating category "${category.name}":`, error);
      }
    }

    console.log(
      `✅ Successfully created ${categoryCount} categories, ${subcategoryCount} subcategories, ${threadCount} threads, and ${replyCount} replies`,
    );
  } catch (error) {
    console.error('❌ Error seeding forums:', error);
  }
}
