/**
 * @file article.seeder.ts
 * @description Seeder for creating educational articles as resources
 */
import { v4 as uuidv4 } from 'uuid';

import { ArticleStatus, PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Educational article data with titles, descriptions, and topics
 */
const articleData = [
  {
    title: 'Getting Started with React Hooks',
    description:
      'A comprehensive guide to React Hooks and how they can simplify your functional components.',
    status: ArticleStatus.APPROVED,
    topicTag: 'react',
    category: 'frontend',
  },
  {
    title: 'Understanding JavaScript Promises',
    description:
      'Learn how to use Promises in JavaScript to handle asynchronous operations effectively.',
    status: ArticleStatus.APPROVED,
    topicTag: 'javascript',
    category: 'frontend',
  },
  {
    title: 'CSS Grid vs Flexbox: When to Use Each',
    description:
      'A detailed comparison of CSS Grid and Flexbox with practical examples for different layout scenarios.',
    status: ArticleStatus.APPROVED,
    topicTag: 'css',
    category: 'frontend',
  },
  {
    title: 'Introduction to TypeScript for JavaScript Developers',
    description:
      'Learn how TypeScript can enhance your JavaScript development with static typing and advanced features.',
    status: ArticleStatus.APPROVED,
    topicTag: 'typescript',
    category: 'frontend',
  },
  {
    title: 'Building RESTful APIs with Node.js and Express',
    description:
      'A step-by-step guide to creating robust RESTful APIs using Node.js and Express.',
    status: ArticleStatus.APPROVED,
    topicTag: 'nodejs',
    category: 'backend',
  },
  {
    title: 'Data Structures Every Developer Should Know',
    description:
      'An overview of essential data structures and their practical applications in software development.',
    status: ArticleStatus.APPROVED,
    topicTag: 'algorithms',
    category: 'computer science',
  },
  {
    title: 'Optimizing React Performance',
    description:
      'Advanced techniques for improving the performance of your React applications.',
    status: ArticleStatus.DRAFT,
    topicTag: 'react',
    category: 'frontend',
  },
  {
    title: 'Introduction to Docker for Web Developers',
    description:
      'Learn how to use Docker to containerize your web applications for consistent development and deployment.',
    status: ArticleStatus.APPROVED,
    topicTag: 'devops',
    category: 'devops',
  },
  {
    title: 'Mastering Git Workflows',
    description:
      'Advanced Git techniques and workflows for effective team collaboration.',
    status: ArticleStatus.APPROVED,
    topicTag: 'git',
    category: 'devops',
  },
  {
    title: 'Building Responsive UIs with Tailwind CSS',
    description:
      'How to use Tailwind CSS to create beautiful, responsive user interfaces without writing custom CSS.',
    status: ArticleStatus.APPROVED,
    topicTag: 'css',
    category: 'frontend',
  },
  {
    title: 'Introduction to GraphQL',
    description:
      'Learn how GraphQL can improve your API development experience compared to traditional REST APIs.',
    status: ArticleStatus.APPROVED,
    topicTag: 'graphql',
    category: 'backend',
  },
  {
    title: 'JavaScript Design Patterns',
    description:
      'Common design patterns in JavaScript and how to implement them in your applications.',
    status: ArticleStatus.APPROVED,
    topicTag: 'javascript',
    category: 'frontend',
  },
  {
    title: 'Database Indexing Strategies',
    description:
      'Learn how to optimize database performance with effective indexing strategies.',
    status: ArticleStatus.APPROVED,
    topicTag: 'databases',
    category: 'backend',
  },
  {
    title: 'Microservices Architecture Explained',
    description:
      'Understanding microservices architecture and when to use it in your applications.',
    status: ArticleStatus.APPROVED,
    topicTag: 'architecture',
    category: 'backend',
  },
  {
    title: 'Test-Driven Development (TDD) in Practice',
    description:
      'A practical guide to implementing Test-Driven Development in your workflow.',
    status: ArticleStatus.APPROVED,
    topicTag: 'testing',
    category: 'best practices',
  },
];

/**
 * Seeds the database with educational articles
 */
export async function seedArticles() {
  console.log('\n==================================================');
  console.log('🌱 ARTICLE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    // Get users to assign as authors
    const users = await prisma.user.findMany({
      take: 5,
    });

    if (users.length === 0) {
      console.error('❌ No users found. Please run the user seeder first.');
      return;
    }

    // Get topics to assign to articles
    const topics = await prisma.topic.findMany();

    if (topics.length === 0) {
      console.error('❌ No topics found. Please run the topic seeder first.');
      return;
    }

    console.log(
      `ℹ️ Found ${users.length} users and ${topics.length} topics for articles`,
    );

    // Process each article
    for (const article of articleData) {
      try {
        // Find a suitable topic based on tags or title
        let topic = topics.find(
          (t) =>
            t.title.toLowerCase().includes(article.topicTag) ||
            t.description.toLowerCase().includes(article.topicTag),
        );

        // If no specific topic found, find one by category
        if (!topic) {
          topic = topics.find(
            (t) =>
              t.title.toLowerCase().includes(article.category) ||
              t.description.toLowerCase().includes(article.category),
          );
        }

        // If still no topic found, use the first one
        if (!topic) {
          topic = topics[0];
        }

        // Select a random author
        const author = users[Math.floor(Math.random() * users.length)];

        // Check if article already exists
        const existingArticle = await prisma.article.findFirst({
          where: { title: article.title },
        });

        // Generate article content
        const content = await generateArticleContent(
          article.title,
          article.topicTag,
        );

        if (existingArticle) {
          // Update existing article
          await prisma.article.update({
            where: { id: existingArticle.id },
            data: {
              content,
              status: article.status,
              updated_at: new Date(),
            },
          });
          updatedCount++;
          console.log(`🔄 Updated article: ${article.title}`);
        } else {
          // Create new article
          await prisma.article.create({
            data: {
              id: uuidv4(),
              title: article.title,
              content,
              author_id: author.id,
              topic_id: topic.id,
              status: article.status,
              created_at: new Date(),
              updated_at: new Date(),
            },
          });
          createdCount++;
          console.log(`✅ Created article: ${article.title}`);
        }
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to process article ${article.title}:`, error);
      }
    }

    console.log('\n✅ ARTICLE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding articles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Generate article content for a given title and topic
 * @param title The article title
 * @param topicTag The topic tag
 * @returns A string with article content
 */
async function generateArticleContent(
  title: string,
  topicTag: string,
): Promise<string> {
  // Common introduction paragraphs
  const introductions = [
    `Welcome to this comprehensive guide on ${title}. This article aims to provide you with practical insights that you can apply to your projects immediately.`,
    `In the world of software development, understanding ${title} is crucial for building efficient and maintainable applications. This article will walk you through everything you need to know.`,
    `${title} is a fundamental concept that every developer should master. In this article, we'll explore its core principles and practical applications.`,
  ];

  // Common conclusion paragraphs
  const conclusions = [
    `In conclusion, mastering ${title} will significantly enhance your development skills and enable you to build more robust, efficient, and maintainable applications.`,
    `As you continue to work with ${title}, remember the principles and best practices we've discussed in this article. They will serve as a solid foundation for your future projects.`,
    `We hope this guide has given you a better understanding of ${title}. Keep practicing and exploring to deepen your knowledge further.`,
  ];

  // Select random introduction and conclusion
  const introduction =
    introductions[Math.floor(Math.random() * introductions.length)];
  const conclusion =
    conclusions[Math.floor(Math.random() * conclusions.length)];

  // Topic-specific content
  let mainContent = '';

  // Add topic-specific content based on the topic tag
  switch (topicTag) {
    case 'react':
      mainContent = `
## Understanding React Components

React components are the building blocks of any React application. They are reusable pieces of code that return React elements describing what should appear on the screen.

### Functional Components

Functional components are simpler and more concise:

\`\`\`jsx
function Welcome(props) {
  return <h1>Hello, {props.name}</h1>;
}
\`\`\`

### Class Components

Class components provide more features:

\`\`\`jsx
class Welcome extends React.Component {
  render() {
    return <h1>Hello, {this.props.name}</h1>;
  }
}
\`\`\`

## React Hooks

Hooks are a newer addition to React that let you use state and other React features without writing a class.

### useState Hook

The useState hook lets you add React state to functional components:

\`\`\`jsx
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
\`\`\`

### useEffect Hook

The useEffect hook lets you perform side effects in functional components:

\`\`\`jsx
import React, { useState, useEffect } from 'react';

function Example() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    document.title = \`You clicked \${count} times\`;
  }, [count]);

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
\`\`\`

## Best Practices

1. **Keep components small and focused** - Each component should do one thing well
2. **Use functional components with hooks** - They're simpler and cover most use cases
3. **Implement proper state management** - Consider using Context API or Redux for complex state
4. **Optimize rendering performance** - Use React.memo, useMemo, and useCallback to prevent unnecessary re-renders
5. **Follow the React component lifecycle** - Understand when components mount, update, and unmount
`;
      break;

    case 'javascript':
      mainContent = `
## JavaScript Fundamentals

JavaScript is a versatile programming language that powers the modern web. Understanding its core concepts is essential for any web developer.

### Variables and Data Types

JavaScript has several data types:

\`\`\`javascript
// Primitive types
let string = "Hello";
let number = 42;
let boolean = true;
let nullValue = null;
let undefinedValue = undefined;
let symbol = Symbol("unique");
let bigInt = 9007199254740991n;

// Object types
let object = { name: "John", age: 30 };
let array = [1, 2, 3, 4];
let date = new Date();
let regExp = /pattern/;
let map = new Map();
let set = new Set();
\`\`\`

### Functions

Functions are first-class citizens in JavaScript:

\`\`\`javascript
// Function declaration
function greet(name) {
  return \`Hello, \${name}!\`;
}

// Function expression
const greetExpression = function(name) {
  return \`Hello, \${name}!\`;
};

// Arrow function
const greetArrow = (name) => \`Hello, \${name}!\`;

// Higher-order function
function createGreeter(greeting) {
  return function(name) {
    return \`\${greeting}, \${name}!\`;
  };
}

const sayHello = createGreeter("Hello");
console.log(sayHello("John")); // "Hello, John!"
\`\`\`

## Asynchronous JavaScript

JavaScript handles asynchronous operations through callbacks, promises, and async/await.

### Promises

Promises represent the eventual completion or failure of an asynchronous operation:

\`\`\`javascript
const fetchData = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const data = { id: 1, name: "User" };
      if (data) {
        resolve(data);
      } else {
        reject("No data available");
      }
    }, 1000);
  });
};

fetchData()
  .then(data => console.log(data))
  .catch(error => console.error(error));
\`\`\`

### Async/Await

Async/await provides a more synchronous-looking way to work with promises:

\`\`\`javascript
async function getData() {
  try {
    const data = await fetchData();
    console.log(data);
  } catch (error) {
    console.error(error);
  }
}

getData();
\`\`\`

## Modern JavaScript Features

ES6 and beyond introduced many useful features:

1. **Destructuring** - Extract values from objects and arrays
2. **Spread/Rest operators** - Expand or collect elements
3. **Template literals** - String interpolation and multi-line strings
4. **Default parameters** - Set default values for function parameters
5. **Modules** - Organize code into reusable pieces
6. **Classes** - Syntactic sugar over JavaScript's prototype-based inheritance
`;
      break;

    case 'css':
      mainContent = `
## CSS Layout Systems

CSS offers several layout systems to structure web pages effectively.

### Flexbox

Flexbox is designed for one-dimensional layouts (either rows or columns):

\`\`\`css
.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item {
  flex: 1;
  margin: 10px;
}
\`\`\`

Key flexbox properties:
- \`flex-direction\`: row, row-reverse, column, column-reverse
- \`justify-content\`: flex-start, flex-end, center, space-between, space-around
- \`align-items\`: flex-start, flex-end, center, stretch, baseline
- \`flex-wrap\`: nowrap, wrap, wrap-reverse
- \`flex\`: grow, shrink, basis

### CSS Grid

CSS Grid is designed for two-dimensional layouts (rows and columns):

\`\`\`css
.container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: auto;
  grid-gap: 20px;
}

.item {
  grid-column: span 2;
}
\`\`\`

Key grid properties:
- \`grid-template-columns\`: defines the columns
- \`grid-template-rows\`: defines the rows
- \`grid-gap\`: spacing between grid items
- \`grid-column\`: placement and span of items across columns
- \`grid-row\`: placement and span of items across rows

## When to Use Each

- **Use Flexbox when**:
  - You need a one-dimensional layout (row OR column)
  - You want to align items within a container
  - You need to distribute space among items
  - You're working with small-scale layouts

- **Use Grid when**:
  - You need a two-dimensional layout (rows AND columns)
  - You want to align items in both directions
  - You're creating a complex layout with overlapping elements
  - You're working with large-scale layouts

## Responsive Design

Both Flexbox and Grid work well with responsive design:

\`\`\`css
@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
  }
}
\`\`\`

## Best Practices

1. **Start with semantic HTML** - Your layout should be based on meaningful markup
2. **Use relative units** - Prefer rem, em, %, vw, vh over pixels
3. **Mobile-first approach** - Design for small screens first, then enhance for larger ones
4. **Combine layout methods** - Use Grid for page structure and Flexbox for component alignment
5. **Test across browsers** - Ensure compatibility with all major browsers
`;
      break;

    default:
      mainContent = `
## Core Concepts

Understanding the fundamentals of ${title} is essential for any developer looking to master this topic.

### Key Principles

1. **Principle One** - Description of the first principle with examples
2. **Principle Two** - Description of the second principle with examples
3. **Principle Three** - Description of the third principle with examples

### Code Examples

Here's a basic implementation example:

\`\`\`javascript
// Sample code related to the topic
function example() {
  console.log("This is an example related to " + "${title}");
  
  // More implementation details
  const result = performOperation();
  return result;
}

function performOperation() {
  // Core logic
  return "operation result";
}
\`\`\`

## Best Practices

When working with ${title}, keep these best practices in mind:

1. **Practice One** - Description and rationale
2. **Practice Two** - Description and rationale
3. **Practice Three** - Description and rationale

## Common Pitfalls

Avoid these common mistakes:

1. **Pitfall One** - How to identify and avoid it
2. **Pitfall Two** - How to identify and avoid it
3. **Pitfall Three** - How to identify and avoid it

## Advanced Techniques

Once you've mastered the basics, explore these advanced techniques:

1. **Advanced Technique One** - Description and implementation
2. **Advanced Technique Two** - Description and implementation
3. **Advanced Technique Three** - Description and implementation
`;
  }

  // Combine all sections
  return `${introduction}\n\n${mainContent}\n\n${conclusion}`;
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedArticles()
    .then(() => {
      console.log('Article seeder completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error running article seeder:', error);
      process.exit(1);
    });
}
