/**
 * @file battle.seeder.ts
 * @description Seeder for creating sample coding battles
 */
import { v4 as uuidv4 } from 'uuid';

import {
  BattleStatus,
  BattleType,
  Difficulty,
  Length,
  PrismaClient,
} from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds the database with sample battles and related data
 */
async function seedBattles() {
  console.log('\n==================================================');
  console.log('🌱 BATTLE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    // Get users for battle creators and participants
    const users = await prisma.user.findMany({ take: 10 });

    if (users.length < 2) {
      console.error(
        '❌ Not enough users found. Please run the user seeder first.',
      );
      return;
    }

    // Get topics for battles
    const topics = await prisma.topic.findMany();

    if (topics.length === 0) {
      console.error('❌ No topics found. Please run the topic seeder first.');
      return;
    }

    console.log(
      `ℹ️  Found ${users.length} users and ${topics.length} topics for battles`,
    );

    // Sample battle titles
    const battleTitles = [
      'JavaScript Fundamentals Showdown',
      'Python Coding Challenge',
      'Data Structures Battle',
      'Algorithm Masters Competition',
      'Web Development Face-off',
      'Database Design Challenge',
      'React vs Angular Debate',
      'Machine Learning Contest',
      'DevOps Skills Test',
      'Mobile Development Battle',
    ];

    // Sample battle descriptions
    const battleDescriptions = [
      'Test your knowledge of core programming concepts in this exciting battle!',
      'Race against other developers to solve challenging coding problems.',
      'Prove your mastery of data structures and algorithms.',
      'A friendly competition to showcase your development skills.',
      'Challenge yourself with real-world programming scenarios.',
    ];

    // Sample questions for battles
    const sampleQuestions = [
      {
        question:
          'What is the output of console.log(typeof null) in JavaScript?',
        options: {
          A: '"null"',
          B: '"object"',
          C: '"undefined"',
          D: '"boolean"',
        },
        correct_answer: 'B',
      },
      {
        question:
          'Which data structure uses LIFO (Last In First Out) principle?',
        options: { A: 'Queue', B: 'Stack', C: 'Linked List', D: 'Tree' },
        correct_answer: 'B',
      },
      {
        question: 'What is the time complexity of binary search?',
        options: { A: 'O(1)', B: 'O(n)', C: 'O(log n)', D: 'O(n²)' },
        correct_answer: 'C',
      },
      {
        question: 'Which HTTP status code represents "Not Found"?',
        options: { A: '200', B: '404', C: '500', D: '301' },
        correct_answer: 'B',
      },
      {
        question: 'What does CSS stand for?',
        options: {
          A: 'Computer Style Sheets',
          B: 'Creative Style Sheets',
          C: 'Cascading Style Sheets',
          D: 'Colorful Style Sheets',
        },
        correct_answer: 'C',
      },
      {
        question:
          'Which of the following is NOT a JavaScript framework/library?',
        options: { A: 'React', B: 'Angular', C: 'Django', D: 'Vue' },
        correct_answer: 'C',
      },
      {
        question: 'What is the purpose of the "git clone" command?',
        options: {
          A: 'To create a new branch',
          B: 'To copy a repository',
          C: 'To merge branches',
          D: 'To delete a repository',
        },
        correct_answer: 'B',
      },
      {
        question:
          'Which SQL statement is used to extract data from a database?',
        options: { A: 'EXTRACT', B: 'SELECT', C: 'GET', D: 'OPEN' },
        correct_answer: 'B',
      },
      {
        question: 'What does API stand for?',
        options: {
          A: 'Application Programming Interface',
          B: 'Automated Program Integration',
          C: 'Advanced Programming Implementation',
          D: 'Application Process Integration',
        },
        correct_answer: 'A',
      },
      {
        question:
          'Which of these is NOT a valid way to declare a variable in JavaScript?',
        options: { A: 'var', B: 'let', C: 'const', D: 'function' },
        correct_answer: 'D',
      },
      {
        question:
          'What is the correct way to check if a variable is an array in JavaScript?',
        options: {
          A: 'typeof variable === "array"',
          B: 'variable instanceof Array',
          C: 'variable.isArray()',
          D: 'variable.type === "array"',
        },
        correct_answer: 'B',
      },
      {
        question: 'Which of the following is a NoSQL database?',
        options: { A: 'MySQL', B: 'PostgreSQL', C: 'MongoDB', D: 'Oracle' },
        correct_answer: 'C',
      },
      {
        question: 'What does the "this" keyword refer to in JavaScript?',
        options: {
          A: 'The current function',
          B: 'The global object',
          C: 'The object the method belongs to',
          D: 'It depends on how the function is called',
        },
        correct_answer: 'D',
      },
      {
        question: 'Which CSS property is used to change the text color?',
        options: {
          A: 'text-color',
          B: 'font-color',
          C: 'color',
          D: 'text-style',
        },
        correct_answer: 'C',
      },
      {
        question: 'What is the purpose of the "useState" hook in React?',
        options: {
          A: 'To fetch data from an API',
          B: 'To manage component state',
          C: 'To handle routing',
          D: 'To optimize performance',
        },
        correct_answer: 'B',
      },
    ];

    // Create different types of battles
    const battleTypes = [
      { type: BattleType.INSTANT, status: BattleStatus.COMPLETED },
      { type: BattleType.SCHEDULED, status: BattleStatus.UPCOMING },
      { type: BattleType.TOURNAMENT, status: BattleStatus.IN_PROGRESS },
      { type: BattleType.PRACTICE, status: BattleStatus.COMPLETED },
      { type: BattleType.INSTANT, status: BattleStatus.COMPLETED },
    ];

    // Create sample battles
    for (let i = 0; i < battleTypes.length; i++) {
      try {
        const battleType = battleTypes[i];
        const title = battleTitles[i % battleTitles.length];
        const description = battleDescriptions[i % battleDescriptions.length];
        const topic = topics[i % topics.length];
        const creator = users[i % users.length];

        // Create battle
        const battle = await prisma.battle.create({
          data: {
            id: uuidv4(),
            title,
            description,
            difficulty:
              i % 2 === 0
                ? Difficulty.EASY
                : i % 3 === 0
                  ? Difficulty.HARD
                  : Difficulty.MEDIUM,
            length:
              i % 2 === 0
                ? Length.short
                : i % 3 === 0
                  ? Length.long
                  : Length.medium,
            topic_id: topic.id,
            user_id: creator.id,
            type: battleType.type,
            status: battleType.status,
            total_questions: 5 + (i % 6), // Between 5-10 questions
            time_per_question: 30 + i * 10, // Between 30-70 seconds
            points_per_question: 10 + i * 5, // Between 10-30 points
            max_participants: 2 + (i % 3), // Between 2-4 participants
            current_participants:
              battleType.status !== BattleStatus.UPCOMING ? 2 : 0,
            start_time:
              battleType.status === BattleStatus.UPCOMING
                ? new Date(Date.now() + 86400000)
                : new Date(Date.now() - 3600000),
            end_time:
              battleType.status === BattleStatus.COMPLETED ? new Date() : null,
            created_at: new Date(Date.now() - 86400000 * (i + 1)), // Created 1-5 days ago
            updated_at: new Date(),
          },
        });

        // Create battle questions
        const questionCount = 5 + (i % 6);
        for (let q = 0; q < questionCount; q++) {
          const questionData = sampleQuestions[q % sampleQuestions.length];
          await prisma.battleQuestion.create({
            data: {
              id: uuidv4(),
              battle_id: battle.id,
              question: questionData.question,
              options: questionData.options,
              correct_answer: questionData.correct_answer,
              points: 10 + i * 5,
              time_limit: 30 + i * 10,
              order: q + 1,
              created_at: new Date(),
            },
          });
        }

        // Add participants for non-upcoming battles
        if (battleType.status !== BattleStatus.UPCOMING) {
          // Add creator as participant
          await prisma.battleParticipant.create({
            data: {
              id: uuidv4(),
              battle_id: battle.id,
              user_id: creator.id,
              score: 30 + i * 20,
              rank: 1,
              joined_at: new Date(Date.now() - 7200000),
              completed_at:
                battleType.status === BattleStatus.COMPLETED
                  ? new Date(Date.now() - 3600000)
                  : null,
            },
          });

          // Add another participant
          const otherUser = users[(i + 1) % users.length];
          await prisma.battleParticipant.create({
            data: {
              id: uuidv4(),
              battle_id: battle.id,
              user_id: otherUser.id,
              score: 20 + i * 15,
              rank: 2,
              joined_at: new Date(Date.now() - 7200000),
              completed_at:
                battleType.status === BattleStatus.COMPLETED
                  ? new Date(Date.now() - 3650000)
                  : null,
            },
          });

          // Create leaderboard entries for completed battles
          if (battleType.status === BattleStatus.COMPLETED) {
            await prisma.battleLeaderboard.create({
              data: {
                id: uuidv4(),
                battle_id: battle.id,
                user_id: creator.id,
                score: 30 + i * 20,
                rank: 1,
                time_taken: 250 + i * 10,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });

            await prisma.battleLeaderboard.create({
              data: {
                id: uuidv4(),
                battle_id: battle.id,
                user_id: otherUser.id,
                score: 20 + i * 15,
                rank: 2,
                time_taken: 280 + i * 15,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
          }
        }

        createdCount++;
        console.log(
          `✅ Created battle: ${title} (${battleType.type}, ${battleType.status})`,
        );
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to create battle ${i + 1}:`, error);
      }
    }

    console.log('\n✅ BATTLE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding battles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  seedBattles().catch((error) => {
    console.error('Failed to seed battles:', error);
    process.exit(1);
  });
}

export { seedBattles };
