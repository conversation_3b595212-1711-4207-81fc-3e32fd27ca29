import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

import { subjects } from '../../resources/subjects';

const prisma = new PrismaClient();

const seedSubjects = async () => {
  try {
    console.log('\n==================================================');
    console.log('🌱 SUBJECT SEEDER STARTED');
    console.log('==================================================');

    let createdCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    for (const subject of subjects) {
      try {
        // Convert the subject data to match the schema
        const subjectData = {
          title: subject.name,
          description: subject.description,
          order: subject.order || 0,
        };

        // Check if subject exists by title
        const existingSubject = await prisma.subject.findFirst({
          where: { title: subjectData.title },
        });

        if (existingSubject) {
          // Update existing subject
          await prisma.subject.update({
            where: { id: existingSubject.id },
            data: subjectData,
          });
          updatedCount++;
          console.log(`✅ Updated subject: ${subjectData.title}`);
        } else {
          // Create new subject
          await prisma.subject.create({
            data: {
              id: uuidv4(),
              ...subjectData,
            },
          });
          createdCount++;
          console.log(`✅ Created subject: ${subjectData.title}`);
        }
      } catch (error) {
        console.error(`❌ Error processing subject "${subject.name}":`, error);
        errorCount++;
      }
    }

    console.log('\n✅ SUBJECT SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created ${createdCount} new subjects`);
    console.log(`✅ Updated ${updatedCount} existing subjects`);
    console.log(`❌ Encountered ${errorCount} errors`);
    console.log('==================================================');
  } catch (error) {
    console.error('Error seeding subjects:', error);
  } finally {
    await prisma.$disconnect();
  }
};

seedSubjects();
