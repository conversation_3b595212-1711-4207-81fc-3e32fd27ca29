import { PrismaClient, RoleType } from '@prisma/client';

const prisma = new PrismaClient();

const rolesToSeed = [
  { name: 'ADMIN', type: RoleType.ADMIN },
  { name: 'MODERATOR', type: RoleType.MODERATOR },
  { name: 'USER', type: RoleType.USER }, // Changed from STUDENT to align with RoleType and user.seeder.ts
  { name: 'CONTRIBUTOR', type: RoleType.CONTRIBUTOR }, // Added to align with RoleType enum
];

const seedRoles = async () => {
  console.log('\n==================================================');
  console.log('🌱 ROLE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    console.log(`ℹ️  Found ${rolesToSeed.length} roles to process`);

    for (const roleData of rolesToSeed) {
      try {
        const existingRole = await prisma.role.findUnique({
          where: { name: roleData.name }, // Could also use type if type is unique, but name is good for user-facing ID
        });

        if (existingRole) {
          await prisma.role.update({
            where: { name: roleData.name },
            data: { name: roleData.name, type: roleData.type }, // Ensure both name and type are updated
          });
          updatedCount++;
          console.log(
            `🔄 Updated role: ${roleData.name} (type: ${roleData.type})`,
          );
        } else {
          await prisma.role.create({
            data: { name: roleData.name, type: roleData.type },
          });
          createdCount++;
          console.log(
            `✅ Created role: ${roleData.name} (type: ${roleData.type})`,
          );
        }
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to process role ${roleData.name}:`, error);
      }
    }

    console.log('\n✅ ROLE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding roles:', error);
  } finally {
    await prisma.$disconnect();
  }
};

seedRoles();
