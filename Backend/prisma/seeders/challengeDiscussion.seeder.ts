/**
 * @file challengeDiscussion.seeder.ts
 * @description Seeder for creating discussions for challenges
 */
import { v4 as uuidv4 } from 'uuid';

import { DiscussionType, PrismaClient } from '@prisma/client';

import logger from '../../src/utils/logger';

const prisma = new PrismaClient();

/**
 * Seeds the database with challenge discussions
 */
export async function seedChallengeDiscussions() {
  logger.info('Starting to seed challenge discussions...');

  try {
    // Get users to create discussions for
    const users = await prisma.user.findMany({
      take: 20, // Limit to 20 users to avoid creating too much data
    });

    if (users.length === 0) {
      logger.error('No users found. Please run the user seeder first.');
      return;
    }

    // Get challenges to create discussions for
    const challenges = await prisma.challenge.findMany({
      take: 15, // Limit to 15 challenges to avoid creating too much data
    });

    if (challenges.length === 0) {
      logger.error(
        'No challenges found. Please run the challenge seeder first.',
      );
      return;
    }

    // Get coding challenges to create discussions for
    const codingChallenges = await prisma.codingChallenge.findMany({
      take: 15, // Limit to 15 coding challenges to avoid creating too much data
    });

    if (codingChallenges.length === 0) {
      logger.error(
        'No coding challenges found. Please run the coding challenge seeder first.',
      );
      return;
    }

    logger.info(
      `Found ${users.length} users, ${challenges.length} challenges, and ${codingChallenges.length} coding challenges for creating discussions`,
    );

    /**
     * Generate a random date within a range
     * @param start The start date
     * @param end The end date
     * @returns A random date between start and end
     */
    const randomDate = (start: Date, end: Date): Date => {
      return new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime()),
      );
    };

    /**
     * Generate a discussion title based on type
     * @param type The discussion type
     * @returns A random discussion title
     */
    const generateDiscussionTitle = (type: DiscussionType): string => {
      const questionTitles = [
        'How to approach this problem?',
        'Struggling with this challenge',
        'Need help understanding the problem statement',
        "What's the optimal solution for this?",
        'Time complexity of the solution?',
        'Is there a more efficient algorithm?',
        'Stuck on edge cases',
        'How to handle this specific input?',
        'Alternative approaches to solve this?',
        'Clarification needed on problem constraints',
      ];

      const solutionTitles = [
        'My solution with detailed explanation',
        'Efficient solution with O(n) time complexity',
        'Step-by-step approach to solve this',
        'Optimized solution with examples',
        'Clean and readable solution',
        'Solution using dynamic programming',
        'Recursive approach with memoization',
        'Iterative solution with constant space',
        'Multiple approaches compared',
        'Solution with edge case handling',
      ];

      const hintTitles = [
        'Hint: Think about using a specific data structure',
        'Helpful hint without giving away the solution',
        'Small hint to get you started',
        'Key insight for solving this challenge',
        'Hint: Consider the problem constraints',
        'Approach hint: Break down the problem',
        'Subtle hint for those who are stuck',
        'Direction pointer for the solution',
        'Hint: Look for patterns in the input',
        'Algorithmic hint for optimal solution',
      ];

      switch (type) {
        case DiscussionType.QUESTION:
          return questionTitles[
            Math.floor(Math.random() * questionTitles.length)
          ];
        case DiscussionType.SOLUTION:
          return solutionTitles[
            Math.floor(Math.random() * solutionTitles.length)
          ];
        case DiscussionType.HINT:
          return hintTitles[Math.floor(Math.random() * hintTitles.length)];
        default:
          return 'Discussion about this challenge';
      }
    };

    /**
     * Generate discussion content based on type
     * @param type The discussion type
     * @returns A random discussion content
     */
    const generateDiscussionContent = (type: DiscussionType): string => {
      const questionContents = [
        "I'm having trouble understanding how to approach this problem. Can someone provide some guidance on where to start? I've tried using a brute force approach, but it seems inefficient for larger inputs.",

        "I'm stuck on this challenge and would appreciate some help. I understand that we need to find a solution, but I'm not sure which algorithm or data structure would be most appropriate here. Has anyone solved this before?",

        "Could someone clarify the problem statement? I'm confused about some of the constraints and requirements. Specifically, I'm not sure if we need to handle edge cases like empty inputs or negative values.",

        "I've been working on this problem for hours and can't seem to get past certain test cases. My solution works for the basic examples, but fails on more complex inputs. Any suggestions on what I might be missing?",

        "What's the expected time and space complexity for an optimal solution to this problem? I have a solution that works in O(n²) time, but I feel like there should be a more efficient approach.",
      ];

      const solutionContents = [
        "Here's my solution to this challenge:\n\n```\nfunction solution(input) {\n  // Initialize variables\n  let result = 0;\n  \n  // Process the input\n  for (let i = 0; i < input.length; i++) {\n    // Logic here\n    result += input[i];\n  }\n  \n  return result;\n}\n```\n\nThe approach is to iterate through the input and apply the necessary logic. The time complexity is O(n) and space complexity is O(1).",

        'I solved this using a dynamic programming approach:\n\n```\nfunction solution(input) {\n  // Create DP table\n  const dp = new Array(input.length + 1).fill(0);\n  \n  // Fill the table\n  for (let i = 1; i <= input.length; i++) {\n    dp[i] = Math.max(dp[i-1] + input[i-1], input[i-1]);\n  }\n  \n  return Math.max(...dp);\n}\n```\n\nThis solution has O(n) time complexity and O(n) space complexity. We can optimize the space to O(1) by using variables instead of an array.',

        'I approached this problem using a hash map for efficient lookups:\n\n```\nfunction solution(input, target) {\n  const map = new Map();\n  \n  for (let i = 0; i < input.length; i++) {\n    const complement = target - input[i];\n    \n    if (map.has(complement)) {\n      return [map.get(complement), i];\n    }\n    \n    map.set(input[i], i);\n  }\n  \n  return null;\n}\n```\n\nThis solution has O(n) time complexity and O(n) space complexity, which is optimal for this problem.',

        'For this challenge, I used a two-pointer technique:\n\n```\nfunction solution(input) {\n  let left = 0;\n  let right = input.length - 1;\n  let result = 0;\n  \n  while (left < right) {\n    // Logic here\n    if (input[left] < input[right]) {\n      left++;\n    } else {\n      right--;\n    }\n  }\n  \n  return result;\n}\n```\n\nThis approach is efficient with O(n) time complexity and O(1) space complexity.',

        'I solved this recursively with memoization to avoid redundant calculations:\n\n```\nfunction solution(input) {\n  const memo = new Map();\n  \n  function dp(index) {\n    if (index >= input.length) return 0;\n    if (memo.has(index)) return memo.get(index);\n    \n    const result = Math.max(input[index] + dp(index + 2), dp(index + 1));\n    memo.set(index, result);\n    return result;\n  }\n  \n  return dp(0);\n}\n```\n\nThis solution has O(n) time complexity due to memoization and O(n) space complexity for the memoization table.',
      ];

      const hintContents = [
        "Without giving away the full solution, consider using a hash map to store values you've seen so far. This can help you achieve O(1) lookups which is crucial for an efficient solution.",

        'Think about whether a greedy approach would work here. Sometimes, making the locally optimal choice at each step leads to the globally optimal solution for this type of problem.',

        'Consider breaking down the problem into smaller subproblems. This is a classic scenario where dynamic programming can be applied to avoid redundant calculations.',

        'For this challenge, a two-pointer technique might be useful. Try maintaining pointers at different positions and moving them based on certain conditions to find the solution efficiently.',

        'Pay attention to the constraints of the problem. The input size might give you a clue about the expected time complexity. If the input can be very large, a solution with O(n²) or worse might time out.',
      ];

      switch (type) {
        case DiscussionType.QUESTION:
          return questionContents[
            Math.floor(Math.random() * questionContents.length)
          ];
        case DiscussionType.SOLUTION:
          return solutionContents[
            Math.floor(Math.random() * solutionContents.length)
          ];
        case DiscussionType.HINT:
          return hintContents[Math.floor(Math.random() * hintContents.length)];
        default:
          return 'This is a discussion about the challenge. Please share your thoughts and questions.';
      }
    };

    /**
     * Generate a reply to a discussion
     * @returns A random reply content
     */
    const generateReplyContent = (): string => {
      const replies = [
        'Thanks for sharing your approach! I found it very helpful.',

        'I tried your solution and it worked perfectly. The time complexity analysis is spot on.',

        'Have you considered edge cases like empty arrays or negative numbers? I ran into issues with those.',

        "I think there's a more efficient solution using a different data structure. Let me try to implement it and get back to you.",

        'Your explanation really helped me understand the problem better. I was overthinking it.',

        "I'm still struggling with this. Could you elaborate on the part where you initialize the variables?",

        'This is a great solution! I especially like how you handled the edge cases.',

        'I implemented a similar approach but used recursion instead of iteration. It works but has higher space complexity due to the call stack.',

        "Have you tested this with very large inputs? I'm concerned about potential stack overflow with the recursive approach.",

        'I found a small optimization: we can actually stop the iteration early under certain conditions, which improves the average case performance.',
      ];

      return replies[Math.floor(Math.random() * replies.length)];
    };

    /**
     * Generate a random number of replies for a discussion
     * @returns A number between 0 and 5
     */
    const generateReplyCount = (): number => {
      // 30% chance of no replies, 30% chance of 1 reply, 20% chance of 2 replies, 20% chance of 3-5 replies
      const random = Math.random();
      if (random < 0.3) {
        return 0;
      } else if (random < 0.6) {
        return 1;
      } else if (random < 0.8) {
        return 2;
      } else {
        return Math.floor(Math.random() * 3) + 3; // 3-5 replies
      }
    };

    // Create discussions for challenges
    logger.info('Creating discussions for challenges...');

    let discussionCount = 0;
    let replyCount = 0;

    // Combine regular challenges and coding challenges
    const allChallenges = [
      ...challenges.map((challenge) => ({
        id: challenge.id,
        type: 'CHALLENGE',
      })),
      ...codingChallenges.map((challenge) => ({
        id: challenge.id,
        type: 'CODING_CHALLENGE',
      })),
    ];

    // Generate random dates for the discussions (within the last 6 months)
    const now = new Date();
    const sixMonthsAgo = new Date(now);
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    // Create discussions for each challenge
    for (const challenge of allChallenges) {
      try {
        // Determine how many discussions to create for this challenge (1-5)
        const numDiscussions = Math.floor(Math.random() * 5) + 1;

        for (let i = 0; i < numDiscussions; i++) {
          try {
            // Randomly select a user to create the discussion
            const userIndex = Math.floor(Math.random() * users.length);
            const user = users[userIndex];

            // Randomly select a discussion type
            const discussionTypes = [
              DiscussionType.QUESTION,
              DiscussionType.SOLUTION,
              DiscussionType.HINT,
            ];
            const discussionType =
              discussionTypes[
                Math.floor(Math.random() * discussionTypes.length)
              ];

            // Generate title and content based on the discussion type
            const title = generateDiscussionTitle(discussionType);
            const content = generateDiscussionContent(discussionType);

            // Generate a random date for the discussion
            const discussionDate = randomDate(sixMonthsAgo, now);

            // Create the discussion
            const discussion = await prisma.discussion.create({
              data: {
                id: uuidv4(),
                user_id: user.id,
                title,
                content,
                type: discussionType,
                challenge_id:
                  challenge.type === 'CHALLENGE' ? challenge.id : null,
                coding_challenge_id:
                  challenge.type === 'CODING_CHALLENGE' ? challenge.id : null,
                created_at: discussionDate,
                updated_at: discussionDate,
              },
            });

            discussionCount++;

            // Determine if this discussion should have replies
            const numReplies = generateReplyCount();

            // Create replies for this discussion
            if (numReplies > 0) {
              // Replies should be after the parent discussion date
              const replyStartDate = new Date(discussionDate);
              replyStartDate.setHours(replyStartDate.getHours() + 1); // At least 1 hour after parent discussion

              for (let j = 0; j < numReplies; j++) {
                try {
                  // Randomly select a user to create the reply (different from the discussion creator)
                  let replyUserIndex;
                  do {
                    replyUserIndex = Math.floor(Math.random() * users.length);
                  } while (users[replyUserIndex].id === user.id);

                  const replyUser = users[replyUserIndex];

                  // Generate reply content
                  const replyContent = generateReplyContent();

                  // Generate a random date for the reply (after discussion, before now)
                  const replyDate = randomDate(replyStartDate, now);

                  // Create the reply
                  await prisma.discussionReply.create({
                    data: {
                      id: uuidv4(),
                      discussion_id: discussion.id,
                      user_id: replyUser.id,
                      content: replyContent,
                      created_at: replyDate,
                      updated_at: replyDate,
                    },
                  });

                  replyCount++;
                } catch (error) {
                  logger.error(`Error creating reply for discussion:`, error);
                }
              }
            }
          } catch (error) {
            logger.error(`Error creating discussion for challenge:`, error);
          }
        }
      } catch (error) {
        logger.error(`Error processing challenge for discussions:`, error);
      }
    }

    logger.info(
      `Successfully created ${discussionCount} discussions with ${replyCount} replies`,
    );
  } catch (error) {
    logger.error('Error seeding challenge discussions:', error);
  }
}
