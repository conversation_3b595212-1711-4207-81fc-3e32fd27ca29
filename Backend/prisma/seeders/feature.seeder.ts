/**
 * @file feature.seeder.ts
 * @description Seed script for features
 */
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Define all features
const features = [
  {
    name: 'User Management',
    description: 'User profile and account management',
    permissions: [
      'users:view',
      'users:create',
      'users:edit',
      'users:delete',
      'users:manage_roles',
    ],
  },
  {
    name: 'Content Management',
    description: 'Content creation, editing, and publishing',
    permissions: [
      'content:view',
      'content:create',
      'content:edit',
      'content:delete',
      'content:publish',
      'content:moderate',
    ],
  },
  {
    name: 'Role Management',
    description: 'Role and permission management',
    permissions: [
      'roles:view',
      'roles:create',
      'roles:edit',
      'roles:delete',
      'roles:manage_permissions',
    ],
  },
  {
    name: 'System Settings',
    description: 'System configuration and settings',
    permissions: ['settings:view', 'settings:edit'],
  },
  {
    name: 'Analytics',
    description: 'Analytics and reporting',
    permissions: ['analytics:view', 'analytics:export'],
  },
];

/**
 * Seed features and connect them with permissions
 */
async function seedFeatures() {
  console.log('\n==================================================');
  console.log('🌱 FEATURE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    console.log(`ℹ️ Found ${features.length} features to process`);

    for (const featureData of features) {
      try {
        // Extract permissions array and other feature data
        const { permissions: permissionKeys, ...featureInfo } = featureData;

        // Find existing feature
        const existingFeature = await prisma.feature.findUnique({
          where: { name: featureData.name },
          include: { permissions: true },
        });

        // Find permission records for the given keys
        const permissionRecords = await prisma.permission.findMany({
          where: {
            key: { in: permissionKeys },
          },
        });

        if (permissionRecords.length !== permissionKeys.length) {
          console.warn(
            `⚠️ Some permissions not found for feature ${featureData.name}`,
          );
        }

        if (existingFeature) {
          // Update feature and connect permissions
          await prisma.feature.update({
            where: { id: existingFeature.id },
            data: {
              ...featureInfo,
              permissions: {
                connect: permissionRecords.map((p) => ({ id: p.id })),
              },
            },
          });
          updatedCount++;
          console.log(`🔄 Updated feature: ${featureData.name}`);
        } else {
          // Create feature and connect permissions
          await prisma.feature.create({
            data: {
              ...featureInfo,
              permissions: {
                connect: permissionRecords.map((p) => ({ id: p.id })),
              },
            },
          });
          createdCount++;
          console.log(`✅ Created feature: ${featureData.name}`);
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process feature ${featureData.name}:`,
          error,
        );
      }
    }

    console.log('\n✅ FEATURE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding features:', error);
  }
}

/**
 * Main function
 */
async function main() {
  try {
    await seedFeatures();
  } catch (error) {
    console.error('❌ Error in feature seeding process:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the main function if this file is run directly
if (require.main === module) {
  main();
}

export { seedFeatures };
