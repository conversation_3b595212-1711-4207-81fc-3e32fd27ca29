/**
 * @file challengeExample.seeder.ts
 * @description Seeder for creating examples for existing challenges
 */
import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedChallengeExamples() {
  console.log('\n==================================================');
  console.log('🌱 CHALLENGE EXAMPLE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    const challenges = await prisma.challenge.findMany();

    if (challenges.length === 0) {
      console.error(
        '❌ No challenges found. Please run the challenge seeder first.',
      );
      return;
    }

    console.log(`ℹ️  Found ${challenges.length} challenges for examples`);

    for (const challenge of challenges) {
      try {
        // Check if challenge already has examples
        const existingExamples = await prisma.challengeExample.findMany({
          where: { challenge_id: challenge.id },
        });

        if (existingExamples.length > 0) {
          console.log(
            `ℹ️  Challenge "${challenge.title}" already has ${existingExamples.length} examples. Skipping.`,
          );
          continue;
        }

        // Create 2-3 examples per challenge
        const numExamples = Math.floor(Math.random() * 2) + 2;

        // Use the challenge's own example as the first example
        await prisma.challengeExample.create({
          data: {
            id: uuidv4(),
            challenge_id: challenge.id,
            input: challenge.example_input,
            output: challenge.example_output,
            explanation:
              challenge.explanation ||
              `Example showing how to solve the ${challenge.title} challenge.`,
            order_index: 1,
            created_at: new Date(),
            updated_at: new Date(),
          },
        });
        createdCount++;
        console.log(
          `✅ Created primary example for challenge: ${challenge.title}`,
        );

        // Create additional examples
        for (let i = 2; i <= numExamples; i++) {
          const example = {
            id: uuidv4(),
            challenge_id: challenge.id,
            input: `Example input ${i} for ${challenge.title}`,
            output: `Example output ${i} for ${challenge.title}`,
            explanation: `This is explanation ${i} for how the solution works.`,
            order_index: i,
            created_at: new Date(),
            updated_at: new Date(),
          };

          await prisma.challengeExample.create({
            data: example,
          });

          createdCount++;
          console.log(
            `✅ Created additional example ${i} for challenge: ${challenge.title}`,
          );
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process examples for ${challenge.title}:`,
          error,
        );
      }
    }

    console.log('\n✅ CHALLENGE EXAMPLE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding challenge examples:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  seedChallengeExamples().catch((error) => {
    console.error('Failed to seed challenge examples:', error);
    process.exit(1);
  });
}

export { seedChallengeExamples };
