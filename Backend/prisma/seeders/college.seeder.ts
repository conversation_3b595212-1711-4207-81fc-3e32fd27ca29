import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const colleges = [
  {
    name: 'Indian Institute of Technology, Bombay',
    location: 'Mumbai, Maharashtra',
    website: 'https://www.iitb.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/1/1d/IIT_Bombay_Logo.svg/1200px-IIT_Bombay_Logo.svg.png',
    description: 'One of the premier engineering institutions in India.',
  },
  {
    name: 'Indian Institute of Technology, Delhi',
    location: 'New Delhi, Delhi',
    website: 'https://www.iitd.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/f/fd/Indian_Institute_of_Technology_Delhi_Logo.svg/1200px-Indian_Institute_of_Technology_Delhi_Logo.svg.png',
    description: 'A leading public technical and research university.',
  },
  {
    name: 'Indian Institute of Technology, Madras',
    location: 'Chennai, Tamil Nadu',
    website: 'https://www.iitm.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/6/69/IIT_Madras_Logo.svg/1200px-IIT_Madras_Logo.svg.png',
    description: 'Known for its technical education, research, and innovation.',
  },
  {
    name: 'Indian Institute of Technology, Kanpur',
    location: 'Kanpur, Uttar Pradesh',
    website: 'https://www.iitk.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/1/1c/IIT_Kanpur_Logo.svg/1200px-IIT_Kanpur_Logo.svg.png',
    description:
      'Established in 1959, one of the first IITs to be established.',
  },
  {
    name: 'Indian Institute of Technology, Kharagpur',
    location: 'Kharagpur, West Bengal',
    website: 'https://www.iitkgp.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/1/1c/IIT_Kharagpur_Logo.svg/1200px-IIT_Kharagpur_Logo.svg.png',
    description: 'The first IIT to be established in 1951.',
  },
  {
    name: 'National Institute of Technology, Tiruchirappalli',
    location: 'Tiruchirappalli, Tamil Nadu',
    website: 'https://www.nitt.edu',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/3/3a/NIT_Trichy_logo.png/220px-NIT_Trichy_logo.png',
    description: 'One of the premier engineering institutions in India.',
  },
  {
    name: 'Birla Institute of Technology and Science, Pilani',
    location: 'Pilani, Rajasthan',
    website: 'https://www.bits-pilani.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/d/d3/BITS_Pilani-Logo.svg/1200px-BITS_Pilani-Logo.svg.png',
    description: 'A leading private university in India.',
  },
  {
    name: 'Delhi Technological University',
    location: 'New Delhi, Delhi',
    website: 'https://www.dtu.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/b/b5/DTU_logo.png/220px-DTU_logo.png',
    description: 'Formerly known as Delhi College of Engineering.',
  },
  {
    name: 'Vellore Institute of Technology',
    location: 'Vellore, Tamil Nadu',
    website: 'https://www.vit.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/4/4e/Vellore_Institute_of_Technology_seal_2017.svg/1200px-Vellore_Institute_of_Technology_seal_2017.svg.png',
    description: 'A private research university.',
  },
  {
    name: 'SRM Institute of Science and Technology',
    location: 'Chennai, Tamil Nadu',
    website: 'https://www.srmist.edu.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/0/08/SRM_University_Logo.svg/1200px-SRM_University_Logo.svg.png',
    description: 'A private university with multiple campuses.',
  },
  {
    name: 'Manipal Institute of Technology',
    location: 'Manipal, Karnataka',
    website: 'https://manipal.edu/mit.html',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/8/88/Manipal_University_logo.svg/1200px-Manipal_University_logo.svg.png',
    description:
      'A constituent institution of Manipal Academy of Higher Education.',
  },
  {
    name: 'Indian Institute of Information Technology, Allahabad',
    location: 'Allahabad, Uttar Pradesh',
    website: 'https://www.iiita.ac.in',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/2/2e/IIIT_Allahabad_Logo.png/220px-IIIT_Allahabad_Logo.png',
    description: 'A premier institute focusing on information technology.',
  },
];

async function seedColleges() {
  console.log('\n==================================================');
  console.log('🌱 COLLEGE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    console.log(`ℹ️  Found ${colleges.length} colleges to process`);

    for (const college of colleges) {
      try {
        const existingCollege = await prisma.college.findFirst({
          where: { name: college.name },
        });

        if (existingCollege) {
          await prisma.college.update({
            where: { id: existingCollege.id },
            data: college,
          });
          updatedCount++;
          console.log(`🔄 Updated college: ${college.name}`);
        } else {
          await prisma.college.create({
            data: {
              id: uuidv4(),
              ...college,
              created_at: new Date(),
              updated_at: new Date(),
            },
          });
          createdCount++;
          console.log(`✅ Created college: ${college.name}`);
        }
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to process college ${college.name}:`, error);
      }
    }

    console.log('\n✅ COLLEGE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding colleges:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  seedColleges().catch((error) => {
    console.error('Failed to seed colleges:', error);
    process.exit(1);
  });
}

export { seedColleges };
