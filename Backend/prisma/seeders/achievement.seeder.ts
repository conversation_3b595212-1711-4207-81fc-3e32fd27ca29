/**
 * @file achievement.seeder.ts
 * @description Seeder for creating achievements
 */
import { v4 as uuidv4 } from 'uuid';

import {
  AchievementCategory,
  AchievementTriggerType,
  PrismaClient,
} from '@prisma/client';

const prisma = new PrismaClient();

const achievements = [
  // Challenge achievements
  {
    name: 'Challenge Novice',
    description: 'Complete 5 challenges',
    category: AchievementCategory.CHALLENGE,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2620/2620559.png',
    is_active: true,
    is_hidden: false,
    points: 10,
    tier: 1,
    trigger_type: AchievementTriggerType.CHALLENGE_COUNT,
    trigger_value: 5,
  },
  {
    name: 'Challenge Adept',
    description: 'Complete 25 challenges',
    category: AchievementCategory.CHALLENGE,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2620/2620576.png',
    is_active: true,
    is_hidden: false,
    points: 30,
    tier: 2,
    trigger_type: AchievementTriggerType.CHALLENGE_COUNT,
    trigger_value: 25,
  },
  {
    name: 'Challenge Master',
    description: 'Complete 100 challenges',
    category: AchievementCategory.CHALLENGE,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2620/2620598.png',
    is_active: true,
    is_hidden: false,
    points: 100,
    tier: 3,
    trigger_type: AchievementTriggerType.CHALLENGE_COUNT,
    trigger_value: 100,
  },
  {
    name: 'Perfect Solution',
    description:
      'Submit a solution that passes all test cases on the first try',
    category: AchievementCategory.CHALLENGE,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/1533/1533913.png',
    is_active: true,
    is_hidden: false,
    points: 20,
    tier: 2,
    trigger_type: AchievementTriggerType.CHALLENGE_COMPLETION,
    trigger_value: 1,
  },

  // Streak achievements
  {
    name: 'Consistent Learner',
    description: 'Maintain a learning streak for 7 days',
    category: AchievementCategory.STREAK,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2617/2617812.png',
    is_active: true,
    is_hidden: false,
    points: 15,
    tier: 1,
    trigger_type: AchievementTriggerType.STREAK_DAYS,
    trigger_value: 7,
  },
  {
    name: 'Dedicated Student',
    description: 'Maintain a learning streak for 30 days',
    category: AchievementCategory.STREAK,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2617/2617835.png',
    is_active: true,
    is_hidden: false,
    points: 50,
    tier: 2,
    trigger_type: AchievementTriggerType.STREAK_DAYS,
    trigger_value: 30,
  },
  {
    name: 'Learning Machine',
    description: 'Maintain a learning streak for 100 days',
    category: AchievementCategory.STREAK,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2617/2617864.png',
    is_active: true,
    is_hidden: false,
    points: 150,
    tier: 3,
    trigger_type: AchievementTriggerType.STREAK_DAYS,
    trigger_value: 100,
  },

  // Social achievements
  {
    name: 'Discussion Starter',
    description: 'Start 5 discussions on challenges',
    category: AchievementCategory.SOCIAL,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/1946/1946429.png',
    is_active: true,
    is_hidden: false,
    points: 10,
    tier: 1,
    trigger_type: AchievementTriggerType.DISCUSSION_COUNT,
    trigger_value: 5,
  },
  {
    name: 'Community Helper',
    description: 'Have your solutions viewed by 10 other users',
    category: AchievementCategory.SOCIAL,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/1946/1946488.png',
    is_active: true,
    is_hidden: false,
    points: 20,
    tier: 2,
    trigger_type: AchievementTriggerType.SOLUTION_VIEWS,
    trigger_value: 10,
  },
  {
    name: 'Community Leader',
    description: 'Have your solutions viewed by 50 other users',
    category: AchievementCategory.SOCIAL,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/1946/1946511.png',
    is_active: true,
    is_hidden: false,
    points: 50,
    tier: 3,
    trigger_type: AchievementTriggerType.SOLUTION_VIEWS,
    trigger_value: 50,
  },

  // Roadmap achievements
  {
    name: 'Roadmap Explorer',
    description: 'Complete your first roadmap',
    category: AchievementCategory.ROADMAP,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2750/2750784.png',
    is_active: true,
    is_hidden: false,
    points: 30,
    tier: 1,
    trigger_type: AchievementTriggerType.ROADMAP_COMPLETION,
    trigger_value: 1,
  },
  {
    name: 'Roadmap Adventurer',
    description: 'Complete 3 different roadmaps',
    category: AchievementCategory.ROADMAP,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2750/2750791.png',
    is_active: true,
    is_hidden: false,
    points: 60,
    tier: 2,
    trigger_type: AchievementTriggerType.ROADMAP_COMPLETION,
    trigger_value: 3,
  },
  {
    name: 'Roadmap Master',
    description: 'Complete 5 different roadmaps',
    category: AchievementCategory.ROADMAP,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2750/2750805.png',
    is_active: true,
    is_hidden: false,
    points: 100,
    tier: 3,
    trigger_type: AchievementTriggerType.ROADMAP_COMPLETION,
    trigger_value: 5,
  },

  // Battle achievements
  {
    name: 'Battle Initiate',
    description: 'Win your first coding battle',
    category: AchievementCategory.BATTLE,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2706/2706914.png',
    is_active: true,
    is_hidden: false,
    points: 20,
    tier: 1,
    trigger_type: AchievementTriggerType.BATTLE_WINS,
    trigger_value: 1,
  },
  {
    name: 'Battle Veteran',
    description: 'Win 10 coding battles',
    category: AchievementCategory.BATTLE,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2706/2706933.png',
    is_active: true,
    is_hidden: false,
    points: 50,
    tier: 2,
    trigger_type: AchievementTriggerType.BATTLE_WINS,
    trigger_value: 10,
  },
  {
    name: 'Battle Champion',
    description: 'Win 25 coding battles',
    category: AchievementCategory.BATTLE,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2706/2706962.png',
    is_active: true,
    is_hidden: false,
    points: 100,
    tier: 3,
    trigger_type: AchievementTriggerType.BATTLE_WINS,
    trigger_value: 25,
  },

  // Profile achievements
  {
    name: 'Profile Starter',
    description: 'Complete your profile with all basic information',
    category: AchievementCategory.PROFILE,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/1077/1077063.png',
    is_active: true,
    is_hidden: false,
    points: 10,
    tier: 1,
    trigger_type: AchievementTriggerType.PROFILE_COMPLETION,
    trigger_value: 1,
  },

  // Special achievements
  {
    name: 'Early Adopter',
    description: "Joined during the platform's beta phase",
    category: AchievementCategory.SPECIAL,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/1205/1205526.png',
    is_active: true,
    is_hidden: false,
    points: 50,
    tier: 1,
    trigger_type: AchievementTriggerType.MANUAL,
    trigger_value: 1,
  },
  {
    name: 'Bug Hunter',
    description: 'Reported a confirmed bug in the platform',
    category: AchievementCategory.SPECIAL,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2535/2535488.png',
    is_active: true,
    is_hidden: false,
    points: 30,
    tier: 1,
    trigger_type: AchievementTriggerType.MANUAL,
    trigger_value: 1,
  },

  // Daily topic achievements (using SPECIAL category since DAILY_TOPIC doesn't exist)
  {
    name: 'Daily Learner',
    description: 'Complete 5 daily topics',
    category: AchievementCategory.SPECIAL,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2232/2232688.png',
    is_active: true,
    is_hidden: false,
    points: 15,
    tier: 1,
    trigger_type: AchievementTriggerType.MANUAL,
    trigger_value: 5,
  },
  {
    name: 'Topic Explorer',
    description: 'Complete 20 daily topics',
    category: AchievementCategory.SPECIAL,
    icon_url: 'https://cdn-icons-png.flaticon.com/512/2232/2232701.png',
    is_active: true,
    is_hidden: false,
    points: 40,
    tier: 2,
    trigger_type: AchievementTriggerType.MANUAL,
    trigger_value: 20,
  },
];

/**
 * Seeds the database with achievements
 */
async function seedAchievements() {
  console.log('\n==================================================');
  console.log('🌱 ACHIEVEMENT SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    console.log(`ℹ️  Found ${achievements.length} achievements to process`);

    // Log the available achievement categories for debugging
    console.log(
      'Available achievement categories:',
      Object.values(AchievementCategory),
    );
    console.log(
      'Available achievement trigger types:',
      Object.values(AchievementTriggerType),
    );

    for (const achievement of achievements) {
      try {
        const existingAchievement = await prisma.achievement.findUnique({
          where: { name: achievement.name },
        });

        if (existingAchievement) {
          await prisma.achievement.update({
            where: { id: existingAchievement.id },
            data: {
              description: achievement.description,
              category: achievement.category,
              icon_url: achievement.icon_url,
              is_active: achievement.is_active,
              is_hidden: achievement.is_hidden,
              points: achievement.points,
              tier: achievement.tier,
              trigger_type: achievement.trigger_type,
              trigger_value: achievement.trigger_value,
              updated_at: new Date(),
            },
          });
          updatedCount++;
          console.log(`🔄 Updated achievement: ${achievement.name}`);
        } else {
          await prisma.achievement.create({
            data: {
              id: uuidv4(),
              name: achievement.name,
              description: achievement.description,
              category: achievement.category,
              icon_url: achievement.icon_url,
              is_active: achievement.is_active,
              is_hidden: achievement.is_hidden,
              points: achievement.points,
              tier: achievement.tier,
              trigger_type: achievement.trigger_type,
              trigger_value: achievement.trigger_value,
              created_at: new Date(),
            },
          });
          createdCount++;
          console.log(`✅ Created achievement: ${achievement.name}`);
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process achievement ${achievement.name}:`,
          error,
        );
      }
    }

    console.log('\n✅ ACHIEVEMENT SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding achievements:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedAchievements().catch((error) => {
    console.error('Failed to seed achievements:', error);
    process.exit(1);
  });
}

export { seedAchievements };
