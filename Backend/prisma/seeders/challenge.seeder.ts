import { v4 as uuidv4 } from 'uuid';

import {
  ChallengeCategory,
  ChallengeStatus,
  Difficulty,
  PrismaClient,
} from '@prisma/client';

import { codingChallenges } from '../../resources/challenges/codingChallenges';
import logger from '../../src/utils/logger';

const prisma = new PrismaClient();

/**
 * Seeds the database with coding challenges
 */
async function seedChallenges() {
  console.log('\n==================================================');
  console.log('🌱 CHALLENGE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    // Get topics for challenges
    const topics = await prisma.topic.findMany();

    if (topics.length === 0) {
      console.error('❌ No topics found. Please run the topic seeder first.');
      return;
    }

    console.log(`ℹ️  Found ${topics.length} topics for challenges`);

    // Map of category to topic
    const categoryToTopic = {
      algorithms:
        topics.find((t) => t.title.toLowerCase().includes('algorithm'))?.id ||
        topics[0].id,
      data_structures:
        topics.find((t) => t.title.toLowerCase().includes('data structure'))
          ?.id || topics[0].id,
      system_design:
        topics.find((t) => t.title.toLowerCase().includes('system design'))
          ?.id || topics[0].id,
      databases:
        topics.find((t) => t.title.toLowerCase().includes('database'))?.id ||
        topics[0].id,
      web_development:
        topics.find((t) => t.title.toLowerCase().includes('web'))?.id ||
        topics[0].id,
    };

    // Process each challenge from the coding challenges file
    for (const challenge of codingChallenges) {
      try {
        // Find a suitable topic based on the challenge category
        const topicId = categoryToTopic[challenge.category] || topics[0].id;

        // Check if challenge already exists
        const existingChallenge = await prisma.challenge.findFirst({
          where: { title: challenge.title },
        });

        if (existingChallenge) {
          // Update the challenge
          await prisma.challenge.update({
            where: { id: existingChallenge.id },
            data: {
              description: challenge.description,
              points: challenge.points,
              difficulty: challenge.difficulty,
              category: challenge.category,
              input_format: challenge.input_format,
              output_format: challenge.output_format,
              example_input: challenge.example_input,
              example_output: challenge.example_output,
              constraints: challenge.constraints,
              function_signature: challenge.function_signature,
              time_limit: challenge.time_limit,
              memory_limit: challenge.memory_limit,
              tags: challenge.tags,
              solutions: challenge.solutions,
              status: ChallengeStatus.ACTIVE,
              updated_at: new Date(),
            },
          });

          // Delete existing test cases
          await prisma.testCase.deleteMany({
            where: { challenge_id: existingChallenge.id },
          });

          // Create new test cases
          for (const testCase of challenge.test_cases) {
            await prisma.testCase.create({
              data: {
                id: uuidv4(),
                challenge_id: existingChallenge.id,
                input: testCase.input,
                output: testCase.output,
                is_hidden: testCase.is_hidden || false,
                order_index: 0, // Will be updated later
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
          }

          // Delete existing boilerplates
          await prisma.challengeBoilerplate.deleteMany({
            where: { challenge_id: existingChallenge.id },
          });

          // Create boilerplates if they exist
          if (challenge.solutions) {
            for (const [language, solution] of Object.entries(
              challenge.solutions,
            )) {
              await prisma.challengeBoilerplate.create({
                data: {
                  id: uuidv4(),
                  challenge_id: existingChallenge.id,
                  language: language,
                  boilerplate_code:
                    typeof solution === 'string'
                      ? solution
                      : JSON.stringify(solution),
                  created_at: new Date(),
                  updated_at: new Date(),
                },
              });
            }
          }

          // Create examples
          await prisma.challengeExample.deleteMany({
            where: { challenge_id: existingChallenge.id },
          });

          await prisma.challengeExample.create({
            data: {
              id: uuidv4(),
              challenge_id: existingChallenge.id,
              input: challenge.example_input,
              output: challenge.example_output,
              explanation: challenge.description.split('.')[0] + '.',
              order_index: 1,
              created_at: new Date(),
              updated_at: new Date(),
            },
          });

          updatedCount++;
          console.log(`🔄 Updated challenge: ${challenge.title}`);
        } else {
          // Create a new challenge
          const newChallenge = await prisma.challenge.create({
            data: {
              id: uuidv4(),
              title: challenge.title,
              description: challenge.description,
              points: challenge.points,
              difficulty: challenge.difficulty,
              category: challenge.category,
              input_format: challenge.input_format,
              output_format: challenge.output_format,
              example_input: challenge.example_input,
              example_output: challenge.example_output,
              constraints: challenge.constraints,
              function_signature: challenge.function_signature,
              time_limit: challenge.time_limit,
              memory_limit: challenge.memory_limit,
              tags: challenge.tags,
              solutions: challenge.solutions,
              topic_id: topicId,
              status: ChallengeStatus.ACTIVE,
              created_at: new Date(),
              updated_at: new Date(),
            },
          });

          // Create test cases
          for (let i = 0; i < challenge.test_cases.length; i++) {
            const testCase = challenge.test_cases[i];
            await prisma.testCase.create({
              data: {
                id: uuidv4(),
                challenge_id: newChallenge.id,
                input: testCase.input,
                output: testCase.output,
                is_hidden: testCase.is_hidden || false,
                order_index: i + 1,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
          }

          // Create boilerplates if they exist
          if (challenge.solutions) {
            for (const [language, solution] of Object.entries(
              challenge.solutions,
            )) {
              await prisma.challengeBoilerplate.create({
                data: {
                  id: uuidv4(),
                  challenge_id: newChallenge.id,
                  language: language,
                  boilerplate_code:
                    typeof solution === 'string'
                      ? solution
                      : JSON.stringify(solution),
                  created_at: new Date(),
                  updated_at: new Date(),
                },
              });
            }
          }

          // Create examples
          await prisma.challengeExample.create({
            data: {
              id: uuidv4(),
              challenge_id: newChallenge.id,
              input: challenge.example_input,
              output: challenge.example_output,
              explanation: challenge.description.split('.')[0] + '.',
              order_index: 1,
              created_at: new Date(),
              updated_at: new Date(),
            },
          });

          createdCount++;
          console.log(`✅ Created challenge: ${challenge.title}`);
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process challenge ${challenge.title}:`,
          error,
        );
      }
    }

    // Add some additional challenges with hints
    try {
      const additionalChallenges = [
        {
          title: 'System Design: URL Shortener',
          description: 'Design a URL shortening service like TinyURL.',
          points: 400,
          difficulty: Difficulty.HARD,
          category: ChallengeCategory.system_design,
          constraints:
            'The system should handle high traffic and provide analytics.',
          tags: ['system-design', 'scalability', 'databases'],
          status: ChallengeStatus.ACTIVE,
          example_input: 'URL shortener service requirements',
          example_output: 'System design document',
          function_signature: 'N/A (Submit design document)',
          input_format: 'System requirements for a URL shortener service.',
          output_format: 'System design document with architecture diagrams.',
          time_limit: null,
          memory_limit: null,
          topic_id: topics[0]?.id || '',
          explanation:
            'The design should include components for URL shortening, redirection, analytics, and scaling considerations.',
        },
        {
          title: 'Implement a Cache',
          description:
            'Implement an LRU (Least Recently Used) cache with O(1) time complexity for both get and put operations.',
          points: 350,
          difficulty: Difficulty.HARD,
          category: ChallengeCategory.data_structures,
          constraints:
            'The cache should have a fixed capacity and evict the least recently used item when full.',
          tags: ['cache', 'hash-table', 'linked-list'],
          status: ChallengeStatus.ACTIVE,
          example_input:
            'LRUCache cache = new LRUCache(2); cache.put(1, 1); cache.put(2, 2); cache.get(1); cache.put(3, 3); cache.get(2); cache.put(4, 4); cache.get(1); cache.get(3); cache.get(4);',
          example_output: '[1, -1, -1, 3, 4]',
          function_signature:
            'class LRUCache { constructor(capacity: number); get(key: number): number; put(key: number, value: number): void; }',
          input_format: 'A series of operations on the LRU cache.',
          output_format: 'The results of the get operations.',
          time_limit: 1000,
          memory_limit: 128000,
          topic_id: topics[0]?.id || '',
          explanation:
            'The LRU cache should maintain items in order of usage and evict the least recently used item when full.',
        },
      ];

      for (const challenge of additionalChallenges) {
        const existingChallenge = await prisma.challenge.findFirst({
          where: { title: challenge.title },
        });

        if (!existingChallenge) {
          const newChallenge = await prisma.challenge.create({
            data: {
              id: uuidv4(),
              ...challenge,
              created_at: new Date(),
              updated_at: new Date(),
            },
          });

          // Create hints for the challenge
          await prisma.challengeHint.createMany({
            skipDuplicates: true,
            data: [
              {
                id: uuidv4(),
                challenge_id: newChallenge.id,
                content: 'Consider the key components needed for this system.',
                difficulty: 'BEGINNER',
                order: 1,
                point_penalty: 5,
                created_at: new Date(),
                updated_at: new Date(),
              },
              {
                id: uuidv4(),
                challenge_id: newChallenge.id,
                content: 'Think about how to handle collisions and scaling.',
                difficulty: 'INTERMEDIATE',
                order: 2,
                point_penalty: 10,
                created_at: new Date(),
                updated_at: new Date(),
              },
              {
                id: uuidv4(),
                challenge_id: newChallenge.id,
                content:
                  'Consider using a combination of hash tables and linked lists for optimal performance.',
                difficulty: 'ADVANCED',
                order: 3,
                point_penalty: 15,
                created_at: new Date(),
                updated_at: new Date(),
              },
            ],
          });

          console.log(
            `✅ Created additional challenge with hints: ${challenge.title}`,
          );
          createdCount++;
        }
      }
    } catch (error) {
      console.error('❌ Failed to create additional challenges:', error);
    }

    console.log('\n✅ CHALLENGE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding challenges:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  seedChallenges().catch((error) => {
    console.error('Failed to seed challenges:', error);
    process.exit(1);
  });
}

export { seedChallenges };
