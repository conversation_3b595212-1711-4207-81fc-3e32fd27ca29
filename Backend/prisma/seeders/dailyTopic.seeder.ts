/**
 * @file dailyTopic.seeder.ts
 * @description Seeder for creating daily topics with better topic distribution
 */
import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds the database with daily topics for the next 60 days
 */
async function seedDailyTopics() {
  console.log('\n==================================================');
  console.log('🌱 DAILY TOPIC SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    // Get topics to create daily topics from
    const topics = await prisma.topic.findMany({
      orderBy: {
        order: 'asc',
      },
    });

    if (topics.length === 0) {
      console.error('❌ No topics found. Please run the topic seeder first.');
      return;
    }

    console.log(`ℹ️  Found ${topics.length} topics for daily topics`);

    // Create daily topics for the next 60 days with better distribution
    const startDate = new Date();
    startDate.setHours(0, 0, 0, 0); // Normalize to start of day

    // Group topics by subject for better organization
    const topicsBySubject = new Map();

    // Get all subject-topic relationships
    const subjectTopics = await prisma.subjectTopic.findMany({
      include: {
        subject: true,
        topic: true,
      },
      orderBy: {
        order: 'asc',
      },
    });

    // Organize topics by subject
    for (const st of subjectTopics) {
      if (!topicsBySubject.has(st.subject_id)) {
        topicsBySubject.set(st.subject_id, {
          subject: st.subject,
          topics: [],
        });
      }
      topicsBySubject.get(st.subject_id).topics.push(st.topic);
    }

    // If no subject-topic relationships, fall back to random topics
    const subjectKeys = Array.from(topicsBySubject.keys());

    // Create daily topics for the next 60 days
    for (let i = 0; i < 60; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);

      try {
        // Select topic based on day of week and subject rotation
        let selectedTopic;

        if (subjectKeys.length > 0) {
          // Rotate through subjects
          const subjectIndex = i % subjectKeys.length;
          const subjectId = subjectKeys[subjectIndex];
          const subjectData = topicsBySubject.get(subjectId);

          // Select a topic from this subject
          const topicIndex =
            Math.floor(i / subjectKeys.length) % subjectData.topics.length;
          selectedTopic = subjectData.topics[topicIndex];
        } else {
          // Fallback to random selection if no subject organization
          const topicIndex = i % topics.length;
          selectedTopic = topics[topicIndex];
        }

        // Format date for display
        const formattedDate = date.toISOString().split('T')[0];

        // Check if a daily topic already exists for this date
        const existingDailyTopic = await prisma.dailyTopic.findUnique({
          where: { date: date },
        });

        if (existingDailyTopic) {
          await prisma.dailyTopic.update({
            where: { id: existingDailyTopic.id },
            data: {
              topic_id: selectedTopic.id,
            },
          });
          updatedCount++;
          console.log(
            `🔄 Updated daily topic for ${formattedDate}: ${selectedTopic.title}`,
          );
        } else {
          await prisma.dailyTopic.create({
            data: {
              id: uuidv4(),
              topic_id: selectedTopic.id,
              date: date,
              created_at: new Date(),
            },
          });
          createdCount++;
          console.log(
            `✅ Created daily topic for ${formattedDate}: ${selectedTopic.title}`,
          );
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process daily topic for ${date.toISOString().split('T')[0]}:`,
          error,
        );
      }
    }

    console.log('\n✅ DAILY TOPIC SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding daily topics:', error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Creates sample completions and views for daily topics
 */
async function createSampleActivity() {
  try {
    console.log('\n🔄 Creating sample daily topic activity...');

    // Get users
    const users = await prisma.user.findMany({
      take: 5,
    });

    if (users.length === 0) {
      console.log('⚠️ No users found, skipping sample activity creation');
      return;
    }

    // Get daily topics
    const dailyTopics = await prisma.dailyTopic.findMany({
      take: 10,
      orderBy: {
        date: 'asc',
      },
    });

    if (dailyTopics.length === 0) {
      console.log(
        '⚠️ No daily topics found, skipping sample activity creation',
      );
      return;
    }

    // Create views and completions
    let viewCount = 0;
    let completionCount = 0;

    for (const topic of dailyTopics) {
      for (const user of users) {
        // 70% chance of viewing a topic
        if (Math.random() < 0.7) {
          try {
            await prisma.dailyTopicView.create({
              data: {
                id: uuidv4(),
                daily_topic_id: topic.id,
                user_id: user.id,
                view_count: Math.floor(Math.random() * 5) + 1,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
            viewCount++;
          } catch (error) {
            // Skip if unique constraint violated
            if (
              !(
                error instanceof Error &&
                error.message.includes('Unique constraint')
              )
            ) {
              console.error('Error creating view:', error);
            }
          }

          // 50% chance of completing a viewed topic
          if (Math.random() < 0.5) {
            try {
              await prisma.dailyTopicCompletion.create({
                data: {
                  id: uuidv4(),
                  daily_topic_id: topic.id,
                  user_id: user.id,
                  time_spent: Math.floor(Math.random() * 30) + 5,
                  created_at: new Date(),
                },
              });
              completionCount++;
            } catch (error) {
              console.error('Error creating completion:', error);
            }
          }
        }
      }
    }

    console.log(
      `✅ Created ${viewCount} topic views and ${completionCount} completions`,
    );
  } catch (error) {
    console.error('❌ Error creating sample activity:', error);
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedDailyTopics()
    .then(() => createSampleActivity())
    .then(() => {
      console.log('Daily topic seeder completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Failed to seed daily topics:', error);
      process.exit(1);
    });
}

export { seedDailyTopics };
