/**
 * @file auth-system.seeder.ts
 * @description Main seeder for the role-based authentication system
 */
import { PrismaClient } from '@prisma/client';

import { seedFeatures } from './feature.seeder';
import { seedPermissions } from './permission.seeder';
import { seedRolePermissions } from './role-permission.seeder';
// Import seeders
import { assignDefaultRolesToUsers, seedRoles } from './role.seeder';

const prisma = new PrismaClient();

/**
 * Main function to run all auth system seeders in the correct order
 */
async function seedAuthSystem() {
  console.log('\n==================================================');
  console.log('🔐 AUTH SYSTEM SEEDER STARTED');
  console.log('==================================================');
  console.log(
    'This seeder will set up the complete role-based authentication system',
  );
  console.log('Running seeders in the following order:');
  console.log('1. Roles');
  console.log('2. Permissions');
  console.log('3. Features');
  console.log('4. Role-Permission assignments');
  console.log('--------------------------------------------------');

  try {
    // 1. Seed roles first
    console.log('\n🔹 STEP 1: SEEDING ROLES');
    await seedRoles();

    // 1.1 Assign default roles to users
    console.log('\n🔹 STEP 1.1: ASSIGNING DEFAULT ROLES TO USERS');
    await assignDefaultRolesToUsers();

    // 2. Seed permissions
    console.log('\n🔹 STEP 2: SEEDING PERMISSIONS');
    await seedPermissions();

    // 3. Seed features
    console.log('\n🔹 STEP 3: SEEDING FEATURES');
    await seedFeatures();

    // 4. Seed role-permission assignments
    console.log('\n🔹 STEP 4: ASSIGNING PERMISSIONS TO ROLES');
    await seedRolePermissions();

    console.log('\n==================================================');
    console.log('✅ AUTH SYSTEM SEEDER COMPLETED SUCCESSFULLY');
    console.log('==================================================');
    console.log(
      'The role-based authentication system has been set up successfully.',
    );
    console.log('You can now use the following roles:');
    console.log('- ADMIN: Full access to all system features');
    console.log('- MODERATOR: Can moderate content and view users');
    console.log('- CONTRIBUTOR: Can create and edit content');
    console.log('- USER: Basic access to view content');
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error in auth system seeding process:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the main function if this file is run directly
if (require.main === module) {
  seedAuthSystem();
}

export { seedAuthSystem };
