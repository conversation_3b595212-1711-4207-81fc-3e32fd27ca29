/**
 * @file bookmarkCollection.seeder.ts
 * @description Seeder for creating bookmark collections and adding challenges to them
 */
import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds the database with bookmark collections and their challenges
 */
export async function seedBookmarkCollections() {
  console.log('🌱 Seeding bookmark collections...');

  try {
    // Get users to create bookmark collections for
    const users = await prisma.user.findMany({
      take: 20, // Limit to 20 users to avoid creating too much data
    });

    if (users.length === 0) {
      console.error('❌ No users found. Please run the user seeder first.');
      return;
    }

    // Get challenges to add to collections
    const challenges = await prisma.challenge.findMany();

    if (challenges.length === 0) {
      console.error(
        '❌ No challenges found. Please run the challenge seeder first.',
      );
      return;
    }

    // Get coding challenges to add to collections
    const codingChallenges = await prisma.codingChallenge.findMany();

    if (codingChallenges.length === 0) {
      console.error(
        '❌ No coding challenges found. Please run the coding challenge seeder first.',
      );
      return;
    }

    console.log(
      `✅ Found ${users.length} users, ${challenges.length} challenges, and ${codingChallenges.length} coding challenges for creating bookmark collections`,
    );

    /**
     * Generate a random date within a range
     * @param start The start date
     * @param end The end date
     * @returns A random date between start and end
     */
    const randomDate = (start: Date, end: Date): Date => {
      return new Date(
        start.getTime() + Math.random() * (end.getTime() - start.getTime()),
      );
    };

    /**
     * Generate a collection name
     * @returns A random collection name
     */
    const generateCollectionName = (): string => {
      const prefixes = [
        'My',
        'Favorite',
        'Important',
        'Must-Solve',
        'Top',
        'Essential',
        'Interesting',
        'Challenging',
        'Practice',
        'Learning',
        'Review',
        'Interview Prep',
        'Advanced',
        'Beginner',
        'Intermediate',
      ];

      const topics = [
        'Algorithms',
        'Data Structures',
        'Problems',
        'Challenges',
        'Coding Exercises',
        'Programming Tasks',
        'Puzzles',
        'Questions',
        'Coding Problems',
        'Technical Challenges',
        'Coding Challenges',
        'Interview Questions',
        'LeetCode Problems',
        'HackerRank Problems',
        'Project Euler Problems',
      ];

      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
      const topic = topics[Math.floor(Math.random() * topics.length)];

      return `${prefix} ${topic}`;
    };

    /**
     * Generate a collection description
     * @param name The collection name
     * @returns A random collection description
     */
    const generateCollectionDescription = (name: string): string => {
      const descriptions = [
        `A collection of ${name.toLowerCase()} that I want to solve.`,
        `My personal list of ${name.toLowerCase()} for practice and learning.`,
        `Curated selection of ${name.toLowerCase()} to improve my skills.`,
        `Important ${name.toLowerCase()} that I need to review regularly.`,
        `A set of ${name.toLowerCase()} that I find particularly interesting or challenging.`,
        `Collection of ${name.toLowerCase()} for interview preparation.`,
        `${name} that I want to revisit in the future.`,
        `Handpicked ${name.toLowerCase()} to enhance my problem-solving abilities.`,
        `My go-to list of ${name.toLowerCase()} for skill development.`,
        `A comprehensive collection of ${name.toLowerCase()} covering various difficulty levels.`,
      ];

      return descriptions[Math.floor(Math.random() * descriptions.length)];
    };

    // Create bookmark collections for users
    console.log('🌱 Creating bookmark collections...');

    let collectionCount = 0;
    let bookmarkCount = 0;

    // For each user, create 1-3 bookmark collections
    for (const user of users) {
      try {
        // Determine how many collections to create for this user (1-3)
        const numCollections = Math.floor(Math.random() * 3) + 1;

        // Generate random dates for the collections (within the last year)
        const now = new Date();
        const oneYearAgo = new Date(now);
        oneYearAgo.setFullYear(now.getFullYear() - 1);

        for (let i = 0; i < numCollections; i++) {
          try {
            // Generate a random name and description for the collection
            const name = generateCollectionName();
            const description = generateCollectionDescription(name);

            // Generate a random date for the collection creation
            const creationDate = randomDate(oneYearAgo, now);

            // Create the bookmark collection
            const collection = await prisma.bookmarkCollection.create({
              data: {
                id: uuidv4(),
                user_id: user.id,
                name,
                description,
                created_at: creationDate,
                updated_at: creationDate,
              },
            });

            collectionCount++;

            // Determine how many challenges to add to this collection (5-15)
            const numChallenges = Math.floor(Math.random() * 11) + 5;

            // Combine regular challenges and coding challenges
            const allChallenges = [
              ...challenges.map((challenge) => ({
                id: challenge.id,
                type: 'CHALLENGE',
              })),
              ...codingChallenges.map((challenge) => ({
                id: challenge.id,
                type: 'CODING_CHALLENGE',
              })),
            ];

            // Randomly select challenges for this collection
            const shuffledChallenges = [...allChallenges].sort(
              () => 0.5 - Math.random(),
            );
            const selectedChallenges = shuffledChallenges.slice(
              0,
              numChallenges,
            );

            // Add challenges to the collection
            for (const challenge of selectedChallenges) {
              try {
                // Generate a random date for the bookmark (after collection creation, before now)
                const bookmarkDate = randomDate(creationDate, now);

                // Create the bookmark
                await prisma.bookmark.create({
                  data: {
                    id: uuidv4(),
                    collection_id: collection.id,
                    challenge_id:
                      challenge.type === 'CHALLENGE' ? challenge.id : null,
                    coding_challenge_id:
                      challenge.type === 'CODING_CHALLENGE'
                        ? challenge.id
                        : null,
                    created_at: bookmarkDate,
                    updated_at: bookmarkDate,
                  },
                });

                bookmarkCount++;
              } catch (error) {
                console.error(
                  `❌ Error adding challenge to collection "${name}":`,
                  error,
                );
              }
            }
          } catch (error) {
            console.error(
              `❌ Error creating bookmark collection for user "${user.email}":`,
              error,
            );
          }
        }
      } catch (error) {
        console.error(
          `❌ Error creating bookmark collections for user "${user.email}":`,
          error,
        );
      }
    }

    console.log(
      `✅ Successfully created ${collectionCount} bookmark collections with ${bookmarkCount} bookmarks`,
    );
  } catch (error) {
    console.error('❌ Error seeding bookmark collections:', error);
  }
}
