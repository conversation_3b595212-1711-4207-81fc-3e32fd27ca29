/**
 * @file roadmap.seeder.ts
 * @description Seeder for creating learning roadmaps and their relationships
 */
import { v4 as uuidv4 } from 'uuid';

import { Difficulty, PrismaClient, ProgressStatus } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds the database with roadmaps and their relationships
 */
async function seedRoadmaps() {
  console.log('\n==================================================');
  console.log('🌱 ROADMAP SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    // Get users for roadmap creators
    const users = await prisma.user.findMany({ take: 5 });

    if (users.length === 0) {
      console.error('❌ No users found. Please run the user seeder first.');
      return;
    }

    // Get topics to include in roadmaps
    const topics = await prisma.topic.findMany();

    if (topics.length === 0) {
      console.error('❌ No topics found. Please run the topic seeder first.');
      return;
    }

    // Get main concepts to include in roadmaps
    const mainConcepts = await prisma.mainConcept.findMany();

    if (mainConcepts.length === 0) {
      console.log('⚠️ No main concepts found. Creating some main concepts...');

      // Create some main concepts if none exist
      const conceptsToCreate = [
        {
          name: 'Frontend Development',
          description: 'Building user interfaces and client-side applications',
          order: 1,
        },
        {
          name: 'Backend Development',
          description: 'Server-side programming and API development',
          order: 2,
        },
        {
          name: 'Database Design',
          description: 'Designing and optimizing database structures',
          order: 3,
        },
        {
          name: 'DevOps',
          description: 'Deployment, automation, and infrastructure management',
          order: 4,
        },
        {
          name: 'Mobile Development',
          description: 'Building applications for mobile devices',
          order: 5,
        },
        {
          name: 'Machine Learning',
          description: 'Algorithms and models for data analysis and prediction',
          order: 6,
        },
      ];

      for (const concept of conceptsToCreate) {
        await prisma.mainConcept.create({
          data: {
            id: uuidv4(),
            name: concept.name,
            description: concept.description,
            order: concept.order,
            created_at: new Date(),
            updated_at: new Date(),
          },
        });
      }

      // Fetch the newly created concepts
      const newMainConcepts = await prisma.mainConcept.findMany();
      console.log(`✅ Created ${newMainConcepts.length} main concepts`);
    }

    // Create roadmap categories
    const categories = [
      {
        name: 'Web Development',
        description: 'Paths for learning web development',
      },
      {
        name: 'Mobile Development',
        description: 'Paths for learning mobile app development',
      },
      {
        name: 'Data Science',
        description: 'Paths for learning data science and analytics',
      },
      {
        name: 'DevOps',
        description: 'Paths for learning DevOps practices and tools',
      },
      {
        name: 'Cloud Computing',
        description: 'Paths for learning cloud platforms and services',
      },
    ];

    const createdCategories = [];
    for (const category of categories) {
      const existingCategory = await prisma.roadmapCategory.findUnique({
        where: { name: category.name },
      });

      if (existingCategory) {
        createdCategories.push(existingCategory);
      } else {
        const newCategory = await prisma.roadmapCategory.create({
          data: {
            id: uuidv4(),
            name: category.name,
            description: category.description,
            created_at: new Date(),
            updated_at: new Date(),
          },
        });
        createdCategories.push(newCategory);
        console.log(`✅ Created roadmap category: ${category.name}`);
      }
    }

    // Sample roadmap data
    const roadmapData = [
      {
        title: 'Frontend Developer Path',
        description:
          'A comprehensive roadmap to become a frontend developer, covering HTML, CSS, JavaScript, and modern frameworks.',
        difficulty: Difficulty.MEDIUM,
        estimatedHours: 120,
        category: 'Web Development',
        tags: 'frontend,javascript,react,html,css',
        is_public: true,
      },
      {
        title: 'Backend Developer Path',
        description:
          'Learn server-side programming, APIs, databases, and everything needed to become a backend developer.',
        difficulty: Difficulty.MEDIUM,
        estimatedHours: 150,
        category: 'Web Development',
        tags: 'backend,nodejs,express,databases,api',
        is_public: true,
      },
      {
        title: 'Full Stack Developer Path',
        description:
          'Master both frontend and backend development to become a versatile full stack developer.',
        difficulty: Difficulty.HARD,
        estimatedHours: 200,
        category: 'Web Development',
        tags: 'fullstack,javascript,nodejs,react,databases',
        is_public: true,
      },
      {
        title: 'DevOps Engineer Path',
        description:
          'Learn the tools and practices for implementing DevOps in your organization.',
        difficulty: Difficulty.HARD,
        estimatedHours: 180,
        category: 'DevOps',
        tags: 'devops,ci/cd,docker,kubernetes,automation',
        is_public: true,
      },
      {
        title: 'Mobile App Developer Path',
        description:
          'Build cross-platform mobile applications using modern frameworks and tools.',
        difficulty: Difficulty.MEDIUM,
        estimatedHours: 140,
        category: 'Mobile Development',
        tags: 'mobile,react-native,flutter,ios,android',
        is_public: true,
      },
      {
        title: 'Data Scientist Path',
        description:
          'Master data analysis, machine learning, and visualization to become a data scientist.',
        difficulty: Difficulty.HARD,
        estimatedHours: 220,
        category: 'Data Science',
        tags: 'data-science,python,machine-learning,statistics,visualization',
        is_public: true,
      },
      {
        title: 'Cloud Architect Path',
        description:
          'Learn to design and implement cloud-based solutions across major platforms.',
        difficulty: Difficulty.HARD,
        estimatedHours: 200,
        category: 'Cloud Computing',
        tags: 'cloud,aws,azure,gcp,infrastructure',
        is_public: true,
      },
    ];

    // Create roadmaps
    for (const [index, roadmap] of roadmapData.entries()) {
      try {
        // Find the category
        const category = createdCategories.find(
          (c) => c.name === roadmap.category,
        );

        // Select a user as creator
        const user = users[index % users.length];

        // Check if roadmap already exists
        const existingRoadmap = await prisma.roadmap.findUnique({
          where: { title: roadmap.title },
        });

        if (existingRoadmap) {
          // Update existing roadmap
          await prisma.roadmap.update({
            where: { id: existingRoadmap.id },
            data: {
              description: roadmap.description,
              difficulty: roadmap.difficulty,
              estimatedHours: roadmap.estimatedHours,
              category_id: category?.id,
              tags: roadmap.tags,
              is_public: roadmap.is_public,
              updated_at: new Date(),
            },
          });
          updatedCount++;
          console.log(`🔄 Updated roadmap: ${roadmap.title}`);
        } else {
          // Create new roadmap
          const newRoadmap = await prisma.roadmap.create({
            data: {
              id: uuidv4(),
              title: roadmap.title,
              description: roadmap.description,
              difficulty: roadmap.difficulty,
              estimatedHours: roadmap.estimatedHours,
              category_id: category?.id,
              tags: roadmap.tags,
              is_public: roadmap.is_public,
              user_id: user.id,
              created_at: new Date(),
              updated_at: new Date(),
              popularity: Math.floor(Math.random() * 100),
            },
          });

          // Add topics to the roadmap
          const topicsForRoadmap = topics
            .filter((topic) => {
              // Filter topics based on roadmap category
              if (
                roadmap.category === 'Web Development' &&
                topic.title.toLowerCase().includes('web')
              )
                return true;
              if (
                roadmap.category === 'Mobile Development' &&
                topic.title.toLowerCase().includes('mobile')
              )
                return true;
              if (
                roadmap.category === 'Data Science' &&
                topic.title.toLowerCase().includes('data')
              )
                return true;
              if (
                roadmap.category === 'DevOps' &&
                topic.title.toLowerCase().includes('devops')
              )
                return true;
              if (
                roadmap.category === 'Cloud Computing' &&
                topic.title.toLowerCase().includes('cloud')
              )
                return true;

              // Add some general topics
              return Math.random() > 0.7;
            })
            .slice(0, 5 + Math.floor(Math.random() * 5)); // 5-10 topics per roadmap

          // Add topics to roadmap
          for (let i = 0; i < topicsForRoadmap.length; i++) {
            await prisma.roadmapTopic.create({
              data: {
                id: uuidv4(),
                roadmap_id: newRoadmap.id,
                topic_id: topicsForRoadmap[i].id,
                order: i + 1,
                created_at: new Date(),
              },
            });
          }

          // Add main concepts to roadmap
          const conceptsForRoadmap = await prisma.mainConcept.findMany();
          for (let i = 0; i < Math.min(3, conceptsForRoadmap.length); i++) {
            await prisma.roadmapMainConcept.create({
              data: {
                id: uuidv4(),
                roadmap_id: newRoadmap.id,
                main_concept_id: conceptsForRoadmap[i].id,
                order: i + 1,
                created_at: new Date(),
              },
            });
          }

          // Add some challenges to the roadmap
          const challenges = await prisma.challenge.findMany({
            take: 5,
          });

          if (challenges.length > 0) {
            for (let i = 0; i < Math.min(5, challenges.length); i++) {
              await prisma.roadmapChallenge.create({
                data: {
                  id: uuidv4(),
                  roadmap_id: newRoadmap.id,
                  challenge_id: challenges[i].id,
                  order: i + 1,
                  is_required: Math.random() > 0.3, // 70% chance of being required
                  created_at: new Date(),
                  updated_at: new Date(),
                },
              });
            }
          }

          // Create some user roadmaps (enrollments)
          for (const enrollUser of users.slice(0, 3)) {
            if (enrollUser.id !== user.id) {
              // Don't enroll the creator
              try {
                // Check if UserRoadmap has a status field
                const userRoadmapFields = await prisma.$queryRaw`
                  SELECT column_name 
                  FROM information_schema.columns 
                  WHERE table_name = 'UserRoadmap' 
                  AND column_name = 'status'
                `;

                // Create user roadmap with or without status field
                if (
                  Array.isArray(userRoadmapFields) &&
                  userRoadmapFields.length > 0
                ) {
                  // Status field exists
                  await prisma.userRoadmap.create({
                    data: {
                      id: uuidv4(),
                      user_id: enrollUser.id,
                      roadmap_id: newRoadmap.id,
                      is_custom: false,
                      status:
                        Math.random() > 0.7
                          ? ProgressStatus.COMPLETED
                          : Math.random() > 0.5
                            ? ProgressStatus.IN_PROGRESS
                            : ProgressStatus.PENDING,
                      created_at: new Date(),
                      updated_at: new Date(),
                    },
                  });
                } else {
                  // Status field doesn't exist
                  await prisma.userRoadmap.create({
                    data: {
                      id: uuidv4(),
                      user_id: enrollUser.id,
                      roadmap_id: newRoadmap.id,
                      is_custom: false,
                      created_at: new Date(),
                      updated_at: new Date(),
                    },
                  });
                }

                console.log(
                  `✅ Enrolled user ${enrollUser.username} in roadmap: ${roadmap.title}`,
                );
              } catch (error) {
                console.log(
                  `⚠️ Could not enroll user in roadmap: ${error.message}`,
                );
              }
            }
          }

          createdCount++;
          console.log(
            `✅ Created roadmap: ${roadmap.title} with ${topicsForRoadmap.length} topics`,
          );
        }
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to process roadmap ${roadmap.title}:`, error);
      }
    }

    console.log('\n✅ ROADMAP SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding roadmaps:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedRoadmaps().catch((error) => {
    console.error('Failed to seed roadmaps:', error);
    process.exit(1);
  });
}

export { seedRoadmaps };
