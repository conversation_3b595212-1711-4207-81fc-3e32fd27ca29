/**
 * @file resource.seeder.ts
 * @description Seeder for creating sample educational resources
 */
import { v4 as uuidv4 } from 'uuid';

import { Difficulty, PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Resource types available in the system
 */
const RESOURCE_TYPES = [
  'video',
  'article',
  'tutorial',
  'documentation',
  'book',
  'course',
  'tool',
  'repository',
  'podcast',
  'cheatsheet',
];

/**
 * Resource categories
 */
const RESOURCE_CATEGORIES = [
  'frontend',
  'backend',
  'fullstack',
  'devops',
  'mobile',
  'database',
  'machine-learning',
  'security',
  'cloud',
  'design',
];

/**
 * Programming languages
 */
const PROGRAMMING_LANGUAGES = [
  'JavaScript',
  'TypeScript',
  'Python',
  'Java',
  'C++',
  'Go',
  'Rust',
  'PHP',
  'Ruby',
  'Swift',
];

/**
 * Sample resource data with titles, descriptions, and URLs
 */
const resourceData = [
  {
    title: 'Complete React Tutorial for Beginners',
    description:
      'A comprehensive guide to learning React from scratch, covering components, state, props, and hooks.',
    type: 'tutorial',
    category: 'frontend',
    url: 'https://reactjs.org/tutorial/tutorial.html',
    language: 'JavaScript',
    difficulty: Difficulty.EASY,
    tags: ['react', 'javascript', 'frontend', 'web-development'],
  },
  {
    title: 'Node.js API Design Best Practices',
    description:
      'Learn how to design robust and scalable APIs using Node.js and Express.',
    type: 'article',
    category: 'backend',
    url: 'https://blog.logrocket.com/node-js-express-api-best-practices/',
    language: 'JavaScript',
    difficulty: Difficulty.MEDIUM,
    tags: ['node.js', 'express', 'api', 'backend'],
  },
  {
    title: 'Advanced TypeScript Patterns',
    description:
      'Explore advanced TypeScript patterns and techniques to improve your code quality and type safety.',
    type: 'video',
    category: 'frontend',
    url: 'https://www.youtube.com/watch?v=7o9z_ciOpKM',
    language: 'TypeScript',
    difficulty: Difficulty.HARD,
    tags: ['typescript', 'advanced', 'patterns', 'type-safety'],
  },
  {
    title: 'Docker for Developers',
    description:
      'A practical guide to using Docker for local development and production deployments.',
    type: 'course',
    category: 'devops',
    url: 'https://www.udemy.com/course/docker-for-developers/',
    language: 'Bash',
    difficulty: Difficulty.MEDIUM,
    tags: ['docker', 'devops', 'containers', 'deployment'],
  },
  {
    title: 'Python Data Science Handbook',
    description:
      'Essential tools for working with data in Python, including NumPy, Pandas, Matplotlib, and Scikit-Learn.',
    type: 'book',
    category: 'machine-learning',
    url: 'https://jakevdp.github.io/PythonDataScienceHandbook/',
    language: 'Python',
    difficulty: Difficulty.MEDIUM,
    tags: ['python', 'data-science', 'machine-learning', 'pandas'],
  },
  {
    title: 'Modern CSS Techniques',
    description:
      'Learn modern CSS techniques like Grid, Flexbox, and CSS Variables to create responsive layouts.',
    type: 'tutorial',
    category: 'frontend',
    url: 'https://css-tricks.com/guides/',
    language: 'CSS',
    difficulty: Difficulty.MEDIUM,
    tags: ['css', 'frontend', 'web-design', 'responsive'],
  },
  {
    title: 'GraphQL API Development',
    description:
      'A complete guide to building GraphQL APIs with Apollo Server and Node.js.',
    type: 'documentation',
    category: 'backend',
    url: 'https://www.apollographql.com/docs/apollo-server/',
    language: 'JavaScript',
    difficulty: Difficulty.MEDIUM,
    tags: ['graphql', 'apollo', 'api', 'backend'],
  },
  {
    title: 'AWS Lambda and Serverless Architecture',
    description:
      'Learn how to build serverless applications using AWS Lambda and the Serverless Framework.',
    type: 'course',
    category: 'cloud',
    url: 'https://www.serverless.com/learn/',
    language: 'JavaScript',
    difficulty: Difficulty.MEDIUM,
    tags: ['aws', 'lambda', 'serverless', 'cloud'],
  },
  {
    title: 'Secure Coding Practices in Java',
    description:
      'Best practices for writing secure Java code and preventing common vulnerabilities.',
    type: 'article',
    category: 'security',
    url: 'https://owasp.org/www-project-secure-coding-practices-quick-reference-guide/',
    language: 'Java',
    difficulty: Difficulty.HARD,
    tags: ['java', 'security', 'secure-coding', 'owasp'],
  },
  {
    title: 'PostgreSQL Performance Tuning',
    description:
      'Advanced techniques for optimizing PostgreSQL database performance.',
    type: 'tutorial',
    category: 'database',
    url: 'https://www.postgresql.org/docs/current/performance-tips.html',
    language: 'SQL',
    difficulty: Difficulty.HARD,
    tags: ['postgresql', 'database', 'performance', 'optimization'],
  },
  {
    title: 'Flutter Mobile App Development',
    description: 'Build cross-platform mobile apps with Flutter and Dart.',
    type: 'video',
    category: 'mobile',
    url: 'https://flutter.dev/docs/get-started/codelab',
    language: 'Dart',
    difficulty: Difficulty.EASY,
    tags: ['flutter', 'dart', 'mobile', 'cross-platform'],
  },
  {
    title: 'Kubernetes for Developers',
    description:
      'Learn how to deploy and manage containerized applications with Kubernetes.',
    type: 'documentation',
    category: 'devops',
    url: 'https://kubernetes.io/docs/tutorials/',
    language: 'YAML',
    difficulty: Difficulty.HARD,
    tags: ['kubernetes', 'devops', 'containers', 'orchestration'],
  },
  {
    title: 'Responsive Web Design Fundamentals',
    description:
      'Master the fundamentals of responsive web design to create websites that work on any device.',
    type: 'course',
    category: 'frontend',
    url: 'https://web.dev/responsive-web-design-basics/',
    language: 'HTML/CSS',
    difficulty: Difficulty.EASY,
    tags: ['responsive', 'web-design', 'css', 'frontend'],
  },
  {
    title: 'Microservices Architecture Patterns',
    description:
      'Explore common patterns and best practices for building microservices-based applications.',
    type: 'book',
    category: 'backend',
    url: 'https://microservices.io/patterns/index.html',
    language: 'Java',
    difficulty: Difficulty.HARD,
    tags: ['microservices', 'architecture', 'patterns', 'backend'],
  },
  {
    title: 'Git and GitHub for Version Control',
    description:
      'A comprehensive guide to using Git and GitHub for effective version control and collaboration.',
    type: 'tutorial',
    category: 'devops',
    url: 'https://www.atlassian.com/git/tutorials',
    language: 'Git',
    difficulty: Difficulty.EASY,
    tags: ['git', 'github', 'version-control', 'collaboration'],
  },
  {
    title: 'Machine Learning with TensorFlow',
    description:
      'Learn how to build and train machine learning models using TensorFlow and Keras.',
    type: 'course',
    category: 'machine-learning',
    url: 'https://www.tensorflow.org/tutorials',
    language: 'Python',
    difficulty: Difficulty.MEDIUM,
    tags: ['tensorflow', 'machine-learning', 'deep-learning', 'python'],
  },
  {
    title: 'Web Accessibility Guidelines',
    description:
      'Learn how to make your websites accessible to everyone, including people with disabilities.',
    type: 'documentation',
    category: 'frontend',
    url: 'https://www.w3.org/WAI/standards-guidelines/',
    language: 'HTML',
    difficulty: Difficulty.MEDIUM,
    tags: ['accessibility', 'a11y', 'wcag', 'frontend'],
  },
  {
    title: 'RESTful API Design Principles',
    description:
      'Best practices and principles for designing clean and effective RESTful APIs.',
    type: 'article',
    category: 'backend',
    url: 'https://restfulapi.net/',
    language: 'HTTP',
    difficulty: Difficulty.MEDIUM,
    tags: ['rest', 'api', 'backend', 'http'],
  },
  {
    title: 'React Native for Mobile Development',
    description:
      'Build native mobile apps for iOS and Android using React Native.',
    type: 'video',
    category: 'mobile',
    url: 'https://reactnative.dev/docs/getting-started',
    language: 'JavaScript',
    difficulty: Difficulty.MEDIUM,
    tags: ['react-native', 'mobile', 'ios', 'android'],
  },
  {
    title: 'MongoDB Database Design',
    description:
      'Learn how to design efficient and scalable MongoDB database schemas.',
    type: 'tutorial',
    category: 'database',
    url: 'https://www.mongodb.com/blog/post/building-with-patterns-a-summary',
    language: 'JavaScript',
    difficulty: Difficulty.MEDIUM,
    tags: ['mongodb', 'nosql', 'database', 'schema-design'],
  },
];

/**
 * Seeds the database with educational resources
 */
export async function seedResources() {
  console.log('\n==================================================');
  console.log('🌱 RESOURCE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    // Get users to assign as resource creators
    const users = await prisma.user.findMany({
      take: 5,
    });

    if (users.length === 0) {
      console.error('❌ No users found. Please run the user seeder first.');
      return;
    }

    console.log(`ℹ️ Found ${users.length} users for resource creation`);

    // Process each resource
    for (const resource of resourceData) {
      try {
        // Select a random user as the creator
        const user = users[Math.floor(Math.random() * users.length)];

        // Generate sample content for the resource
        const content = generateResourceContent(resource);

        // Check if resource already exists
        const existingResource = await prisma.resource.findFirst({
          where: { title: resource.title },
        });

        if (existingResource) {
          // Update existing resource
          await prisma.resource.update({
            where: { id: existingResource.id },
            data: {
              description: resource.description,
              type: resource.type,
              url: resource.url,
              category: resource.category,
              tags: resource.tags,
              content,
              difficulty: resource.difficulty,
              language: resource.language,
              updated_at: new Date(),
            },
          });
          updatedCount++;
          console.log(`🔄 Updated resource: ${resource.title}`);
        } else {
          // Create new resource
          const newResource = await prisma.resource.create({
            data: {
              id: uuidv4(),
              title: resource.title,
              description: resource.description,
              type: resource.type,
              url: resource.url,
              category: resource.category,
              tags: resource.tags,
              content,
              difficulty: resource.difficulty,
              language: resource.language,
              user_id: user.id,
              created_at: new Date(),
              updated_at: new Date(),
              downloadCount: Math.floor(Math.random() * 1000),
              rating: (3 + Math.random() * 2).toFixed(2),
            },
          });

          // Create an interview question related to this resource (for some resources)
          if (Math.random() > 0.7) {
            await prisma.interviewQuestion.create({
              data: {
                id: uuidv4(),
                question: generateInterviewQuestion(resource),
                answer: generateInterviewAnswer(resource),
                difficulty: resource.difficulty,
                topic: resource.category,
                tags: { tags: resource.tags },
                resource_id: newResource.id,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
            console.log(`✅ Created interview question for: ${resource.title}`);
          }

          // Create an article related to this resource (for some resources)
          if (Math.random() > 0.7) {
            // Find a suitable topic
            const topics = await prisma.topic.findMany({
              take: 10,
            });

            if (topics.length > 0) {
              const topic =
                topics.find(
                  (t) =>
                    t.title.toLowerCase().includes(resource.category) ||
                    t.description.toLowerCase().includes(resource.category),
                ) || topics[0];

              await prisma.article.create({
                data: {
                  id: uuidv4(),
                  title: `Guide to ${resource.title}`,
                  content: generateArticleContent(resource),
                  author_id: user.id,
                  topic_id: topic.id,
                  resource_id: newResource.id,
                  status: 'APPROVED',
                  created_at: new Date(),
                  updated_at: new Date(),
                },
              });
              console.log(`✅ Created article for: ${resource.title}`);
            }
          }

          createdCount++;
          console.log(`✅ Created resource: ${resource.title}`);
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process resource ${resource.title}:`,
          error,
        );
      }
    }

    console.log('\n✅ RESOURCE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding resources:', error);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Generate content for a resource
 * @param resource The resource data
 * @returns A string with resource content
 */
function generateResourceContent(resource: any): string {
  return `# ${resource.title}

## Overview

${resource.description}

## Key Topics Covered

${generateTopicsList(resource)}

## Why This Resource is Valuable

This ${resource.type} on ${resource.title} is an excellent resource for ${resource.difficulty === Difficulty.EASY ? 'beginners' : resource.difficulty === Difficulty.MEDIUM ? 'intermediate developers' : 'advanced developers'} looking to enhance their skills in ${resource.category}. 

${generateValueProposition(resource)}

## How to Use This Resource

${generateUsageInstructions(resource)}

## Prerequisites

Before diving into this resource, you should have:

${generatePrerequisites(resource)}

## Related Topics

If you find this resource helpful, you might also be interested in:

${generateRelatedTopics(resource)}
`;
}

/**
 * Generate a list of topics covered in the resource
 */
function generateTopicsList(resource: any): string {
  const topics = [];

  // Generate topics based on resource category and tags
  switch (resource.category) {
    case 'frontend':
      topics.push(
        'User Interface Design',
        'Client-side Rendering',
        'Browser APIs',
      );
      break;
    case 'backend':
      topics.push('Server Architecture', 'API Design', 'Database Integration');
      break;
    case 'devops':
      topics.push(
        'Continuous Integration',
        'Deployment Strategies',
        'Infrastructure as Code',
      );
      break;
    case 'database':
      topics.push(
        'Data Modeling',
        'Query Optimization',
        'Transaction Management',
      );
      break;
    case 'machine-learning':
      topics.push('Data Preprocessing', 'Model Training', 'Evaluation Metrics');
      break;
    default:
      topics.push('Core Concepts', 'Best Practices', 'Advanced Techniques');
  }

  // Add topics based on tags
  if (resource.tags.includes('react')) {
    topics.push('Component Architecture', 'State Management', 'React Hooks');
  }
  if (resource.tags.includes('javascript')) {
    topics.push(
      'Asynchronous Programming',
      'ES6+ Features',
      'DOM Manipulation',
    );
  }
  if (resource.tags.includes('python')) {
    topics.push(
      'Data Structures',
      'Libraries & Frameworks',
      'Python Best Practices',
    );
  }

  // Format as a list
  return topics.map((topic) => `- ${topic}`).join('\n');
}

/**
 * Generate a value proposition for the resource
 */
function generateValueProposition(resource: any): string {
  const propositions = [
    `The author provides clear explanations with practical examples that you can apply immediately in your projects.`,
    `This resource stands out for its comprehensive coverage and hands-on approach to learning.`,
    `What makes this resource particularly valuable is its focus on real-world applications and industry best practices.`,
    `You'll appreciate the step-by-step approach that builds your knowledge progressively.`,
    `The content is regularly updated to reflect the latest developments in ${resource.language} and ${resource.category}.`,
  ];

  return propositions[Math.floor(Math.random() * propositions.length)];
}

/**
 * Generate usage instructions for the resource
 */
function generateUsageInstructions(resource: any): string {
  switch (resource.type) {
    case 'video':
      return `Watch the video tutorials in sequence, pausing to practice the concepts demonstrated. The video includes timestamps for easy navigation between topics.`;
    case 'article':
      return `Read through the article completely once, then revisit specific sections as needed. The code examples can be copied and run in your local development environment.`;
    case 'tutorial':
      return `Follow the tutorial step by step, completing each exercise before moving on. The hands-on approach will help reinforce the concepts being taught.`;
    case 'documentation':
      return `Use this documentation as a reference guide. Start with the "Getting Started" section, then explore specific topics as needed for your projects.`;
    case 'book':
      return `Read the chapters in order, completing the exercises at the end of each section. The book is designed to build your knowledge progressively.`;
    case 'course':
      return `Enroll in the course and follow the curriculum at your own pace. Make sure to complete all assignments and quizzes to reinforce your learning.`;
    default:
      return `Explore the resource at your own pace, focusing on the sections most relevant to your current projects or learning goals.`;
  }
}

/**
 * Generate prerequisites for the resource
 */
function generatePrerequisites(resource: any): string {
  const prerequisites = [];

  // Basic prerequisites based on difficulty
  if (resource.difficulty === Difficulty.EASY) {
    prerequisites.push('Basic programming knowledge');
    prerequisites.push(`Familiarity with ${resource.language} syntax`);
  } else if (resource.difficulty === Difficulty.MEDIUM) {
    prerequisites.push(`Intermediate ${resource.language} programming skills`);
    prerequisites.push(
      'Understanding of fundamental concepts in software development',
    );
  } else {
    prerequisites.push(`Advanced ${resource.language} programming experience`);
    prerequisites.push(`Strong background in ${resource.category}`);
    prerequisites.push('Problem-solving skills and analytical thinking');
  }

  // Additional prerequisites based on category
  switch (resource.category) {
    case 'frontend':
      prerequisites.push('Knowledge of HTML, CSS, and JavaScript');
      break;
    case 'backend':
      prerequisites.push('Understanding of server-side programming');
      prerequisites.push('Basic knowledge of databases');
      break;
    case 'devops':
      prerequisites.push('Command line experience');
      prerequisites.push('Basic understanding of cloud services');
      break;
    case 'database':
      prerequisites.push('Understanding of data modeling concepts');
      prerequisites.push('Basic SQL knowledge');
      break;
  }

  // Format as a list
  return prerequisites.map((prereq) => `- ${prereq}`).join('\n');
}

/**
 * Generate related topics for the resource
 */
function generateRelatedTopics(resource: any): string {
  const relatedTopics = [];

  // Generate related topics based on category
  switch (resource.category) {
    case 'frontend':
      relatedTopics.push(
        'CSS Frameworks',
        'JavaScript Libraries',
        'Web Performance Optimization',
      );
      break;
    case 'backend':
      relatedTopics.push('API Security', 'Microservices', 'Database Design');
      break;
    case 'devops':
      relatedTopics.push(
        'Containerization',
        'CI/CD Pipelines',
        'Infrastructure Monitoring',
      );
      break;
    case 'database':
      relatedTopics.push(
        'Database Indexing',
        'Query Optimization',
        'NoSQL Databases',
      );
      break;
    case 'machine-learning':
      relatedTopics.push(
        'Neural Networks',
        'Data Visualization',
        'Feature Engineering',
      );
      break;
    default:
      relatedTopics.push(
        'Advanced Techniques',
        'Industry Best Practices',
        'Emerging Technologies',
      );
  }

  // Format as a list
  return relatedTopics.map((topic) => `- ${topic}`).join('\n');
}

/**
 * Generate an interview question related to the resource
 */
function generateInterviewQuestion(resource: any): string {
  const questions = {
    frontend: [
      `Explain the concept of virtual DOM in React and why it's important.`,
      `What are the key differences between CSS Grid and Flexbox?`,
      `How would you optimize the performance of a JavaScript-heavy web application?`,
      `Explain the concept of responsive design and how you implement it.`,
      `What are Web Components and how do they work?`,
    ],
    backend: [
      `What are the RESTful API design principles and why are they important?`,
      `Explain the concept of middleware in Express.js.`,
      `How would you handle authentication and authorization in a Node.js API?`,
      `What are microservices and when would you use them?`,
      `Explain the concept of database normalization.`,
    ],
    devops: [
      `What is Docker and how does it differ from virtual machines?`,
      `Explain the concept of CI/CD and its benefits.`,
      `How would you handle secrets management in a Kubernetes environment?`,
      `What is Infrastructure as Code and why is it important?`,
      `Explain the concept of blue-green deployment.`,
    ],
    database: [
      `What is database indexing and when would you use it?`,
      `Explain the difference between SQL and NoSQL databases.`,
      `How would you optimize a slow SQL query?`,
      `What is database sharding and when would you use it?`,
      `Explain ACID properties in database transactions.`,
    ],
    'machine-learning': [
      `What is overfitting in machine learning and how can you prevent it?`,
      `Explain the difference between supervised and unsupervised learning.`,
      `How would you handle imbalanced datasets in classification problems?`,
      `What is gradient descent and how does it work?`,
      `Explain the concept of feature engineering.`,
    ],
  };

  // Get questions for the resource category or default to general questions
  const categoryQuestions = questions[
    resource.category as keyof typeof questions
  ] || [
    `What are the key concepts in ${resource.title}?`,
    `How would you implement ${resource.title} in a real-world project?`,
    `Explain the advantages and disadvantages of using ${resource.language} for ${resource.category}.`,
    `What are the best practices for ${resource.title}?`,
    `How would you troubleshoot issues related to ${resource.title}?`,
  ];

  // Return a random question
  return categoryQuestions[
    Math.floor(Math.random() * categoryQuestions.length)
  ];
}

/**
 * Generate an interview answer related to the resource
 */
function generateInterviewAnswer(resource: any): string {
  // This is a simplified version - in a real implementation, you would match answers to questions
  return `This is a sample answer for an interview question related to ${resource.title}. 
  
The answer would typically cover key concepts, best practices, and practical examples related to ${resource.category} and ${resource.language}.

For a comprehensive understanding of this topic, refer to the resource "${resource.title}" which provides detailed explanations and examples.`;
}

/**
 * Generate article content related to the resource
 */
function generateArticleContent(resource: any): string {
  return `# A Comprehensive Guide to ${resource.title}

## Introduction

Welcome to this comprehensive guide on ${resource.title}. This article aims to provide you with practical insights that you can apply to your projects immediately.

## Why ${resource.title} Matters

${resource.description}

In today's rapidly evolving tech landscape, mastering ${resource.title} can significantly enhance your development capabilities and career prospects.

## Key Concepts

Let's explore the fundamental concepts that you need to understand:

1. **Core Principle One** - Detailed explanation with examples
2. **Core Principle Two** - Detailed explanation with examples
3. **Core Principle Three** - Detailed explanation with examples

## Practical Implementation

Here's how you can implement these concepts in your projects:

\`\`\`${resource.language.toLowerCase()}
// Sample implementation code
function implementConcept() {
  // Implementation details
  console.log("Implementing ${resource.title}");
}
\`\`\`

## Best Practices

Follow these best practices to get the most out of ${resource.title}:

- Best practice one with explanation
- Best practice two with explanation
- Best practice three with explanation

## Common Pitfalls and How to Avoid Them

Be aware of these common mistakes:

1. **Pitfall One** - How to identify and avoid it
2. **Pitfall Two** - How to identify and avoid it
3. **Pitfall Three** - How to identify and avoid it

## Advanced Techniques

Once you've mastered the basics, explore these advanced techniques:

- Advanced technique one with explanation
- Advanced technique two with explanation
- Advanced technique three with explanation

## Conclusion

Mastering ${resource.title} will significantly enhance your development skills and enable you to build more robust, efficient, and maintainable applications. Keep practicing and exploring to deepen your knowledge further.

## Additional Resources

For more information, check out these resources:

- [${resource.title}](${resource.url})
- Other related resources and documentation
`;
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedResources()
    .then(() => {
      console.log('Resource seeder completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error running resource seeder:', error);
      process.exit(1);
    });
}
