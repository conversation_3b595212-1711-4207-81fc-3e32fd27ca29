/**
 * @file challengeBoilerplate.seeder.ts
 * @description Seeder for creating boilerplate code for existing challenges
 */
import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeds the database with boilerplate code for existing challenges
 */
export async function seedChallengeBoilerplates() {
  console.log('\n==================================================');
  console.log('🌱 CHALLENGE BOILERPLATE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    const challenges = await prisma.challenge.findMany();

    if (challenges.length === 0) {
      console.error(
        '❌ No challenges found. Please run the challenge seeder first.',
      );
      return;
    }

    console.log(`ℹ️  Found ${challenges.length} challenges for boilerplates`);

    // Define supported languages
    const languages = ['javascript', 'python', 'java', 'typescript', 'cpp'];

    for (const challenge of challenges) {
      try {
        // Check if challenge already has boilerplates
        const existingBoilerplates = await prisma.challengeBoilerplate.findMany(
          {
            where: { challenge_id: challenge.id },
          },
        );

        // Create a map of existing boilerplates by language
        const existingByLanguage = existingBoilerplates.reduce((acc, bp) => {
          acc[bp.language] = bp;
          return acc;
        }, {});

        // Function to generate boilerplate code based on language and challenge
        const generateBoilerplate = (language, challenge) => {
          const functionName = challenge.title
            .toLowerCase()
            .replace(/\s+/g, '_');

          switch (language) {
            case 'javascript':
              return `/**
 * ${challenge.title}
 * 
 * ${challenge.description.split('.')[0]}.
 * 
 * @param {*} params - Input parameters based on the challenge
 * @returns {*} - Solution based on the challenge requirements
 */
function ${functionName}(params) {
  // Your code here
  return null;
}`;
            case 'python':
              return `"""
${challenge.title}

${challenge.description.split('.')[0]}.

Args:
    params: Input parameters based on the challenge
    
Returns:
    Solution based on the challenge requirements
"""
def ${functionName}(params):
    # Your code here
    pass`;
            case 'java':
              return `/**
 * ${challenge.title}
 * 
 * ${challenge.description.split('.')[0]}.
 */
public class Solution {
    public static Object ${functionName}(Object params) {
        // Your code here
        return null;
    }
}`;
            case 'typescript':
              return `/**
 * ${challenge.title}
 * 
 * ${challenge.description.split('.')[0]}.
 * 
 * @param params - Input parameters based on the challenge
 * @returns Solution based on the challenge requirements
 */
function ${functionName}(params: any): any {
  // Your code here
  return null;
}`;
            case 'cpp':
              return `/**
 * ${challenge.title}
 * 
 * ${challenge.description.split('.')[0]}.
 */
#include <iostream>
#include <vector>
#include <string>

using namespace std;

// Your solution here
class Solution {
public:
    void ${functionName}(vector<int>& params) {
        // Your code here
    }
};`;
            default:
              return `// Boilerplate code for ${challenge.title} in ${language}`;
          }
        };

        // Process each language
        for (const language of languages) {
          const boilerplateCode = generateBoilerplate(language, challenge);

          if (existingByLanguage[language]) {
            // Update existing boilerplate
            await prisma.challengeBoilerplate.update({
              where: { id: existingByLanguage[language].id },
              data: {
                boilerplate_code: boilerplateCode,
                updated_at: new Date(),
              },
            });
            updatedCount++;
            console.log(
              `🔄 Updated ${language} boilerplate for challenge: ${challenge.title}`,
            );
          } else {
            // Create new boilerplate
            await prisma.challengeBoilerplate.create({
              data: {
                id: uuidv4(),
                challenge_id: challenge.id,
                language: language,
                boilerplate_code: boilerplateCode,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
            createdCount++;
            console.log(
              `✅ Created ${language} boilerplate for challenge: ${challenge.title}`,
            );
          }
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process boilerplate for ${challenge.title}:`,
          error,
        );
      }
    }

    console.log('\n✅ CHALLENGE BOILERPLATE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding challenge boilerplates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  seedChallengeBoilerplates().catch((error) => {
    console.error('Failed to seed challenge boilerplates:', error);
    process.exit(1);
  });
}
