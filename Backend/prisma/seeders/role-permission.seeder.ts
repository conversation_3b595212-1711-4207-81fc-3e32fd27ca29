/**
 * @file role-permission.seeder.ts
 * @description Seed script for role-permission assignments
 */
import { PrismaClient, RoleType } from '@prisma/client';

const prisma = new PrismaClient();

// Define role-permission mappings
const rolePermissions = [
  {
    roleType: RoleType.ADMIN,
    permissions: [
      // Admin has all permissions
      'users:view',
      'users:create',
      'users:edit',
      'users:delete',
      'users:manage_roles',
      'content:view',
      'content:create',
      'content:edit',
      'content:delete',
      'content:publish',
      'content:moderate',
      'roles:view',
      'roles:create',
      'roles:edit',
      'roles:delete',
      'roles:manage_permissions',
      'settings:view',
      'settings:edit',
      'analytics:view',
      'analytics:export',
    ],
  },
  {
    roleType: RoleType.MODERATOR,
    permissions: [
      // Moderator has user viewing and content moderation permissions
      'users:view',
      'content:view',
      'content:create',
      'content:edit',
      'content:moderate',
      'content:publish',
      'roles:view',
      'analytics:view',
    ],
  },
  {
    roleType: RoleType.CONTRIBUTOR,
    permissions: [
      // Contributor can create and edit content
      'users:view',
      'content:view',
      'content:create',
      'content:edit',
    ],
  },
  {
    roleType: RoleType.USER,
    permissions: [
      // Regular user has minimal permissions
      'content:view',
    ],
  },
];

/**
 * Seed role-permission assignments
 */
async function seedRolePermissions() {
  console.log('\n==================================================');
  console.log('🌱 ROLE-PERMISSION SEEDER STARTED');
  console.log('==================================================');

  let assignedCount = 0;
  let errorCount = 0;

  try {
    // Process each role
    for (const rolePerm of rolePermissions) {
      try {
        // Find the role by type
        const role = await prisma.role.findFirst({
          where: { type: rolePerm.roleType },
        });

        if (!role) {
          console.error(
            `❌ Role ${rolePerm.roleType} not found. Please run role seeder first.`,
          );
          continue;
        }

        console.log(`ℹ️ Processing permissions for role: ${role.name}`);

        // Find permissions by keys
        const permissions = await prisma.permission.findMany({
          where: {
            key: { in: rolePerm.permissions },
          },
        });

        if (permissions.length !== rolePerm.permissions.length) {
          console.warn(`⚠️ Some permissions not found for role ${role.name}`);
        }

        // Get existing role-permission assignments
        const existingRolePermissions = await prisma.rolePermission.findMany({
          where: { role_id: role.id },
          select: { permission_id: true },
        });

        const existingPermissionIds = existingRolePermissions.map(
          (rp) => rp.permission_id,
        );

        // Create new role-permission assignments
        for (const permission of permissions) {
          // Skip if assignment already exists
          if (existingPermissionIds.includes(permission.id)) {
            console.log(
              `ℹ️ Permission ${permission.name} already assigned to ${role.name}`,
            );
            continue;
          }

          await prisma.rolePermission.create({
            data: {
              role_id: role.id,
              permission_id: permission.id,
            },
          });

          assignedCount++;
          console.log(
            `✅ Assigned permission ${permission.name} to role ${role.name}`,
          );
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process permissions for role ${rolePerm.roleType}:`,
          error,
        );
      }
    }

    console.log('\n✅ ROLE-PERMISSION SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Assigned: ${assignedCount} permissions`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding role-permissions:', error);
  }
}

/**
 * Main function
 */
async function main() {
  try {
    await seedRolePermissions();
  } catch (error) {
    console.error('❌ Error in role-permission seeding process:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the main function if this file is run directly
if (require.main === module) {
  main();
}

export { seedRolePermissions };
