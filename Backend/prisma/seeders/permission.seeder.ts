/**
 * @file permission.seeder.ts
 * @description Seed script for permissions
 */
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Define all permissions
const permissions = [
  // User management permissions
  {
    name: 'View Users',
    key: 'users:view',
    description: 'Can view user profiles and list users',
  },
  {
    name: 'Create Users',
    key: 'users:create',
    description: 'Can create new users',
  },
  {
    name: 'Edit Users',
    key: 'users:edit',
    description: 'Can edit user profiles',
  },
  {
    name: 'Delete Users',
    key: 'users:delete',
    description: 'Can delete users',
  },
  {
    name: 'Manage User Roles',
    key: 'users:manage_roles',
    description: 'Can assign or remove roles from users',
  },

  // Content management permissions
  {
    name: 'View Content',
    key: 'content:view',
    description: 'Can view all content including drafts',
  },
  {
    name: 'Create Content',
    key: 'content:create',
    description: 'Can create new content',
  },
  {
    name: 'Edit Content',
    key: 'content:edit',
    description: 'Can edit content',
  },
  {
    name: 'Delete Content',
    key: 'content:delete',
    description: 'Can delete content',
  },
  {
    name: 'Publish Content',
    key: 'content:publish',
    description: 'Can publish or unpublish content',
  },
  {
    name: 'Moderate Content',
    key: 'content:moderate',
    description: 'Can moderate content (approve/reject/flag)',
  },

  // Role management permissions
  {
    name: 'View Roles',
    key: 'roles:view',
    description: 'Can view roles and permissions',
  },
  {
    name: 'Create Roles',
    key: 'roles:create',
    description: 'Can create new roles',
  },
  {
    name: 'Edit Roles',
    key: 'roles:edit',
    description: 'Can edit roles',
  },
  {
    name: 'Delete Roles',
    key: 'roles:delete',
    description: 'Can delete roles',
  },
  {
    name: 'Manage Role Permissions',
    key: 'roles:manage_permissions',
    description: 'Can assign or remove permissions from roles',
  },

  // System settings permissions
  {
    name: 'View Settings',
    key: 'settings:view',
    description: 'Can view system settings',
  },
  {
    name: 'Edit Settings',
    key: 'settings:edit',
    description: 'Can edit system settings',
  },

  // Analytics permissions
  {
    name: 'View Analytics',
    key: 'analytics:view',
    description: 'Can view analytics and reports',
  },
  {
    name: 'Export Analytics',
    key: 'analytics:export',
    description: 'Can export analytics data',
  },
];

/**
 * Seed permissions
 */
async function seedPermissions() {
  console.log('\n==================================================');
  console.log('🌱 PERMISSION SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    console.log(`ℹ️ Found ${permissions.length} permissions to process`);

    for (const permissionData of permissions) {
      try {
        const existingPermission = await prisma.permission.findUnique({
          where: { key: permissionData.key },
        });

        if (existingPermission) {
          await prisma.permission.update({
            where: { id: existingPermission.id },
            data: permissionData,
          });
          updatedCount++;
          console.log(`🔄 Updated permission: ${permissionData.name}`);
        } else {
          await prisma.permission.create({
            data: permissionData,
          });
          createdCount++;
          console.log(`✅ Created permission: ${permissionData.name}`);
        }
      } catch (error) {
        errorCount++;
        console.error(
          `❌ Failed to process permission ${permissionData.name}:`,
          error,
        );
      }
    }

    console.log('\n✅ PERMISSION SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created: ${createdCount}`);
    console.log(`🔄 Updated: ${updatedCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding permissions:', error);
  }
}

/**
 * Main function
 */
async function main() {
  try {
    await seedPermissions();
  } catch (error) {
    console.error('❌ Error in permission seeding process:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Execute the main function if this file is run directly
if (require.main === module) {
  main();
}

export { seedPermissions };
