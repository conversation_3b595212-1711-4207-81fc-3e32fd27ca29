/**
 * @file challengeTestCase.seeder.ts
 * @description Seeder for creating test cases for existing challenges
 */
import { v4 as uuidv4 } from 'uuid';

import { PrismaClient } from '@prisma/client';

import logger from '../../src/utils/logger';

const prisma = new PrismaClient();

/**
 * Generic test cases that can be adapted to different challenge types
 */
const genericTestCases = [
  // Two Sum variations
  { input: '[1, 2, 3, 4, 5], 9', expected_output: '[3, 4]' },
  { input: '[5, 10, 15, 20, 25], 35', expected_output: '[1, 3]' },
  { input: '[7, 14, 21, 28, 35], 42', expected_output: '[1, 2]' },
  { input: '[2, 4, 6, 8, 10], 14', expected_output: '[2, 3]' },
  { input: '[1, 3, 5, 7, 9], 8', expected_output: '[1, 2]' },
  { input: '[10, 20, 30, 40, 50], 60', expected_output: '[0, 2]' },
  { input: '[11, 22, 33, 44, 55], 77', expected_output: '[1, 3]' },

  // String manipulation variations
  { input: '"hello"', expected_output: '"olleh"' },
  { input: '"programming"', expected_output: '"gnimmargorp"' },
  { input: '"algorithm"', expected_output: '"mhtirogla"' },
  { input: '"javascript"', expected_output: '"tpircsavaj"' },
  { input: '"python"', expected_output: '"nohtyp"' },
  { input: '"developer"', expected_output: '"repoleved"' },
  { input: '"challenge"', expected_output: '"egnellahc"' },

  // Palindrome variations
  { input: '"racecar"', expected_output: 'true' },
  { input: '"level"', expected_output: 'true' },
  { input: '"hello"', expected_output: 'false' },
  { input: '"madam"', expected_output: 'true' },
  { input: '"algorithm"', expected_output: 'false' },
  { input: '"noon"', expected_output: 'true' },
  { input: '"civic"', expected_output: 'true' },

  // Fibonacci variations
  { input: '5', expected_output: '5' },
  { input: '10', expected_output: '55' },
  { input: '15', expected_output: '610' },
  { input: '20', expected_output: '6765' },
  { input: '25', expected_output: '75025' },
  { input: '30', expected_output: '832040' },
  { input: '1', expected_output: '1' },

  // Sorting variations
  { input: '[5, 3, 1, 4, 2]', expected_output: '[1, 2, 3, 4, 5]' },
  { input: '[10, 8, 6, 4, 2]', expected_output: '[2, 4, 6, 8, 10]' },
  { input: '[7, 2, 9, 1, 5]', expected_output: '[1, 2, 5, 7, 9]' },
  { input: '[3, 3, 2, 2, 1]', expected_output: '[1, 2, 2, 3, 3]' },
  { input: '[100, 50, 75, 25]', expected_output: '[25, 50, 75, 100]' },
  { input: '[42, 13, 7, 99, 0]', expected_output: '[0, 7, 13, 42, 99]' },
  { input: '[5, 5, 5, 5, 5]', expected_output: '[5, 5, 5, 5, 5]' },
];

/**
 * Specific test cases for different challenge types
 */
const specificTestCases = {
  // Array-related test cases
  array: [
    { input: '[1, 2, 3, 4, 5]', expected_output: '15' }, // Sum
    { input: '[10, 20, 30, 40, 50]', expected_output: '150' }, // Sum
    { input: '[5, 10, 15, 20, 25]', expected_output: '15' }, // Average
    { input: '[2, 4, 6, 8, 10]', expected_output: '6' }, // Average
    { input: '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]', expected_output: '5.5' }, // Average
    { input: '[5, 3, 8, 1, 9, 2, 7]', expected_output: '9' }, // Max
    { input: '[10, 5, 8, 12, 3, 15]', expected_output: '15' }, // Max
    { input: '[5, 3, 8, 1, 9, 2, 7]', expected_output: '1' }, // Min
    { input: '[10, 5, 8, 12, 3, 15]', expected_output: '3' }, // Min
    {
      input: '[1, 2, 2, 3, 3, 3, 4, 4, 4, 4]',
      expected_output: '[1, 2, 3, 4]',
    }, // Unique
  ],

  // String-related test cases
  string: [
    { input: '"hello", "world"', expected_output: '"helloworld"' }, // Concatenate
    { input: '"javascript", "python"', expected_output: '"javascriptpython"' }, // Concatenate
    { input: '"hello"', expected_output: '5' }, // Length
    { input: '"programming"', expected_output: '11' }, // Length
    { input: '"hello", "l"', expected_output: '2' }, // Count occurrences
    { input: '"programming", "m"', expected_output: '2' }, // Count occurrences
    { input: '"hello world", " "', expected_output: '1' }, // Count occurrences
    { input: '"hello", "world"', expected_output: 'false' }, // Contains
    { input: '"hello world", "world"', expected_output: 'true' }, // Contains
    { input: '"HELLO"', expected_output: '"hello"' }, // Lowercase
  ],

  // Linked list-related test cases
  linkedList: [
    { input: '[1, 2, 3, 4, 5]', expected_output: '5' }, // Length
    { input: '[]', expected_output: '0' }, // Length
    { input: '[1, 2, 3], 2', expected_output: '3' }, // Get value at index
    { input: '[1, 2, 3, 4, 5], 3', expected_output: '4' }, // Get value at index
    { input: '[1, 2, 3], 3', expected_output: '[1, 2, 3, 3]' }, // Append
    { input: '[1, 2, 3], 0', expected_output: '[0, 1, 2, 3]' }, // Prepend
    { input: '[1, 2, 3, 4, 5], 2', expected_output: '[1, 2, 4, 5]' }, // Remove at index
    { input: '[1, 2, 3, 4, 5]', expected_output: '[5, 4, 3, 2, 1]' }, // Reverse
    { input: '[1, 2, 3], [4, 5, 6]', expected_output: '[1, 2, 3, 4, 5, 6]' }, // Merge
    { input: '[1, 2, 3, 3, 4, 4, 5]', expected_output: '[1, 2, 3, 4, 5]' }, // Remove duplicates
  ],

  // Tree-related test cases
  tree: [
    { input: '[3, 9, 20, null, null, 15, 7]', expected_output: '3' }, // Height
    { input: '[1, null, 2]', expected_output: '2' }, // Height
    {
      input: '[3, 9, 20, null, null, 15, 7]',
      expected_output: '[3, 9, 20, 15, 7]',
    }, // Level order traversal
    { input: '[1, 2, 3, 4, 5]', expected_output: '[1, 2, 3, 4, 5]' }, // Level order traversal
    { input: '[1, 2, 3, 4, 5]', expected_output: '[1, 2, 4, 5, 3]' }, // Preorder traversal
    { input: '[1, 2, 3, 4, 5]', expected_output: '[4, 2, 5, 1, 3]' }, // Inorder traversal
    { input: '[1, 2, 3, 4, 5]', expected_output: '[4, 5, 2, 3, 1]' }, // Postorder traversal
    { input: '[3, 9, 20, null, null, 15, 7]', expected_output: 'true' }, // Is balanced
    { input: '[1, 2, 2, 3, 3, 3, 3]', expected_output: 'true' }, // Is symmetric
    { input: '[5, 3, 6, 2, 4, null, 7]', expected_output: 'true' }, // Is BST
  ],

  // Graph-related test cases
  graph: [
    {
      input: '[[0, 1], [0, 2], [1, 2], [2, 0], [2, 3], [3, 3]]',
      expected_output: 'true',
    }, // Has cycle
    { input: '[[0, 1], [0, 2], [1, 2], [2, 3]]', expected_output: 'false' }, // Has cycle
    { input: '[[0, 1], [0, 2], [1, 2], [2, 3]]', expected_output: '4' }, // Number of vertices
    { input: '[[0, 1], [0, 2], [1, 2], [2, 3]]', expected_output: '4' }, // Number of edges
    {
      input: '[[0, 1], [0, 2], [1, 2], [2, 3]], 0, 3',
      expected_output: 'true',
    }, // Path exists
    {
      input: '[[0, 1], [0, 2], [1, 2], [2, 3]], 0, 3',
      expected_output: '[0, 2, 3]',
    }, // Shortest path
    {
      input: '[[0, 1], [0, 2], [1, 2], [2, 3]], 0',
      expected_output: '[0, 1, 2, 3]',
    }, // BFS traversal
    {
      input: '[[0, 1], [0, 2], [1, 2], [2, 3]], 0',
      expected_output: '[0, 1, 2, 3]',
    }, // DFS traversal
    {
      input: '[[0, 1, 2], [1, 0, 3], [2, 3, 0], [3, 1, 2]]',
      expected_output:
        '[[0, 1, 2, 3], [1, 0, 3, 2], [2, 0, 3, 1], [3, 1, 2, 0]]',
    }, // Floyd-Warshall
    {
      input: '[[0, 1, 2], [1, 0, 3], [2, 3, 0], [3, 1, 2]], 0',
      expected_output: '[[0, 0], [1, 1], [2, 2], [3, 3]]',
    }, // Dijkstra
  ],

  // Dynamic programming test cases
  dp: [
    { input: '5', expected_output: '5' }, // Fibonacci
    { input: '10', expected_output: '55' }, // Fibonacci
    { input: '[1, 2, 3, 4, 5]', expected_output: '15' }, // Maximum subarray sum
    { input: '[-2, 1, -3, 4, -1, 2, 1, -5, 4]', expected_output: '6' }, // Maximum subarray sum
    { input: '[1, 2, 3, 4, 5], 10', expected_output: 'true' }, // Subset sum
    { input: '[1, 2, 3, 4, 5], 20', expected_output: 'false' }, // Subset sum
    { input: '"abcde", "ace"', expected_output: '3' }, // Longest common subsequence
    { input: '"abcde", "xyz"', expected_output: '0' }, // Longest common subsequence
    { input: '"abcabcbb"', expected_output: '3' }, // Longest substring without repeating characters
    { input: '"bbbbb"', expected_output: '1' }, // Longest substring without repeating characters
  ],
};

/**
 * Seeds the database with test cases for existing challenges
 */
export async function seedChallengeTestCases() {
  console.log('\n==================================================');
  console.log('🌱 CHALLENGE TEST CASE SEEDER STARTED');
  console.log('==================================================');

  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  try {
    // Get existing challenges
    const challenges = await prisma.challenge.findMany();

    if (challenges.length === 0) {
      console.error(
        '❌ No challenges found. Please run the challenge seeder first.',
      );
      return;
    }

    console.log(
      `ℹ️ Found ${challenges.length} challenges for adding test cases`,
    );

    /**
     * Determine the challenge type based on title and description
     * @param challenge The challenge to analyze
     * @returns The challenge type (array, string, linkedList, tree, graph, dp)
     */
    const determineChallengeType = (challenge: any): string => {
      const title = challenge.title.toLowerCase();
      const description = challenge.description?.toLowerCase() || '';

      // Check for array-related keywords
      if (
        title.includes('array') ||
        description.includes('array') ||
        title.includes('subarray') ||
        description.includes('subarray') ||
        title.includes('element') ||
        description.includes('element')
      ) {
        return 'array';
      }

      // Check for string-related keywords
      if (
        title.includes('string') ||
        description.includes('string') ||
        title.includes('substring') ||
        description.includes('substring') ||
        title.includes('character') ||
        description.includes('character')
      ) {
        return 'string';
      }

      // Check for linked list-related keywords
      if (
        title.includes('linked list') ||
        description.includes('linked list') ||
        title.includes('node') ||
        description.includes('node')
      ) {
        return 'linkedList';
      }

      // Check for tree-related keywords
      if (
        title.includes('tree') ||
        description.includes('tree') ||
        title.includes('binary') ||
        description.includes('binary') ||
        title.includes('root') ||
        description.includes('root')
      ) {
        return 'tree';
      }

      // Check for graph-related keywords
      if (
        title.includes('graph') ||
        description.includes('graph') ||
        title.includes('edge') ||
        description.includes('edge') ||
        title.includes('vertex') ||
        description.includes('vertex')
      ) {
        return 'graph';
      }

      // Check for dynamic programming-related keywords
      if (
        title.includes('dynamic') ||
        description.includes('dynamic') ||
        title.includes('dp') ||
        description.includes('dp') ||
        title.includes('fibonacci') ||
        description.includes('fibonacci')
      ) {
        return 'dp';
      }

      // Default to generic if no specific type is detected
      return 'generic';
    };

    // Create test cases for each challenge
    console.log('\n🔄 Creating challenge test cases...');

    for (const challenge of challenges) {
      // Determine the challenge type
      const challengeType = determineChallengeType(challenge);

      // Get test cases based on challenge type
      let testCases = [];

      if (challengeType === 'generic') {
        // Use generic test cases
        testCases = genericTestCases.slice(0, 10); // Take first 10 generic test cases
      } else {
        // Use specific test cases for the challenge type
        testCases =
          specificTestCases[challengeType as keyof typeof specificTestCases] ||
          genericTestCases.slice(0, 10);
      }

      // Create test cases for the challenge
      for (let i = 0; i < Math.min(testCases.length, 10); i++) {
        // Create up to 10 test cases per challenge
        const testCase = testCases[i];

        try {
          // Check if test case already exists
          const existingTestCase = await prisma.testCase.findFirst({
            where: {
              challenge_id: challenge.id,
              input: testCase.input,
              output: testCase.expected_output,
            },
          });

          if (existingTestCase) {
            // Update existing test case
            await prisma.testCase.update({
              where: { id: existingTestCase.id },
              data: {
                updated_at: new Date(),
              },
            });
          } else {
            // Create new test case
            await prisma.testCase.create({
              data: {
                id: uuidv4(),
                challenge_id: challenge.id,
                input: testCase.input,
                output: testCase.expected_output,
                is_hidden: i >= 5, // Make half of the test cases hidden
                order_index: i, // Set the order index based on the position
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
          }

          if (existingTestCase) {
            updatedCount++;
            console.log(
              `🔄 Updated test case for challenge "${challenge.title}"`,
            );
          } else {
            createdCount++;
            console.log(
              `✅ Created test case for challenge "${challenge.title}"`,
            );
          }
        } catch (error) {
          console.error(
            `❌ Error creating test case for challenge "${challenge.title}":`,
            error,
          );
          errorCount++;
        }
      }
    }

    console.log('\n✅ CHALLENGE TEST CASE SEEDER COMPLETED');
    console.log('--------------------------------------------------');
    console.log(`✅ Created ${createdCount} test cases`);
    console.log(`🔄 Updated ${updatedCount} test cases`);
    console.log(`❌ Encountered ${errorCount} errors`);
    console.log('==================================================');
  } catch (error) {
    console.error('❌ Error seeding challenge test cases:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedChallengeTestCases()
    .then(() => {
      console.log('Challenge test case seeder completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error running challenge test case seeder:', error);
      process.exit(1);
    });
}
