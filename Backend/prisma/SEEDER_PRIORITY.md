# Seeder Priority List

This file lists all available seeders in order of priority for execution. We'll work through them one by one, fixing issues and marking them as completed.

## Priority Order

1. [x] **Role Seeder** (`role.seeder.ts`)

   - Creates basic user roles (Admin, Moderator, User)
   - Command: `npm run seed:roles`

2. [x] **User Seeder** (`user.seeder.ts`)

   - Creates sample users with different roles
   - Command: `npm run seed:user`
   - Dependencies: Role Seeder

3. [x] **Subject Seeder** (`subject.seeder.ts`)

   - Creates main subject categories
   - Command: `tsx ./prisma/seeders/subject.seeder.ts`

4. [x] **Topic Seeder** (`topic.seeder.ts`)

   - Creates topics within subjects
   - Command: `tsx ./prisma/seeders/topic.seeder.ts`
   - Dependencies: Subject Seeder

5. [x] **Challenge Seeder** (`challenge.seeder.ts`)

   - Creates coding challenges with examples, test cases, and boilerplates
   - Command: `tsx ./prisma/seeders/challenge.seeder.ts`
   - Dependencies: Topic Seeder

6. [x] **Article Seeder** (`article.seeder.ts`)

   - Creates sample articles
   - Command: `tsx ./prisma/seeders/article.seeder.ts`
   - Dependencies: User Seeder, Topic Seeder

7. [x] **Resource Seeder** (`resource.seeder.ts`)

   - Creates learning resources
   - Command: `tsx ./prisma/seeders/resource.seeder.ts`
   - Dependencies: User Seeder

8. [x] **Achievement Seeder** (`achievement.seeder.ts`)

   - Creates achievements that users can earn
   - Command: `tsx ./prisma/seeders/achievement.seeder.ts`

9. [x] **Daily Topic Seeder** (`dailyTopic.seeder.ts`)

   - Creates daily topics for the platform
   - Command: `tsx ./prisma/seeders/dailyTopic.seeder.ts`
   - Dependencies: Topic Seeder

10. [x] **Battle Seeder** (`battle.seeder.ts`)

    - Creates coding battles
    - Command: `tsx ./prisma/seeders/battle.seeder.ts`
    - Dependencies: User Seeder, Topic Seeder

11. [x] **Roadmap Seeder** (`roadmap.seeder.ts`)
    - Creates learning roadmaps with topics, main concepts, and challenges
    - Command: `tsx ./prisma/seeders/roadmap.seeder.ts`
    - Dependencies: User Seeder, Topic Seeder, MainConcept Seeder, Challenge Seeder

## Removed or Merged Seeders

- `codingChallenge.seeder.ts` - Merged into challenge.seeder.ts
- `challengeExample.seeder.ts` - Now handled within challenge.seeder.ts
- `challengeTestCase.seeder.ts` - Now handled within challenge.seeder.ts
- `challengeBoilerplate.seeder.ts` - Now handled within challenge.seeder.ts
- `roadmapSubject.seeder.ts` - Functionality now included in roadmap.seeder.ts

## Removed Seeders (User-Generated Content)

These seeders have been removed as they create content that should be generated by users:

- `like.seeder.ts`
- `comment.seeder.ts`
- `challengeDiscussion.seeder.ts`
- `userAchievement.seeder.ts`
- `userProgress.seeder.ts`
- `userRoadmap.seeder.ts`
- `userStreak.seeder.ts`
- `bookmarkCollection.seeder.ts`
- `leaderboardEntry.seeder.ts`
- `mentorship.seeder.ts`
- `studyGroup.seeder.ts`
- `forum.seeder.ts`

## Progress Tracking

- [x] Fixed Role Seeder
- [x] Fixed User Seeder
- [x] Fixed Subject Seeder
- [x] Fixed Topic Seeder
- [x] Fixed Challenge Seeder
- [x] Fixed Challenge Example Seeder
- [x] Fixed Challenge Test Case Seeder
- [x] Fixed Challenge Boilerplate Seeder
- [x] Fixed Article Seeder
- [x] Fixed Resource Seeder
- [x] Fixed Achievement Seeder
- [x] Fixed Daily Topic Seeder
- [x] Fixed Battle Seeder
- [x] Fixed Roadmap Seeder