/*
  Warnings:

  - You are about to drop the column `date` on the `Battle` table. All the data in the column will be lost.
  - You are about to drop the column `time` on the `Battle` table. All the data in the column will be lost.
  - Added the required column `type` to the `Battle` table without a default value. This is not possible if the table is not empty.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "BattleType" AS ENUM ('INSTANT', 'SCHEDULED', 'TOURNAMENT', 'PRACTICE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "BattleStatus" AS ENUM ('UPCOMING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'ARCHIVED');

-- AlterTable
ALTER TABLE "Battle" DROP COLUMN "date",
DROP COLUMN "time",
ADD COLUMN     "current_participants" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "end_time" TIMESTAMP(3),
ADD COLUMN     "max_participants" INTEGER NOT NULL DEFAULT 2,
ADD COLUMN     "points_per_question" INTEGER NOT NULL DEFAULT 10,
ADD COLUMN     "start_time" TIMESTAMP(3),
ADD COLUMN     "status" "BattleStatus" NOT NULL DEFAULT 'UPCOMING',
ADD COLUMN     "time_per_question" INTEGER NOT NULL DEFAULT 30,
ADD COLUMN     "total_questions" INTEGER NOT NULL DEFAULT 10,
ADD COLUMN     "type" "BattleType" NOT NULL;

-- AlterTable
ALTER TABLE "UserProgress" ALTER COLUMN "confidence_level" SET DEFAULT NULL,
ALTER COLUMN "progress_percentage" SET DEFAULT NULL;

-- CreateTable
CREATE TABLE "BattleParticipant" (
    "id" TEXT NOT NULL,
    "battle_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "score" INTEGER NOT NULL DEFAULT 0,
    "rank" INTEGER,
    "joined_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP(3),

    CONSTRAINT "BattleParticipant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BattleQuestion" (
    "id" TEXT NOT NULL,
    "battle_id" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "options" JSONB NOT NULL,
    "correct_answer" TEXT NOT NULL,
    "points" INTEGER NOT NULL DEFAULT 10,
    "time_limit" INTEGER NOT NULL DEFAULT 30,
    "order" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BattleQuestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BattleAnswer" (
    "id" TEXT NOT NULL,
    "question_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "is_correct" BOOLEAN NOT NULL,
    "time_taken" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BattleAnswer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BattleLeaderboard" (
    "id" TEXT NOT NULL,
    "battle_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "score" INTEGER NOT NULL DEFAULT 0,
    "rank" INTEGER NOT NULL,
    "time_taken" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BattleLeaderboard_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "activities" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "roadmap_id" TEXT,
    "roadmap_title" TEXT,

    CONSTRAINT "activities_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "BattleParticipant_battle_id_idx" ON "BattleParticipant"("battle_id");

-- CreateIndex
CREATE INDEX "BattleParticipant_user_id_idx" ON "BattleParticipant"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "BattleParticipant_battle_id_user_id_key" ON "BattleParticipant"("battle_id", "user_id");

-- CreateIndex
CREATE INDEX "BattleQuestion_battle_id_idx" ON "BattleQuestion"("battle_id");

-- CreateIndex
CREATE INDEX "BattleAnswer_question_id_idx" ON "BattleAnswer"("question_id");

-- CreateIndex
CREATE INDEX "BattleAnswer_user_id_idx" ON "BattleAnswer"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "BattleAnswer_question_id_user_id_key" ON "BattleAnswer"("question_id", "user_id");

-- CreateIndex
CREATE INDEX "BattleLeaderboard_battle_id_idx" ON "BattleLeaderboard"("battle_id");

-- CreateIndex
CREATE INDEX "BattleLeaderboard_user_id_idx" ON "BattleLeaderboard"("user_id");

-- CreateIndex
CREATE INDEX "BattleLeaderboard_score_idx" ON "BattleLeaderboard"("score");

-- CreateIndex
CREATE UNIQUE INDEX "BattleLeaderboard_battle_id_user_id_key" ON "BattleLeaderboard"("battle_id", "user_id");

-- CreateIndex
CREATE INDEX "activities_user_id_idx" ON "activities"("user_id");

-- CreateIndex
CREATE INDEX "activities_timestamp_idx" ON "activities"("timestamp");

-- CreateIndex
CREATE INDEX "Battle_topic_id_idx" ON "Battle"("topic_id");

-- CreateIndex
CREATE INDEX "Battle_user_id_idx" ON "Battle"("user_id");

-- CreateIndex
CREATE INDEX "Battle_status_idx" ON "Battle"("status");

-- CreateIndex
CREATE INDEX "Battle_type_idx" ON "Battle"("type");

-- AddForeignKey
ALTER TABLE "BattleParticipant" ADD CONSTRAINT "BattleParticipant_battle_id_fkey" FOREIGN KEY ("battle_id") REFERENCES "Battle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleParticipant" ADD CONSTRAINT "BattleParticipant_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleQuestion" ADD CONSTRAINT "BattleQuestion_battle_id_fkey" FOREIGN KEY ("battle_id") REFERENCES "Battle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleAnswer" ADD CONSTRAINT "BattleAnswer_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "BattleQuestion"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleAnswer" ADD CONSTRAINT "BattleAnswer_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleLeaderboard" ADD CONSTRAINT "BattleLeaderboard_battle_id_fkey" FOREIGN KEY ("battle_id") REFERENCES "Battle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleLeaderboard" ADD CONSTRAINT "BattleLeaderboard_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
