-- CreateTable
CREATE TABLE "ChallengeDiscussion" (
    "id" TEXT NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "parent_id" TEXT,
    "content" TEXT NOT NULL,
    "upvotes" INTEGER NOT NULL DEFAULT 0,
    "downvotes" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChallengeDiscussion_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ChallengeDiscussion_challenge_id_idx" ON "ChallengeDiscussion"("challenge_id");

-- CreateIndex
CREATE INDEX "ChallengeDiscussion_user_id_idx" ON "ChallengeDiscussion"("user_id");

-- CreateIndex
CREATE INDEX "ChallengeDiscussion_parent_id_idx" ON "ChallengeDiscussion"("parent_id");

-- AddForeignKey
ALTER TABLE "ChallengeDiscussion" ADD CONSTRAINT "ChallengeDiscussion_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "Challenge"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChallengeDiscussion" ADD CONSTRAINT "ChallengeDiscussion_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChallengeDiscussion" ADD CONSTRAINT "ChallengeDiscussion_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "ChallengeDiscussion"("id") ON DELETE CASCADE ON UPDATE CASCADE;
