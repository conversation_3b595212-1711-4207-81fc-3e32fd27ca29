/*
  Warnings:

  - You are about to drop the column `criteria` on the `Achievement` table. All the data in the column will be lost.
  - You are about to drop the column `earned_at` on the `Achievement` table. All the data in the column will be lost.
  - You are about to drop the column `title` on the `Achievement` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `Achievement` table. All the data in the column will be lost.
  - You are about to drop the column `user_id` on the `Achievement` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name]` on the table `Achievement` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `category` to the `Achievement` table without a default value. This is not possible if the table is not empty.
  - Added the required column `icon_url` to the `Achievement` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name` to the `Achievement` table without a default value. This is not possible if the table is not empty.
  - Added the required column `points` to the `Achievement` table without a default value. This is not possible if the table is not empty.
  - Added the required column `trigger_type` to the `Achievement` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `Achievement` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "AchievementCategory" AS ENUM ('CHALLENGE', 'STREAK', 'SOCIAL', 'ROADMAP', 'BATTLE', 'PROFILE', 'SPECIAL');

-- CreateEnum
CREATE TYPE "AchievementTriggerType" AS ENUM ('CHALLENGE_COMPLETION', 'CHALLENGE_COUNT', 'STREAK_DAYS', 'DISCUSSION_COUNT', 'SOLUTION_VIEWS', 'PROFILE_COMPLETION', 'BATTLE_WINS', 'ROADMAP_COMPLETION', 'MANUAL');

-- DropForeignKey
ALTER TABLE "Achievement" DROP CONSTRAINT "Achievement_user_id_fkey";

-- DropIndex
DROP INDEX "Achievement_user_id_idx";

-- AlterTable
ALTER TABLE "Achievement" DROP COLUMN "criteria",
DROP COLUMN "earned_at",
DROP COLUMN "title",
DROP COLUMN "type",
DROP COLUMN "user_id",
ADD COLUMN     "category" "AchievementCategory" NOT NULL,
ADD COLUMN     "icon_url" TEXT NOT NULL,
ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "is_hidden" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "name" TEXT NOT NULL,
ADD COLUMN     "points" INTEGER NOT NULL,
ADD COLUMN     "tier" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "trigger_type" "AchievementTriggerType" NOT NULL,
ADD COLUMN     "trigger_value" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "UserProgress" ALTER COLUMN "confidence_level" SET DEFAULT NULL,
ALTER COLUMN "progress_percentage" SET DEFAULT NULL;

-- CreateTable
CREATE TABLE "UserAchievement" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "achievement_id" TEXT NOT NULL,
    "unlocked_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_seen" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "UserAchievement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AchievementNotification" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "user_achievement_id" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "is_read" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AchievementNotification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UserAchievement_user_id_idx" ON "UserAchievement"("user_id");

-- CreateIndex
CREATE INDEX "UserAchievement_achievement_id_idx" ON "UserAchievement"("achievement_id");

-- CreateIndex
CREATE INDEX "UserAchievement_is_seen_idx" ON "UserAchievement"("is_seen");

-- CreateIndex
CREATE UNIQUE INDEX "UserAchievement_user_id_achievement_id_key" ON "UserAchievement"("user_id", "achievement_id");

-- CreateIndex
CREATE UNIQUE INDEX "AchievementNotification_user_achievement_id_key" ON "AchievementNotification"("user_achievement_id");

-- CreateIndex
CREATE INDEX "AchievementNotification_user_id_idx" ON "AchievementNotification"("user_id");

-- CreateIndex
CREATE INDEX "AchievementNotification_is_read_idx" ON "AchievementNotification"("is_read");

-- CreateIndex
CREATE UNIQUE INDEX "Achievement_name_key" ON "Achievement"("name");

-- CreateIndex
CREATE INDEX "Achievement_category_idx" ON "Achievement"("category");

-- CreateIndex
CREATE INDEX "Achievement_trigger_type_idx" ON "Achievement"("trigger_type");

-- CreateIndex
CREATE INDEX "Achievement_is_active_idx" ON "Achievement"("is_active");

-- AddForeignKey
ALTER TABLE "UserAchievement" ADD CONSTRAINT "UserAchievement_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAchievement" ADD CONSTRAINT "UserAchievement_achievement_id_fkey" FOREIGN KEY ("achievement_id") REFERENCES "Achievement"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AchievementNotification" ADD CONSTRAINT "AchievementNotification_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AchievementNotification" ADD CONSTRAINT "AchievementNotification_user_achievement_id_fkey" FOREIGN KEY ("user_achievement_id") REFERENCES "UserAchievement"("id") ON DELETE CASCADE ON UPDATE CASCADE;
