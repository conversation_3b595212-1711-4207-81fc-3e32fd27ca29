/*
  Warnings:

  - Added the required column `type` to the `roles` table without a default value. This is not possible if the table is not empty.

*/
-- <PERSON>reateEnum
CREATE TYPE "RoleType" AS ENUM ('ADMIN', 'MODERATOR', 'CONTRIBUTOR', 'USER');

-- CreateEnum
CREATE TYPE "RuleCategory" AS ENUM ('SCORING', 'TIMING', 'PARTICIPATION', 'QUESTION', 'MODERATION', 'OTHER');

-- AlterTable
-- First add the column as nullable
ALTER TABLE "roles" ADD COLUMN "type" "RoleType" NULL;

-- Update existing records
-- Assuming admin role has id=1, moderator=2, contributor=3, user=4
-- Adjust these IDs based on your actual data
UPDATE "roles" SET "type" = 'ADMIN' WHERE "name" = 'admin';
UPDATE "roles" SET "type" = 'MODERATOR' WHERE "name" = 'moderator';
UPDATE "roles" SET "type" = 'CONTRIBUTOR' WHERE "name" = 'contributor';
UPDATE "roles" SET "type" = 'USER' WHERE "name" = 'user';

-- Set any remaining roles to <PERSON><PERSON> as a fallback
UPDATE "roles" SET "type" = 'USER' WHERE "type" IS NULL;

-- Now make the column NOT NULL
ALTER TABLE "roles" ALTER COLUMN "type" SET NOT NULL;

-- CreateTable
CREATE TABLE "BattleRule" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "category" "RuleCategory" NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BattleRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BattleToRule" (
    "id" TEXT NOT NULL,
    "battle_id" TEXT NOT NULL,
    "rule_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BattleToRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_roles" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "role_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_roles_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BattleRule_name_key" ON "BattleRule"("name");

-- CreateIndex
CREATE INDEX "BattleRule_category_idx" ON "BattleRule"("category");

-- CreateIndex
CREATE INDEX "BattleRule_is_default_idx" ON "BattleRule"("is_default");

-- CreateIndex
CREATE INDEX "BattleToRule_battle_id_idx" ON "BattleToRule"("battle_id");

-- CreateIndex
CREATE INDEX "BattleToRule_rule_id_idx" ON "BattleToRule"("rule_id");

-- CreateIndex
CREATE UNIQUE INDEX "BattleToRule_battle_id_rule_id_key" ON "BattleToRule"("battle_id", "rule_id");

-- CreateIndex
CREATE INDEX "user_roles_user_id_idx" ON "user_roles"("user_id");

-- CreateIndex
CREATE INDEX "user_roles_role_id_idx" ON "user_roles"("role_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_roles_user_id_role_id_key" ON "user_roles"("user_id", "role_id");

-- AddForeignKey
ALTER TABLE "BattleToRule" ADD CONSTRAINT "BattleToRule_battle_id_fkey" FOREIGN KEY ("battle_id") REFERENCES "Battle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BattleToRule" ADD CONSTRAINT "BattleToRule_rule_id_fkey" FOREIGN KEY ("rule_id") REFERENCES "BattleRule"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
