-- Update Challenge model to add order_index for examples
ALTER TABLE "Challenge" ADD COLUMN "explanation" TEXT;

-- Create ChallengeExample model
CREATE TABLE "ChallengeExample" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "challenge_id" UUID NOT NULL REFERENCES "Challenge"("id") ON DELETE CASCADE,
  "input" TEXT NOT NULL,
  "output" TEXT NOT NULL,
  "explanation" TEXT,
  "order_index" INTEGER NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX "challenge_example_challenge_id_idx" ON "ChallengeExample"("challenge_id");
CREATE INDEX "challenge_example_order_idx" ON "ChallengeExample"("order_index");

-- Create ChallengeBoilerplate model
CREATE TABLE "ChallengeBoilerplate" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "challenge_id" UUID NOT NULL REFERENCES "Challenge"("id") ON DELETE CASCADE,
  "language" VARCHAR(50) NOT NULL,
  "boilerplate_code" TEXT NOT NULL,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE("challenge_id", "language")
);

CREATE INDEX "challenge_boilerplate_challenge_id_idx" ON "ChallengeBoilerplate"("challenge_id");
CREATE INDEX "challenge_boilerplate_language_idx" ON "ChallengeBoilerplate"("language");

-- Create UserChallengeBookmark model
CREATE TABLE "UserChallengeBookmark" (
  "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  "user_id" UUID NOT NULL REFERENCES "User"("id") ON DELETE CASCADE,
  "challenge_id" UUID NOT NULL REFERENCES "Challenge"("id") ON DELETE CASCADE,
  "created_at" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE("user_id", "challenge_id")
);

CREATE INDEX "user_challenge_bookmark_user_id_idx" ON "UserChallengeBookmark"("user_id");
CREATE INDEX "user_challenge_bookmark_challenge_id_idx" ON "UserChallengeBookmark"("challenge_id");

-- Update TestCase model to add order_index
ALTER TABLE "TestCase" ADD COLUMN "order_index" INTEGER NOT NULL DEFAULT 0;
CREATE INDEX "test_case_order_idx" ON "TestCase"("order_index");
