-- CreateTable
CREATE TABLE "analytics_reports" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "metrics" JSONB NOT NULL,
    "dimensions" JSONB NOT NULL,
    "visualizations" JSONB,
    "schedule_enabled" BOOLEAN NOT NULL DEFAULT false,
    "schedule_frequency" TEXT,
    "schedule_recipients" JSONB,
    "schedule_export_format" TEXT,
    "user_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "analytics_reports_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "analytics_settings" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "analytics_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "analytics_reports_user_id_idx" ON "analytics_reports"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "analytics_settings_key_key" ON "analytics_settings"("key");

-- AddForeignKey
ALTER TABLE "analytics_reports" ADD CONSTRAINT "analytics_reports_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
