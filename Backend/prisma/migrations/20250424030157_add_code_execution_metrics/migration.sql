-- AlterTable
ALTER TABLE "UserProgress" ALTER COLUMN "confidence_level" SET DEFAULT NULL,
ALTER COLUMN "progress_percentage" SET DEFAULT NULL;

-- CreateTable
CREATE TABLE "SubmissionMetrics" (
    "id" TEXT NOT NULL,
    "submission_id" TEXT NOT NULL,
    "memory_peak_kb" INTEGER,
    "cpu_time_ms" INTEGER,
    "io_time_ms" INTEGER,
    "compilation_time_ms" INTEGER,
    "execution_details" JSONB,
    "percentile_runtime" DOUBLE PRECISION,
    "percentile_memory" DOUBLE PRECISION,
    "optimization_score" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SubmissionMetrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OptimizationSuggestion" (
    "id" TEXT NOT NULL,
    "submission_id" TEXT NOT NULL,
    "suggestion_type" TEXT NOT NULL,
    "suggestion" TEXT NOT NULL,
    "code_snippet" TEXT,
    "line_start" INTEGER,
    "line_end" INTEGER,
    "priority" INTEGER NOT NULL DEFAULT 1,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "OptimizationSuggestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LanguageMetrics" (
    "id" TEXT NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "avg_runtime_ms" DOUBLE PRECISION NOT NULL,
    "avg_memory_kb" DOUBLE PRECISION NOT NULL,
    "min_runtime_ms" DOUBLE PRECISION NOT NULL,
    "min_memory_kb" DOUBLE PRECISION NOT NULL,
    "submission_count" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LanguageMetrics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SubmissionMetrics_submission_id_key" ON "SubmissionMetrics"("submission_id");

-- CreateIndex
CREATE INDEX "SubmissionMetrics_submission_id_idx" ON "SubmissionMetrics"("submission_id");

-- CreateIndex
CREATE INDEX "OptimizationSuggestion_submission_id_idx" ON "OptimizationSuggestion"("submission_id");

-- CreateIndex
CREATE INDEX "OptimizationSuggestion_suggestion_type_idx" ON "OptimizationSuggestion"("suggestion_type");

-- CreateIndex
CREATE INDEX "LanguageMetrics_challenge_id_idx" ON "LanguageMetrics"("challenge_id");

-- CreateIndex
CREATE INDEX "LanguageMetrics_language_idx" ON "LanguageMetrics"("language");

-- CreateIndex
CREATE UNIQUE INDEX "LanguageMetrics_challenge_id_language_key" ON "LanguageMetrics"("challenge_id", "language");

-- AddForeignKey
ALTER TABLE "SubmissionMetrics" ADD CONSTRAINT "SubmissionMetrics_submission_id_fkey" FOREIGN KEY ("submission_id") REFERENCES "ChallengeSubmission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OptimizationSuggestion" ADD CONSTRAINT "OptimizationSuggestion_submission_id_fkey" FOREIGN KEY ("submission_id") REFERENCES "ChallengeSubmission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LanguageMetrics" ADD CONSTRAINT "LanguageMetrics_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "Challenge"("id") ON DELETE CASCADE ON UPDATE CASCADE;
