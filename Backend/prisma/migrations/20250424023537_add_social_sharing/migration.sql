-- AlterTable
ALTER TABLE "UserProgress" ALTER COLUMN "confidence_level" SET DEFAULT NULL,
ALTER COLUMN "progress_percentage" SET DEFAULT NULL;

-- CreateTable
CREATE TABLE "SharedSolution" (
    "id" TEXT NOT NULL,
    "submission_id" TEXT NOT NULL,
    "share_token" TEXT NOT NULL,
    "title" TEXT,
    "description" TEXT,
    "is_public" BOOLEAN NOT NULL DEFAULT true,
    "expires_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "view_count" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "SharedSolution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SolutionView" (
    "id" TEXT NOT NULL,
    "shared_solution_id" TEXT NOT NULL,
    "viewer_id" TEXT,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "referrer" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SolutionView_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SharedSolution_submission_id_key" ON "SharedSolution"("submission_id");

-- CreateIndex
CREATE UNIQUE INDEX "SharedSolution_share_token_key" ON "SharedSolution"("share_token");

-- CreateIndex
CREATE INDEX "SharedSolution_share_token_idx" ON "SharedSolution"("share_token");

-- CreateIndex
CREATE INDEX "SharedSolution_submission_id_idx" ON "SharedSolution"("submission_id");

-- CreateIndex
CREATE INDEX "SolutionView_shared_solution_id_idx" ON "SolutionView"("shared_solution_id");

-- CreateIndex
CREATE INDEX "SolutionView_viewer_id_idx" ON "SolutionView"("viewer_id");

-- CreateIndex
CREATE INDEX "ChallengeSubmission_challenge_id_idx" ON "ChallengeSubmission"("challenge_id");

-- CreateIndex
CREATE INDEX "ChallengeSubmission_user_id_idx" ON "ChallengeSubmission"("user_id");

-- CreateIndex
CREATE INDEX "ChallengeSubmission_status_idx" ON "ChallengeSubmission"("status");

-- AddForeignKey
ALTER TABLE "SharedSolution" ADD CONSTRAINT "SharedSolution_submission_id_fkey" FOREIGN KEY ("submission_id") REFERENCES "ChallengeSubmission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SolutionView" ADD CONSTRAINT "SolutionView_shared_solution_id_fkey" FOREIGN KEY ("shared_solution_id") REFERENCES "SharedSolution"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SolutionView" ADD CONSTRAINT "SolutionView_viewer_id_fkey" FOREIGN KEY ("viewer_id") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
