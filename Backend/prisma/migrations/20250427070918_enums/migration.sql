/*
  Warnings:

  - The `status` column on the `Article` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `status` column on the `Progress` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Changed the type of `status` on the `ApprovalHistory` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Added the required column `updated_at` to the `UserChallengeBookmark` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "ArticleStatus" AS ENUM ('DRAFT', 'PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "ProgressStatus" AS ENUM ('PENDING', 'COMPLETED', 'IN_PROGRESS', 'SKIPPED');

-- CreateEnum
CREATE TYPE "ApprovalStatus" AS ENUM ('APPROVED', 'REJECTED', 'PENDING', 'NEEDS_REVISION');

-- CreateEnum
CREATE TYPE "FilterPresetType" AS ENUM ('CHALLENGE', 'ROADMAP', 'RESOURCE', 'ARTICLE');

-- CreateEnum
CREATE TYPE "VoteType" AS ENUM ('UPVOTE', 'DOWNVOTE');

-- CreateEnum
CREATE TYPE "FlagReason" AS ENUM ('SPAM', 'INAPPROPRIATE', 'OFFENSIVE', 'INCORRECT', 'OTHER');

-- CreateEnum
CREATE TYPE "ModerationStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "HintDifficulty" AS ENUM ('BEGINNER', 'INTERMEDIATE', 'ADVANCED');

-- AlterTable
ALTER TABLE "ApprovalHistory" DROP COLUMN "status",
ADD COLUMN     "status" "ApprovalStatus" NOT NULL;

-- AlterTable
ALTER TABLE "Article" DROP COLUMN "status",
ADD COLUMN     "status" "ArticleStatus" NOT NULL DEFAULT 'DRAFT';

-- AlterTable
ALTER TABLE "ChallengeDiscussion" ADD COLUMN     "code_language" TEXT,
ADD COLUMN     "code_snippet" TEXT,
ADD COLUMN     "flag_reason" TEXT,
ADD COLUMN     "is_flagged" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "is_hidden" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "Progress" DROP COLUMN "status",
ADD COLUMN     "status" "ProgressStatus" NOT NULL DEFAULT 'PENDING';

-- AlterTable
ALTER TABLE "UserChallengeBookmark" ADD COLUMN     "collection_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "UserProgress" ALTER COLUMN "confidence_level" SET DEFAULT NULL,
ALTER COLUMN "progress_percentage" SET DEFAULT NULL;

-- CreateTable
CREATE TABLE "BookmarkCollection" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "user_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BookmarkCollection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserFilterPreset" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "filters" JSONB NOT NULL,
    "type" "FilterPresetType" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserFilterPreset_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RoadmapChallenge" (
    "id" TEXT NOT NULL,
    "roadmap_id" TEXT NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,
    "is_required" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RoadmapChallenge_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DiscussionVote" (
    "id" TEXT NOT NULL,
    "discussion_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "vote_type" "VoteType" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DiscussionVote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DiscussionFlag" (
    "id" TEXT NOT NULL,
    "discussion_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "reason" "FlagReason" NOT NULL,
    "details" TEXT,
    "status" "ModerationStatus" NOT NULL DEFAULT 'PENDING',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DiscussionFlag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DiscussionNotification" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "discussion_id" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "is_read" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "DiscussionNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChallengeHint" (
    "id" TEXT NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "difficulty" "HintDifficulty" NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,
    "point_penalty" INTEGER NOT NULL DEFAULT 5,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChallengeHint_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserHint" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "hint_id" TEXT NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "unlocked_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "points_deducted" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "UserHint_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "BookmarkCollection_user_id_idx" ON "BookmarkCollection"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "BookmarkCollection_user_id_name_key" ON "BookmarkCollection"("user_id", "name");

-- CreateIndex
CREATE INDEX "UserFilterPreset_user_id_idx" ON "UserFilterPreset"("user_id");

-- CreateIndex
CREATE INDEX "UserFilterPreset_type_idx" ON "UserFilterPreset"("type");

-- CreateIndex
CREATE UNIQUE INDEX "UserFilterPreset_user_id_name_key" ON "UserFilterPreset"("user_id", "name");

-- CreateIndex
CREATE INDEX "RoadmapChallenge_roadmap_id_idx" ON "RoadmapChallenge"("roadmap_id");

-- CreateIndex
CREATE INDEX "RoadmapChallenge_challenge_id_idx" ON "RoadmapChallenge"("challenge_id");

-- CreateIndex
CREATE UNIQUE INDEX "RoadmapChallenge_roadmap_id_challenge_id_key" ON "RoadmapChallenge"("roadmap_id", "challenge_id");

-- CreateIndex
CREATE INDEX "DiscussionVote_discussion_id_idx" ON "DiscussionVote"("discussion_id");

-- CreateIndex
CREATE INDEX "DiscussionVote_user_id_idx" ON "DiscussionVote"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "DiscussionVote_discussion_id_user_id_key" ON "DiscussionVote"("discussion_id", "user_id");

-- CreateIndex
CREATE INDEX "DiscussionFlag_discussion_id_idx" ON "DiscussionFlag"("discussion_id");

-- CreateIndex
CREATE INDEX "DiscussionFlag_user_id_idx" ON "DiscussionFlag"("user_id");

-- CreateIndex
CREATE INDEX "DiscussionFlag_status_idx" ON "DiscussionFlag"("status");

-- CreateIndex
CREATE INDEX "DiscussionNotification_user_id_idx" ON "DiscussionNotification"("user_id");

-- CreateIndex
CREATE INDEX "DiscussionNotification_discussion_id_idx" ON "DiscussionNotification"("discussion_id");

-- CreateIndex
CREATE INDEX "DiscussionNotification_is_read_idx" ON "DiscussionNotification"("is_read");

-- CreateIndex
CREATE INDEX "ChallengeHint_challenge_id_idx" ON "ChallengeHint"("challenge_id");

-- CreateIndex
CREATE INDEX "ChallengeHint_difficulty_idx" ON "ChallengeHint"("difficulty");

-- CreateIndex
CREATE INDEX "UserHint_user_id_idx" ON "UserHint"("user_id");

-- CreateIndex
CREATE INDEX "UserHint_hint_id_idx" ON "UserHint"("hint_id");

-- CreateIndex
CREATE INDEX "UserHint_challenge_id_idx" ON "UserHint"("challenge_id");

-- CreateIndex
CREATE UNIQUE INDEX "UserHint_user_id_hint_id_key" ON "UserHint"("user_id", "hint_id");

-- CreateIndex
CREATE INDEX "Progress_user_id_status_idx" ON "Progress"("user_id", "status");

-- CreateIndex
CREATE INDEX "Progress_roadmap_id_status_idx" ON "Progress"("roadmap_id", "status");

-- CreateIndex
CREATE INDEX "UserChallengeBookmark_collection_id_idx" ON "UserChallengeBookmark"("collection_id");

-- AddForeignKey
ALTER TABLE "BookmarkCollection" ADD CONSTRAINT "BookmarkCollection_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserChallengeBookmark" ADD CONSTRAINT "UserChallengeBookmark_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "BookmarkCollection"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserFilterPreset" ADD CONSTRAINT "UserFilterPreset_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RoadmapChallenge" ADD CONSTRAINT "RoadmapChallenge_roadmap_id_fkey" FOREIGN KEY ("roadmap_id") REFERENCES "Roadmap"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RoadmapChallenge" ADD CONSTRAINT "RoadmapChallenge_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "Challenge"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiscussionVote" ADD CONSTRAINT "DiscussionVote_discussion_id_fkey" FOREIGN KEY ("discussion_id") REFERENCES "ChallengeDiscussion"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiscussionVote" ADD CONSTRAINT "DiscussionVote_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiscussionFlag" ADD CONSTRAINT "DiscussionFlag_discussion_id_fkey" FOREIGN KEY ("discussion_id") REFERENCES "ChallengeDiscussion"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiscussionFlag" ADD CONSTRAINT "DiscussionFlag_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiscussionNotification" ADD CONSTRAINT "DiscussionNotification_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DiscussionNotification" ADD CONSTRAINT "DiscussionNotification_discussion_id_fkey" FOREIGN KEY ("discussion_id") REFERENCES "ChallengeDiscussion"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChallengeHint" ADD CONSTRAINT "ChallengeHint_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "Challenge"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserHint" ADD CONSTRAINT "UserHint_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserHint" ADD CONSTRAINT "UserHint_hint_id_fkey" FOREIGN KEY ("hint_id") REFERENCES "ChallengeHint"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserHint" ADD CONSTRAINT "UserHint_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "Challenge"("id") ON DELETE CASCADE ON UPDATE CASCADE;
