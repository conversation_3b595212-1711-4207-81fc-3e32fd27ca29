-- AlterEnum
ALTER TYPE "AchievementCategory" ADD VALUE 'DAILY_TOPIC';

-- AlterEnum
ALTER TYPE "NotificationType" ADD VALUE 'discussion';

-- AlterTable
ALTER TABLE "BattleParticipant" ADD COLUMN     "current_question_id" TEXT,
ADD COLUMN     "last_activity" TIMESTAMP(3),
ADD COLUMN     "question_order" JSONB;

-- AlterTable
ALTER TABLE "UserRoadmap" ADD COLUMN     "status" "ProgressStatus" NOT NULL DEFAULT 'PENDING';

-- CreateIndex
CREATE INDEX "BattleParticipant_current_question_id_idx" ON "BattleParticipant"("current_question_id");

-- AddForeignKey
ALTER TABLE "BattleParticipant" ADD CONSTRAINT "BattleParticipant_current_question_id_fkey" FOREIGN KEY ("current_question_id") REFERENCES "BattleQuestion"("id") ON DELETE SET NULL ON UPDATE CASCADE;
