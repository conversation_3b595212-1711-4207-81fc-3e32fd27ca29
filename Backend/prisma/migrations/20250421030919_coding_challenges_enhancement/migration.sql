-- AlterTable
ALTER TABLE "Challenge" ADD COLUMN     "explanation" TEXT;

-- AlterTable
ALTER TABLE "TestCase" ADD COLUMN     "order_index" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "UserProgress" ALTER COLUMN "confidence_level" SET DEFAULT NULL,
ALTER COLUMN "progress_percentage" SET DEFAULT NULL;

-- CreateTable
CREATE TABLE "ChallengeExample" (
    "id" TEXT NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "input" TEXT NOT NULL,
    "output" TEXT NOT NULL,
    "explanation" TEXT,
    "order_index" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChallengeExample_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChallengeBoilerplate" (
    "id" TEXT NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "boilerplate_code" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChallengeBoilerplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserChallengeBookmark" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "challenge_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserChallengeBookmark_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ChallengeExample_challenge_id_idx" ON "ChallengeExample"("challenge_id");

-- CreateIndex
CREATE INDEX "ChallengeExample_order_index_idx" ON "ChallengeExample"("order_index");

-- CreateIndex
CREATE INDEX "ChallengeBoilerplate_challenge_id_idx" ON "ChallengeBoilerplate"("challenge_id");

-- CreateIndex
CREATE INDEX "ChallengeBoilerplate_language_idx" ON "ChallengeBoilerplate"("language");

-- CreateIndex
CREATE UNIQUE INDEX "ChallengeBoilerplate_challenge_id_language_key" ON "ChallengeBoilerplate"("challenge_id", "language");

-- CreateIndex
CREATE INDEX "UserChallengeBookmark_user_id_idx" ON "UserChallengeBookmark"("user_id");

-- CreateIndex
CREATE INDEX "UserChallengeBookmark_challenge_id_idx" ON "UserChallengeBookmark"("challenge_id");

-- CreateIndex
CREATE UNIQUE INDEX "UserChallengeBookmark_user_id_challenge_id_key" ON "UserChallengeBookmark"("user_id", "challenge_id");

-- CreateIndex
CREATE INDEX "TestCase_order_index_idx" ON "TestCase"("order_index");

-- AddForeignKey
ALTER TABLE "ChallengeExample" ADD CONSTRAINT "ChallengeExample_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "Challenge"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChallengeBoilerplate" ADD CONSTRAINT "ChallengeBoilerplate_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "Challenge"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserChallengeBookmark" ADD CONSTRAINT "UserChallengeBookmark_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserChallengeBookmark" ADD CONSTRAINT "UserChallengeBookmark_challenge_id_fkey" FOREIGN KEY ("challenge_id") REFERENCES "Challenge"("id") ON DELETE CASCADE ON UPDATE CASCADE;
