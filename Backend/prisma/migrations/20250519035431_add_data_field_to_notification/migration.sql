-- AlterTable
ALTER TABLE "Notification" ADD COLUMN     "data" TEXT;

-- CreateTable
CREATE TABLE "UserQuestionHistory" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "question_id" TEXT NOT NULL,
    "seen_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_seen_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "times_seen" INTEGER NOT NULL DEFAULT 1,
    "last_correct" BOOLEAN,

    CONSTRAINT "UserQuestionHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UserQuestionHistory_user_id_idx" ON "UserQuestionHistory"("user_id");

-- CreateIndex
CREATE INDEX "UserQuestionHistory_question_id_idx" ON "UserQuestionHistory"("question_id");

-- CreateIndex
CREATE UNIQUE INDEX "UserQuestionHistory_user_id_question_id_key" ON "UserQuestionHistory"("user_id", "question_id");

-- AddForeignKey
ALTER TABLE "UserQuestionHistory" ADD CONSTRAINT "UserQuestionHistory_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
