import { PrismaClient } from '@prisma/client';

import { seedAchievements } from './seeders/achievement.seeder';
import { seedArticles } from './seeders/article.seeder';
import { seedBattles } from './seeders/battle.seeder';
import { seedChallenges } from './seeders/challenge.seeder';
import { seedDailyTopics } from './seeders/dailyTopic.seeder';
import { seedResources } from './seeders/resource.seeder';
import { seedUsers } from './seeders/user.seeder';

const prisma = new PrismaClient();

async function main() {
  console.log('\n==================================================');
  console.log('🌱 DATABASE SEEDING PROCESS STARTED');
  console.log('==================================================');
  console.log('This process will populate the database with sample data');
  console.log('Please wait while all seeders run...');

  const startTime = Date.now();

  // First seed roles as they're required for users
  console.log('\n🔑 SEEDING ROLES...');
  await import('./seeders/role.seeder');

  // Then seed users
  console.log('\n👤 SEEDING USERS...');
  await seedUsers();

  // Then seed content
  console.log('\n📚 SEEDING CORE CONTENT...');
  console.log('--------------------------------------------------');
  console.log('🔄 Seeding subjects...');
  await import('./seeders/subject.seeder');
  console.log('🔄 Seeding topics...');
  await import('./seeders/topic.seeder');

  // Seed challenges (merged with coding challenges)
  console.log('🔄 Seeding challenges...');
  await seedChallenges();

  // Seed battles
  console.log('🔄 Seeding battles...');
  await seedBattles();

  // Seed articles
  console.log('🔄 Seeding articles...');
  await seedArticles();

  // Seed resources
  console.log('🔄 Seeding resources...');
  await seedResources();

  // Seed achievements
  console.log('🔄 Seeding achievements...');
  await seedAchievements();

  // Seed daily topics
  console.log('🔄 Seeding daily topics...');
  await seedDailyTopics();

  const endTime = Date.now();
  const totalTimeSeconds = Math.floor((endTime - startTime) / 1000);
  const minutes = Math.floor(totalTimeSeconds / 60);
  const seconds = totalTimeSeconds % 60;

  console.log('\n==================================================');
  console.log('✅ DATABASE SEEDING COMPLETED SUCCESSFULLY');
  console.log('==================================================');
  console.log(
    `⏱️ Total seeding time: ${minutes} minutes and ${seconds} seconds`,
  );
  console.log('🎉 The database is now populated with sample data');
  console.log('==================================================');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
