/**
 * @file roles.ts
 * @description Seed script for roles and user role assignments
 */
import { PrismaClient, RoleType } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Default roles to be created in the system
 */
const defaultRoles = [
  {
    name: 'Administrator',
    description: 'Full access to all system features',
    type: RoleType.ADMIN,
  },
  {
    name: 'Moderator',
    description: 'Can moderate content and users',
    type: RoleType.MODERATOR,
  },
  {
    name: 'Contributor',
    description: 'Can create and edit content',
    type: RoleType.CONTRIBUTOR,
  },
  {
    name: 'User',
    description: 'Standard user with basic access',
    type: RoleType.USER,
  },
];

/**
 * Create default roles in the database
 */
async function createDefaultRoles() {
  console.log('Creating default roles...');

  const roles = [];

  for (const roleData of defaultRoles) {
    // Check if role already exists
    const existingRole = await prisma.role.findFirst({
      where: { type: roleData.type },
    });

    if (existingRole) {
      console.log(`Role ${roleData.name} (${roleData.type}) already exists.`);
      roles.push(existingRole);
      continue;
    }

    // Create new role
    const role = await prisma.role.create({
      data: roleData,
    });

    console.log(`Created role: ${role.name} (${role.type})`);
    roles.push(role);
  }

  return roles;
}

/**
 * Assign default USER role to all users who don't have any roles
 */
async function assignDefaultRolesToUsers() {
  console.log('Assigning default roles to users...');

  // Get the USER role
  const userRole = await prisma.role.findFirst({
    where: { type: RoleType.USER },
  });

  if (!userRole) {
    console.error('USER role not found. Please create roles first.');
    return;
  }

  // Get all users who don't have any roles assigned
  const usersWithoutRoles = await prisma.user.findMany({
    where: {
      user_roles: {
        none: {},
      },
    },
  });

  console.log(`Found ${usersWithoutRoles.length} users without roles.`);

  // Assign USER role to each user
  for (const user of usersWithoutRoles) {
    await prisma.userRole.create({
      data: {
        user_id: user.id,
        role_id: userRole.id,
      },
    });

    console.log(`Assigned USER role to user: ${user.email}`);
  }
}

/**
 * Assign ADMIN role to specified users
 * @param adminEmails Array of email addresses for admin users
 */
async function assignAdminRoles(adminEmails: string[]) {
  console.log('Assigning admin roles...');

  // Get the ADMIN role
  const adminRole = await prisma.role.findFirst({
    where: { type: RoleType.ADMIN },
  });

  if (!adminRole) {
    console.error('ADMIN role not found. Please create roles first.');
    return;
  }

  // Get users by email
  for (const email of adminEmails) {
    const user = await prisma.user.findFirst({
      where: { email },
    });

    if (!user) {
      console.warn(`User with email ${email} not found.`);
      continue;
    }

    // Check if user already has ADMIN role
    const existingRole = await prisma.userRole.findFirst({
      where: {
        user_id: user.id,
        role_id: adminRole.id,
      },
    });

    if (existingRole) {
      console.log(`User ${email} already has ADMIN role.`);
      continue;
    }

    // Assign ADMIN role
    await prisma.userRole.create({
      data: {
        user_id: user.id,
        role_id: adminRole.id,
      },
    });

    console.log(`Assigned ADMIN role to user: ${email}`);
  }
}

/**
 * Main seed function
 */
export async function seedRoles() {
  try {
    // Create default roles
    await createDefaultRoles();

    // Assign default roles to users
    await assignDefaultRolesToUsers();

    // Assign admin roles to specific users
    // Replace these with actual admin email addresses
    const adminEmails = [
      '<EMAIL>',
      // Add more admin emails as needed
    ];

    await assignAdminRoles(adminEmails);

    console.log('Role seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding roles:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedRoles()
    .then(() => console.log('Seeding completed.'))
    .catch((e) => {
      console.error('Error during seeding:', e);
      process.exit(1);
    });
}
