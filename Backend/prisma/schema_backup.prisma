generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Article {
  id          String              @id @default(uuid())
  title       String              @db.VarChar(255)
  content     String
  created_at  DateTime            @default(now())
  updated_at  DateTime            @updatedAt
  author_id   String
  resource_id String?
  topic_id    String
  status      ArticleStatus       @default(DRAFT)
  author      User                @relation(fields: [author_id], references: [id], onDelete: Cascade)
  resource    Resource?           @relation(fields: [resource_id], references: [id])
  topic       Topic               @relation(fields: [topic_id], references: [id], onDelete: Cascade)
  comments    Comment[]
  moderations ContentModeration[]
  likes       Like[]
  logs        SubmissionLog[]
  versions    Version[]

  @@index([author_id])
  @@index([topic_id])
}

model SubmissionLog {
  id         String   @id @default(uuid())
  type       String
  created_at DateTime @default(now())
  article_id String
  author_id  String
  article    Article  @relation(fields: [article_id], references: [id])
  author     User     @relation(fields: [author_id], references: [id])

  @@index([author_id])
  @@index([article_id])
}

model Battle {
  id                   String              @id @default(uuid())
  title                String              @db.VarChar(255)
  description          String?
  difficulty           Difficulty
  length               Length
  created_at           DateTime            @default(now())
  updated_at           DateTime            @updatedAt
  topic_id             String
  user_id              String
  current_participants Int                 @default(0)
  end_time             DateTime?
  max_participants     Int                 @default(2)
  points_per_question  Int                 @default(10)
  start_time           DateTime?
  status               BattleStatus        @default(UPCOMING)
  time_per_question    Int                 @default(30)
  total_questions      Int                 @default(10)
  type                 BattleType
  topic                Topic               @relation(fields: [topic_id], references: [id], onDelete: Cascade)
  user                 User                @relation(fields: [user_id], references: [id], onDelete: Cascade)
  leaderboard          BattleLeaderboard[]
  participants         BattleParticipant[]
  questions            BattleQuestion[]
  rules                BattleToRule[]

  @@index([topic_id])
  @@index([user_id])
  @@index([status])
  @@index([type])
}

model BattleParticipant {
  id                  String          @id @default(uuid())
  battle_id           String
  user_id             String
  score               Int             @default(0)
  rank                Int?
  joined_at           DateTime        @default(now())
  completed_at        DateTime?
  current_question_id String?
  question_order      Json? // Stores the personalized order of questions for this participant
  last_activity       DateTime? // Tracks when the participant last interacted with a question
  battle              Battle          @relation(fields: [battle_id], references: [id], onDelete: Cascade)
  user                User            @relation(fields: [user_id], references: [id], onDelete: Cascade)
  current_question    BattleQuestion? @relation("CurrentQuestion", fields: [current_question_id], references: [id])

  @@unique([battle_id, user_id])
  @@index([battle_id])
  @@index([user_id])
  @@index([current_question_id])
}

model BattleQuestion {
  id             String              @id @default(uuid())
  battle_id      String
  question       String
  options        Json
  correct_answer String
  points         Int                 @default(10)
  time_limit     Int                 @default(30)
  order          Int
  created_at     DateTime            @default(now())
  answers        BattleAnswer[]
  battle         Battle              @relation(fields: [battle_id], references: [id], onDelete: Cascade)
  participants   BattleParticipant[] @relation("CurrentQuestion")

  @@index([battle_id])
}

model BattleAnswer {
  id          String         @id @default(uuid())
  question_id String
  user_id     String
  answer      String
  is_correct  Boolean
  time_taken  Int
  created_at  DateTime       @default(now())
  question    BattleQuestion @relation(fields: [question_id], references: [id], onDelete: Cascade)
  user        User           @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([question_id, user_id], name: "question_id_user_id")
  @@index([question_id])
  @@index([user_id])
}

model UserQuestionHistory {
  id           String   @id @default(uuid())
  user_id      String
  question_id  String
  seen_at      DateTime @default(now())
  last_seen_at DateTime @default(now())
  times_seen   Int      @default(1)
  last_correct Boolean? // null if never answered
  user         User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, question_id], name: "user_id_question_id")
  @@index([user_id])
  @@index([question_id])
}

model BattleLeaderboard {
  id         String   @id @default(uuid())
  battle_id  String
  user_id    String
  score      Int      @default(0)
  rank       Int
  time_taken Int
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  battle     Battle   @relation(fields: [battle_id], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([battle_id, user_id])
  @@index([battle_id])
  @@index([user_id])
  @@index([score])
}

model BattleRule {
  id          String         @id @default(uuid())
  name        String         @unique
  description String
  category    RuleCategory
  is_active   Boolean        @default(true)
  is_default  Boolean        @default(false)
  created_at  DateTime       @default(now())
  updated_at  DateTime       @updatedAt
  battles     BattleToRule[]

  @@index([category])
  @@index([is_default])
}

model BattleToRule {
  id         String     @id @default(uuid())
  battle_id  String
  rule_id    String
  created_at DateTime   @default(now())
  battle     Battle     @relation(fields: [battle_id], references: [id], onDelete: Cascade)
  rule       BattleRule @relation(fields: [rule_id], references: [id], onDelete: Cascade)

  @@unique([battle_id, rule_id])
  @@index([battle_id])
  @@index([rule_id])
}

model Challenge {
  id                 String                  @id @default(uuid())
  title              String
  description        String
  points             Int
  difficulty         Difficulty
  category           ChallengeCategory
  constraints        String
  tags               String[]
  solutions          Json?
  created_at         DateTime                @default(now())
  updated_at         DateTime                @updatedAt
  status             ChallengeStatus         @default(PENDING)
  example_input      String
  example_output     String
  function_signature String
  input_format       String
  memory_limit       Int?
  output_format      String
  time_limit         Int?
  topic_id           String
  explanation        String?
  topic              Topic                   @relation(fields: [topic_id], references: [id])
  boilerplates       ChallengeBoilerplate[]
  discussions        ChallengeDiscussion[]
  examples           ChallengeExample[]
  hints              ChallengeHint[]
  submissions        ChallengeSubmission[]
  language_metrics   LanguageMetrics[]
  roadmap_challenges RoadmapChallenge[]
  test_cases         TestCase[]
  bookmarks          UserChallengeBookmark[]
  user_hints         UserHint[]

  @@index([difficulty, points])
  @@index([topic_id])
}

model Chat {
  id         String   @id @default(uuid())
  message    String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  user1_id   String
  user2_id   String
  user1      User     @relation("ChatUser1", fields: [user1_id], references: [id])
  user2      User     @relation("ChatUser2", fields: [user2_id], references: [id])
}

model Course {
  id           String        @id @default(uuid())
  title        String
  description  String?
  instructor   String?
  duration     Int?
  level        CourseLevel?
  created_at   DateTime      @default(now())
  updated_at   DateTime      @updatedAt
  end_date     DateTime?
  start_date   DateTime?
  certificates Certificate[]
}

model Forum {
  id          String      @id @default(uuid())
  title       String
  description String?
  tags        Json?
  created_at  DateTime    @default(now())
  updated_at  DateTime    @updatedAt
  created_by  String
  is_active   Boolean     @default(true)
  user        User        @relation(fields: [created_by], references: [id], onDelete: Cascade)
  posts       ForumPost[]
}

model InterviewQuestion {
  id          String      @id @default(uuid())
  question    String
  answer      String?
  difficulty  Difficulty?
  topic       String?
  tags        Json?
  created_at  DateTime    @default(now())
  updated_at  DateTime    @updatedAt
  resource_id String?
  resource    Resource?   @relation(fields: [resource_id], references: [id])
}

model Job {
  id                   String    @id @default(uuid())
  title                String
  description          String?
  company              String
  location             String?
  salary               Decimal?  @db.Decimal(10, 2)
  created_at           DateTime  @default(now())
  updated_at           DateTime  @updatedAt
  application_deadline DateTime?
  job_type             JobType?
  posted_date          DateTime?
}

model MainConcept {
  id          String               @id @default(uuid())
  name        String               @unique
  description String
  created_at  DateTime             @default(now())
  updated_at  DateTime             @updatedAt
  order       Int
  subjects    MainConceptSubject[]
  quizzes     Quiz[]
  roadmaps    RoadmapMainConcept[]
}

model Quiz {
  id              String           @id @default(uuid())
  title           String
  description     String
  type            QuizType
  created_at      DateTime         @default(now())
  updated_at      DateTime         @updatedAt
  passing_score   Int
  subject_id      String?
  time_limit      Int?
  topic_id        String?
  main_concept_id String?
  questions       Question[]
  main_concept    MainConcept?     @relation(fields: [main_concept_id], references: [id])
  subject         Subject?         @relation(fields: [subject_id], references: [id])
  topic           Topic?           @relation(fields: [topic_id], references: [id])
  quiz_questions  QuizQuestion[]
  submissions     QuizSubmission[]

  @@index([topic_id])
  @@index([subject_id])
  @@index([main_concept_id])
}

model Question {
  id             String   @id @default(uuid())
  question       String
  type           String
  points         Int
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt
  correct_answer String
  quiz_id        String
  test_cases     Json?
  options        Option[]
  quiz           Quiz     @relation(fields: [quiz_id], references: [id], onDelete: Cascade)

  @@index([quiz_id])
}

model QuizAnswer {
  id          String                 @id @default(uuid())
  answer      String
  created_at  DateTime               @default(now())
  updated_at  DateTime               @updatedAt
  is_correct  Boolean                @default(false)
  question_id String
  question    QuizQuestion           @relation(fields: [question_id], references: [id])
  submissions QuizSubmissionAnswer[]
}

model QuizOption {
  id               String       @id @default(uuid())
  created_at       DateTime     @default(now())
  updated_at       DateTime     @updatedAt
  answer_text      String
  is_correct       Boolean
  quiz_question_id String
  quiz_question    QuizQuestion @relation(fields: [quiz_question_id], references: [id])
}

model QuizSubmission {
  id         String                 @id @default(uuid())
  score      Int
  results    Json
  created_at DateTime               @default(now())
  is_passed  Boolean
  quiz_id    String
  time_spent Int
  user_id    String
  quiz       Quiz                   @relation(fields: [quiz_id], references: [id])
  user       User                   @relation(fields: [user_id], references: [id])
  answers    QuizSubmissionAnswer[]

  @@index([user_id])
  @@index([quiz_id])
}

model QuizSubmissionAnswer {
  id            String         @id @default(uuid())
  created_at    DateTime       @default(now())
  updated_at    DateTime       @updatedAt
  answer_id     String
  question_id   String
  submission_id String
  answer        QuizAnswer     @relation(fields: [answer_id], references: [id])
  question      QuizQuestion   @relation(fields: [question_id], references: [id])
  submission    QuizSubmission @relation(fields: [submission_id], references: [id])
}

model QuizQuestion {
  id          String                 @id @default(uuid())
  question    String
  type        String
  created_at  DateTime               @default(now())
  updated_at  DateTime               @updatedAt
  quiz_id     String
  answers     QuizAnswer[]
  options     QuizOption[]
  quiz        Quiz                   @relation(fields: [quiz_id], references: [id])
  submissions QuizSubmissionAnswer[]

  @@index([quiz_id])
}

model Resource {
  id                 String              @id @default(uuid())
  title              String
  description        String
  type               String
  url                String
  category           String
  tags               String[]
  content            String?
  created_at         DateTime            @default(now())
  updated_at         DateTime            @updatedAt
  downloadCount      Int                 @default(0)
  rating             Decimal?            @db.Decimal(3, 2)
  difficulty         Difficulty
  language           String
  user_id            String
  articles           Article[]
  moderations        ContentModeration[]
  interviewQuestions InterviewQuestion[]
  user               User                @relation(fields: [user_id], references: [id])
  versions           ResourceVersion[]

  @@index([user_id])
  @@index([category])
  @@index([type])
}

model RoadmapCategory {
  id          String    @id @default(uuid())
  name        String    @unique
  description String?
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  roadmaps    Roadmap[]
}

model Roadmap {
  id             String               @id @default(uuid())
  title          String               @unique
  description    String?
  difficulty     Difficulty?
  estimatedHours Int?
  popularity     Int                  @default(0)
  version        String               @default("1.0")
  created_at     DateTime             @default(now())
  deleted_at     DateTime?
  is_public      Boolean              @default(false)
  updated_at     DateTime             @updatedAt
  user_id        String
  category_id    String?
  tags           String?
  comments       Comment[]
  likes          Like[]
  progress       Progress[]
  category       RoadmapCategory?     @relation(fields: [category_id], references: [id])
  user           User                 @relation(fields: [user_id], references: [id], onDelete: Cascade)
  challenges     RoadmapChallenge[]
  main_concepts  RoadmapMainConcept[]
  topics         RoadmapTopic[]
  user_roadmaps  UserRoadmap[]

  @@index([user_id, is_public])
  @@index([deleted_at])
  @@index([title])
}

model Subject {
  id              String               @id @default(uuid())
  title           String               @unique
  description     String
  order           Int
  created_at      DateTime             @default(now())
  updated_at      DateTime             @default(now())
  main_concepts   MainConceptSubject[]
  placement_tests PlacementTest[]
  quizzes         Quiz[]
  topics          SubjectTopic[]
  user_progress   UserProgress[]
  virtual_labs    VirtualLab[]

  @@index([id])
}

model Topic {
  id            String              @id @default(uuid())
  title         String
  description   String
  order         Int
  content       String?
  resources     String[]
  prerequisites String[]
  created_at    DateTime            @default(now())
  updated_at    DateTime            @default(now())
  articles      Article[]
  battles       Battle[]
  challenges    Challenge[]
  moderations   ContentModeration[]
  daily_topics  DailyTopic[]
  progress      Progress[]
  quizzes       Quiz[]
  roadmaps      RoadmapTopic[]
  study_groups  StudyGroup[]
  subjects      SubjectTopic[]
  user_progress UserProgress[]

  @@index([id])
}

model RoadmapMainConcept {
  id              String      @id @default(uuid())
  roadmap_id      String
  main_concept_id String
  order           Int
  created_at      DateTime    @default(now())
  main_concept    MainConcept @relation(fields: [main_concept_id], references: [id])
  roadmap         Roadmap     @relation(fields: [roadmap_id], references: [id])

  @@unique([roadmap_id, main_concept_id])
  @@index([roadmap_id])
  @@index([main_concept_id])
}

model MainConceptSubject {
  id              String      @id @default(uuid())
  main_concept_id String
  subject_id      String
  order           Int
  created_at      DateTime    @default(now())
  main_concept    MainConcept @relation(fields: [main_concept_id], references: [id])
  subject         Subject     @relation(fields: [subject_id], references: [id])

  @@unique([main_concept_id, subject_id])
  @@index([main_concept_id])
  @@index([subject_id])
}

model SubjectTopic {
  id         String   @id @default(uuid())
  subject_id String
  topic_id   String
  order      Int
  created_at DateTime @default(now())
  subject    Subject  @relation(fields: [subject_id], references: [id])
  topic      Topic    @relation(fields: [topic_id], references: [id])

  @@unique([subject_id, topic_id])
  @@index([subject_id])
  @@index([topic_id])
}

model RoadmapTopic {
  id         String   @id @default(uuid())
  roadmap_id String
  topic_id   String
  order      Int
  created_at DateTime @default(now())
  roadmap    Roadmap  @relation(fields: [roadmap_id], references: [id])
  topic      Topic    @relation(fields: [topic_id], references: [id])

  @@unique([roadmap_id, topic_id])
  @@index([roadmap_id])
  @@index([topic_id])
}

model College {
  id          String   @id @default(uuid())
  name        String   @unique
  location    String?
  website     String?
  logo_url    String?
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  users       User[]
}

model User {
  id                              String                         @id @default(uuid())
  supabase_id                     String                         @unique
  email                           String                         @unique
  username                        String                         @unique
  full_name                       String?
  avatar_url                      String?
  bio                             String?
  address                         String?
  github_url                      String?
  linkedin_url                    String?
  twitter_url                     String?
  website_url                     String?
  specialization                  String?
  college_id                      String?
  college                         College?                       @relation(fields: [college_id], references: [id])
  college_name                    String?
  graduation_year                 Int?
  skills                          String[]
  experience_level                ExperienceLevel?
  created_at                      DateTime                       @default(now())
  updated_at                      DateTime                       @updatedAt
  status                          String?                        @default("active")
  note                            String?
  is_active                       Boolean                        @default(true)
  timezone                        String?
  accessibility_prefs             Json?
  deleted_at                      DateTime?
  github_stats                    Json?
  is_verified                     Boolean                        @default(false)
  learning_streak                 Int                            @default(0)
  leetcode_stats                  Json?
  preferred_language              String?
  role_id                         String?
  access_logs                     AccessLog[]
  achievement_notifications       AchievementNotification[]
  activityLogs                    ActivityLog[]
  admin_audit_logs                AdminAuditLog[]
  api_keys                        ApiKey[]
  approval_history                ApprovalHistory[]
  articles                        Article[]
  audit_logs                      AuditLog[]
  battles                         Battle[]
  battle_answers                  BattleAnswer[]
  battle_leaderboard_entries      BattleLeaderboard[]
  battle_participations           BattleParticipant[]
  question_history                UserQuestionHistory[]
  bookmark_collections            BookmarkCollection[]
  assigned_bugs                   BugReport[]                    @relation("BugAssignee")
  bug_reports                     BugReport[]
  certificates                    Certificate[]
  challenge_discussions           ChallengeDiscussion[]
  challenges                      ChallengeSubmission[]
  change_history                  ChangeHistory[]
  chats_as_user1                  Chat[]                         @relation("ChatUser1")
  chats_as_user2                  Chat[]                         @relation("ChatUser2")
  authored_code_reviews           CodeReview[]                   @relation("CodeAuthor")
  reviewed_code_reviews           CodeReview[]                   @relation("CodeReviewer")
  coding_sandboxes                CodingSandbox[]
  comments                        Comment[]
  community_challenge_submissions CommunityChallengeSubmission[]
  contents                        Content[]
  moderations                     ContentModeration[]
  curated_playlists               ContentPlaylist[]
  content_reports                 ContentReport[]
  daily_challenge_submissions     DailyChallengeSubmission[]
  daily_topic_completions         DailyTopicCompletion[]
  daily_topic_views               DailyTopicView[]
  discussion_flags                DiscussionFlag[]
  discussion_notifications        DiscussionNotification[]
  discussion_votes                DiscussionVote[]
  event_registrations             EventRegistration[]
  feature_requests                FeatureRequest[]
  feature_votes                   FeatureRequestVote[]
  followers                       Follow[]                       @relation("FollowerRelation")
  following                       Follow[]                       @relation("FollowingRelation")
  forums                          Forum[]
  forum_comments                  ForumComment[]
  forum_posts                     ForumPost[]
  hardware_kits_sold              HardwareKit[]
  in_app_purchases                InAppPurchase[]
  internship_applications         InternshipApplication[]
  interview_preps                 InterviewPrep[]
  iot_devices                     IoTDevice[]
  lab_sessions                    LabSession[]
  leaderboard_entries             LeaderboardEntry[]
  learning_recommendations        LearningRecommendation[]
  likes                           Like[]
  mentees                         Mentorship[]                   @relation("Mentee")
  mentorships                     Mentorship[]                   @relation("Mentor")
  moderation_logs                 ModerationLog[]
  notifications                   Notification[]
  projects                        Project[]
  collaborations                  ProjectCollaboration[]
  quiz_submissions                QuizSubmission[]
  resources                       Resource[]
  roadmaps                        Roadmap[]
  security_audit_logs             SecurityAuditLog[]
  skill_assessments               SkillAssessment[]
  solution_views                  SolutionView[]
  study_groups                    StudyGroupMember[]
  submission_logs                 SubmissionLog[]
  subscriptions                   Subscription[]
  assigned_tickets                SupportTicket[]                @relation("TicketAgent")
  support_tickets                 SupportTicket[]                @relation("TicketCreator")
  team_projects                   TeamProjectMember[]
  ticket_responses                TicketResponse[]
  role                            Role?                          @relation(fields: [role_id], references: [id])
  user_achievements               UserAchievement[]
  user_activity_logs              UserActivityLog[]
  challenge_bookmarks             UserChallengeBookmark[]
  filter_presets                  UserFilterPreset[]
  user_hints                      UserHint[]
  learning_analytics              UserLearningAnalytics?
  learning_preferences            UserLearningPreference?        @relation("UserLearningPreferences")
  user_points                     UserPoints?
  user_progress                   UserProgress[]
  user_roadmaps                   UserRoadmap[]
  sessions                        UserSession[]
  settings                        UserSettings?
  activities                      Activity[]
  daily_activities                UserDailyActivity[]
  user_permissions                UserPermission[]
  user_streak                     UserStreak?

  @@index([role_id])
  @@index([email, username])
  @@index([deleted_at])
}

model UserPoints {
  id         String   @id @default(uuid())
  points     Int
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  user_id    String   @unique
  user       User     @relation(fields: [user_id], references: [id])
}

model UserProgress {
  id                  String    @id @default(uuid())
  created_at          DateTime  @default(now())
  updated_at          DateTime  @updatedAt
  completed_at        DateTime?
  confidence_level    Int?      @db.SmallInt
  is_completed        Boolean   @default(false)
  last_reviewed       DateTime?
  progress_percentage Int?      @db.SmallInt
  subject_id          String?
  time_spent          Int?
  topic_id            String?
  user_id             String
  subject             Subject?  @relation(fields: [subject_id], references: [id])
  topic               Topic?    @relation(fields: [topic_id], references: [id])
  user                User      @relation(fields: [user_id], references: [id])

  @@unique([user_id, topic_id])
  @@index([user_id])
  @@index([topic_id])
}

model UserRoadmap {
  id         String         @id @default(uuid())
  created_at DateTime       @default(now())
  updated_at DateTime       @updatedAt
  is_custom  Boolean        @default(false)
  roadmap_id String
  user_id    String
  roadmap    Roadmap        @relation(fields: [roadmap_id], references: [id])
  user       User           @relation(fields: [user_id], references: [id])
  status     ProgressStatus @default(PENDING)

  @@unique([user_id, roadmap_id])
}

model Project {
  id             String                 @id @default(uuid())
  title          String
  description    String
  github_url     String?
  demo_url       String?
  tech_stack     String[]
  image_url      String?
  contributors   String[]
  project_status ProjectStatus          @default(ongoing)
  visibility     Visibility             @default(public)
  created_at     DateTime               @default(now())
  updated_at     DateTime               @updatedAt
  user_id        String
  user           User                   @relation(fields: [user_id], references: [id], onDelete: Cascade)
  collaborations ProjectCollaboration[]
}

model TeamProject {
  id             String              @id @default(uuid())
  title          String
  description    String
  github_url     String?
  demo_url       String?
  tech_stack     String[]
  image_url      String?
  project_status ProjectStatus       @default(ongoing)
  visibility     Visibility          @default(public)
  created_at     DateTime            @default(now())
  updated_at     DateTime            @default(now())
  members        TeamProjectMember[]
}

model TeamProjectMember {
  id              String      @id @default(uuid())
  role            ProjectRole @default(member)
  created_at      DateTime    @default(now())
  updated_at      DateTime    @default(now())
  team_project_id String
  user_id         String
  team_project    TeamProject @relation(fields: [team_project_id], references: [id])
  user            User        @relation(fields: [user_id], references: [id])

  @@unique([user_id, team_project_id])
}

model ForumPost {
  id         String         @id @default(uuid())
  title      String
  content    String
  tags       String[]
  upvotes    Int            @default(0)
  created_at DateTime       @default(now())
  updated_at DateTime       @default(now())
  forum_id   String
  user_id    String
  comments   ForumComment[]
  forum      Forum          @relation(fields: [forum_id], references: [id])
  user       User           @relation(fields: [user_id], references: [id])
}

model ForumComment {
  id         String         @id @default(uuid())
  content    String
  upvotes    Int            @default(0)
  created_at DateTime       @default(now())
  updated_at DateTime       @default(now())
  parent_id  String?
  post_id    String
  user_id    String
  parent     ForumComment?  @relation("CommentReplies", fields: [parent_id], references: [id])
  replies    ForumComment[] @relation("CommentReplies")
  post       ForumPost      @relation(fields: [post_id], references: [id])
  user       User           @relation(fields: [user_id], references: [id])
}

model ChallengeSubmission {
  id                       String                   @id @default(uuid())
  code                     String
  language                 String
  status                   SubmissionStatus
  runtime_ms               Int?
  memory_used_kb           Int?
  score                    Int
  feedback                 String?
  created_at               DateTime                 @default(now())
  updated_at               DateTime                 @updatedAt
  challenge_id             String
  user_id                  String
  challenge                Challenge                @relation(fields: [challenge_id], references: [id])
  user                     User                     @relation(fields: [user_id], references: [id])
  optimization_suggestions OptimizationSuggestion[]
  shared_solution          SharedSolution?
  metrics                  SubmissionMetrics?

  @@index([challenge_id])
  @@index([user_id])
  @@index([status])
}

model Certificate {
  id              String    @id @default(uuid())
  title           String
  description     String
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt
  certificate_url String
  course_id       String?
  expiry_date     DateTime?
  issue_date      DateTime  @default(now())
  user_id         String
  course          Course?   @relation(fields: [course_id], references: [id])
  user            User      @relation(fields: [user_id], references: [id])
}

model Notification {
  id         String           @id @default(uuid())
  title      String
  message    String
  type       NotificationType
  link       String?
  data       String?
  created_at DateTime         @default(now())
  updated_at DateTime         @updatedAt
  is_read    Boolean          @default(false)
  user_id    String
  user       User             @relation(fields: [user_id], references: [id])
}

model Mentorship {
  id          String              @id @default(uuid())
  status      MentorshipStatus    @default(pending)
  topics      String[]
  description String?
  created_at  DateTime            @default(now())
  updated_at  DateTime            @updatedAt
  mentee_id   String
  mentor_id   String
  mentee      User                @relation("Mentee", fields: [mentee_id], references: [id])
  mentor      User                @relation("Mentor", fields: [mentor_id], references: [id])
  sessions    MentorshipSession[]

  @@unique([mentor_id, mentee_id])
}

model StudyGroup {
  id               String               @id @default(uuid())
  name             String
  description      String
  created_at       DateTime             @default(now())
  updated_at       DateTime             @default(now())
  is_private       Boolean              @default(false)
  max_members      Int                  @default(5)
  meeting_schedule String?
  topic_id         String
  weekly_goal      String?
  topic            Topic                @relation(fields: [topic_id], references: [id])
  members          StudyGroupMember[]
  shared_resources StudyGroupResource[]
}

model StudyGroupResource {
  id          String     @id @default(uuid())
  title       String
  url         String
  description String
  created_at  DateTime   @default(now())
  group_id    String
  group       StudyGroup @relation(fields: [group_id], references: [id])
}

model StudyGroupMember {
  id             String     @id @default(uuid())
  role           GroupRole  @default(member)
  created_at     DateTime   @default(now())
  updated_at     DateTime   @default(now())
  study_group_id String
  user_id        String
  study_group    StudyGroup @relation(fields: [study_group_id], references: [id])
  user           User       @relation(fields: [user_id], references: [id])

  @@unique([user_id, study_group_id])
}

model Comment {
  id         String    @id @default(uuid())
  content    String
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  article_id String?
  parent_id  String?
  roadmap_id String?
  user_id    String
  article    Article?  @relation(fields: [article_id], references: [id])
  parent     Comment?  @relation("CommentReplies", fields: [parent_id], references: [id])
  replies    Comment[] @relation("CommentReplies")
  roadmap    Roadmap?  @relation(fields: [roadmap_id], references: [id])
  user       User      @relation(fields: [user_id], references: [id])
  likes      Like[]

  @@index([user_id])
  @@index([roadmap_id])
  @@index([article_id])
  @@index([parent_id])
}

model Like {
  id         String   @id @default(uuid())
  article_id String?
  comment_id String?
  created_at DateTime @default(now())
  roadmap_id String?
  user_id    String
  article    Article? @relation(fields: [article_id], references: [id])
  comment    Comment? @relation(fields: [comment_id], references: [id])
  roadmap    Roadmap? @relation(fields: [roadmap_id], references: [id])
  user       User     @relation(fields: [user_id], references: [id])

  @@unique([user_id, article_id])
  @@unique([user_id, roadmap_id])
  @@unique([user_id, comment_id])
  @@index([article_id])
  @@index([roadmap_id])
  @@index([comment_id])
}

model Achievement {
  id                String                 @id @default(uuid())
  description       String
  created_at        DateTime               @default(now())
  category          AchievementCategory
  icon_url          String
  is_active         Boolean                @default(true)
  is_hidden         Boolean                @default(false)
  name              String                 @unique
  points            Int
  tier              Int                    @default(1)
  trigger_type      AchievementTriggerType
  trigger_value     Int                    @default(1)
  updated_at        DateTime               @updatedAt
  user_achievements UserAchievement[]

  @@index([category])
  @@index([trigger_type])
  @@index([is_active])
}

model DailyTopic {
  id          String                 @id @default(uuid())
  date        DateTime               @unique
  created_at  DateTime               @default(now())
  topic_id    String
  topic       Topic                  @relation(fields: [topic_id], references: [id])
  completions DailyTopicCompletion[]
  views       DailyTopicView[]

  @@index([topic_id])
}

model DailyTopicView {
  id             String     @id @default(uuid())
  created_at     DateTime   @default(now())
  updated_at     DateTime   @updatedAt
  daily_topic_id String
  user_id        String
  view_count     Int        @default(0)
  daily_topic    DailyTopic @relation(fields: [daily_topic_id], references: [id])
  user           User       @relation(fields: [user_id], references: [id])

  @@unique([user_id, daily_topic_id])
  @@index([user_id])
  @@index([daily_topic_id])
}

model DailyTopicCompletion {
  id             String     @id @default(uuid())
  created_at     DateTime   @default(now())
  daily_topic_id String
  time_spent     Int        @default(0)
  user_id        String
  daily_topic    DailyTopic @relation(fields: [daily_topic_id], references: [id])
  user           User       @relation(fields: [user_id], references: [id])

  @@index([user_id])
  @@index([daily_topic_id])
}

model Role {
  id          String           @id @default(uuid())
  name        String           @unique
  description String?
  created_at  DateTime         @default(now()) @map("created_at")
  updated_at  DateTime         @updatedAt @map("updated_at")
  users       User[]
  permissions RolePermission[]

  @@map("roles")
}

model Permission {
  id          String           @id @default(uuid())
  name        String           @unique
  description String?
  key         String           @unique
  created_at  DateTime         @default(now()) @map("created_at")
  updated_at  DateTime         @updatedAt @map("updated_at")
  roles       RolePermission[]
  users       UserPermission[]
  features    Feature[]        @relation("FeatureToPermission")

  @@map("permissions")
}

model Feature {
  id          String       @id @default(uuid())
  name        String       @unique
  description String?
  created_at  DateTime     @default(now()) @map("created_at")
  updated_at  DateTime     @updatedAt @map("updated_at")
  permissions Permission[] @relation("FeatureToPermission")

  @@map("features")
}

model RolePermission {
  id            String     @id @default(uuid())
  role_id       String
  permission_id String
  created_at    DateTime   @default(now()) @map("created_at")
  updated_at    DateTime   @updatedAt @map("updated_at")
  permission    Permission @relation(fields: [permission_id], references: [id])
  role          Role       @relation(fields: [role_id], references: [id])

  @@unique([role_id, permission_id])
  @@map("role_permissions")
}

model UserPermission {
  id            String     @id @default(uuid())
  user_id       String
  permission_id String
  created_at    DateTime   @default(now()) @map("created_at")
  updated_at    DateTime   @updatedAt @map("updated_at")
  permission    Permission @relation(fields: [permission_id], references: [id])
  user          User       @relation(fields: [user_id], references: [id])

  @@unique([user_id, permission_id])
  @@map("user_permissions")
}

model UserActivityLog {
  id        String   @id @default(uuid())
  action    String
  details   Json?
  timestamp DateTime @default(now())
  user_id   String
  user      User     @relation(fields: [user_id], references: [id])

  @@index([user_id])
  @@index([action])
}

model ContentReport {
  id           String   @id @default(uuid())
  reason       String
  details      String?
  status       String   @default("pending")
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  content_id   String
  content_type String
  reporter_id  String
  reporter     User     @relation(fields: [reporter_id], references: [id])

  @@index([content_id])
  @@index([reporter_id])
}

model ModerationLog {
  id           String   @id @default(uuid())
  action       String
  notes        String?
  created_at   DateTime @default(now())
  content_id   String
  content_type String
  moderator_id String
  moderator    User     @relation(fields: [moderator_id], references: [id])

  @@index([content_id])
  @@index([moderator_id])
}

model SystemConfig {
  id          String   @id @default(uuid())
  key         String   @unique
  value       Json
  category    String
  description String?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  is_active   Boolean  @default(true)

  @@index([category])
}

model EmailTemplate {
  id         String   @id @default(uuid())
  name       String   @unique
  subject    String
  content    String
  variables  String[]
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  is_active  Boolean  @default(true)
}

model FeatureFlag {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  rules       Json?
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  is_enabled  Boolean  @default(false)
}

model AdminAuditLog {
  id         String   @id @default(uuid())
  action     String
  entity     String
  details    Json?
  created_at DateTime @default(now())
  admin_id   String
  entity_id  String
  ip_address String?
  user_agent String?
  admin      User     @relation(fields: [admin_id], references: [id])

  @@index([admin_id])
  @@index([action])
  @@index([entity])
}

model SecurityAuditLog {
  id          String   @id @default(uuid())
  type        String
  severity    String
  description String
  metadata    Json?
  created_at  DateTime @default(now())
  ip_address  String?
  user_agent  String?
  user_id     String?
  user        User?    @relation(fields: [user_id], references: [id])

  @@index([type])
  @@index([severity])
  @@index([user_id])
}

model ChangeHistory {
  id         String   @id @default(uuid())
  entity     String
  action     String
  changes    Json
  reason     String?
  created_at DateTime @default(now())
  entity_id  String
  user_id    String
  user       User     @relation(fields: [user_id], references: [id])

  @@index([entity])
  @@index([entity_id])
  @@index([user_id])
}

model AccessLog {
  id          String   @id @default(uuid())
  route       String
  method      String
  duration    Int
  created_at  DateTime @default(now())
  ip_address  String
  status_code Int
  user_agent  String?
  user_id     String?
  user        User?    @relation(fields: [user_id], references: [id])

  @@index([user_id])
  @@index([route])
  @@index([status_code])
}

model SystemLog {
  id         String   @id @default(uuid())
  type       String
  level      String
  message    String
  metadata   Json?
  created_at DateTime @default(now())

  @@index([type])
  @@index([level])
}

model SupportTicket {
  id          String           @id @default(uuid())
  title       String
  description String
  status      TicketStatus     @default(open)
  priority    TicketPriority   @default(medium)
  category    String
  created_at  DateTime         @default(now())
  updated_at  DateTime         @updatedAt
  assigned_to String?
  user_id     String
  agent       User?            @relation("TicketAgent", fields: [assigned_to], references: [id])
  user        User             @relation("TicketCreator", fields: [user_id], references: [id])
  responses   TicketResponse[]

  @@index([user_id])
  @@index([assigned_to])
  @@index([status])
}

model TicketResponse {
  id          String        @id @default(uuid())
  content     String
  created_at  DateTime      @default(now())
  updated_at  DateTime      @updatedAt
  is_internal Boolean       @default(false)
  ticket_id   String
  user_id     String
  ticket      SupportTicket @relation(fields: [ticket_id], references: [id])
  user        User          @relation(fields: [user_id], references: [id])

  @@index([ticket_id])
  @@index([user_id])
}

model BugReport {
  id                 String       @id @default(uuid())
  title              String
  description        String
  status             ReportStatus @default(pending)
  severity           Severity     @default(medium)
  environment        String?
  created_at         DateTime     @default(now())
  updated_at         DateTime     @updatedAt
  actual_behavior    String?
  assigned_to        String?
  expected_behavior  String?
  steps_to_reproduce String?
  user_id            String
  assignee           User?        @relation("BugAssignee", fields: [assigned_to], references: [id])
  user               User         @relation(fields: [user_id], references: [id])

  @@index([user_id])
  @@index([assigned_to])
  @@index([status])
}

model FeatureRequest {
  id          String               @id @default(uuid())
  title       String
  description String
  status      RequestStatus        @default(pending)
  priority    Priority             @default(medium)
  category    String
  upvotes     Int                  @default(0)
  created_at  DateTime             @default(now())
  updated_at  DateTime             @updatedAt
  user_id     String
  user        User                 @relation(fields: [user_id], references: [id])
  voters      FeatureRequestVote[]

  @@index([user_id])
  @@index([status])
}

model FeatureRequestVote {
  id                 String         @id @default(uuid())
  created_at         DateTime       @default(now())
  feature_request_id String
  user_id            String
  feature_request    FeatureRequest @relation(fields: [feature_request_id], references: [id])
  user               User           @relation(fields: [user_id], references: [id])

  @@unique([feature_request_id, user_id])
}

model SharedSolution {
  id            String              @id @default(uuid())
  submission_id String              @unique
  share_token   String              @unique
  title         String?
  description   String?
  is_public     Boolean             @default(true)
  expires_at    DateTime?
  created_at    DateTime            @default(now())
  updated_at    DateTime            @updatedAt
  view_count    Int                 @default(0)
  submission    ChallengeSubmission @relation(fields: [submission_id], references: [id], onDelete: Cascade)
  views         SolutionView[]

  @@index([share_token])
  @@index([submission_id])
}

model SolutionView {
  id                 String         @id @default(uuid())
  shared_solution_id String
  viewer_id          String?
  ip_address         String?
  user_agent         String?
  referrer           String?
  created_at         DateTime       @default(now())
  shared_solution    SharedSolution @relation(fields: [shared_solution_id], references: [id], onDelete: Cascade)
  viewer             User?          @relation(fields: [viewer_id], references: [id])

  @@index([shared_solution_id])
  @@index([viewer_id])
}

model UserAchievement {
  id             String                   @id @default(uuid())
  user_id        String
  achievement_id String
  unlocked_at    DateTime                 @default(now())
  is_seen        Boolean                  @default(false)
  notification   AchievementNotification?
  achievement    Achievement              @relation(fields: [achievement_id], references: [id], onDelete: Cascade)
  user           User                     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, achievement_id])
  @@index([user_id])
  @@index([achievement_id])
  @@index([is_seen])
}

model AchievementNotification {
  id                  String          @id @default(uuid())
  user_id             String
  user_achievement_id String          @unique
  message             String
  is_read             Boolean         @default(false)
  created_at          DateTime        @default(now())
  user_achievement    UserAchievement @relation(fields: [user_achievement_id], references: [id], onDelete: Cascade)
  user                User            @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([is_read])
}

model SubmissionMetrics {
  id                  String              @id @default(uuid())
  submission_id       String              @unique
  memory_peak_kb      Int?
  cpu_time_ms         Int?
  io_time_ms          Int?
  compilation_time_ms Int?
  execution_details   Json?
  percentile_runtime  Float?
  percentile_memory   Float?
  optimization_score  Int?
  created_at          DateTime            @default(now())
  updated_at          DateTime            @updatedAt
  submission          ChallengeSubmission @relation(fields: [submission_id], references: [id], onDelete: Cascade)

  @@index([submission_id])
}

model OptimizationSuggestion {
  id              String              @id @default(uuid())
  submission_id   String
  suggestion_type String
  suggestion      String
  code_snippet    String?
  line_start      Int?
  line_end        Int?
  priority        Int                 @default(1)
  created_at      DateTime            @default(now())
  submission      ChallengeSubmission @relation(fields: [submission_id], references: [id], onDelete: Cascade)

  @@index([submission_id])
  @@index([suggestion_type])
}

model LanguageMetrics {
  id               String    @id @default(uuid())
  challenge_id     String
  language         String
  avg_runtime_ms   Float
  avg_memory_kb    Float
  min_runtime_ms   Float
  min_memory_kb    Float
  submission_count Int
  created_at       DateTime  @default(now())
  updated_at       DateTime  @updatedAt
  challenge        Challenge @relation(fields: [challenge_id], references: [id], onDelete: Cascade)

  @@unique([challenge_id, language])
  @@index([challenge_id])
  @@index([language])
}

model HelpArticle {
  id           String   @id @default(uuid())
  title        String
  content      String
  category     String
  tags         String[]
  views        Int      @default(0)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  is_published Boolean  @default(false)

  @@index([category])
  @@index([is_published])
}

model ApiKey {
  id         String    @id @default(uuid())
  key        String    @unique
  scope      String[]
  created_at DateTime  @default(now())
  expires_at DateTime?
  last_used  DateTime?
  user_id    String
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id, last_used])
  @@index([expires_at])
}

model Progress {
  id         String         @id @default(uuid())
  created_at DateTime       @default(now())
  roadmap_id String
  topic_id   String
  updated_at DateTime       @updatedAt
  user_id    String
  status     ProgressStatus @default(PENDING)
  roadmap    Roadmap        @relation(fields: [roadmap_id], references: [id], onDelete: Cascade)
  topic      Topic          @relation(fields: [topic_id], references: [id], onDelete: Cascade)

  @@unique([user_id, topic_id])
  @@index([user_id, status])
  @@index([roadmap_id, status])
}

model ApprovalHistory {
  id          String         @id @default(uuid())
  comments    String?
  content_id  String
  created_at  DateTime       @default(now())
  reviewer_id String
  status      ApprovalStatus
  reviewer    User           @relation(fields: [reviewer_id], references: [id])

  @@index([content_id])
  @@index([reviewer_id])
}

model TestCase {
  id           String    @id @default(uuid())
  input        String
  output       String
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  challenge_id String
  is_hidden    Boolean   @default(false)
  order_index  Int       @default(0)
  challenge    Challenge @relation(fields: [challenge_id], references: [id], onDelete: Cascade)

  @@index([challenge_id])
  @@index([order_index])
}

model ChallengeExample {
  id           String    @id @default(uuid())
  challenge_id String
  input        String
  output       String
  explanation  String?
  order_index  Int
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  challenge    Challenge @relation(fields: [challenge_id], references: [id], onDelete: Cascade)

  @@index([challenge_id])
  @@index([order_index])
}

model ChallengeBoilerplate {
  id               String    @id @default(uuid())
  challenge_id     String
  language         String
  boilerplate_code String
  created_at       DateTime  @default(now())
  updated_at       DateTime  @updatedAt
  challenge        Challenge @relation(fields: [challenge_id], references: [id], onDelete: Cascade)

  @@unique([challenge_id, language])
  @@index([challenge_id])
  @@index([language])
}

model BookmarkCollection {
  id          String                  @id @default(uuid())
  name        String
  description String?
  user_id     String
  created_at  DateTime                @default(now())
  updated_at  DateTime                @updatedAt
  user        User                    @relation(fields: [user_id], references: [id], onDelete: Cascade)
  bookmarks   UserChallengeBookmark[]

  @@unique([user_id, name])
  @@index([user_id])
}

model UserChallengeBookmark {
  id            String              @id @default(uuid())
  user_id       String
  challenge_id  String
  created_at    DateTime            @default(now())
  collection_id String?
  updated_at    DateTime            @updatedAt
  challenge     Challenge           @relation(fields: [challenge_id], references: [id], onDelete: Cascade)
  collection    BookmarkCollection? @relation(fields: [collection_id], references: [id])
  user          User                @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, challenge_id])
  @@index([user_id])
  @@index([challenge_id])
  @@index([collection_id])
}

model EmailLog {
  id         String   @id @default(uuid())
  recipient  String
  subject    String
  status     String
  created_at DateTime @default(now())
  sent_at    DateTime

  @@index([recipient])
  @@index([status])
}

model Version {
  id         String   @id @default(uuid())
  title      String
  content    String
  version    Int
  article_id String
  created_at DateTime @default(now())
  article    Article  @relation(fields: [article_id], references: [id])

  @@index([article_id])
}

model Follow {
  id           String   @id @default(uuid())
  created_at   DateTime @default(now())
  follower_id  String
  following_id String
  follower     User     @relation("FollowerRelation", fields: [follower_id], references: [id])
  following    User     @relation("FollowingRelation", fields: [following_id], references: [id])

  @@unique([follower_id, following_id])
}

model UserLearningPreference {
  id                String   @id @default(uuid())
  interests         String[]
  learning_style    String[]
  skill_level       String
  time_availability Int
  user_id           String   @unique
  user              User     @relation("UserLearningPreferences", fields: [user_id], references: [id])
}

model CodingSandbox {
  id         String   @id @default(uuid())
  language   String
  code       String
  output     String?
  created_at DateTime @default(now())
  is_public  Boolean  @default(false)
  user_id    String
  user       User     @relation(fields: [user_id], references: [id])

  @@index([user_id, language])
}

model VirtualLab {
  id          String   @id @default(uuid())
  title       String
  description String
  simulations Json
  created_at  DateTime @default(now())
  embed_url   String
  subject_id  String
  subject     Subject  @relation(fields: [subject_id], references: [id])
}

model Internship {
  id          String                  @id @default(uuid())
  title       String
  company     String
  description String
  location    String?
  duration    String
  stipend     Decimal?                @db.Decimal(10, 2)
  deadline    DateTime?
  created_at  DateTime                @default(now())
  applicants  InternshipApplication[]
}

model InternshipApplication {
  id            String            @id @default(uuid())
  status        ApplicationStatus @default(pending)
  created_at    DateTime          @default(now())
  internship_id String
  resume_url    String
  user_id       String
  internship    Internship        @relation(fields: [internship_id], references: [id])
  user          User              @relation(fields: [user_id], references: [id])
}

model TechEvent {
  id            String              @id @default(uuid())
  title         String
  description   String
  type          String
  venue         String
  created_at    DateTime            @default(now())
  end_time      DateTime
  max_attendees Int?
  start_time    DateTime
  registrations EventRegistration[]
}

model EventRegistration {
  id       String    @id @default(uuid())
  status   String    @default("registered")
  event_id String
  user_id  String
  event    TechEvent @relation(fields: [event_id], references: [id])
  user     User      @relation(fields: [user_id], references: [id])
}

model LearningRecommendation {
  id                  String   @id @default(uuid())
  type                String
  score               Decimal  @db.Decimal(3, 2)
  generated_at        DateTime @default(now())
  recommended_item_id String
  user_id             String
  user                User     @relation(fields: [user_id], references: [id])
}

model HardwareKit {
  id          String   @id @default(uuid())
  title       String
  description String
  price       Decimal  @db.Decimal(10, 2)
  components  Json
  created_at  DateTime @default(now())
  image_urls  String[]
  seller_id   String
  seller      User     @relation(fields: [seller_id], references: [id])
}

model CodeReview {
  id          String           @id @default(uuid())
  code        String
  language    String
  feedback    String
  status      CodeReviewStatus @default(pending)
  created_at  DateTime         @default(now())
  author_id   String
  reviewer_id String
  author      User             @relation("CodeAuthor", fields: [author_id], references: [id])
  reviewer    User             @relation("CodeReviewer", fields: [reviewer_id], references: [id])
}

model ProjectCollaboration {
  id           String    @id @default(uuid())
  role         String
  contribution String
  created_at   DateTime  @default(now())
  end_date     DateTime?
  project_id   String
  start_date   DateTime
  user_id      String
  project      Project   @relation(fields: [project_id], references: [id])
  user         User      @relation(fields: [user_id], references: [id])
}

model DailyChallenge {
  id           String                     @id @default(uuid())
  title        String
  description  String
  difficulty   Difficulty
  created_at   DateTime                   @default(now())
  end_time     DateTime
  start_time   DateTime
  streak_bonus Int                        @default(1)
  submissions  DailyChallengeSubmission[]
}

model DailyChallengeSubmission {
  id           String         @id @default(uuid())
  score        Int
  challenge_id String
  user_id      String
  challenge    DailyChallenge @relation(fields: [challenge_id], references: [id])
  user         User           @relation(fields: [user_id], references: [id])

  @@unique([user_id, challenge_id])
}

model InterviewPrep {
  id         String   @id @default(uuid())
  company    String
  position   String
  progress   Json
  resources  String[]
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  user_id    String
  user       User     @relation(fields: [user_id], references: [id])
}

model SkillAssessment {
  id              String   @id @default(uuid())
  skill           String
  score           Int
  created_at      DateTime @default(now())
  assessment_date DateTime
  user_id         String
  user            User     @relation(fields: [user_id], references: [id])

  @@index([skill, score])
}

model ContentPlaylist {
  id          String   @id @default(uuid())
  title       String
  description String
  items       Json
  likes       Int      @default(0)
  created_at  DateTime @default(now())
  curator_id  String
  curator     User     @relation(fields: [curator_id], references: [id])
}

model ResourceVersion {
  id          String   @id @default(uuid())
  content     String
  version     Int
  changelog   String
  created_at  DateTime @default(now())
  resource_id String
  resource    Resource @relation(fields: [resource_id], references: [id])

  @@index([resource_id])
}

model Subscription {
  id         String   @id @default(uuid())
  tier       String
  features   String[]
  created_at DateTime @default(now())
  end_date   DateTime
  start_date DateTime
  user_id    String
  user       User     @relation(fields: [user_id], references: [id])
}

model InAppPurchase {
  id         String   @id @default(uuid())
  amount     Decimal  @db.Decimal(10, 2)
  created_at DateTime @default(now())
  item_id    String
  item_type  String
  user_id    String
  user       User     @relation(fields: [user_id], references: [id])
}

model UserSettings {
  id            String   @id @default(uuid())
  language      String   @default("en")
  theme         String   @default("light")
  created_at    DateTime @default(now())
  high_contrast Boolean  @default(false)
  text_size     Int      @default(16)
  user_id       String   @unique
  user          User     @relation(fields: [user_id], references: [id])
}

model TranslatedContent {
  id          String   @id @default(uuid())
  language    String
  content     String
  created_at  DateTime @default(now())
  original_id String

  @@unique([original_id, language])
}

model UserLearningAnalytics {
  id           String   @id @default(uuid())
  strengths    String[]
  weaknesses   String[]
  created_at   DateTime @default(now())
  user_id      String   @unique
  weekly_hours Json
  user         User     @relation(fields: [user_id], references: [id])
}

model AdminDashboard {
  id           String   @id @default(uuid())
  metrics      Json
  generated_at DateTime @default(now())
}

model IoTDevice {
  id           String       @id @default(uuid())
  identifier   String
  created_at   DateTime     @default(now())
  device_type  String
  last_seen    DateTime
  user_id      String
  user         User         @relation(fields: [user_id], references: [id])
  lab_sessions LabSession[]
}

model LabSession {
  id          String    @id @default(uuid())
  created_at  DateTime  @default(now())
  device_id   String
  end_time    DateTime
  sensor_data Json
  start_time  DateTime
  user_id     String
  device      IoTDevice @relation(fields: [device_id], references: [id])
  user        User      @relation(fields: [user_id], references: [id])
}

model CommunityChallenge {
  id          String                         @id @default(uuid())
  title       String
  description String
  reward      String
  created_at  DateTime                       @default(now())
  end_date    DateTime
  start_date  DateTime
  submissions CommunityChallengeSubmission[]
}

model ChallengeDiscussion {
  id            String                   @id @default(uuid())
  challenge_id  String
  user_id       String
  parent_id     String?
  content       String
  upvotes       Int                      @default(0)
  downvotes     Int                      @default(0)
  created_at    DateTime                 @default(now())
  updated_at    DateTime                 @updatedAt
  code_language String?
  code_snippet  String?
  flag_reason   String?
  is_flagged    Boolean                  @default(false)
  is_hidden     Boolean                  @default(false)
  challenge     Challenge                @relation(fields: [challenge_id], references: [id], onDelete: Cascade)
  parent        ChallengeDiscussion?     @relation("DiscussionReplies", fields: [parent_id], references: [id], onDelete: Cascade)
  replies       ChallengeDiscussion[]    @relation("DiscussionReplies")
  user          User                     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  flags         DiscussionFlag[]
  notifications DiscussionNotification[]
  user_votes    DiscussionVote[]

  @@index([challenge_id])
  @@index([user_id])
  @@index([parent_id])
}

model AutomatedModeration {
  id           String   @id @default(uuid())
  decision     String
  confidence   Decimal  @db.Decimal(3, 2)
  created_at   DateTime @default(now())
  content_id   String
  content_type String
}

model CommunityChallengeSubmission {
  id           String             @id @default(uuid())
  content      String
  status       String             @default("pending")
  created_at   DateTime           @default(now())
  challenge_id String
  user_id      String
  challenge    CommunityChallenge @relation(fields: [challenge_id], references: [id])
  user         User               @relation(fields: [user_id], references: [id])

  @@index([challenge_id])
  @@index([user_id])
}

model ActivityLog {
  id          String     @id @default(uuid())
  activity    String
  metadata    Json?
  timestamp   DateTime   @default(now())
  device_type DeviceType @default(WEB)
  user_id     String
  user        User       @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([timestamp])
}

model Category {
  id          String     @id @default(uuid())
  name        String     @unique
  description String?
  parent_id   String?
  parent      Category?  @relation("CategoryHierarchy", fields: [parent_id], references: [id])
  children    Category[] @relation("CategoryHierarchy")
}

model MentorshipSession {
  id            String     @id @default(uuid())
  date          DateTime
  duration      Int
  notes         String?
  objectives    Json?
  created_at    DateTime   @default(now())
  updated_at    DateTime   @updatedAt
  mentorship_id String
  mentorship    Mentorship @relation(fields: [mentorship_id], references: [id])
}

model ErrorLog {
  id        String   @id @default(uuid())
  message   String
  service   String
  timestamp DateTime @default(now())
}

model UserSession {
  id         String    @id @default(uuid())
  device     String
  created_at DateTime  @default(now())
  end_time   DateTime?
  start_time DateTime
  updated_at DateTime  @updatedAt
  user_id    String
  user       User      @relation(fields: [user_id], references: [id])

  @@index([user_id])
}

model Option {
  id          String   @id @default(uuid())
  text        String
  created_at  DateTime @default(now())
  is_correct  Boolean
  question_id String
  updated_at  DateTime @updatedAt
  question    Question @relation(fields: [question_id], references: [id])
}

model PlacementTest {
  id         String          @id @default(uuid())
  status     PlacementStatus @default(PENDING)
  results    Json?
  created_at DateTime        @default(now())
  subject_id String
  updated_at DateTime        @updatedAt
  user_id    String
  subject    Subject         @relation(fields: [subject_id], references: [id])

  @@index([user_id])
  @@index([subject_id])
}

model PlacementBook {
  id          String   @id @default(uuid())
  title       String
  description String
  level       String
  created_at  DateTime @default(now())
  file_path   String
  subject_id  String
  updated_at  DateTime @updatedAt

  @@index([subject_id])
  @@index([level])
}

model LeaderboardEntry {
  id         String   @id @default(uuid())
  score      Int
  created_at DateTime @default(now())
  subject_id String
  time_taken Int
  updated_at DateTime @updatedAt
  user_id    String
  user       User     @relation(fields: [user_id], references: [id])

  @@index([subject_id])
  @@index([score])
  @@index([time_taken])
}

model AuditLog {
  id        String   @id @default(uuid())
  action    String
  timestamp DateTime @default(now())
  details   Json
  user_id   String
  user      User     @relation(fields: [user_id], references: [id])

  @@index([user_id])
  @@index([timestamp])
}

model Content {
  id          String              @id @default(uuid())
  title       String
  body        String
  status      ContentStatus       @default(PENDING_REVIEW)
  created_at  DateTime            @default(now())
  updated_at  DateTime            @updatedAt
  author_id   String
  author      User                @relation(fields: [author_id], references: [id])
  moderations ContentModeration[]

  @@index([author_id])
  @@index([status])
}

model ContentModeration {
  id           String    @id @default(uuid())
  notes        String?
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  article_id   String?
  content_id   String?
  resource_id  String?
  topic_id     String?
  action       String
  content_type String
  moderator_id String
  article      Article?  @relation(fields: [article_id], references: [id])
  content      Content?  @relation(fields: [content_id], references: [id])
  moderator    User      @relation(fields: [moderator_id], references: [id])
  resource     Resource? @relation(fields: [resource_id], references: [id])
  topic        Topic?    @relation(fields: [topic_id], references: [id])

  @@index([content_id])
  @@index([article_id])
  @@index([resource_id])
  @@index([moderator_id])
}

model UserStreak {
  id                 String              @id @default(uuid())
  user_id            String              @unique
  current_streak     Int                 @default(0)
  longest_streak     Int                 @default(0)
  last_activity_date DateTime?
  streak_start_date  DateTime?
  timezone           String              @default("UTC")
  daily_activities   UserDailyActivity[]
  user               User                @relation(fields: [user_id], references: [id], map: "user_streaks_user_id_fkey")

  @@map("user_streaks")
}

model Activity {
  id            String   @id @default(uuid())
  user_id       String
  type          String
  description   String
  timestamp     DateTime @default(now())
  roadmap_id    String?
  roadmap_title String?
  user          User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([timestamp])
  @@map("activities")
}

model UserDailyActivity {
  id            String       @id @default(uuid())
  user_id       String
  activity_type ActivityType
  minutes_spent Int
  created_at    DateTime     @default(now())
  user_streak   UserStreak   @relation(fields: [user_id], references: [user_id], map: "user_daily_activities_user_streak_id_fkey")
  user          User         @relation(fields: [user_id], references: [id], map: "user_daily_activities_user_id_fkey")

  @@map("user_daily_activities")
}

model UserFilterPreset {
  id         String           @id @default(uuid())
  user_id    String
  name       String
  filters    Json
  type       FilterPresetType
  created_at DateTime         @default(now())
  updated_at DateTime         @updatedAt
  user       User             @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, name])
  @@index([user_id])
  @@index([type])
}

model RoadmapChallenge {
  id           String    @id @default(uuid())
  roadmap_id   String
  challenge_id String
  order        Int       @default(0)
  is_required  Boolean   @default(true)
  created_at   DateTime  @default(now())
  updated_at   DateTime  @updatedAt
  challenge    Challenge @relation(fields: [challenge_id], references: [id], onDelete: Cascade)
  roadmap      Roadmap   @relation(fields: [roadmap_id], references: [id], onDelete: Cascade)

  @@unique([roadmap_id, challenge_id])
  @@index([roadmap_id])
  @@index([challenge_id])
}

model DiscussionVote {
  id            String              @id @default(uuid())
  discussion_id String
  user_id       String
  vote_type     VoteType
  created_at    DateTime            @default(now())
  updated_at    DateTime            @updatedAt
  discussion    ChallengeDiscussion @relation(fields: [discussion_id], references: [id], onDelete: Cascade)
  user          User                @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([discussion_id, user_id])
  @@index([discussion_id])
  @@index([user_id])
}

model DiscussionFlag {
  id            String              @id @default(uuid())
  discussion_id String
  user_id       String
  reason        FlagReason
  details       String?
  status        ModerationStatus    @default(PENDING)
  created_at    DateTime            @default(now())
  updated_at    DateTime            @updatedAt
  discussion    ChallengeDiscussion @relation(fields: [discussion_id], references: [id], onDelete: Cascade)
  user          User                @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([discussion_id])
  @@index([user_id])
  @@index([status])
}

model DiscussionNotification {
  id            String              @id @default(uuid())
  user_id       String
  discussion_id String
  message       String
  is_read       Boolean             @default(false)
  created_at    DateTime            @default(now())
  discussion    ChallengeDiscussion @relation(fields: [discussion_id], references: [id], onDelete: Cascade)
  user          User                @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([discussion_id])
  @@index([is_read])
}

model ChallengeHint {
  id            String         @id @default(uuid())
  challenge_id  String
  content       String
  difficulty    HintDifficulty
  order         Int            @default(0)
  point_penalty Int            @default(5)
  created_at    DateTime       @default(now())
  updated_at    DateTime       @updatedAt
  challenge     Challenge      @relation(fields: [challenge_id], references: [id], onDelete: Cascade)
  user_hints    UserHint[]

  @@index([challenge_id])
  @@index([difficulty])
}

model UserHint {
  id              String        @id @default(uuid())
  user_id         String
  hint_id         String
  challenge_id    String
  unlocked_at     DateTime      @default(now())
  points_deducted Int           @default(0)
  challenge       Challenge     @relation(fields: [challenge_id], references: [id], onDelete: Cascade)
  hint            ChallengeHint @relation(fields: [hint_id], references: [id], onDelete: Cascade)
  user            User          @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, hint_id])
  @@index([user_id])
  @@index([hint_id])
  @@index([challenge_id])
}

enum BattleType {
  INSTANT
  SCHEDULED
  TOURNAMENT
  PRACTICE
}

enum BattleStatus {
  UPCOMING
  IN_PROGRESS
  COMPLETED
  CANCELLED
  ARCHIVED
}

enum AchievementCategory {
  CHALLENGE
  STREAK
  SOCIAL
  ROADMAP
  BATTLE
  PROFILE
  SPECIAL
  DAILY_TOPIC
}

enum AchievementTriggerType {
  CHALLENGE_COMPLETION
  CHALLENGE_COUNT
  STREAK_DAYS
  DISCUSSION_COUNT
  SOLUTION_VIEWS
  PROFILE_COMPLETION
  BATTLE_WINS
  ROADMAP_COMPLETION
  MANUAL
}

enum Status {
  DRAFT
  PENDING
  APPROVED
  REJECTED
}

enum ArticleStatus {
  DRAFT
  PENDING
  APPROVED
  REJECTED
}

enum ProgressStatus {
  PENDING
  COMPLETED
  IN_PROGRESS
  SKIPPED
}

enum ApprovalStatus {
  APPROVED
  REJECTED
  PENDING
  NEEDS_REVISION
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}

enum Length {
  short
  medium
  long
}

enum CourseLevel {
  Beginner
  Intermediate
  Advanced
}

enum JobType {
  full_time
  part_time
  contract
  internship
}

enum ProjectStatus {
  planning
  ongoing
  completed
  archived
}

enum Visibility {
  public
  private
  unlisted
}

enum ProjectRole {
  owner
  admin
  member
}

enum SubmissionStatus {
  pending
  running
  accepted
  wrong_answer
  time_limit_exceeded
  memory_limit_exceeded
  runtime_error
  compilation_error
}

enum NotificationType {
  system
  achievement
  mention
  comment
  follow
  project
  challenge
  course
  mentorship
  discussion
}

enum MentorshipStatus {
  pending
  active
  completed
  cancelled
}

enum ExperienceLevel {
  beginner
  intermediate
  advanced
  expert
}

enum GroupRole {
  admin
  moderator
  member
}

enum ChallengeCategory {
  algorithms
  data_structures
  system_design
  databases
  web_development
}

enum QuizType {
  multiple_choice
  coding
  theory
}

enum TicketStatus {
  open
  in_progress
  resolved
  closed
}

enum TicketPriority {
  low
  medium
  high
  urgent
}

enum ReportStatus {
  pending
  investigating
  resolved
  rejected
}

enum RequestStatus {
  pending
  approved
  rejected
}

enum Severity {
  low
  medium
  high
  critical
}

enum Priority {
  low
  medium
  high
  critical
}

enum RuleCategory {
  SCORING
  TIMING
  PARTICIPATION
  QUESTION
  MODERATION
  OTHER
}

enum ApplicationStatus {
  pending
  reviewed
  accepted
  rejected
}

enum CodeReviewStatus {
  pending
  in_review
  completed
}

enum DeviceType {
  WEB
  MOBILE
  TABLET
  DESKTOP
  UNKNOWN
}

enum ChallengeStatus {
  DRAFT
  PENDING
  ACTIVE
  ARCHIVED
  INACTIVE
}

enum PlacementStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  EVALUATED
}

enum ContentStatus {
  DRAFT
  PENDING_REVIEW
  APPROVED
  REJECTED
  ARCHIVED
}

enum ActivityType {
  TOPIC_COMPLETION
  QUIZ_COMPLETION
  CODE_CHALLENGE
  RESOURCE_STUDY
  PRACTICE_SESSION
}

enum FilterPresetType {
  CHALLENGE
  ROADMAP
  RESOURCE
  ARTICLE
}

enum VoteType {
  UPVOTE
  DOWNVOTE
}

enum FlagReason {
  SPAM
  INAPPROPRIATE
  OFFENSIVE
  INCORRECT
  OTHER
}

enum ModerationStatus {
  PENDING
  APPROVED
  REJECTED
}

enum HintDifficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}
