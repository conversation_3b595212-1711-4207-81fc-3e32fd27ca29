{"error":{"clientVersion":"6.5.0","code":"P2024","meta":{"connection_limit":17,"modelName":"User","timeout":10},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Authentication failed","timestamp":"2025-04-06T15:21:28.488Z"}
{"level":"error","message":"Unexpected Error Authentication failed","path":"GET /api/v1/roadmaps/2302c7f4-3dcf-498a-96dc-c8265ad254dd/comments","stack":"AppError: Authentication failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:45:10)","status":500,"timestamp":"2025-04-06T15:21:28.490Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-06T15:21:28.491Z"}
{"error":{"clientVersion":"6.5.0","code":"P2024","meta":{"connection_limit":17,"modelName":"User","timeout":10},"name":"PrismaClientKnownRequestError"},"level":"error","message":"Authentication failed","timestamp":"2025-04-06T15:21:37.698Z"}
{"level":"error","message":"Unexpected Error Authentication failed","path":"GET /api/v1/roadmaps/2302c7f4-3dcf-498a-96dc-c8265ad254dd/comments","stack":"AppError: Authentication failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:45:10)","status":500,"timestamp":"2025-04-06T15:21:37.702Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-06T15:21:37.703Z"}
{"level":"error","message":"Unexpected Error \nInvalid `prisma.like.create()` invocation in\n/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/controllers/roadMapControllers.ts:354:23\n\n  351   return sendResponse(res, 'ROADMAP_UNLIKED', { data: null });\n  352 }\n  353 \n→ 354 await prisma.like.create(\nUnique constraint failed on the fields: (`user_id`,`roadmap_id`)","path":"POST /api/v1/roadmaps/2302c7f4-3dcf-498a-96dc-c8265ad254dd/like","stack":"PrismaClientKnownRequestError: \nInvalid `prisma.like.create()` invocation in\n/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/controllers/roadMapControllers.ts:354:23\n\n  351   return sendResponse(res, 'ROADMAP_UNLIKED', { data: null });\n  352 }\n  353 \n→ 354 await prisma.like.create(\nUnique constraint failed on the fields: (`user_id`,`roadmap_id`)\n    at Wn.handleRequestError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/@prisma/client/runtime/library.js:121:7534)\n    at Wn.handleAndLogRequestError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/@prisma/client/runtime/library.js:121:6858)\n    at Wn.request (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/@prisma/client/runtime/library.js:121:6565)\n    at async l (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/@prisma/client/runtime/library.js:130:10067)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/controllers/roadMapControllers.ts:354:5)","status":500,"timestamp":"2025-04-07T03:26:06.409Z"}
{"level":"error","name":"PrismaClientKnownRequestError","statusCode":500,"timestamp":"2025-04-07T03:26:06.412Z"}
{"level":"error","message":"Unexpected Error Authorization token required","path":"GET /api/v1/users/me","stack":"AppError: Authorization token required\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:20:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/index.js:175:3)","status":500,"timestamp":"2025-04-07T16:17:01.268Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-07T16:17:01.271Z"}
{"level":"error","message":"Unexpected Error Invalid authentication token","path":"GET /api/v1/roadMaps/categories","stack":"AppError: Invalid authentication token\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:30:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","status":500,"timestamp":"2025-04-07T16:51:49.235Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-07T16:51:49.236Z"}
{"level":"error","message":"Unexpected Error Invalid authentication token","path":"GET /api/v1/roadmaps?limit=6&type=featured","stack":"AppError: Invalid authentication token\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:30:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runNextTicks (node:internal/process/task_queues:64:3)\n    at process.processImmediate (node:internal/timers:449:9)","status":500,"timestamp":"2025-04-07T16:51:49.239Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-07T16:51:49.239Z"}
{"level":"error","message":"Unexpected Error Invalid authentication token","path":"GET /api/v1/roadmaps?limit=6&type=trending","stack":"AppError: Invalid authentication token\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:30:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","status":500,"timestamp":"2025-04-07T16:51:49.240Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-07T16:51:49.240Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?limit=6&type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:30:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-07T17:21:33.721Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-07T17:21:33.722Z"}
{"level":"error","message":"Unexpected Error Maximum call stack size exceeded","path":"GET /api/v1/roadmaps?limit=6&type=featured","stack":"RangeError: Maximum call stack size exceeded\n    at JSON.stringify (<anonymous>)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:42:33)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)","status":500,"timestamp":"2025-04-07T17:21:36.067Z"}
{"level":"error","name":"RangeError","statusCode":500,"timestamp":"2025-04-07T17:21:36.067Z"}
{"level":"error","message":"Unexpected Error Maximum call stack size exceeded","path":"GET /api/v1/roadmaps","stack":"RangeError: Maximum call stack size exceeded\n    at JSON.stringify (<anonymous>)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:42:33)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)","status":500,"timestamp":"2025-04-07T17:22:22.434Z"}
{"level":"error","name":"RangeError","statusCode":500,"timestamp":"2025-04-07T17:22:22.435Z"}
{"level":"error","message":"Unexpected Error Maximum call stack size exceeded","path":"GET /api/v1/roadmaps","stack":"RangeError: Maximum call stack size exceeded\n    at JSON.stringify (<anonymous>)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:42:33)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)","status":500,"timestamp":"2025-04-07T17:24:02.840Z"}
{"level":"error","name":"RangeError","statusCode":500,"timestamp":"2025-04-07T17:24:02.840Z"}
{"level":"error","message":"Unexpected Error Maximum call stack size exceeded","path":"GET /api/v1/roadmaps","stack":"RangeError: Maximum call stack size exceeded\n    at JSON.stringify (<anonymous>)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:42:33)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)","status":500,"timestamp":"2025-04-07T17:28:29.616Z"}
{"level":"error","name":"RangeError","statusCode":500,"timestamp":"2025-04-07T17:28:29.617Z"}
{"level":"error","message":"Unexpected Error Maximum call stack size exceeded","path":"GET /api/v1/roadmaps","stack":"RangeError: Maximum call stack size exceeded\n    at JSON.stringify (<anonymous>)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:42:33)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)\n    at sendResponse (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/apiResponse.ts:989:36)\n    at ServerResponse.res.json (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/cacheControl.ts:45:16)","status":500,"timestamp":"2025-04-07T17:29:44.932Z"}
{"level":"error","name":"RangeError","statusCode":500,"timestamp":"2025-04-07T17:29:44.933Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-07T17:35:43.184Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-07T17:35:43.185Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-07T17:36:12.542Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-07T17:36:12.544Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-07T17:36:13.351Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-07T17:36:13.351Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-07T17:37:05.649Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-07T17:37:05.650Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-07T17:37:06.094Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-07T17:37:06.095Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-08T15:58:55.285Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-08T15:58:55.285Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-08T15:58:55.676Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-08T15:58:55.677Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-08T16:01:15.646Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-08T16:01:15.647Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-08T16:01:16.044Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-08T16:01:16.045Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-08T16:01:52.400Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-08T16:01:52.401Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-08T16:01:53.356Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-08T16:01:53.357Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-08T16:02:42.469Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-08T16:02:42.470Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-08T16:02:58.738Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-08T16:02:58.738Z"}
{"clientVersion":"6.6.0","errorCode":"P1001","level":"error","message":"Failed to start server: Can't reach database server at `aws-0-ap-south-1.pooler.supabase.com:6543`\n\nPlease make sure your database server is running at `aws-0-ap-south-1.pooler.supabase.com:6543`.","name":"PrismaClientInitializationError","stack":"PrismaClientInitializationError: Can't reach database server at `aws-0-ap-south-1.pooler.supabase.com:6543`\n\nPlease make sure your database server is running at `aws-0-ap-south-1.pooler.supabase.com:6543`.\n    at r (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/@prisma/client/runtime/library.js:112:2565)\n    at App.start (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/index.ts:89:7)","timestamp":"2025-04-10T15:31:26.979Z"}
{"clientVersion":"6.6.0","errorCode":"P1001","level":"error","message":"Failed to start server: Can't reach database server at `aws-0-ap-south-1.pooler.supabase.com:6543`\n\nPlease make sure your database server is running at `aws-0-ap-south-1.pooler.supabase.com:6543`.","name":"PrismaClientInitializationError","stack":"PrismaClientInitializationError: Can't reach database server at `aws-0-ap-south-1.pooler.supabase.com:6543`\n\nPlease make sure your database server is running at `aws-0-ap-south-1.pooler.supabase.com:6543`.\n    at r (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/@prisma/client/runtime/library.js:112:2565)\n    at App.start (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/index.ts:89:7)","timestamp":"2025-04-10T15:32:57.927Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-10T15:36:45.462Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-10T15:36:45.462Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-10T15:36:45.794Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-10T15:36:45.794Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-18T03:16:57.846Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-18T03:16:57.847Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-18T03:16:58.193Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-18T03:16:58.195Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-18T03:16:58.508Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-18T03:16:58.509Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-18T03:16:58.824Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-18T03:16:58.827Z"}
{"level":"error","message":"Unexpected Error Invalid authentication token","path":"GET /api/v1/users/me","stack":"AppError: Invalid authentication token\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:30:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","status":500,"timestamp":"2025-04-19T08:37:17.750Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-19T08:37:17.751Z"}
{"level":"error","message":"Unexpected Error Invalid authentication token","path":"GET /api/v1/users/me","stack":"AppError: Invalid authentication token\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:30:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","status":500,"timestamp":"2025-04-19T08:37:18.626Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-19T08:37:18.626Z"}
{"level":"error","message":"Unexpected Error Invalid authentication token","path":"GET /api/v1/users/me","stack":"AppError: Invalid authentication token\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:30:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","status":500,"timestamp":"2025-04-19T08:37:18.854Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-19T08:37:18.854Z"}
{"level":"error","message":"Unexpected Error Invalid authentication token","path":"GET /api/v1/users/me","stack":"AppError: Invalid authentication token\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:30:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","status":500,"timestamp":"2025-04-19T08:37:19.000Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-19T08:37:19.000Z"}
{"level":"error","message":"Unexpected Error Invalid authentication token","path":"GET /api/v1/users/me","stack":"AppError: Invalid authentication token\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:30:19)","status":500,"timestamp":"2025-04-19T14:30:46.331Z"}
{"level":"error","name":"AppError","statusCode":401,"timestamp":"2025-04-19T14:30:46.332Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T15:52:32.166Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T15:52:32.167Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T15:52:32.565Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T15:52:32.567Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:09:30.809Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:09:30.811Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:09:31.259Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:09:31.260Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:09:37.584Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:09:37.585Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:09:38.056Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:09:38.057Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:09:43.720Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:09:43.722Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=recommended","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:09:44.239Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:09:44.240Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:09:59.073Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:09:59.074Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:09:59.566Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:09:59.567Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:10:00.041Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:10:00.041Z"}
{"level":"error","message":"Unexpected Error Validation failed","path":"GET /api/v1/roadmaps?type=trending","stack":"AppError: Validation failed\n    at createAppError (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/utils/errorHandler.ts:45:17)\n    at <anonymous> (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/validateRequest.ts:22:19)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/Coding/mrengineer/Backend/node_modules/express/lib/router/route.js:149:13)\n    at authMiddleware (/Users/<USER>/Desktop/Coding/mrengineer/Backend/src/middlewares/authMiddleware.ts:42:5)","status":500,"timestamp":"2025-04-19T16:10:00.541Z"}
{"level":"error","name":"AppError","statusCode":400,"timestamp":"2025-04-19T16:10:00.542Z"}
