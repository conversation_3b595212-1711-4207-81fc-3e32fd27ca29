import { ChallengeCategory, Difficulty } from '@prisma/client';

export interface CodingChallenge {
  title: string;
  description: string;
  difficulty: Difficulty;
  category: ChallengeCategory;
  points: number;
  input_format: string;
  output_format: string;
  example_input: string;
  example_output: string;
  constraints: string;
  function_signature: string;
  time_limit?: number;
  memory_limit?: number;
  tags: string[];
  test_cases: {
    input: string;
    output: string;
    is_hidden?: boolean;
  }[];
  solutions?: Record<string, string>;
}

export const codingChallenges: CodingChallenge[] = [
  // Algorithms - Easy
  {
    title: 'Two Sum',
    description:
      'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target. You may assume that each input would have exactly one solution, and you may not use the same element twice.',
    difficulty: Difficulty.EASY,
    category: ChallengeCategory.algorithms,
    points: 10,
    input_format:
      'First line contains an array of integers separated by space. Second line contains the target sum.',
    output_format:
      'Two integers representing the indices of the two numbers that add up to the target.',
    example_input: '[2, 7, 11, 15]\n9',
    example_output: '0 1',
    constraints:
      'The array length is between 2 and 10^4. Each element is between -10^9 and 10^9. There is exactly one solution.',
    function_signature:
      'function twoSum(nums: number[], target: number): number[]',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['array', 'hash table'],
    test_cases: [
      {
        input: '[2, 7, 11, 15]\n9',
        output: '0 1',
      },
      {
        input: '[3, 2, 4]\n6',
        output: '1 2',
      },
      {
        input: '[3, 3]\n6',
        output: '0 1',
      },
      {
        input: '[-1, -2, -3, -4, -5]\n-8',
        output: '2 4',
        is_hidden: true,
      },
    ],
    solutions: {
      javascript: `function twoSum(nums, target) {
  const map = new Map();
  for (let i = 0; i < nums.length; i++) {
    const complement = target - nums[i];
    if (map.has(complement)) {
      return [map.get(complement), i];
    }
    map.set(nums[i], i);
  }
  return [];
}`,
      python: `def two_sum(nums, target):
    num_map = {}
    for i, num in enumerate(nums):
        complement = target - num
        if complement in num_map:
            return [num_map[complement], i]
        num_map[num] = i
    return []`,
    },
  },

  // Algorithms - Easy
  {
    title: 'Valid Palindrome',
    description:
      'Given a string s, determine if it is a palindrome, considering only alphanumeric characters and ignoring cases.',
    difficulty: Difficulty.EASY,
    category: ChallengeCategory.algorithms,
    points: 10,
    input_format: 'A string s.',
    output_format: 'true if the string is a palindrome, false otherwise.',
    example_input: 'A man, a plan, a canal: Panama',
    example_output: 'true',
    constraints:
      'The string length is between 1 and 2 * 10^5. The string consists only of printable ASCII characters.',
    function_signature: 'function isPalindrome(s: string): boolean',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['string', 'two pointers'],
    test_cases: [
      {
        input: 'A man, a plan, a canal: Panama',
        output: 'true',
      },
      {
        input: 'race a car',
        output: 'false',
      },
      {
        input: ' ',
        output: 'true',
      },
      {
        input: "Madam, I'm Adam.",
        output: 'true',
        is_hidden: true,
      },
    ],
    solutions: {
      javascript: `function isPalindrome(s) {
  const cleaned = s.toLowerCase().replace(/[^a-z0-9]/g, '');
  let left = 0;
  let right = cleaned.length - 1;
  
  while (left < right) {
    if (cleaned[left] !== cleaned[right]) {
      return false;
    }
    left++;
    right--;
  }
  
  return true;
}`,
      python: `def is_palindrome(s):
    cleaned = ''.join(c.lower() for c in s if c.isalnum())
    return cleaned == cleaned[::-1]`,
    },
  },

  // Data Structures - Easy
  {
    title: 'Reverse Linked List',
    description:
      'Given the head of a singly linked list, reverse the list, and return the reversed list.',
    difficulty: Difficulty.EASY,
    category: ChallengeCategory.data_structures,
    points: 10,
    input_format: 'A linked list represented as an array of integers.',
    output_format:
      'The reversed linked list represented as an array of integers.',
    example_input: '[1, 2, 3, 4, 5]',
    example_output: '[5, 4, 3, 2, 1]',
    constraints:
      'The number of nodes in the list is between 0 and 5000. The value of each node is between -5000 and 5000.',
    function_signature: 'function reverseList(head: ListNode): ListNode',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['linked list'],
    test_cases: [
      {
        input: '[1, 2, 3, 4, 5]',
        output: '[5, 4, 3, 2, 1]',
      },
      {
        input: '[1, 2]',
        output: '[2, 1]',
      },
      {
        input: '[]',
        output: '[]',
      },
      {
        input: '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]',
        output: '[10, 9, 8, 7, 6, 5, 4, 3, 2, 1]',
        is_hidden: true,
      },
    ],
    solutions: {
      javascript: `function reverseList(head) {
  let prev = null;
  let current = head;
  
  while (current !== null) {
    const next = current.next;
    current.next = prev;
    prev = current;
    current = next;
  }
  
  return prev;
}`,
      python: `def reverse_list(head):
    prev = None
    current = head
    
    while current:
        next_temp = current.next
        current.next = prev
        prev = current
        current = next_temp
        
    return prev`,
    },
  },

  // Algorithms - Medium
  {
    title: 'Longest Substring Without Repeating Characters',
    description:
      'Given a string s, find the length of the longest substring without repeating characters.',
    difficulty: Difficulty.MEDIUM,
    category: ChallengeCategory.algorithms,
    points: 20,
    input_format: 'A string s.',
    output_format:
      'An integer representing the length of the longest substring without repeating characters.',
    example_input: 'abcabcbb',
    example_output: '3',
    constraints:
      'The string length is between 0 and 5 * 10^4. The string consists of English letters, digits, symbols and spaces.',
    function_signature: 'function lengthOfLongestSubstring(s: string): number',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['string', 'sliding window', 'hash table'],
    test_cases: [
      {
        input: 'abcabcbb',
        output: '3',
      },
      {
        input: 'bbbbb',
        output: '1',
      },
      {
        input: 'pwwkew',
        output: '3',
      },
      {
        input: 'aab',
        output: '2',
      },
      {
        input: 'dvdf',
        output: '3',
        is_hidden: true,
      },
    ],
    solutions: {
      javascript: `function lengthOfLongestSubstring(s) {
  const charMap = new Map();
  let maxLength = 0;
  let start = 0;
  
  for (let end = 0; end < s.length; end++) {
    const currentChar = s[end];
    
    if (charMap.has(currentChar) && charMap.get(currentChar) >= start) {
      start = charMap.get(currentChar) + 1;
    }
    
    charMap.set(currentChar, end);
    maxLength = Math.max(maxLength, end - start + 1);
  }
  
  return maxLength;
}`,
      python: `def length_of_longest_substring(s):
    char_map = {}
    max_length = 0
    start = 0
    
    for end, char in enumerate(s):
        if char in char_map and char_map[char] >= start:
            start = char_map[char] + 1
        
        char_map[char] = end
        max_length = max(max_length, end - start + 1)
    
    return max_length`,
    },
  },

  // Data Structures - Medium
  {
    title: 'Binary Tree Level Order Traversal',
    description:
      "Given the root of a binary tree, return the level order traversal of its nodes' values. (i.e., from left to right, level by level).",
    difficulty: Difficulty.MEDIUM,
    category: ChallengeCategory.data_structures,
    points: 20,
    input_format:
      'A binary tree represented as an array in level order traversal. Null nodes are represented as null.',
    output_format:
      'An array of arrays, where each inner array contains the values of nodes at that level.',
    example_input: '[3, 9, 20, null, null, 15, 7]',
    example_output: '[[3], [9, 20], [15, 7]]',
    constraints:
      'The number of nodes in the tree is between 0 and 2000. The value of each node is between -1000 and 1000.',
    function_signature: 'function levelOrder(root: TreeNode): number[][]',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['tree', 'binary tree', 'breadth-first search'],
    test_cases: [
      {
        input: '[3, 9, 20, null, null, 15, 7]',
        output: '[[3], [9, 20], [15, 7]]',
      },
      {
        input: '[1]',
        output: '[[1]]',
      },
      {
        input: '[]',
        output: '[]',
      },
      {
        input: '[1, 2, 3, 4, 5, 6, 7]',
        output: '[[1], [2, 3], [4, 5, 6, 7]]',
        is_hidden: true,
      },
    ],
    solutions: {
      javascript: `function levelOrder(root) {
  if (!root) return [];
  
  const result = [];
  const queue = [root];
  
  while (queue.length > 0) {
    const levelSize = queue.length;
    const currentLevel = [];
    
    for (let i = 0; i < levelSize; i++) {
      const node = queue.shift();
      currentLevel.push(node.val);
      
      if (node.left) queue.push(node.left);
      if (node.right) queue.push(node.right);
    }
    
    result.push(currentLevel);
  }
  
  return result;
}`,
      python: `def level_order(root):
    if not root:
        return []
    
    result = []
    queue = [root]
    
    while queue:
        level_size = len(queue)
        current_level = []
        
        for _ in range(level_size):
            node = queue.pop(0)
            current_level.append(node.val)
            
            if node.left:
                queue.append(node.left)
            if node.right:
                queue.append(node.right)
        
        result.append(current_level)
    
    return result`,
    },
  },

  // Algorithms - Hard
  {
    title: 'Median of Two Sorted Arrays',
    description:
      'Given two sorted arrays nums1 and nums2 of size m and n respectively, return the median of the two sorted arrays.',
    difficulty: Difficulty.HARD,
    category: ChallengeCategory.algorithms,
    points: 30,
    input_format: 'Two arrays of integers, each sorted in ascending order.',
    output_format:
      'A floating-point number representing the median of the two sorted arrays.',
    example_input: '[1, 3]\n[2]',
    example_output: '2.0',
    constraints:
      'The total number of elements is between 1 and 2000. Each array may be empty. Each element is between -10^6 and 10^6.',
    function_signature:
      'function findMedianSortedArrays(nums1: number[], nums2: number[]): number',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['array', 'binary search', 'divide and conquer'],
    test_cases: [
      {
        input: '[1, 3]\n[2]',
        output: '2.0',
      },
      {
        input: '[1, 2]\n[3, 4]',
        output: '2.5',
      },
      {
        input: '[0, 0]\n[0, 0]',
        output: '0.0',
      },
      {
        input: '[]\n[1]',
        output: '1.0',
      },
      {
        input: '[1, 3, 5, 7, 9]\n[2, 4, 6, 8, 10]',
        output: '5.5',
        is_hidden: true,
      },
    ],
    solutions: {
      javascript: `function findMedianSortedArrays(nums1, nums2) {
  if (nums1.length > nums2.length) {
    [nums1, nums2] = [nums2, nums1];
  }
  
  const x = nums1.length;
  const y = nums2.length;
  let low = 0;
  let high = x;
  
  while (low <= high) {
    const partitionX = Math.floor((low + high) / 2);
    const partitionY = Math.floor((x + y + 1) / 2) - partitionX;
    
    const maxX = partitionX === 0 ? Number.NEGATIVE_INFINITY : nums1[partitionX - 1];
    const maxY = partitionY === 0 ? Number.NEGATIVE_INFINITY : nums2[partitionY - 1];
    
    const minX = partitionX === x ? Number.POSITIVE_INFINITY : nums1[partitionX];
    const minY = partitionY === y ? Number.POSITIVE_INFINITY : nums2[partitionY];
    
    if (maxX <= minY && maxY <= minX) {
      const maxLeft = Math.max(maxX, maxY);
      
      if ((x + y) % 2 === 1) {
        return maxLeft;
      }
      
      const minRight = Math.min(minX, minY);
      return (maxLeft + minRight) / 2;
    } else if (maxX > minY) {
      high = partitionX - 1;
    } else {
      low = partitionX + 1;
    }
  }
}`,
      python: `def find_median_sorted_arrays(nums1, nums2):
    if len(nums1) > len(nums2):
        nums1, nums2 = nums2, nums1
    
    x, y = len(nums1), len(nums2)
    low, high = 0, x
    
    while low <= high:
        partition_x = (low + high) // 2
        partition_y = (x + y + 1) // 2 - partition_x
        
        max_x = float('-inf') if partition_x == 0 else nums1[partition_x - 1]
        max_y = float('-inf') if partition_y == 0 else nums2[partition_y - 1]
        
        min_x = float('inf') if partition_x == x else nums1[partition_x]
        min_y = float('inf') if partition_y == y else nums2[partition_y]
        
        if max_x <= min_y and max_y <= min_x:
            max_left = max(max_x, max_y)
            
            if (x + y) % 2 == 1:
                return max_left
            
            min_right = min(min_x, min_y)
            return (max_left + min_right) / 2
        elif max_x > min_y:
            high = partition_x - 1
        else:
            low = partition_x + 1`,
    },
  },

  // Data Structures - Hard
  {
    title: 'LRU Cache',
    description:
      'Design a data structure that follows the constraints of a Least Recently Used (LRU) cache. Implement the LRUCache class with get and put methods.',
    difficulty: Difficulty.HARD,
    category: ChallengeCategory.data_structures,
    points: 30,
    input_format:
      'First line contains the capacity of the cache. Subsequent lines contain operations: "get key" or "put key value".',
    output_format:
      "For each get operation, return the value or -1 if the key doesn't exist.",
    example_input:
      '2\nput 1 1\nput 2 2\nget 1\nput 3 3\nget 2\nget 3\nput 4 4\nget 1\nget 3\nget 4',
    example_output: '1\n-1\n3\n-1\n3\n4',
    constraints:
      'The capacity is between 1 and 3000. The number of operations is between 1 and 3000. The key and value are between 0 and 10^4.',
    function_signature:
      'class LRUCache { constructor(capacity: number); get(key: number): number; put(key: number, value: number): void; }',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['hash table', 'linked list', 'design'],
    test_cases: [
      {
        input:
          '2\nput 1 1\nput 2 2\nget 1\nput 3 3\nget 2\nget 3\nput 4 4\nget 1\nget 3\nget 4',
        output: '1\n-1\n3\n-1\n3\n4',
      },
      {
        input: '1\nput 1 1\nput 2 2\nget 1\nget 2',
        output: '-1\n2',
      },
      {
        input:
          '3\nput 1 1\nput 2 2\nput 3 3\nput 4 4\nget 4\nget 3\nget 2\nget 1',
        output: '4\n3\n2\n-1',
        is_hidden: true,
      },
    ],
    solutions: {
      javascript: `class LRUCache {
  constructor(capacity) {
    this.capacity = capacity;
    this.cache = new Map();
  }
  
  get(key) {
    if (!this.cache.has(key)) return -1;
    
    const value = this.cache.get(key);
    this.cache.delete(key);
    this.cache.set(key, value);
    return value;
  }
  
  put(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, value);
  }
}`,
      python: `class LRUCache:
    def __init__(self, capacity):
        self.capacity = capacity
        self.cache = {}
        self.order = []
    
    def get(self, key):
        if key not in self.cache:
            return -1
        
        self.order.remove(key)
        self.order.append(key)
        return self.cache[key]
    
    def put(self, key, value):
        if key in self.cache:
            self.order.remove(key)
        elif len(self.cache) >= self.capacity:
            oldest = self.order.pop(0)
            del self.cache[oldest]
        
        self.cache[key] = value
        self.order.append(key)`,
    },
  },

  // Web Development - Medium
  {
    title: 'Implement Debounce Function',
    description:
      'Implement a debounce function that takes a function and a delay as arguments and returns a debounced version of the function.',
    difficulty: Difficulty.MEDIUM,
    category: ChallengeCategory.web_development,
    points: 20,
    input_format: 'A function and a delay in milliseconds.',
    output_format:
      'A debounced version of the function that only executes after the specified delay has passed without the function being called again.',
    example_input: 'function(x) { console.log(x); }, 1000',
    example_output:
      'A function that logs the input only after 1000ms of inactivity.',
    constraints:
      'The delay is between 0 and 1000ms. The function can take any number of arguments.',
    function_signature:
      'function debounce(func: Function, delay: number): Function',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['javascript', 'function', 'debounce'],
    test_cases: [
      {
        input: 'test_debounce()',
        output: 'success',
      },
    ],
    solutions: {
      javascript: `function debounce(func, delay) {
  let timeoutId;
  
  return function(...args) {
    clearTimeout(timeoutId);
    
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}`,
    },
  },

  // System Design - Medium
  {
    title: 'Design a Rate Limiter',
    description:
      'Design a rate limiter that restricts the number of requests a client can make within a time window.',
    difficulty: Difficulty.MEDIUM,
    category: ChallengeCategory.system_design,
    points: 20,
    input_format: 'A series of requests with timestamps and client IDs.',
    output_format:
      'For each request, return true if the request is allowed, or false if it exceeds the rate limit.',
    example_input:
      'RateLimiter(2, 1); // 2 requests per second\nrequestAllowed("client1", 1); // timestamp 1\nrequestAllowed("client1", 1.1); // timestamp 1.1\nrequestAllowed("client1", 1.2); // timestamp 1.2\nrequestAllowed("client1", 2.1); // timestamp 2.1',
    example_output: 'true\ntrue\nfalse\ntrue',
    constraints:
      'The rate limit is between 1 and 1000 requests per time window. The time window is between 1 and 3600 seconds.',
    function_signature:
      'class RateLimiter { constructor(limit: number, timeWindow: number); requestAllowed(clientId: string, timestamp: number): boolean; }',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['system design', 'rate limiting'],
    test_cases: [
      {
        input:
          'RateLimiter(2, 1);\nrequestAllowed("client1", 1);\nrequestAllowed("client1", 1.1);\nrequestAllowed("client1", 1.2);\nrequestAllowed("client1", 2.1);',
        output: 'true\ntrue\nfalse\ntrue',
      },
      {
        input:
          'RateLimiter(3, 60);\nrequestAllowed("client2", 1);\nrequestAllowed("client2", 2);\nrequestAllowed("client2", 3);\nrequestAllowed("client2", 4);\nrequestAllowed("client2", 61);',
        output: 'true\ntrue\ntrue\nfalse\ntrue',
      },
    ],
    solutions: {
      javascript: `class RateLimiter {
  constructor(limit, timeWindow) {
    this.limit = limit;
    this.timeWindow = timeWindow;
    this.clients = new Map();
  }
  
  requestAllowed(clientId, timestamp) {
    if (!this.clients.has(clientId)) {
      this.clients.set(clientId, []);
    }
    
    const requests = this.clients.get(clientId);
    
    // Remove expired requests
    while (requests.length > 0 && requests[0] <= timestamp - this.timeWindow) {
      requests.shift();
    }
    
    if (requests.length < this.limit) {
      requests.push(timestamp);
      return true;
    }
    
    return false;
  }
}`,
      python: `class RateLimiter:
    def __init__(self, limit, time_window):
        self.limit = limit
        self.time_window = time_window
        self.clients = {}
    
    def request_allowed(self, client_id, timestamp):
        if client_id not in self.clients:
            self.clients[client_id] = []
        
        requests = self.clients[client_id]
        
        # Remove expired requests
        while requests and requests[0] <= timestamp - self.time_window:
            requests.pop(0)
        
        if len(requests) < self.limit:
            requests.append(timestamp)
            return True
        
        return False`,
    },
  },

  // Databases - Medium
  {
    title: 'SQL Query Optimization',
    description:
      'Optimize a slow SQL query that retrieves user activity data from multiple tables.',
    difficulty: Difficulty.MEDIUM,
    category: ChallengeCategory.databases,
    points: 20,
    input_format: 'A SQL query and the database schema.',
    output_format: 'An optimized version of the SQL query.',
    example_input: `
SELECT u.username, COUNT(a.id) as activity_count
FROM users u
LEFT JOIN (
  SELECT * FROM activities
  WHERE created_at > '2023-01-01'
) a ON u.id = a.user_id
WHERE u.status = 'active'
GROUP BY u.username
ORDER BY activity_count DESC;

-- Schema:
-- users (id, username, email, status, created_at)
-- activities (id, user_id, type, created_at)
`,
    example_output: `
SELECT u.username, COUNT(a.id) as activity_count
FROM users u
LEFT JOIN activities a ON u.id = a.user_id AND a.created_at > '2023-01-01'
WHERE u.status = 'active'
GROUP BY u.username
ORDER BY activity_count DESC;
`,
    constraints:
      'The query should be compatible with MySQL or PostgreSQL. The optimized query should produce the same results as the original query.',
    function_signature: 'function optimizeQuery(query: string): string',
    time_limit: 1000,
    memory_limit: 128,
    tags: ['sql', 'databases', 'optimization'],
    test_cases: [
      {
        input: `
SELECT u.username, COUNT(a.id) as activity_count
FROM users u
LEFT JOIN (
  SELECT * FROM activities
  WHERE created_at > '2023-01-01'
) a ON u.id = a.user_id
WHERE u.status = 'active'
GROUP BY u.username
ORDER BY activity_count DESC;
`,
        output: `
SELECT u.username, COUNT(a.id) as activity_count
FROM users u
LEFT JOIN activities a ON u.id = a.user_id AND a.created_at > '2023-01-01'
WHERE u.status = 'active'
GROUP BY u.username
ORDER BY activity_count DESC;
`,
      },
    ],
    solutions: {
      sql: `
SELECT u.username, COUNT(a.id) as activity_count
FROM users u
LEFT JOIN activities a ON u.id = a.user_id AND a.created_at > '2023-01-01'
WHERE u.status = 'active'
GROUP BY u.username
ORDER BY activity_count DESC;
`,
    },
  },
];
