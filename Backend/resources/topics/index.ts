export const topics = [
  {
    name: 'Arrays',
    description: 'Collection of elements identified by index or key.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Linked Lists',
    description:
      'Linear collection of data elements pointing to the next node.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Stacks',
    description: 'LIFO (last in, first out) data structure.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Queues',
    description: 'FIFO (first in, first out) data structure.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Trees',
    description: 'Hierarchical data structure with a root value and subtrees.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Binary Trees',
    description:
      'A tree data structure in which each node has at most two children.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Binary Search Trees',
    description:
      'A binary tree with the property that all nodes in the left subtree are less than the root node and all nodes in the right subtree are greater.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'AVL Trees',
    description: 'Self-balancing binary search tree.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Heaps',
    description: 'Complete binary tree used to implement priority queues.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Graphs',
    description: 'Non-linear data structure of nodes connected by edges.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Hash Tables',
    description: 'Data structure that implements an associative array.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Recursion',
    description:
      'Method where the solution depends on solutions to smaller instances.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Sorting Algorithms',
    description: 'Algorithms for arranging data in a particular order.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Searching Algorithms',
    description: 'Algorithms for finding an item from a collection of items.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Dynamic Programming',
    description:
      'Optimization technique for solving problems by breaking them down into simpler subproblems.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Greedy Algorithms',
    description: 'Algorithms that make the optimal choice at each step.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Backtracking',
    description:
      'Algorithmic technique for solving problems recursively by trying to build a solution incrementally.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Divide and Conquer',
    description: 'Algorithm design paradigm based on multi-branched recursion.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Graph Traversal',
    description:
      'Techniques for visiting all the nodes in a graph (e.g., BFS, DFS).',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Minimum Spanning Tree',
    description:
      'A subset of the edges in a weighted graph that connects all the vertices without cycles and with the minimum possible total edge weight.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Shortest Path Algorithms',
    description:
      'Algorithms for finding the shortest paths between nodes in a graph.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Disjoint Set',
    description:
      'Data structure that keeps track of a partition of a set into disjoint subsets.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Bit Manipulation',
    description:
      'Techniques to perform operations on individual bits of a number.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'String Algorithms',
    description:
      'Algorithms for performing operations on strings, such as searching and matching.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Trie',
    description:
      'Tree-like data structure used for storing a dynamic set or associative array where the keys are usually strings.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Suffix Trees',
    description: 'Data structure that represents the suffixes of a string.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Segment Trees',
    description:
      'Data structure for storing information about intervals or segments.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Fenwick Trees',
    description:
      'Data structure that can efficiently update elements and calculate prefix sums in a table of numbers.',
    subject: 'Data Structures and Algorithms',
  },
  {
    name: 'Introduction to Object-Oriented Programming',
    description:
      'Fundamentals and principles of Object-Oriented Programming (OOP).',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Classes and Objects',
    description:
      'Blueprints of objects, representing real-world entities and their properties.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Encapsulation',
    description:
      'The concept of bundling data and methods that operate on that data within one unit.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Inheritance',
    description:
      'Mechanism by which one class can inherit properties and methods from another class.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Polymorphism',
    description:
      'The ability to process objects differently based on their data type or class.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Abstraction',
    description:
      'The concept of hiding the complex implementation details and showing only the essential features.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Interfaces',
    description:
      'Abstract types used to specify a behavior that classes must implement.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Abstract Classes',
    description:
      'Classes that cannot be instantiated and are designed to be subclassed.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Constructor and Destructor',
    description:
      'Special methods for initializing and cleaning up instances of a class.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Method Overloading',
    description:
      'Defining multiple methods in the same class with the same name but different parameters.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Method Overriding',
    description:
      'Providing a new implementation for a method inherited from a superclass.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Access Modifiers',
    description:
      'Keywords that set the accessibility of classes, methods, and other members.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Static and Instance Members',
    description:
      'Difference between static (class-level) and instance (object-level) members.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Composition',
    description:
      'A design principle where a class references one or more objects of other classes in its instance variables.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Aggregation',
    description:
      "A special form of association that represents a 'whole-part' relationship between a whole and its parts.",
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Association',
    description:
      'A relationship between two classes that establishes a connection between their objects.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Delegation',
    description:
      'A design pattern where an object passes on its responsibilities to another helper object.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Design Patterns',
    description:
      'Reusable solutions to common problems in software design, including patterns like Singleton, Factory, Observer, etc.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Exception Handling',
    description:
      'Mechanisms to handle runtime errors and exceptions in an orderly fashion.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'UML (Unified Modeling Language)',
    description:
      'A standardized modeling language for visualizing the design of a system.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'SOLID Principles',
    description:
      'A set of five design principles intended to make software designs more understandable, flexible, and maintainable.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Object Life Cycle',
    description:
      'The stages through which an object passes during its lifetime: creation, use, and destruction.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Object-Oriented Analysis and Design',
    description:
      'The process of analyzing and designing a system by visualizing it as a group of interacting objects.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Memory Management in OOP',
    description:
      'Techniques for efficient allocation and deallocation of memory in OOP languages.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Refactoring',
    description:
      'The process of restructuring existing code without changing its external behavior.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Mixins',
    description:
      'A class that provides methods to other classes without being a parent class.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Multiple Inheritance',
    description:
      'A feature of some OOP languages where a class can inherit from more than one superclass.',
    subject: 'Object-Oriented Programming',
  },
  {
    name: 'Introduction to Databases',
    description: 'Overview of database concepts and the need for databases.',
    subject: 'Database Systems',
  },
  {
    name: 'Database Management Systems (DBMS)',
    description: 'Software that uses databases to store and manage data.',
    subject: 'Database Systems',
  },
  {
    name: 'Relational Databases',
    description:
      'Databases structured to recognize relations among stored items of information.',
    subject: 'Database Systems',
  },
  {
    name: 'SQL (Structured Query Language)',
    description:
      'Standard language for managing and manipulating relational databases.',
    subject: 'Database Systems',
  },
  {
    name: 'NoSQL Databases',
    description:
      'Non-relational databases that provide flexible schemas for unstructured data.',
    subject: 'Database Systems',
  },
  {
    name: 'Data Modeling',
    description:
      'The process of creating a data model for an information system by defining data structures and relationships.',
    subject: 'Database Systems',
  },
  {
    name: 'Entity-Relationship Model (ER Model)',
    description: 'A diagrammatic technique for modeling data relationships.',
    subject: 'Database Systems',
  },
  {
    name: 'Normalization',
    description:
      'The process of organizing data to minimize redundancy and improve data integrity.',
    subject: 'Database Systems',
  },
  {
    name: 'SQL Joins',
    description:
      'Techniques for combining rows from two or more tables based on a related column.',
    subject: 'Database Systems',
  },
  {
    name: 'Indexes',
    description:
      'Database structures that improve the speed of data retrieval.',
    subject: 'Database Systems',
  },
  {
    name: 'Transactions',
    description:
      'A sequence of operations performed as a single logical unit of work.',
    subject: 'Database Systems',
  },
  {
    name: 'ACID Properties',
    description:
      'Set of properties that guarantee database transactions are processed reliably (Atomicity, Consistency, Isolation, Durability).',
    subject: 'Database Systems',
  },
  {
    name: 'Database Security',
    description:
      'Measures used to protect the database from unauthorized access or malicious attacks.',
    subject: 'Database Systems',
  },
  {
    name: 'Backup and Recovery',
    description:
      'Processes for creating copies of data and restoring data in the event of loss.',
    subject: 'Database Systems',
  },
  {
    name: 'Data Warehousing',
    description:
      'Systems used for reporting and data analysis, centralizing large amounts of data.',
    subject: 'Database Systems',
  },
  {
    name: 'Data Mining',
    description:
      'The process of discovering patterns and knowledge from large amounts of data.',
    subject: 'Database Systems',
  },
  {
    name: 'Database Design',
    description:
      'The process of designing the structure of a database to store and manage data effectively.',
    subject: 'Database Systems',
  },
  {
    name: 'Database Schema',
    description:
      'The structure that defines the organization of data within a database.',
    subject: 'Database Systems',
  },
  {
    name: 'Distributed Databases',
    description: 'Databases distributed across different physical locations.',
    subject: 'Database Systems',
  },
  {
    name: 'Data Integrity',
    description: 'The accuracy and consistency of data within a database.',
    subject: 'Database Systems',
  },
  {
    name: 'Data Concurrency',
    description:
      'The ability of the database to allow multiple users to access the data at the same time.',
    subject: 'Database Systems',
  },
  {
    name: 'Data Replication',
    description: 'The process of copying data from one location to another.',
    subject: 'Database Systems',
  },
  {
    name: 'Big Data',
    description:
      'Large and complex data sets that require advanced tools to process and analyze.',
    subject: 'Database Systems',
  },
  {
    name: 'Cloud Databases',
    description: 'Databases that run on cloud computing platforms.',
    subject: 'Database Systems',
  },
  {
    name: 'Graph Databases',
    description: 'Databases that use graph structures for semantic queries.',
    subject: 'Database Systems',
  },
  {
    name: 'In-Memory Databases',
    description: 'Databases that store data in memory to improve performance.',
    subject: 'Database Systems',
  },
  {
    name: 'Object-Oriented Databases',
    description:
      'Databases that integrate object-oriented programming with database technology.',
    subject: 'Database Systems',
  },
  {
    name: 'Database Normal Forms',
    description:
      'Guidelines to reduce redundancy and dependency in a relational database.',
    subject: 'Database Systems',
  },
  {
    name: 'Query Optimization',
    description: 'Techniques to improve the efficiency of query processing.',
    subject: 'Database Systems',
  },
  {
    name: 'Database Migration',
    description: 'The process of moving data from one database to another.',
    subject: 'Database Systems',
  },
  {
    name: 'Data Governance',
    description:
      'The management of data availability, usability, integrity, and security.',
    subject: 'Database Systems',
  },
  {
    name: 'Data Visualization',
    description:
      'The representation of data in graphical format to help understand patterns and insights.',
    subject: 'Database Systems',
  },
  {
    name: 'Database Performance Tuning',
    description: 'Techniques to optimize and improve database performance.',
    subject: 'Database Systems',
  },
  {
    name: 'Introduction',
    description:
      'An overview of the C language, including its definition, history, features, technical aspects, usage, and significance.',
    subject: 'C Language',
  },
  {
    name: 'Installation',
    description:
      'Guidelines on how to install the C compiler and set up the environment.',
    subject: 'C Language',
  },
  {
    name: 'Syntax & Statements',
    description: 'Basic syntax and statement structure in C programming.',
    subject: 'C Language',
  },
  {
    name: 'Output & New Line',
    description: 'How to generate output and use new line characters in C.',
    subject: 'C Language',
  },
  {
    name: 'Variables',
    description:
      'Introduction to variables, including how to create, modify, and use them in C.',
    subject: 'C Language',
  },
  {
    name: 'Create Variables',
    description: 'Instructions on creating variables in C.',
    subject: 'C Language',
  },
  {
    name: 'Format Specifiers',
    description: 'Explanation of format specifiers and their usage in C.',
    subject: 'C Language',
  },
  {
    name: 'Change Values',
    description: 'How to change the values of variables in C.',
    subject: 'C Language',
  },
  {
    name: 'Multiple Variables',
    description: 'Handling multiple variables in a single statement in C.',
    subject: 'C Language',
  },
  {
    name: 'Variable Names',
    description: 'Rules and best practices for naming variables in C.',
    subject: 'C Language',
  },
  {
    name: 'Real-Life Examples',
    description:
      'Practical examples of using variables in real-world scenarios.',
    subject: 'C Language',
  },
  {
    name: 'C Data Types',
    description:
      'Overview of data types in C, including characters, numbers, and more.',
    subject: 'C Language',
  },
  {
    name: 'Data Types',
    description: 'Detailed explanation of various data types in C.',
    subject: 'C Language',
  },
  {
    name: 'Characters',
    description: 'Understanding and using character data types in C.',
    subject: 'C Language',
  },
  {
    name: 'Numbers',
    description: 'Working with numerical data types in C.',
    subject: 'C Language',
  },
  {
    name: 'Decimal Precision',
    description: 'Managing decimal precision with floating-point numbers in C.',
    subject: 'C Language',
  },
  {
    name: 'Memory Size',
    description: 'Memory size considerations for different data types in C.',
    subject: 'C Language',
  },
  {
    name: 'Real-Life Examples',
    description:
      'Practical examples of using data types in real-world scenarios.',
    subject: 'C Language',
  },
  {
    name: 'Type Conversion',
    description: 'Converting between different data types in C.',
    subject: 'C Language',
  },
  {
    name: 'Constants',
    description:
      'Explanation of constants in C, including how to declare and use them.',
    subject: 'C Language',
  },
  {
    name: 'Operators',
    description:
      'Overview of operators in C, including arithmetic, relational, and logical operators.',
    subject: 'C Language',
  },
  {
    name: 'Booleans',
    description: 'Understanding boolean data types and operations in C.',
    subject: 'C Language',
  },
  {
    name: 'Real-Life Examples',
    description:
      'Practical examples of using booleans in real-world scenarios.',
    subject: 'C Language',
  },
  {
    name: 'If Else',
    description: 'Control flow in C using if, else, and else-if statements.',
    subject: 'C Language',
  },
  {
    name: 'If',
    description: 'Detailed explanation of the if statement in C.',
    subject: 'C Language',
  },
  {
    name: 'Else',
    description: 'How to use the else statement in conjunction with if in C.',
    subject: 'C Language',
  },
  {
    name: 'Else If',
    description: 'Explanation of the else-if ladder in C.',
    subject: 'C Language',
  },
  {
    name: 'Short Hand If',
    description: 'Usage of the shorthand if (ternary operator) in C.',
    subject: 'C Language',
  },
  {
    name: 'Real-Life Examples',
    description:
      'Practical examples of using if-else statements in real-world scenarios.',
    subject: 'C Language',
  },
  {
    name: 'Switch',
    description: 'Switch case statements for multi-way branching in C.',
    subject: 'C Language',
  },
  {
    name: 'While Loop',
    description: 'Explanation of while and do/while loops in C.',
    subject: 'C Language',
  },
  {
    name: 'Do/While Loop',
    description: 'How to use the do/while loop in C.',
    subject: 'C Language',
  },
  {
    name: 'Real-Life Examples',
    description: 'Practical examples of using loops in real-world scenarios.',
    subject: 'C Language',
  },
  {
    name: 'For Loop',
    description: 'Explanation of the for loop in C and how to use it.',
    subject: 'C Language',
  },
  {
    name: 'Nested Loops',
    description: 'Understanding and using nested loops in C.',
    subject: 'C Language',
  },
  {
    name: 'Real-Life Examples',
    description:
      'Practical examples of using for loops in real-world scenarios.',
    subject: 'C Language',
  },
  {
    name: 'Break/Continue',
    description: 'Explanation of break and continue statements in C.',
    subject: 'C Language',
  },
  {
    name: 'Arrays',
    description:
      'Introduction to arrays, including how to declare, initialize, and use them in C.',
    subject: 'C Language',
  },
  {
    name: 'Array Size',
    description: 'Understanding and managing array size in C.',
    subject: 'C Language',
  },
  {
    name: 'Real-Life Examples',
    description: 'Practical examples of using arrays in real-world scenarios.',
    subject: 'C Language',
  },
  {
    name: 'Multidimensional Arrays',
    description: 'Working with multidimensional arrays in C.',
    subject: 'C Language',
  },
  {
    name: 'Strings',
    description:
      'Introduction to strings in C, including how to declare, manipulate, and use them.',
    subject: 'C Language',
  },
  {
    name: 'Special Characters',
    description:
      'Explanation of special characters in strings and how to use them.',
    subject: 'C Language',
  },
  {
    name: 'String Functions',
    description: 'Common string functions in C and how to use them.',
    subject: 'C Language',
  },
  {
    name: 'User Input',
    description: 'How to take input from the user in C.',
    subject: 'C Language',
  },
  {
    name: 'Memory Address',
    description: 'Understanding and using memory addresses in C.',
    subject: 'C Language',
  },
  {
    name: 'Pointers',
    description:
      'Introduction to pointers, including how to declare, initialize, and use them in C.',
    subject: 'C Language',
  },
  {
    name: 'Pointers and Arrays',
    description: 'Relationship between pointers and arrays in C.',
    subject: 'C Language',
  },
  {
    name: 'Functions',
    description:
      'Introduction to functions, including how to declare, define, and call them in C.',
    subject: 'C Language',
  },
  {
    name: 'Functions Parameters',
    description: 'Understanding function parameters and how to use them in C.',
    subject: 'C Language',
  },
  {
    name: 'Scope',
    description:
      'Explanation of scope in C, including local and global variables.',
    subject: 'C Language',
  },
  {
    name: 'Function Declaration',
    description: 'How to declare functions in C.',
    subject: 'C Language',
  },
  {
    name: 'Recursion',
    description: 'Explanation of recursion in C and how to use it.',
    subject: 'C Language',
  },
  {
    name: 'Math Functions',
    description: 'Common math functions in C and how to use them.',
    subject: 'C Language',
  },
  {
    name: 'Files',
    description:
      'Working with files in C, including how to create, write, and read files.',
    subject: 'C Language',
  },
  {
    name: 'Create Files',
    description: 'How to create files in C.',
    subject: 'C Language',
  },
  {
    name: 'Write to Files',
    description: 'How to write data to files in C.',
    subject: 'C Language',
  },
  {
    name: 'Read Files',
    description: 'How to read data from files in C.',
    subject: 'C Language',
  },
  {
    name: 'Structures',
    description:
      'Introduction to structures in C, including how to declare and use them.',
    subject: 'C Language',
  },
  {
    name: 'Enums',
    description: 'Understanding enums in C and how to use them.',
    subject: 'C Language',
  },
  {
    name: 'Memory',
    description: 'Memory management in C, including dynamic memory allocation.',
    subject: 'C Language',
  },
  {
    name: 'References',
    description:
      'Additional resources and references for further reading on C programming.',
    subject: 'C Language',
  },
  {
    name: 'Examples',
    description:
      'Collection of examples to demonstrate C programming concepts.',
    subject: 'C Language',
  },
  {
    name: 'Exercises',
    description:
      'Practice exercises to reinforce understanding of C programming concepts.',
    subject: 'C Language',
  },
  {
    name: 'Quiz',
    description: 'Quizzes to test knowledge of C programming concepts.',
    subject: 'C Language',
  },
  {
    name: 'Compiler',
    description:
      'Information on compilers and how to use them with C programming.',
    subject: 'C Language',
  },
  {
    name: 'Introduction to Python',
    description:
      'Installing Python, writing and running your first Python program, understanding the Python interpreter and IDEs.',
    subject: 'Python',
  },
  {
    name: 'Python Variables and Data Types',
    description:
      'Introduction to variables, constants, and primitive data types like integers, floats, strings, and booleans.',
    subject: 'Python',
  },
  {
    name: 'Python Number and Casting',
    description:
      'Basic arithmetic operations and type conversion (casting) between different data types.',
    subject: 'Python',
  },
  {
    name: 'Python Strings',
    description:
      'String operations, methods, string formatting, and f-strings.',
    subject: 'Python',
  },
  {
    name: 'Python Booleans',
    description:
      'Understanding true/false logic and boolean operations in Python.',
    subject: 'Python',
  },
  {
    name: 'Python Operators',
    description:
      'Working with arithmetic, comparison, logical, assignment, identity, membership, and bitwise operators.',
    subject: 'Python',
  },
  {
    name: 'Python Input and Output',
    description:
      'Reading user input and printing output using Python’s input() and print() functions.',
    subject: 'Python',
  },
  {
    name: 'Python Lists',
    description:
      'List operations, slicing, and list comprehensions for working with lists in Python.',
    subject: 'Python',
  },
  {
    name: 'Python Tuples',
    description:
      'Introduction to tuples, their immutability, and how to work with them.',
    subject: 'Python',
  },
  {
    name: 'Python Sets',
    description:
      'Set operations, methods, and understanding the uniqueness property of set elements.',
    subject: 'Python',
  },
  {
    name: 'Python Dictionaries',
    description:
      'Working with dictionaries (key-value pairs), common methods, and dictionary comprehensions.',
    subject: 'Python',
  },
  {
    name: 'Python Conditional Statements',
    description:
      'Using if, else, and elif for decision-making in Python programs.',
    subject: 'Python',
  },
  {
    name: 'Python Loops',
    description:
      'Introduction to while and for loops, along with nested loops and loop control statements like break, continue, and pass.',
    subject: 'Python',
  },
  {
    name: 'Python Functions',
    description:
      'Defining and calling functions, function arguments, return values, and lambda functions.',
    subject: 'Python',
  },
  {
    name: 'Python Recursion',
    description:
      'Understanding recursion, base cases, and using recursive functions to solve problems.',
    subject: 'Python',
  },
  {
    name: 'Python Error and Exception Handling',
    description:
      'Using try, except, and finally blocks for handling exceptions and raising custom exceptions.',
    subject: 'Python',
  },
  {
    name: 'Python File Handling',
    description:
      "Reading and writing files using Python's file handling methods, including context managers.",
    subject: 'Python',
  },
  {
    name: 'Python Modules',
    description:
      'Importing, using, and creating Python modules for better code organization.',
    subject: 'Python',
  },
  {
    name: 'Python Packages',
    description:
      'Understanding packages and how to organize your Python code into modules and packages.',
    subject: 'Python',
  },
  {
    name: 'Python Arrays',
    description:
      'Working with arrays using the list data structure or the array module.',
    subject: 'Python',
  },
  {
    name: 'Python List Comprehensions and Generators',
    description:
      'Using list comprehensions and generator functions to write efficient code.',
    subject: 'Python',
  },
  {
    name: 'Object-Oriented Programming (OOP) in Python',
    description:
      'Introduction to classes, objects, inheritance, polymorphism, encapsulation, and abstraction in Python.',
    subject: 'Python',
  },
  {
    name: 'Python Decorators',
    description:
      'Function decorators, class decorators, and built-in decorators like @staticmethod and @classmethod.',
    subject: 'Python',
  },
  {
    name: 'Python Iterators and Generators',
    description:
      'Working with Python’s iterator protocol and creating generator functions using yield.',
    subject: 'Python',
  },
  {
    name: 'Python Regular Expressions',
    description:
      'Using the re module to match, search, and replace patterns with regular expressions.',
    subject: 'Python',
  },
  {
    name: 'Python Date and Time',
    description:
      'Working with dates and times using the datetime and time modules.',
    subject: 'Python',
  },
  {
    name: 'Python Functional Programming',
    description:
      'Using map, filter, reduce, lambda functions, and higher-order functions for functional programming in Python.',
    subject: 'Python',
  },
  {
    name: 'Python Comprehensions',
    description:
      'Advanced use of list, set, and dictionary comprehensions, with conditional logic.',
    subject: 'Python',
  },
  {
    name: 'Python Exception Handling (Advanced)',
    description:
      'Handling multiple exceptions, raising exceptions, and understanding the exception hierarchy.',
    subject: 'Python',
  },
  {
    name: 'Python File I/O (Advanced)',
    description:
      'Binary file handling, memory-mapped files, and reading/writing large files.',
    subject: 'Python',
  },
  {
    name: 'Python Unit Testing',
    description:
      'Writing and running test cases using unittest, mocking, and automating test suites.',
    subject: 'Python',
  },
  {
    name: 'Python Logging',
    description:
      'Setting up logging, using different logging levels, and configuring logs for applications.',
    subject: 'Python',
  },
  {
    name: 'Python Multi-threading and Concurrency',
    description:
      'Using threading, concurrent.futures, and asyncio to manage concurrency and parallelism in Python.',
    subject: 'Python',
  },
  {
    name: 'Python Networking',
    description:
      'Working with sockets, HTTP requests, APIs, and web scraping using Python.',
    subject: 'Python',
  },
  {
    name: 'Python Databases',
    description:
      'Connecting to databases (SQLite, MySQL, PostgreSQL) and using the sqlite3 module or SQLAlchemy ORM.',
    subject: 'Python',
  },
  {
    name: 'Python Data Science Libraries',
    description:
      'Introduction to popular data science libraries like NumPy, Pandas, Matplotlib, and SciPy.',
    subject: 'Python',
  },
  {
    name: 'Python Web Development',
    description:
      'Introduction to building web applications using Flask, Django, or FastAPI.',
    subject: 'Python',
  },
  {
    name: 'Python for Automation',
    description:
      'Automating tasks using os, shutil, subprocess, and other built-in Python modules.',
    subject: 'Python',
  },
  {
    name: 'Python for APIs',
    description:
      'Building and consuming REST APIs using Flask, FastAPI, or the requests module.',
    subject: 'Python',
  },
  {
    name: 'Python Best Practices and Code Optimization',
    description:
      'Writing efficient, clean Python code, avoiding common pitfalls, and profiling code for optimization.',
    subject: 'Python',
  },
  {
    name: 'Introduction to C++',
    description:
      'Overview of C++, history, uses, and setting up the development environment.',
    subject: 'C++',
  },
  {
    name: 'C++ Syntax and Structure',
    description:
      'Basic syntax, structure of a C++ program, writing and compiling your first program.',
    subject: 'C++',
  },
  {
    name: 'C++ Variables and Data Types',
    description:
      'Introduction to variables, constants, and primitive data types (int, float, char, double, bool).',
    subject: 'C++',
  },
  {
    name: 'C++ Input and Output',
    description:
      'Using cin for input, cout for output, and basic I/O operations.',
    subject: 'C++',
  },
  {
    name: 'C++ Operators',
    description:
      'Arithmetic, relational, logical, bitwise, assignment, and other operators.',
    subject: 'C++',
  },
  {
    name: 'C++ Control Structures',
    description:
      'Using if, else, switch, and ternary operators for decision-making.',
    subject: 'C++',
  },
  {
    name: 'C++ Loops',
    description:
      'For, while, do-while loops and loop control (break, continue).',
    subject: 'C++',
  },
  {
    name: 'C++ Functions',
    description:
      'Defining and calling functions, function arguments, return values, and function overloading.',
    subject: 'C++',
  },
  {
    name: 'C++ Arrays',
    description:
      'Working with single and multi-dimensional arrays, array manipulation.',
    subject: 'C++',
  },
  {
    name: 'C++ Strings',
    description:
      'String manipulation, C++ string class, common string operations.',
    subject: 'C++',
  },
  {
    name: 'C++ Pointers',
    description:
      'Introduction to pointers, pointer arithmetic, and memory management.',
    subject: 'C++',
  },
  {
    name: 'C++ References',
    description:
      'Working with references, reference variables, and pointer vs reference.',
    subject: 'C++',
  },
  {
    name: 'C++ Dynamic Memory',
    description:
      'Using new and delete for dynamic memory allocation, managing heap memory.',
    subject: 'C++',
  },
  {
    name: 'C++ Structures',
    description: 'Defining and using structures to group data types together.',
    subject: 'C++',
  },
  {
    name: 'C++ Enumerations',
    description: 'Working with enumerations to define user-defined data types.',
    subject: 'C++',
  },
  {
    name: 'C++ Functions (Advanced)',
    description:
      'Inline functions, recursive functions, and default arguments.',
    subject: 'C++',
  },
  {
    name: 'C++ Classes and Objects',
    description:
      'Introduction to OOP, defining classes, creating objects, access specifiers (public, private, protected).',
    subject: 'C++',
  },
  {
    name: 'C++ Constructors and Destructors',
    description:
      'Creating constructors and destructors, constructor overloading.',
    subject: 'C++',
  },
  {
    name: 'C++ Inheritance',
    description:
      'Understanding inheritance, types of inheritance (single, multiple, hierarchical), base and derived classes.',
    subject: 'C++',
  },
  {
    name: 'C++ Polymorphism',
    description:
      'Function and operator overloading, virtual functions, and runtime polymorphism.',
    subject: 'C++',
  },
  {
    name: 'C++ Encapsulation and Abstraction',
    description:
      'Encapsulation using classes and abstraction using abstract classes.',
    subject: 'C++',
  },
  {
    name: 'C++ Friend Functions and Classes',
    description:
      'Using friend functions and friend classes to access private members.',
    subject: 'C++',
  },
  {
    name: 'C++ Static Members',
    description: 'Working with static variables and static functions.',
    subject: 'C++',
  },
  {
    name: 'C++ Namespaces',
    description: 'Understanding and using namespaces to avoid name conflicts.',
    subject: 'C++',
  },
  {
    name: 'C++ Templates',
    description: 'Using function and class templates for generic programming.',
    subject: 'C++',
  },
  {
    name: 'C++ Exception Handling',
    description:
      'Using try, catch, throw to handle exceptions and error conditions.',
    subject: 'C++',
  },
  {
    name: 'C++ File Handling',
    description:
      'Reading from and writing to files, using file streams (ifstream, ofstream).',
    subject: 'C++',
  },
  {
    name: 'C++ Standard Template Library (STL)',
    description:
      'Introduction to STL containers (vector, list, map, set) and algorithms.',
    subject: 'C++',
  },
  {
    name: 'C++ Iterators',
    description:
      'Using iterators with STL containers, iterator types (input, output, forward, bidirectional, random access).',
    subject: 'C++',
  },
  {
    name: 'C++ Lambda Expressions',
    description: 'Introduction to lambda expressions and their usage in C++.',
    subject: 'C++',
  },
  {
    name: 'C++ Smart Pointers',
    description:
      'Using unique_ptr, shared_ptr, and weak_ptr for automatic memory management.',
    subject: 'C++',
  },
  {
    name: 'C++ Multithreading',
    description:
      'Creating and managing threads, synchronization using mutex and condition variables.',
    subject: 'C++',
  },
  {
    name: 'C++ Regular Expressions',
    description: 'Using regex for pattern matching and string manipulation.',
    subject: 'C++',
  },
  {
    name: 'C++ Type Casting',
    description:
      'Understanding static_cast, dynamic_cast, const_cast, and reinterpret_cast.',
    subject: 'C++',
  },
  {
    name: 'C++ Function Pointers',
    description:
      'Using pointers to functions and passing functions as arguments.',
    subject: 'C++',
  },
  {
    name: 'C++ Move Semantics',
    description:
      'Understanding rvalue references, move constructors, and move assignment.',
    subject: 'C++',
  },
  {
    name: 'C++ Memory Management',
    description:
      'Advanced memory management techniques, handling memory leaks, and using smart pointers.',
    subject: 'C++',
  },
  {
    name: 'C++ Best Practices',
    description:
      'Writing clean, efficient, and maintainable code in C++, avoiding common pitfalls.',
    subject: 'C++',
  },
];
