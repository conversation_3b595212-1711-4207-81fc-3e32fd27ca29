/**
 * Available metrics for analytics reports
 */
export const AVAILABLE_METRICS = [
  {
    id: 'total_users',
    name: 'Total Users',
    category: 'Users',
    description: 'Total number of registered users',
  },
  {
    id: 'active_users',
    name: 'Active Users',
    category: 'Users',
    description: 'Number of users active in the selected period',
  },
  {
    id: 'new_users',
    name: 'New Users',
    category: 'Users',
    description: 'Number of new user registrations',
  },
  {
    id: 'retention_rate',
    name: 'Retention Rate',
    category: 'Users',
    description: 'Percentage of users who return after their first visit',
  },
  {
    id: 'total_enrollments',
    name: 'Total Enrollments',
    category: 'Roadmaps',
    description: 'Total number of roadmap enrollments',
  },
  {
    id: 'completion_rate',
    name: 'Completion Rate',
    category: 'Roadmaps',
    description: 'Percentage of users who complete enrolled roadmaps',
  },
  {
    id: 'challenge_attempts',
    name: 'Challenge Attempts',
    category: 'Challenges',
    description: 'Number of challenge attempts',
  },
  {
    id: 'challenge_success_rate',
    name: 'Challenge Success Rate',
    category: 'Challenges',
    description: 'Percentage of successful challenge completions',
  },
  {
    id: 'resource_views',
    name: 'Resource Views',
    category: 'Resources',
    description: 'Number of resource views',
  },
  {
    id: 'resource_completion_rate',
    name: 'Resource Completion Rate',
    category: 'Resources',
    description: 'Percentage of resources marked as completed',
  },
  {
    id: 'avg_session_duration',
    name: 'Avg. Session Duration',
    category: 'Engagement',
    description: 'Average time users spend per session',
  },
  {
    id: 'avg_pages_per_session',
    name: 'Avg. Pages Per Session',
    category: 'Engagement',
    description: 'Average number of pages viewed per session',
  },
];

/**
 * Available dimensions for analytics reports
 */
export const AVAILABLE_DIMENSIONS = [
  {
    id: 'date',
    name: 'Date',
    category: 'Time',
    description: 'Group by date',
  },
  {
    id: 'week',
    name: 'Week',
    category: 'Time',
    description: 'Group by week',
  },
  {
    id: 'month',
    name: 'Month',
    category: 'Time',
    description: 'Group by month',
  },
  {
    id: 'quarter',
    name: 'Quarter',
    category: 'Time',
    description: 'Group by quarter',
  },
  {
    id: 'year',
    name: 'Year',
    category: 'Time',
    description: 'Group by year',
  },
  {
    id: 'user_role',
    name: 'User Role',
    category: 'Users',
    description: 'Group by user role',
  },
  {
    id: 'user_country',
    name: 'User Country',
    category: 'Users',
    description: 'Group by user country',
  },
  {
    id: 'roadmap_name',
    name: 'Roadmap Name',
    category: 'Roadmaps',
    description: 'Group by roadmap',
  },
  {
    id: 'challenge_difficulty',
    name: 'Challenge Difficulty',
    category: 'Challenges',
    description: 'Group by challenge difficulty',
  },
  {
    id: 'resource_type',
    name: 'Resource Type',
    category: 'Resources',
    description: 'Group by resource type',
  },
  {
    id: 'content_category',
    name: 'Content Category',
    category: 'Content',
    description: 'Group by content category',
  },
];

/**
 * Default data retention periods
 */
export const DATA_RETENTION_PERIODS = {
  '30days': 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
  '90days': 90 * 24 * 60 * 60 * 1000, // 90 days
  '180days': 180 * 24 * 60 * 60 * 1000, // 180 days
  '1year': 365 * 24 * 60 * 60 * 1000, // 1 year
  forever: null, // No automatic deletion
};

/**
 * Default report settings
 */
export const DEFAULT_REPORT_SETTINGS = {
  scheduleEnabled: false,
  scheduleFrequency: 'weekly',
  scheduleRecipients: [],
  scheduleExportFormat: 'pdf',
};
