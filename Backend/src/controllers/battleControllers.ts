import { Request, Response } from 'express';

import { BattleStatus, BattleType, Difficulty, Length } from '@prisma/client';

import prisma from '@/lib/prisma';
import { BattleRepository } from '@/repositories/battleRepository';
import socketService from '@/services/socket';
import { sendResponse } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import { catchAsync } from '../utils';

// Import or define a battle socket service
const battleSocketService = {
  initializeBattle: (battleId: string) => {
    logger.info(`Initializing battle socket for battle: ${battleId}`);
    // Implementation would go here
  },
  handleParticipantJoin: (battleId: string, userId: string) => {
    logger.info(`User ${userId} joined battle ${battleId}`);
    // Implementation would go here
  },
  startBattle: async (battleId: string) => {
    logger.info(`Starting battle ${battleId}`);
    // Implementation would go here
  },
  endBattle: async (battleId: string) => {
    logger.info(`Ending battle ${battleId}`);
    // Implementation would go here
  },
  handleScoreUpdate: (battleId: string, userId: string, score: number) => {
    logger.info(
      `Score update for user ${userId} in battle ${battleId}: ${score}`,
    );
    // Implementation would go here
  },
};

export default class BattleController {
  private readonly battleRepo: BattleRepository;

  constructor() {
    this.battleRepo = new BattleRepository();
  }

  /**
   * Get battle zone statistics
   * @route GET /api/battles/statistics
   * @access Public
   */
  public getBattleZoneStatistics = catchAsync(
    async (req: Request, res: Response) => {
      const { user_id, refresh } = req.query;

      // Get user ID from query parameter or authenticated user
      const userId = (user_id as string) || (req.user?.id as string);

      // Clear cache if refresh is requested
      if (refresh === 'true') {
        await this.battleRepo.clearBattleZoneStatisticsCache(userId);
      }

      const statistics = await this.battleRepo.getBattleZoneStatistics(userId);

      return sendResponse(res, 'BATTLE_ZONE_STATISTICS_FETCHED', {
        data: statistics,
      });
    },
  );

  /**
   * Get all battles with pagination, filtering, and sorting
   */
  public getBattles = catchAsync(async (req: Request, res: Response) => {
    const {
      page,
      limit,
      search,
      status,
      difficulty,
      type,
      length,
      topic_id,
      user_id,
      sort_by,
      sort_order,
    } = req.query;

    const result = await this.battleRepo.getBattles({
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      search: search as string,
      status: status as BattleStatus,
      difficulty: difficulty as Difficulty,
      type: type as BattleType,
      length: length as Length,
      topic_id: topic_id as string,
      user_id: user_id as string,
      sort_by: sort_by as string,
      sort_order: sort_order as 'asc' | 'desc',
    });

    sendResponse(res, 'BATTLES_FETCHED', {
      data: result.data,
      meta: result.meta,
    });
  });

  /**
   * Get a single battle by ID with detailed information
   */
  public getBattle = catchAsync(async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const battle = await this.battleRepo.getBattleDetails(id);
      sendResponse(res, 'BATTLE_FETCHED', { data: battle });
    } catch (error) {
      logger.error('Error fetching battle: ', error);
      throw createAppError('Battle not found', 404);
    }
  });

  /**
   * Create a new battle
   */
  public createBattle = catchAsync(async (req: Request, res: Response) => {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      throw createAppError('User not authenticated', 401);
    }

    const {
      title,
      description,
      topic_id,
      difficulty,
      length,
      type,
      max_participants,
      start_time,
      end_time,
      points_per_question,
      time_per_question,
      total_questions,
      rule_ids,
    } = req.body;

    // Additional validation checks if needed
    if (new Date(start_time) >= new Date(end_time)) {
      throw createAppError('End time must be after start time', 400);
    }

    try {
      // Start a transaction to ensure both battle and questions are created together
      const result = await prisma.$transaction(async (tx) => {
        // Create the battle
        const battle = await this.battleRepo.create({
          data: {
            title,
            description,
            topic_id,
            difficulty,
            length,
            type,
            max_participants: max_participants || 10,
            start_time: new Date(start_time),
            end_time: new Date(end_time),
            points_per_question: points_per_question || 10,
            time_per_question: time_per_question || 30,
            total_questions: total_questions || 10,
            user_id: req.user.id,
            status: 'UPCOMING', // Default status
          },
          include: {
            topic: true,
            user: {
              select: {
                id: true,
                username: true,
                avatar_url: true,
              },
            },
          },
        });

        // Select questions for the battle based on topic and difficulty
        logger.info(`Selecting questions for battle ${battle.id}`);
        const questionCount = total_questions || 10;

        try {
          // Select questions for the battle
          const selectedQuestions =
            await this.battleRepo.selectQuestionsForBattle(
              topic_id,
              difficulty,
              questionCount,
            );

          // Associate questions with the battle
          if (selectedQuestions.length > 0) {
            await this.battleRepo.associateQuestionsWithBattle(
              battle.id,
              selectedQuestions,
            );
            logger.info(
              `Associated ${selectedQuestions.length} questions with battle ${battle.id}`,
            );
          } else {
            logger.warn(
              `No questions found for battle ${battle.id} with topic ${topic_id} and difficulty ${difficulty}`,
            );
          }
        } catch (error) {
          logger.error(
            `Error selecting questions for battle: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          // Continue with battle creation even if question selection fails
          // We'll handle this case in the frontend
        }

        // Associate battle rules if provided
        if (rule_ids && Array.isArray(rule_ids) && rule_ids.length > 0) {
          try {
            // Create battle rule associations using direct Prisma commands
            const ruleAssociations = rule_ids.map((ruleId) => ({
              battle_id: battle.id,
              rule_id: ruleId,
            }));

            // Use the transaction to create the associations
            await Promise.all(
              ruleAssociations.map((association) =>
                tx.battleToRule
                  .create({
                    data: association,
                  })
                  .catch((e) => {
                    // Skip duplicates silently
                    if (!e.message.includes('Unique constraint')) {
                      throw e;
                    }
                  }),
              ),
            );

            logger.info(
              `Associated ${rule_ids.length} rules with battle ${battle.id}`,
            );
          } catch (error) {
            logger.error(
              `Error associating rules with battle: ${error instanceof Error ? error.message : 'Unknown error'}`,
            );
            // Continue with battle creation even if rule association fails
          }
        } else {
          // If no rules provided, try to get default rules
          try {
            // Since we're having issues with the Prisma model access, we'll use a separate query
            // outside the transaction to get default rule IDs
            const defaultRulesQuery = await prisma.$queryRaw`
              SELECT id FROM "BattleRule" WHERE is_default = true
            `;

            // Convert the query result to an array of rule objects
            const defaultRules = Array.isArray(defaultRulesQuery)
              ? defaultRulesQuery.map((rule: any) => ({ id: rule.id }))
              : [];

            if (defaultRules.length > 0) {
              // Create associations for default rules
              for (const rule of defaultRules) {
                try {
                  // Create each association individually within the transaction
                  await tx.battleToRule.create({
                    data: {
                      battle_id: battle.id,
                      rule_id: rule.id,
                    },
                  });
                } catch (e: any) {
                  // Skip duplicates silently
                  if (!e.message.includes('Unique constraint')) {
                    throw e;
                  }
                }
              }

              logger.info(
                `Associated ${defaultRules.length} default rules with battle ${battle.id}`,
              );
            }
          } catch (error) {
            logger.error(
              `Error associating default rules with battle: ${error instanceof Error ? error.message : 'Unknown error'}`,
            );
          }
        }

        return battle;
      });

      // Initialize battle real-time features
      battleSocketService.initializeBattle(result.id);
      logger.info(`Battle created: ${result.id} by user: ${req.user.id}`);

      // Return the created battle
      sendResponse(res, 'BATTLE_CREATED', { data: result });
    } catch (error) {
      logger.error(
        `Error creating battle: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw createAppError('Failed to create battle', 500);
    }
  });

  /**
   * Select questions for a battle
   * @route POST /api/battles/questions/select
   * @access Private
   */
  public selectQuestionsForBattle = catchAsync(
    async (req: Request, res: Response) => {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        throw createAppError('User not authenticated', 401);
      }

      const { topic_id, difficulty, count, battle_id } = req.body;

      // Validate required fields
      if (!topic_id) {
        throw createAppError('Topic ID is required', 400);
      }

      if (!difficulty) {
        throw createAppError('Difficulty is required', 400);
      }

      if (!count || count <= 0) {
        throw createAppError('Valid question count is required', 400);
      }

      try {
        // Select questions for the battle based on topic and difficulty
        const selectedQuestions =
          await this.battleRepo.selectQuestionsForBattle(
            topic_id,
            difficulty,
            count,
          );

        // Check if we have enough questions
        if (selectedQuestions.length === 0) {
          throw createAppError(
            'No questions available for the selected criteria',
            404,
          );
        }

        if (selectedQuestions.length < count) {
          logger.warn(
            `Not enough questions found. Requested: ${count}, Found: ${selectedQuestions.length}`,
          );
        }

        // If battle_id is provided, associate questions with the battle
        if (battle_id) {
          // Check if the battle exists and belongs to the user
          const battle = await this.battleRepo.findUnique({
            where: { id: battle_id },
          });

          if (!battle) {
            throw createAppError('Battle not found', 404);
          }

          if (battle.user_id !== req.user.id) {
            throw createAppError(
              'You do not have permission to modify this battle',
              403,
            );
          }

          // Associate questions with the battle
          await this.battleRepo.associateQuestionsWithBattle(
            battle_id,
            selectedQuestions,
          );

          logger.info(
            `Associated ${selectedQuestions.length} questions with battle ${battle_id}`,
          );
        }

        // Return the selected questions (without correct answers for security)
        const sanitizedQuestions = selectedQuestions.map((q) => ({
          id: q.id,
          question: q.question,
          options: q.options
            ? q.options.map((o) => ({
                id: o.id,
                text: o.answer_text || o.text,
                // Don't include is_correct flag for security
              }))
            : [],
        }));

        sendResponse(res, 'QUESTIONS_FETCHED', {
          data: sanitizedQuestions,
          meta: {
            requested: count,
            found: selectedQuestions.length,
            topic_id,
            difficulty,
          },
        });
      } catch (error) {
        logger.error(
          `Error selecting questions: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        if (error instanceof Error && error.message.includes('not found')) {
          throw createAppError(error.message, 404);
        }
        throw createAppError('Failed to select questions', 500);
      }
    },
  );

  /**
   * Regenerate questions for a battle
   * @route POST /api/battles/:id/questions/regenerate
   * @access Private
   */
  public regenerateBattleQuestions = catchAsync(
    async (req: Request, res: Response) => {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        throw createAppError('User not authenticated', 401);
      }

      const { id } = req.params;
      const { count } = req.body;

      try {
        // Check if the battle exists and belongs to the user
        const battle = await this.battleRepo.findUnique({
          where: { id },
        });

        if (!battle) {
          throw createAppError('Battle not found', 404);
        }

        if (battle.user_id !== req.user.id) {
          throw createAppError(
            'You do not have permission to modify this battle',
            403,
          );
        }

        // Check if the battle is in a state where questions can be regenerated
        if (battle.status !== BattleStatus.UPCOMING) {
          throw createAppError(
            `Cannot regenerate questions for a battle that is ${battle.status.toLowerCase()}`,
            400,
          );
        }

        // Determine how many questions to select
        const questionCount = count || battle.total_questions || 10;

        // Select new questions for the battle
        const selectedQuestions =
          await this.battleRepo.selectQuestionsForBattle(
            battle.topic_id,
            battle.difficulty,
            questionCount,
          );

        // Check if we have enough questions
        if (selectedQuestions.length === 0) {
          throw createAppError(
            'No questions available for the battle criteria',
            404,
          );
        }

        // Associate new questions with the battle (this will replace existing questions)
        const battleQuestions =
          await this.battleRepo.associateQuestionsWithBattle(
            id,
            selectedQuestions,
          );

        logger.info(
          `Regenerated ${battleQuestions.length} questions for battle ${id}`,
        );

        // Return success response with the number of questions regenerated
        sendResponse(res, 'BATTLE_UPDATED', {
          data: {
            battle_id: id,
            questions_count: battleQuestions.length,
          },
        });
      } catch (error) {
        logger.error(
          `Error regenerating battle questions: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        if (error instanceof Error && error.message.includes('not found')) {
          throw createAppError(error.message, 404);
        }
        throw createAppError('Failed to regenerate battle questions', 500);
      }
    },
  );

  /**
   * Update an existing battle
   */
  public updateBattle = catchAsync(async (req: Request, res: Response) => {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      throw createAppError('User not authenticated', 401);
    }

    const { id } = req.params;
    const updatedBattle = await this.battleRepo.updateBattle(
      id,
      req.body,
      req.user.id,
    );

    logger.info(`Battle updated: ${id} by user: ${req.user.id}`);
    sendResponse(res, 'BATTLE_UPDATED', { data: updatedBattle });
  });

  /**
   * Delete a battle or mark it as cancelled
   */
  public deleteBattle = catchAsync(async (req: Request, res: Response) => {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      throw createAppError('User not authenticated', 401);
    }

    const { id } = req.params;
    await this.battleRepo.deleteBattle(id, req.user.id);

    logger.info(`Battle deleted/cancelled: ${id} by user: ${req.user.id}`);
    sendResponse(res, 'BATTLE_DELETED');
  });

  /**
   * Join a battle
   */
  public joinBattle = catchAsync(async (req: Request, res: Response) => {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      throw createAppError('User not authenticated', 401);
    }

    const { id } = req.params;
    const result = await this.battleRepo.joinBattle(id, req.user.id);

    // Notify all participants about the new participant via WebSocket
    battleSocketService.handleParticipantJoin(id, req.user.id);

    // Join the WebSocket room for this battle
    socketService.updateBattleState(id, {
      battle_id: id,
      status: result.battle.status,
      current_participants: result.battle.current_participants,
    });

    logger.info(`User ${req.user.id} joined battle ${id}`);
    sendResponse(res, 'BATTLE_JOINED', { data: result });
  });

  /**
   * Update battle status with proper validation
   */
  public updateBattleStatus = catchAsync(
    async (req: Request, res: Response) => {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        throw createAppError('User not authenticated', 401);
      }

      const { id } = req.params;
      const { status } = req.body;

      // Validate status
      if (!status || !Object.values(BattleStatus).includes(status)) {
        throw createAppError('Invalid battle status', 400);
      }

      // Get current battle status
      const battle = await this.battleRepo.findUnique({
        where: { id },
        select: {
          status: true,
          user_id: true,
          current_participants: true,
          max_participants: true,
          start_time: true,
          end_time: true,
        },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Check if user is the creator
      if (battle.user_id !== req.user.id) {
        throw createAppError(
          'Only the battle creator can update the status',
          403,
        );
      }

      // Validate status transitions
      const currentStatus = battle.status;
      const newStatus = status as BattleStatus;

      // Define valid status transitions
      const validTransitions: Record<BattleStatus, BattleStatus[]> = {
        UPCOMING: ['IN_PROGRESS', 'CANCELLED'],
        IN_PROGRESS: ['COMPLETED', 'CANCELLED'],
        COMPLETED: [], // No transitions allowed from completed
        CANCELLED: [], // No transitions allowed from cancelled
        ARCHIVED: [], // No transitions allowed from archived
      };

      // Check if the transition is valid
      if (!validTransitions[currentStatus].includes(newStatus)) {
        throw createAppError(
          `Cannot transition battle from ${currentStatus} to ${newStatus}`,
          400,
        );
      }

      // Additional validation for specific transitions
      if (newStatus === 'IN_PROGRESS') {
        // Check if battle has enough participants
        if (battle.current_participants < 2) {
          throw createAppError(
            'Battle needs at least 2 participants to start',
            400,
          );
        }

        // Check if start time has been reached
        if (battle.start_time && new Date(battle.start_time) > new Date()) {
          throw createAppError(
            'Battle cannot start before its scheduled start time',
            400,
          );
        }
      }

      // Update the battle status
      const updatedBattle = await this.battleRepo.updateBattle(
        id,
        { status: newStatus },
        req.user.id,
      );

      // Handle specific status changes
      if (newStatus === 'IN_PROGRESS') {
        // Start the battle in the socket service
        await battleSocketService.startBattle(id);
      } else if (newStatus === 'COMPLETED') {
        // End the battle in the socket service
        await battleSocketService.endBattle(id);
      } else if (newStatus === 'CANCELLED') {
        // Notify participants about cancellation
        socketService.updateBattleState(id, {
          battle_id: id,
          status: 'CANCELLED',
          current_participants: battle.current_participants,
        });
      }

      logger.info(
        `Battle ${id} status updated from ${currentStatus} to ${newStatus} by user ${req.user.id}`,
      );
      sendResponse(res, 'BATTLE_STATUS_UPDATED', { data: updatedBattle });
    },
  );

  /**
   * Get battle questions
   */
  public getBattleQuestions = catchAsync(
    async (req: Request, res: Response) => {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        throw createAppError('User not authenticated', 401);
      }

      const { id } = req.params;
      const questions = await this.battleRepo.getBattleQuestions(
        id,
        req.user.id,
      );

      sendResponse(res, 'QUESTIONS_FETCHED', { data: questions });
    },
  );

  /**
   * Submit an answer to a battle question
   */
  public submitAnswer = catchAsync(async (req: Request, res: Response) => {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      throw createAppError('User not authenticated', 401);
    }

    const { battle_id, question_id, answer, time_taken } = req.body;

    const result = await this.battleRepo.submitAnswer(
      battle_id,
      question_id,
      req.user.id,
      answer,
      time_taken,
    );

    // Update score in real-time via WebSocket
    battleSocketService.handleScoreUpdate(
      battle_id,
      req.user.id,
      result.participant.score,
    );

    logger.info(
      `User ${req.user.id} submitted answer for question ${question_id} in battle ${battle_id}`,
    );
    sendResponse(res, 'ANSWER_SUBMITTED', { data: result });
  });

  /**
   * Get battle leaderboard
   */
  public getBattleLeaderboard = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;
      const { limit, page } = req.query;

      const leaderboard = await this.battleRepo.getBattleLeaderboard(
        id,
        limit ? parseInt(limit as string) : undefined,
        page ? parseInt(page as string) : undefined,
      );

      sendResponse(res, 'LEADERBOARD_FETCHED', {
        data: leaderboard.data,
        meta: leaderboard.meta,
      });
    },
  );

  /**
   * Reschedule a battle
   */
  public rescheduleBattle = catchAsync(async (req: Request, res: Response) => {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      throw createAppError('User not authenticated', 401);
    }

    const { id } = req.params;
    const { start_time, end_time } = req.body;

    // Convert string dates to Date objects
    const startTime = new Date(start_time);
    const endTime = new Date(end_time);

    // Validate dates
    if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
      throw createAppError('Invalid date format', 400);
    }

    const updatedBattle = await this.battleRepo.rescheduleBattle(
      id,
      req.user.id,
      startTime,
      endTime,
    );

    logger.info(`Battle ${id} rescheduled by user ${req.user.id}`);
    sendResponse(res, 'BATTLE_UPDATED', { data: updatedBattle });
  });

  /**
   * Archive a battle
   */
  public archiveBattle = catchAsync(async (req: Request, res: Response) => {
    // Check if user is authenticated
    if (!req.user || !req.user.id) {
      throw createAppError('User not authenticated', 401);
    }

    const { id } = req.params;
    const archivedBattle = await this.battleRepo.archiveBattle(id, req.user.id);

    logger.info(`Battle ${id} archived by user ${req.user.id}`);
    sendResponse(res, 'BATTLE_ARCHIVED', { data: archivedBattle });
  });

  /**
   * Update progress for a battle challenge
   */
  public updateBattleProgress = catchAsync(
    async (req: Request, res: Response) => {
      // Check if user is authenticated
      if (!req.user || !req.user.id) {
        throw createAppError('User not authenticated', 401);
      }

      const { id: battleId } = req.params;
      const { question_id, is_completed, time_taken } = req.body;

      // Update the progress
      const result = await this.battleRepo.updateBattleProgress(
        battleId,
        req.user.id,
        question_id,
        is_completed,
        time_taken,
      );

      // Notify other participants about the score update
      battleSocketService.handleScoreUpdate(
        battleId,
        req.user.id,
        result.participant.score,
      );

      // If this is the last question and all participants have completed it,
      // check if the battle should be marked as completed
      const battle = await this.battleRepo.getBattleDetails(battleId);
      const allQuestions = await this.battleRepo.getBattleQuestions(
        battleId,
        req.user.id,
      );

      if (allQuestions.length > 0) {
        const lastQuestion = allQuestions[allQuestions.length - 1];

        if (question_id === lastQuestion.id) {
          const participants = await prisma.battleParticipant.findMany({
            where: { battle_id: battleId },
          });

          // Check if each participant has answered each question
          const allCompleted = participants.every((participant) => {
            return allQuestions.every(async (question) => {
              const answers =
                await this.battleRepo.getAllBattleAnswers(battleId);
              return answers.some(
                (answer) =>
                  answer.user_id === participant.user_id &&
                  answer.question_id === question.id,
              );
            });
          });

          if (allCompleted) {
            // Mark the battle as completed
            await this.battleRepo.updateBattleStatus(battleId, 'COMPLETED');

            // Notify all participants that the battle is completed
            await battleSocketService.endBattle(battleId);
          }
        }
      }

      logger.info(
        `User ${req.user.id} updated progress for question ${question_id} in battle ${battleId}`,
      );
      sendResponse(res, 'PROGRESS_UPDATED', { data: result });
    },
  );
}
