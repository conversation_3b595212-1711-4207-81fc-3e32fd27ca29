import { Request, Response } from 'express';

import CollegeRepository from '@/repositories/collegeRepository';

import { catchAsync, parse } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class CollegeController {
  private readonly collegeRepo: CollegeRepository;

  constructor() {
    this.collegeRepo = new CollegeRepository();
  }

  public getColleges = catchAsync(async (req: Request, res: Response) => {
    const colleges = await this.collegeRepo.findMany();

    sendResponse(res, 'COLLEGES_FETCHED', {
      data: { colleges },
    });
  });
}
