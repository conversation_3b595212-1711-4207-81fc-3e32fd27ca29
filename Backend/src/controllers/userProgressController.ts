import { Request, Response } from 'express';

import { ActivityType } from '@prisma/client';

import UserPointsRepository from '@/repositories/userPointsRepository';
import { catchAsync } from '@/utils';
import { sendResponse } from '@/utils/apiResponse';
import logger from '@/utils/logger';

import { createAppError } from '../utils/errorHandler';
import UserProgressRepository from '../repositories/userProgressRepository';

export default class UserProgressController {
  private readonly userProgressRepo: UserProgressRepository;
  private readonly userPointsRepo: UserPointsRepository;

  constructor() {
    this.userProgressRepo = new UserProgressRepository();
    this.userPointsRepo = new UserPointsRepository();
  }

  /**
   * Get user progress data
   */
  public getProgress = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) throw createAppError('User not found', 404);

    const [progress, achievements, experienceLevel] = await Promise.all([
      this.userProgressRepo.getUserProgress(userId),
      this.userProgressRepo.getAchievements(userId),
      this.userProgressRepo.calculateExperienceLevel(userId),
    ]);

    sendResponse(res, 'PROGRESS_FETCHED', {
      data: { ...progress, achievements, experienceLevel },
    });
  });

  /**
   * Update user progress for a topic
   */
  public updateProgress = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) throw createAppError('User not found', 404);

    const { topicId, status, score, timeSpent } = req.body;

    await this.userProgressRepo.updateUserProgress(userId, {
      topic_id: topicId,
      is_completed: status === 'completed',
      timeSpent: timeSpent || 0,
    });

    if (status === 'completed') {
      await this.userPointsRepo.updateUserPoints(userId, score || 10);

      // Track daily activity for topic completion
      await this.userProgressRepo.trackDailyActivity(
        userId,
        ActivityType.TOPIC_COMPLETION,
        timeSpent || 30, // Default to 30 minutes if not provided
      );
    }

    sendResponse(res, 'PROGRESS_UPDATED');
  });

  /**
   * Get user streak data
   */
  public getStreak = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) throw createAppError('User not found', 404);

    const streakData = await this.userProgressRepo.trackStreak(userId);

    sendResponse(res, 'STREAK_FETCHED', {
      data: streakData,
    });
  });

  /**
   * Track daily activity
   */
  public trackActivity = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) throw createAppError('User not found', 404);

    const { activity_type, minutes_spent } = req.body;

    if (!Object.values(ActivityType).includes(activity_type)) {
      throw createAppError('Invalid activity type', 400);
    }

    await this.userProgressRepo.trackDailyActivity(
      userId,
      activity_type,
      minutes_spent,
    );

    sendResponse(res, 'ACTIVITY_TRACKED');
  });

  /**
   * Get comprehensive user progress statistics
   */
  public getProgressStats = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) throw createAppError('User not found', 404);

    const stats = await this.userProgressRepo.getUserProgressStats(userId);

    sendResponse(res, 'PROGRESS_STATS_FETCHED', {
      data: stats,
    });
  });

  /**
   * Get user activity timeline
   */
  public getActivityTimeline = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;
      if (!userId) throw createAppError('User not found', 404);

      const { days = 30 } = req.query;

      const timeline = await this.userProgressRepo.getUserActivityTimeline(
        userId,
        Number(days),
      );

      sendResponse(res, 'ACTIVITY_TIMELINE_FETCHED', {
        data: timeline,
      });
    },
  );

  /**
   * Get learning path recommendations
   */
  public getLearningPath = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) throw createAppError('User not found', 404);

    const learningPath =
      await this.userProgressRepo.calculateLearningPath(userId);

    sendResponse(res, 'LEARNING_PATH_FETCHED', {
      data: learningPath,
    });
  });
}
