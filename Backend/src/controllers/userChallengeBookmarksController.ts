import { Request, Response } from 'express';

import ChallengeRepository from '../repositories/challengeRepository';
import UserChallengeBookmarksRepository from '../repositories/userChallengeBookmarksRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';
import { createAppError } from '../utils/errorHandler';

export default class UserChallengeBookmarksController {
  private readonly bookmarksRepo: UserChallengeBookmarksRepository;
  private readonly challengeRepo: ChallengeRepository;

  constructor() {
    this.bookmarksRepo = new UserChallengeBookmarksRepository();
    this.challengeRepo = new ChallengeRepository();
  }

  /**
   * Get all bookmarked challenges for a user
   */
  public getBookmarkedChallenges = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('Unauthorized', 401);
      }

      const { collectionId } = req.query;

      const bookmarks = await this.bookmarksRepo.getBookmarkedChallenges(
        userId,
        collectionId as string,
      );

      return sendResponse(res, 'BOOKMARKS_FETCHED', { data: bookmarks });
    },
  );

  /**
   * Toggle bookmark for a challenge
   */
  public toggleBookmark = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { challengeId } = req.params;
    const { collectionId } = req.body;

    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    // Check if challenge exists
    const challenge = await this.challengeRepo.findUnique({
      where: { id: challengeId },
    });

    if (!challenge) {
      throw createAppError('Challenge not found', 404);
    }

    // Toggle the bookmark
    const result = await this.bookmarksRepo.toggleBookmark(
      userId,
      challengeId,
      collectionId,
    );

    return sendResponse(
      res,
      result.bookmarked ? 'CHALLENGE_BOOKMARKED' : 'CHALLENGE_UNBOOKMARKED',
      {
        data: result,
      },
    );
  });

  /**
   * Bookmark a challenge
   */
  public bookmarkChallenge = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { challengeId } = req.params;
    const { collectionId } = req.body;

    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    // Check if challenge exists
    const challenge = await this.challengeRepo.findUnique({
      where: { id: challengeId },
    });

    if (!challenge) {
      throw createAppError('Challenge not found', 404);
    }

    // Check if already bookmarked
    const existingBookmark = await this.bookmarksRepo.isBookmarked(
      userId,
      challengeId,
    );

    if (existingBookmark) {
      throw createAppError('Challenge already bookmarked', 409);
    }

    // Create the bookmark
    const bookmark = await this.bookmarksRepo.createBookmark(
      userId,
      challengeId,
      collectionId,
    );

    return sendResponse(res, 'CHALLENGE_BOOKMARKED', { data: { bookmark } });
  });

  /**
   * Remove a bookmark
   */
  public removeBookmark = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { challengeId } = req.params;

    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    // Check if bookmark exists
    const existingBookmark = await this.bookmarksRepo.isBookmarked(
      userId,
      challengeId,
    );

    if (!existingBookmark) {
      throw createAppError('Bookmark not found', 404);
    }

    // Delete the bookmark
    await this.bookmarksRepo.deleteBookmark(existingBookmark.id, userId);

    return sendResponse(res, 'CHALLENGE_UNBOOKMARKED', { data: null });
  });

  /**
   * Batch create bookmarks
   */
  public batchCreateBookmarks = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('Unauthorized', 401);
      }

      const { challengeIds, collectionId } = req.body;

      if (!Array.isArray(challengeIds) || challengeIds.length === 0) {
        throw createAppError('Challenge IDs are required', 400);
      }

      const result = await this.bookmarksRepo.batchCreateBookmarks(
        userId,
        challengeIds,
        collectionId,
      );

      return sendResponse(res, 'CHALLENGES_BOOKMARKED', { data: result });
    },
  );

  /**
   * Batch delete bookmarks
   */
  public batchDeleteBookmarks = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('Unauthorized', 401);
      }

      const { challengeIds } = req.body;

      if (!Array.isArray(challengeIds) || challengeIds.length === 0) {
        throw createAppError('Challenge IDs are required', 400);
      }

      const result = await this.bookmarksRepo.batchDeleteBookmarks(
        userId,
        challengeIds,
      );

      return sendResponse(res, 'CHALLENGES_UNBOOKMARKED', { data: result });
    },
  );

  /**
   * Update bookmark collection
   */
  public updateBookmarkCollection = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('Unauthorized', 401);
      }

      const { bookmarkId } = req.params;
      const { collectionId } = req.body;

      const bookmark = await this.bookmarksRepo.updateBookmarkCollection(
        bookmarkId,
        userId,
        collectionId,
      );

      return sendResponse(res, 'BOOKMARK_UPDATED', { data: bookmark });
    },
  );
}
