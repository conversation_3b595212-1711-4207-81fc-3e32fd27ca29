import { Request, Response } from 'express';

import prisma from '@/lib/prisma';
import SharedSolutionRepository from '@/repositories/sharedSolutionRepository';

import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class SharedSolutionController {
  private readonly sharedSolutionRepo: SharedSolutionRepository;

  constructor() {
    this.sharedSolutionRepo = new SharedSolutionRepository();
  }

  /**
   * Create a shared solution from a submission
   */
  public createSharedSolution = catchAsync(
    async (req: Request, res: Response) => {
      const { submission_id, title, description, is_public, expires_at } =
        req.body;
      const userId = req.user?.id;

      if (!userId) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      // Verify that the submission belongs to the user
      const submission = await prisma.challengeSubmission.findUnique({
        where: { id: submission_id },
        select: { user_id: true },
      });

      if (!submission) {
        return sendResponse(res, 'SUBMISSION_NOT_FOUND');
      }

      if (submission.user_id !== userId) {
        return sendResponse(res, 'UNAUTHORIZED');
      }

      // Create the shared solution
      const sharedSolution = await this.sharedSolutionRepo.createSharedSolution(
        submission_id,
        title,
        description,
        is_public !== undefined ? is_public : true,
        expires_at ? new Date(expires_at) : undefined,
      );

      return sendResponse(res, 'SOLUTION_SHARED', {
        data: { sharedSolution },
      });
    },
  );

  /**
   * Get a shared solution by its share token
   */
  public getSharedSolution = catchAsync(async (req: Request, res: Response) => {
    const { token } = req.params;

    try {
      const sharedSolution =
        await this.sharedSolutionRepo.getSharedSolutionByToken(token);

      // Record the view
      await this.sharedSolutionRepo.recordView(
        sharedSolution.id,
        req.user?.id,
        req.ip,
        req.headers['user-agent'],
        req.headers.referer,
      );

      return sendResponse(res, 'SHARED_SOLUTION_FETCHED', {
        data: { sharedSolution },
      });
    } catch (error: any) {
      if (error.statusCode === 404) {
        return sendResponse(res, 'SHARED_SOLUTION_NOT_FOUND');
      }
      if (error.statusCode === 410) {
        return sendResponse(res, 'SHARED_SOLUTION_EXPIRED');
      }
      if (error.statusCode === 403) {
        return sendResponse(res, 'SHARED_SOLUTION_PRIVATE');
      }
      throw error;
    }
  });

  /**
   * Get metadata for a shared solution (for social media previews)
   */
  public getSharedSolutionMetadata = catchAsync(
    async (req: Request, res: Response) => {
      const { token } = req.params;

      try {
        const sharedSolution =
          await this.sharedSolutionRepo.getSharedSolutionByToken(token);

        // Prepare metadata for social media previews
        const metadata = {
          title:
            sharedSolution.title ||
            `Coding Solution - ${sharedSolution.submission.challenge.title}`,
          description:
            sharedSolution.description ||
            `Check out this ${sharedSolution.submission.challenge.difficulty} level coding solution for ${sharedSolution.submission.challenge.title}`,
          language: sharedSolution.submission.language,
          author: sharedSolution.submission.user.username,
          challenge: {
            title: sharedSolution.submission.challenge.title,
            difficulty: sharedSolution.submission.challenge.difficulty,
            category: sharedSolution.submission.challenge.category,
          },
        };

        return sendResponse(res, 'SHARED_SOLUTION_METADATA_FETCHED', {
          data: { metadata },
        });
      } catch (error: any) {
        if (error.statusCode === 404) {
          return sendResponse(res, 'SHARED_SOLUTION_NOT_FOUND');
        }
        if (error.statusCode === 410) {
          return sendResponse(res, 'SHARED_SOLUTION_EXPIRED');
        }
        if (error.statusCode === 403) {
          return sendResponse(res, 'SHARED_SOLUTION_PRIVATE');
        }
        throw error;
      }
    },
  );

  /**
   * Get view statistics for a shared solution
   */
  public getViewStatistics = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    // Verify that the shared solution belongs to the user
    const sharedSolution = await this.sharedSolutionRepo.findUnique({
      where: { id },
      include: { submission: { select: { user_id: true } } },
    });

    if (!sharedSolution) {
      return sendResponse(res, 'SHARED_SOLUTION_NOT_FOUND');
    }

    // if (sharedSolution.submission.user_id !== userId) {
    //   return sendResponse(res, 'UNAUTHORIZED');
    // }

    // Get the view statistics
    const statistics = await this.sharedSolutionRepo.getViewStatistics(id);

    return sendResponse(res, 'VIEW_STATISTICS_FETCHED', {
      data: { statistics },
    });
  });

  /**
   * Update a shared solution
   */
  public updateSharedSolution = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;
      const { title, description, is_public, expires_at } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      // Verify that the shared solution belongs to the user
      const sharedSolution = await this.sharedSolutionRepo.findUnique({
        where: { id },
        include: { submission: { select: { user_id: true } } },
      });

      if (!sharedSolution) {
        return sendResponse(res, 'SHARED_SOLUTION_NOT_FOUND');
      }

      // if (sharedSolution.submission.user_id !== userId) {
      //   return sendResponse(res, 'UNAUTHORIZED');
      // }

      // Update the shared solution
      const updatedSharedSolution =
        await this.sharedSolutionRepo.updateSharedSolution(id, {
          title,
          description,
          is_public,
          expires_at: expires_at ? new Date(expires_at) : undefined,
        });

      return sendResponse(res, 'SHARED_SOLUTION_UPDATED', {
        data: { sharedSolution: updatedSharedSolution },
      });
    },
  );

  /**
   * Delete a shared solution
   */
  public deleteSharedSolution = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      // Verify that the shared solution belongs to the user
      const sharedSolution = await this.sharedSolutionRepo.findUnique({
        where: { id },
        include: { submission: { select: { user_id: true } } },
      });

      if (!sharedSolution) {
        return sendResponse(res, 'SHARED_SOLUTION_NOT_FOUND');
      }

      // if (sharedSolution.submission.user_id !== userId) {
      //   return sendResponse(res, 'UNAUTHORIZED');
      // }

      // Delete the shared solution
      await this.sharedSolutionRepo.deleteSharedSolution(id);

      return sendResponse(res, 'SHARED_SOLUTION_DELETED');
    },
  );
}
