import { Request, Response } from 'express';

import { createAppError } from '@/utils/errorHandler';

import ActivityRepository from '../repositories/activityRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class ActivityController {
  private readonly activityRepo: ActivityRepository;

  constructor() {
    this.activityRepo = new ActivityRepository();
  }

  public getActivities = catchAsync(async (req: Request, res: Response) => {
    const { limit = 10, page = 1, type } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const where: any = {};

    // If user is authenticated, filter by user_id
    if (req.user?.id) {
      where.user_id = req.user.id;
    }

    // Filter by activity type if provided
    if (type) {
      where.type = type;
    }

    const activities = await this.activityRepo.findMany({
      where,
      take: Number(limit),
      skip,
      orderBy: { timestamp: 'desc' },
    });

    const total = await this.activityRepo.countActivities(where);

    sendResponse(res, 'ACTIVITIES_FETCHED', {
      data: activities,
      meta: {
        total,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(total / Number(limit)),
      },
    });
  });

  public getActivity = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const activity = await this.activityRepo.findById(id);

    if (!activity) {
      throw createAppError('Activity not found', 404);
    }

    // Check if the activity belongs to the authenticated user
    if (req.user?.id && activity.user_id !== req.user.id) {
      throw createAppError('Unauthorized', 403);
    }

    sendResponse(res, 'ACTIVITY_FETCHED', { data: activity });
  });

  public createActivity = catchAsync(async (req: Request, res: Response) => {
    const { type, description, roadmap_id, roadmap_title } = req.body;

    if (!req.user?.id) {
      throw createAppError('Unauthorized', 401);
    }

    const activity = await this.activityRepo.create({
      user_id: req.user.id,
      type,
      description,
      roadmap_id,
      roadmap_title,
    });

    sendResponse(res, 'ACTIVITY_CREATED', { data: activity });
  });

  public deleteActivity = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    if (!req.user?.id) {
      throw createAppError('Unauthorized', 401);
    }

    const activity = await this.activityRepo.findById(id);

    if (!activity) {
      throw createAppError('Activity not found', 404);
    }

    // Check if the activity belongs to the authenticated user
    if (activity.user_id !== req.user.id) {
      throw createAppError('Unauthorized', 403);
    }

    await this.activityRepo.delete(id);

    sendResponse(res, 'ACTIVITY_DELETED');
  });
}
