import { Request, Response } from 'express';

import { createAppError } from '@/utils/errorHandler';

import AchievementRepository from '../repositories/achievementRepository';
import achievementService from '../services/achievementService';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class AchievementController {
  private readonly achievementRepo: AchievementRepository;

  constructor() {
    this.achievementRepo = new AchievementRepository();
  }

  /**
   * Get all achievements
   */
  public getAllAchievements = catchAsync(
    async (req: Request, res: Response) => {
      const { category, trigger_type } = req.query;

      let achievements;

      if (category) {
        achievements = await this.achievementRepo.getAchievementsByCategory(
          String(category) as any,
        );
      } else if (trigger_type) {
        achievements = await this.achievementRepo.getAchievementsByTriggerType(
          String(trigger_type) as any,
        );
      } else {
        achievements = await this.achievementRepo.findMany({
          where: { is_active: true },
          orderBy: [
            { category: 'asc' },
            { tier: 'asc' },
            { trigger_value: 'asc' },
          ],
        });
      }

      return sendResponse(res, 'ACHIEVEMENTS_FETCHED', {
        data: { achievements },
      });
    },
  );

  /**
   * Get a specific achievement
   */
  public getAchievement = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    const achievement = await this.achievementRepo.findUnique({
      where: { id },
    });

    if (!achievement) {
      return sendResponse(res, 'ACHIEVEMENT_NOT_FOUND');
    }

    return sendResponse(res, 'ACHIEVEMENT_FETCHED', {
      data: { achievement },
    });
  });

  /**
   * Create a new achievement (admin only)
   */
  public createAchievement = catchAsync(async (req: Request, res: Response) => {
    const achievement = await this.achievementRepo.createAchievement(req.body);

    return sendResponse(res, 'ACHIEVEMENT_CREATED', {
      data: { achievement },
    });
  });

  /**
   * Update an achievement (admin only)
   */
  public updateAchievement = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    const achievement = await this.achievementRepo.updateAchievement(
      id,
      req.body,
    );

    return sendResponse(res, 'ACHIEVEMENT_UPDATED', {
      data: { achievement },
    });
  });

  /**
   * Get user's achievements
   */
  public getUserAchievements = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;

      if (!userId) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      const achievements =
        await this.achievementRepo.getUserAchievements(userId);

      return sendResponse(res, 'USER_ACHIEVEMENTS_FETCHED', {
        data: { achievements },
      });
    },
  );

  /**
   * Get achievement notifications
   */
  public getAchievementNotifications = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;
      const { unread_only } = req.query;

      if (!userId) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      const notifications =
        await achievementService.getAchievementNotifications(
          userId,
          unread_only === 'true',
        );

      return sendResponse(res, 'ACHIEVEMENT_NOTIFICATIONS_FETCHED', {
        data: { notifications },
      });
    },
  );

  /**
   * Mark achievement notifications as read
   */
  public markNotificationsAsRead = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;
      const { notification_ids } = req.body;

      if (!userId) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      await achievementService.markNotificationsAsRead(
        userId,
        notification_ids || [],
      );

      return sendResponse(res, 'NOTIFICATIONS_MARKED_AS_READ');
    },
  );

  /**
   * Check for new achievements
   */
  public checkAchievements = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const newAchievements =
      await achievementService.checkAllAchievements(userId);

    return sendResponse(res, 'ACHIEVEMENTS_CHECKED', {
      data: {
        newAchievements,
        count: newAchievements.length,
      },
    });
  });

  /**
   * Manually award an achievement (admin only)
   */
  public awardAchievement = catchAsync(async (req: Request, res: Response) => {
    const { user_id, achievement_id } = req.body;

    if (!user_id || !achievement_id) {
      throw createAppError('User ID and achievement ID are required', 400);
    }

    const achievement = await achievementService.awardAchievement(
      user_id,
      achievement_id,
    );

    return sendResponse(res, 'ACHIEVEMENT_AWARDED', {
      data: { achievement },
    });
  });

  /**
   * Get achievement statistics (admin only)
   */
  public getAchievementStats = catchAsync(
    async (req: Request, res: Response) => {
      const stats = await this.achievementRepo.getAchievementStats();

      return sendResponse(res, 'ACHIEVEMENT_STATS_FETCHED', {
        data: { stats },
      });
    },
  );

  /**
   * Get most popular achievements
   */
  public getMostPopularAchievements = catchAsync(
    async (req: Request, res: Response) => {
      const { limit } = req.query;

      const achievements =
        await this.achievementRepo.getMostPopularAchievements(
          limit ? parseInt(String(limit)) : 10,
        );

      return sendResponse(res, 'POPULAR_ACHIEVEMENTS_FETCHED', {
        data: { achievements },
      });
    },
  );

  /**
   * Get rarest achievements
   */
  public getRarestAchievements = catchAsync(
    async (req: Request, res: Response) => {
      const { limit } = req.query;

      const achievements = await this.achievementRepo.getRarestAchievements(
        limit ? parseInt(String(limit)) : 10,
      );

      return sendResponse(res, 'RAREST_ACHIEVEMENTS_FETCHED', {
        data: { achievements },
      });
    },
  );
}
