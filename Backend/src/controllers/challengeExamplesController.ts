import { Request, Response } from 'express';

import ChallengeExamplesRepository from '../repositories/challengeExamplesRepository';
import ChallengeRepository from '../repositories/challengeRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';
import { createAppError } from '../utils/errorHandler';

export default class ChallengeExamplesController {
  private readonly examplesRepo: ChallengeExamplesRepository;
  private readonly challengeRepo: ChallengeRepository;

  constructor() {
    this.examplesRepo = new ChallengeExamplesRepository();
    this.challengeRepo = new ChallengeRepository();
  }

  /**
   * Get all examples for a challenge
   */
  public getExamples = catchAsync(async (req: Request, res: Response) => {
    const { challengeId } = req.params;

    const examples =
      await this.examplesRepo.getExamplesByChallenge(challengeId);

    return sendResponse(res, 'CHALLENGES_FETCHED', { data: { examples } });
  });

  /**
   * Create a new example
   */
  public createExample = catchAsync(async (req: Request, res: Response) => {
    const { challengeId } = req.params;
    const { input, output, explanation, order_index } = req.body;

    // Check if challenge exists
    const challenge = await this.challengeRepo.findUnique({
      where: { id: challengeId },
    });

    if (!challenge) {
      throw createAppError('Challenge not found', 404);
    }

    // Create the example
    const example = await this.examplesRepo.createExample({
      challenge_id: challengeId,
      input,
      output,
      explanation,
      order_index,
    });

    return sendResponse(res, 'CHALLENGE_CREATED', { data: { example } });
  });

  /**
   * Update an example
   */
  public updateExample = catchAsync(async (req: Request, res: Response) => {
    const { challengeId, id } = req.params;
    const { input, output, explanation, order_index } = req.body;

    // Check if example exists and belongs to the challenge
    const exampleExists = await this.examplesRepo.exampleBelongsToChallenge(
      id,
      challengeId,
    );

    if (!exampleExists) {
      throw createAppError(
        'Example not found or does not belong to the challenge',
        404,
      );
    }

    // Update the example
    const example = await this.examplesRepo.updateExample(id, {
      input,
      output,
      explanation,
      order_index,
    });

    return sendResponse(res, 'CHALLENGE_UPDATED', { data: { example } });
  });

  /**
   * Delete an example
   */
  public deleteExample = catchAsync(async (req: Request, res: Response) => {
    const { challengeId, id } = req.params;

    // Check if example exists and belongs to the challenge
    const exampleExists = await this.examplesRepo.exampleBelongsToChallenge(
      id,
      challengeId,
    );

    if (!exampleExists) {
      throw createAppError(
        'Example not found or does not belong to the challenge',
        404,
      );
    }

    // Delete the example
    await this.examplesRepo.deleteExample(id);

    return sendResponse(res, 'CHALLENGE_UPDATED', { data: null });
  });
}
