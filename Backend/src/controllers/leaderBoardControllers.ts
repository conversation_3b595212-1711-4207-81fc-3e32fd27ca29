import { Request, Response } from 'express';

import LeaderboardRepository, {
  LeaderboardOptions,
} from '@/repositories/leaderboardRepository';
import { sendResponse } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import { catchAsync } from '../utils';

export default class LeaderboardController {
  private readonly leaderboardRepo: LeaderboardRepository;

  constructor() {
    this.leaderboardRepo = new LeaderboardRepository();
  }

  /**
   * Get leaderboard entries with various filtering options
   */
  public getLeaderboardEntries = catchAsync(
    async (req: Request, res: Response) => {
      const {
        subject_id,
        challenge_id,
        language,
        time_range = 'all',
        limit = 10,
        page = 1,
      } = req.query;

      const options: LeaderboardOptions = {
        subject_id: subject_id as string,
        challenge_id: challenge_id as string,
        language: language as string,
        time_range: time_range as 'daily' | 'weekly' | 'monthly' | 'all',
        limit: Number(limit),
        page: Number(page),
      };

      const leaderboard = await this.leaderboardRepo.getLeaderboard(options);

      sendResponse(res, 'LEADERBOARD_FETCHED', {
        data: leaderboard.data,
        meta: leaderboard.meta,
      });
    },
  );

  /**
   * Get language-specific leaderboard for a challenge
   */
  public getLanguageLeaderboard = catchAsync(
    async (req: Request, res: Response) => {
      const { challenge_id } = req.params;
      const { language, limit = 10, page = 1 } = req.query;

      if (!challenge_id) {
        throw createAppError('Challenge ID is required', 400);
      }

      if (!language) {
        throw createAppError('Language is required', 400);
      }

      const leaderboard = await this.leaderboardRepo.getLanguageLeaderboard(
        challenge_id,
        language as string,
        Number(limit),
        Number(page),
      );

      sendResponse(res, 'LANGUAGE_LEADERBOARD_FETCHED', {
        data: leaderboard.data,
        meta: leaderboard.meta,
      });
    },
  );

  /**
   * Get time-based leaderboard (daily, weekly, monthly, all-time)
   */
  public getTimeBasedLeaderboard = catchAsync(
    async (req: Request, res: Response) => {
      const { time_range = 'all', limit = 10, page = 1 } = req.query;

      const validTimeRanges = ['daily', 'weekly', 'monthly', 'all'];
      if (!validTimeRanges.includes(time_range as string)) {
        throw createAppError(
          'Invalid time range. Must be one of: daily, weekly, monthly, all',
          400,
        );
      }

      const leaderboard = await this.leaderboardRepo.getTimeBasedLeaderboard(
        time_range as 'daily' | 'weekly' | 'monthly' | 'all',
        Number(limit),
        Number(page),
      );

      sendResponse(res, 'TIME_BASED_LEADERBOARD_FETCHED', {
        data: leaderboard.data,
        meta: leaderboard.meta,
      });
    },
  );

  /**
   * Get weekly leaderboard for the landing page
   */
  public getWeeklyLeaderboard = catchAsync(
    async (req: Request, res: Response) => {
      const { limit = 10 } = req.query;

      const leaderboard = await this.leaderboardRepo.getWeeklyLeaderboard(
        Number(limit),
      );
      const stats = await this.leaderboardRepo.getCurrentWeekStats();

      sendResponse(res, 'WEEKLY_LEADERBOARD_FETCHED', {
        data: { leaderboard, stats },
      });
    },
  );

  /**
   * Check if a submission is suspicious (potential cheating)
   */
  public checkSubmission = catchAsync(async (req: Request, res: Response) => {
    const { submission_id } = req.params;

    if (!submission_id) {
      throw createAppError('Submission ID is required', 400);
    }

    const result = await this.leaderboardRepo.detectCheating(submission_id);

    if (result.isSuspicious) {
      logger.warn('Suspicious submission detected', {
        submission_id,
        reasons: result.reasons,
      });
    }

    sendResponse(res, 'SUBMISSION_CHECKED', {
      data: result,
    });
  });
}
