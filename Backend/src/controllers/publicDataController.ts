import { Request, Response } from 'express';

import CollegeRepository from '../repositories/collegeRepository';
import LeaderboardRepository from '../repositories/leaderboardRepository';
import { PublicStatsRepository } from '../repositories/publicStatsRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';
import { createAppError } from '../utils/errorHandler';
import { publicDataValidation } from '../validations/publicDataValidation';

export default class PublicDataController {
  private readonly leaderboardRepo: LeaderboardRepository;
  private readonly statsRepo: PublicStatsRepository;
  private readonly collegeRepo: CollegeRepository;

  constructor() {
    this.leaderboardRepo = new LeaderboardRepository();
    this.statsRepo = new PublicStatsRepository();
    this.collegeRepo = new CollegeRepository();
  }

  public getWeeklyLeaderboard = catchAsync(
    async (req: Request, res: Response) => {
      const { error, value } = publicDataValidation.leaderboardSchema.validate(
        req.query,
      );
      if (error) throw createAppError(error.message, 400);

      const { limit = 10 } = value;
      const leaderboard =
        await this.leaderboardRepo.getWeeklyLeaderboard(limit);

      return sendResponse(res, 'LEADERBOARD_FETCHED', { data: leaderboard });
    },
  );

  public getPlatformStats = catchAsync(async (req: Request, res: Response) => {
    const stats = await this.statsRepo.getPlatformStats();
    return sendResponse(res, 'PLATFORM_STATS_FETCHED', { data: stats });
  });

  public getTopColleges = catchAsync(async (req: Request, res: Response) => {
    const { error, value } = publicDataValidation.collegeSchema.validate(
      req.query,
    );
    if (error) throw createAppError(error.message, 400);

    const { limit = 10 } = value;
    const colleges = await this.collegeRepo.getTopColleges(limit);

    return sendResponse(res, 'COLLEGES_FETCHED', { data: colleges });
  });

  public searchColleges = catchAsync(async (req: Request, res: Response) => {
    const { error, value } = publicDataValidation.collegeSearchSchema.validate(
      req.query,
    );
    if (error) throw createAppError(error.message, 400);

    const { query, limit = 10 } = value;
    const colleges = await this.collegeRepo.searchColleges(query, limit);

    return sendResponse(res, 'COLLEGES_FETCHED', { data: colleges });
  });
}
