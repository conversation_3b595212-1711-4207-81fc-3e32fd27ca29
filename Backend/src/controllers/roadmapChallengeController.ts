import { Request, Response } from 'express';

import { createAppError } from '@/utils/errorHandler';

import RoadmapChallengeRepository from '../repositories/roadmapChallengeRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class RoadmapChallengeController {
  private readonly roadmapChallengeRepo: RoadmapChallengeRepository;

  constructor() {
    this.roadmapChallengeRepo = new RoadmapChallengeRepository();
  }

  /**
   * Get all challenges for a roadmap
   */
  public getChallengesByRoadmap = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;

      if (!id) {
        throw createAppError('Roadmap ID is required', 400);
      }

      const challenges =
        await this.roadmapChallengeRepo.getChallengesByRoadmap(id);

      return sendResponse(res, 'ROADMAP_CHALLENGES_FETCHED', {
        data: { challenges },
      });
    },
  );

  /**
   * Get all roadmaps for a challenge
   */
  public getRoadmapsByChallenge = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;

      if (!id) {
        throw createAppError('Challenge ID is required', 400);
      }

      const roadmaps =
        await this.roadmapChallengeRepo.getRoadmapsByChallenge(id);

      return sendResponse(res, 'CHALLENGE_ROADMAPS_FETCHED', {
        data: { roadmaps },
      });
    },
  );

  /**
   * Add a challenge to a roadmap
   */
  public addChallengeToRoadmap = catchAsync(
    async (req: Request, res: Response) => {
      const { roadmap_id, challenge_id, order, is_required } = req.body;

      if (!roadmap_id || !challenge_id) {
        throw createAppError('Roadmap ID and Challenge ID are required', 400);
      }

      const roadmapChallenge =
        await this.roadmapChallengeRepo.addChallengeToRoadmap(
          roadmap_id,
          challenge_id,
          order,
          is_required,
        );

      return sendResponse(res, 'CHALLENGE_ADDED_TO_ROADMAP', {
        data: { roadmapChallenge },
      });
    },
  );

  /**
   * Remove a challenge from a roadmap
   */
  public removeChallengeFromRoadmap = catchAsync(
    async (req: Request, res: Response) => {
      const { roadmap_id, challenge_id } = req.body;

      if (!roadmap_id || !challenge_id) {
        throw createAppError('Roadmap ID and Challenge ID are required', 400);
      }

      const roadmapChallenge =
        await this.roadmapChallengeRepo.removeChallengeFromRoadmap(
          roadmap_id,
          challenge_id,
        );

      return sendResponse(res, 'CHALLENGE_REMOVED_FROM_ROADMAP', {
        data: { roadmapChallenge },
      });
    },
  );

  /**
   * Update a roadmap challenge
   */
  public updateRoadmapChallenge = catchAsync(
    async (req: Request, res: Response) => {
      const { roadmap_id, challenge_id, order, is_required } = req.body;

      if (!roadmap_id || !challenge_id) {
        throw createAppError('Roadmap ID and Challenge ID are required', 400);
      }

      const roadmapChallenge =
        await this.roadmapChallengeRepo.updateRoadmapChallenge(
          roadmap_id,
          challenge_id,
          { order, is_required },
        );

      return sendResponse(res, 'ROADMAP_CHALLENGE_UPDATED', {
        data: { roadmapChallenge },
      });
    },
  );

  /**
   * Get suggested challenges based on roadmap progress
   */
  public getSuggestedChallenges = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;
      const userId = req.user?.id;
      const { limit } = req.query;

      if (!id) {
        throw createAppError('Roadmap ID is required', 400);
      }

      if (!userId) {
        throw createAppError('User not authenticated', 401);
      }

      const challenges = await this.roadmapChallengeRepo.getSuggestedChallenges(
        userId,
        id,
        limit ? parseInt(String(limit)) : 5,
      );

      return sendResponse(res, 'SUGGESTED_CHALLENGES_FETCHED', {
        data: { challenges },
      });
    },
  );

  /**
   * Update roadmap progress when a challenge is completed
   */
  public updateRoadmapProgress = catchAsync(
    async (req: Request, res: Response) => {
      const { challenge_id } = req.body;
      const userId = req.user?.id;

      if (!challenge_id) {
        throw createAppError('Challenge ID is required', 400);
      }

      if (!userId) {
        throw createAppError('User not authenticated', 401);
      }

      await this.roadmapChallengeRepo.updateRoadmapProgress(
        userId,
        challenge_id,
      );

      return sendResponse(res, 'ROADMAP_PROGRESS_UPDATED');
    },
  );
}
