import { Request, Response } from 'express';

import { createAppError } from '@/utils/errorHandler';

import AchievementRepository from '../repositories/achievementRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class AchievementController {
  private readonly achievementRepo: AchievementRepository;

  constructor() {
    this.achievementRepo = new AchievementRepository();
  }

  public getAchievements = catchAsync(async (req: Request, res: Response) => {
    const { limit = 10, page = 1, rarity } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const where: any = {};

    // If user is authenticated, filter by user_id
    if (req.user?.id) {
      where.user_id = req.user.id;
    }

    // Filter by type if provided (we're using type instead of rarity)
    if (rarity) {
      where.type = rarity;
    }

    const achievements = await this.achievementRepo.findMany({
      where,
      take: Number(limit),
      skip,
      orderBy: { earned_at: 'desc' },
    });

    const total = await this.achievementRepo.count({ where });

    sendResponse(res, 'ACHIEVEMENTS_FETCHED', {
      data: achievements,
      meta: {
        total,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(total / Number(limit)),
      },
    });
  });

  public getAchievement = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const achievement = await this.achievementRepo.findUnique(id);

    if (!achievement) {
      throw createAppError('Achievement not found', 404);
    }

    // Check if the achievement belongs to the authenticated user
    if (req.user?.id) {
      throw createAppError('Unauthorized', 403);
    }

    sendResponse(res, 'ACHIEVEMENT_FETCHED', { data: achievement });
  });

  public createAchievement = catchAsync(async (req: Request, res: Response) => {
    const { title, description, icon, rarity = 'common' } = req.body;

    if (!req.user?.id) {
      throw createAppError('Unauthorized', 401);
    }

    const achievement = await this.achievementRepo.create({
      user_id: req.user.id,
      title,
      description,
      icon,
      rarity,
    });

    sendResponse(res, 'ACHIEVEMENT_CREATED', { data: achievement });
  });

  public deleteAchievement = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    if (!req.user?.id) {
      throw createAppError('Unauthorized', 401);
    }

    const achievement = await this.achievementRepo.findUnique(id);

    if (!achievement) {
      throw createAppError('Achievement not found', 404);
    }

    await this.achievementRepo.delete(id);

    sendResponse(res, 'ACHIEVEMENT_DELETED');
  });
}
