import { Request, Response } from 'express';

import prisma from '@/lib/prisma';
import { createAppError } from '@/utils/errorHandler';

import HintRepository from '../repositories/hintRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class HintController {
  private readonly hintRepo: HintRepository;

  constructor() {
    this.hintRepo = new HintRepository();
  }

  /**
   * Get hints for a challenge
   */
  public getHintsForChallenge = catchAsync(
    async (req: Request, res: Response) => {
      const { challengeId } = req.params;
      const userId = req.user?.id;

      const hints = await this.hintRepo.getHintsForChallenge(
        challengeId,
        userId,
      );

      return sendResponse(res, 'HINTS_FETCHED', {
        data: { hints },
      });
    },
  );

  /**
   * Create a new hint
   */
  public createHint = catchAsync(async (req: Request, res: Response) => {
    const { challenge_id, content, difficulty, order, point_penalty } =
      req.body;

    const hint = await this.hintRepo.createHint({
      challenge_id,
      content,
      difficulty,
      order,
      point_penalty,
    });

    return sendResponse(res, 'HINT_CREATED', {
      data: { hint },
    });
  });

  /**
   * Update a hint
   */
  public updateHint = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { content, difficulty, order, point_penalty } = req.body;

    const hint = await this.hintRepo.updateHint(id, {
      content,
      difficulty,
      order,
      point_penalty,
    });

    return sendResponse(res, 'HINT_UPDATED', {
      data: { hint },
    });
  });

  /**
   * Delete a hint
   */
  public deleteHint = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    await this.hintRepo.deleteHint(id);

    return sendResponse(res, 'HINT_DELETED');
  });

  /**
   * Unlock a hint for a user
   */
  public unlockHint = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    // Check if the user has enough points
    const userPoints = await prisma.userPoints.findUnique({
      where: { user_id: userId },
    });

    const hint = await prisma.challengeHint.findUnique({
      where: { id },
    });

    if (!hint) {
      throw createAppError('Hint not found', 404);
    }

    if (!userPoints || userPoints.points < hint.point_penalty) {
      throw createAppError('Not enough points to unlock this hint', 400);
    }

    const unlockedHint = await this.hintRepo.unlockHint(id, userId);

    return sendResponse(res, 'HINT_UNLOCKED', {
      data: { hint: unlockedHint },
    });
  });

  /**
   * Get hint usage statistics
   */
  public getHintUsageStats = catchAsync(async (req: Request, res: Response) => {
    const { challenge_id } = req.query;

    const stats = await this.hintRepo.getHintUsageStats(
      challenge_id ? String(challenge_id) : undefined,
    );

    return sendResponse(res, 'HINT_USAGE_STATS_FETCHED', {
      data: { stats },
    });
  });

  /**
   * Get user's hint usage
   */
  public getUserHintUsage = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const usage = await this.hintRepo.getUserHintUsage(userId);

    return sendResponse(res, 'USER_HINT_USAGE_FETCHED', {
      data: { usage },
    });
  });
}
