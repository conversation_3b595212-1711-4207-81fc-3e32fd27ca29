import { Request, Response } from 'express';

import RoadmapRepository from '../repositories/roadmapRepository';
import { sendResponse } from '../utils/apiResponse';
import { catchAsync } from '../utils/catchAsync';
import { createAppError } from '../utils/createAppError';
import logger from '../utils/logger';

export default class RoadmapAdminController {
  private readonly roadmapRepo: RoadmapRepository;

  constructor() {
    this.roadmapRepo = new RoadmapRepository();
  }

  // Get all roadmaps with pagination for admin
  getAllRoadmaps = catchAsync(async (req: Request, res: Response) => {
    logger.info('[RoadmapAdminController] Fetching all roadmaps for admin');

    const { page = 1, per_page = 10, search, difficulty, status } = req.query;

    // Create filter object
    const filter: any = {};

    // Add difficulty filter if provided
    if (difficulty && difficulty !== 'all') {
      filter.difficulty = difficulty.toString().toLowerCase();
    }
    
    // Add status filter if provided
    if (status && status !== 'all') {
      filter.status = status.toString();
    }

    // Add search filter if provided
    if (search) {
      filter.title = {
        contains: search.toString(),
        mode: 'insensitive',
      };
    }

    // Get roadmaps with pagination
    const result = await this.roadmapRepo.getAdminRoadmaps({
      page: Number(page),
      perPage: Number(per_page),
      filter,
    });

    sendResponse(res, 'ROADMAPS_FETCHED', { data: result });
  });

  // Get a specific roadmap by ID
  getRoadmapById = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    logger.info(`[RoadmapAdminController] Fetching roadmap with ID: ${id}`);

    const roadmap = await this.roadmapRepo.getRoadmap(id);

    if (!roadmap) {
      throw createAppError('Roadmap not found', 404);
    }

    sendResponse(res, 'ROADMAP_FETCHED', { data: roadmap });
  });

  // Create a new roadmap
  createRoadmap = catchAsync(async (req: Request, res: Response) => {
    logger.info('[RoadmapAdminController] Creating new roadmap');

    const userId = req.user?.id;
    if (!userId) {
      throw createAppError('User not authenticated', 401);
    }

    const {
      title,
      description,
      category_id,
      difficulty,
      estimatedHours,
      is_public,
      version,
      tags,
      main_concepts,
    } = req.body;

    // Create roadmap with the required fields from the interface
    const roadmap = await this.roadmapRepo.createRoadmap({
      title,
      description,
      author_id: userId,
      is_public: is_public || false,
      concepts: main_concepts,
    });

    sendResponse(res, 'ROADMAP_CREATED', { data: roadmap });
  });

  // Update an existing roadmap
  updateRoadmap = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    logger.info(`[RoadmapAdminController] Updating roadmap with ID: ${id}`);

    const {
      title,
      description,
      category_id,
      difficulty,
      estimatedHours,
      is_public,
      version,
      tags,
      main_concepts,
    } = req.body;

    // Check if roadmap exists
    const existingRoadmap = await this.roadmapRepo.getRoadmap(id);
    if (!existingRoadmap) {
      throw createAppError('Roadmap not found', 404);
    }

    // Check if status is being updated
    const isStatusUpdate = req.body.hasOwnProperty('status');
    const { status } = req.body;
    
    // Update roadmap with the fields from the interface
    // Create the update object with the allowed properties
    const updateData: any = {};
    
    // Only add properties that are provided in the request
    if (title) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (is_public !== undefined) updateData.is_public = is_public;
    if (main_concepts) updateData.concepts = main_concepts;
    if (difficulty) updateData.difficulty = difficulty;
    if (estimatedHours) updateData.estimatedHours = estimatedHours;
    if (category_id) updateData.category_id = category_id;
    if (tags) updateData.tags = tags;
    
    // Add status if it's provided (handled separately from RoadmapData interface)
    if (isStatusUpdate && ['DRAFT', 'ACTIVE', 'INACTIVE'].includes(status.toUpperCase())) {
      updateData.status = status.toUpperCase();
    }
    
    const updatedRoadmap = await this.roadmapRepo.updateRoadmap(id, updateData);

    // Include toast flag in the response if it's a status update
    if (isStatusUpdate) {
      sendResponse(res, 'ROADMAP_UPDATED', { 
        data: updatedRoadmap,
        toast: true,
        message: `Roadmap status updated to ${updateData.status} successfully` 
      });
    } else {
      sendResponse(res, 'ROADMAP_UPDATED', { data: updatedRoadmap });
    }
  });

  // Delete a roadmap
  deleteRoadmap = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    logger.info(`[RoadmapAdminController] Deleting roadmap with ID: ${id}`);

    // Check if roadmap exists
    const existingRoadmap = await this.roadmapRepo.getRoadmap(id);
    if (!existingRoadmap) {
      throw createAppError('Roadmap not found', 404);
    }

    await this.roadmapRepo.deleteRoadmap(id);

    sendResponse(res, 'ROADMAP_DELETED', { data: { id } });
  });

  // Toggle featured status
  toggleFeatured = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { isFeatured } = req.body;

    logger.info(
      `[RoadmapAdminController] Toggling featured status for roadmap ${id} to ${isFeatured}`,
    );

    // Check if roadmap exists
    const existingRoadmap = await this.roadmapRepo.getRoadmap(id);
    if (!existingRoadmap) {
      throw createAppError('Roadmap not found', 404);
    }

    // Update featured status
    const updatedRoadmap = await this.roadmapRepo.updateRoadmap(id, {
      is_featured: isFeatured,
    });

    sendResponse(res, 'ROADMAP_FEATURED_TOGGLED', { data: updatedRoadmap });
  });

  // Bulk update status for multiple roadmaps
  bulkUpdateStatus = catchAsync(async (req: Request, res: Response) => {
    const { roadmapIds, status } = req.body;
    
    logger.info(
      `[RoadmapAdminController] Bulk updating status for ${roadmapIds.length} roadmaps to ${status}`,
    );

    if (!roadmapIds || !Array.isArray(roadmapIds) || roadmapIds.length === 0) {
      throw createAppError('No roadmap IDs provided', 400);
    }

    if (!status || !['DRAFT', 'ACTIVE', 'INACTIVE'].includes(status.toUpperCase())) {
      throw createAppError('Invalid status value', 400);
    }

    // Update status for all provided roadmaps
    const results = await this.roadmapRepo.bulkUpdateStatus(roadmapIds, status.toUpperCase());

    sendResponse(res, 'ROADMAPS_STATUS_UPDATED', { 
      data: { 
        updatedCount: results.count,
        status: status.toUpperCase() 
      },
      toast: true,
      message: `${results.count} roadmaps updated to ${status.toUpperCase()} status`
    });
  });
}
