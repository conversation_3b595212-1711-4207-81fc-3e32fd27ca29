import { Request, Response } from 'express';

import BookmarkCollectionRepository from '@/repositories/bookmarkCollectionRepository';
import { catchAsync } from '@/utils';
import { sendResponse } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

export default class BookmarkCollectionController {
  private readonly collectionRepo: BookmarkCollectionRepository;

  constructor() {
    this.collectionRepo = new BookmarkCollectionRepository();
  }

  /**
   * Get all bookmark collections for the authenticated user
   */
  public getCollections = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    const collections = await this.collectionRepo.getUserCollections(userId);

    return sendResponse(res, 'COLLECTIONS_FETCHED', { data: collections });
  });

  /**
   * Get a single bookmark collection by ID
   */
  public getCollection = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    const { id } = req.params;
    const collection = await this.collectionRepo.getCollectionById(id, userId);

    return sendResponse(res, 'COLLECTION_FETCHED', { data: collection });
  });

  /**
   * Create a new bookmark collection
   */
  public createCollection = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    const { name, description } = req.body;
    const collection = await this.collectionRepo.createCollection(
      userId,
      name,
      description,
    );

    logger.info(`User ${userId} created bookmark collection ${collection.id}`);
    return sendResponse(res, 'COLLECTION_CREATED', { data: collection });
  });

  /**
   * Update a bookmark collection
   */
  public updateCollection = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    const { id } = req.params;
    const { name, description } = req.body;
    const collection = await this.collectionRepo.updateCollection(id, userId, {
      name,
      description,
    });

    logger.info(`User ${userId} updated bookmark collection ${id}`);
    return sendResponse(res, 'COLLECTION_UPDATED', { data: collection });
  });

  /**
   * Delete a bookmark collection
   */
  public deleteCollection = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    const { id } = req.params;
    await this.collectionRepo.deleteCollection(id, userId);

    logger.info(`User ${userId} deleted bookmark collection ${id}`);
    return sendResponse(res, 'COLLECTION_DELETED', { data: null });
  });

  /**
   * Add bookmarks to a collection
   */
  public addBookmarksToCollection = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;
      if (!userId) {
        throw createAppError('Unauthorized', 401);
      }

      const { id } = req.params;
      const { bookmarkIds } = req.body;

      if (!Array.isArray(bookmarkIds) || bookmarkIds.length === 0) {
        throw createAppError('Bookmark IDs are required', 400);
      }

      const collection = await this.collectionRepo.addBookmarksToCollection(
        id,
        userId,
        bookmarkIds,
      );

      logger.info(
        `User ${userId} added ${bookmarkIds.length} bookmarks to collection ${id}`,
      );
      return sendResponse(res, 'BOOKMARKS_ADDED_TO_COLLECTION', {
        data: collection,
      });
    },
  );

  /**
   * Remove bookmarks from a collection
   */
  public removeBookmarksFromCollection = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;
      if (!userId) {
        throw createAppError('Unauthorized', 401);
      }

      const { id } = req.params;
      const { bookmarkIds } = req.body;

      if (!Array.isArray(bookmarkIds) || bookmarkIds.length === 0) {
        throw createAppError('Bookmark IDs are required', 400);
      }

      const collection =
        await this.collectionRepo.removeBookmarksFromCollection(
          id,
          userId,
          bookmarkIds,
        );

      logger.info(
        `User ${userId} removed ${bookmarkIds.length} bookmarks from collection ${id}`,
      );
      return sendResponse(res, 'BOOKMARKS_REMOVED_FROM_COLLECTION', {
        data: collection,
      });
    },
  );

  /**
   * Move bookmarks between collections
   */
  public moveBookmarks = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) {
      throw createAppError('Unauthorized', 401);
    }

    const { sourceId, targetId } = req.params;
    const { bookmarkIds } = req.body;

    if (!Array.isArray(bookmarkIds) || bookmarkIds.length === 0) {
      throw createAppError('Bookmark IDs are required', 400);
    }

    const collection = await this.collectionRepo.moveBookmarks(
      sourceId,
      targetId,
      userId,
      bookmarkIds,
    );

    logger.info(
      `User ${userId} moved ${bookmarkIds.length} bookmarks from collection ${sourceId} to ${targetId}`,
    );
    return sendResponse(res, 'BOOKMARKS_MOVED', { data: collection });
  });
}
