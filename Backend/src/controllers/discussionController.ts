import { Request, Response } from 'express';

import { RoleType } from '@prisma/client';

import { createAppError } from '@/utils/errorHandler';

import DiscussionRepository from '../repositories/discussionRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class DiscussionController {
  private readonly discussionRepo: DiscussionRepository;

  constructor() {
    this.discussionRepo = new DiscussionRepository();
  }

  /**
   * Get discussions for a challenge
   */
  public getDiscussionsForChallenge = catchAsync(
    async (req: Request, res: Response) => {
      const { challengeId } = req.params;
      const userId = req.user?.id;
      const isAdmin = req.hasRole ? req.hasRole(RoleType.ADMIN) : false;

      const discussions = await this.discussionRepo.getDiscussionsForChallenge(
        challengeId,
        userId,
        isAdmin,
      );

      return sendResponse(res, 'DISCUSSIONS_FETCHED', {
        data: { discussions },
      });
    },
  );

  /**
   * Get a single discussion with its replies
   */
  public getDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;

    const discussion = await this.discussionRepo.getDiscussion(id, userId);

    return sendResponse(res, 'DISCUSSION_FETCHED', {
      data: { discussion },
    });
  });

  /**
   * Create a new discussion
   */
  public createDiscussion = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const { challenge_id, parent_id, content, code_snippet, code_language } =
      req.body;

    const discussion = await this.discussionRepo.createDiscussion({
      challenge_id,
      user_id: userId,
      parent_id,
      content,
      code_snippet,
      code_language,
    });

    return sendResponse(res, 'DISCUSSION_CREATED', {
      data: { discussion },
    });
  });

  /**
   * Update a discussion
   */
  public updateDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const { content, code_snippet, code_language } = req.body;

    const discussion = await this.discussionRepo.updateDiscussion(id, userId, {
      content,
      code_snippet,
      code_language,
    });

    return sendResponse(res, 'DISCUSSION_UPDATED', {
      data: { discussion },
    });
  });

  /**
   * Delete a discussion
   */
  public deleteDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;
    const isAdmin = req.hasRole ? req.hasRole(RoleType.ADMIN) : false;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    await this.discussionRepo.deleteDiscussion(id, userId, isAdmin);

    return sendResponse(res, 'DISCUSSION_DELETED');
  });

  /**
   * Vote on a discussion
   */
  public voteDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const { vote_type } = req.body;

    if (!vote_type || !['UPVOTE', 'DOWNVOTE'].includes(vote_type)) {
      throw createAppError('Invalid vote type', 400);
    }

    const discussion = await this.discussionRepo.voteDiscussion(
      id,
      userId,
      vote_type,
    );

    return sendResponse(res, 'DISCUSSION_VOTED', {
      data: { discussion },
    });
  });

  /**
   * Flag a discussion
   */
  public flagDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const { reason, details } = req.body;

    if (
      !reason ||
      !['SPAM', 'INAPPROPRIATE', 'OFFENSIVE', 'INCORRECT', 'OTHER'].includes(
        reason,
      )
    ) {
      throw createAppError('Invalid flag reason', 400);
    }

    const flag = await this.discussionRepo.flagDiscussion(
      id,
      userId,
      reason,
      details,
    );

    return sendResponse(res, 'DISCUSSION_FLAGGED', {
      data: { flag },
    });
  });

  /**
   * Moderate a flagged discussion
   */
  public moderateDiscussion = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;
      const { action } = req.body;

      if (!action || !['HIDE', 'APPROVE'].includes(action)) {
        throw createAppError('Invalid moderation action', 400);
      }

      const discussion = await this.discussionRepo.moderateDiscussion(
        id,
        action,
      );

      return sendResponse(res, 'DISCUSSION_MODERATED', {
        data: { discussion },
      });
    },
  );

  /**
   * Get flagged discussions
   */
  public getFlaggedDiscussions = catchAsync(
    async (req: Request, res: Response) => {
      const { status } = req.query;

      const validStatuses = ['PENDING', 'APPROVED', 'REJECTED'];
      const moderationStatus =
        status && validStatuses.includes(String(status))
          ? String(status)
          : undefined;

      const flaggedDiscussions =
        await this.discussionRepo.getFlaggedDiscussions(
          moderationStatus as any,
        );

      return sendResponse(res, 'FLAGGED_DISCUSSIONS_FETCHED', {
        data: { flaggedDiscussions },
      });
    },
  );

  /**
   * Get notifications for a user
   */
  public getNotifications = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { unread_only } = req.query;

    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const notifications = await this.discussionRepo.getNotifications(
      userId,
      unread_only === 'true',
    );

    return sendResponse(res, 'DISCUSSION_NOTIFICATIONS_FETCHED', {
      data: { notifications },
    });
  });

  /**
   * Mark notifications as read
   */
  public markNotificationsAsRead = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;
      const { notification_ids } = req.body;

      if (!userId) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      await this.discussionRepo.markNotificationsAsRead(
        userId,
        notification_ids || [],
      );

      return sendResponse(res, 'NOTIFICATIONS_MARKED_AS_READ');
    },
  );

  /**
   * Get discussion statistics
   */
  public getDiscussionStats = catchAsync(
    async (req: Request, res: Response) => {
      const stats = await this.discussionRepo.getDiscussionStats();

      return sendResponse(res, 'DISCUSSION_STATS_FETCHED', {
        data: { stats },
      });
    },
  );
}
