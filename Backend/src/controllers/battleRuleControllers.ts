import { Request, Response } from 'express';

import { RuleCategory } from '@prisma/client';

import prisma from '@/lib/prisma';
import { sendResponse } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import { catchAsync } from '../utils';

export default class BattleRuleController {
  /**
   * Get all battle rules with optional filtering
   * @route GET /api/battle-rules
   * @access Admin/Moderator
   */
  public getBattleRules = catchAsync(async (req: Request, res: Response) => {
    const { category, is_active, is_default } = req.query;

    const filters: any = {};

    if (category) {
      filters.category = category as RuleCategory;
    }

    if (is_active !== undefined) {
      filters.is_active = is_active === 'true';
    }

    if (is_default !== undefined) {
      filters.is_default = is_default === 'true';
    }

    const rules = await prisma.battleRule.findMany({
      where: filters,
      orderBy: {
        created_at: 'desc',
      },
    });

    return sendResponse(res, 'BATTLE_RULES_FETCHED', {
      data: rules,
    });
  });

  /**
   * Get a single battle rule by ID
   * @route GET /api/battle-rules/:id
   * @access Admin/Moderator
   */
  public getBattleRule = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    const rule = await prisma.battleRule.findUnique({
      where: { id },
      include: {
        battles: {
          include: {
            battle: {
              select: {
                id: true,
                title: true,
                status: true,
              },
            },
          },
        },
      },
    });

    if (!rule) {
      throw createAppError('Battle rule not found', 404);
    }

    return sendResponse(res, 'BATTLE_RULE_FETCHED', {
      data: rule,
    });
  });

  /**
   * Create a new battle rule
   * @route POST /api/battle-rules
   * @access Admin/Moderator
   */
  public createBattleRule = catchAsync(async (req: Request, res: Response) => {
    const { name, description, category, is_active, is_default } = req.body;

    // Check if rule with the same name already exists
    const existingRule = await prisma.battleRule.findUnique({
      where: { name },
    });

    if (existingRule) {
      throw createAppError('A rule with this name already exists', 400);
    }

    const rule = await prisma.battleRule.create({
      data: {
        name,
        description,
        category: category as RuleCategory,
        is_active: is_active !== undefined ? is_active : true,
        is_default: is_default !== undefined ? is_default : false,
      },
    });

    logger.info(`Battle rule created: ${rule.id}`);
    return sendResponse(res, 'BATTLE_RULE_CREATED', {
      data: rule,
    });
  });

  /**
   * Update an existing battle rule
   * @route PUT /api/battle-rules/:id
   * @access Admin/Moderator
   */
  public updateBattleRule = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { name, description, category, is_active, is_default } = req.body;

    // Check if rule exists
    const existingRule = await prisma.battleRule.findUnique({
      where: { id },
    });

    if (!existingRule) {
      throw createAppError('Battle rule not found', 404);
    }

    // Check if another rule with the same name exists
    if (name && name !== existingRule.name) {
      const duplicateRule = await prisma.battleRule.findUnique({
        where: { name },
      });

      if (duplicateRule) {
        throw createAppError('A rule with this name already exists', 400);
      }
    }

    const updatedRule = await prisma.battleRule.update({
      where: { id },
      data: {
        name: name !== undefined ? name : undefined,
        description: description !== undefined ? description : undefined,
        category:
          category !== undefined ? (category as RuleCategory) : undefined,
        is_active: is_active !== undefined ? is_active : undefined,
        is_default: is_default !== undefined ? is_default : undefined,
      },
    });

    logger.info(`Battle rule updated: ${id}`);
    return sendResponse(res, 'BATTLE_RULE_UPDATED', {
      data: updatedRule,
    });
  });

  /**
   * Delete a battle rule
   * @route DELETE /api/battle-rules/:id
   * @access Admin/Moderator
   */
  public deleteBattleRule = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    // Check if rule exists
    const existingRule = await prisma.battleRule.findUnique({
      where: { id },
      include: {
        battles: true,
      },
    });

    if (!existingRule) {
      throw createAppError('Battle rule not found', 404);
    }

    // Check if rule is associated with any battles
    if (existingRule.battles.length > 0) {
      throw createAppError(
        'Cannot delete rule that is associated with battles',
        400,
      );
    }

    await prisma.battleRule.delete({
      where: { id },
    });

    logger.info(`Battle rule deleted: ${id}`);
    return sendResponse(res, 'BATTLE_RULE_DELETED', {
      data: { id },
    });
  });

  /**
   * Associate rules with a battle
   * @route POST /api/battle-rules/associate
   * @access Admin/Moderator
   */
  public associateRulesWithBattle = catchAsync(
    async (req: Request, res: Response) => {
      const { battle_id, rule_ids } = req.body;

      // Check if battle exists
      const battle = await prisma.battle.findUnique({
        where: { id: battle_id },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Check if all rules exist
      const rules = await prisma.battleRule.findMany({
        where: {
          id: {
            in: rule_ids,
          },
        },
      });

      if (rules.length !== rule_ids.length) {
        throw createAppError('One or more rules not found', 404);
      }

      // Delete existing associations
      await prisma.battleToRule.deleteMany({
        where: {
          battle_id,
        },
      });

      // Create new associations
      const associations = await Promise.all(
        rule_ids.map(async (ruleId: string) => {
          return prisma.battleToRule.create({
            data: {
              battle_id,
              rule_id: ruleId,
            },
          });
        }),
      );

      logger.info(
        `Associated ${associations.length} rules with battle ${battle_id}`,
      );
      return sendResponse(res, 'BATTLE_RULES_ASSOCIATED', {
        data: { battle_id, rule_count: associations.length },
      });
    },
  );

  /**
   * Get rules for a specific battle
   * @route GET /api/battle-rules/battle/:id
   * @access Public
   */
  public getBattleRulesByBattleId = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;

      // Check if battle exists
      const battle = await prisma.battle.findUnique({
        where: { id },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Get rules associated with the battle
      const battleRules = await prisma.battleToRule.findMany({
        where: {
          battle_id: id,
        },
        include: {
          rule: true,
        },
      });

      // If no specific rules are associated, get default rules
      let rules = battleRules.map((br) => br.rule);

      if (rules.length === 0) {
        rules = await prisma.battleRule.findMany({
          where: {
            is_default: true,
            is_active: true,
          },
        });
      }

      return sendResponse(res, 'BATTLE_RULES_FETCHED', {
        data: rules,
      });
    },
  );
}
