import { Request, Response } from 'express';
import fs from 'fs';
import path from 'path';

import { ChallengeStatus, PrismaClient } from '@prisma/client';

import prisma from '@/lib/prisma';
import AnalyticsRepository from '@/repositories/analyticsRepository';
import ChallengeRepository from '@/repositories/challengeRepository';
import ResourceRepository from '@/repositories/resourceRepository';
import UserProgressRepository from '@/repositories/userProgressRepository';
import UserRepository from '@/repositories/userRepository';
import { createAppError } from '@/utils/errorHandler';

// Available metrics and dimensions for analytics reports
import {
  AVAILABLE_DIMENSIONS,
  AVAILABLE_METRICS,
} from '../constants/analyticsConstants';
import { sendResponse } from '../utils/apiResponse';
import { catchAsync } from '../utils/catchAsync';
import logger from '../utils/logger';

// Define the report interface
interface IReport {
  id: string;
  name: string;
  description?: string;
  metrics: string[];
  dimensions: string[];
  visualizations?: {
    type: 'bar' | 'line' | 'pie' | 'table';
    title: string;
    metrics: string[];
    dimensions?: string[];
  }[];
  scheduleEnabled?: boolean;
  scheduleFrequency?: 'daily' | 'weekly' | 'monthly';
  scheduleRecipients?: string[];
  scheduleExportFormat?: 'pdf' | 'csv' | 'excel';
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export default class AnalyticsController {
  private readonly defaultStartDate: Date;
  private readonly userRepo: UserRepository;
  private readonly userProgressRepo: UserProgressRepository;
  private readonly resourceRepo: ResourceRepository;
  private readonly challengeRepo: ChallengeRepository;
  private readonly analyticsRepo: AnalyticsRepository;
  private dataRetentionSetting: string = '90days'; // Default retention period

  constructor() {
    this.defaultStartDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    this.userRepo = new UserRepository();
    this.userProgressRepo = new UserProgressRepository();
    this.resourceRepo = new ResourceRepository();

    this.challengeRepo = new ChallengeRepository();
    this.analyticsRepo = new AnalyticsRepository();
  }

  public getUserAnalytics = catchAsync(async (req: Request, res: Response) => {
    const { userId } = req.params;
    const analytics = await this.userProgressRepo.getUserAnalytics(userId);
    sendResponse(res, 'USER_ANALYTICS_FETCHED', { data: analytics });
  });

  public getCurrentUserAnalytics = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('User not found', 404);
      }
      const analytics = await this.userProgressRepo.getUserAnalytics(userId);
      sendResponse(res, 'USER_ANALYTICS_FETCHED', { data: analytics });
    },
  );

  public getPlatformAnalytics = catchAsync(
    async (req: Request, res: Response) => {
      logger.info('[AnalyticsController] Attempting to get platform analytics');
      const startDate = req.query.startDate
        ? new Date(req.query.startDate as string)
        : this.defaultStartDate;
      const endDate = req.query.endDate
        ? new Date(req.query.endDate as string)
        : new Date();

      const [
        userGrowth,
        contentEngagement,
        challengeCompletion,
        resourceUsage,
      ] = await Promise.all([
        this.userRepo.groupBy({
          by: ['created_at'],
          where: {
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          },
          _count: true,
          orderBy: {
            created_at: 'asc',
          },
        }),
        this.resourceRepo.groupBy({
          by: ['type'],
          where: {
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          },
          _count: true,
          _avg: {
            rating: true,
          },
          orderBy: {
            type: 'asc',
          },
        }),
        this.challengeRepo.groupBy({
          by: ['status'],
          where: {
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          },
          _count: true,
          orderBy: {
            status: 'asc',
          },
        }),
        this.resourceRepo.groupBy({
          by: ['type'],
          where: {
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          },
          _count: true,
          orderBy: {
            type: 'asc',
          },
        }),
      ]);

      sendResponse(res, 'PLATFORM_ANALYTICS_FETCHED', {
        data: {
          userGrowth,
          contentEngagement,
          challengeCompletion,
          resourceUsage,
        },
      });
    },
  );

  // Get all saved reports
  public getReports = catchAsync(async (req: Request, res: Response) => {
    try {
      // Get user ID from the authenticated user
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('User not authenticated', 401);
      }

      // Fetch reports from the database using repository
      const reports = await this.analyticsRepo.getReports(userId);

      // Transform the data to match our interface
      const formattedReports = reports.map((report) => ({
        id: report.id,
        name: report.name,
        description: report.description || '',
        metrics: report.metrics as string[],
        dimensions: report.dimensions as string[],
        visualizations: report.visualizations as {
          type: 'bar' | 'line' | 'pie' | 'table';
          title: string;
          metrics: string[];
          dimensions?: string[];
        }[],
        scheduleEnabled: report.schedule_enabled,
        scheduleFrequency: report.schedule_frequency,
        scheduleRecipients: report.schedule_recipients as string[],
        scheduleExportFormat: report.schedule_export_format,
        createdAt: report.created_at,
        updatedAt: report.updated_at,
        userId: report.user_id,
      }));

      sendResponse(res, 'REPORTS_FETCHED', { data: formattedReports });
    } catch (error) {
      if (error instanceof Error) {
        throw createAppError(error.message, 500);
      }
      throw createAppError('Failed to fetch reports', 500);
    }
  });

  // Create a new report
  public createReport = catchAsync(async (req: Request, res: Response) => {
    try {
      // Get user ID from the authenticated user
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('User not authenticated', 401);
      }

      const {
        name,
        description,
        metrics,
        dimensions,
        visualizations,
        scheduleEnabled,
        scheduleFrequency,
        scheduleRecipients,
        scheduleExportFormat,
      } = req.body;

      // Create the report in the database using repository
      const report = await this.analyticsRepo.createReport({
        name,
        description: description || '',
        metrics,
        dimensions,
        visualizations: visualizations || [],
        schedule_enabled: scheduleEnabled || false,
        schedule_frequency: scheduleFrequency || 'weekly',
        schedule_recipients: scheduleRecipients || [],
        schedule_export_format: scheduleExportFormat || 'pdf',
        user_id: userId,
      });

      // Format the response
      const formattedReport = {
        id: report.id,
        name: report.name,
        description: report.description || '',
        metrics: report.metrics as string[],
        dimensions: report.dimensions as string[],
        visualizations: report.visualizations as {
          type: 'bar' | 'line' | 'pie' | 'table';
          title: string;
          metrics: string[];
          dimensions?: string[];
        }[],
        scheduleEnabled: report.schedule_enabled,
        scheduleFrequency: report.schedule_frequency,
        scheduleRecipients: report.schedule_recipients as string[],
        scheduleExportFormat: report.schedule_export_format,
        createdAt: report.created_at,
        updatedAt: report.updated_at,
        userId: report.user_id,
      };

      sendResponse(res, 'REPORT_CREATED', { data: formattedReport });
    } catch (error) {
      if (error instanceof Error) {
        throw createAppError(error.message, 500);
      }
      throw createAppError('Failed to create report', 500);
    }
  });

  // Update an existing report
  public updateReport = catchAsync(async (req: Request, res: Response) => {
    try {
      // Get user ID from the authenticated user
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('User not authenticated', 401);
      }

      const { id } = req.params;

      // Check if the report exists and belongs to the user using repository
      const existingReport = await this.analyticsRepo.getReportById(id, userId);

      if (!existingReport) {
        throw createAppError('Report not found or access denied', 404);
      }

      const {
        name,
        description,
        metrics,
        dimensions,
        visualizations,
        scheduleEnabled,
        scheduleFrequency,
        scheduleRecipients,
        scheduleExportFormat,
      } = req.body;

      // Update the report using repository
      const report = await this.analyticsRepo.updateReport(id, {
        name: name !== undefined ? name : existingReport.name,
        description:
          description !== undefined ? description : existingReport.description,
        metrics: metrics !== undefined ? metrics : existingReport.metrics,
        dimensions:
          dimensions !== undefined ? dimensions : existingReport.dimensions,
        visualizations:
          visualizations !== undefined
            ? visualizations
            : existingReport.visualizations,
        schedule_enabled:
          scheduleEnabled !== undefined
            ? scheduleEnabled
            : existingReport.schedule_enabled,
        schedule_frequency:
          scheduleFrequency !== undefined
            ? scheduleFrequency
            : existingReport.schedule_frequency,
        schedule_recipients:
          scheduleRecipients !== undefined
            ? scheduleRecipients
            : existingReport.schedule_recipients,
        schedule_export_format:
          scheduleExportFormat !== undefined
            ? scheduleExportFormat
            : existingReport.schedule_export_format,
      });

      // Format the response
      const formattedReport = {
        id: report.id,
        name: report.name,
        description: report.description || '',
        metrics: report.metrics as string[],
        dimensions: report.dimensions as string[],
        visualizations: report.visualizations as {
          type: 'bar' | 'line' | 'pie' | 'table';
          title: string;
          metrics: string[];
          dimensions?: string[];
        }[],
        scheduleEnabled: report.schedule_enabled,
        scheduleFrequency: report.schedule_frequency,
        scheduleRecipients: report.schedule_recipients as string[],
        scheduleExportFormat: report.schedule_export_format,
        createdAt: report.created_at,
        updatedAt: report.updated_at,
        userId: report.user_id,
      };

      sendResponse(res, 'REPORT_UPDATED', { data: formattedReport });
    } catch (error) {
      if (error instanceof Error) {
        throw createAppError(error.message, 500);
      }
      throw createAppError('Failed to update report', 500);
    }
  });

  // Delete a report
  public deleteReport = catchAsync(async (req: Request, res: Response) => {
    try {
      // Get user ID from the authenticated user
      const userId = req.user?.id;

      if (!userId) {
        throw createAppError('User not authenticated', 401);
      }

      const { id } = req.params;

      // Check if the report exists and belongs to the user using repository
      const existingReport = await this.analyticsRepo.getReportById(id, userId);

      if (!existingReport) {
        throw createAppError('Report not found or access denied', 404);
      }

      // Delete the report using repository
      await this.analyticsRepo.deleteReport(id);

      sendResponse(res, 'REPORT_DELETED', {
        data: { id },
      });
    } catch (error) {
      if (error instanceof Error) {
        throw createAppError(error.message, 500);
      }
      throw createAppError('Failed to delete report', 500);
    }
  });

  // Generate a report by ID
  public generateReportById = catchAsync(
    async (req: Request, res: Response) => {
      try {
        // Get user ID from the authenticated user
        const userId = req.user?.id;

        if (!userId) {
          throw createAppError('User not authenticated', 401);
        }

        const { id, format } = req.params;

        // Special case for 'current' - generate a report without saving
        if (id === 'current') {
          // Get the current report data from the session or request body
          // This is a placeholder - in a real implementation, you'd get the current report data
          const reportData = {
            // Sample data for demonstration
            metrics: ['total_users', 'active_users'],
            dimensions: ['date'],
            // Generate sample data based on metrics and dimensions
            data: [
              { date: '2023-01-01', total_users: 100, active_users: 80 },
              { date: '2023-01-02', total_users: 110, active_users: 85 },
              { date: '2023-01-03', total_users: 120, active_users: 90 },
            ],
          };

          // Generate the report in the requested format
          return this.generateReportFile(
            reportData,
            format as 'pdf' | 'csv' | 'excel',
            res,
          );
        }

        // Check if the report exists and belongs to the user using repository
        const report = await this.analyticsRepo.getReportById(id, userId);

        if (!report) {
          throw createAppError('Report not found or access denied', 404);
        }

        // Get the report data based on the metrics and dimensions
        const reportData = await this.getReportData(
          report.metrics as string[],
          report.dimensions as string[],
        );

        // Generate the report in the requested format
        return this.generateReportFile(
          reportData,
          format as 'pdf' | 'csv' | 'excel',
          res,
        );
      } catch (error) {
        if (error instanceof Error) {
          throw createAppError(error.message, 500);
        }
        throw createAppError('Failed to generate report', 500);
      }
    },
  );

  // Helper method to get report data based on metrics and dimensions
  private async getReportData(metrics: string[], dimensions: string[]) {
    // This is a placeholder - in a real implementation, you'd query the database
    // based on the metrics and dimensions to get the actual data

    // Sample data for demonstration
    return {
      metrics,
      dimensions,
      // Generate sample data based on metrics and dimensions
      data: [
        { date: '2023-01-01', total_users: 100, active_users: 80 },
        { date: '2023-01-02', total_users: 110, active_users: 85 },
        { date: '2023-01-03', total_users: 120, active_users: 90 },
      ],
    };
  }

  // Helper method to generate a report file in the requested format
  private generateReportFile(
    reportData: any,
    format: 'pdf' | 'csv' | 'excel',
    res: Response,
  ) {
    // This is a placeholder - in a real implementation, you'd generate the actual file

    // For demonstration purposes, we'll just return the data
    switch (format) {
      case 'pdf':
        // In a real implementation, you'd generate a PDF file
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'attachment; filename=report.pdf');
        // For now, just return the data as JSON
        return sendResponse(res, 'REPORT_GENERATED', {
          data: reportData,
          meta: { format: 'pdf' },
        });

      case 'csv':
        // In a real implementation, you'd generate a CSV file
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=report.csv');
        // For now, just return the data as JSON
        return sendResponse(res, 'REPORT_GENERATED', {
          data: reportData,
          meta: { format: 'csv' },
        });

      case 'excel':
        // In a real implementation, you'd generate an Excel file
        res.setHeader(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        );
        res.setHeader(
          'Content-Disposition',
          'attachment; filename=report.xlsx',
        );
        // For now, just return the data as JSON
        return sendResponse(res, 'REPORT_GENERATED', {
          data: reportData,
          meta: { format: 'excel' },
        });

      default:
        throw createAppError('Invalid format', 400);
    }
  }

  // Get available metrics
  public getMetrics = catchAsync(async (req: Request, res: Response) => {
    try {
      sendResponse(res, 'METRICS_FETCHED', {
        data: { metrics: AVAILABLE_METRICS },
      });
    } catch (error) {
      if (error instanceof Error) {
        throw createAppError(error.message, 500);
      }
      throw createAppError('Failed to fetch metrics', 500);
    }
  });

  // Get available dimensions
  public getDimensions = catchAsync(async (req: Request, res: Response) => {
    try {
      sendResponse(res, 'DIMENSIONS_FETCHED', {
        data: { dimensions: AVAILABLE_DIMENSIONS },
      });
    } catch (error) {
      if (error instanceof Error) {
        throw createAppError(error.message, 500);
      }
      throw createAppError('Failed to fetch dimensions', 500);
    }
  });

  // Update data retention period
  public updateRetentionPeriod = catchAsync(
    async (req: Request, res: Response) => {
      try {
        const { period } = req.body;

        // In a real implementation, you'd store this in the database
        // Update the data retention setting in the database
        if (!req.user?.id) {
          throw createAppError('User not authenticated', 401);
        }

        await this.analyticsRepo.upsertSettings(req.user.id, {
          data_retention_period: period,
        });

        // Also update the instance variable
        this.dataRetentionSetting = period;

        sendResponse(res, 'CONFIG_UPDATED', {
          data: { period },
        });
      } catch (error) {
        if (error instanceof Error) {
          throw createAppError(error.message, 500);
        }
        throw createAppError('Failed to update retention period', 500);
      }
    },
  );

  // Original generateReport method
  public generateReport = catchAsync(async (req: Request, res: Response) => {
    const { reportType } = req.params;
    const filters = {
      startDate: req.query.startDate
        ? new Date(req.query.startDate as string)
        : this.defaultStartDate,
      endDate: req.query.endDate
        ? new Date(req.query.endDate as string)
        : new Date(),
      userId: req.query.userId as string,
      type: req.query.type as string,
      status: (req.query.status as string)?.toUpperCase() as ChallengeStatus,
    };

    let reportData;

    switch (reportType) {
      case 'user_activity':
        // TODO: update this method if required in future
        reportData = await prisma.userActivityLog.findMany({
          where: {
            timestamp: {
              gte: filters.startDate,
              lte: filters.endDate,
            },
            user_id: filters.userId,
          },
          include: {
            user: {
              select: {
                username: true,
                email: true,
              },
            },
          },
        });
        break;

      case 'resource_usage':
        reportData = await this.resourceRepo.findMany({
          where: {
            created_at: {
              gte: filters.startDate,
              lte: filters.endDate,
            },
            type: filters.type,
            user_id: filters.userId,
          },
          include: {
            user: {
              select: {
                username: true,
                email: true,
              },
            },
          },
        });
        break;

      case 'challenge_submissions':
        reportData = await this.challengeRepo.findMany({
          where: {
            created_at: {
              gte: filters.startDate,
              lte: filters.endDate,
            },
            status: filters.status,
          },
        });
        break;

      default:
        throw createAppError('Invalid report type', 400);
    }
    sendResponse(res, 'REPORT_GENERATED', { data: reportData });
  });
}
