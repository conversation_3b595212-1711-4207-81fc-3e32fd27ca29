import { Request, Response } from 'express';

import { sendResponse } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';

import { catchAsync } from '../utils';
import { CodeExecutionResult, executeCode } from '../utils/codeExecutor';
import logger from '../utils/logger';
import { supportedLanguages } from '../validations/codeRunnerValidation';

export default class CodeRunnerController {
  /**
   * Execute code with the provided input
   */
  executeCode = catchAsync(async (req: Request, res: Response) => {
    const { code, language, input, timeLimit, memoryLimit } = req.body;

    logger.info(`Executing ${language} code`);

    try {
      const result = await executeCode({
        code,
        language,
        input,
        timeLimit,
        memoryLimit,
      });

      if (result.error) {
        throw createAppError(result.error, 400);
      }

      sendResponse(res, 'CODE_EXECUTED', {
        data: result,
      });
    } catch (error) {
      logger.error('Error executing code:', error);
      throw createAppError('Failed to execute code', 500);
    }
  });

  /**
   * Test code against multiple test cases
   */
  testCode = catchAsync(async (req: Request, res: Response) => {
    const { code, language, testCases, timeLimit, memoryLimit } = req.body;

    if (!testCases || !Array.isArray(testCases) || testCases.length === 0) {
      throw createAppError('Test cases are required', 400);
    }

    logger.info(`Testing ${language} code with ${testCases.length} test cases`);

    try {
      const results = await Promise.all(
        testCases.map(async (testCase, index) => {
          const { input, expectedOutput } = testCase;

          const result = await executeCode({
            code,
            language,
            input,
            timeLimit,
            memoryLimit,
          });

          // Normalize output by trimming whitespace and newlines
          const normalizedOutput = result.output.trim();
          const normalizedExpectedOutput = expectedOutput.trim();

          const passed = normalizedOutput === normalizedExpectedOutput;

          return {
            testCase: index + 1,
            input,
            expectedOutput,
            actualOutput: result.output,
            executionTime: result.executionTime,
            memoryUsed: result.memoryUsed,
            passed,
            error: result.error,
            status: result.status,
          };
        }),
      );

      // Calculate summary
      const totalTests = results.length;
      const passedTests = results.filter((r) => r.passed).length;
      const failedTests = totalTests - passedTests;

      sendResponse(res, 'CODE_TESTED', {
        data: {
          results,
          summary: {
            totalTests,
            passedTests,
            failedTests,
            success: passedTests === totalTests,
          },
        },
      });
    } catch (error) {
      logger.error('Error testing code:', error);
      throw createAppError('Failed to test code', 500);
    }
  });

  /**
   * Get supported languages
   */
  getSupportedLanguages = catchAsync(async (req: Request, res: Response) => {
    sendResponse(res, 'LANGUAGES_FETCHED', {
      data: supportedLanguages,
    });
  });
}
