import { Request, Response } from 'express';

import { RoleType } from '@prisma/client';

import RoleRepository from '@/repositories/roleRepository';
import UserRepository from '@/repositories/userRepository';
import { catchAsync } from '@/utils';
import { sendResponse } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';
import { updateUserRolesInSupabase } from '@/utils/supabaseAdmin';

export default class RoleController {
  private readonly roleRepo: RoleRepository;
  private readonly userRepo: UserRepository;

  constructor() {
    this.roleRepo = new RoleRepository();
    this.userRepo = new UserRepository();
  }

  /**
   * Get all roles
   * @route GET /api/roles
   * @access Admin
   */
  public getAllRoles = catchAsync(async (req: Request, res: Response) => {
    const roles = await this.roleRepo.getAllRoles();
    sendResponse(res, 'ROLES_FETCHED', { data: roles });
  });

  /**
   * Get a single role by ID
   * @route GET /api/roles/:id
   * @access Admin
   */
  public getRoleById = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const role = await this.roleRepo.getRoleById(id);

    if (!role) {
      return sendResponse(res, 'ROLE_NOT_FOUND');
    }

    sendResponse(res, 'ROLE_FETCHED', { data: role });
  });

  /**
   * Create a new role
   * @route POST /api/roles
   * @access Admin
   */
  public createRole = catchAsync(async (req: Request, res: Response) => {
    const { name, description, type } = req.body;

    const role = await this.roleRepo.createRole({
      name,
      description,
      type: type as RoleType,
    });

    sendResponse(res, 'ROLE_CREATED', { data: role });
  });

  /**
   * Update a role
   * @route PUT /api/roles/:id
   * @access Admin
   */
  public updateRole = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { name, description, type } = req.body;

    const role = await this.roleRepo.updateRole(id, {
      name,
      description,
      type: type as RoleType,
    });

    sendResponse(res, 'ROLE_UPDATED', { data: role });
  });

  /**
   * Delete a role
   * @route DELETE /api/roles/:id
   * @access Admin
   */
  public deleteRole = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    const role = await this.roleRepo.deleteRole(id);

    sendResponse(res, 'ROLE_DELETED', { data: role });
  });

  /**
   * Assign a role to a user
   * @route POST /api/roles/:roleId/users/:userId
   * @access Admin
   */
  public assignRoleToUser = catchAsync(async (req: Request, res: Response) => {
    const { roleId, userId } = req.params;

    // Get the user to check if they exist and to get their Supabase ID
    const user = await this.userRepo.findUnique({
      where: { id: userId },
      select: { id: true, supabase_id: true },
    });

    if (!user) {
      throw createAppError('User not found', 404);
    }

    // Get the role to check if it exists and to get its type
    const role = await this.roleRepo.findUnique({
      where: { id: roleId },
      select: { id: true, type: true },
    });

    if (!role) {
      throw createAppError('Role not found', 404);
    }

    // Assign the role to the user in our database
    const userRole = await this.roleRepo.assignRoleToUser(roleId, userId);

    // Get all roles for this user after assignment
    const userRoles = await this.roleRepo.getRolesByUser(userId);
    const roleTypes = userRoles.map((role) => role.type);

    // Update the user's roles in Supabase
    if (user.supabase_id) {
      try {
        await updateUserRolesInSupabase(user.supabase_id, roleTypes);
        logger.info(`Updated Supabase roles for user ${userId}`);
      } catch (error) {
        logger.error('Failed to update Supabase roles:', error);
        // Don't fail the request if Supabase update fails
      }
    }

    sendResponse(res, 'ROLE_ASSIGNED', { data: userRole });
  });

  /**
   * Remove a role from a user
   * @route DELETE /api/roles/:roleId/users/:userId
   * @access Admin
   */
  public removeRoleFromUser = catchAsync(
    async (req: Request, res: Response) => {
      const { roleId, userId } = req.params;

      // Get the user to check if they exist and to get their Supabase ID
      const user = await this.userRepo.findUnique({
        where: { id: userId },
        select: { id: true, supabase_id: true },
      });

      if (!user) {
        throw createAppError('User not found', 404);
      }

      // Remove the role from the user in our database
      const userRole = await this.roleRepo.removeRoleFromUser(roleId, userId);

      // Get all roles for this user after removal
      const userRoles = await this.roleRepo.getRolesByUser(userId);
      const roleTypes = userRoles.map((role) => role.type);

      // Update the user's roles in Supabase
      if (user.supabase_id) {
        try {
          await updateUserRolesInSupabase(user.supabase_id, roleTypes);
          logger.info(
            `Updated Supabase roles for user ${userId} after role removal`,
          );
        } catch (error) {
          logger.error('Failed to update Supabase roles after removal:', error);
          // Don't fail the request if Supabase update fails
        }
      }

      sendResponse(res, 'ROLE_REMOVED', { data: userRole });
    },
  );

  /**
   * Get users by role
   * @route GET /api/roles/:roleId/users
   * @access Admin
   */
  public getUsersByRole = catchAsync(async (req: Request, res: Response) => {
    const { roleId } = req.params;

    const users = await this.roleRepo.getUsersByRole(roleId);

    sendResponse(res, 'USERS_FETCHED', { data: users });
  });

  /**
   * Get roles by user
   * @route GET /api/users/:userId/roles
   * @access Admin
   */
  public getRolesByUser = catchAsync(async (req: Request, res: Response) => {
    const { userId: supabaseId } = req.params; // userId from params is the supabase_id

    // Find the user in the local database by their supabase_id
    const user = await this.userRepo.findUnique({
      where: { supabase_id: supabaseId },
      select: { id: true }, // We only need the Prisma User ID (internal DB ID)
    });

    if (!user) {
      // If user is not found in local DB by supabase_id, return empty roles.
      // This could happen if user exists in Supabase Auth but not yet synced to local DB,
      // or if an invalid ID is passed.
      logger.warn(
        `User with supabase_id ${supabaseId} not found in local database when fetching roles.`,
      );
      return sendResponse(res, 'ROLES_FETCHED', { data: [] });
    }

    // Use the obtained internal DB User ID (user.id) to get roles
    const roles = await this.roleRepo.getRolesByUser(user.id);

    sendResponse(res, 'ROLES_FETCHED', { data: roles });
  });
}
