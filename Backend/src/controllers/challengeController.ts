import { Request, Response } from 'express';

import {
  ChallengeCategory,
  Difficulty,
  SubmissionStatus,
} from '@prisma/client';

import ChallengeRepository from '../repositories/challengeRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class ChallengeController {
  private readonly challengeRepo: ChallengeRepository;

  constructor() {
    this.challengeRepo = new ChallengeRepository();
  }

  public getChallenges = catchAsync(async (req: Request, res: Response) => {
    const {
      page = 1,
      limit = 10,
      search = '',
      difficulty,
      category,
      tags,
      status,
      sort,
    } = req.query;

    // Build where clause for filtering
    const whereClause: Record<string, any> = {
      // Only show active challenges by default
      status: 'ACTIVE',
    };

    // Add difficulty filter if provided
    if (difficulty && difficulty !== 'all') {
      whereClause.difficulty = String(difficulty).toUpperCase();
    }

    // Add category filter if provided
    if (category && category !== 'all') {
      // Convert category to match the ChallengeCategory enum values
      whereClause.category = String(category);
    }

    // Parse tags if provided
    let parsedTags: string[] | undefined;
    if (tags) {
      try {
        if (typeof tags === 'string') {
          parsedTags = tags.split(',').map((tag) => tag.trim());
        } else if (Array.isArray(tags)) {
          parsedTags = tags.map((tag) => String(tag).trim());
        }
      } catch (error) {
        console.error('Error parsing tags:', error);
      }
    }

    // Get challenges with filters
    const result = await this.challengeRepo.paginate(
      {
        page: Number(page),
        limit: Number(limit),
        search: String(search),
        filter: {
          ...whereClause,
          ...(parsedTags ? { tags: { hasEvery: parsedTags } } : {}),
        },
        sort: sort
          ? {
              field: this.getSortField(String(sort)),
              direction: this.getSortDirection(String(sort)),
            }
          : undefined,
      },
      ['title', 'description'],
      {
        include: {
          topic: true,
          _count: {
            select: {
              submissions: { where: { status: 'ACCEPTED' } },
              bookmarks: true,
            },
          },
          ...(req.user
            ? {
                bookmarks: {
                  where: { user_id: req.user.id },
                  select: { id: true },
                },
                submissions: {
                  where: { user_id: req.user.id },
                  orderBy: { created_at: 'desc' },
                  take: 1,
                },
              }
            : {}),
        },
      },
    );

    // Transform the data to include user-specific information
    const transformedData = result.data
      .map((challenge) => {
        // Determine challenge status for the user
        let challengeStatus = 'not_started';
        if (
          req.user &&
          'submissions' in challenge &&
          challenge.submissions &&
          Array.isArray(challenge.submissions) &&
          challenge.submissions.length > 0
        ) {
          challengeStatus =
            challenge.submissions[0].status === 'ACCEPTED'
              ? 'completed'
              : 'in_progress';
        }

        // Filter by status if requested
        if (status && challengeStatus !== status) {
          return null; // Will be filtered out below
        }

        return {
          ...challenge,
          isBookmarked:
            req.user &&
            'bookmarks' in challenge &&
            Array.isArray(challenge.bookmarks)
              ? challenge.bookmarks.length > 0
              : false,
          status: challengeStatus,
          // Remove the bookmarks and submissions arrays from the response
          bookmarks: undefined,
          submissions: undefined,
        };
      })
      .filter(Boolean); // Remove null entries (filtered by status)

    // Update the meta information if we filtered by status
    const meta = { ...result.meta };
    if (status) {
      meta.total = transformedData.length;
      meta.totalPages = Math.ceil(meta.total / Number(limit));
      meta.hasNextPage = meta.currentPage < meta.totalPages;
      meta.hasPreviousPage = meta.currentPage > 1;
    }

    return sendResponse(res, 'CHALLENGES_FETCHED', {
      data: { challenges: transformedData },
      meta,
    });
  });

  public getChallenge = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const challenge = await this.challengeRepo.findUnique({
      where: { id },
    });

    if (!challenge) {
      return sendResponse(res, 'CHALLENGE_NOT_FOUND');
    }

    return sendResponse(res, 'CHALLENGE_FETCHED', { data: { challenge } });
  });

  public createNewChallenge = catchAsync(
    async (req: Request, res: Response) => {
      const challenge = await this.challengeRepo.create(req.body);
      return sendResponse(res, 'CHALLENGE_CREATED', { data: { challenge } });
    },
  );

  public updateExistingChallenge = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;
      const challenge = await this.challengeRepo.update({
        where: { id },
        data: req.body,
      });
      return sendResponse(res, 'CHALLENGE_UPDATED', { data: { challenge } });
    },
  );

  public getChallengeStatistics = catchAsync(
    async (req: Request, res: Response) => {
      const stats = await this.challengeRepo.getChallengeStats();
      return sendResponse(res, 'CHALLENGE_FETCHED', { data: { stats } });
    },
  );

  /**
   * Get all challenges with filtering, sorting, and pagination
   */
  public getAllChallengesWithFilters = catchAsync(
    async (req: Request, res: Response) => {
      const { difficulty, category, tags } = req.query;
      const challenges = await this.challengeRepo.getAllChallenges({
        difficulty: difficulty as Difficulty,
        category: category as string,
        tags: tags ? (tags as string).split(',') : undefined,
      });

      return sendResponse(res, 'CHALLENGES_FETCHED', { data: { challenges } });
    },
  );

  public submitChallengeAttempt = catchAsync(
    async (req: Request, res: Response) => {
      const user_id = req.user?.id;
      if (!user_id) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      const { challenge_id } = req.params;
      const { code, language, quiz_id, answers, time_spent } = req.body;

      const submission = await this.challengeRepo.submitChallenge({
        code,
        language,
        user_id,
        challenge_id,
        quiz_id,
        answers,
        time_spent,
      });

      // If the submission was successful, update roadmap progress
      if (submission.status === SubmissionStatus.accepted) {
        try {
          // Import dynamically to avoid circular dependencies
          const RoadmapChallengeRepository =
            require('../repositories/roadmapChallengeRepository').default;
          const roadmapChallengeRepo = new RoadmapChallengeRepository();

          // Update roadmap progress asynchronously
          roadmapChallengeRepo
            .updateRoadmapProgress(user_id, challenge_id)
            .catch((error) =>
              console.error('Error updating roadmap progress:', error),
            );
        } catch (error) {
          console.error('Error importing roadmap challenge repository:', error);
        }
      }

      return sendResponse(res, 'CHALLENGE_SUBMITTED', { data: { submission } });
    },
  );

  public getChallengeLeaderboard = catchAsync(
    async (req: Request, res: Response) => {
      const { challengeId } = req.query;
      const leaderboard = await this.challengeRepo.getLeaderboard(
        challengeId as string,
      );
      return sendResponse(res, 'LEADERBOARD_FETCHED', {
        data: { leaderboard },
      });
    },
  );

  /**
   * Save a user's filter preset
   */
  public saveFilterPreset = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const { name, filters } = req.body;

    // Parse tags if they're a comma-separated string
    if (filters.tags && typeof filters.tags === 'string') {
      const parsedTags = filters.tags.split(',').map((tag) => tag.trim());
      filters.tags = parsedTags;
    } else if (Array.isArray(filters.tags)) {
      const parsedTags = filters.tags.map((tag) => String(tag).trim());
      filters.tags = parsedTags;
    }

    const preset = await this.challengeRepo.saveFilterPreset(
      userId,
      name,
      filters,
    );

    return sendResponse(res, 'FILTER_PRESET_SAVED', {
      data: { preset },
    });
  });

  /**
   * Get a user's filter presets
   */
  public getFilterPresets = catchAsync(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    if (!userId) {
      return sendResponse(res, 'USER_NOT_FOUND');
    }

    const presets = await this.challengeRepo.getFilterPresets(userId);

    return sendResponse(res, 'FILTER_PRESETS_FETCHED', {
      data: { presets },
    });
  });

  /**
   * Delete a user's filter preset
   */
  public deleteFilterPreset = catchAsync(
    async (req: Request, res: Response) => {
      const userId = req.user?.id;
      if (!userId) {
        return sendResponse(res, 'USER_NOT_FOUND');
      }

      const { presetId } = req.params;
      await this.challengeRepo.deleteFilterPreset(userId, presetId);

      return sendResponse(res, 'FILTER_PRESET_DELETED');
    },
  );

  /**
   * Helper method to get the sort field from the sort parameter
   */
  private getSortField(sort: string): string {
    switch (sort) {
      case 'newest':
        return 'created_at';
      case 'most_popular':
        return 'bookmarks';
      case 'highest_rated':
        return 'points';
      case 'most_submissions':
        return 'submissions';
      default:
        return 'created_at';
    }
  }

  /**
   * Helper method to get the sort direction from the sort parameter
   */
  private getSortDirection(sort: string): 'asc' | 'desc' {
    switch (sort) {
      case 'newest':
      case 'most_popular':
      case 'highest_rated':
      case 'most_submissions':
        return 'desc';
      default:
        return 'desc';
    }
  }
}
