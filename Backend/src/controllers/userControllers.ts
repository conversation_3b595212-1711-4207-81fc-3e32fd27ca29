import { logger } from '@sentry/node';
import { Request, Response } from 'express';

import { RoleType } from '@prisma/client';

import { supabase } from '@/config/supabase';
import RoleRepository from '@/repositories/roleRepository';
import UserRoadmapRepository from '@/repositories/userRoadmapRepository';
import { createAppError } from '@/utils/errorHandler';
import { updateUserRolesInSupabase } from '@/utils/supabaseAdmin';

import UserRepository from '../repositories/userRepository';
import { catchAsync, parse } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class UserController {
  private readonly userRepo: UserRepository;
  private readonly userRoadmapRepo: UserRoadmapRepository;
  private readonly roleRepo: RoleRepository;

  constructor() {
    this.userRepo = new UserRepository();
    this.userRoadmapRepo = new UserRoadmapRepository();
    this.roleRepo = new RoleRepository();
  }

  public getProfile = catchAsync(async (req: Request, res: Response) => {
    const token = req.headers.authorization?.split(' ')[1];
    const {
      data: { user },
    } = await supabase.auth.getUser(token);
    const userData = await this.userRepo.findUnique({
      where: { supabase_id: user?.id ?? '' },
    });

    if (!userData) {
      return sendResponse(res, 'USER_NOT_CREATED');
    }

    sendResponse(res, 'PROFILE_FETCHED', {
      data: { user: parse(user) },
    });
  });

  public getUserProgress = catchAsync(async (req: Request, res: Response) => {
    // TODO: Implement logic to fetch user progress

    sendResponse(res, 'PROGRESS_FETCHED', {
      data: { progress: 0 },
    });
  });

  public getUserRoadmap = catchAsync(async (req: Request, res: Response) => {
    const user_id = req.user?.id ?? '';
    const userRoadmap = await this.userRepo.findUnique({
      where: {
        id: user_id,
      },
      include: {
        user_roadmaps: {
          include: {
            roadmap: true,
          },
        },
      },
    });

    if (!userRoadmap) {
      throw createAppError('You are not enrolled in any roadmap', 404);
    }

    sendResponse(res, 'ROADMAP_FETCHED', {
      data: { userRoadmap },
    });
  });

  public insertUserRoadmap = catchAsync(async (req: Request, res: Response) => {
    const userRoadmap = await this.userRoadmapRepo.create({
      data: {
        user_id: req.user?.id ?? '',
        roadmap_id: req.body.roadmap_id,
      },
    });

    sendResponse(res, 'ROADMAP_ENROLLED', { data: userRoadmap });
  });

  public deleteUserRoadmap = catchAsync(async (req: Request, res: Response) => {
    const user_id = req.user?.id ?? '';
    const { id } = req.params;

    await this.userRoadmapRepo.delete({ where: { id, user_id } });

    sendResponse(res, 'ROADMAP_REMOVED');
  });

  public upsertUser = catchAsync(async (req: Request, res: Response) => {
    const token = req.headers.authorization?.split(' ')[1];

    if (!token) {
      throw createAppError('Unauthorized', 401);
    }

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);

    if (error || !user) {
      logger.warn('Invalid authentication attempt', { error });
      return createAppError('Invalid authentication', 401);
    }

    // First check if user exists by supabase_id
    let existingUser = await this.userRepo.findFirst({
      where: { supabase_id: user?.id ?? '' },
    });

    // If not found by supabase_id, check by email as a fallback
    if (!existingUser) {
      existingUser = await this.userRepo.findFirst({
        where: { email: user.email },
      });

      if (existingUser) {
        logger.info(
          `Found user by email: ${existingUser.id}, updating supabase_id`,
        );
        // Update the supabase_id to match the current auth user
        await this.userRepo.update({
          where: { id: existingUser.id },
          data: { supabase_id: user.id },
        });
      }
    }

    let userData;

    if (existingUser) {
      // Update the existing user
      userData = await this.userRepo.update({
        where: { id: existingUser.id },
        data: {
          ...req.body,
          // Don't update email as it comes from Supabase and should remain the same
          updated_at: new Date(),
        },
      });

      logger.info(`Updated existing user: ${existingUser.id}`);
    } else {
      // Create the user
      userData = await this.userRepo.create({
        data: {
          supabase_id: user?.id ?? '',
          ...req.body,
          email: user.email,
        },
      });

      try {
        // Find the default USER role
        const defaultRole = await this.roleRepo.findFirst({
          where: { type: RoleType.USER },
        });

        if (defaultRole) {
          // Assign the default USER role to the new user
          await this.roleRepo.assignRoleToUser(defaultRole.id, userData.id);
          logger.info(`Assigned default USER role to new user: ${userData.id}`);

          // Update the user's roles in Supabase
          try {
            await updateUserRolesInSupabase(user.id, [RoleType.USER]);
            logger.info(`Updated Supabase roles for new user ${userData.id}`);
          } catch (supabaseError) {
            logger.error(
              'Failed to update Supabase roles for new user:',
              supabaseError,
            );
            // Continue with the response even if Supabase update fails
          }
        } else {
          logger.warn(
            'Default USER role not found. Unable to assign role to new user.',
          );
        }
      } catch (roleError) {
        logger.error('Failed to assign default role to user', {
          error: roleError,
        });
        // Continue with the response even if role assignment fails
      }
    }

    // If this is an existing user, sync their roles with Supabase
    if (existingUser && user.id) {
      try {
        // Get all roles for this user
        const userRoles = await this.roleRepo.getRolesByUser(userData.id);
        const roleTypes = userRoles.map((role) => role.type);

        // Update the user's roles in Supabase
        await updateUserRolesInSupabase(user.id, roleTypes);
        logger.info(
          `Synced roles for existing user ${userData.id} with Supabase`,
        );
      } catch (error) {
        logger.error('Failed to sync user roles with Supabase:', error);
        // Continue with the response even if Supabase update fails
      }
    }

    sendResponse(res, 'USER_UPDATED', { data: { user: userData } });
  });

  public checkUsername = catchAsync(async (req: Request, res: Response) => {
    const { username } = req.query;

    if (!username || typeof username !== 'string') {
      throw createAppError('Invalid username', 400);
    }

    const isAvailable = await this.userRepo.findFirst({ where: { username } });

    sendResponse(res, 'USERNAME_AVAILABILITY_CHECKED', {
      data: { isAvailable: !isAvailable },
    });
  });
}
