import { Request, Response } from 'express';

import { createAppError } from '@/utils/errorHandler';

import metricsService from '../services/metricsService';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';

export default class MetricsController {
  /**
   * Get metrics for a challenge
   */
  public getChallengeMetrics = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;

      if (!id) {
        throw createAppError('Challenge ID is required', 400);
      }

      const metrics = await metricsService.getChallengeMetrics(id);

      return sendResponse(res, 'CHALLENGE_METRICS_FETCHED', {
        data: { metrics },
      });
    },
  );

  /**
   * Get metrics for a submission
   */
  public getSubmissionMetrics = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;

      if (!id) {
        throw createAppError('Submission ID is required', 400);
      }

      const metrics = await metricsService.getSubmissionMetrics(id);

      return sendResponse(res, 'SUBMISSION_METRICS_FETCHED', {
        data: { metrics },
      });
    },
  );

  /**
   * Generate optimization suggestions for a submission
   */
  public generateOptimizationSuggestions = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;

      if (!id) {
        throw createAppError('Submission ID is required', 400);
      }

      const suggestions =
        await metricsService.generateOptimizationSuggestions(id);

      return sendResponse(res, 'OPTIMIZATION_SUGGESTIONS_GENERATED', {
        data: { suggestions },
      });
    },
  );

  /**
   * Update percentiles for a submission
   */
  public updatePercentiles = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;

    if (!id) {
      throw createAppError('Submission ID is required', 400);
    }

    await metricsService.updatePercentiles(id);

    return sendResponse(res, 'PERCENTILES_UPDATED');
  });

  /**
   * Record metrics for a submission
   */
  public recordMetrics = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const metrics = req.body;

    if (!id) {
      throw createAppError('Submission ID is required', 400);
    }

    const result = await metricsService.recordMetrics(id, metrics);

    return sendResponse(res, 'METRICS_RECORDED', {
      data: { metrics: result },
    });
  });
}
