import { Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';

import { sendResponse } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';

import { ChallengeDiscussionRepository } from '../repositories/challengeDiscussionRepository';
import { catchAsync } from '../utils/catchAsync';

// Validation schema for creating/updating discussions
const discussionSchema = Joi.object({
  content: Joi.string().required().min(1).messages({
    'string.empty': 'Content is required',
    'string.min': 'Content must be at least 1 character long',
    'any.required': 'Content is required',
  }),
  parent_id: Joi.string().optional(),
});

export default class ChallengeDiscussionController {
  private readonly discussionRepo: ChallengeDiscussionRepository;

  constructor() {
    this.discussionRepo = new ChallengeDiscussionRepository();
  }

  public getDiscussions = catchAsync(async (req: Request, res: Response) => {
    const { challengeId } = req.params;
    const discussions =
      await this.discussionRepo.getDiscussionsByChallenge(challengeId);
    return sendResponse(res, 'DISCUSSIONS_FETCHED', { data: { discussions } });
  });

  public getDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const discussion = await this.discussionRepo.getDiscussionById(id);

    if (!discussion) {
      return sendResponse(res, 'DISCUSSION_NOT_FOUND');
    }

    return sendResponse(res, 'DISCUSSION_FETCHED', { data: { discussion } });
  });

  public createDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { challengeId } = req.params;
    const user_id = req.user?.id;

    if (!user_id) {
      throw createAppError('User not authenticated', 401);
    }

    const { error, value } = discussionSchema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors = error.details.map((detail) => ({
        field: detail.path.join('.'),
        message: detail.message.replace(/["']/g, ''),
      }));
      return sendResponse(res, 'INVALID_INPUT', { error: errors });
    }

    const { content, parent_id } = value;

    const discussion = await this.discussionRepo.createDiscussion({
      challenge_id: challengeId,
      user_id,
      content,
      parent_id,
    });

    return sendResponse(res, 'DISCUSSION_CREATED', { data: { discussion } });
  });

  public updateDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const user_id = req.user?.id;

    if (!user_id) {
      throw createAppError('User not authenticated', 401);
    }

    const { error, value } = discussionSchema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors = error.details.map((detail) => ({
        field: detail.path.join('.'),
        message: detail.message.replace(/["']/g, ''),
      }));
      return sendResponse(res, 'INVALID_INPUT', { error: errors });
    }

    const { content } = value;

    // Check if the discussion exists and belongs to the user
    const existingDiscussion = await this.discussionRepo.findUnique({
      where: { id },
    });

    if (!existingDiscussion) {
      return sendResponse(res, 'DISCUSSION_NOT_FOUND');
    }

    if (existingDiscussion.user_id !== user_id) {
      throw createAppError('Not authorized to update this discussion', 403);
    }

    const discussion = await this.discussionRepo.update({
      where: { id },
      data: { content },
    });

    return sendResponse(res, 'DISCUSSION_UPDATED', { data: { discussion } });
  });

  public deleteDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const user_id = req.user?.id;

    if (!user_id) {
      throw createAppError('User not authenticated', 401);
    }

    // Check if the discussion exists and belongs to the user
    const existingDiscussion = await this.discussionRepo.findUnique({
      where: { id },
    });

    if (!existingDiscussion) {
      return sendResponse(res, 'DISCUSSION_NOT_FOUND');
    }

    if (existingDiscussion.user_id !== user_id) {
      throw createAppError('Not authorized to delete this discussion', 403);
    }

    await this.discussionRepo.deleteDiscussion(id);

    return sendResponse(res, 'DISCUSSION_DELETED');
  });

  public upvoteDiscussion = catchAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const user_id = req.user?.id;

    if (!user_id) {
      throw createAppError('User not authenticated', 401);
    }

    const discussion = await this.discussionRepo.upvoteDiscussion(id);

    return sendResponse(res, 'DISCUSSION_UPVOTED', { data: { discussion } });
  });

  public downvoteDiscussion = catchAsync(
    async (req: Request, res: Response) => {
      const { id } = req.params;
      const user_id = req.user?.id;

      if (!user_id) {
        throw createAppError('User not authenticated', 401);
      }

      const discussion = await this.discussionRepo.downvoteDiscussion(id);

      return sendResponse(res, 'DISCUSSION_DOWNVOTED', {
        data: { discussion },
      });
    },
  );
}
