import { Request, Response } from 'express';

import ChallengeBoilerplatesRepository from '../repositories/challengeBoilerplatesRepository';
import ChallengeRepository from '../repositories/challengeRepository';
import { catchAsync } from '../utils';
import { sendResponse } from '../utils/apiResponse';
import { createAppError } from '../utils/errorHandler';

export default class ChallengeBoilerplatesController {
  private readonly boilerplatesRepo: ChallengeBoilerplatesRepository;
  private readonly challengeRepo: ChallengeRepository;

  constructor() {
    this.boilerplatesRepo = new ChallengeBoilerplatesRepository();
    this.challengeRepo = new ChallengeRepository();
  }

  /**
   * Get all boilerplates for a challenge
   */
  public getBoilerplates = catchAsync(async (req: Request, res: Response) => {
    const { challengeId } = req.params;

    const boilerplates =
      await this.boilerplatesRepo.getBoilerplatesByChallenge(challengeId);

    return sendResponse(res, 'CHALLENGES_FETCHED', { data: { boilerplates } });
  });

  /**
   * Get a specific boilerplate by language
   */
  public getBoilerplateByLanguage = catchAsync(
    async (req: Request, res: Response) => {
      const { challengeId, language } = req.params;

      const boilerplate = await this.boilerplatesRepo.getBoilerplateByLanguage(
        challengeId,
        language,
      );

      if (!boilerplate) {
        throw createAppError(
          `Boilerplate for language ${language} not found`,
          404,
        );
      }

      return sendResponse(res, 'CHALLENGE_FETCHED', { data: { boilerplate } });
    },
  );

  /**
   * Create a new boilerplate
   */
  public createBoilerplate = catchAsync(async (req: Request, res: Response) => {
    const { challengeId } = req.params;
    const { language, boilerplate_code } = req.body;

    // Check if challenge exists
    const challenge = await this.challengeRepo.findUnique({
      where: { id: challengeId },
    });

    if (!challenge) {
      throw createAppError('Challenge not found', 404);
    }

    // Check if boilerplate already exists for this language
    const existingBoilerplate = await this.boilerplatesRepo.boilerplateExists(
      challengeId,
      language,
    );

    if (existingBoilerplate) {
      throw createAppError(
        `Boilerplate for language ${language} already exists`,
        409,
      );
    }

    // Create the boilerplate
    const boilerplate = await this.boilerplatesRepo.createBoilerplate({
      challenge_id: challengeId,
      language,
      boilerplate_code,
    });

    return sendResponse(res, 'CHALLENGE_CREATED', { data: { boilerplate } });
  });

  /**
   * Update a boilerplate
   */
  public updateBoilerplate = catchAsync(async (req: Request, res: Response) => {
    const { challengeId, language } = req.params;
    const { boilerplate_code } = req.body;

    // Check if boilerplate exists
    const existingBoilerplate = await this.boilerplatesRepo.boilerplateExists(
      challengeId,
      language,
    );

    if (!existingBoilerplate) {
      throw createAppError(
        `Boilerplate for language ${language} not found`,
        404,
      );
    }

    // Update the boilerplate
    const boilerplate = await this.boilerplatesRepo.updateBoilerplate(
      existingBoilerplate.id,
      {
        boilerplate_code,
      },
    );

    return sendResponse(res, 'CHALLENGE_UPDATED', { data: { boilerplate } });
  });

  /**
   * Delete a boilerplate
   */
  public deleteBoilerplate = catchAsync(async (req: Request, res: Response) => {
    const { challengeId, language } = req.params;

    // Check if boilerplate exists
    const existingBoilerplate = await this.boilerplatesRepo.boilerplateExists(
      challengeId,
      language,
    );

    if (!existingBoilerplate) {
      throw createAppError(
        `Boilerplate for language ${language} not found`,
        404,
      );
    }

    // Delete the boilerplate
    await this.boilerplatesRepo.deleteBoilerplate(existingBoilerplate.id);

    return sendResponse(res, 'CHALLENGE_UPDATED', { data: null });
  });
}
