import { MainConcept, Prisma } from '@prisma/client';

import prisma from '@/lib/prisma';

import BaseRepository from './baseRepository';

/**
 * Repository for MainConcept entity
 * Extends BaseRepository with MainConcept as the entity type
 */
export class MainConceptRepository extends BaseRepository<MainConcept> {
  constructor() {
    super(prisma.mainConcept);
  }

  async getMainConceptWithSubjects(id: string): Promise<MainConcept | null> {
    return this.findUnique({
      where: { id },
      include: {
        subjects: {
          include: {
            subject: true,
          },
        },
      },
    });
  }

  async createWithSubjects(
    data: Omit<Prisma.MainConceptCreateInput, 'subjects'> & {
      subjectId: string;
    },
  ): Promise<MainConcept> {
    const { subjectId, ...mainConceptData } = data;
    return this.create({
      data: {
        ...mainConceptData,
        subjects: {
          create: {
            order: 1,
            subject: {
              connect: { id: subjectId },
            },
          },
        },
      },
    });
  }

  async getMainConceptById(id: string): Promise<MainConcept | null> {
    return this.findUnique({
      where: { id },
      include: { subjects: true },
    });
  }

  async updateMainConcept(
    id: string,
    data: Prisma.MainConceptUpdateInput,
  ): Promise<MainConcept> {
    return this.update({ where: { id }, data });
  }

  async deleteMainConcept(id: string): Promise<MainConcept> {
    return this.delete({ where: { id } });
  }
}
