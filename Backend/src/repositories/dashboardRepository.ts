import { DashboardStats } from '@/types';

import prisma from '../lib/prisma';

/**
 * Repository for Dashboard-related operations
 * This is a standalone repository that doesn't extend BaseRepository
 * since there's no actual Dashboard model in the database
 */
export class DashboardRepository {
  /**
   * Get dashboard statistics for a user
   * @param userId The ID of the user
   * @returns Dashboard statistics
   */
  async getDashboardStats(userId: string): Promise<DashboardStats> {
    const [
      enrolledRoadmaps,
      totalTopics,
      totalTopicsCompleted,
      totalHoursSpent,
    ] = await Promise.all([
      prisma.userRoadmap.count({
        where: { user_id: userId },
      }),
      prisma.topic.count(),
      prisma.userProgress.count({
        where: {
          user_id: userId,
          is_completed: true,
        },
      }),
      prisma.userProgress.aggregate({
        where: {
          user_id: userId,
          is_completed: true,
        },
        _sum: {
          time_spent: true,
        },
      }),
    ]);

    return {
      enrolledRoadmaps,
      totalTopics,
      totalTopicsCompleted,
      totalHoursSpent: Math.round((totalHoursSpent._sum.time_spent || 0) / 60), // Convert minutes to hours
    };
  }

  /**
   * Get enrolled roadmaps for a user
   * @param userId The ID of the user
   * @returns List of enrolled roadmaps
   */
  async getEnrolledRoadmaps(userId: string) {
    return prisma.userRoadmap.findMany({
      where: { user_id: userId },
      include: {
        roadmap: {
          include: {
            user: {
              select: {
                username: true,
                avatar_url: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * Get recommended roadmaps for a user
   * @param userId The ID of the user
   * @returns List of recommended roadmaps
   */
  async getRecommendedRoadmaps(userId: string) {
    return prisma.roadmap.findMany({
      where: {
        is_public: true,
        NOT: {
          user_roadmaps: {
            some: {
              user_id: userId,
            },
          },
        },
      },
      include: {
        user: {
          select: {
            username: true,
            avatar_url: true,
          },
        },
      },
      take: 5,
    });
  }

  /**
   * Get recent activities for a user
   * @param userId The ID of the user
   * @returns List of recent activities
   */
  async getRecentActivities(userId: string) {
    return prisma.userActivityLog.findMany({
      where: { user_id: userId },
      orderBy: { timestamp: 'desc' },
      take: 10,
      include: {
        user: true,
      },
    });
  }

  /**
   * Get learning progress for a user
   * @param userId The ID of the user
   * @returns List of learning progress entries
   */
  async getLearningProgress(userId: string) {
    return prisma.userProgress.findMany({
      where: { user_id: userId },
      include: {
        topic: true,
      },
    });
  }

  /**
   * Get achievements for a user
   * @param userId The ID of the user
   * @returns List of user achievements with achievement details
   */
  async getAchievements(userId: string) {
    return prisma.userAchievement.findMany({
      where: { user_id: userId },
      orderBy: { unlocked_at: 'desc' },
      include: {
        achievement: true,
      },
    });
  }
}
