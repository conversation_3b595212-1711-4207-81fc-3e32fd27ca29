import { Prisma, SystemConfig } from '@prisma/client';

import prisma from '@/lib/prisma';
import { deleteCache, getCache, setCache } from '@/services/cacheService';

import { createAppError } from '../utils/errorHandler';
import BaseRepository from './baseRepository';

/**
 * Repository for SystemConfig entity
 * Extends BaseRepository with SystemConfig as the entity type
 */
export default class SystemConfigRepository extends BaseRepository<SystemConfig> {
  constructor() {
    super(prisma.systemConfig);
  }

  async getConfig(key: string): Promise<Prisma.JsonValue> {
    const cached = await getCache<Prisma.JsonValue>(`config:${key}`);
    if (cached) return cached;

    const config = await this.findUnique({ where: { key } });

    if (!config) {
      throw createAppError(`Configuration '${key}' not found`, 404);
    }

    await setCache(`config:${key}`, config.value, { ttl: 3600 });
    return config.value;
  }

  async setConfig(data: {
    key: string;
    value: Prisma.JsonValue;
    category: string;
    description?: string;
  }) {
    const validatedValue: Prisma.InputJsonValue =
      data.value === null
        ? Prisma.JsonNull
        : JSON.parse(JSON.stringify(data.value));
    const createData = {
      ...data,
      value: validatedValue,
    };

    const config = await this.upsert({
      where: { key: data.key },
      update: {
        value: validatedValue,
        category: data.category,
        description: data.description,
      },
      create: createData,
    });

    await deleteCache(`config:${data.key}`);
    return config;
  }

  async getNotificationSettings() {
    const settings = await this.findMany({
      where: { category: 'notifications' },
    });
    return settings.reduce(
      (acc, setting) => ({
        ...acc,
        [setting.key]: setting.value,
      }),
      {},
    );
  }

  async updateNotificationSettings(settings: Record<string, Prisma.JsonValue>) {
    const updates = Object.entries(settings).map(([key, value]) =>
      this.setConfig({
        key,
        value,
        category: 'notifications',
      }),
    );

    await Promise.all(updates);
  }
}
