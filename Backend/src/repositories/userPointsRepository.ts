import { UserPoints } from '@prisma/client';

import prisma from '@/lib/prisma';

import BaseRepository from './baseRepository';

/**
 * Repository for UserPoints entity
 * Handles CRUD operations and specialized queries for user points
 */

export default class UserPointsRepository extends BaseRepository<UserPoints> {
  constructor() {
    // Pass the Prisma delegate for the user model (prisma.user)
    super(prisma.userPoints);
  }

  async getUserPoints(user_id: string) {
    const points = await this.findMany({
      where: { user_id },
    });

    return points;
  }

  // Update user points
  async updateUserPoints(user_id: string, points: number): Promise<void> {
    await this.upsert({
      where: { user_id },
      update: { points: { increment: points } },
      create: { user_id, points },
    });
  }
}
