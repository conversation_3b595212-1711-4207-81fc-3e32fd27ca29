import { ChallengeExample } from '@prisma/client';

import prisma from '@/lib/prisma';

import { createAppError } from '../utils/errorHandler';
import BaseRepository from './baseRepository';

/**
 * Repository for ChallengeExample entity
 * Handles CRUD operations and specialized queries for challengeExamples
 */
export interface ChallengeExampleData {
  challenge_id: string;
  input: string;
  output: string;
  explanation?: string;
  order_index: number;
}

export class ChallengeExampleRepository extends BaseRepository<ChallengeExample> {
  constructor() {
    super(prisma.challengeExample);
  }

  /**
   * Get all examples for a challenge
   * @param challengeId - The ID of the challenge
   * @returns An array of challenge examples
   */
  async getExamplesByChallenge(
    challengeId: string,
  ): Promise<ChallengeExample[]> {
    return prisma.challengeExample.findMany({
      where: { challenge_id: challengeId },
      orderBy: { order_index: 'asc' },
    });
  }

  /**
   * Create a new challenge example
   * @param data - The example data
   * @returns The created example
   */
  async createExample(data: ChallengeExampleData): Promise<ChallengeExample> {
    // Check if challenge exists
    const challenge = await prisma.challenge.findUnique({
      where: { id: data.challenge_id },
    });

    if (!challenge) {
      throw createAppError('Challenge not found', 404);
    }

    return prisma.challengeExample.create({
      data,
    });
  }

  /**
   * Update an existing challenge example
   * @param id - The ID of the example to update
   * @param data - The updated example data
   * @returns The updated example
   */
  async updateExample(
    id: string,
    data: Partial<Omit<ChallengeExampleData, 'challenge_id'>>,
  ): Promise<ChallengeExample> {
    const example = await prisma.challengeExample.findUnique({
      where: { id },
    });

    if (!example) {
      throw createAppError('Example not found', 404);
    }

    return prisma.challengeExample.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a challenge example
   * @param id - The ID of the example to delete
   * @returns The deleted example
   */
  async deleteExample(id: string): Promise<ChallengeExample> {
    const example = await prisma.challengeExample.findUnique({
      where: { id },
    });

    if (!example) {
      throw createAppError('Example not found', 404);
    }

    return prisma.challengeExample.delete({
      where: { id },
    });
  }

  /**
   * Reorder examples for a challenge
   * @param challengeId - The ID of the challenge
   * @param exampleIds - An array of example IDs in the desired order
   * @returns An array of updated examples
   */
  async reorderExamples(
    challengeId: string,
    exampleIds: string[],
  ): Promise<ChallengeExample[]> {
    // Check if all examples belong to the challenge
    const examples = await prisma.challengeExample.findMany({
      where: { challenge_id: challengeId },
    });

    const exampleMap = new Map(examples.map((e) => [e.id, e]));

    // Validate that all provided IDs belong to the challenge
    for (const id of exampleIds) {
      if (!exampleMap.has(id)) {
        throw createAppError(
          `Example with ID ${id} not found or does not belong to this challenge`,
          400,
        );
      }
    }

    // Update the order of each example
    const updates = exampleIds.map((id, index) =>
      prisma.challengeExample.update({
        where: { id },
        data: { order_index: index },
      }),
    );

    return Promise.all(updates);
  }
}

export default new ChallengeExampleRepository();
