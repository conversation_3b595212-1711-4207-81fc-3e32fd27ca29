import { Prisma, User } from '@prisma/client';

import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import prisma from '../lib/prisma';
import BaseRepository from './baseRepository';

/**
 * Repository for User entity
 * Extends BaseRepository with User as the entity type
 */
export default class UserRepository extends BaseRepository<User> {
  constructor() {
    super(prisma.user);
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.findUnique({
      where: { email },
    });
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.findUnique({
      where: { username },
    });
  }

  async createUser(data: Prisma.UserCreateInput): Promise<User> {
    return this.create({
      data,
    });
  }

  async updateUser(id: string, data: Prisma.UserUpdateInput): Promise<User> {
    return this.update({
      where: { id },
      data,
    });
  }

  async deleteUser(id: string): Promise<void> {
    await this.delete({
      where: { id },
    });
  }

  async getUserProfile(id: string): Promise<User | null> {
    return this.findUnique({
      where: { id },
      include: {
        role: true,
        user_permissions: true,
      },
    });
  }

  async upsertUserProfile(
    data: {
      id: string;
      email: string;
      username: string;
      graduation_year?: number;
      skills?: string[];
    } & Prisma.UserCreateInput,
  ) {
    return this.upsert({
      where: { id: data.id },
      create: {
        ...data,
        role: { connect: { name: 'STUDENT' } },
      },
      update: {
        ...data,
        role: { connect: { name: 'STUDENT' } },
        updated_at: new Date(),
      },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        specialization: true,
        college: true,
      },
    });
  }

  async updateUserRole(userId: string, roleId: string): Promise<User> {
    try {
      const user = await this.update({
        where: { id: userId },
        data: { role_id: roleId },
      });

      if (!user) throw createAppError('User not found', 404);
      return user;
    } catch (error) {
      logger.error('[UserRepository] Error in getUserStats', { error });
      logger.error('Error updating user role:', error);
      throw createAppError('Failed to update user role', 500);
    }
  }

  async updateUserStatus(user_id: string, status: string): Promise<User> {
    const user = await this.update({
      where: { id: user_id },
      data: { status },
    });

    return user;
  }

  async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    usersByRole: Record<
      'beginner' | 'intermediate' | 'advanced' | 'expert' | 'unknown',
      number
    >;
    completionRates: Record<string, number>;
  }> {
    logger.info('[UserRepository] Entering getUserStats');
    try {
      const [totalUsers, activeUsers, newUsers] = await Promise.all([
        this.count(),
        this.count({
          where: { status: 'active' },
        }),
        this.count({
          where: {
            created_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        }),
      ]);

      const usersByRoleResults = await this.groupBy({
        by: [Prisma.UserScalarFieldEnum.experience_level],
        _count: true,
        orderBy: {
          experience_level: 'asc',
        },
      });

      const roleCountMap: Record<
        'beginner' | 'intermediate' | 'advanced' | 'expert' | 'unknown',
        number
      > = {
        beginner: 0,
        intermediate: 0,
        advanced: 0,
        expert: 0,
        unknown: 0,
      };

      // TODO: Type 'group' properly if possible (it's related to Prisma GroupBy result type)
      usersByRoleResults.forEach((group: any) => {
        const level =
          group[Prisma.UserScalarFieldEnum.experience_level] ?? 'unknown';
        if (roleCountMap.hasOwnProperty(level.toLowerCase())) {
          roleCountMap[level.toLowerCase() as keyof typeof roleCountMap] =
            typeof group._count === 'number'
              ? group._count
              : (group._count?.id ?? 0); // Adjusted for potential GroupBy structure
        }
      });

      // TODO: Add actual logic here for completionRates
      const completionRates: Record<string, number> = {};

      logger.info('[UserRepository] Exiting getUserStats successfully');
      return {
        totalUsers,
        activeUsers,
        newUsers,
        usersByRole: roleCountMap,
        completionRates,
      };
    } catch (error) {
      let errorDetails: Record<string, unknown>;
      if (error instanceof Error) {
        errorDetails = {
          message: error.message,
          name: error.name,
          stack: error.stack,
        };
      } else {
        errorDetails = { unknownError: String(error) };
      }
      logger.error('[UserRepository] Error in getUserStats:', errorDetails);
      throw createAppError('Failed to get user stats', 500, errorDetails);
    }
  }

  async searchUsers(params: {
    search?: string;
    role?: string;
    status?: string;
    date_range?: { start: Date; end: Date };
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const {
      search,
      role,
      status,
      date_range,
      sortBy = 'created_at',
      sortOrder = 'desc',
    } = params;

    const page = Number(params?.page ?? 1);
    const limit = Number(params?.limit ?? 10);

    const where: Prisma.UserWhereInput = {};

    if (role) {
      where.role_id = role;
    }

    if (status) {
      where.status = status;
    }

    if (date_range) {
      where.created_at = {
        gte: date_range.start,
        lte: date_range.end,
      };
    }

    const response = await this.paginate(
      {
        page,
        limit,
        search,
        sort: {
          field: sortBy,
          direction: sortOrder,
        },
      },
      ['username', 'email', 'full_name'],
      {},
      where,
    );

    return response;
  }

  async bulkUpdateUsers(
    user_ids: string[],
    action: 'suspend' | 'activate' | 'delete' | 'changeRole',
    params?: { role_id?: string; reason?: string },
  ): Promise<void> {
    const transaction: Prisma.PrismaPromise<unknown>[] = [];

    for (const user_id of user_ids) {
      switch (action) {
        case 'suspend':
          transaction.push(
            this.update({
              where: { id: user_id },
              data: { status: 'suspended' },
            }) as Prisma.PrismaPromise<unknown>,
          );
          break;
        case 'activate':
          transaction.push(
            this.update({
              where: { id: user_id },
              data: { status: 'active' },
            }) as Prisma.PrismaPromise<unknown>,
          );
          break;
        case 'delete':
          transaction.push(
            this.delete({
              where: { id: user_id },
            }) as Prisma.PrismaPromise<unknown>,
          );
          break;
        case 'changeRole':
          if (params?.role_id) {
            transaction.push(
              this.update({
                where: { id: user_id },
                data: { role_id: params.role_id },
              }) as Prisma.PrismaPromise<unknown>,
            );
          }
          break;
      }

      // TODO: Update this with relevant common function
      transaction.push(
        prisma.userActivityLog.create({
          data: {
            user_id,
            action: `BULK_${action.toUpperCase()}`,
            details: params as Prisma.InputJsonValue,
            timestamp: new Date(),
          },
        }),
      );
    }

    await prisma.$transaction(transaction);
  }

  async getAllUsers(params: { page: number; limit: number; search: string }) {
    const { page = 1, limit = 10, search = '' } = params;
    return this.paginate(
      {
        page,
        limit,
        search,
      },
      [],
      {},
      {},
    );
  }

  async getUserByEmail(email: string) {
    return this.findUnique({
      where: { email },
    });
  }

  async getUserById(id: string) {
    return this.findUnique({
      where: { id },
    });
  }

  // assign role to user
  async assignRole(id: string, role_id: string) {
    await this.update({
      where: { id },
      data: { role_id },
    });
  }
}
