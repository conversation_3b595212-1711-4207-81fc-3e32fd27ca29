import { ActivityType, ExperienceLevel, UserProgress } from '@prisma/client';

import prisma from '@/lib/prisma';
import {
  deleteCache,
  getCache,
  getOrSetCache,
  setCache,
} from '@/services/cacheService';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

interface StreakData {
  current_streak: number;
  longest_streak: number;
  last_activity_date: Date | null;
  streak_start_date: Date | null;
}

interface UserProgressStats {
  total_challenges_completed: number;
  total_challenges_attempted: number;
  total_points: number;
  average_score: number;
  favorite_language: string;
  language_distribution: Record<string, number>;
  difficulty_distribution: Record<string, number>;
  category_distribution: Record<string, number>;
  daily_activity: Record<string, number>;
  streak_data: StreakData;
  recent_achievements: any[];
}

interface ActivityData {
  date: string;
  type: ActivityType;
  minutes_spent: number;
}

/**
 * Repository for UserProgress entity
 * Extends BaseRepository with <PERSON>r<PERSON>rog<PERSON> as the entity type
 */
export default class UserProgressRepository extends BaseRepository<UserProgress> {
  constructor() {
    // Pass the Prisma delegate for the user model (prisma.user)
    super(prisma.userProgress);
  }

  // Get user progress
  async getUserProgress(user_id: string) {
    // Fetch user progress data including related topic information
    const progress = await this.findMany({
      where: { user_id },
      include: {
        topic: {
          select: {
            title: true,
            subjects: {
              select: {
                subject: {
                  select: {
                    title: true,
                    main_concepts: {
                      select: {
                        main_concept: {
                          select: {
                            name: true,
                            roadmaps: {
                              select: {
                                roadmap: {
                                  select: {
                                    title: true,
                                  },
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    // Fetch user points
    const points = await prisma.userPoints.findUnique({
      where: { user_id },
    });

    // Return an object containing user progress information
    return {
      progress,
      total_points: points?.points ?? 0, // Use 0 if points are not available
      completed_topics: progress.filter((p) => p.is_completed).length,
      in_progress_topics: progress.filter((p) => !p.is_completed).length,
    };
  }

  // Update user progress
  async updateUserProgress(
    user_id: string,
    data: {
      topic_id: string;
      is_completed: boolean;
      timeSpent: number;
    },
  ): Promise<void> {
    await this.upsert({
      where: { user_id_topic_id: { user_id, topic_id: data.topic_id } },
      update: {
        is_completed: data.is_completed,
        completed_at: data.is_completed ? new Date() : null,
        time_spent: data.timeSpent,
      },
      create: {
        user_id,
        topic_id: data.topic_id,
        is_completed: data.is_completed,
        completed_at: data.is_completed ? new Date() : null,
        time_spent: data.timeSpent ?? 0,
        subject_id: '',
      },
    });
  }

  // Get user's achievements
  async getAchievements(userId: string) {
    const progress = await this.getUserProgress(userId);
    const achievements = [];

    if (progress.total_points >= 1000) achievements.push('Points Master');
    if (progress.completed_topics >= 10) achievements.push('Learning Explorer');
    if (progress.completed_topics >= 50) achievements.push('Knowledge Warrior');

    return achievements;
  }

  // Get user's experience level
  async calculateExperienceLevel(user_id: string): Promise<ExperienceLevel> {
    const { total_points } = await this.getUserProgress(user_id);

    if (total_points >= 5000) return 'expert';
    if (total_points >= 2000) return 'advanced';
    if (total_points >= 500) return 'intermediate';
    return 'beginner';
  }

  async getUserAnalytics(userId: string) {
    try {
      const [courseProgress, challengeStats, resourceUsage] = await Promise.all(
        [
          this.findMany({
            where: { user_id: userId },
            include: {
              topic: {
                select: {
                  title: true,
                  subjects: {
                    select: {
                      subject: { select: { title: true } },
                    },
                  },
                },
              },
            },
          }),
          prisma.challengeSubmission.groupBy({
            by: ['status'],
            where: { user_id: userId },
            _count: true,
          }),
          prisma.resource.groupBy({
            by: ['type'],
            where: { user_id: userId },
            _count: true,
          }),
        ],
      );

      return {
        courseProgress,
        challengeStats,
        resourceUsage,
      };
    } catch (error) {
      logger.error('Error: ', error);
      throw createAppError('Failed to fetch user analytics', 500);
    }
  }

  async trackProgress(data: {
    user_id: string;
    topic_id: string;
    subject_id?: string;
    progress_percentage: number;
    is_completed: boolean;
  }) {
    const existing = await this.findFirst({
      where: { user_id: data.user_id, topic_id: data.topic_id },
    });

    const progress = await prisma.userProgress.upsert({
      where: { id: existing?.id ?? '' },
      create: {
        user_id: data.user_id,
        topic_id: data.topic_id,
        subject_id: data.subject_id,
        progress_percentage: data.progress_percentage,
        is_completed: data.is_completed,
        completed_at: data.is_completed ? new Date() : null,
      },
      update: {
        progress_percentage: data.progress_percentage,
        is_completed: data.is_completed,
        completed_at: data.is_completed ? new Date() : null,
      },
    });

    await deleteCache(`user:${data.user_id}:progress`);
    return progress;
  }

  async resetProgress(user_id: string, topic_id: string) {
    await this.deleteMany({
      where: { user_id, topic_id },
    });

    await deleteCache(`user:${user_id}:progress`);
  }

  async calculateLearningPath(user_id: string) {
    const cacheKey = `user:${user_id}:learning-path`;
    const cached = await getCache(cacheKey);
    if (cached) return cached;

    const progress = await prisma.userProgress.findMany({
      where: { user_id },
      include: { topic: true },
    });

    const completed = progress.filter((p) => p.is_completed);
    const recommendations = await prisma.topic.findMany({
      where: {
        OR: [
          { prerequisites: { isEmpty: true } },
          {
            prerequisites: {
              hasEvery: completed
                .map((c) => c.topic_id)
                .filter((id): id is string => id !== null),
            },
          },
        ],
        NOT: {
          id: {
            in: completed
              .map((c) => c.topic_id)
              .filter((id): id is string => id !== null),
          },
        },
      },
      orderBy: { order: 'asc' },
    });

    await setCache(cacheKey, recommendations, { ttl: 3600 });
    return recommendations;
  }

  /**
   * Track user streak and update streak data
   */
  async trackStreak(user_id: string): Promise<StreakData> {
    const startTime = Date.now();
    try {
      // Get user's streak data
      let userStreak = await prisma.userStreak.findUnique({
        where: { user_id },
      });

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // If user doesn't have streak data, create it
      if (!userStreak) {
        userStreak = await prisma.userStreak.create({
          data: {
            user_id,
            current_streak: 1,
            longest_streak: 1,
            last_activity_date: today,
            streak_start_date: today,
          },
        });

        return {
          current_streak: 1,
          longest_streak: 1,
          last_activity_date: today,
          streak_start_date: today,
        };
      }

      // If user already has activity today, return current streak
      if (userStreak.last_activity_date) {
        const lastActivityDate = new Date(userStreak.last_activity_date);
        lastActivityDate.setHours(0, 0, 0, 0);

        if (lastActivityDate.getTime() === today.getTime()) {
          return {
            current_streak: userStreak.current_streak,
            longest_streak: userStreak.longest_streak,
            last_activity_date: userStreak.last_activity_date,
            streak_start_date: userStreak.streak_start_date,
          };
        }

        // Check if streak is broken (more than 1 day since last activity)
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);

        if (lastActivityDate.getTime() < yesterday.getTime()) {
          // Streak broken, reset to 1
          const updatedStreak = await prisma.userStreak.update({
            where: { user_id },
            data: {
              current_streak: 1,
              last_activity_date: today,
              streak_start_date: today,
            },
          });

          return {
            current_streak: 1,
            longest_streak: updatedStreak.longest_streak,
            last_activity_date: today,
            streak_start_date: today,
          };
        }

        // Streak continues
        const newCurrentStreak = userStreak.current_streak + 1;
        const newLongestStreak = Math.max(
          newCurrentStreak,
          userStreak.longest_streak,
        );

        const updatedStreak = await prisma.userStreak.update({
          where: { user_id },
          data: {
            current_streak: newCurrentStreak,
            longest_streak: newLongestStreak,
            last_activity_date: today,
          },
        });

        return {
          current_streak: updatedStreak.current_streak,
          longest_streak: updatedStreak.longest_streak,
          last_activity_date: updatedStreak.last_activity_date,
          streak_start_date: updatedStreak.streak_start_date,
        };
      }

      // First activity ever
      const updatedStreak = await prisma.userStreak.update({
        where: { user_id },
        data: {
          current_streak: 1,
          longest_streak: 1,
          last_activity_date: today,
          streak_start_date: today,
        },
      });

      return {
        current_streak: updatedStreak.current_streak,
        longest_streak: updatedStreak.longest_streak,
        last_activity_date: updatedStreak.last_activity_date,
        streak_start_date: updatedStreak.streak_start_date,
      };
    } catch (error) {
      logger.error('Error tracking streak:', error);
      throw createAppError('Failed to track streak', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Streak tracking took ${duration}ms`);
    }
  }

  /**
   * Track daily activity
   */
  async trackDailyActivity(
    user_id: string,
    activity_type: ActivityType,
    minutes_spent: number,
  ): Promise<void> {
    const startTime = Date.now();
    try {
      // Get user's streak data
      let userStreak = await prisma.userStreak.findUnique({
        where: { user_id },
      });

      // If user doesn't have streak data, create it
      if (!userStreak) {
        userStreak = await prisma.userStreak.create({
          data: {
            user_id,
            current_streak: 0,
            longest_streak: 0,
          },
        });
      }

      // Create daily activity record
      await prisma.userDailyActivity.create({
        data: {
          user_id,
          activity_type,
          minutes_spent,
        },
      });

      // Update streak
      await this.trackStreak(user_id);
    } catch (error) {
      logger.error('Error tracking daily activity:', error);
      throw createAppError('Failed to track daily activity', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Daily activity tracking took ${duration}ms`);
    }
  }

  /**
   * Get user's comprehensive progress statistics
   */
  async getUserProgressStats(user_id: string): Promise<UserProgressStats> {
    const startTime = Date.now();
    try {
      const cacheKey = `user:${user_id}:progress-stats`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          // Get user's challenge submissions
          const submissions = await prisma.challengeSubmission.findMany({
            where: { user_id },
            include: {
              challenge: {
                select: {
                  difficulty: true,
                  category: true,
                },
              },
            },
          });

          // Get user's streak data
          const userStreak = await prisma.userStreak.findUnique({
            where: { user_id },
          });

          // Get user's points
          const userPoints = await prisma.userPoints.findUnique({
            where: { user_id },
          });

          // Get user's achievements
          const userAchievements = await prisma.userAchievement.findMany({
            where: { user_id },
            orderBy: { unlocked_at: 'desc' },
            take: 5,
            include: {
              achievement: true,
            },
          });

          // Get user's daily activities
          const dailyActivities = await prisma.userDailyActivity.findMany({
            where: { user_id },
            orderBy: { created_at: 'desc' },
            take: 30, // Last 30 days
          });

          // Calculate statistics
          const completedSubmissions = submissions.filter(
            (s) => s.status === 'accepted',
          );

          // Language distribution
          const languageDistribution: Record<string, number> = {};
          submissions.forEach((s) => {
            if (s.language) {
              languageDistribution[s.language] =
                (languageDistribution[s.language] || 0) + 1;
            }
          });

          // Favorite language
          let favoriteLanguage = 'none';
          let maxCount = 0;
          Object.entries(languageDistribution).forEach(([language, count]) => {
            if (count > maxCount) {
              maxCount = count;
              favoriteLanguage = language;
            }
          });

          // Difficulty distribution
          const difficultyDistribution: Record<string, number> = {};
          submissions.forEach((s) => {
            if (s.challenge.difficulty) {
              const difficulty = s.challenge.difficulty.toLowerCase();
              difficultyDistribution[difficulty] =
                (difficultyDistribution[difficulty] || 0) + 1;
            }
          });

          // Category distribution
          const categoryDistribution: Record<string, number> = {};
          submissions.forEach((s) => {
            if (s.challenge.category) {
              const category = s.challenge.category.toLowerCase();
              categoryDistribution[category] =
                (categoryDistribution[category] || 0) + 1;
            }
          });

          // Daily activity
          const dailyActivityMap: Record<string, number> = {};
          dailyActivities.forEach((activity) => {
            const date = activity.created_at.toISOString().split('T')[0];
            dailyActivityMap[date] =
              (dailyActivityMap[date] || 0) + activity.minutes_spent;
          });

          return {
            total_challenges_completed: completedSubmissions.length,
            total_challenges_attempted: submissions.length,
            total_points: userPoints?.points || 0,
            average_score:
              submissions.length > 0
                ? submissions.reduce((sum, s) => sum + (s.score || 0), 0) /
                  submissions.length
                : 0,
            favorite_language: favoriteLanguage,
            language_distribution: languageDistribution,
            difficulty_distribution: difficultyDistribution,
            category_distribution: categoryDistribution,
            daily_activity: dailyActivityMap,
            streak_data: {
              current_streak: userStreak?.current_streak || 0,
              longest_streak: userStreak?.longest_streak || 0,
              last_activity_date: userStreak?.last_activity_date || null,
              streak_start_date: userStreak?.streak_start_date || null,
            },
            recent_achievements: userAchievements.map((ua) => ({
              id: ua.achievement_id,
              name: ua.achievement.name,
              description: ua.achievement.description,
              icon_url: ua.achievement.icon_url,
              earned_at: ua.unlocked_at,
            })),
          };
        },
        { ttl: 3600 }, // Cache for 1 hour
      );
    } catch (error) {
      logger.error('Error getting user progress stats:', error);
      throw createAppError('Failed to get user progress stats', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`User progress stats took ${duration}ms`);
    }
  }

  /**
   * Get user's activity timeline
   */
  async getUserActivityTimeline(
    user_id: string,
    days: number = 30,
  ): Promise<ActivityData[]> {
    const startTime = Date.now();
    try {
      const cacheKey = `user:${user_id}:activity-timeline:${days}`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - days);

          const activities = await prisma.userDailyActivity.findMany({
            where: {
              user_id,
              created_at: {
                gte: startDate,
              },
            },
            orderBy: {
              created_at: 'asc',
            },
          });

          return activities.map((activity) => ({
            date: activity.created_at.toISOString().split('T')[0],
            type: activity.activity_type,
            minutes_spent: activity.minutes_spent,
          }));
        },
        { ttl: 3600 }, // Cache for 1 hour
      );
    } catch (error) {
      logger.error('Error getting user activity timeline:', error);
      throw createAppError('Failed to get user activity timeline', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Activity timeline took ${duration}ms`);
    }
  }
}
