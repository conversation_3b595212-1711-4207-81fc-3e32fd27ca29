import { SharedSolution, SolutionView } from '@prisma/client';

import prisma from '@/lib/prisma';
import { createAppError } from '@/utils/errorHandler';
import { generateRandomToken } from '@/utils/tokenGenerator';

import BaseRepository from './baseRepository';

/**
 * Repository for SharedSolution entity
 * Extends BaseRepository with SharedSolution as the entity type
 */
export default class SharedSolutionRepository extends BaseRepository<SharedSolution> {
  constructor() {
    super(prisma.sharedSolution);
  }

  /**
   * Create a shared solution from a submission
   * @param submissionId - The ID of the submission to share
   * @param title - Optional title for the shared solution
   * @param description - Optional description for the shared solution
   * @param isPublic - Whether the solution is publicly accessible
   * @param expiresAt - Optional expiration date for the shared solution
   * @returns The created shared solution
   */
  async createSharedSolution(
    submissionId: string,
    title?: string,
    description?: string,
    isPublic: boolean = true,
    expiresAt?: Date,
  ): Promise<SharedSolution> {
    // Check if the submission exists
    const submission = await prisma.challengeSubmission.findUnique({
      where: { id: submissionId },
      include: { challenge: true },
    });

    if (!submission) {
      throw createAppError('Submission not found', 404);
    }

    // Check if a shared solution already exists for this submission
    const existingShare = await prisma.sharedSolution.findUnique({
      where: { submission_id: submissionId },
    });

    if (existingShare) {
      return existingShare;
    }

    // Generate a unique share token
    const shareToken = generateRandomToken(12);

    // Create default title if not provided
    const defaultTitle = title || `Solution for ${submission.challenge.title}`;

    // Create the shared solution
    return prisma.sharedSolution.create({
      data: {
        submission_id: submissionId,
        share_token: shareToken,
        title: defaultTitle,
        description,
        is_public: isPublic,
        expires_at: expiresAt,
        view_count: 0,
      },
    });
  }

  /**
   * Get a shared solution by its share token
   * @param shareToken - The share token
   * @returns The shared solution with submission details
   */
  async getSharedSolutionByToken(shareToken: string): Promise<
    SharedSolution & {
      submission: {
        code: string;
        language: string;
        status: string;
        runtime_ms: number | null;
        memory_used_kb: number | null;
        user: {
          username: string;
          avatar_url: string | null;
        };
        challenge: {
          id: string;
          title: string;
          difficulty: string;
          category: string;
        };
      };
    }
  > {
    const sharedSolution = await prisma.sharedSolution.findUnique({
      where: { share_token: shareToken },
      include: {
        submission: {
          select: {
            code: true,
            language: true,
            status: true,
            runtime_ms: true,
            memory_used_kb: true,
            user: {
              select: {
                username: true,
                avatar_url: true,
              },
            },
            challenge: {
              select: {
                id: true,
                title: true,
                difficulty: true,
                category: true,
              },
            },
          },
        },
      },
    });

    if (!sharedSolution) {
      throw createAppError('Shared solution not found', 404);
    }

    // Check if the solution has expired
    if (sharedSolution.expires_at && sharedSolution.expires_at < new Date()) {
      throw createAppError('This shared solution has expired', 410);
    }

    // Check if the solution is public
    if (!sharedSolution.is_public) {
      throw createAppError('This shared solution is private', 403);
    }

    return sharedSolution as any;
  }

  /**
   * Record a view for a shared solution
   * @param sharedSolutionId - The ID of the shared solution
   * @param viewerId - Optional ID of the viewer (if logged in)
   * @param ipAddress - IP address of the viewer
   * @param userAgent - User agent of the viewer
   * @param referrer - Referrer URL
   * @returns The created view record
   */
  async recordView(
    sharedSolutionId: string,
    viewerId?: string,
    ipAddress?: string,
    userAgent?: string,
    referrer?: string,
  ): Promise<SolutionView> {
    // Create the view record
    const view = await prisma.solutionView.create({
      data: {
        shared_solution_id: sharedSolutionId,
        viewer_id: viewerId,
        ip_address: ipAddress,
        user_agent: userAgent,
        referrer,
      },
    });

    // Increment the view count on the shared solution
    await prisma.sharedSolution.update({
      where: { id: sharedSolutionId },
      data: { view_count: { increment: 1 } },
    });

    return view;
  }

  /**
   * Get view statistics for a shared solution
   * @param sharedSolutionId - The ID of the shared solution
   * @returns View statistics
   */
  async getViewStatistics(sharedSolutionId: string): Promise<{
    totalViews: number;
    uniqueViewers: number;
    viewsByDate: { date: string; count: number }[];
    referrers: { referrer: string; count: number }[];
  }> {
    // Get the shared solution
    const sharedSolution = await prisma.sharedSolution.findUnique({
      where: { id: sharedSolutionId },
    });

    if (!sharedSolution) {
      throw createAppError('Shared solution not found', 404);
    }

    // Get total views
    const totalViews = await prisma.solutionView.count({
      where: { shared_solution_id: sharedSolutionId },
    });

    // Get unique viewers (by IP address)
    const uniqueViewers = await prisma.$queryRaw<{ count: BigInt }[]>`
      SELECT COUNT(DISTINCT ip_address) as count
      FROM "SolutionView"
      WHERE shared_solution_id = ${sharedSolutionId}
    `;

    // Get views by date
    const viewsByDate = await prisma.$queryRaw<
      { date: string; count: BigInt }[]
    >`
      SELECT
        TO_CHAR(created_at, 'YYYY-MM-DD') as date,
        COUNT(*) as count
      FROM "SolutionView"
      WHERE shared_solution_id = ${sharedSolutionId}
      GROUP BY date
      ORDER BY date
    `;

    // Get referrers
    const referrers = await prisma.$queryRaw<
      { referrer: string; count: BigInt }[]
    >`
      SELECT
        COALESCE(referrer, 'Direct') as referrer,
        COUNT(*) as count
      FROM "SolutionView"
      WHERE shared_solution_id = ${sharedSolutionId}
      GROUP BY referrer
      ORDER BY count DESC
      LIMIT 10
    `;

    return {
      totalViews,
      uniqueViewers: Number(uniqueViewers[0]?.count || 0),
      viewsByDate: viewsByDate.map((item) => ({
        date: item.date,
        count: Number(item.count),
      })),
      referrers: referrers.map((item) => ({
        referrer: item.referrer,
        count: Number(item.count),
      })),
    };
  }

  /**
   * Update a shared solution
   * @param id - The ID of the shared solution
   * @param data - The data to update
   * @returns The updated shared solution
   */
  async updateSharedSolution(
    id: string,
    data: {
      title?: string;
      description?: string;
      is_public?: boolean;
      expires_at?: Date | null;
    },
  ): Promise<SharedSolution> {
    return prisma.sharedSolution.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a shared solution
   * @param id - The ID of the shared solution
   * @returns The deleted shared solution
   */
  async deleteSharedSolution(id: string): Promise<SharedSolution> {
    return prisma.sharedSolution.delete({
      where: { id },
    });
  }
}
