import { Role, RoleType, UserRole } from '@prisma/client';

import prisma from '@/lib/prisma';

import BaseRepository from './baseRepository';

/**
 * Repository for Role entity
 * Extends BaseRepository with Role as the entity type
 */
export default class RoleRepository extends BaseRepository<Role> {
  constructor() {
    super(prisma.role);
  }

  /**
   * Get all roles
   */
  async getAllRoles(): Promise<Role[]> {
    return this.findMany({
      orderBy: { name: 'asc' },
    });
  }

  /**
   * Get role by ID
   */
  async getRoleById(id: string): Promise<Role | null> {
    return this.findUnique({
      where: { id },
      include: {
        user_roles: {
          include: {
            user: true,
          },
        },
      },
    });
  }

  /**
   * Create a new role
   */
  async createRole(data: {
    name: string;
    description?: string;
    type: RoleType;
  }): Promise<Role> {
    return this.create({
      data,
    });
  }

  /**
   * Update a role
   */
  async updateRole(
    id: string,
    data: {
      name?: string;
      description?: string;
      type?: RoleType;
    },
  ): Promise<Role> {
    return this.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a role
   */
  async deleteRole(id: string): Promise<Role> {
    return this.delete({
      where: { id },
    });
  }

  /**
   * Assign a role to a user
   */
  async assignRoleToUser(roleId: string, userId: string): Promise<UserRole> {
    return prisma.userRole.create({
      data: {
        role_id: roleId,
        user_id: userId,
      },
    });
  }

  /**
   * Remove a role from a user
   */
  async removeRoleFromUser(roleId: string, userId: string): Promise<UserRole> {
    // First find the user role to delete
    const userRole = await prisma.userRole.findFirst({
      where: {
        role_id: roleId,
        user_id: userId,
      },
    });

    if (!userRole) {
      throw new Error('User role not found');
    }

    // Then delete it by ID
    return prisma.userRole.delete({
      where: {
        id: userRole.id,
      },
    });
  }

  /**
   * Get users by role
   */
  async getUsersByRole(roleId: string) {
    // Get all user roles for this role
    const userRoles = await prisma.userRole.findMany({
      where: { role_id: roleId },
      include: {
        user: true,
      },
    });

    // Extract and return just the users
    return userRoles.map((ur) => ur.user) || [];
  }

  /**
   * Get roles by user
   */
  async getRolesByUser(userId: string): Promise<Role[]> {
    const userRoles = await prisma.userRole.findMany({
      where: { user_id: userId },
      include: {
        role: true,
      },
    });

    return userRoles.map((ur) => ur.role);
  }
}
