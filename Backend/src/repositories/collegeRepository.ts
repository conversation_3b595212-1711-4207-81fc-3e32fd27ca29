// PrismaClient will be used after migration
import { College } from '@prisma/client';

import prisma from '@/lib/prisma';

import BaseRepository from './baseRepository';

// Mock data for colleges until the migration is run
const mockColleges = [
  {
    id: 'college-1',
    name: 'Indian Institute of Technology, Bombay',
    location: 'Mumbai, Maharashtra',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/1/1d/IIT_Bombay_Logo.svg/1200px-IIT_Bombay_Logo.svg.png',
    userCount: 1250,
  },
  {
    id: 'college-2',
    name: 'Indian Institute of Technology, Delhi',
    location: 'New Delhi, Delhi',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/f/fd/Indian_Institute_of_Technology_Delhi_Logo.svg/1200px-Indian_Institute_of_Technology_Delhi_Logo.svg.png',
    userCount: 1100,
  },
  {
    id: 'college-3',
    name: 'Indian Institute of Technology, Madras',
    location: 'Chennai, Tamil Nadu',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/6/69/IIT_Madras_Logo.svg/1200px-IIT_Madras_Logo.svg.png',
    userCount: 980,
  },
  {
    id: 'college-4',
    name: 'Birla Institute of Technology and Science, Pilani',
    location: 'Pilani, Rajasthan',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/d/d3/BITS_Pilani-Logo.svg/1200px-BITS_Pilani-Logo.svg.png',
    userCount: 850,
  },
  {
    id: 'college-5',
    name: 'Vellore Institute of Technology',
    location: 'Vellore, Tamil Nadu',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/4/4e/Vellore_Institute_of_Technology_seal_2017.svg/1200px-Vellore_Institute_of_Technology_seal_2017.svg.png',
    userCount: 780,
  },
];

/**
 * Repository for College entity
 * Extends BaseRepository with College as the entity type
 */
export default class CollegeRepository extends BaseRepository<College> {
  constructor() {
    // Using a dummy model until the migration is run
    super(prisma.college);
  }

  async getTopColleges(limit: number = 10) {
    try {
      // Return mock data until the migration is run
      return mockColleges.slice(0, limit);

      // The following code will be used after the migration is run
      // Get colleges with the most users
      const colleges = await prisma.college.findMany({
        take: limit,
        select: {
          id: true,
          name: true,
          location: true,
          logo_url: true,
          _count: {
            select: {
              users: true,
            },
          },
        },
        orderBy: {
          users: {
            _count: 'desc',
          },
        },
      });

      // Format the data for the frontend
      return colleges.map((college) => ({
        id: college.id,
        name: college.name,
        location: college.location || '',
        logo_url: college.logo_url || '',
        userCount: college._count.users,
      }));
    } catch (error) {
      console.error('Error fetching colleges:', error);
      return mockColleges.slice(0, limit);
    }
  }

  async searchColleges(query: string, limit: number = 10) {
    try {
      // Return filtered mock data until the migration is run
      return mockColleges
        .filter((college) =>
          college.name.toLowerCase().includes(query.toLowerCase()),
        )
        .slice(0, limit);

      // The following code will be used after the migration is run
      return prisma.college.findMany({
        where: {
          name: {
            contains: query,
            mode: 'insensitive',
          },
        },
        take: limit,
        select: {
          id: true,
          name: true,
          location: true,
          logo_url: true,
        },
      });
    } catch (error) {
      console.error('Error searching colleges:', error);
      return mockColleges
        .filter((college) =>
          college.name.toLowerCase().includes(query.toLowerCase()),
        )
        .slice(0, limit);
    }
  }
}
