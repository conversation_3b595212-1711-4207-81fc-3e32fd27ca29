import { ChallengeDiscussion } from '@prisma/client';

import prisma from '@/lib/prisma';

import BaseRepository from './baseRepository';

export interface DiscussionData {
  challenge_id: string;
  user_id: string;
  parent_id?: string;
  content: string;
}

/**
 * Repository for ChallengeDiscussion entity
 * Extends BaseRepository with ChallengeDiscussion as the entity type
 */
export class ChallengeDiscussionRepository extends BaseRepository<ChallengeDiscussion> {
  constructor() {
    super(prisma.challengeDiscussion);
  }

  async createDiscussion(data: DiscussionData): Promise<ChallengeDiscussion> {
    return this.create({
      data: {
        challenge_id: data.challenge_id,
        user_id: data.user_id,
        parent_id: data.parent_id,
        content: data.content,
      },
    });
  }

  async getDiscussionsByChallenge(
    challenge_id: string,
    includeReplies: boolean = true,
  ): Promise<ChallengeDiscussion[]> {
    // Get top-level discussions (no parent)
    const discussions = await this.findMany({
      where: {
        challenge_id,
        parent_id: null, // Only get top-level discussions
      },
      orderBy: {
        created_at: 'desc',
      },
      include: includeReplies
        ? {
            user: {
              select: {
                id: true,
                username: true,
                avatar_url: true,
              },
            },
            replies: {
              include: {
                user: {
                  select: {
                    id: true,
                    username: true,
                    avatar_url: true,
                  },
                },
              },
              orderBy: {
                created_at: 'asc',
              },
            },
          }
        : {
            user: {
              select: {
                id: true,
                username: true,
                avatar_url: true,
              },
            },
          },
    });

    return discussions;
  }

  async getDiscussionById(id: string): Promise<ChallengeDiscussion | null> {
    return this.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            avatar_url: true,
          },
        },
        replies: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                avatar_url: true,
              },
            },
          },
          orderBy: {
            created_at: 'asc',
          },
        },
      },
    });
  }

  async upvoteDiscussion(id: string): Promise<ChallengeDiscussion> {
    return this.update({
      where: { id },
      data: {
        upvotes: {
          increment: 1,
        },
      },
    });
  }

  async downvoteDiscussion(id: string): Promise<ChallengeDiscussion> {
    return this.update({
      where: { id },
      data: {
        downvotes: {
          increment: 1,
        },
      },
    });
  }

  async deleteDiscussion(id: string): Promise<void> {
    await this.delete({
      where: { id },
    });
  }
}

export default ChallengeDiscussionRepository;
