import { ChallengeBoilerplate } from '@prisma/client';

import prisma from '@/lib/prisma';

import BaseRepository from './baseRepository';

/**
 * Repository for ChallengeBoilerplate entity
 * Extends BaseRepository with ChallengeBoilerplate as the entity type
 */
export default class ChallengeBoilerplatesRepository extends BaseRepository<ChallengeBoilerplate> {
  constructor() {
    super(prisma.challengeBoilerplate);
  }

  /**
   * Get all boilerplates for a challenge
   * @param challengeId The ID of the challenge
   */
  async getBoilerplatesByChallenge(challengeId: string) {
    return this.findMany({
      where: { challenge_id: challengeId },
    });
  }

  /**
   * Get a specific boilerplate by language
   * @param challengeId The ID of the challenge
   * @param language The programming language
   */
  async getBoilerplateByLanguage(challengeId: string, language: string) {
    return this.findFirst({
      where: {
        challenge_id: challengeId,
        language,
      },
    });
  }

  /**
   * Create a new boilerplate
   * @param data The boilerplate data
   */
  async createBoilerplate(data: {
    challenge_id: string;
    language: string;
    boilerplate_code: string;
  }) {
    return this.create({
      data,
    });
  }

  /**
   * Update a boilerplate
   * @param id The ID of the boilerplate
   * @param data The updated boilerplate data
   */
  async updateBoilerplate(
    id: string,
    data: {
      boilerplate_code: string;
    },
  ) {
    return this.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a boilerplate
   * @param id The ID of the boilerplate
   */
  async deleteBoilerplate(id: string) {
    return this.delete({
      where: { id },
    });
  }

  /**
   * Check if a boilerplate exists for a language
   * @param challengeId The ID of the challenge
   * @param language The programming language
   */
  async boilerplateExists(challengeId: string, language: string) {
    const boilerplate = await this.findFirst({
      where: {
        challenge_id: challengeId,
        language,
      },
    });
    return boilerplate;
  }
}
