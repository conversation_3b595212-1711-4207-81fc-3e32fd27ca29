/* eslint-disable @typescript-eslint/no-explicit-any */
import { PrismaClient } from '@prisma/client';

import prisma from '@/lib/prisma';
import { PaginatedResult, PaginationParams } from '@/types';

/**
 * Base Repository class that provides common CRUD operations
 * Uses a simplified approach to avoid TypeScript circular reference errors
 */
export default abstract class BaseRepository<T = any> {
  protected prismaClient: PrismaClient;
  protected delegate: any;

  constructor(delegate: any) {
    this.delegate = delegate;
    this.prismaClient = prisma;
  }

  /**
   * Finds a unique record.
   */
  async findUnique(args: any): Promise<T | null> {
    return this.delegate.findUnique(args);
  }

  /**
   * Finds the first record.
   */
  async findFirst(args: any): Promise<T | null> {
    return this.delegate.findFirst(args);
  }

  /**
   * Finds multiple records.
   */
  async findMany(args?: any): Promise<T[]> {
    return this.delegate.findMany(args);
  }

  /**
   * Creates a new record.
   */
  async create(args: any): Promise<T> {
    return this.delegate.create(args);
  }

  /**
   * Creates multiple records.
   */
  async createMany(args: any): Promise<any> {
    return this.delegate.createMany(args);
  }

  /**
   * Updates an existing record.
   */
  async update(args: any): Promise<T> {
    return this.delegate.update(args);
  }

  /**
   * Updates multiple records.
   */
  async updateMany(args: any): Promise<any> {
    return this.delegate.updateMany(args);
  }

  /**
   * Deletes a record.
   */
  async delete(args: any): Promise<T> {
    return this.delegate.delete(args);
  }

  /**
   * Deletes multiple records.
   */
  async deleteMany(args: any): Promise<any> {
    return this.delegate.deleteMany(args);
  }

  /**
   * Upserts a record.
   */
  async upsert(args: any): Promise<T> {
    return this.delegate.upsert(args);
  }

  /**
   * Counts records matching the criteria.
   */
  async count(args?: any): Promise<number> {
    return this.delegate.count(args);
  }

  /**
   * Get records grouped by a specific field.
   */
  async groupBy(args: any): Promise<any> {
    return this.delegate.groupBy(args);
  }

  /**
   * Paginates records with support for search, filtering, and sorting.
   *
   * @param options - Pagination options (page, limit, search, filter, sort)
   * @param searchFields - List of fields to perform text search on
   * @param selection - Additional query selections (include or select)
   * @param whereClause - Base filtering conditions
   */
  async paginate(
    options: PaginationParams,
    searchFields?: string[],
    selection?: { include?: Record<string, any>; select?: Record<string, any> },
    whereClause: Record<string, any> = {},
  ): Promise<PaginatedResult<T>> {
    const page = options.page || 1;
    const limit = options.limit || 10;
    const skip = (page - 1) * limit;
    let finalWhere = { ...whereClause };

    // If search text is provided and search fields exist, add search conditions.
    if (options.search && searchFields && searchFields.length > 0) {
      const searchConditions = searchFields.map((field) => ({
        [field]: {
          contains: options.search,
          mode: 'insensitive',
        },
      }));
      finalWhere = { ...finalWhere, OR: searchConditions };
    }

    // Merge additional filters if provided.
    if (options.filter) {
      finalWhere = {
        ...finalWhere,
        AND: [
          ...(finalWhere.AND || []),
          ...Object.entries(options.filter).map(([key, value]) => ({
            [key]: value,
          })),
        ],
      };
    }

    const [total, data] = await Promise.all([
      this.delegate.count({ where: finalWhere }),
      this.delegate.findMany({
        where: finalWhere,
        skip,
        take: limit,
        orderBy: options.sort
          ? { [options.sort.field]: options.sort.direction }
          : undefined,
        ...selection,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        total,
        currentPage: page,
        totalPages,
        limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
}
