/**
 * Analytics Repository
 * Handles database operations for analytics data
 */
import {
  AnalyticsReport,
  AnalyticsSetting,
  PrismaClient,
} from '@prisma/client';

const prisma = new PrismaClient();

export class AnalyticsRepository {
  /**
   * Get all reports for a user
   * @param userId User ID
   * @returns List of analytics reports
   */
  async getReports(userId: string): Promise<AnalyticsReport[]> {
    return prisma.analyticsReport.findMany({
      where: { user_id: userId },
      orderBy: { created_at: 'desc' },
    });
  }

  /**
   * Create a new report
   * @param data Report data
   * @returns Created report
   */
  async createReport(data: any): Promise<AnalyticsReport> {
    return prisma.analyticsReport.create({
      data,
    });
  }

  /**
   * Get a report by ID
   * @param id Report ID
   * @param userId User ID (for authorization)
   * @returns Report or null if not found
   */
  async getReportById(
    id: string,
    userId: string,
  ): Promise<AnalyticsReport | null> {
    return prisma.analyticsReport.findFirst({
      where: {
        id,
        user_id: userId,
      },
    });
  }

  /**
   * Update a report
   * @param id Report ID
   * @param data Updated report data
   * @returns Updated report
   */
  async updateReport(id: string, data: any): Promise<AnalyticsReport> {
    return prisma.analyticsReport.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete a report
   * @param id Report ID
   * @returns Deleted report
   */
  async deleteReport(id: string): Promise<AnalyticsReport> {
    return prisma.analyticsReport.delete({
      where: { id },
    });
  }

  /**
   * Get analytics settings
   * @param userId User ID
   * @returns Analytics settings or null if not found
   */
  async getSettings(userId: string): Promise<AnalyticsSetting | null> {
    return prisma.analyticsSetting.findFirst({
      where: { key: `user_settings_${userId}` },
    });
  }

  /**
   * Create or update analytics settings
   * @param userId User ID
   * @param data Settings data
   * @returns Updated settings
   */
  async upsertSettings(userId: string, data: any): Promise<AnalyticsSetting> {
    const settingsKey = `user_settings_${userId}`;
    return prisma.analyticsSetting.upsert({
      where: {
        key: settingsKey,
      },
      update: data,
      create: {
        ...data,
        key: settingsKey,
      },
    });
  }
}

export default AnalyticsRepository;
