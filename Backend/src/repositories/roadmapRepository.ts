import { Request } from 'express';

import { Prisma } from '@prisma/client';

import { invalidateCachePattern } from '@/services/cacheService';
import {
  CommentData,
  ConceptData,
  PaginatedResult,
  ResourceStats,
  RoadmapData,
  SubjectData,
  SubjectOrder,
  TopicData,
} from '@/types';

import prisma from '../lib/prisma';
import { createAppError } from '../utils/errorHandler';
import BaseRepository from './baseRepository';

/**
 * Repository for Roadmap entity
 * Handles CRUD operations and specialized queries for roadmaps
 */
export default class RoadmapRepository extends BaseRepository<
  typeof prisma.roadmap
> {
  constructor() {
    super(prisma.roadmap);
  }

  async createRoadmap(data: RoadmapData) {
    try {
      return await prisma.$transaction(async (tx) => {
        const roadmap = await tx.roadmap.create({
          data: {
            title: data.title,
            description: data.description,
            user: { connect: { id: data.author_id } },
            is_public: data.is_public ?? false,
          },
        });

        if (data.concepts) {
          for (const concept of data.concepts) {
            const mainConcept = await tx.mainConcept.create({
              data: {
                name: concept.title,
                description: concept.description,
                order: concept.order,
              },
            });

            await tx.roadmapMainConcept.create({
              data: {
                roadmap_id: roadmap.id,
                main_concept_id: mainConcept.id,
                order: concept.order,
              },
            });

            if (concept.subjects) {
              for (const subjectData of concept.subjects) {
                const subject = await tx.subject.create({
                  data: {
                    title: subjectData.title,
                    description: subjectData.description,
                    order: subjectData.order,
                  },
                });

                await tx.mainConceptSubject.create({
                  data: {
                    main_concept_id: mainConcept.id,
                    subject_id: subject.id,
                    order: subjectData.order,
                  },
                });

                if (subjectData.topics) {
                  for (const topicData of subjectData.topics) {
                    const topic = await tx.topic.create({
                      data: {
                        title: topicData.title,
                        description: topicData.description,
                        order: topicData.order,
                        content: topicData.content,
                        resources: topicData.resources,
                        prerequisites: topicData.prerequisites,
                      },
                    });

                    await tx.subjectTopic.create({
                      data: {
                        subject_id: subject.id,
                        topic_id: topic.id,
                        order: topicData.order,
                      },
                    });
                  }
                }
              }
            }
          }
        }

        return roadmap;
      });
    } catch (error) {
      throw createAppError(
        'Failed to create roadmap',
        500,
        error as Record<string, unknown>,
      );
    }
  }

  async getRoadmap(id: string, userId?: string) {
    const roadmap = await prisma.roadmap.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            full_name: true,
            avatar_url: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        main_concepts: {
          orderBy: { order: 'asc' },
          include: {
            main_concept: {
              include: {
                subjects: {
                  orderBy: { order: 'asc' },
                  include: {
                    subject: {
                      include: {
                        topics: {
                          orderBy: { order: 'asc' },
                          include: {
                            topic: {
                              include: {
                                articles: true,
                                quizzes: true,
                                challenges: true,
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        topics: {
          select: {
            topic: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        },
        _count: {
          select: {
            likes: true,
            comments: {
              where: {
                parent_id: null,
              },
            },
            user_roadmaps: true,
            topics: true,
          },
        },
        likes: userId
          ? {
              where: {
                user_id: userId,
              },
              select: {
                id: true,
              },
            }
          : false,
        user_roadmaps: userId
          ? {
              where: {
                user_id: userId,
              },
              select: {
                id: true,
              },
            }
          : false,
      },
    });

    if (!roadmap) {
      throw createAppError('Roadmap not found', 404);
    }

    // Calculate progress based on user's progress in topics
    let progress = 0;
    if (userId && roadmap.user_roadmaps && roadmap.user_roadmaps.length > 0) {
      // Get user's progress for this roadmap's topics
      const userProgress = await prisma.userProgress.findMany({
        where: {
          user_id: userId,
          topic_id: {
            in: roadmap.topics.map((t) => t.topic.id),
          },
        },
        select: {
          is_completed: true,
        },
      });

      const completedTopics = userProgress.filter((p) => p.is_completed).length;
      progress =
        roadmap.topics.length > 0
          ? Math.round((completedTopics / roadmap.topics.length) * 100)
          : 0;
    }

    return {
      ...roadmap,
      likesCount: roadmap._count.likes,
      commentsCount: roadmap._count.comments,
      bookmarksCount: roadmap._count.user_roadmaps,
      isLiked: Boolean(roadmap.likes?.length),
      isBookmarked: Boolean(roadmap.user_roadmaps?.length),
      steps: roadmap._count.topics,
      isEnrolled: Boolean(roadmap.user_roadmaps?.length),
      progress: progress,
      estimatedTime: roadmap.estimatedHours
        ? `${roadmap.estimatedHours} hours`
        : undefined,
      isFeatured: roadmap.popularity > 100,
    };
  }

  async getUserRoadmap(id: string, user_id: string) {
    const roadmap = await prisma.roadmap.findUnique({
      where: { id },
      include: {
        user_roadmaps: {
          where: {
            user_id,
          },
        },
      },
    });

    if (!roadmap) {
      throw createAppError('Roadmap not found', 404);
    }

    return roadmap;
  }

  async updateRoadmap(id: string, data: Partial<RoadmapData>) {
    const updated = await prisma.roadmap.update({
      where: { id },
      data: {
        title: data.title,
        description: data.description,
        is_public: data.is_public,
        is_featured: data.is_featured,
      },
      include: {
        main_concepts: {
          select: {
            main_concept: {
              include: {
                subjects: {
                  select: {
                    subject: {
                      include: {
                        topics: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return updated;
  }

  async addConcept(roadmap_id: string, data: ConceptData) {
    return await prisma.$transaction(async (tx) => {
      const mainConcept = await tx.mainConcept.create({
        data: {
          name: data.title,
          description: data.description,
          order: data.order,
        },
      });

      await tx.roadmapMainConcept.create({
        data: {
          roadmap_id,
          main_concept_id: mainConcept.id,
          order: data.order,
        },
      });

      if (data.subjects) {
        for (const subjectData of data.subjects) {
          const subject = await tx.subject.create({
            data: {
              title: subjectData.title,
              description: subjectData.description,
              order: subjectData.order,
            },
          });

          await tx.mainConceptSubject.create({
            data: {
              main_concept_id: mainConcept.id,
              subject_id: subject.id,
              order: subjectData.order,
            },
          });

          if (subjectData.topics) {
            for (const topicData of subjectData.topics) {
              const topic = await tx.topic.create({
                data: {
                  title: topicData.title,
                  description: topicData.description,
                  order: topicData.order,
                  content: topicData.content,
                  resources: topicData.resources,
                  prerequisites: topicData.prerequisites,
                },
              });

              await tx.subjectTopic.create({
                data: {
                  subject_id: subject.id,
                  topic_id: topic.id,
                  order: topicData.order,
                },
              });
            }
          }
        }
      }

      return mainConcept;
    });
  }

  async addSubject(main_concept_id: string, data: SubjectData) {
    const result = await prisma.$transaction(async (tx) => {
      const subject = await tx.subject.create({
        data: {
          title: data.title,
          description: data.description,
          order: data.order,
          main_concepts: { connect: { id: main_concept_id } },
        },
      });

      if (data.topics) {
        for (const topicData of data.topics) {
          const topic = await tx.topic.create({
            data: {
              title: topicData.title,
              description: topicData.description,
              order: topicData.order,
              content: topicData.content,
              resources: topicData.resources,
              prerequisites: topicData.prerequisites,
            },
          });

          await tx.subjectTopic.create({
            data: {
              subject_id: subject.id,
              topic_id: topic.id,
              order: topicData.order,
            },
          });
        }
      }

      return subject;
    });

    return result;
  }

  async addTopic(subject_id: string, data: TopicData) {
    const topic = await prisma.topic.create({
      data: {
        title: data.title,
        description: data.description,
        order: data.order,
        content: data.content,
        resources: data.resources,
        prerequisites: data.prerequisites,
        subjects: {
          create: {
            subject_id: subject_id,
            order: data.order,
          },
        },
      },
    });

    return topic;
  }

  async updateTopicContent(
    topicId: string,
    content: string,
    resources?: string[],
  ) {
    const topic = await prisma.topic.update({
      where: { id: topicId },
      data: {
        content,
        resources,
      },
    });

    return topic;
  }

  async linkContent(
    topicId: string,
    data: {
      articleIds?: string[];
      quizIds?: string[];
      challengeIds?: string[];
    },
  ) {
    const topic = await prisma.topic.update({
      where: { id: topicId },
      data: {
        articles: {
          connect: data.articleIds?.map((id) => ({ id })),
        },
        quizzes: {
          connect: data.quizIds?.map((id) => ({ id })),
        },
        challenges: {
          connect: data.challengeIds?.map((id) => ({ id })),
        },
      },
      include: {
        articles: true,
        quizzes: true,
        challenges: true,
      },
    });

    return topic;
  }

  async trackProgress(userId: string, topicId: string, completed: boolean) {
    const progress = await prisma.userProgress.upsert({
      where: {
        user_id_topic_id: {
          user_id: userId,
          topic_id: topicId,
        },
      },
      update: {
        is_completed: completed,
        completed_at: completed ? new Date() : null,
      },
      create: {
        user_id: userId,
        topic_id: topicId,
        subject_id: '',
        is_completed: completed,
        completed_at: completed ? new Date() : null,
      },
    });

    return progress;
  }

  async getAllRoadmaps(req: Request, where?: Prisma.RoadmapWhereInput) {
    const {
      limit = 10,
      page = 1,
      search = '',
      category,
      difficulty,
      sort = 'popular', // Default sort
    } = req.query;
    const userId = req.user?.id;

    // Build where clause with additional filters
    const whereClause: Prisma.RoadmapWhereInput = {
      ...where,
      ...(category ? { category_id: String(category) } : {}),
      ...(difficulty
        ? { difficulty: String(difficulty) as 'EASY' | 'MEDIUM' | 'HARD' }
        : {}),
    };

    // Combine the where clause with the existing where parameter
    const combinedWhere: Prisma.RoadmapWhereInput = {
      ...where,
      ...whereClause,
    };

    // Determine sort options based on sort parameter
    let sortOptions: { field: string; direction: 'asc' | 'desc' } | undefined;
    switch (sort) {
      case 'popular':
        sortOptions = { field: 'popularity', direction: 'desc' };
        break;
      case 'recent':
        sortOptions = { field: 'created_at', direction: 'desc' };
        break;
      case 'rating':
        // For rating, we'll need to handle this differently as it's a relation
        // We'll use popularity as a fallback
        sortOptions = { field: 'popularity', direction: 'desc' };
        break;
      default:
        sortOptions = { field: 'popularity', direction: 'desc' };
    }

    const roadmaps = (await this.paginate(
      {
        limit: Number(limit),
        page: Number(page),
        search: String(search),
        sort: sortOptions,
      },
      ['title'],
      {
        include: {
          user: {
            select: {
              id: true,
              username: true,
              full_name: true,
              avatar_url: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          main_concepts: {
            select: {
              main_concept: {
                include: {
                  subjects: true,
                },
              },
            },
          },
          topics: {
            select: {
              topic: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
          _count: {
            select: {
              likes: true,
              comments: {
                where: {
                  parent_id: null,
                },
              },
              user_roadmaps: true,
              topics: true,
            },
          },
          likes: userId
            ? {
                where: {
                  user_id: userId,
                },
                select: {
                  id: true,
                },
              }
            : false,
          user_roadmaps: userId
            ? {
                where: {
                  user_id: userId,
                },
                select: {
                  id: true,
                },
              }
            : false,
        },
      },
      combinedWhere,
    )) as unknown as {
      data: Array<
        any & {
          id: string;
          _count: {
            likes: number;
            comments: number;
            user_roadmaps: number;
            topics: number;
          };
          likes?: Array<{ id: string }>;
          user_roadmaps?: Array<{ id: string }>;
          topics: Array<{ topic: { id: string; title: string } }>;
        }
      >;
      meta: any;
    };

    // If user is logged in, get progress for all roadmaps
    let userProgressMap: Record<string, number> = {};
    if (userId) {
      // Get all roadmap IDs
      const roadmapIds = roadmaps.data.map((roadmap) => roadmap.id);

      // Get all topic IDs for these roadmaps
      const roadmapTopicIds = roadmaps.data.flatMap((roadmap) =>
        roadmap.topics.map((t: { topic: { id: string } }) => t.topic.id),
      );

      // Get user's progress for all topics
      const userProgress = await prisma.userProgress.findMany({
        where: {
          user_id: userId,
          topic_id: {
            in: roadmapTopicIds,
          },
        },
        select: {
          topic_id: true,
          is_completed: true,
        },
      });

      // Create a map of topic ID to completion status
      const topicCompletionMap: Record<string, boolean> = {};
      userProgress.forEach((progress) => {
        if (progress.topic_id) {
          topicCompletionMap[progress.topic_id] = progress.is_completed;
        }
      });

      // Calculate progress for each roadmap
      roadmaps.data.forEach((roadmap) => {
        if (roadmap.id) {
          const roadmapTopics = roadmap.topics.map(
            (t: { topic: { id: string } }) => t.topic.id,
          );
          const completedTopics = roadmapTopics.filter(
            (topicId: string) => topicCompletionMap[topicId] === true,
          ).length;

          userProgressMap[roadmap.id] =
            roadmapTopics.length > 0
              ? Math.round((completedTopics / roadmapTopics.length) * 100)
              : 0;
        }
      });
    }

    return {
      ...roadmaps,
      data: roadmaps.data.map((roadmap) => ({
        ...roadmap,
        likesCount: roadmap._count.likes,
        commentsCount: roadmap._count.comments,
        bookmarksCount: roadmap._count.user_roadmaps,
        isLiked: Boolean(roadmap.likes?.length),
        isBookmarked: Boolean(roadmap.user_roadmaps?.length),
        steps: roadmap._count.topics,
        isEnrolled: Boolean(roadmap.user_roadmaps?.length),
        progress: roadmap.id ? userProgressMap[roadmap.id] || 0 : 0,
        estimatedTime: roadmap.estimatedHours
          ? `${roadmap.estimatedHours} hours`
          : undefined,
        isFeatured: roadmap.popularity > 100,
      })),
    };
  }

  async deleteRoadmap(id: string): Promise<void> {
    await prisma.roadmap.delete({
      where: { id },
    });
  }

  async updateSubjectsOrder(
    roadmap_id: string,
    subject_orders: SubjectOrder[],
  ): Promise<void> {
    await prisma.$transaction(
      subject_orders.map((order) =>
        prisma.mainConceptSubject.updateMany({
          where: { subject_id: order.subject_id },
          data: { order: order.order },
        }),
      ),
    );
  }

  async createCustomRoadmap(data: RoadmapData & { sourceRoadmapId?: string }) {
    const roadmap = await prisma.$transaction(async (tx) => {
      const newRoadmap = await tx.roadmap.create({
        data: {
          title: data.title,
          description: data.description,
          user: { connect: { id: data.author_id } },
          is_public: data.is_public ?? false,
        },
      });

      if (data.sourceRoadmapId) {
        const sourceRoadmap = await tx.roadmap.findUnique({
          where: { id: data.sourceRoadmapId },
          include: {
            main_concepts: {
              select: {
                main_concept: {
                  include: {
                    subjects: {
                      select: {
                        order: true,
                        subject: {
                          include: {
                            topics: {
                              select: {
                                topic: true,
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        });

        if (!sourceRoadmap) {
          throw createAppError('Source roadmap not found', 404);
        }

        for (const main_concept of sourceRoadmap.main_concepts) {
          await tx.mainConcept.create({
            data: {
              name: main_concept.main_concept?.name,
              description: main_concept.main_concept?.description,
              order: main_concept.main_concept?.order,
            },
          });
          for (const subject of main_concept.main_concept?.subjects ?? []) {
            await tx.subject.create({
              data: {
                title: subject.subject?.title,
                description: subject.subject?.description,
                order: subject.subject?.order,
                main_concepts: {
                  create: {
                    main_concept_id: main_concept.main_concept?.id,
                    order: subject.order,
                  },
                },
              },
            });
            if (subject.subject?.topics) {
              const subjectTopics = subject.subject?.topics?.map((topic) => ({
                subject_id: subject.subject?.id || '',
                topic_id: topic.topic?.id || '',
                order: topic.topic?.order || 0,
              }));
              await tx.subjectTopic.createMany({
                data: subjectTopics,
              });
            }
          }
        }
      }

      return newRoadmap;
    });

    return roadmap;
  }

  async manageRoadmap(roadmap_id: string, action: string) {
    const roadmap = await this.findUnique({
      where: { id: roadmap_id },
      include: { topics: true },
    });

    if (!roadmap) throw createAppError('Roadmap not found', 404);

    switch (action) {
      case 'publish':
        await this.update({
          where: { id: roadmap_id },
          data: { is_public: true },
        });
        break;
      case 'unpublish':
        await this.update({
          where: { id: roadmap_id },
          data: { is_public: false },
        });
        break;
      case 'delete':
        await this.delete({ where: { id: roadmap_id } });
        break;
      default:
        throw createAppError('Invalid action', 400);
    }

    await invalidateCachePattern(`roadmap:${roadmap_id}:*`);
  }

  async getRoadmapStats(): Promise<ResourceStats> {
    const [total, active, pending, reported] = await Promise.all([
      prisma.roadmap.count(),
      prisma.roadmap.count({ where: { is_public: true } }),
      prisma.roadmap.count({ where: { is_public: false } }),
      prisma.contentReport.count({
        where: { content_type: 'roadmap' },
      }),
    ]);

    return { total, active, pending, reported };
  }

  async addComment(data: CommentData) {
    return prisma.comment.create({
      data: {
        content: data.content,
        user_id: data.user_id,
        roadmap_id: data.roadmap_id,
        parent_id: data.parent_id,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            full_name: true,
            avatar_url: true,
          },
        },
        replies: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                full_name: true,
                avatar_url: true,
              },
            },
          },
        },
        _count: {
          select: {
            likes: true,
            replies: true,
          },
        },
      },
    });
  }

  async getRoadmapComments(roadmap_id: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;
    const [total, comments] = await Promise.all([
      prisma.comment.count({
        where: {
          roadmap_id,
          parent_id: null,
        },
      }),
      prisma.comment.findMany({
        where: {
          roadmap_id,
          parent_id: null,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              full_name: true,
              avatar_url: true,
            },
          },
          replies: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  full_name: true,
                  avatar_url: true,
                },
              },
              _count: {
                select: {
                  likes: true,
                },
              },
            },
          },
          _count: {
            select: {
              likes: true,
              replies: true,
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
        skip,
        take: limit,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: comments,
      meta: {
        total,
        currentPage: page,
        totalPages,
        limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async toggleLike(user_id: string, roadmap_id: string): Promise<void> {
    const existingLike = await prisma.like.findUnique({
      where: {
        user_id_roadmap_id: {
          user_id,
          roadmap_id,
        },
      },
    });

    if (existingLike) {
      await prisma.like.delete({
        where: {
          user_id_roadmap_id: {
            user_id,
            roadmap_id,
          },
        },
      });
    } else {
      await prisma.like.create({
        data: {
          user_id,
          roadmap_id,
        },
      });
    }
  }

  async getRecommendedRoadmaps(user_id: string) {
    const userInterests = await prisma.userRoadmap.findMany({
      where: { user_id },
      include: {
        roadmap: {
          include: {
            main_concepts: {
              include: {
                main_concept: {
                  include: {
                    subjects: {
                      include: {
                        subject: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    const interests = new Set<string>();
    userInterests.forEach((ur) => {
      ur.roadmap.main_concepts.forEach((concept) => {
        concept.main_concept.subjects.forEach((subject) => {
          interests.add(subject.subject.title.toLowerCase());
        });
      });
    });

    return this.findMany({
      where: {
        is_public: true,
        NOT: {
          id: {
            in: userInterests.map((ur) => ur.roadmap_id),
          },
        },
        main_concepts: {
          some: {
            main_concept: {
              subjects: {
                some: {
                  subject: {
                    title: {
                      in: Array.from(interests),
                      mode: 'insensitive',
                    },
                  },
                },
              },
            },
          },
        },
      },
      include: {
        user: {
          select: {
            username: true,
            avatar_url: true,
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
          },
        },
      },
      orderBy: {
        likes: {
          _count: 'desc',
        },
      },
      take: 10,
    });
  }

  async getTrendingRoadmaps() {
    // Get roadmaps with the most engagement in the last 60 days
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    // First, get roadmaps with recent activity
    const roadmaps = await prisma.roadmap.findMany({
      where: {
        is_public: true,
        OR: [
          {
            likes: {
              some: {
                created_at: {
                  gte: sixtyDaysAgo,
                },
              },
            },
          },
          {
            comments: {
              some: {
                created_at: {
                  gte: sixtyDaysAgo,
                },
              },
            },
          },
          {
            user_roadmaps: {
              some: {
                created_at: {
                  gte: sixtyDaysAgo,
                },
              },
            },
          },
        ],
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            full_name: true,
            avatar_url: true,
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
            user_roadmaps: true,
            topics: true,
          },
        },
        likes: {
          take: 1,
          select: {
            id: true,
          },
        },
        user_roadmaps: {
          take: 1,
          select: {
            id: true,
          },
        },
      },
      take: 10,
    });

    // Calculate an engagement score for each roadmap
    const roadmapsWithScore = roadmaps.map((roadmap) => {
      // Calculate engagement score: likes (3 points) + comments (2 points) + bookmarks (1 point)
      const engagementScore =
        roadmap._count.likes * 3 +
        roadmap._count.comments * 2 +
        roadmap._count.user_roadmaps * 1;

      return {
        ...roadmap,
        likesCount: roadmap._count.likes,
        commentsCount: roadmap._count.comments,
        bookmarksCount: roadmap._count.user_roadmaps,
        isLiked: Boolean(roadmap.likes?.length),
        isBookmarked: Boolean(roadmap.user_roadmaps?.length),
        steps: roadmap._count.topics,
        engagementScore,
        estimatedTime: roadmap.estimatedHours
          ? `${roadmap.estimatedHours} hours`
          : undefined,
        isFeatured: roadmap.popularity > 100,
      };
    });

    // Sort by engagement score (highest first)
    return roadmapsWithScore.sort(
      (a, b) => b.engagementScore - a.engagementScore,
    );
  }

  async getEngagementMetrics(roadmap_id: string) {
    const [likes, comments, saves] = await Promise.all([
      prisma.like.count({
        where: { roadmap_id },
      }),
      prisma.comment.count({
        where: { roadmap_id },
      }),
      prisma.userRoadmap.count({
        where: { roadmap_id },
      }),
    ]);

    return {
      likes,
      comments,
      saves,
      engagementScore: likes * 2 + comments * 3 + saves * 5,
    };
  }

  async shareRoadmap(roadmapId: string, platform: string) {
    const baseUrl = process.env.FRONTEND_URL;
    const roadmapUrl = `${baseUrl}/roadmaps/${roadmapId}`;

    const sharingUrls = {
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(roadmapUrl)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(roadmapUrl)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(roadmapUrl)}`,
    };

    return sharingUrls[platform as keyof typeof sharingUrls] || roadmapUrl;
  }

  /**
   * Bulk update status for multiple roadmaps
   * @param roadmapIds Array of roadmap IDs to update
   * @param status New status to set (ACTIVE, INACTIVE, DRAFT)
   * @returns Object with count of updated records
   */
  async bulkUpdateStatus(roadmapIds: string[], status: string) {
    // Validate status
    const validStatus = status.toUpperCase();
    if (!['ACTIVE', 'INACTIVE', 'DRAFT'].includes(validStatus)) {
      throw new Error('Invalid status value');
    }

    // Update all roadmaps with the provided IDs
    const result = await prisma.roadmap.updateMany({
      where: {
        id: {
          in: roadmapIds
        }
      },
      data: {
        status: validStatus,
        updated_at: new Date()
      }
    });

    // Invalidate cache for each roadmap
    for (const id of roadmapIds) {
      await invalidateCachePattern(`roadmap:${id}:*`);
    }

    return { count: result.count };
  }

  /**
   * Get roadmaps for admin dashboard with pagination and filtering
   * @param options Pagination and filter options
   * @returns Paginated roadmaps with metadata
   */
  async getAdminRoadmaps(options: {
    page: number;
    perPage: number;
    filter?: any;
  }) {
    const { page, perPage, filter = {} } = options;
    const skip = (page - 1) * perPage;

    // Create where clause based on filters
    const where: Prisma.RoadmapWhereInput = { ...filter };

    // Handle status filter if provided
    if (filter.status && filter.status !== 'all') {
      where.status = filter.status.toUpperCase();
    }

    // Get total count for pagination
    const total = await prisma.roadmap.count({ where });

    // Get roadmaps with pagination
    const roadmaps = await prisma.roadmap.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            full_name: true,
            avatar_url: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            likes: true,
            comments: true,
            user_roadmaps: true,
            topics: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
      skip,
      take: perPage,
    });

    // Format roadmaps with additional metadata
    const formattedRoadmaps = roadmaps.map((roadmap) => ({
      ...roadmap,
      likesCount: roadmap._count.likes,
      commentsCount: roadmap._count.comments,
      enrollmentsCount: roadmap._count.user_roadmaps,
      topicsCount: roadmap._count.topics,
      estimatedTime: roadmap.estimatedHours
        ? `${roadmap.estimatedHours} hours`
        : undefined,
    }));

    // Return paginated result
    return {
      data: formattedRoadmaps,
      meta: {
        total,
        per_page: perPage,
        current_page: page,
        last_page: Math.ceil(total / perPage) || 1,
      },
    };
  }
}
