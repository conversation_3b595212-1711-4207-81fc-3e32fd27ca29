import { Challenge, UserChallengeBookmark } from '@prisma/client';

import prisma from '@/lib/prisma';

import { createAppError } from '../utils/errorHandler';
import BaseRepository from './baseRepository';

/**
 * Repository for UserChallengeBookmark entity
 * Handles CRUD operations and specialized queries for userChallengeBookmarks
 */
export class UserChallengeBookmarkRepository extends BaseRepository<UserChallengeBookmark> {
  constructor() {
    super(prisma.userChallengeBookmark);
  }

  /**
   * Get all bookmarked challenges for a user
   * @param userId - The ID of the user
   * @returns An array of bookmarked challenges with challenge details
   */
  async getBookmarkedChallenges(
    userId: string,
  ): Promise<(UserChallengeBookmark & { challenge: Challenge })[]> {
    return prisma.userChallengeBookmark.findMany({
      where: { user_id: userId },
      include: { challenge: true },
    });
  }

  /**
   * Check if a challenge is bookmarked by a user
   * @param userId - The ID of the user
   * @param challengeId - The ID of the challenge
   * @returns The bookmark if found, null otherwise
   */
  async getBookmark(
    userId: string,
    challengeId: string,
  ): Promise<UserChallengeBookmark | null> {
    return prisma.userChallengeBookmark.findFirst({
      where: {
        user_id: userId,
        challenge_id: challengeId,
      },
    });
  }

  /**
   * Bookmark a challenge for a user
   * @param userId - The ID of the user
   * @param challengeId - The ID of the challenge
   * @returns The created bookmark
   */
  async bookmarkChallenge(
    userId: string,
    challengeId: string,
  ): Promise<UserChallengeBookmark> {
    // Check if challenge exists
    const challenge = await prisma.challenge.findUnique({
      where: { id: challengeId },
    });

    if (!challenge) {
      throw createAppError('Challenge not found', 404);
    }

    // Check if already bookmarked
    const existingBookmark = await this.getBookmark(userId, challengeId);

    if (existingBookmark) {
      throw createAppError('Challenge already bookmarked', 409);
    }

    return prisma.userChallengeBookmark.create({
      data: {
        user_id: userId,
        challenge_id: challengeId,
      },
    });
  }

  /**
   * Remove a bookmark
   * @param userId - The ID of the user
   * @param challengeId - The ID of the challenge
   * @returns The deleted bookmark
   */
  async removeBookmark(
    userId: string,
    challengeId: string,
  ): Promise<UserChallengeBookmark> {
    const bookmark = await this.getBookmark(userId, challengeId);

    if (!bookmark) {
      throw createAppError('Bookmark not found', 404);
    }

    return prisma.userChallengeBookmark.delete({
      where: { id: bookmark.id },
    });
  }

  /**
   * Get bookmarked challenge IDs for a user
   * @param userId - The ID of the user
   * @returns An array of bookmarked challenge IDs
   */
  async getBookmarkedChallengeIds(userId: string): Promise<string[]> {
    const bookmarks = await prisma.userChallengeBookmark.findMany({
      where: { user_id: userId },
      select: { challenge_id: true },
    });

    return bookmarks.map((b) => b.challenge_id);
  }

  /**
   * Get bookmark count for a challenge
   * @param challengeId - The ID of the challenge
   * @returns The number of bookmarks for the challenge
   */
  async getBookmarkCount(challengeId: string): Promise<number> {
    return prisma.userChallengeBookmark.count({
      where: { challenge_id: challengeId },
    });
  }
}

export default new UserChallengeBookmarkRepository();
