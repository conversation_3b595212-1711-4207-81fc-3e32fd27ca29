import {
  Battle,
  BattleAnswer,
  BattleLeaderboard,
  BattleParticipant,
  BattleStatus,
  BattleType,
  Difficulty,
  Length,
  Prisma,
  PrismaClient,
  QuizQuestion,
} from '@prisma/client';

import prisma from '@/lib/prisma';
import { deleteCache, getOrSetCache } from '@/services/cacheService';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

interface BattleListMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  cacheControl?: string;
  etag?: string;
}

interface BattleListResponse {
  data: any[];
  meta: BattleListMeta;
}

/**
 * Repository for Battle entity
 * Extends BaseRepository with Battle as the entity type
 */
export class BattleRepository extends BaseRepository<Battle> {
  private static readonly CACHE_TTL = 3600; // 1 hour
  private static readonly BATTLE_CACHE_PREFIX = 'battle:';
  private static readonly BATTLE_LIST_CACHE_PREFIX = 'battle:list:';
  private static readonly BATTLE_STATS_CACHE_PREFIX = 'battle:stats:';
  private prisma: PrismaClient;

  constructor() {
    super(prisma.battle);
    this.prisma = new PrismaClient();
  }

  /**
   * Get battle zone statistics
   * @param userId Optional user ID to get user-specific statistics
   * @returns Battle zone statistics including active battles, upcoming battles, total participants, and win rate
   */
  /**
   * Clear the battle zone statistics cache
   * @param userId Optional user ID to clear user-specific cache
   */
  async clearBattleZoneStatisticsCache(userId?: string): Promise<void> {
    try {
      if (userId) {
        // Clear user-specific cache
        await deleteCache(`${BattleRepository.BATTLE_STATS_CACHE_PREFIX}${userId}`);
      } else {
        // Clear global cache
        await deleteCache(`${BattleRepository.BATTLE_STATS_CACHE_PREFIX}global`);
      }
      logger.info(`Cleared battle zone statistics cache for ${userId || 'global'}`);
    } catch (error) {
      logger.error('Error clearing battle zone statistics cache:', error);
    }
  }

  /**
   * Get battle zone statistics
   * @param userId Optional user ID to get user-specific statistics
   * @returns Battle zone statistics including active battles, upcoming battles, total participants, and win rate
   */
  async getBattleZoneStatistics(userId?: string): Promise<{
    activeBattles: number;
    upcomingBattles: number;
    totalParticipants: number;
    winRate: number;
  }> {
    try {
      // Try to get from cache first
      const cacheKey = `${BattleRepository.BATTLE_STATS_CACHE_PREFIX}${userId || 'global'}`;
      const cachedStats = await getOrSetCache(cacheKey, async () => {
        // Active battles count (IN_PROGRESS)
        const activeBattlesCount = await this.prisma.battle.count({
          where: {
            status: BattleStatus.IN_PROGRESS,
            ...(userId ? { user_id: userId } : {}),
          },
        });

        // Upcoming battles count (UPCOMING)
        const upcomingBattlesCount = await this.prisma.battle.count({
          where: {
            status: BattleStatus.UPCOMING,
            ...(userId ? { user_id: userId } : {}),
          },
        });

        // Total participants across all battles
        // Get the count of unique participants
        const totalParticipants = await this.prisma.$queryRaw<[{count: number}]>`
          SELECT COUNT(DISTINCT user_id) as count 
          FROM battle_participant
          ${userId ? Prisma.sql`WHERE battle_id IN (SELECT id FROM battle WHERE user_id = ${userId})` : Prisma.empty}
        `;
        
        const uniqueParticipantsCount = Number(totalParticipants[0]?.count || 0);

        // Win rate calculation
        let winRate = 0;
        if (userId) {
          // For a specific user
          const totalBattles = await this.prisma.battleParticipant.count({
            where: {
              user_id: userId,
              battle: {
                status: BattleStatus.COMPLETED,
              },
            },
          });

          // Check if is_winner field exists in schema, otherwise use rank = 1 as a winner
          const wonBattles = await this.prisma.battleParticipant.count({
            where: {
              user_id: userId,
              rank: 1, // Assuming rank 1 means winner
              battle: {
                status: BattleStatus.COMPLETED,
              },
            },
          });

          winRate = totalBattles > 0 ? Math.round((wonBattles / totalBattles) * 100) : 0;
        } else {
          // For global win rate, we'll calculate the average win rate of all users
          // First, get all completed battles
          const completedBattles = await this.prisma.battle.findMany({
            where: {
              status: BattleStatus.COMPLETED,
            },
            select: {
              id: true,
            },
          });
          
          // If there are no completed battles, win rate is 0
          if (completedBattles.length === 0) {
            winRate = 0;
          } else {
            // Get all participants in completed battles
            const allParticipants = await this.prisma.battleParticipant.findMany({
              where: {
                battle: {
                  status: BattleStatus.COMPLETED,
                },
              },
              select: {
                user_id: true,
                rank: true,
                battle_id: true,
              },
            });
            
            // Group participants by battle to count total participants per battle
            const battleParticipantCounts = new Map<string, number>();
            allParticipants.forEach(p => {
              const count = battleParticipantCounts.get(p.battle_id) || 0;
              battleParticipantCounts.set(p.battle_id, count + 1);
            });
            
            // Calculate the total number of participants across all battles
            const totalParticipantsInCompletedBattles = allParticipants.length;
            
            // Calculate the total number of winners (rank 1)
            const totalWinners = allParticipants.filter(p => p.rank === 1).length;
            
            // Calculate win rate as percentage of winners out of total participants
            winRate = Math.round((totalWinners / totalParticipantsInCompletedBattles) * 100);
          }
        }

        return {
          activeBattles: activeBattlesCount,
          upcomingBattles: upcomingBattlesCount,
          totalParticipants: uniqueParticipantsCount,
          winRate,
        };
      }, { ttl: BattleRepository.CACHE_TTL });

      return cachedStats;
    } catch (error) {
      logger.error('Error getting battle zone statistics:', error);
      throw createAppError('Failed to get battle zone statistics', 500);
    }
  }

  /**
   * Get battle with detailed information including topic, creator, participants, and questions
   */
  async getBattleDetails(id: string) {
    const startTime = Date.now();
    try {
      const cacheKey = `${BattleRepository.BATTLE_CACHE_PREFIX}${id}`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          const battle = await this.findUnique({
            where: { id },
            include: {
              topic: true,
              user: {
                select: {
                  id: true,
                  username: true,
                  avatar_url: true,
                },
              },
              participants: {
                include: {
                  user: {
                    select: {
                      id: true,
                      username: true,
                      avatar_url: true,
                    },
                  },
                },
                orderBy: {
                  score: 'desc',
                },
                take: 10, // Limit to top 10 participants for performance
              },
              questions: {
                orderBy: {
                  order: 'asc',
                },
                select: {
                  id: true,
                  question: true,
                  options: true,
                  points: true,
                  time_limit: true,
                  order: true,
                },
              },
            },
          });

          if (!battle) {
            throw createAppError('Battle not found', 404);
          }

          return battle;
        },
        { ttl: BattleRepository.CACHE_TTL },
      );
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Get battles with pagination, filtering, and sorting
   */
  async getBattles(params: {
    page?: number;
    limit?: number;
    search?: string;
    status?: BattleStatus;
    difficulty?: Difficulty;
    type?: BattleType;
    length?: Length;
    topic_id?: string;
    user_id?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<BattleListResponse> {
    const startTime = Date.now();
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status,
        difficulty,
        type,
        length,
        topic_id,
        user_id,
        sort_by = 'created_at',
        sort_order = 'desc',
      } = params;

      // Generate cache key based on filters
      const cacheKey = `${BattleRepository.BATTLE_LIST_CACHE_PREFIX}${JSON.stringify(params)}`;

      const result = await getOrSetCache(
        cacheKey,
        async () => {
          const where: Prisma.BattleWhereInput = {
            ...(search && {
              OR: [
                {
                  title: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
                {
                  description: {
                    contains: search,
                    mode: 'insensitive',
                  },
                },
              ],
            }),
            ...(status && { status }),
            ...(difficulty && { difficulty }),
            ...(type && { type }),
            ...(length && { length }),
            ...(topic_id && { topic_id }),
            ...(user_id && { user_id }),
          };

          const [total, battles] = await Promise.all([
            this.count({ where }),
            this.findMany({
              where,
              include: {
                topic: true,
                user: {
                  select: {
                    id: true,
                    username: true,
                    avatar_url: true,
                  },
                },
              },
              orderBy: {
                [sort_by]: sort_order,
              },
              skip: (page - 1) * limit,
              take: limit,
            }),
          ]);

          const response: BattleListResponse = {
            data: battles.map((battle) => ({
              ...battle,
              current_participants: battle.max_participants,
            })),
            meta: {
              total,
              page,
              limit,
              totalPages: Math.ceil(total / limit),
              cacheControl: 'public, max-age=3600',
              etag: require('crypto')
                .createHash('md5')
                .update(JSON.stringify(battles))
                .digest('hex'),
            },
          };

          return response;
        },
        { ttl: BattleRepository.CACHE_TTL },
      );

      return result;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Update a battle
   */
  async updateBattle(id: string, data: any, userId: string) {
    const startTime = Date.now();
    try {
      // First check if the battle exists and belongs to the user
      const battle = await this.findUnique({
        where: { id },
        select: { user_id: true, status: true },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Check if the user is the creator of the battle
      if (battle.user_id !== userId) {
        throw createAppError(
          'You are not authorized to update this battle',
          403,
        );
      }

      // Check if the battle is already completed or cancelled
      if (battle.status === 'COMPLETED' || battle.status === 'CANCELLED') {
        throw createAppError(
          `Cannot update a battle that is ${battle.status.toLowerCase()}`,
          400,
        );
      }

      // Update the battle
      const updatedBattle = await this.update({
        where: { id },
        data,
        include: {
          topic: true,
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
        },
      });

      // Invalidate related caches
      await Promise.all([
        deleteCache(`${BattleRepository.BATTLE_CACHE_PREFIX}${id}`),
        deleteCache(`${BattleRepository.BATTLE_LIST_CACHE_PREFIX}*`),
      ]);

      return updatedBattle;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Delete a battle with proper cleanup
   */
  async deleteBattle(id: string, userId: string) {
    const startTime = Date.now();
    try {
      const battle = await prisma.battle.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              participants: true,
              questions: true,
            },
          },
        },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Check if the user is the creator of the battle
      if (battle.user_id !== userId) {
        throw createAppError(
          'You are not authorized to delete this battle',
          403,
        );
      }

      // If the battle has participants, update the status to CANCELLED instead of deleting
      if (battle._count.participants > 0) {
        logger.info(
          `Battle ${id} has participants, updating status to CANCELLED`,
        );

        // Use a transaction to ensure all related data is updated
        const updatedBattle = await prisma.$transaction(async (tx) => {
          // Update battle status to CANCELLED
          const battle = await tx.battle.update({
            where: { id },
            data: {
              status: 'CANCELLED',
            },
          });

          // Notify participants about cancellation (this would be handled by the controller)

          return battle;
        });

        // Invalidate related caches
        await Promise.all([
          deleteCache(`${BattleRepository.BATTLE_CACHE_PREFIX}${id}`),
          deleteCache(`${BattleRepository.BATTLE_LIST_CACHE_PREFIX}*`),
        ]);

        return updatedBattle;
      }

      // If no participants, delete the battle and all related data
      const deletedBattle = await prisma.$transaction(async (tx) => {
        // First get all question IDs for this battle
        const questions = await tx.battleQuestion.findMany({
          where: { battle_id: id },
          select: { id: true },
        });

        const questionIds = questions.map((q) => q.id);

        // Delete battle answers for all questions in this battle
        if (questionIds.length > 0) {
          await tx.battleAnswer.deleteMany({
            where: {
              question_id: {
                in: questionIds,
              },
            },
          });
        }

        // Delete battle questions
        await tx.battleQuestion.deleteMany({
          where: { battle_id: id },
        });

        // Delete battle participants
        await tx.battleParticipant.deleteMany({
          where: { battle_id: id },
        });

        // Delete the battle
        const battle = await tx.battle.delete({
          where: { id },
        });

        return battle;
      });

      // Invalidate related caches
      await Promise.all([
        deleteCache(`${BattleRepository.BATTLE_CACHE_PREFIX}${id}`),
        deleteCache(`${BattleRepository.BATTLE_LIST_CACHE_PREFIX}*`),
      ]);

      return deletedBattle;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Join a battle
   * @param battleId - The ID of the battle to join
   * @param userId - The ID of the user joining the battle
   * @returns The created participant and updated battle
   */
  async joinBattle(battleId: string, userId: string) {
    const startTime = Date.now();
    try {
      logger.info(`User ${userId} attempting to join battle ${battleId}`);
      
      // Check if battle exists
      const battle = await prisma.battle.findUnique({
        where: { id: battleId },
        include: {
          participants: {
            where: { user_id: userId },
          },
        },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Check if user is already a participant
      if (battle.participants.length > 0) {
        throw createAppError('You are already a participant in this battle', 400);
      }

      // Check if battle is joinable
      if (battle.status !== 'UPCOMING' && battle.status !== 'IN_PROGRESS') {
        throw createAppError(
          `Cannot join a battle that is ${battle.status.toLowerCase()}`,
          400,
        );
      }

      // Check if battle has reached max participants
      if (
        battle.max_participants &&
        battle.current_participants >= battle.max_participants
      ) {
        throw createAppError(
          'This battle has reached its maximum number of participants',
          400,
        );
      }

      // Get all questions for this battle to create a randomized order
      const battleQuestions = await prisma.battleQuestion.findMany({
        where: { battle_id: battleId },
        orderBy: { order: 'asc' },
        select: { id: true },
      });

      if (battleQuestions.length === 0) {
        throw createAppError('This battle has no questions', 400);
      }

      // Create a randomized question order for this participant
      const questionOrder = this.shuffleArray([...battleQuestions.map(q => q.id)]);
      
      // Get the first question ID as the current question
      const currentQuestionId = questionOrder.length > 0 ? questionOrder[0] : null;

      logger.debug(`Created randomized question order for user ${userId} in battle ${battleId}`);

      // Create participant and update battle count in a transaction
      const [participant, updatedBattle] = await prisma.$transaction([
        prisma.battleParticipant.create({
          data: {
            battle_id: battleId,
            user_id: userId,
            score: 0,
            question_order: questionOrder as any, // Store the randomized question order
            current_question_id: currentQuestionId, // Set the first question as current
            last_activity: new Date() // Track when the participant joined
          },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                avatar_url: true,
              },
            },
          },
        }),
        prisma.battle.update({
          where: { id: battleId },
          data: {
            current_participants: { increment: 1 },
          },
          include: {
            topic: true,
            user: {
              select: {
                id: true,
                username: true,
                avatar_url: true,
              },
            },
          },
        }),
      ]);

      logger.info(`User ${userId} successfully joined battle ${battleId}`);

      // Invalidate related caches
      await Promise.all([
        deleteCache(`${BattleRepository.BATTLE_CACHE_PREFIX}${battleId}`),
        deleteCache(`${BattleRepository.BATTLE_LIST_CACHE_PREFIX}*`),
      ]);

      return { participant, battle: updatedBattle };
    } catch (error) {
      logger.error(`Error joining battle: ${error instanceof Error ? error.message : 'Unknown error'}`);
      if (error instanceof Error) {
        throw createAppError(error.message, error.message.includes('not found') ? 404 : 400);
      }
      throw createAppError('Failed to join battle', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Get battle questions
   * @param battleId - The ID of the battle
   * @param userId - The ID of the user requesting questions
   * @returns Questions for the battle, filtered based on user role and battle status
   */
  async getBattleQuestions(battleId: string, userId: string) {
    const startTime = Date.now();
    try {
      logger.info(`User ${userId} requesting questions for battle ${battleId}`);
      
      // Check if battle exists with participant information
      const battle = await prisma.battle.findUnique({
        where: { id: battleId },
        include: {
          participants: {
            where: { user_id: userId },
            include: {
              current_question: true // Include the current question details
            }
          },
        },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Check if user is a participant or creator
      const isParticipant = battle.participants.length > 0;
      const isCreator = battle.user_id === userId;

      if (!isParticipant && !isCreator) {
        throw createAppError(
          "You do not have access to this battle's questions",
          403,
        );
      }

      // Check if battle is active for participants
      if (isParticipant && !isCreator && battle.status !== 'IN_PROGRESS') {
        throw createAppError(
          `Questions are only available when the battle is in progress`,
          403,
        );
      }

      // For participants, we'll handle questions differently than for creators
      if (isParticipant && !isCreator) {
        const participant = battle.participants[0];
        
        // Update last activity time
        await prisma.battleParticipant.update({
          where: { id: participant.id },
          data: { last_activity: new Date() }
        });

        // If participant has a current question, return only that question
        if (participant.current_question_id) {
          // Get the current question with full details
          const currentQuestion = await prisma.battleQuestion.findUnique({
            where: { id: participant.current_question_id },
            select: {
              id: true,
              question: true,
              options: true,
              points: true,
              time_limit: true,
              order: true,
              // Don't include correct_answer for security
            },
          });
          
          if (!currentQuestion) {
            throw createAppError('Current question not found', 404);
          }
          
          // Return only the current question as an array for consistency
          logger.info(`Returning current question ${participant.current_question_id} for participant ${userId}`);
          return [currentQuestion];
        } else {
          // If no current question (participant completed all questions)
          logger.info(`Participant ${userId} has no current question`);
          return [];
        }
      }

      // For creators, return all questions with correct answers
      const questions = await prisma.battleQuestion.findMany({
        where: { battle_id: battleId },
        orderBy: { order: 'asc' },
        select: {
          id: true,
          question: true,
          options: true,
          points: true,
          time_limit: true,
          order: true,
          correct_answer: true, // Include correct answers for creators
        },
      });

      logger.info(`Returning all ${questions.length} questions for battle creator ${userId}`);
      return questions;
    } catch (error) {
      logger.error(`Error getting battle questions: ${error instanceof Error ? error.message : 'Unknown error'}`);
      if (error instanceof Error) {
        throw createAppError(error.message, error.message.includes('not found') ? 404 : 400);
      }
      throw createAppError('Failed to get battle questions', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Move to the next question for a participant
   * @param battleId - The ID of the battle
   * @param userId - The ID of the participant
   * @returns The ID of the next question or null if no more questions
   */
  async moveToNextQuestion(battleId: string, userId: string): Promise<string | null> {
    const startTime = Date.now();
    try {
      logger.info(`Moving to next question for user ${userId} in battle ${battleId}`);
      
      // Get the participant with their question order
      const participant = await prisma.battleParticipant.findUnique({
        where: {
          battle_id_user_id: {
            battle_id: battleId,
            user_id: userId,
          },
        },
      });

      if (!participant) {
        throw createAppError('Participant not found', 404);
      }

      // Get the current question order
      const questionOrder = participant.question_order as string[];
      
      if (!questionOrder || !Array.isArray(questionOrder)) {
        throw createAppError('Question order not found', 400);
      }

      // Find the current index
      const currentIndex = questionOrder.findIndex(
        id => id === participant.current_question_id
      );

      // If at the end or not found, mark as completed
      if (currentIndex === -1 || currentIndex >= questionOrder.length - 1) {
        // Update participant as completed
        await prisma.battleParticipant.update({
          where: { id: participant.id },
          data: {
            current_question_id: null,
            completed_at: new Date(),
          },
        });
        logger.info(`Participant ${userId} has completed all questions in battle ${battleId}`);
        return null;
      }

      // Move to the next question
      const nextQuestionId = questionOrder[currentIndex + 1];
      
      // Update the participant's current question
      await prisma.battleParticipant.update({
        where: { id: participant.id },
        data: {
          current_question_id: nextQuestionId,
          last_activity: new Date(),
        },
      });

      logger.info(`Moved participant ${userId} to next question ${nextQuestionId}`);
      return nextQuestionId;
    } catch (error) {
      logger.error(`Error moving to next question: ${error instanceof Error ? error.message : 'Unknown error'}`);
      if (error instanceof Error) {
        throw createAppError(error.message, error.message.includes('not found') ? 404 : 400);
      }
      throw createAppError('Failed to move to next question', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Submit an answer to a battle question
   * @param battleId - The ID of the battle
   * @param questionId - The ID of the question being answered
   * @param userId - The ID of the user submitting the answer
   * @param answer - The answer submitted by the user
   * @param timeTaken - The time taken to answer in seconds
   * @returns The created answer record
   */
  async submitAnswer(
    battleId: string,
    questionId: string,
    userId: string,
    answer: string,
    timeTaken: number,
  ) {
    const startTime = Date.now();
    try {
      logger.info(`User ${userId} submitting answer for question ${questionId} in battle ${battleId}`);
      
      // Check if battle exists and is in progress
      const battle = await prisma.battle.findUnique({
        where: { id: battleId },
        include: {
          participants: {
            where: { user_id: userId },
          },
        },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      if (battle.status !== 'IN_PROGRESS') {
        throw createAppError(
          `Cannot submit answers to a battle that is ${battle.status.toLowerCase()}`,
          400,
        );
      }

      // Check if user is a participant
      if (battle.participants.length === 0) {
        throw createAppError('You are not a participant in this battle', 403);
      }

      const participant = battle.participants[0];
      
      // Check if this is the participant's current question
      if (participant.current_question_id !== questionId) {
        throw createAppError('This is not your current question', 400);
      }

      // Check if question exists and belongs to the battle
      const question = await prisma.battleQuestion.findFirst({
        where: {
          id: questionId,
          battle_id: battleId,
        },
      });

      if (!question) {
        throw createAppError(
          'Question not found or does not belong to this battle',
          404,
        );
      }

      // Check if answer has already been submitted
      const existingAnswer = await prisma.battleAnswer.findUnique({
        where: {
          question_id_user_id: {
            question_id: questionId,
            user_id: userId,
          },
        },
      });

      if (existingAnswer) {
        throw createAppError(
          'You have already submitted an answer for this question',
          400,
        );
      }

      // Check if time taken is reasonable
      if (timeTaken < 0 || timeTaken > question.time_limit * 2) {
        throw createAppError('Invalid time taken for submission', 400);
      }

    // Determine if answer is correct
    const isCorrect = answer === question.correct_answer;

    // Calculate score based on correctness and time taken
    let score = 0;
    if (isCorrect) {
      // Base score for correct answer
      score = question.points;

      // Bonus for quick answers (up to 50% bonus for answering in half the time)
      if (timeTaken < question.time_limit) {
        const timeBonus = Math.floor(
          question.points * 0.5 * (1 - timeTaken / question.time_limit)
        );
        score += timeBonus;
      }
    }

    // Create answer and update participant score in a transaction
    const [battleAnswer, updatedParticipant] = await prisma.$transaction([
      prisma.battleAnswer.create({
        data: {
          question_id: questionId,
          user_id: userId,
          answer,
          is_correct: isCorrect,
          time_taken: timeTaken,
        },
      }),
      prisma.battleParticipant.update({
        where: {
          battle_id_user_id: {
            battle_id: battleId,
            user_id: userId,
          },
        },
        data: {
          score: { increment: score },
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
        },
      }),
    ]);

    // Move to the next question
    await this.moveToNextQuestion(battleId, userId);

    // Update leaderboard
    await this.updateLeaderboard(battleId);

    logger.info(`User ${userId} submitted answer for question ${questionId} in battle ${battleId}, score: ${score}`);
    
    return {
      answer: battleAnswer,
      participant: updatedParticipant,
      score,
      isCorrect
    };
  } catch (error) {
    logger.error(`Error submitting answer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    if (error instanceof Error) {
      throw createAppError(error.message, error.message.includes('not found') ? 404 : 400);
    }
    throw createAppError('Failed to submit answer', 500);
  } finally {
    const duration = Date.now() - startTime;
    logger.debug(`Operation took ${duration}ms`);
  }
  }

  /**
   * Get battle leaderboard
   */
  async getBattleLeaderboard(battleId: string, limit = 10, page = 1) {
    // Check if battle exists
    const battle = await this.findUnique({
      where: { id: battleId },
    });

    if (!battle) {
      throw createAppError('Battle not found', 404);
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get participants ordered by score
    const participants = await prisma.battleParticipant.findMany({
      where: { battle_id: battleId },
      orderBy: { score: 'desc' },
      skip,
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            avatar_url: true,
          },
        },
      },
    });

    // Get total count for pagination
    const total = await prisma.battleParticipant.count({
      where: { battle_id: battleId },
    });

    const totalPages = Math.ceil(total / limit);

    return {
      data: participants,
      meta: {
        total,
        currentPage: page,
        totalPages,
        limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Update battle leaderboard with current rankings
   * This is called after each answer submission
   */
  private async updateLeaderboard(battleId: string) {
    // Get all participants ordered by score
    const participants = await prisma.battleParticipant.findMany({
      where: { battle_id: battleId },
      orderBy: { score: 'desc' },
    });

    // Update ranks in a transaction
    const updates = participants.map((participant, index) => {
      return prisma.battleParticipant.update({
        where: { id: participant.id },
        data: { rank: index + 1 },
      });
    });

    await prisma.$transaction(updates);

    return participants.length;
  }

  async updateBattleStatus(id: string, status: BattleStatus): Promise<Battle> {
    const startTime = Date.now();
    try {
      const battle = await this.prisma.battle.update({
        where: { id },
        data: { status },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  avatar_url: true,
                },
              },
            },
          },
        },
      });

      // Invalidate cache
      await deleteCache(`${BattleRepository.BATTLE_CACHE_PREFIX}${id}`);
      await deleteCache(`${BattleRepository.BATTLE_LIST_CACHE_PREFIX}*`);

      return battle;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Get all answers for a battle
   */
  async getAllBattleAnswers(battleId: string): Promise<BattleAnswer[]> {
    const startTime = Date.now();
    try {
      return await this.prisma.battleAnswer.findMany({
        where: {
          question: {
            battle_id: battleId,
          },
        },
        include: {
          question: true,
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
        },
      });
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Update battle progress for a participant
   */
  async updateBattleProgress(
    battleId: string,
    userId: string,
    questionId: string,
    isCompleted: boolean,
    timeTaken: number,
  ): Promise<{
    participant: BattleParticipant;
    answer: BattleAnswer;
    leaderboard: BattleLeaderboard[];
  }> {
    const startTime = Date.now();
    try {
      // First, verify the battle and question exist and are valid
      const battle = await this.prisma.battle.findUnique({
        where: { id: battleId },
        include: {
          questions: {
            where: { id: questionId },
          },
        },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      if (battle.questions.length === 0) {
        throw createAppError('Question not found in this battle', 404);
      }

      const question = battle.questions[0];

      // Check if the user is a participant in this battle
      const participant = await this.prisma.battleParticipant.findUnique({
        where: {
          battle_id_user_id: {
            battle_id: battleId,
            user_id: userId,
          },
        },
      });

      if (!participant) {
        throw createAppError('User is not a participant in this battle', 403);
      }

      // Check if the answer already exists
      const existingAnswer = await this.prisma.battleAnswer.findUnique({
        where: {
          question_id_user_id: {
            question_id: questionId,
            user_id: userId,
          },
        },
      });

      // If the answer exists, update it; otherwise, create a new one
      let answer;
      if (existingAnswer) {
        answer = await this.prisma.battleAnswer.update({
          where: {
            id: existingAnswer.id,
          },
          data: {
            is_correct: isCompleted,
            time_taken: timeTaken,
          },
        });
      } else {
        answer = await this.prisma.battleAnswer.create({
          data: {
            question_id: questionId,
            user_id: userId,
            answer: 'completed', // For progress tracking, we just mark it as completed
            is_correct: isCompleted,
            time_taken: timeTaken,
          },
        });
      }

      // Calculate the new score for the participant
      const allAnswers = await this.prisma.battleAnswer.findMany({
        where: {
          user_id: userId,
          question: {
            battle_id: battleId,
          },
          is_correct: true,
        },
      });

      // Calculate score based on correct answers and time taken
      const totalScore = allAnswers.reduce((score, ans) => {
        // Base points for correct answer
        const basePoints = question.points;

        // Time bonus: faster answers get more points
        // If answered within 50% of the time limit, get full bonus
        const timeRatio = Math.min(
          1,
          ans.time_taken / (question.time_limit * 0.5),
        );
        const timeBonus = Math.round(basePoints * 0.5 * (1 - timeRatio));

        return score + basePoints + timeBonus;
      }, 0);

      // Update the participant's score
      const updatedParticipant = await this.prisma.battleParticipant.update({
        where: {
          id: participant.id,
        },
        data: {
          score: totalScore,
        },
      });

      // Update the leaderboard
      // First, get all participants with their scores
      const participants = await this.prisma.battleParticipant.findMany({
        where: {
          battle_id: battleId,
        },
        orderBy: {
          score: 'desc',
        },
      });

      // Assign ranks
      let currentRank = 1;
      let previousScore = -1;
      let rankedParticipants = participants.map((p, index) => {
        if (index > 0 && p.score < previousScore) {
          currentRank = index + 1;
        }
        previousScore = p.score;
        return {
          ...p,
          rank: currentRank,
        };
      });

      // Update or create leaderboard entries
      const leaderboardEntries = await Promise.all(
        rankedParticipants.map(async (p) => {
          // Calculate total time taken from all answers
          const userAnswers = await this.prisma.battleAnswer.findMany({
            where: {
              user_id: p.user_id,
              question: {
                battle_id: battleId,
              },
            },
          });

          const totalTimeTaken = userAnswers.reduce(
            (total, ans) => total + ans.time_taken,
            0,
          );

          // Update or create leaderboard entry
          const existingEntry = await this.prisma.battleLeaderboard.findFirst({
            where: {
              battle_id: battleId,
              user_id: p.user_id,
            },
          });

          if (existingEntry) {
            return this.prisma.battleLeaderboard.update({
              where: {
                id: existingEntry.id,
              },
              data: {
                score: p.score,
                rank: p.rank,
                time_taken: totalTimeTaken,
              },
            });
          } else {
            return this.prisma.battleLeaderboard.create({
              data: {
                battle_id: battleId,
                user_id: p.user_id,
                score: p.score,
                rank: p.rank,
                time_taken: totalTimeTaken,
              },
            });
          }
        }),
      );

      // Invalidate cache
      await deleteCache(`${BattleRepository.BATTLE_CACHE_PREFIX}${battleId}`);
      await deleteCache(`${BattleRepository.BATTLE_LIST_CACHE_PREFIX}*`);

      return {
        participant: updatedParticipant,
        answer,
        leaderboard: leaderboardEntries,
      };
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Reschedule a battle
   */
  async rescheduleBattle(
    id: string,
    userId: string,
    startTime: Date,
    endTime: Date,
  ) {
    const startTimeMs = Date.now();
    try {
      const battle = await this.findUnique({
        where: { id },
        select: {
          user_id: true,
          status: true,
          current_participants: true,
        },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Check if the user is the creator of the battle
      if (battle.user_id !== userId) {
        throw createAppError(
          'You are not authorized to reschedule this battle',
          403,
        );
      }

      // Check if the battle is in a reschedulable state
      if (battle.status !== 'UPCOMING') {
        throw createAppError('Only upcoming battles can be rescheduled', 400);
      }

      // Check if the battle has participants
      if (battle.current_participants > 0) {
        throw createAppError(
          'Cannot reschedule a battle with participants',
          400,
        );
      }

      // Validate start and end times
      if (startTime >= endTime) {
        throw createAppError('End time must be after start time', 400);
      }

      // Update the battle with new start and end times
      const updatedBattle = await this.update({
        where: { id },
        data: {
          start_time: startTime,
          end_time: endTime,
        },
        include: {
          topic: true,
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
        },
      });

      // Invalidate related caches
      await Promise.all([
        deleteCache(`${BattleRepository.BATTLE_CACHE_PREFIX}${id}`),
        deleteCache(`${BattleRepository.BATTLE_LIST_CACHE_PREFIX}*`),
      ]);

      return updatedBattle;
    } finally {
      const duration = Date.now() - startTimeMs;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Archive a battle
   */
  async archiveBattle(id: string, userId: string) {
    const startTime = Date.now();
    try {
      const battle = await prisma.battle.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              participants: true,
            },
          },
        },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // Check if the user is the creator of the battle
      if (battle.user_id !== userId) {
        throw createAppError(
          'You are not authorized to archive this battle',
          403,
        );
      }

      // Only completed or cancelled battles can be archived
      if (battle.status !== 'COMPLETED' && battle.status !== 'CANCELLED') {
        throw createAppError(
          'Only completed or cancelled battles can be archived',
          400,
        );
      }

      // Update the battle status to ARCHIVED
      const archivedBattle = await prisma.battle.update({
        where: { id },
        data: {
          status: BattleStatus.ARCHIVED,
        },
      });

      // Invalidate related caches
      await Promise.all([
        deleteCache(`${BattleRepository.BATTLE_CACHE_PREFIX}${id}`),
        deleteCache(`${BattleRepository.BATTLE_LIST_CACHE_PREFIX}*`),
      ]);

      return archivedBattle;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Select questions for a battle based on topic, difficulty, and length
   * @param topicId - The topic ID to filter questions by
   * @param difficulty - The difficulty level of the battle
   * @param totalQuestions - The number of questions to select
   * @param userId - Optional user ID to consider question history
   * @returns Array of selected questions with their content
   */
  async selectQuestionsForBattle(
    topicId: string,
    difficulty: Difficulty,
    totalQuestions: number,
    userId?: string
  ): Promise<any[]> {
    const startTime = Date.now();
    try {
      logger.info(`Selecting ${totalQuestions} ${difficulty} questions for topic ${topicId}${userId ? ` for user ${userId}` : ''}`);

      // Define difficulty weights for question selection
      // This ensures a good mix of questions even within the same difficulty level
      const difficultyWeights = {
        easy: {
          easy: 0.7,    // 70% easy questions
          medium: 0.3,  // 30% medium questions
          hard: 0,      // 0% hard questions
        },
        medium: {
          easy: 0.3,    // 30% easy questions
          medium: 0.5,  // 50% medium questions
          hard: 0.2,    // 20% hard questions
        },
        hard: {
          easy: 0.1,    // 10% easy questions
          medium: 0.4,  // 40% medium questions
          hard: 0.5,    // 50% hard questions
        },
      };

      // Calculate number of questions to select from each difficulty level
      const weights = difficultyWeights[difficulty.toLowerCase() as keyof typeof difficultyWeights];
      const easyCount = Math.round(totalQuestions * weights.easy);
      const mediumCount = Math.round(totalQuestions * weights.medium);
      const hardCount = Math.round(totalQuestions * weights.hard);

      // Adjust counts to ensure we get exactly totalQuestions
      let adjustedEasyCount = easyCount;
      let adjustedMediumCount = mediumCount;
      let adjustedHardCount = hardCount;

      const sum = easyCount + mediumCount + hardCount;
      if (sum < totalQuestions) {
        // Add the remaining questions to medium difficulty
        adjustedMediumCount += (totalQuestions - sum);
      } else if (sum > totalQuestions) {
        // Remove excess questions proportionally
        const excess = sum - totalQuestions;
        if (excess <= hardCount) {
          adjustedHardCount -= excess;
        } else if (excess <= hardCount + mediumCount) {
          adjustedHardCount = 0;
          adjustedMediumCount -= (excess - hardCount);
        } else {
          adjustedHardCount = 0;
          adjustedMediumCount = 0;
          adjustedEasyCount -= (excess - hardCount - mediumCount);
        }
      }

      logger.debug(`Question distribution - Easy: ${adjustedEasyCount}, Medium: ${adjustedMediumCount}, Hard: ${adjustedHardCount}`);

      // Get previously seen question IDs for this user if userId is provided
      let excludeQuestionIds: string[] = [];
      if (userId) {
        excludeQuestionIds = await this.getUserSeenQuestionIds(userId);
        logger.debug(`Found ${excludeQuestionIds.length} previously seen questions for user ${userId}`);
      }

      // Fetch questions for each difficulty level, avoiding previously seen questions if possible
      const easyQuestions = adjustedEasyCount > 0 ? 
        await this.getRandomQuestions(topicId, 'EASY', adjustedEasyCount) : [];
      const mediumQuestions = adjustedMediumCount > 0 ? 
        await this.getRandomQuestions(topicId, 'MEDIUM', adjustedMediumCount) : [];
      const hardQuestions = adjustedHardCount > 0 ? 
        await this.getRandomQuestions(topicId, 'HARD', adjustedHardCount) : [];

      // Combine all questions and filter out previously seen questions if possible
      let selectedQuestions = [...easyQuestions, ...mediumQuestions, ...hardQuestions];
      
      if (userId && excludeQuestionIds.length > 0) {
        // Filter out questions the user has already seen, if we have enough questions
        const unseenQuestions = selectedQuestions.filter(q => !excludeQuestionIds.includes(q.id));
        
        // Only use unseen questions if we have enough of them
        if (unseenQuestions.length >= totalQuestions) {
          selectedQuestions = unseenQuestions;
          logger.debug(`Using ${unseenQuestions.length} unseen questions for user ${userId}`);
        } else {
          logger.debug(`Not enough unseen questions (${unseenQuestions.length}/${totalQuestions}), using some previously seen questions`);
        }
      }

      // Check if we have enough questions
      if (selectedQuestions.length < totalQuestions) {
        logger.warn(`Not enough questions available. Requested: ${totalQuestions}, Found: ${selectedQuestions.length}`);
        
        // Try to get additional questions from any difficulty if we don't have enough
        if (selectedQuestions.length === 0) {
          throw createAppError(`No questions available for the selected topic and difficulty`, 400);
        }
        
        // Get additional questions from any difficulty
        const additionalQuestions = await this.getRandomQuestionsAnyDifficulty(
          topicId, 
          totalQuestions - selectedQuestions.length,
          selectedQuestions.map(q => q.id)
        );
        
        selectedQuestions.push(...additionalQuestions);
      }

      // Randomize the order of questions
      const shuffledQuestions = this.shuffleArray(selectedQuestions);
      
      // Limit to the requested number of questions
      const finalQuestions = shuffledQuestions.slice(0, totalQuestions);
      
      // If userId is provided, update the user's question history
      if (userId) {
        // Update question history in the background (don't await)
        finalQuestions.forEach(question => {
          this.updateUserQuestionHistory(userId, question.id).catch(error => {
            logger.error(`Error updating user question history: ${error instanceof Error ? error.message : 'Unknown error'}`);
          });
        });
      }
      
      // Return the selected questions
      return finalQuestions;
    } catch (error) {
      logger.error('Error selecting questions for battle:', error);
      if (error instanceof Error) {
        throw createAppError(error.message, 400);
      }
      throw createAppError('Failed to select questions for battle', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Question selection took ${duration}ms`);
    }
  }

  /**
   * Associate selected questions with a battle
   * @param battleId - The ID of the battle
   * @param questions - Array of questions to associate with the battle
   * @returns The updated battle with questions
   */
  async associateQuestionsWithBattle(battleId: string, questions: any[]) {
    const startTime = Date.now();
    try {
      logger.info(`Associating ${questions.length} questions with battle ${battleId}`);

      // Check if the battle exists
      const battle = await this.findUnique({
        where: { id: battleId },
      });

      if (!battle) {
        throw createAppError('Battle not found', 404);
      }

      // First, delete any existing questions for this battle to avoid duplicates
      await this.prisma.battleQuestion.deleteMany({
        where: { battle_id: battleId },
      });

      // Create battle_question entries with order
      const battleQuestions = await Promise.all(
        questions.map(async (q, index) => {
          // Get options for this question
          const options = q.options || [];
          
          // Find the correct answer
          const correctOption = options.find(option => option.is_correct);
          const correctAnswer = correctOption ? correctOption.answer_text || correctOption.text : '';
          
          // Format options for storage as JSON
          const formattedOptions = options.map(o => ({
            id: o.id,
            text: o.answer_text || o.text,
            is_correct: o.is_correct,
          }));
          
          // Create the battle question
          return this.prisma.battleQuestion.create({
            data: {
              battle_id: battleId,
              question: q.question,
              options: formattedOptions,
              correct_answer: correctAnswer,
              points: battle.points_per_question || 10, // Use battle setting or default
              time_limit: battle.time_per_question || 30, // Use battle setting or default
              order: index + 1, // 1-based ordering
            },
          });
        })
      );

      // Update the battle with the total number of questions
      await this.update({
        where: { id: battleId },
        data: {
          total_questions: questions.length,
        },
      });

      logger.info(`Successfully associated ${battleQuestions.length} questions with battle ${battleId}`);
      return battleQuestions;
    } catch (error) {
      logger.error('Error associating questions with battle:', error);
      if (error instanceof Error) {
        throw createAppError(error.message, 400);
      }
      throw createAppError('Failed to associate questions with battle', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Question association took ${duration}ms`);
    }
  }

  /**
   * Get random questions for a topic and difficulty
   * @param topicId - The topic ID
   * @param difficulty - The difficulty level
   * @param count - Number of questions to retrieve
   * @returns Array of questions
   */
  private async getRandomQuestions(topicId: string, difficulty: string, count: number) {
    try {
      logger.debug(`Getting ${count} ${difficulty} questions for topic ${topicId}`);
      
      // Get quizzes for the topic - using a more type-safe approach
      const whereClause: Prisma.QuizWhereInput = {
        topic_id: topicId
      };
      
      // Add the difficulty filter using Prisma.sql for raw query if needed
      const quizzes = await this.prisma.quiz.findMany({
        where: whereClause,
        select: {
          id: true,
        },
      });
      
      if (quizzes.length === 0) {
        logger.warn(`No quizzes found for topic ${topicId} with difficulty ${difficulty}`);
        return [];
      }
      
      const quizIds = quizzes.map(quiz => quiz.id);
      
      // Query to get random questions matching the criteria
      const questions = await this.prisma.quizQuestion.findMany({
        where: {
          quiz_id: {
            in: quizIds,
          },
        },
        include: {
          options: true,
        },
        take: count * 2, // Fetch more than needed to ensure we have enough after filtering
      });
      
      // Filter questions to ensure they have valid options and at least one correct answer
      const validQuestions = questions.filter(question => {
        // Make sure options exists and is an array
        const options = question.options || [];
        const hasOptions = options.length >= 2;
        const hasCorrectAnswer = options.some(option => option.is_correct);
        return hasOptions && hasCorrectAnswer;
      });
      
      // Shuffle and limit to requested count
      const shuffledQuestions = this.shuffleArray(validQuestions);
      return shuffledQuestions.slice(0, count);
    } catch (error) {
      logger.error(`Error getting random questions: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  /**
   * Get random questions for a topic and difficulty
   * @param topicId - The topic ID
   * @param difficulty - The difficulty level
   * @param count - Number of questions to retrieve
   * @returns Array of questions
   */
  private async getRandomQuestions(topicId: string, difficulty: string, count: number) {
    try {
      logger.debug(`Getting ${count} ${difficulty} questions for topic ${topicId}`);
      
      // Get quizzes for the topic - using a more type-safe approach
      const whereClause: Prisma.QuizWhereInput = {
        topic_id: topicId
      };
      
      // Add the difficulty filter using Prisma.sql for raw query if needed
      const quizzes = await this.prisma.quiz.findMany({
        where: whereClause,
        select: {
          id: true,
        },
      });
      
      if (quizzes.length === 0) {
        logger.warn(`No quizzes found for topic ${topicId} with difficulty ${difficulty}`);
        return [];
      }
      
      const quizIds = quizzes.map(quiz => quiz.id);
      
      // Query to get random questions matching the criteria
      const questions = await this.prisma.quizQuestion.findMany({
        where: {
          quiz_id: {
            in: quizIds,
          },
        },
        include: {
          options: true,
        },
        take: count * 2, // Fetch more than needed to ensure we have enough after filtering
      });
      
      // Filter questions to ensure they have valid options and at least one correct answer
      const validQuestions = questions.filter(question => {
        // Make sure options exists and is an array
        const options = question.options || [];
        const hasOptions = options.length >= 2;
        const hasCorrectAnswer = options.some(option => option.is_correct);
        return hasOptions && hasCorrectAnswer;
      });
      
      // Shuffle and limit to requested count
      const shuffledQuestions = this.shuffleArray(validQuestions);
      return shuffledQuestions.slice(0, count);
    } catch (error) {
      logger.error(`Error getting random questions: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  /**
   * Get random questions for a topic with any difficulty level
   * @param topicId - The topic ID
   * @param count - Number of questions to retrieve
   * @param excludeIds - Question IDs to exclude from results
   * @returns Array of questions
   */
  private async getRandomQuestionsAnyDifficulty(topicId: string, count: number, excludeIds: string[] = []) {
    try {
      logger.debug(`Getting ${count} questions of any difficulty for topic ${topicId}`);
      
      // Get all quizzes for the topic regardless of difficulty
      const quizzes = await this.prisma.quiz.findMany({
        where: {
          topic_id: topicId,
        },
        select: {
          id: true,
        },
      });
      
      if (quizzes.length === 0) {
        logger.warn(`No quizzes found for topic ${topicId}`);
        return [];
      }
      
      const quizIds = quizzes.map(quiz => quiz.id);
      
      // Query to get random questions matching the criteria
      const questions = await this.prisma.quizQuestion.findMany({
        where: {
          quiz_id: {
            in: quizIds,
          },
          id: {
            notIn: excludeIds,
          },
        },
        include: {
          options: true,
        },
        take: count * 2, // Fetch more than needed to ensure we have enough after filtering
      });
      
      // Filter questions to ensure they have valid options and at least one correct answer
      const validQuestions = questions.filter(question => {
        // Make sure options exists and is an array
        const options = question.options || [];
        const hasOptions = options.length >= 2;
        const hasCorrectAnswer = options.some(option => option.is_correct);
        return hasOptions && hasCorrectAnswer;
      });
      
      // Shuffle and limit to requested count
      const shuffledQuestions = this.shuffleArray(validQuestions);
      return shuffledQuestions.slice(0, count);
    } catch (error) {
      logger.error(`Error getting random questions of any difficulty: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  /**
   * Get questions that a user has seen, ordered by least recently seen
   * @param userId - The user ID
   * @param topicId - The topic ID
   * @param count - Number of questions to retrieve
   * @returns Array of questions
   */
  private async getLeastRecentlySeenQuestions(userId: string, topicId: string, count: number) {
    try {
      // Get quizzes for the topic
      const quizzes = await this.prisma.quiz.findMany({
        where: {
          topic_id: topicId,
        },
        select: {
          id: true,
        },
      });
      
      if (quizzes.length === 0) {
        logger.warn(`No quizzes found for topic ${topicId}`);
        return [];
      }
      
      const quizIds = quizzes.map(quiz => quiz.id);
      
      // Get user's question history, ordered by least recently seen
      const userHistory = await this.prisma.userQuestionHistory.findMany({
        where: {
          user_id: userId,
        },
        orderBy: {
          last_seen_at: 'asc', // Oldest first
        },
        take: count * 2, // Get more than we need to allow for filtering
      });
      
      if (userHistory.length === 0) {
        logger.debug(`No question history found for user ${userId}`);
        return [];
      }
      
      // Get the question IDs from history
      const questionIds = userHistory.map(history => history.question_id);
      
      // Fetch the actual questions
      const questions = await this.prisma.quizQuestion.findMany({
        where: {
          id: {
            in: questionIds,
          },
          quiz_id: {
            in: quizIds, // Filter by topic
          },
        },
        include: {
          options: true,
        },
        take: count,
      });
      
      logger.debug(`Found ${questions.length} least recently seen questions for user ${userId} and topic ${topicId}`);
      return questions;
    } catch (error) {
      logger.error(`Error getting least recently seen questions: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  /**
   * Get question IDs that a user has previously seen
   * @param userId - The user ID to check history for
   * @returns Array of question IDs the user has seen before
   */
  private async getUserSeenQuestionIds(userId: string): Promise<string[]> {
    try {
      const userHistory = await this.prisma.userQuestionHistory.findMany({
        where: { user_id: userId },
        select: { question_id: true }
      });
      
      return userHistory.map(history => history.question_id);
    } catch (error) {
      logger.error(`Error getting user question history: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  }

  /**
   * Update user question history when they see or answer a question
   * @param userId - The user ID
   * @param questionId - The question ID
   * @param isCorrect - Whether the user answered correctly (optional)
   */
  private async updateUserQuestionHistory(userId: string, questionId: string, isCorrect?: boolean): Promise<void> {
    try {
      // Check if there's an existing record
      const existingRecord = await this.prisma.userQuestionHistory.findUnique({
        where: {
          user_id_question_id: {
            user_id: userId,
            question_id: questionId
          }
        }
      });

      if (existingRecord) {
        // Update existing record
        await this.prisma.userQuestionHistory.update({
          where: {
            user_id_question_id: {
              user_id: userId,
              question_id: questionId
            }
          },
          data: {
            last_seen_at: new Date(),
            times_seen: { increment: 1 },
            ...(isCorrect !== undefined && { last_correct: isCorrect })
          }
        });
      } else {
        // Create new record
        await this.prisma.userQuestionHistory.create({
          data: {
            user_id: userId,
            question_id: questionId,
            seen_at: new Date(),
            last_seen_at: new Date(),
            times_seen: 1,
            ...(isCorrect !== undefined && { last_correct: isCorrect })
          }
        });
      }
    } catch (error) {
      logger.error(`Error updating user question history: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Shuffle an array using Fisher-Yates algorithm
   * @param array - The array to shuffle
   * @returns Shuffled array
   */
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
