import { BookmarkCollection } from '@prisma/client';

import prisma from '@/lib/prisma';
import { deleteCache, getOrSetCache } from '@/services/cacheService';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

/**
 * Repository for BookmarkCollection entity
 * Extends BaseRepository with BookmarkCollection as the entity type
 */
export default class BookmarkCollectionRepository extends BaseRepository<BookmarkCollection> {
  private static readonly CACHE_PREFIX = 'bookmark-collection:';
  private static readonly CACHE_TTL = 3600; // 1 hour

  constructor() {
    super(prisma.bookmarkCollection);
  }

  /**
   * Get all bookmark collections for a user
   * @param userId - The ID of the user
   * @returns An array of bookmark collections with bookmarks
   */
  async getUserCollections(userId: string) {
    const startTime = Date.now();
    try {
      const cacheKey = `${BookmarkCollectionRepository.CACHE_PREFIX}${userId}:collections`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          return prisma.bookmarkCollection.findMany({
            where: { user_id: userId },
            include: {
              bookmarks: {
                include: {
                  challenge: {
                    select: {
                      id: true,
                      title: true,
                      description: true,
                      difficulty: true,
                      category: true,
                      points: true,
                      tags: true,
                    },
                  },
                },
              },
              _count: {
                select: {
                  bookmarks: true,
                },
              },
            },
            orderBy: { created_at: 'desc' },
          });
        },
        { ttl: BookmarkCollectionRepository.CACHE_TTL },
      );
    } catch (error) {
      logger.error('Error fetching user collections:', error);
      throw createAppError('Failed to fetch user collections', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Get a single bookmark collection by ID
   * @param collectionId - The ID of the collection
   * @param userId - The ID of the user (for authorization)
   * @returns The bookmark collection with bookmarks
   */
  async getCollectionById(collectionId: string, userId: string) {
    const startTime = Date.now();
    try {
      const cacheKey = `${BookmarkCollectionRepository.CACHE_PREFIX}${userId}:collection:${collectionId}`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          const collection = await prisma.bookmarkCollection.findUnique({
            where: { id: collectionId },
            include: {
              bookmarks: {
                include: {
                  challenge: {
                    select: {
                      id: true,
                      title: true,
                      description: true,
                      difficulty: true,
                      category: true,
                      points: true,
                      tags: true,
                    },
                  },
                },
              },
              _count: {
                select: {
                  bookmarks: true,
                },
              },
            },
          });

          if (!collection) {
            throw createAppError('Collection not found', 404);
          }

          // Check if the collection belongs to the user
          if (collection.user_id !== userId) {
            throw createAppError('Unauthorized', 403);
          }

          return collection;
        },
        { ttl: BookmarkCollectionRepository.CACHE_TTL },
      );
    } catch (error) {
      logger.error('Error fetching collection by ID:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Create a new bookmark collection
   * @param userId - The ID of the user
   * @param name - The name of the collection
   * @param description - The description of the collection (optional)
   * @returns The created bookmark collection
   */
  async createCollection(
    userId: string,
    name: string,
    description?: string,
  ): Promise<BookmarkCollection> {
    const startTime = Date.now();
    try {
      // Check if a collection with the same name already exists for this user
      const existingCollection = await prisma.bookmarkCollection.findFirst({
        where: {
          user_id: userId,
          name,
        },
      });

      if (existingCollection) {
        throw createAppError(
          `Collection with name "${name}" already exists`,
          409,
        );
      }

      const collection = await prisma.bookmarkCollection.create({
        data: {
          user_id: userId,
          name,
          description,
        },
      });

      // Invalidate cache
      await this.invalidateUserCollectionsCache(userId);

      return collection;
    } catch (error) {
      logger.error('Error creating bookmark collection:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Update a bookmark collection
   * @param collectionId - The ID of the collection
   * @param userId - The ID of the user (for authorization)
   * @param data - The data to update
   * @returns The updated bookmark collection
   */
  async updateCollection(
    collectionId: string,
    userId: string,
    data: { name?: string; description?: string },
  ): Promise<BookmarkCollection> {
    const startTime = Date.now();
    try {
      // Check if the collection exists and belongs to the user
      const collection = await prisma.bookmarkCollection.findUnique({
        where: { id: collectionId },
      });

      if (!collection) {
        throw createAppError('Collection not found', 404);
      }

      if (collection.user_id !== userId) {
        throw createAppError('Unauthorized', 403);
      }

      // If name is being updated, check for duplicates
      if (data.name && data.name !== collection.name) {
        const existingCollection = await prisma.bookmarkCollection.findFirst({
          where: {
            user_id: userId,
            name: data.name,
            id: { not: collectionId },
          },
        });

        if (existingCollection) {
          throw createAppError(
            `Collection with name "${data.name}" already exists`,
            409,
          );
        }
      }

      const updatedCollection = await prisma.bookmarkCollection.update({
        where: { id: collectionId },
        data,
      });

      // Invalidate cache
      await this.invalidateUserCollectionsCache(userId);
      await this.invalidateCollectionCache(collectionId, userId);

      return updatedCollection;
    } catch (error) {
      logger.error('Error updating bookmark collection:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Delete a bookmark collection
   * @param collectionId - The ID of the collection
   * @param userId - The ID of the user (for authorization)
   * @returns True if deleted successfully
   */
  async deleteCollection(
    collectionId: string,
    userId: string,
  ): Promise<boolean> {
    const startTime = Date.now();
    try {
      // Check if the collection exists and belongs to the user
      const collection = await prisma.bookmarkCollection.findUnique({
        where: { id: collectionId },
      });

      if (!collection) {
        throw createAppError('Collection not found', 404);
      }

      if (collection.user_id !== userId) {
        throw createAppError('Unauthorized', 403);
      }

      // Update all bookmarks in this collection to have no collection
      await prisma.userChallengeBookmark.updateMany({
        where: { collection_id: collectionId },
        data: { collection_id: null },
      });

      // Delete the collection
      await prisma.bookmarkCollection.delete({
        where: { id: collectionId },
      });

      // Invalidate cache
      await this.invalidateUserCollectionsCache(userId);
      await this.invalidateCollectionCache(collectionId, userId);

      return true;
    } catch (error) {
      logger.error('Error deleting bookmark collection:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Add bookmarks to a collection
   * @param collectionId - The ID of the collection
   * @param userId - The ID of the user (for authorization)
   * @param bookmarkIds - Array of bookmark IDs to add to the collection
   * @returns The updated bookmark collection
   */
  async addBookmarksToCollection(
    collectionId: string,
    userId: string,
    bookmarkIds: string[],
  ) {
    const startTime = Date.now();
    try {
      // Check if the collection exists and belongs to the user
      const collection = await prisma.bookmarkCollection.findUnique({
        where: { id: collectionId },
      });

      if (!collection) {
        throw createAppError('Collection not found', 404);
      }

      if (collection.user_id !== userId) {
        throw createAppError('Unauthorized', 403);
      }

      // Verify all bookmarks exist and belong to the user
      const bookmarks = await prisma.userChallengeBookmark.findMany({
        where: {
          id: { in: bookmarkIds },
          user_id: userId,
        },
      });

      if (bookmarks.length !== bookmarkIds.length) {
        throw createAppError('One or more bookmarks not found', 404);
      }

      // Update all bookmarks to be in this collection
      await prisma.userChallengeBookmark.updateMany({
        where: {
          id: { in: bookmarkIds },
          user_id: userId,
        },
        data: {
          collection_id: collectionId,
          updated_at: new Date(),
        },
      });

      // Invalidate cache
      await this.invalidateUserCollectionsCache(userId);
      await this.invalidateCollectionCache(collectionId, userId);

      // Return the updated collection
      return this.getCollectionById(collectionId, userId);
    } catch (error) {
      logger.error('Error adding bookmarks to collection:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Remove bookmarks from a collection
   * @param collectionId - The ID of the collection
   * @param userId - The ID of the user (for authorization)
   * @param bookmarkIds - Array of bookmark IDs to remove from the collection
   * @returns The updated bookmark collection
   */
  async removeBookmarksFromCollection(
    collectionId: string,
    userId: string,
    bookmarkIds: string[],
  ) {
    const startTime = Date.now();
    try {
      // Check if the collection exists and belongs to the user
      const collection = await prisma.bookmarkCollection.findUnique({
        where: { id: collectionId },
      });

      if (!collection) {
        throw createAppError('Collection not found', 404);
      }

      if (collection.user_id !== userId) {
        throw createAppError('Unauthorized', 403);
      }

      // Update all bookmarks to have no collection
      await prisma.userChallengeBookmark.updateMany({
        where: {
          id: { in: bookmarkIds },
          user_id: userId,
          collection_id: collectionId,
        },
        data: {
          collection_id: null,
          updated_at: new Date(),
        },
      });

      // Invalidate cache
      await this.invalidateUserCollectionsCache(userId);
      await this.invalidateCollectionCache(collectionId, userId);

      // Return the updated collection
      return this.getCollectionById(collectionId, userId);
    } catch (error) {
      logger.error('Error removing bookmarks from collection:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Move bookmarks to a different collection
   * @param sourceCollectionId - The ID of the source collection
   * @param targetCollectionId - The ID of the target collection
   * @param userId - The ID of the user (for authorization)
   * @param bookmarkIds - Array of bookmark IDs to move
   * @returns The updated target collection
   */
  async moveBookmarks(
    sourceCollectionId: string,
    targetCollectionId: string,
    userId: string,
    bookmarkIds: string[],
  ) {
    const startTime = Date.now();
    try {
      // Check if both collections exist and belong to the user
      const [sourceCollection, targetCollection] = await Promise.all([
        prisma.bookmarkCollection.findUnique({
          where: { id: sourceCollectionId },
        }),
        prisma.bookmarkCollection.findUnique({
          where: { id: targetCollectionId },
        }),
      ]);

      if (!sourceCollection || !targetCollection) {
        throw createAppError('One or both collections not found', 404);
      }

      if (
        sourceCollection.user_id !== userId ||
        targetCollection.user_id !== userId
      ) {
        throw createAppError('Unauthorized', 403);
      }

      // Update all bookmarks to be in the target collection
      await prisma.userChallengeBookmark.updateMany({
        where: {
          id: { in: bookmarkIds },
          user_id: userId,
          collection_id: sourceCollectionId,
        },
        data: {
          collection_id: targetCollectionId,
          updated_at: new Date(),
        },
      });

      // Invalidate cache
      await this.invalidateUserCollectionsCache(userId);
      await this.invalidateCollectionCache(sourceCollectionId, userId);
      await this.invalidateCollectionCache(targetCollectionId, userId);

      // Return the updated target collection
      return this.getCollectionById(targetCollectionId, userId);
    } catch (error) {
      logger.error('Error moving bookmarks between collections:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Invalidate user collections cache
   * @param userId - The ID of the user
   */
  private async invalidateUserCollectionsCache(userId: string) {
    await deleteCache(
      `${BookmarkCollectionRepository.CACHE_PREFIX}${userId}:collections`,
    );
  }

  /**
   * Invalidate collection cache
   * @param collectionId - The ID of the collection
   * @param userId - The ID of the user
   */
  private async invalidateCollectionCache(
    collectionId: string,
    userId: string,
  ) {
    await deleteCache(
      `${BookmarkCollectionRepository.CACHE_PREFIX}${userId}:collection:${collectionId}`,
    );
  }
}
