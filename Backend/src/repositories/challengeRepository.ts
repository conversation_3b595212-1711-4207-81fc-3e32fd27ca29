import {
  Challenge,
  ChallengeBoilerplate,
  ChallengeCategory,
  ChallengeExample,
  ChallengeStatus,
  ChallengeSubmission,
  Difficulty,
  Prisma,
  SubmissionStatus,
  TestCase,
} from '@prisma/client';

import prisma from '@/lib/prisma';
import { invalidateCachePattern } from '@/services/cacheService';
import { ResourceStats, SubmissionData } from '@/types';
import { executeCode } from '@/utils/codeExecutor';
import logger from '@/utils/logger';

import { createAppError } from '../utils/errorHandler';
import BaseRepository from './baseRepository';

export interface ChallengeData {
  title: string;
  description: string;
  points: number;
  difficulty: Difficulty;
  category: ChallengeCategory;
  input_format: string;
  output_format: string;
  example_input: string;
  example_output: string;
  constraints: string;
  function_signature: string;
  time_limit?: number;
  memory_limit?: number;
  tags: string[];
  topic_id: string;
  explanation?: string;
  test_cases?: {
    input: string;
    output: string;
    is_hidden?: boolean;
    order_index?: number;
  }[];
  examples?: {
    input: string;
    output: string;
    explanation?: string;
    order_index: number;
  }[];
  boilerplates?: {
    language: string;
    boilerplate_code: string;
  }[];
}

/**
 * Repository for Challenge entity
 * Extends BaseRepository with Challenge as the entity type
 */
export default class ChallengeRepository extends BaseRepository<Challenge> {
  constructor() {
    super(prisma.challenge);
  }
  async createChallenge(data: ChallengeData): Promise<Challenge> {
    const { topic_id, test_cases, examples, boilerplates, ...challenge_data } =
      data;
    return prisma.challenge.create({
      data: {
        ...challenge_data,
        topic: { connect: { id: topic_id } },
        test_cases: test_cases ? { create: test_cases } : undefined,
        examples: examples ? { create: examples } : undefined,
        boilerplates: boilerplates ? { create: boilerplates } : undefined,
      },
      include: {
        test_cases: true,
        examples: true,
        boilerplates: true,
      },
    });
  }

  async updateChallenge(
    id: string,
    data: Partial<ChallengeData>,
  ): Promise<Challenge> {
    const { test_cases, examples, boilerplates, ...challenge_data } = data;

    return prisma.challenge.update({
      where: { id },
      data: {
        ...challenge_data,
        test_cases: test_cases
          ? { deleteMany: {}, create: test_cases }
          : undefined,
        examples: examples ? { deleteMany: {}, create: examples } : undefined,
        boilerplates: boilerplates
          ? { deleteMany: {}, create: boilerplates }
          : undefined,
      },
      include: {
        test_cases: true,
        examples: true,
        boilerplates: true,
      },
    });
  }

  async getChallenge(id: string): Promise<
    Challenge & {
      test_cases: TestCase[];
      examples: ChallengeExample[];
      boilerplates: ChallengeBoilerplate[];
    }
  > {
    const challenge = await prisma.challenge.findUnique({
      where: { id },
      include: {
        test_cases: {
          where: { is_hidden: false },
          orderBy: { order_index: 'asc' },
        },
        examples: { orderBy: { order_index: 'asc' } },
        boilerplates: true,
      },
    });

    if (!challenge) throw createAppError('Challenge not found', 404);
    return challenge;
  }

  async getAllChallenges(filters?: {
    difficulty?: Difficulty;
    category?: string;
    tags?: string[];
    userId?: string;
    status?: 'completed' | 'in_progress' | 'not_started';
    sort?: 'newest' | 'most_popular' | 'highest_rated' | 'most_submissions';
  }) {
    // Build the where clause
    const where: Prisma.ChallengeWhereInput = {
      difficulty: filters?.difficulty,
      category: filters?.category as ChallengeCategory,
      tags: filters?.tags ? { hasEvery: filters.tags } : undefined,
    };

    // Build the orderBy clause based on sort parameter
    let orderBy: Prisma.ChallengeOrderByWithRelationInput = {
      created_at: 'desc',
    };
    if (filters?.sort) {
      switch (filters.sort) {
        case 'newest':
          orderBy = { created_at: 'desc' };
          break;
        case 'most_popular':
          orderBy = { bookmarks: { _count: 'desc' } };
          break;
        case 'highest_rated':
          // Assuming you have a rating field or relationship
          orderBy = { points: 'desc' }; // Fallback to points if no rating
          break;
        case 'most_submissions':
          orderBy = { submissions: { _count: 'desc' } };
          break;
      }
    }

    // Get all challenges with the specified filters
    const challenges = await prisma.challenge.findMany({
      where,
      orderBy,
      include: {
        _count: {
          select: {
            submissions: { where: { status: SubmissionStatus.accepted } },
            bookmarks: true,
          },
        },
        bookmarks: filters?.userId
          ? {
              where: { user_id: filters.userId },
              select: { id: true },
            }
          : undefined,
        submissions: filters?.userId
          ? {
              where: { user_id: filters.userId },
              orderBy: { created_at: 'desc' },
              take: 1,
            }
          : undefined,
      },
    });

    // Transform the result to include isBookmarked flag and status
    return challenges
      .map((challenge) => {
        // Determine challenge status for the user
        let status = 'not_started';
        if (
          filters?.userId &&
          challenge.submissions &&
          challenge.submissions.length > 0
        ) {
          status =
            challenge.submissions[0].status === SubmissionStatus.accepted
              ? 'completed'
              : 'in_progress';
        }

        // Filter by status if requested
        if (filters?.status && status !== filters.status) {
          return null; // Will be filtered out below
        }

        return {
          ...challenge,
          isBookmarked: filters?.userId
            ? challenge.bookmarks.length > 0
            : false,
          status,
          // Remove the bookmarks and submissions arrays from the response
          bookmarks: undefined,
          submissions: undefined,
        };
      })
      .filter(Boolean); // Remove null entries (filtered by status)
  }

  async submitChallenge(data: SubmissionData): Promise<ChallengeSubmission> {
    const challenge = await this.getChallenge(data.challenge_id);
    const test_cases = await prisma.testCase.findMany({
      where: { challenge_id: data.challenge_id },
    });

    // Track detailed execution metrics
    const executionDetails: any[] = [];
    let compilationTime = 0;
    let ioTime = 0;
    let cpuTime = 0;

    const results = await Promise.all(
      test_cases.map(async (test_case) => {
        try {
          const startTime = Date.now();
          const result = await executeCode({
            code: data.code,
            language: data.language,
            input: test_case.input,
            timeLimit: challenge.time_limit ?? 0,
            memoryLimit: challenge.memory_limit ?? 0,
          });
          const endTime = Date.now();

          // Track detailed metrics for each test case
          const testCaseMetrics = {
            test_case_id: test_case.id,
            execution_time: result.executionTime,
            memory_used: result.memoryUsed,
            io_time: result.ioTime || 0,
            cpu_time: result.cpuTime || 0,
            compilation_time: result.compilationTime || 0,
            total_time: endTime - startTime,
          };

          executionDetails.push(testCaseMetrics);

          // Accumulate metrics
          compilationTime += result.compilationTime || 0;
          ioTime += result.ioTime || 0;
          cpuTime += result.cpuTime || 0;

          return {
            passed: result.output.trim() === test_case.output.trim(),
            execution_time: result.executionTime,
            memory_used: result.memoryUsed,
          };
        } catch (error) {
          logger.error('Code execution error:', error);
          return { passed: false, error: (error as Error).message };
        }
      }),
    );

    const allPassed = results.every((r) => r.passed);
    const avgExecutionTime =
      results.reduce((acc, r) => acc + (r.execution_time || 0), 0) /
      results.length;
    const maxMemoryUsed = Math.max(...results.map((r) => r.memory_used || 0));

    // Create the submission
    const submission = await prisma.challengeSubmission.create({
      data: {
        ...data,
        status: allPassed
          ? SubmissionStatus.accepted
          : SubmissionStatus.wrong_answer,
        runtime_ms: avgExecutionTime,
        memory_used_kb: maxMemoryUsed,
        feedback: results.map((r) => r.error).join('\n'),
        score: allPassed ? this.calculatePoints(challenge.difficulty) : 0,
      },
    });

    // Record detailed metrics
    await prisma.submissionMetrics.create({
      data: {
        submission_id: submission.id,
        memory_peak_kb: maxMemoryUsed,
        cpu_time_ms: cpuTime / test_cases.length, // Average CPU time
        io_time_ms: ioTime / test_cases.length, // Average I/O time
        compilation_time_ms: compilationTime / test_cases.length, // Average compilation time
        execution_details: executionDetails,
      },
    });

    // If the submission was successful, update user points
    if (allPassed) {
      await prisma.userPoints.upsert({
        where: { user_id: data.user_id },
        update: {
          points: { increment: this.calculatePoints(challenge.difficulty) },
        },
        create: {
          user_id: data.user_id,
          points: this.calculatePoints(challenge.difficulty),
        },
      });

      // Update language metrics
      await this.updateLanguageMetrics(data.challenge_id, data.language);

      // Generate optimization suggestions
      await this.generateOptimizationSuggestions(submission.id);
    }

    return submission;
  }

  async getLeaderboard(challengeId?: string) {
    const leaderboard = await prisma.userPoints.findMany({
      orderBy: { points: 'desc' },
      take: 100,
      include: { user: { select: { username: true, avatar_url: true } } },
    });

    if (!challengeId) return leaderboard;

    const submissions = await prisma.challengeSubmission.findMany({
      where: {
        challenge_id: challengeId,
        status: 'accepted',
        user_id: { in: leaderboard.map((l) => l.user_id) },
      },
      orderBy: { runtime_ms: 'asc' },
    });

    return leaderboard.map((l) => ({
      ...l,
      bestSubmission: submissions.find((s) => s.user_id === l.user_id),
    }));
  }

  async getChallengeStats(): Promise<ResourceStats> {
    const [total, active, pending, reported] = await Promise.all([
      this.count(),
      this.count({ where: { status: 'ACTIVE' } }),
      this.count({ where: { status: 'PENDING' } }),
      // TODO: Move this to relevant repo
      prisma.contentReport.count({ where: { content_type: 'CHALLENGE' } }),
    ]);

    return { total, active, pending, reported };
  }

  async manageChallenge(challenge_id: string, action: string) {
    const challenge = await prisma.challenge.findUnique({
      where: { id: challenge_id },
    });
    if (!challenge) throw createAppError('Challenge not found', 404);

    switch (action) {
      case 'activate':
        await prisma.challenge.update({
          where: { id: challenge_id },
          data: { status: 'ACTIVE' },
        });
        break;
      case 'deactivate':
        await prisma.challenge.update({
          where: { id: challenge_id },
          data: { status: 'ARCHIVED' },
        });
        break;
      case 'delete':
        await prisma.challenge.delete({ where: { id: challenge_id } });
        break;
      default:
        throw createAppError('Invalid action', 400);
    }

    await invalidateCachePattern(`challenge:${challenge_id}:*`);
  }

  private calculatePoints(difficulty: Difficulty) {
    switch (difficulty) {
      case Difficulty.EASY:
        return 10;
      case Difficulty.MEDIUM:
        return 20;
      case Difficulty.HARD:
        return 30;
      default:
        return 10;
    }
  }

  /**
   * Get all examples for a challenge
   * @param challengeId - The ID of the challenge
   * @returns An array of challenge examples
   */
  async getChallengeExamples(challengeId: string): Promise<ChallengeExample[]> {
    return prisma.challengeExample.findMany({
      where: { challenge_id: challengeId },
      orderBy: { order_index: 'asc' },
    });
  }

  /**
   * Get all boilerplates for a challenge
   * @param challengeId - The ID of the challenge
   * @returns An array of challenge boilerplates
   */
  async getChallengeBoilerplates(
    challengeId: string,
  ): Promise<ChallengeBoilerplate[]> {
    return prisma.challengeBoilerplate.findMany({
      where: { challenge_id: challengeId },
    });
  }

  /**
   * Get a specific boilerplate by language
   * @param challengeId - The ID of the challenge
   * @param language - The programming language
   * @returns The boilerplate or null if not found
   */
  async getChallengeBoilerplateByLanguage(
    challengeId: string,
    language: string,
  ): Promise<ChallengeBoilerplate | null> {
    return prisma.challengeBoilerplate.findFirst({
      where: {
        challenge_id: challengeId,
        language,
      },
    });
  }

  /**
   * Get all test cases for a challenge
   * @param challengeId - The ID of the challenge
   * @param includeHidden - Whether to include hidden test cases
   * @returns An array of test cases
   */
  async getChallengeTestCases(
    challengeId: string,
    includeHidden: boolean = false,
  ): Promise<TestCase[]> {
    return prisma.testCase.findMany({
      where: {
        challenge_id: challengeId,
        ...(includeHidden ? {} : { is_hidden: false }),
      },
      orderBy: { order_index: 'asc' },
    });
  }

  /**
   * Check if a challenge is bookmarked by a user
   * @param challengeId - The ID of the challenge
   * @param userId - The ID of the user
   * @returns True if bookmarked, false otherwise
   */
  async isBookmarked(challengeId: string, userId: string): Promise<boolean> {
    const bookmark = await prisma.userChallengeBookmark.findFirst({
      where: {
        challenge_id: challengeId,
        user_id: userId,
      },
    });

    return !!bookmark;
  }

  /**
   * Get bookmark count for a challenge
   * @param challengeId - The ID of the challenge
   * @returns The number of bookmarks
   */
  async getBookmarkCount(challengeId: string): Promise<number> {
    return prisma.userChallengeBookmark.count({
      where: { challenge_id: challengeId },
    });
  }

  /**
   * Save a user's filter preset
   * @param userId - The ID of the user
   * @param name - The name of the preset
   * @param filters - The filter configuration
   * @returns The created filter preset
   */
  async saveFilterPreset(
    userId: string,
    name: string,
    filters: Record<string, any>,
  ) {
    return prisma.userFilterPreset.upsert({
      where: {
        user_id_name: {
          user_id: userId,
          name,
        },
      },
      update: {
        filters: filters as any,
      },
      create: {
        user_id: userId,
        name,
        filters: filters as any,
        type: 'CHALLENGE',
      },
    });
  }

  /**
   * Get a user's filter presets
   * @param userId - The ID of the user
   * @returns An array of filter presets
   */
  async getFilterPresets(userId: string) {
    return prisma.userFilterPreset.findMany({
      where: {
        user_id: userId,
        type: 'CHALLENGE',
      },
      orderBy: {
        created_at: 'desc',
      },
    });
  }

  /**
   * Delete a user's filter preset
   * @param userId - The ID of the user
   * @param presetId - The ID of the preset to delete
   * @returns The deleted preset
   */
  async deleteFilterPreset(userId: string, presetId: string) {
    return prisma.userFilterPreset.delete({
      where: {
        id: presetId,
        user_id: userId,
      },
    });
  }

  /**
   * Update language metrics for a challenge
   * @param challengeId - The ID of the challenge
   * @param language - The programming language
   */
  async updateLanguageMetrics(challengeId: string, language: string) {
    try {
      // Get all successful submissions for this challenge with the same language
      const submissions = await prisma.challengeSubmission.findMany({
        where: {
          challenge_id: challengeId,
          language,
          status: SubmissionStatus.accepted,
        },
      });

      if (submissions.length === 0) {
        return;
      }

      // Calculate metrics
      const runtimes = submissions
        .map((s) => s.runtime_ms)
        .filter((r): r is number => r !== null && r !== undefined);

      const memories = submissions
        .map((s) => s.memory_used_kb)
        .filter((m): m is number => m !== null && m !== undefined);

      const avgRuntime =
        runtimes.length > 0
          ? runtimes.reduce((sum, r) => sum + r, 0) / runtimes.length
          : 0;

      const avgMemory =
        memories.length > 0
          ? memories.reduce((sum, m) => sum + m, 0) / memories.length
          : 0;

      const minRuntime = runtimes.length > 0 ? Math.min(...runtimes) : 0;

      const minMemory = memories.length > 0 ? Math.min(...memories) : 0;

      // Update or create language metrics
      await prisma.languageMetrics.upsert({
        where: {
          challenge_id_language: {
            challenge_id: challengeId,
            language,
          },
        },
        update: {
          avg_runtime_ms: avgRuntime,
          avg_memory_kb: avgMemory,
          min_runtime_ms: minRuntime,
          min_memory_kb: minMemory,
          submission_count: submissions.length,
          updated_at: new Date(),
        },
        create: {
          challenge_id: challengeId,
          language,
          avg_runtime_ms: avgRuntime,
          avg_memory_kb: avgMemory,
          min_runtime_ms: minRuntime,
          min_memory_kb: minMemory,
          submission_count: submissions.length,
        },
      });
    } catch (error) {
      logger.error('Error updating language metrics:', error);
      throw createAppError('Failed to update language metrics', 500);
    }
  }

  /**
   * Generate optimization suggestions for a submission
   * @param submissionId - The ID of the submission
   */
  async generateOptimizationSuggestions(submissionId: string) {
    try {
      const submission = await prisma.challengeSubmission.findUnique({
        where: { id: submissionId },
        include: {
          metrics: true,
          challenge: true,
        },
      });

      if (!submission) {
        throw createAppError('Submission not found', 404);
      }

      // Get language metrics for comparison
      const languageMetrics = await prisma.languageMetrics.findUnique({
        where: {
          challenge_id_language: {
            challenge_id: submission.challenge_id,
            language: submission.language,
          },
        },
      });

      if (!languageMetrics) {
        return; // No metrics to compare against
      }

      const suggestions: Array<{
        suggestion_type: string;
        suggestion: string;
        code_snippet?: string;
        line_start?: number;
        line_end?: number;
        priority: number;
      }> = [];

      // Runtime optimization suggestions
      if (submission.runtime_ms && languageMetrics.min_runtime_ms) {
        const runtimeRatio =
          submission.runtime_ms / languageMetrics.min_runtime_ms;

        if (runtimeRatio > 2) {
          suggestions.push({
            suggestion_type: 'time',
            suggestion: `Your solution is ${runtimeRatio.toFixed(1)}x slower than the fastest solution. There might be room for optimization.`,
            priority: 1,
          });

          // Add language-specific suggestions
          if (submission.language === 'python') {
            suggestions.push({
              suggestion_type: 'time',
              suggestion:
                'Consider using list comprehensions instead of loops for better performance.',
              priority: 2,
            });
          } else if (submission.language === 'javascript') {
            suggestions.push({
              suggestion_type: 'time',
              suggestion:
                'Consider using Map or Set instead of objects or arrays for lookups.',
              priority: 2,
            });
          } else if (submission.language === 'java') {
            suggestions.push({
              suggestion_type: 'time',
              suggestion:
                'Consider using more efficient data structures like HashSet for lookups.',
              priority: 2,
            });
          }
        }
      }

      // Memory optimization suggestions
      if (submission.memory_used_kb && languageMetrics.min_memory_kb) {
        const memoryRatio =
          submission.memory_used_kb / languageMetrics.min_memory_kb;

        if (memoryRatio > 2) {
          suggestions.push({
            suggestion_type: 'memory',
            suggestion: `Your solution uses ${memoryRatio.toFixed(1)}x more memory than the most efficient solution.`,
            priority: 1,
          });

          // Add language-specific suggestions
          if (submission.language === 'python') {
            suggestions.push({
              suggestion_type: 'memory',
              suggestion:
                'Consider using generators instead of lists to reduce memory usage.',
              priority: 2,
            });
          } else if (submission.language === 'javascript') {
            suggestions.push({
              suggestion_type: 'memory',
              suggestion:
                'Consider using primitive types instead of objects where possible.',
              priority: 2,
            });
          } else if (submission.language === 'java') {
            suggestions.push({
              suggestion_type: 'memory',
              suggestion:
                'Consider using primitive arrays instead of ArrayList for better memory efficiency.',
              priority: 2,
            });
          }
        }
      }

      // Code quality suggestions
      const codeLines = submission.code.split('\n');
      if (codeLines.length > 100) {
        suggestions.push({
          suggestion_type: 'code_quality',
          suggestion:
            'Your solution is quite long. Consider refactoring for better readability.',
          priority: 3,
        });
      }

      // Check for nested loops (simple heuristic)
      const nestedLoopPattern =
        /for.*\s*for|while.*\s*while|for.*\s*while|while.*\s*for/;
      if (nestedLoopPattern.test(submission.code)) {
        suggestions.push({
          suggestion_type: 'time',
          suggestion:
            "Your solution contains nested loops, which might lead to O(n²) time complexity. Consider if there's a more efficient approach.",
          priority: 2,
        });
      }

      // Save suggestions to database
      await prisma.optimizationSuggestion.deleteMany({
        where: { submission_id: submissionId },
      });

      for (const suggestion of suggestions) {
        await prisma.optimizationSuggestion.create({
          data: {
            submission_id: submissionId,
            ...suggestion,
          },
        });
      }
    } catch (error) {
      logger.error('Error generating optimization suggestions:', error);
      // Don't throw error to avoid failing the submission process
    }
  }
}
