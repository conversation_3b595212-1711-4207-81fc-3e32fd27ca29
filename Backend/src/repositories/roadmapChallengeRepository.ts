import {
  NotificationType,
  ProgressStatus,
  RoadmapChallenge,
} from '@prisma/client';

import prisma from '@/lib/prisma';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

/**
 * Repository for RoadmapChallenge entity
 * Extends BaseRepository with RoadmapChallenge as the entity type
 */
export default class RoadmapChallengeRepository extends BaseRepository<RoadmapChallenge> {
  constructor() {
    super(prisma.roadmapChallenge);
  }

  /**
   * Add a challenge to a roadmap
   * @param roadmapId - The ID of the roadmap
   * @param challengeId - The ID of the challenge
   * @param order - The order of the challenge in the roadmap
   * @param isRequired - Whether the challenge is required for roadmap completion
   * @returns The created roadmap challenge
   */
  async addChallengeToRoadmap(
    roadmapId: string,
    challengeId: string,
    order: number = 0,
    isRequired: boolean = true,
  ): Promise<RoadmapChallenge> {
    try {
      // Check if the roadmap exists
      const roadmap = await prisma.roadmap.findUnique({
        where: { id: roadmapId },
      });

      if (!roadmap) {
        throw createAppError('Roadmap not found', 404);
      }

      // Check if the challenge exists
      const challenge = await prisma.challenge.findUnique({
        where: { id: challengeId },
      });

      if (!challenge) {
        throw createAppError('Challenge not found', 404);
      }

      // Check if the challenge is already in the roadmap
      const existingRoadmapChallenge = await prisma.roadmapChallenge.findUnique(
        {
          where: {
            roadmap_id_challenge_id: {
              roadmap_id: roadmapId,
              challenge_id: challengeId,
            },
          },
        },
      );

      if (existingRoadmapChallenge) {
        throw createAppError('Challenge is already in the roadmap', 400);
      }

      // Add the challenge to the roadmap
      return prisma.roadmapChallenge.create({
        data: {
          roadmap_id: roadmapId,
          challenge_id: challengeId,
          order,
          is_required: isRequired,
        },
      });
    } catch (error) {
      logger.error('Error adding challenge to roadmap:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to add challenge to roadmap', 500);
    }
  }

  /**
   * Remove a challenge from a roadmap
   * @param roadmapId - The ID of the roadmap
   * @param challengeId - The ID of the challenge
   * @returns The deleted roadmap challenge
   */
  async removeChallengeFromRoadmap(
    roadmapId: string,
    challengeId: string,
  ): Promise<RoadmapChallenge> {
    try {
      // Check if the roadmap challenge exists
      const roadmapChallenge = await prisma.roadmapChallenge.findUnique({
        where: {
          roadmap_id_challenge_id: {
            roadmap_id: roadmapId,
            challenge_id: challengeId,
          },
        },
      });

      if (!roadmapChallenge) {
        throw createAppError('Challenge is not in the roadmap', 404);
      }

      // Remove the challenge from the roadmap
      return prisma.roadmapChallenge.delete({
        where: {
          id: roadmapChallenge.id,
        },
      });
    } catch (error) {
      logger.error('Error removing challenge from roadmap:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to remove challenge from roadmap', 500);
    }
  }

  /**
   * Update a roadmap challenge
   * @param roadmapId - The ID of the roadmap
   * @param challengeId - The ID of the challenge
   * @param data - The data to update
   * @returns The updated roadmap challenge
   */
  async updateRoadmapChallenge(
    roadmapId: string,
    challengeId: string,
    data: {
      order?: number;
      is_required?: boolean;
    },
  ): Promise<RoadmapChallenge> {
    try {
      // Check if the roadmap challenge exists
      const roadmapChallenge = await prisma.roadmapChallenge.findUnique({
        where: {
          roadmap_id_challenge_id: {
            roadmap_id: roadmapId,
            challenge_id: challengeId,
          },
        },
      });

      if (!roadmapChallenge) {
        throw createAppError('Challenge is not in the roadmap', 404);
      }

      // Update the roadmap challenge
      return prisma.roadmapChallenge.update({
        where: {
          id: roadmapChallenge.id,
        },
        data,
      });
    } catch (error) {
      logger.error('Error updating roadmap challenge:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to update roadmap challenge', 500);
    }
  }

  /**
   * Get all challenges for a roadmap
   * @param roadmapId - The ID of the roadmap
   * @returns Array of challenges with roadmap challenge data
   */
  async getChallengesByRoadmap(roadmapId: string) {
    try {
      // Check if the roadmap exists
      const roadmap = await prisma.roadmap.findUnique({
        where: { id: roadmapId },
      });

      if (!roadmap) {
        throw createAppError('Roadmap not found', 404);
      }

      // Get all challenges for the roadmap
      const roadmapChallenges = await prisma.roadmapChallenge.findMany({
        where: { roadmap_id: roadmapId },
        include: {
          challenge: {
            include: {
              topic: true,
            },
          },
        },
        orderBy: { order: 'asc' },
      });

      return roadmapChallenges.map((rc) => ({
        ...rc.challenge,
        order: rc.order,
        is_required: rc.is_required,
        roadmap_challenge_id: rc.id,
      }));
    } catch (error) {
      logger.error('Error getting challenges by roadmap:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to get challenges by roadmap', 500);
    }
  }

  /**
   * Get all roadmaps for a challenge
   * @param challengeId - The ID of the challenge
   * @returns Array of roadmaps with roadmap challenge data
   */
  async getRoadmapsByChallenge(challengeId: string) {
    try {
      // Check if the challenge exists
      const challenge = await prisma.challenge.findUnique({
        where: { id: challengeId },
      });

      if (!challenge) {
        throw createAppError('Challenge not found', 404);
      }

      // Get all roadmaps for the challenge
      const roadmapChallenges = await prisma.roadmapChallenge.findMany({
        where: { challenge_id: challengeId },
        include: {
          roadmap: true,
        },
      });

      return roadmapChallenges.map((rc) => ({
        ...rc.roadmap,
        order: rc.order,
        is_required: rc.is_required,
        roadmap_challenge_id: rc.id,
      }));
    } catch (error) {
      logger.error('Error getting roadmaps by challenge:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to get roadmaps by challenge', 500);
    }
  }

  /**
   * Get suggested challenges based on roadmap progress
   * @param userId - The ID of the user
   * @param roadmapId - The ID of the roadmap
   * @param limit - The maximum number of challenges to return
   * @returns Array of suggested challenges
   */
  async getSuggestedChallenges(
    userId: string,
    roadmapId: string,
    limit: number = 5,
  ) {
    try {
      // Check if the user is enrolled in the roadmap
      const userRoadmap = await prisma.userRoadmap.findUnique({
        where: {
          user_id_roadmap_id: {
            user_id: userId,
            roadmap_id: roadmapId,
          },
        },
      });

      if (!userRoadmap) {
        throw createAppError('User is not enrolled in the roadmap', 404);
      }

      // Get all challenges for the roadmap
      const roadmapChallenges = await prisma.roadmapChallenge.findMany({
        where: { roadmap_id: roadmapId },
        include: {
          challenge: true,
        },
        orderBy: { order: 'asc' },
      });

      // Get all completed challenges for the user
      const completedChallenges = await prisma.challengeSubmission.findMany({
        where: {
          user_id: userId,
          status: 'accepted',
          challenge: {
            roadmap_challenges: {
              some: {
                roadmap_id: roadmapId,
              },
            },
          },
        },
        select: {
          challenge_id: true,
        },
      });

      const completedChallengeIds = completedChallenges.map(
        (cc) => cc.challenge_id,
      );

      // Filter out completed challenges
      const incompleteChallenges = roadmapChallenges.filter(
        (rc) => !completedChallengeIds.includes(rc.challenge_id),
      );

      // Get user progress for roadmap topics
      const topicProgress = await prisma.progress.findMany({
        where: {
          user_id: userId,
          roadmap_id: roadmapId,
        },
        select: {
          topic_id: true,
          status: true,
        },
      });

      const completedTopicIds = topicProgress
        .filter((tp) => tp.status === ProgressStatus.COMPLETED)
        .map((tp) => tp.topic_id);

      // Prioritize challenges from completed topics
      const suggestedChallenges = incompleteChallenges
        .sort((a, b) => {
          // Prioritize required challenges
          if (a.is_required && !b.is_required) return -1;
          if (!a.is_required && b.is_required) return 1;

          // Prioritize challenges from completed topics
          const aTopicCompleted = completedTopicIds.includes(
            a.challenge.topic_id,
          );
          const bTopicCompleted = completedTopicIds.includes(
            b.challenge.topic_id,
          );
          if (aTopicCompleted && !bTopicCompleted) return -1;
          if (!aTopicCompleted && bTopicCompleted) return 1;

          // Sort by order
          return a.order - b.order;
        })
        .slice(0, limit)
        .map((rc) => ({
          ...rc.challenge,
          order: rc.order,
          is_required: rc.is_required,
          roadmap_challenge_id: rc.id,
        }));

      return suggestedChallenges;
    } catch (error) {
      logger.error('Error getting suggested challenges:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to get suggested challenges', 500);
    }
  }

  /**
   * Update roadmap progress when a challenge is completed
   * @param userId - The ID of the user
   * @param challengeId - The ID of the challenge
   */
  async updateRoadmapProgress(userId: string, challengeId: string) {
    try {
      // Get all roadmaps that contain this challenge
      const roadmapChallenges = await prisma.roadmapChallenge.findMany({
        where: { challenge_id: challengeId },
        select: {
          roadmap_id: true,
        },
      });

      const roadmapIds = roadmapChallenges.map((rc) => rc.roadmap_id);

      // For each roadmap, check if the user is enrolled
      for (const roadmapId of roadmapIds) {
        const userRoadmap = await prisma.userRoadmap.findUnique({
          where: {
            user_id_roadmap_id: {
              user_id: userId,
              roadmap_id: roadmapId,
            },
          },
        });

        if (!userRoadmap) {
          continue; // User is not enrolled in this roadmap
        }

        // Get the challenge to find its topic
        const challenge = await prisma.challenge.findUnique({
          where: { id: challengeId },
          select: {
            topic_id: true,
          },
        });

        if (!challenge) {
          continue; // Challenge not found
        }

        // Get all challenges for this topic in the roadmap
        const topicChallenges = await prisma.roadmapChallenge.findMany({
          where: {
            roadmap_id: roadmapId,
            challenge: {
              topic_id: challenge.topic_id,
            },
          },
          select: {
            challenge_id: true,
            is_required: true,
          },
        });

        // Get all required challenges for this topic
        const requiredChallenges = topicChallenges.filter(
          (tc) => tc.is_required,
        );

        // Get all completed challenges for this topic
        const completedChallenges = await prisma.challengeSubmission.findMany({
          where: {
            user_id: userId,
            status: 'accepted',
            challenge_id: {
              in: requiredChallenges.map((rc) => rc.challenge_id),
            },
          },
          select: {
            challenge_id: true,
          },
        });

        const completedChallengeIds = completedChallenges.map(
          (cc) => cc.challenge_id,
        );

        // Check if all required challenges for this topic are completed
        const allRequiredCompleted = requiredChallenges.every((rc) =>
          completedChallengeIds.includes(rc.challenge_id),
        );

        // Update topic progress if all required challenges are completed
        if (allRequiredCompleted) {
          await prisma.progress.upsert({
            where: {
              user_id_topic_id: {
                user_id: userId,
                topic_id: challenge.topic_id,
              },
            },
            update: {
              status: ProgressStatus.COMPLETED,
              updated_at: new Date(),
            },
            create: {
              user_id: userId,
              roadmap_id: roadmapId,
              topic_id: challenge.topic_id,
              status: ProgressStatus.COMPLETED,
            },
          });

          // Check if all topics in the roadmap are completed
          const roadmapTopics = await prisma.roadmapTopic.findMany({
            where: { roadmap_id: roadmapId },
            select: {
              topic_id: true,
            },
          });

          const topicIds = roadmapTopics.map((rt) => rt.topic_id);

          // Get all topic progress for this roadmap
          const topicProgress = await prisma.progress.findMany({
            where: {
              user_id: userId,
              roadmap_id: roadmapId,
              topic_id: {
                in: topicIds,
              },
            },
            select: {
              topic_id: true,
              status: true,
            },
          });

          const completedTopicIds = topicProgress
            .filter((tp) => tp.status === ProgressStatus.COMPLETED)
            .map((tp) => tp.topic_id);

          // Check if all topics are completed
          const allTopicsCompleted = topicIds.every((tid) =>
            completedTopicIds.includes(tid),
          );

          // If all topics are completed, update user roadmap status
          if (allTopicsCompleted) {
            await prisma.userRoadmap.update({
              where: {
                id: userRoadmap.id,
              },
              data: {
                status: ProgressStatus.COMPLETED,
                updated_at: new Date(),
              },
            });

            // Create a notification for roadmap completion
            await prisma.notification.create({
              data: {
                user_id: userId,
                title: 'Roadmap Completed',
                message: `Congratulations! You have completed the roadmap: ${roadmapId}`,
                type: NotificationType.achievement,
                link: `/roadmaps/${roadmapId}`,
              },
            });
          }
        }
      }
    } catch (error) {
      logger.error('Error updating roadmap progress:', error);
      // Don't throw error to avoid failing the submission process
    }
  }
}
