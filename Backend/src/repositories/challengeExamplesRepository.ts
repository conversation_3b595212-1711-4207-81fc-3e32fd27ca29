import { ChallengeExample } from '@prisma/client';

import prisma from '@/lib/prisma';

import BaseRepository from './baseRepository';

/**
 * Repository for ChallengeExample entity
 * Extends BaseRepository with ChallengeExample as the entity type
 */
export default class ChallengeExamplesRepository extends BaseRepository<ChallengeExample> {
  constructor() {
    super(prisma.challengeExample);
  }

  /**
   * Get all examples for a challenge
   * @param challengeId The ID of the challenge
   */
  async getExamplesByChallenge(challengeId: string) {
    return this.findMany({
      where: { challenge_id: challengeId },
      orderBy: { order_index: 'asc' },
    });
  }

  /**
   * Create a new example
   * @param data The example data
   */
  async createExample(data: {
    challenge_id: string;
    input: string;
    output: string;
    explanation?: string;
    order_index: number;
  }) {
    return this.create({
      data,
    });
  }

  /**
   * Update an example
   * @param id The ID of the example
   * @param data The updated example data
   */
  async updateExample(
    id: string,
    data: {
      input?: string;
      output?: string;
      explanation?: string;
      order_index?: number;
    },
  ) {
    return this.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete an example
   * @param id The ID of the example
   */
  async deleteExample(id: string) {
    return this.delete({
      where: { id },
    });
  }

  /**
   * Check if an example exists and belongs to a challenge
   * @param id The ID of the example
   * @param challengeId The ID of the challenge
   */
  async exampleBelongsToChallenge(id: string, challengeId: string) {
    const example = await this.findFirst({
      where: {
        id,
        challenge_id: challengeId,
      },
    });
    return !!example;
  }
}
