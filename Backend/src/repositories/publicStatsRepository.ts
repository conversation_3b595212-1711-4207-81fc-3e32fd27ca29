import prisma from '@/lib/prisma';

/**
 * Repository for PublicStats entity
 * Handles CRUD operations and specialized queries for publicStats
 */
export class PublicStatsRepository {
  async getPlatformStats() {
    const [userCount, roadmapCount, challengeCount, articleCount] =
      await Promise.all([
        prisma.user.count({ where: { deleted_at: null } }),
        prisma.roadmap.count({ where: { is_public: true } }),
        prisma.challenge.count(),
        prisma.article.count({ where: { status: 'APPROVED' } }),
      ]);

    // Format the stats for the frontend
    return {
      learningPaths: {
        stats: [
          { label: 'Active Users', value: `${userCount}+`, icon: 'FaUsers' },
          { label: 'Roadmaps', value: `${roadmapCount}+`, icon: 'FaRoad' },
          { label: 'Company Guides', value: '200+', icon: 'FaLaptopCode' },
          { label: 'Achievements', value: '100+', icon: 'FaTrophy' },
        ],
      },
      community: {
        stats: [
          { label: 'Active Users', value: `${userCount}+`, icon: 'FaUsers' },
          {
            label: 'Challenges',
            value: `${challengeCount}+`,
            icon: 'FaGamepad',
          },
          { label: 'Articles', value: `${articleCount}+`, icon: 'FaBook' },
          { label: 'Daily Streaks', value: '500+', icon: 'FaFire' },
        ],
      },
      battleZone: {
        stats: [
          { label: 'Battles', value: '1,000+', icon: 'FaGamepad' },
          { label: 'Participants', value: '5,000+', icon: 'FaUsers' },
          { label: 'Challenges', value: `${challengeCount}+`, icon: 'FaCode' },
          { label: 'Winners', value: '300+', icon: 'FaTrophy' },
        ],
      },
    };
  }
}
