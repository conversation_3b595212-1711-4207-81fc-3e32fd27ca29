import { Activity, PrismaClient } from '@prisma/client';

import prisma from '@/lib/prisma';

/**
 * Repository for Activity entity
 * Handles CRUD operations and specialized queries for activities
 */
export class ActivityRepository {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  async findMany(options: {
    where?: any;
    orderBy?: any;
    take?: number;
    skip?: number;
  }): Promise<Activity[]> {
    return this.prisma.activity.findMany({
      where: options.where,
      orderBy: options.orderBy || { timestamp: 'desc' },
      take: options.take,
      skip: options.skip,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            full_name: true,
            avatar_url: true,
          },
        },
      },
    });
  }

  async findById(id: string): Promise<Activity | null> {
    return this.prisma.activity.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            full_name: true,
            avatar_url: true,
          },
        },
      },
    });
  }

  async create(data: {
    user_id: string;
    type: string;
    description: string;
    roadmap_id?: string;
    roadmap_title?: string;
  }): Promise<Activity> {
    return this.prisma.activity.create({
      data,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            full_name: true,
            avatar_url: true,
          },
        },
      },
    });
  }

  async delete(id: string): Promise<Activity> {
    return this.prisma.activity.delete({
      where: { id },
    });
  }

  async countActivities(where: any): Promise<number> {
    return this.prisma.activity.count({
      where,
    });
  }
}

export default ActivityRepository;
