import { UserChallengeBookmark } from '@prisma/client';

import prisma from '@/lib/prisma';
import { deleteCache, getOrSetCache } from '@/services/cacheService';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

/**
 * Repository for UserChallengeBookmark entity
 * Extends BaseRepository with UserChallengeBookmark as the entity type
 */
export default class UserChallengeBookmarksRepository extends BaseRepository<UserChallengeBookmark> {
  private static readonly CACHE_PREFIX = 'bookmarks:';
  private static readonly CACHE_TTL = 3600; // 1 hour

  constructor() {
    super(prisma.userChallengeBookmark);
  }

  /**
   * Get all bookmarked challenges for a user
   * @param userId The ID of the user
   * @param collectionId Optional collection ID to filter by
   */
  async getBookmarkedChallenges(userId: string, collectionId?: string) {
    const startTime = Date.now();
    try {
      const cacheKey = `${UserChallengeBookmarksRepository.CACHE_PREFIX}${userId}:${collectionId || 'all'}`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          return prisma.userChallengeBookmark.findMany({
            where: {
              user_id: userId,
              ...(collectionId && { collection_id: collectionId }),
            },
            include: {
              challenge: {
                select: {
                  id: true,
                  title: true,
                  description: true,
                  difficulty: true,
                  category: true,
                  points: true,
                  tags: true,
                },
              },
              collection: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            orderBy: { created_at: 'desc' },
          });
        },
        { ttl: UserChallengeBookmarksRepository.CACHE_TTL },
      );
    } catch (error) {
      logger.error('Error fetching bookmarked challenges:', error);
      throw createAppError('Failed to fetch bookmarked challenges', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Fetching bookmarked challenges took ${duration}ms`);
    }
  }

  /**
   * Check if a challenge is already bookmarked by a user
   * @param userId The ID of the user
   * @param challengeId The ID of the challenge
   */
  async isBookmarked(userId: string, challengeId: string) {
    const startTime = Date.now();
    try {
      return await prisma.userChallengeBookmark.findFirst({
        where: {
          user_id: userId,
          challenge_id: challengeId,
        },
      });
    } catch (error) {
      logger.error('Error checking if challenge is bookmarked:', error);
      throw createAppError('Failed to check bookmark status', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Create a bookmark
   * @param userId The ID of the user
   * @param challengeId The ID of the challenge
   * @param collectionId Optional collection ID to add the bookmark to
   */
  async createBookmark(
    userId: string,
    challengeId: string,
    collectionId?: string,
  ) {
    const startTime = Date.now();
    try {
      // Check if collection exists and belongs to the user if provided
      if (collectionId) {
        const collection = await prisma.bookmarkCollection.findUnique({
          where: { id: collectionId },
        });

        if (!collection) {
          throw createAppError('Collection not found', 404);
        }

        if (collection.user_id !== userId) {
          throw createAppError('Unauthorized', 403);
        }
      }

      const bookmark = await prisma.userChallengeBookmark.create({
        data: {
          user_id: userId,
          challenge_id: challengeId,
          collection_id: collectionId,
        },
      });

      // Invalidate cache
      await this.invalidateUserBookmarksCache(userId);
      if (collectionId) {
        await deleteCache(
          `${UserChallengeBookmarksRepository.CACHE_PREFIX}${userId}:${collectionId}`,
        );
      }

      return bookmark;
    } catch (error) {
      logger.error('Error creating bookmark:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Delete a bookmark
   * @param bookmarkId The ID of the bookmark
   * @param userId The ID of the user (for authorization)
   */
  async deleteBookmark(bookmarkId: string, userId: string) {
    const startTime = Date.now();
    try {
      // Check if bookmark exists and belongs to the user
      const bookmark = await prisma.userChallengeBookmark.findUnique({
        where: { id: bookmarkId },
      });

      if (!bookmark) {
        throw createAppError('Bookmark not found', 404);
      }

      if (bookmark.user_id !== userId) {
        throw createAppError('Unauthorized', 403);
      }

      const collectionId = bookmark.collection_id;

      await prisma.userChallengeBookmark.delete({
        where: { id: bookmarkId },
      });

      // Invalidate cache
      await this.invalidateUserBookmarksCache(userId);
      if (collectionId) {
        await deleteCache(
          `${UserChallengeBookmarksRepository.CACHE_PREFIX}${userId}:${collectionId}`,
        );
      }

      return true;
    } catch (error) {
      logger.error('Error deleting bookmark:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Toggle bookmark for a challenge
   * @param userId The ID of the user
   * @param challengeId The ID of the challenge
   * @param collectionId Optional collection ID to add the bookmark to
   */
  async toggleBookmark(
    userId: string,
    challengeId: string,
    collectionId?: string,
  ) {
    const startTime = Date.now();
    try {
      // Check if already bookmarked
      const existingBookmark = await this.isBookmarked(userId, challengeId);

      if (existingBookmark) {
        // If already bookmarked, remove it
        await this.deleteBookmark(existingBookmark.id, userId);
        return { bookmarked: false, bookmark: null };
      } else {
        // If not bookmarked, add it
        const bookmark = await this.createBookmark(
          userId,
          challengeId,
          collectionId,
        );
        return { bookmarked: true, bookmark };
      }
    } catch (error) {
      logger.error('Error toggling bookmark:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Batch create bookmarks
   * @param userId The ID of the user
   * @param challengeIds Array of challenge IDs to bookmark
   * @param collectionId Optional collection ID to add the bookmarks to
   */
  async batchCreateBookmarks(
    userId: string,
    challengeIds: string[],
    collectionId?: string,
  ) {
    const startTime = Date.now();
    try {
      // Check if collection exists and belongs to the user if provided
      if (collectionId) {
        const collection = await prisma.bookmarkCollection.findUnique({
          where: { id: collectionId },
        });

        if (!collection) {
          throw createAppError('Collection not found', 404);
        }

        if (collection.user_id !== userId) {
          throw createAppError('Unauthorized', 403);
        }
      }

      // Check which challenges are already bookmarked
      const existingBookmarks = await prisma.userChallengeBookmark.findMany({
        where: {
          user_id: userId,
          challenge_id: { in: challengeIds },
        },
        select: {
          challenge_id: true,
        },
      });

      const existingChallengeIds = existingBookmarks.map((b) => b.challenge_id);
      const newChallengeIds = challengeIds.filter(
        (id) => !existingChallengeIds.includes(id),
      );

      // Create bookmarks for challenges that aren't already bookmarked
      if (newChallengeIds.length > 0) {
        await prisma.userChallengeBookmark.createMany({
          data: newChallengeIds.map((challengeId) => ({
            user_id: userId,
            challenge_id: challengeId,
            collection_id: collectionId,
          })),
          skipDuplicates: true,
        });
      }

      // Invalidate cache
      await this.invalidateUserBookmarksCache(userId);
      if (collectionId) {
        await deleteCache(
          `${UserChallengeBookmarksRepository.CACHE_PREFIX}${userId}:${collectionId}`,
        );
      }

      return {
        added: newChallengeIds.length,
        skipped: existingChallengeIds.length,
      };
    } catch (error) {
      logger.error('Error batch creating bookmarks:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Batch delete bookmarks
   * @param userId The ID of the user
   * @param challengeIds Array of challenge IDs to remove bookmarks for
   */
  async batchDeleteBookmarks(userId: string, challengeIds: string[]) {
    const startTime = Date.now();
    try {
      // Get the bookmarks to delete
      const bookmarks = await prisma.userChallengeBookmark.findMany({
        where: {
          user_id: userId,
          challenge_id: { in: challengeIds },
        },
      });

      if (bookmarks.length === 0) {
        return { deleted: 0 };
      }

      // Get collection IDs to invalidate cache
      const collectionIds = [
        ...new Set(bookmarks.map((b) => b.collection_id).filter(Boolean)),
      ];

      // Delete the bookmarks
      await prisma.userChallengeBookmark.deleteMany({
        where: {
          user_id: userId,
          challenge_id: { in: challengeIds },
        },
      });

      // Invalidate cache
      await this.invalidateUserBookmarksCache(userId);
      for (const collectionId of collectionIds) {
        if (collectionId) {
          await deleteCache(
            `${UserChallengeBookmarksRepository.CACHE_PREFIX}${userId}:${collectionId}`,
          );
        }
      }

      return { deleted: bookmarks.length };
    } catch (error) {
      logger.error('Error batch deleting bookmarks:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Update bookmark collection
   * @param bookmarkId The ID of the bookmark
   * @param userId The ID of the user (for authorization)
   * @param collectionId The ID of the collection to move the bookmark to (null to remove from collection)
   */
  async updateBookmarkCollection(
    bookmarkId: string,
    userId: string,
    collectionId: string | null,
  ) {
    const startTime = Date.now();
    try {
      // Check if bookmark exists and belongs to the user
      const bookmark = await prisma.userChallengeBookmark.findUnique({
        where: { id: bookmarkId },
      });

      if (!bookmark) {
        throw createAppError('Bookmark not found', 404);
      }

      if (bookmark.user_id !== userId) {
        throw createAppError('Unauthorized', 403);
      }

      // Check if collection exists and belongs to the user if provided
      if (collectionId) {
        const collection = await prisma.bookmarkCollection.findUnique({
          where: { id: collectionId },
        });

        if (!collection) {
          throw createAppError('Collection not found', 404);
        }

        if (collection.user_id !== userId) {
          throw createAppError('Unauthorized', 403);
        }
      }

      const oldCollectionId = bookmark.collection_id;

      // Update the bookmark
      const updatedBookmark = await prisma.userChallengeBookmark.update({
        where: { id: bookmarkId },
        data: {
          collection_id: collectionId,
          updated_at: new Date(),
        },
      });

      // Invalidate cache
      await this.invalidateUserBookmarksCache(userId);
      if (oldCollectionId) {
        await deleteCache(
          `${UserChallengeBookmarksRepository.CACHE_PREFIX}${userId}:${oldCollectionId}`,
        );
      }
      if (collectionId) {
        await deleteCache(
          `${UserChallengeBookmarksRepository.CACHE_PREFIX}${userId}:${collectionId}`,
        );
      }

      return updatedBookmark;
    } catch (error) {
      logger.error('Error updating bookmark collection:', error);
      throw error;
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Invalidate user bookmarks cache
   * @param userId The ID of the user
   */
  private async invalidateUserBookmarksCache(userId: string) {
    await deleteCache(
      `${UserChallengeBookmarksRepository.CACHE_PREFIX}${userId}:all`,
    );
  }
}
