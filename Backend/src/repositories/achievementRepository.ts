import {
  Achievement,
  AchievementCategory,
  AchievementTriggerType,
} from '@prisma/client';

import prisma from '@/lib/prisma';
import { ResourceStats } from '@/types';
import { createAppError } from '@/utils/errorHandler';

import BaseRepository from './baseRepository';

/**
 * Repository for Achievement entity
 * Extends BaseRepository with Achievement as the entity type
 */
export default class AchievementRepository extends BaseRepository<Achievement> {
  constructor() {
    super(prisma.achievement);
  }

  /**
   * Create a new achievement
   * @param data - The achievement data
   * @returns The created achievement
   */
  async createAchievement(data: {
    name: string;
    description: string;
    category: AchievementCategory;
    icon_url: string;
    points: number;
    tier?: number;
    trigger_type: AchievementTriggerType;
    trigger_value?: number;
    is_hidden?: boolean;
  }): Promise<Achievement> {
    try {
      return await prisma.achievement.create({
        data: {
          name: data.name,
          description: data.description,
          category: data.category,
          icon_url: data.icon_url,
          points: data.points,
          tier: data.tier || 1,
          trigger_type: data.trigger_type,
          trigger_value: data.trigger_value || 1,
          is_hidden: data.is_hidden || false,
        },
      });
    } catch (error) {
      if (error.code === 'P2002') {
        throw createAppError('Achievement with this name already exists', 400);
      }
      throw createAppError('Failed to create achievement', 500);
    }
  }

  /**
   * Update an achievement
   * @param id - The ID of the achievement to update
   * @param data - The updated achievement data
   * @returns The updated achievement
   */
  async updateAchievement(
    id: string,
    data: Partial<{
      name: string;
      description: string;
      category: AchievementCategory;
      icon_url: string;
      points: number;
      tier: number;
      trigger_type: AchievementTriggerType;
      trigger_value: number;
      is_hidden: boolean;
      is_active: boolean;
    }>,
  ): Promise<Achievement> {
    try {
      return await prisma.achievement.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw createAppError('Achievement not found', 404);
      }
      if (error.code === 'P2002') {
        throw createAppError('Achievement with this name already exists', 400);
      }
      throw createAppError('Failed to update achievement', 500);
    }
  }

  /**
   * Get all achievements by category
   * @param category - The achievement category
   * @returns Array of achievements
   */
  async getAchievementsByCategory(
    category: AchievementCategory,
  ): Promise<Achievement[]> {
    return prisma.achievement.findMany({
      where: {
        category,
        is_active: true,
      },
      orderBy: [{ tier: 'asc' }, { trigger_value: 'asc' }],
    });
  }

  /**
   * Get all achievements by trigger type
   * @param triggerType - The achievement trigger type
   * @returns Array of achievements
   */
  async getAchievementsByTriggerType(
    triggerType: AchievementTriggerType,
  ): Promise<Achievement[]> {
    return prisma.achievement.findMany({
      where: {
        trigger_type: triggerType,
        is_active: true,
      },
      orderBy: [{ tier: 'asc' }, { trigger_value: 'asc' }],
    });
  }

  /**
   * Get user's achievements
   * @param userId - The ID of the user
   * @returns Object containing unlocked and available achievements
   */
  async getUserAchievements(userId: string): Promise<{
    unlocked: Array<Achievement & { unlocked_at: Date }>;
    available: Achievement[];
  }> {
    // Get user's unlocked achievements
    const userAchievements = await prisma.userAchievement.findMany({
      where: { user_id: userId },
      include: { achievement: true },
    });

    // Get all active achievements
    const allAchievements = await prisma.achievement.findMany({
      where: {
        is_active: true,
        // Don't include hidden achievements that the user hasn't unlocked yet
        OR: [
          { is_hidden: false },
          {
            is_hidden: true,
            id: { in: userAchievements.map((ua) => ua.achievement_id) },
          },
        ],
      },
    });

    // Split into unlocked and available
    const unlockedAchievementIds = userAchievements.map(
      (ua) => ua.achievement_id,
    );

    const unlocked = userAchievements.map((ua) => ({
      ...ua.achievement,
      unlocked_at: ua.unlocked_at,
    }));

    const available = allAchievements.filter(
      (achievement) =>
        !unlockedAchievementIds.includes(achievement.id) &&
        !achievement.is_hidden,
    );

    return { unlocked, available };
  }

  /**
   * Get achievement statistics
   * @returns Achievement statistics
   */
  async getAchievementStats(): Promise<ResourceStats> {
    const [total, active, hidden] = await Promise.all([
      this.count(),
      this.count({ where: { is_active: true } }),
      this.count({ where: { is_hidden: true } }),
    ]);

    return { total, active, pending: 0, reported: 0 };
  }

  /**
   * Get most popular achievements
   * @param limit - Maximum number of achievements to return
   * @returns Array of achievements with unlock count
   */
  async getMostPopularAchievements(
    limit: number = 10,
  ): Promise<Array<Achievement & { unlock_count: number }>> {
    const popularAchievements = await prisma.userAchievement.groupBy({
      by: ['achievement_id'],
      _count: {
        user_id: true,
      },
      orderBy: {
        _count: {
          user_id: 'desc',
        },
      },
      take: limit,
    });

    const achievementIds = popularAchievements.map((pa) => pa.achievement_id);
    const achievements = await prisma.achievement.findMany({
      where: {
        id: { in: achievementIds },
      },
    });

    return achievements
      .map((achievement) => {
        const popularAchievement = popularAchievements.find(
          (pa) => pa.achievement_id === achievement.id,
        );
        return {
          ...achievement,
          unlock_count: popularAchievement?._count.user_id || 0,
        };
      })
      .sort((a, b) => b.unlock_count - a.unlock_count);
  }

  /**
   * Get rarest achievements
   * @param limit - Maximum number of achievements to return
   * @returns Array of achievements with unlock count
   */
  async getRarestAchievements(
    limit: number = 10,
  ): Promise<Array<Achievement & { unlock_count: number }>> {
    // Get all achievements
    const achievements = await prisma.achievement.findMany({
      where: {
        is_active: true,
        is_hidden: false,
      },
    });

    // Get unlock counts for each achievement
    const unlockCounts = await prisma.userAchievement.groupBy({
      by: ['achievement_id'],
      _count: {
        user_id: true,
      },
    });

    // Map unlock counts to achievements
    const achievementsWithCounts = achievements.map((achievement) => {
      const unlockCount = unlockCounts.find(
        (uc) => uc.achievement_id === achievement.id,
      );
      return {
        ...achievement,
        unlock_count: unlockCount?._count.user_id || 0,
      };
    });

    // Sort by unlock count (ascending) and return the limit
    return achievementsWithCounts
      .sort((a, b) => a.unlock_count - b.unlock_count)
      .slice(0, limit);
  }
}
