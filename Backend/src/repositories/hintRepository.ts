import { ChallengeHint, HintDifficulty, UserHint } from '@prisma/client';

import prisma from '@/lib/prisma';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

/**
 * Repository for ChallengeHint entity
 * Extends BaseRepository with ChallengeHint as the entity type
 */
export default class HintRepository extends BaseRepository<ChallengeHint> {
  constructor() {
    super(prisma.challengeHint);
  }

  /**
   * Create a new hint for a challenge
   * @param data - The hint data
   * @returns The created hint
   */
  async createHint(data: {
    challenge_id: string;
    content: string;
    difficulty: HintDifficulty;
    order?: number;
    point_penalty?: number;
  }): Promise<ChallengeHint> {
    try {
      // Check if the challenge exists
      const challenge = await prisma.challenge.findUnique({
        where: { id: data.challenge_id },
      });

      if (!challenge) {
        throw createAppError('Challenge not found', 404);
      }

      // Get the highest order for this challenge's hints
      const highestOrderHint = await prisma.challengeHint.findFirst({
        where: { challenge_id: data.challenge_id },
        orderBy: { order: 'desc' },
      });

      const order =
        data.order ?? (highestOrderHint ? highestOrderHint.order + 1 : 0);

      // Create the hint
      return prisma.challengeHint.create({
        data: {
          challenge_id: data.challenge_id,
          content: data.content,
          difficulty: data.difficulty,
          order,
          point_penalty:
            data.point_penalty ?? this.getDefaultPointPenalty(data.difficulty),
        },
      });
    } catch (error) {
      logger.error('Error creating hint:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to create hint', 500);
    }
  }

  /**
   * Update a hint
   * @param id - The ID of the hint to update
   * @param data - The updated hint data
   * @returns The updated hint
   */
  async updateHint(
    id: string,
    data: Partial<{
      content: string;
      difficulty: HintDifficulty;
      order: number;
      point_penalty: number;
    }>,
  ): Promise<ChallengeHint> {
    try {
      // Check if the hint exists
      const hint = await prisma.challengeHint.findUnique({
        where: { id },
      });

      if (!hint) {
        throw createAppError('Hint not found', 404);
      }

      // If difficulty is changing, update the point penalty if it wasn't explicitly provided
      let pointPenalty = data.point_penalty;
      if (
        data.difficulty &&
        data.difficulty !== hint.difficulty &&
        pointPenalty === undefined
      ) {
        pointPenalty = this.getDefaultPointPenalty(data.difficulty);
      }

      // Update the hint
      return prisma.challengeHint.update({
        where: { id },
        data: {
          content: data.content,
          difficulty: data.difficulty,
          order: data.order,
          point_penalty: pointPenalty,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      logger.error('Error updating hint:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to update hint', 500);
    }
  }

  /**
   * Delete a hint
   * @param id - The ID of the hint to delete
   * @returns The deleted hint
   */
  async deleteHint(id: string): Promise<ChallengeHint> {
    try {
      // Check if the hint exists
      const hint = await prisma.challengeHint.findUnique({
        where: { id },
      });

      if (!hint) {
        throw createAppError('Hint not found', 404);
      }

      // Delete the hint
      return prisma.challengeHint.delete({
        where: { id },
      });
    } catch (error) {
      logger.error('Error deleting hint:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to delete hint', 500);
    }
  }

  /**
   * Get hints for a challenge
   * @param challengeId - The ID of the challenge
   * @param userId - The ID of the user (optional)
   * @returns Array of hints with unlock status
   */
  async getHintsForChallenge(challengeId: string, userId?: string) {
    try {
      // Check if the challenge exists
      const challenge = await prisma.challenge.findUnique({
        where: { id: challengeId },
      });

      if (!challenge) {
        throw createAppError('Challenge not found', 404);
      }

      // Get all hints for the challenge
      const hints = await prisma.challengeHint.findMany({
        where: { challenge_id: challengeId },
        orderBy: { order: 'asc' },
      });

      // If no user ID is provided, return the hints without unlock status
      if (!userId) {
        return hints.map((hint) => ({
          ...hint,
          is_unlocked: false,
          unlocked_at: null,
        }));
      }

      // Get the user's unlocked hints for this challenge
      const userHints = await prisma.userHint.findMany({
        where: {
          user_id: userId,
          challenge_id: challengeId,
        },
      });

      // Map the unlocked hints to the hints array
      return hints.map((hint) => {
        const userHint = userHints.find((uh) => uh.hint_id === hint.id);
        return {
          ...hint,
          is_unlocked: !!userHint,
          unlocked_at: userHint?.unlocked_at || null,
        };
      });
    } catch (error) {
      logger.error('Error getting hints for challenge:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to get hints for challenge', 500);
    }
  }

  /**
   * Unlock a hint for a user
   * @param hintId - The ID of the hint to unlock
   * @param userId - The ID of the user
   * @returns The unlocked hint with user data
   */
  async unlockHint(hintId: string, userId: string) {
    try {
      // Check if the hint exists
      const hint = await prisma.challengeHint.findUnique({
        where: { id: hintId },
      });

      if (!hint) {
        throw createAppError('Hint not found', 404);
      }

      // Check if the user has already unlocked this hint
      const existingUserHint = await prisma.userHint.findUnique({
        where: {
          user_id_hint_id: {
            user_id: userId,
            hint_id: hintId,
          },
        },
      });

      if (existingUserHint) {
        // Return the already unlocked hint
        return {
          ...hint,
          is_unlocked: true,
          unlocked_at: existingUserHint.unlocked_at,
          points_deducted: existingUserHint.points_deducted,
        };
      }

      // Start a transaction to unlock the hint and deduct points
      return await prisma.$transaction(async (tx) => {
        // Create the user hint record
        const userHint = await tx.userHint.create({
          data: {
            user_id: userId,
            hint_id: hintId,
            challenge_id: hint.challenge_id,
            points_deducted: hint.point_penalty,
          },
        });

        // Deduct points from the user
        await tx.userPoints.upsert({
          where: { user_id: userId },
          update: {
            points: { decrement: hint.point_penalty },
          },
          create: {
            user_id: userId,
            points: -hint.point_penalty, // Start with negative points if this is the first action
          },
        });

        // Log the hint usage
        await tx.userActivityLog.create({
          data: {
            user_id: userId,
            action: 'HINT_UNLOCKED',
            details: {
              hint_id: hintId,
              challenge_id: hint.challenge_id,
              points_deducted: hint.point_penalty,
            },
          },
        });

        return {
          ...hint,
          is_unlocked: true,
          unlocked_at: userHint.unlocked_at,
          points_deducted: userHint.points_deducted,
        };
      });
    } catch (error) {
      logger.error('Error unlocking hint:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to unlock hint', 500);
    }
  }

  /**
   * Get hint usage statistics
   * @param challengeId - The ID of the challenge (optional)
   * @returns Hint usage statistics
   */
  async getHintUsageStats(challengeId?: string) {
    try {
      // Base query for user hints
      const baseQuery = challengeId
        ? { where: { challenge_id: challengeId } }
        : {};

      // Get total hint usage
      const totalHintUsage = await prisma.userHint.count({
        where: challengeId ? { challenge_id: challengeId } : {},
      });

      // Get most used hints
      const mostUsedHints = await prisma.userHint.groupBy({
        by: ['hint_id'],
        _count: {
          user_id: true,
        },
        orderBy: [
          {
            _count: {
              user_id: 'desc',
            },
          },
        ],
        take: 10,
        where: challengeId ? { challenge_id: challengeId } : undefined,
      });

      // Get hint details for the most used hints
      const hintIds = mostUsedHints.map((h) => h.hint_id);
      const hintDetails = await prisma.challengeHint.findMany({
        where: {
          id: { in: hintIds },
        },
        include: {
          challenge: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      // Map hint details to usage stats
      const mostUsedHintsWithDetails = mostUsedHints.map((usage) => {
        const hint = hintDetails.find((h) => h.id === usage.hint_id);
        return {
          hint,
          usage_count: usage._count.user_id,
        };
      });

      // Get hint usage by difficulty
      const usageByDifficulty = await prisma.userHint.groupBy({
        by: ['hint_id'],
        _count: {
          user_id: true,
        },
        where: challengeId ? { challenge_id: challengeId } : {},
      });

      const hintsByDifficulty = await prisma.challengeHint.groupBy({
        by: ['difficulty'],
        _count: {
          id: true,
        },
        where: challengeId ? { challenge_id: challengeId } : {},
      });

      // Calculate average usage by difficulty
      const difficultyStats = hintsByDifficulty.map((diffGroup) => {
        const hintsInDifficulty = hintDetails.filter(
          (h) => h.difficulty === diffGroup.difficulty,
        );
        const usageForDifficulty = usageByDifficulty.filter((u) =>
          hintsInDifficulty.some((h) => h.id === u.hint_id),
        );
        const totalUsage = usageForDifficulty.reduce(
          (sum, u) => sum + u._count.user_id,
          0,
        );
        const avgUsage =
          diffGroup._count.id > 0 ? totalUsage / diffGroup._count.id : 0;

        return {
          difficulty: diffGroup.difficulty,
          hint_count: diffGroup._count.id,
          total_usage: totalUsage,
          average_usage: avgUsage,
        };
      });

      return {
        total_hint_usage: totalHintUsage,
        most_used_hints: mostUsedHintsWithDetails,
        usage_by_difficulty: difficultyStats,
      };
    } catch (error) {
      logger.error('Error getting hint usage stats:', error);
      throw createAppError('Failed to get hint usage stats', 500);
    }
  }

  /**
   * Get user's hint usage
   * @param userId - The ID of the user
   * @returns User's hint usage statistics
   */
  async getUserHintUsage(userId: string) {
    try {
      // Get all hints used by the user
      const userHints = await prisma.userHint.findMany({
        where: { user_id: userId },
        include: {
          hint: {
            include: {
              challenge: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
        },
        orderBy: { unlocked_at: 'desc' },
      });

      // Calculate total points deducted
      const totalPointsDeducted = userHints.reduce(
        (sum, uh) => sum + uh.points_deducted,
        0,
      );

      // Group by challenge
      const hintsByChallenge = userHints.reduce((acc, uh) => {
        const challengeId = uh.challenge_id;
        if (!acc[challengeId]) {
          acc[challengeId] = {
            challenge: uh.hint.challenge,
            hints: [],
            total_points_deducted: 0,
          };
        }
        acc[challengeId].hints.push({
          id: uh.hint_id,
          content: uh.hint.content,
          difficulty: uh.hint.difficulty,
          points_deducted: uh.points_deducted,
          unlocked_at: uh.unlocked_at,
        });
        acc[challengeId].total_points_deducted += uh.points_deducted;
        return acc;
      }, {});

      return {
        total_hints_used: userHints.length,
        total_points_deducted: totalPointsDeducted,
        hints_by_challenge: Object.values(hintsByChallenge),
      };
    } catch (error) {
      logger.error('Error getting user hint usage:', error);
      throw createAppError('Failed to get user hint usage', 500);
    }
  }

  /**
   * Get default point penalty based on hint difficulty
   * @param difficulty - The hint difficulty
   * @returns The default point penalty
   */
  private getDefaultPointPenalty(difficulty: HintDifficulty): number {
    switch (difficulty) {
      case 'BEGINNER':
        return 5;
      case 'INTERMEDIATE':
        return 10;
      case 'ADVANCED':
        return 15;
      default:
        return 5;
    }
  }
}
