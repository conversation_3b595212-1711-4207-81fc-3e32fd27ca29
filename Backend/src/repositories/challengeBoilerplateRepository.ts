import { ChallengeBoilerplate } from '@prisma/client';

import prisma from '@/lib/prisma';

import { createAppError } from '../utils/errorHandler';
import BaseRepository from './baseRepository';

/**
 * Repository for ChallengeBoilerplate entity
 * Handles CRUD operations and specialized queries for challengeBoilerplates
 */
export interface ChallengeBoilerplateData {
  challenge_id: string;
  language: string;
  boilerplate_code: string;
}

export class ChallengeBoilerplateRepository extends BaseRepository<ChallengeBoilerplate> {
  constructor() {
    super(prisma.challengeBoilerplate);
  }

  /**
   * Get all boilerplates for a challenge
   * @param challengeId - The ID of the challenge
   * @returns An array of challenge boilerplates
   */
  async getBoilerplatesByChallenge(
    challengeId: string,
  ): Promise<ChallengeBoilerplate[]> {
    return prisma.challengeBoilerplate.findMany({
      where: { challenge_id: challengeId },
    });
  }

  /**
   * Get a specific boilerplate by challenge ID and language
   * @param challengeId - The ID of the challenge
   * @param language - The programming language
   * @returns The boilerplate or null if not found
   */
  async getBoilerplateByLanguage(
    challengeId: string,
    language: string,
  ): Promise<ChallengeBoilerplate | null> {
    return prisma.challengeBoilerplate.findFirst({
      where: {
        challenge_id: challengeId,
        language,
      },
    });
  }

  /**
   * Create a new challenge boilerplate
   * @param data - The boilerplate data
   * @returns The created boilerplate
   */
  async createBoilerplate(
    data: ChallengeBoilerplateData,
  ): Promise<ChallengeBoilerplate> {
    // Check if challenge exists
    const challenge = await prisma.challenge.findUnique({
      where: { id: data.challenge_id },
    });

    if (!challenge) {
      throw createAppError('Challenge not found', 404);
    }

    // Check if boilerplate already exists for this language
    const existingBoilerplate = await this.getBoilerplateByLanguage(
      data.challenge_id,
      data.language,
    );

    if (existingBoilerplate) {
      throw createAppError(
        `Boilerplate for language ${data.language} already exists`,
        409,
      );
    }

    return prisma.challengeBoilerplate.create({
      data,
    });
  }

  /**
   * Update an existing challenge boilerplate
   * @param challengeId - The ID of the challenge
   * @param language - The programming language
   * @param boilerplateCode - The updated boilerplate code
   * @returns The updated boilerplate
   */
  async updateBoilerplate(
    challengeId: string,
    language: string,
    boilerplateCode: string,
  ): Promise<ChallengeBoilerplate> {
    const boilerplate = await this.getBoilerplateByLanguage(
      challengeId,
      language,
    );

    if (!boilerplate) {
      throw createAppError(
        `Boilerplate for language ${language} not found`,
        404,
      );
    }

    return prisma.challengeBoilerplate.update({
      where: { id: boilerplate.id },
      data: { boilerplate_code: boilerplateCode },
    });
  }

  /**
   * Delete a challenge boilerplate
   * @param challengeId - The ID of the challenge
   * @param language - The programming language
   * @returns The deleted boilerplate
   */
  async deleteBoilerplate(
    challengeId: string,
    language: string,
  ): Promise<ChallengeBoilerplate> {
    const boilerplate = await this.getBoilerplateByLanguage(
      challengeId,
      language,
    );

    if (!boilerplate) {
      throw createAppError(
        `Boilerplate for language ${language} not found`,
        404,
      );
    }

    return prisma.challengeBoilerplate.delete({
      where: { id: boilerplate.id },
    });
  }

  /**
   * Get supported languages for a challenge
   * @param challengeId - The ID of the challenge
   * @returns An array of supported languages
   */
  async getSupportedLanguages(challengeId: string): Promise<string[]> {
    const boilerplates = await this.getBoilerplatesByChallenge(challengeId);
    return boilerplates.map((b) => b.language);
  }
}

export default new ChallengeBoilerplateRepository();
