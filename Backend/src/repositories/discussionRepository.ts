import {
  ChallengeDiscussion,
  FlagReason,
  NotificationType,
  VoteType,
} from '@prisma/client';

import prisma from '@/lib/prisma';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

/**
 * Repository for ChallengeDiscussion entity
 * Extends BaseRepository with ChallengeDiscussion as the entity type
 */
export default class DiscussionRepository extends BaseRepository<ChallengeDiscussion> {
  constructor() {
    super(prisma.challengeDiscussion);
  }

  /**
   * Create a new discussion
   * @param data - The discussion data
   * @returns The created discussion
   */
  async createDiscussion(data: {
    challenge_id: string;
    user_id: string;
    parent_id?: string;
    content: string;
    code_snippet?: string;
    code_language?: string;
  }): Promise<ChallengeDiscussion> {
    try {
      // Check if the challenge exists
      const challenge = await prisma.challenge.findUnique({
        where: { id: data.challenge_id },
      });

      if (!challenge) {
        throw createAppError('Challenge not found', 404);
      }

      // If this is a reply, check if the parent discussion exists
      if (data.parent_id) {
        const parentDiscussion = await prisma.challengeDiscussion.findUnique({
          where: { id: data.parent_id },
        });

        if (!parentDiscussion) {
          throw createAppError('Parent discussion not found', 404);
        }

        // Ensure the parent discussion is for the same challenge
        if (parentDiscussion.challenge_id !== data.challenge_id) {
          throw createAppError(
            'Parent discussion is for a different challenge',
            400,
          );
        }

        // Ensure we're not creating a nested reply (only 1 level of nesting)
        if (parentDiscussion.parent_id) {
          throw createAppError('Cannot create a reply to a reply', 400);
        }
      }

      // Create the discussion
      const discussion = await prisma.challengeDiscussion.create({
        data: {
          challenge_id: data.challenge_id,
          user_id: data.user_id,
          parent_id: data.parent_id,
          content: data.content,
          code_snippet: data.code_snippet,
          code_language: data.code_language,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
        },
      });

      // If this is a reply, create a notification for the parent discussion author
      if (data.parent_id) {
        const parentDiscussion = await prisma.challengeDiscussion.findUnique({
          where: { id: data.parent_id },
          select: { user_id: true },
        });

        if (parentDiscussion && parentDiscussion.user_id !== data.user_id) {
          await prisma.discussionNotification.create({
            data: {
              user_id: parentDiscussion.user_id,
              discussion_id: discussion.id,
              message: 'Someone replied to your discussion',
            },
          });

          // Also create a general notification
          await prisma.notification.create({
            data: {
              user_id: parentDiscussion.user_id,
              title: 'New Reply',
              message: 'Someone replied to your discussion',
              type: NotificationType.discussion,
              link: `/challenges/${data.challenge_id}/discussions/${data.parent_id}`,
            },
          });
        }
      }

      return discussion;
    } catch (error) {
      logger.error('Error creating discussion:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to create discussion', 500);
    }
  }

  /**
   * Get discussions for a challenge
   * @param challengeId - The ID of the challenge
   * @param userId - The ID of the current user (optional)
   * @param includeHidden - Whether to include hidden discussions (admin only)
   * @returns Array of discussions with replies
   */
  async getDiscussionsForChallenge(
    challengeId: string,
    userId?: string,
    includeHidden: boolean = false,
  ) {
    try {
      // Check if the challenge exists
      const challenge = await prisma.challenge.findUnique({
        where: { id: challengeId },
      });

      if (!challenge) {
        throw createAppError('Challenge not found', 404);
      }

      // Get top-level discussions (no parent_id)
      const discussions = await prisma.challengeDiscussion.findMany({
        where: {
          challenge_id: challengeId,
          parent_id: null,
          ...(includeHidden ? {} : { is_hidden: false }),
        },
        orderBy: {
          created_at: 'desc',
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
          replies: {
            where: includeHidden ? {} : { is_hidden: false },
            orderBy: {
              created_at: 'asc',
            },
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  avatar_url: true,
                },
              },
              ...(userId
                ? {
                    user_votes: {
                      where: { user_id: userId },
                      select: { vote_type: true },
                    },
                  }
                : {}),
            },
          },
          ...(userId
            ? {
                user_votes: {
                  where: { user_id: userId },
                  select: { vote_type: true },
                },
              }
            : {}),
        },
      });

      // Transform the data to include user vote information
      return discussions.map((discussion) => {
        const userVote = userId
          ? discussion.user_votes?.[0]?.vote_type || null
          : null;

        const replies = discussion.replies.map((reply) => {
          const replyUserVote = userId
            ? reply.user_votes?.[0]?.vote_type || null
            : null;

          return {
            ...reply,
            user_vote: replyUserVote,
            user_votes: undefined, // Remove the user_votes array
          };
        });

        return {
          ...discussion,
          user_vote: userVote,
          user_votes: undefined, // Remove the user_votes array
          replies,
        };
      });
    } catch (error) {
      logger.error('Error getting discussions for challenge:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to get discussions for challenge', 500);
    }
  }

  /**
   * Get a single discussion with its replies
   * @param discussionId - The ID of the discussion
   * @param userId - The ID of the current user (optional)
   * @returns The discussion with replies
   */
  async getDiscussion(discussionId: string, userId?: string) {
    try {
      const discussion = await prisma.challengeDiscussion.findUnique({
        where: { id: discussionId },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
          replies: {
            orderBy: {
              created_at: 'asc',
            },
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  avatar_url: true,
                },
              },
              ...(userId
                ? {
                    user_votes: {
                      where: { user_id: userId },
                      select: { vote_type: true },
                    },
                  }
                : {}),
            },
          },
          ...(userId
            ? {
                user_votes: {
                  where: { user_id: userId },
                  select: { vote_type: true },
                },
              }
            : {}),
        },
      });

      if (!discussion) {
        throw createAppError('Discussion not found', 404);
      }

      // Transform the data to include user vote information
      const userVote = userId
        ? discussion.user_votes?.[0]?.vote_type || null
        : null;

      const replies = discussion.replies.map((reply) => {
        const replyUserVote = userId
          ? reply.user_votes?.[0]?.vote_type || null
          : null;

        return {
          ...reply,
          user_vote: replyUserVote,
          user_votes: undefined, // Remove the user_votes array
        };
      });

      return {
        ...discussion,
        user_vote: userVote,
        user_votes: undefined, // Remove the user_votes array
        replies,
      };
    } catch (error) {
      logger.error('Error getting discussion:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to get discussion', 500);
    }
  }

  /**
   * Update a discussion
   * @param discussionId - The ID of the discussion
   * @param userId - The ID of the user making the update
   * @param data - The updated discussion data
   * @returns The updated discussion
   */
  async updateDiscussion(
    discussionId: string,
    userId: string,
    data: {
      content?: string;
      code_snippet?: string;
      code_language?: string;
    },
  ) {
    try {
      // Check if the discussion exists and belongs to the user
      const discussion = await prisma.challengeDiscussion.findUnique({
        where: { id: discussionId },
      });

      if (!discussion) {
        throw createAppError('Discussion not found', 404);
      }

      if (discussion.user_id !== userId) {
        throw createAppError('You can only update your own discussions', 403);
      }

      // Update the discussion
      return prisma.challengeDiscussion.update({
        where: { id: discussionId },
        data: {
          content: data.content,
          code_snippet: data.code_snippet,
          code_language: data.code_language,
          updated_at: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error('Error updating discussion:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to update discussion', 500);
    }
  }

  /**
   * Delete a discussion
   * @param discussionId - The ID of the discussion
   * @param userId - The ID of the user making the deletion
   * @param isAdmin - Whether the user is an admin
   * @returns The deleted discussion
   */
  async deleteDiscussion(
    discussionId: string,
    userId: string,
    isAdmin: boolean = false,
  ) {
    try {
      // Check if the discussion exists
      const discussion = await prisma.challengeDiscussion.findUnique({
        where: { id: discussionId },
      });

      if (!discussion) {
        throw createAppError('Discussion not found', 404);
      }

      // Check if the user is authorized to delete the discussion
      if (!isAdmin && discussion.user_id !== userId) {
        throw createAppError('You can only delete your own discussions', 403);
      }

      // Delete the discussion
      return prisma.challengeDiscussion.delete({
        where: { id: discussionId },
      });
    } catch (error) {
      logger.error('Error deleting discussion:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to delete discussion', 500);
    }
  }

  /**
   * Vote on a discussion
   * @param discussionId - The ID of the discussion
   * @param userId - The ID of the user voting
   * @param voteType - The type of vote (UPVOTE or DOWNVOTE)
   * @returns The updated discussion
   */
  async voteDiscussion(
    discussionId: string,
    userId: string,
    voteType: VoteType,
  ) {
    try {
      // Check if the discussion exists
      const discussion = await prisma.challengeDiscussion.findUnique({
        where: { id: discussionId },
      });

      if (!discussion) {
        throw createAppError('Discussion not found', 404);
      }

      // Check if the user has already voted on this discussion
      const existingVote = await prisma.discussionVote.findUnique({
        where: {
          discussion_id_user_id: {
            discussion_id: discussionId,
            user_id: userId,
          },
        },
      });

      // Start a transaction to update votes
      return await prisma.$transaction(async (tx) => {
        if (existingVote) {
          // If the vote type is the same, remove the vote
          if (existingVote.vote_type === voteType) {
            // Delete the vote
            await tx.discussionVote.delete({
              where: { id: existingVote.id },
            });

            // Update the discussion vote count
            if (voteType === 'UPVOTE') {
              await tx.challengeDiscussion.update({
                where: { id: discussionId },
                data: { upvotes: { decrement: 1 } },
              });
            } else {
              await tx.challengeDiscussion.update({
                where: { id: discussionId },
                data: { downvotes: { decrement: 1 } },
              });
            }
          } else {
            // If the vote type is different, update the vote
            await tx.discussionVote.update({
              where: { id: existingVote.id },
              data: { vote_type: voteType },
            });

            // Update the discussion vote count
            if (voteType === 'UPVOTE') {
              await tx.challengeDiscussion.update({
                where: { id: discussionId },
                data: {
                  upvotes: { increment: 1 },
                  downvotes: { decrement: 1 },
                },
              });
            } else {
              await tx.challengeDiscussion.update({
                where: { id: discussionId },
                data: {
                  upvotes: { decrement: 1 },
                  downvotes: { increment: 1 },
                },
              });
            }
          }
        } else {
          // Create a new vote
          await tx.discussionVote.create({
            data: {
              discussion_id: discussionId,
              user_id: userId,
              vote_type: voteType,
            },
          });

          // Update the discussion vote count
          if (voteType === 'UPVOTE') {
            await tx.challengeDiscussion.update({
              where: { id: discussionId },
              data: { upvotes: { increment: 1 } },
            });
          } else {
            await tx.challengeDiscussion.update({
              where: { id: discussionId },
              data: { downvotes: { increment: 1 } },
            });
          }
        }

        // Return the updated discussion
        return tx.challengeDiscussion.findUnique({
          where: { id: discussionId },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                avatar_url: true,
              },
            },
            user_votes: {
              where: { user_id: userId },
              select: { vote_type: true },
            },
          },
        });
      });
    } catch (error) {
      logger.error('Error voting on discussion:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to vote on discussion', 500);
    }
  }

  /**
   * Flag a discussion
   * @param discussionId - The ID of the discussion
   * @param userId - The ID of the user flagging
   * @param reason - The reason for flagging
   * @param details - Additional details about the flag
   * @returns The created flag
   */
  async flagDiscussion(
    discussionId: string,
    userId: string,
    reason: FlagReason,
    details?: string,
  ) {
    try {
      // Check if the discussion exists
      const discussion = await prisma.challengeDiscussion.findUnique({
        where: { id: discussionId },
      });

      if (!discussion) {
        throw createAppError('Discussion not found', 404);
      }

      // Check if the user has already flagged this discussion
      const existingFlag = await prisma.discussionFlag.findFirst({
        where: {
          discussion_id: discussionId,
          user_id: userId,
        },
      });

      if (existingFlag) {
        throw createAppError('You have already flagged this discussion', 400);
      }

      // Create the flag
      const flag = await prisma.discussionFlag.create({
        data: {
          discussion_id: discussionId,
          user_id: userId,
          reason,
          details,
        },
      });

      // Update the discussion to mark it as flagged
      await prisma.challengeDiscussion.update({
        where: { id: discussionId },
        data: {
          is_flagged: true,
          flag_reason: reason,
        },
      });

      return flag;
    } catch (error) {
      logger.error('Error flagging discussion:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to flag discussion', 500);
    }
  }

  /**
   * Moderate a flagged discussion
   * @param discussionId - The ID of the discussion
   * @param action - The moderation action (HIDE or APPROVE)
   * @returns The updated discussion
   */
  async moderateDiscussion(discussionId: string, action: 'HIDE' | 'APPROVE') {
    try {
      // Check if the discussion exists
      const discussion = await prisma.challengeDiscussion.findUnique({
        where: { id: discussionId },
      });

      if (!discussion) {
        throw createAppError('Discussion not found', 404);
      }

      // Update the discussion based on the action
      if (action === 'HIDE') {
        return prisma.challengeDiscussion.update({
          where: { id: discussionId },
          data: {
            is_hidden: true,
          },
        });
      } else {
        return prisma.challengeDiscussion.update({
          where: { id: discussionId },
          data: {
            is_flagged: false,
            flag_reason: null,
          },
        });
      }
    } catch (error) {
      logger.error('Error moderating discussion:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to moderate discussion', 500);
    }
  }

  /**
   * Get flagged discussions
   * @param status - The moderation status to filter by
   * @returns Array of flagged discussions
   */
  async getFlaggedDiscussions(status?: 'PENDING' | 'APPROVED' | 'REJECTED') {
    try {
      return prisma.discussionFlag.findMany({
        where: status ? { status: status } : {},
        orderBy: {
          created_at: 'desc',
        },
        include: {
          discussion: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  avatar_url: true,
                },
              },
            },
          },
          user: {
            select: {
              id: true,
              username: true,
              avatar_url: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error('Error getting flagged discussions:', error);
      throw createAppError('Failed to get flagged discussions', 500);
    }
  }

  /**
   * Get notifications for a user
   * @param userId - The ID of the user
   * @param unreadOnly - Whether to only return unread notifications
   * @returns Array of notifications
   */
  async getNotifications(userId: string, unreadOnly: boolean = false) {
    try {
      return prisma.discussionNotification.findMany({
        where: {
          user_id: userId,
          ...(unreadOnly ? { is_read: false } : {}),
        },
        orderBy: {
          created_at: 'desc',
        },
        include: {
          discussion: {
            select: {
              id: true,
              challenge_id: true,
              content: true,
              parent_id: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error('Error getting notifications:', error);
      throw createAppError('Failed to get notifications', 500);
    }
  }

  /**
   * Mark notifications as read
   * @param userId - The ID of the user
   * @param notificationIds - Array of notification IDs to mark as read (if empty, mark all as read)
   */
  async markNotificationsAsRead(
    userId: string,
    notificationIds: string[] = [],
  ) {
    try {
      if (notificationIds.length === 0) {
        // Mark all notifications as read
        await prisma.discussionNotification.updateMany({
          where: { user_id: userId },
          data: { is_read: true },
        });
      } else {
        // Mark specific notifications as read
        await prisma.discussionNotification.updateMany({
          where: {
            user_id: userId,
            id: { in: notificationIds },
          },
          data: { is_read: true },
        });
      }
    } catch (error) {
      logger.error('Error marking notifications as read:', error);
      throw createAppError('Failed to mark notifications as read', 500);
    }
  }

  /**
   * Get discussion statistics
   * @returns Discussion statistics
   */
  async getDiscussionStats() {
    try {
      const [totalDiscussions, totalReplies, flaggedDiscussions] =
        await Promise.all([
          prisma.challengeDiscussion.count({
            where: { parent_id: null },
          }),
          prisma.challengeDiscussion.count({
            where: { parent_id: { not: null } },
          }),
          prisma.challengeDiscussion.count({
            where: { is_flagged: true },
          }),
        ]);

      // Get most active discussions
      const mostActiveDiscussions = await prisma.challengeDiscussion.findMany({
        where: { parent_id: null },
        orderBy: {
          replies: { _count: 'desc' },
        },
        take: 5,
        include: {
          _count: {
            select: { replies: true },
          },
          challenge: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      // Get most upvoted discussions
      const mostUpvotedDiscussions = await prisma.challengeDiscussion.findMany({
        orderBy: {
          upvotes: 'desc',
        },
        take: 5,
        include: {
          challenge: {
            select: {
              id: true,
              title: true,
            },
          },
        },
      });

      return {
        totalDiscussions,
        totalReplies,
        flaggedDiscussions,
        mostActiveDiscussions,
        mostUpvotedDiscussions,
      };
    } catch (error) {
      logger.error('Error getting discussion stats:', error);
      throw createAppError('Failed to get discussion stats', 500);
    }
  }
}
