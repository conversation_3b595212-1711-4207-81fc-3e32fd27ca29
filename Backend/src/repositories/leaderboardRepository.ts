import { LeaderboardEntry, Prisma } from '@prisma/client';

import prisma from '@/lib/prisma';
import { deleteCache, getOrSetCache } from '@/services/cacheService';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

export interface LeaderboardOptions {
  subject_id?: string;
  challenge_id?: string;
  language?: string;
  time_range?: 'daily' | 'weekly' | 'monthly' | 'all';
  limit?: number;
  page?: number;
}

/**
 * Repository for LeaderboardEntry entity
 * Extends BaseRepository with LeaderboardEntry as the entity type
 */
export default class LeaderboardRepository extends BaseRepository<LeaderboardEntry> {
  private static readonly CACHE_TTL = 3600; // 1 hour
  private static readonly LEADERBOARD_CACHE_PREFIX = 'leaderboard:';

  constructor() {
    super(prisma.leaderboardEntry);
  }

  /**
   * Get leaderboard with various filtering options and caching
   */
  async getLeaderboard(options: LeaderboardOptions) {
    const startTime = Date.now();
    try {
      const {
        subject_id,
        challenge_id,
        language,
        time_range = 'all',
        limit = 10,
        page = 1,
      } = options;

      // Create a cache key based on the options
      const cacheKey = `${LeaderboardRepository.LEADERBOARD_CACHE_PREFIX}${subject_id || ''}:${challenge_id || ''}:${language || ''}:${time_range}:${limit}:${page}`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          const time_filter = this.getTimeFilter(time_range);
          const skip = (page - 1) * limit;

          // Base where clause
          let where: Prisma.LeaderboardEntryWhereInput = {
            created_at: time_filter,
          };

          // Add subject filter if provided
          if (subject_id) {
            where.subject_id = subject_id;
          }

          // For challenge-specific leaderboards
          if (challenge_id) {
            // Get submissions for this challenge
            const submissions = await prisma.challengeSubmission.findMany({
              where: {
                challenge_id,
                status: 'accepted',
                ...(language && { language }),
              },
              orderBy: [{ runtime_ms: 'asc' }, { memory_used_kb: 'asc' }],
              include: {
                user: {
                  select: {
                    id: true,
                    username: true,
                    avatar_url: true,
                  },
                },
              },
              skip,
              take: limit,
            });

            // Count total for pagination
            const total = await prisma.challengeSubmission.count({
              where: {
                challenge_id,
                status: 'accepted',
                ...(language && { language }),
              },
            });

            // Format the submissions as leaderboard entries
            const entries = submissions.map((submission, index) => ({
              id: submission.id,
              user_id: submission.user_id,
              username: submission.user.username,
              avatar_url: submission.user.avatar_url,
              score: submission.score,
              runtime_ms: submission.runtime_ms,
              memory_used_kb: submission.memory_used_kb,
              language: submission.language,
              submitted_at: submission.created_at,
              rank: skip + index + 1,
            }));

            return {
              data: entries,
              meta: {
                total,
                page,
                limit,
                pages: Math.ceil(total / limit),
              },
            };
          }

          // For general leaderboards
          const [total, entries] = await Promise.all([
            this.count({ where }),
            this.findMany({
              where,
              orderBy: [{ score: 'desc' }, { time_taken: 'asc' }],
              skip,
              take: limit,
              include: {
                user: {
                  select: {
                    id: true,
                    username: true,
                    avatar_url: true,
                  },
                },
              },
            }),
          ]);

          // Format the entries with ranks
          const formattedEntries = entries.map((entry, index) => ({
            ...entry,
            rank: skip + index + 1,
          }));

          return {
            data: formattedEntries,
            meta: {
              total,
              page,
              limit,
              pages: Math.ceil(total / limit),
            },
          };
        },
        { ttl: LeaderboardRepository.CACHE_TTL },
      );
    } catch (error) {
      logger.error('Error fetching leaderboard:', error);
      throw createAppError('Failed to fetch leaderboard', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Get language-specific leaderboard for a challenge
   */
  async getLanguageLeaderboard(
    challenge_id: string,
    language: string,
    limit: number = 10,
    page: number = 1,
  ) {
    return this.getLeaderboard({
      challenge_id,
      language,
      limit,
      page,
    });
  }

  /**
   * Get time-based leaderboard (daily, weekly, monthly, all-time)
   */
  async getTimeBasedLeaderboard(
    time_range: 'daily' | 'weekly' | 'monthly' | 'all',
    limit: number = 10,
    page: number = 1,
  ) {
    return this.getLeaderboard({
      time_range,
      limit,
      page,
    });
  }

  /**
   * Get weekly leaderboard with caching
   */
  async getWeeklyLeaderboard(limit: number = 10) {
    const startTime = Date.now();
    try {
      const cacheKey = `${LeaderboardRepository.LEADERBOARD_CACHE_PREFIX}weekly:${limit}`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          // Get the current week's leaderboard
          const leaderboard = await prisma.user.findMany({
            take: limit,
            select: {
              id: true,
              username: true,
              avatar_url: true,
              college: true, // Use the existing college field until migration is run
              user_points: {
                select: {
                  points: true,
                },
              },
            },
            where: {
              user_points: {
                isNot: null,
              },
              deleted_at: null,
            },
            orderBy: {
              user_points: {
                points: 'desc',
              },
            },
          });

          // Format the data for the frontend
          return leaderboard.map((user, index) => ({
            id: user.id,
            name: user.username,
            college: user.college || 'Unknown',
            college_id: null, // Will be populated after migration
            college_logo: null, // Will be populated after migration
            college_location: null, // Will be populated after migration
            points: user.user_points?.points || 0,
            rank: index + 1,
            avatar_url: user.avatar_url || `/avatars/default-${index % 5}.png`,
          }));
        },
        { ttl: 3600 }, // Cache for 1 hour
      );
    } catch (error) {
      logger.error('Error fetching weekly leaderboard:', error);
      throw createAppError('Failed to fetch weekly leaderboard', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Get current week stats with caching
   */
  async getCurrentWeekStats() {
    const startTime = Date.now();
    try {
      const cacheKey = `${LeaderboardRepository.LEADERBOARD_CACHE_PREFIX}weekly:stats`;

      return await getOrSetCache(
        cacheKey,
        async () => {
          const totalParticipants = await prisma.userPoints.count({
            where: {
              points: {
                gt: 0,
              },
            },
          });

          const totalPoints = await prisma.userPoints.aggregate({
            _sum: {
              points: true,
            },
          });

          return {
            totalParticipants,
            totalPoints: totalPoints._sum.points || 0,
          };
        },
        { ttl: 3600 }, // Cache for 1 hour
      );
    } catch (error) {
      logger.error('Error fetching weekly stats:', error);
      throw createAppError('Failed to fetch weekly stats', 500);
    } finally {
      const duration = Date.now() - startTime;
      logger.debug(`Operation took ${duration}ms`);
    }
  }

  /**
   * Invalidate leaderboard cache when new submissions are made
   */
  async invalidateLeaderboardCache(challenge_id?: string, subject_id?: string) {
    try {
      if (challenge_id) {
        await deleteCache(
          `${LeaderboardRepository.LEADERBOARD_CACHE_PREFIX}*:${challenge_id}:*`,
        );
      } else if (subject_id) {
        await deleteCache(
          `${LeaderboardRepository.LEADERBOARD_CACHE_PREFIX}${subject_id}:*`,
        );
      } else {
        await deleteCache(`${LeaderboardRepository.LEADERBOARD_CACHE_PREFIX}*`);
      }
    } catch (error) {
      logger.error('Error invalidating leaderboard cache:', error);
    }
  }

  /**
   * Get time filter for leaderboard queries
   */
  private getTimeFilter(timeRange: string) {
    const now = new Date();
    const filters: Record<string, { gte: Date }> = {
      daily: { gte: new Date(now.setDate(now.getDate() - 1)) },
      weekly: { gte: new Date(now.setDate(now.getDate() - 7)) },
      monthly: { gte: new Date(now.setMonth(now.getMonth() - 1)) },
      all: { gte: new Date(0) },
    };

    return filters[timeRange] || filters.all;
  }

  /**
   * Detect and flag potentially cheating solutions
   */
  async detectCheating(submission_id: string) {
    try {
      const submission = await prisma.challengeSubmission.findUnique({
        where: { id: submission_id },
        include: {
          challenge: true,
          user: {
            select: {
              id: true,
              username: true,
            },
          },
        },
      });

      if (!submission) {
        throw createAppError('Submission not found', 404);
      }

      // Get average metrics for this challenge and language
      const avgMetrics = await prisma.languageMetrics.findFirst({
        where: {
          challenge_id: submission.challenge_id,
          language: submission.language,
        },
      });

      if (!avgMetrics) {
        // Not enough data to detect cheating
        return { isSuspicious: false, reasons: [] };
      }

      const reasons = [];

      // Check for suspiciously fast runtime (more than 3x faster than average)
      if (
        submission.runtime_ms &&
        avgMetrics.min_runtime_ms * 3 > submission.runtime_ms
      ) {
        reasons.push('Suspiciously fast runtime');
      }

      // Check for suspiciously low memory usage (more than 3x less than average)
      if (
        submission.memory_used_kb &&
        avgMetrics.min_memory_kb * 3 > submission.memory_used_kb
      ) {
        reasons.push('Suspiciously low memory usage');
      }

      // Check for identical code with another user's submission
      const similarSubmissions = await prisma.challengeSubmission.findMany({
        where: {
          challenge_id: submission.challenge_id,
          language: submission.language,
          code: submission.code,
          user_id: { not: submission.user_id },
        },
        take: 1,
      });

      if (similarSubmissions.length > 0) {
        reasons.push("Code is identical to another user's submission");
      }

      // If any reasons were found, flag the submission
      if (reasons.length > 0) {
        // Log the suspicious submission
        logger.warn('Suspicious submission detected', {
          submission_id,
          user_id: submission.user_id,
          challenge_id: submission.challenge_id,
          reasons,
        });

        // Flag the submission in the database
        await prisma.challengeSubmission.update({
          where: { id: submission_id },
          data: {
            // Add a flag field to the submission model if needed
            // is_flagged: true,
            // flag_reason: reasons.join(', '),
          },
        });

        return { isSuspicious: true, reasons };
      }

      return { isSuspicious: false, reasons: [] };
    } catch (error) {
      logger.error('Error detecting cheating:', error);
      throw createAppError('Failed to detect cheating', 500);
    }
  }
}
