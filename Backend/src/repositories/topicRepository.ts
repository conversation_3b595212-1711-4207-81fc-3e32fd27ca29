import { Topic } from '@prisma/client';

import prisma from '@/lib/prisma';

import BaseRepository from './baseRepository';

/**
 * Repository for Topic entity
 * Extends BaseRepository with Topic as the entity type
 */
export default class TopicRepository extends BaseRepository<Topic> {
  constructor() {
    super(prisma.topic);
  }

  async getTopicsBySubjectId(subject_id: string) {
    return await this.findMany({
      where: {
        subjects: {
          some: {
            subject_id,
          },
        },
      },
      select: {
        id: true,
        order: true,
        title: true,
        description: true,
        created_at: true,
        subjects: {
          select: {
            subject_id: true,
            subject: true,
          },
        },
      },
    });
  }

  async getTopicsWithoutSubject(): Promise<Topic[]> {
    return await this.findMany({
      where: {
        subjects: {
          none: {},
        },
      },
    });
  }
}
