import { Notification, NotificationType } from '@prisma/client';

/**
 * Repository for Notification entity
 * Handles CRUD operations and specialized queries for notifications
 */
import prisma from '@/lib/prisma';
import socketService from '@/services/socket';
import logger from '@/utils/logger';

import BaseRepository from './baseRepository';

interface ICreateNotificationParams {
  userId: string;
  type: NotificationType;
  message: string;
  data?: Record<string, any>;
  isRead?: boolean;
}

export default class NotificationRepository extends BaseRepository<Notification> {
  constructor() {
    super(prisma.notification);
  }

  /**
   * Create a new notification for a user
   * @param params Notification parameters
   * @returns The created notification
   */
  async createNotification(
    params: ICreateNotificationParams,
  ): Promise<Notification> {
    try {
      const { userId, type, message, data, isRead = false } = params;

      const notification = await prisma.notification.create({
        data: {
          user_id: userId,
          type,
          title: message.substring(0, 50), // Using first 50 chars of message as title
          message,
          data: data ? JSON.stringify(data) : null,
          is_read: isRead,
        },
      });

      // Emit a socket event to notify the user in real-time
      socketService.emitToUser(userId, 'notification:new', notification);

      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Get all notifications for a user
   * @param userId The user ID
   * @param limit Maximum number of notifications to return
   * @param offset Pagination offset
   * @param includeRead Whether to include read notifications
   * @returns Array of notifications
   */
  async getUserNotifications(
    userId: string,
    limit = 10,
    offset = 0,
    includeRead = true,
  ): Promise<Notification[]> {
    try {
      return await prisma.notification.findMany({
        where: {
          user_id: userId,
          ...(includeRead ? {} : { is_read: false }),
        },
        orderBy: {
          created_at: 'desc',
        },
        take: limit,
        skip: offset,
      });
    } catch (error) {
      logger.error('Error getting user notifications:', error);
      throw error;
    }
  }

  async getNotifications(user_id: string, include_read = false) {
    return await this.findMany({
      where: {
        user_id: user_id,
        is_read: include_read ? undefined : false,
      },
      orderBy: {
        created_at: 'desc',
      },
    });
  }

  /**
   * Mark a notification as read
   * @param notificationId The notification ID
   * @param userId The user ID (for verification)
   * @returns The updated notification
   */
  async markNotificationAsRead(
    notificationId: string,
    userId: string,
  ): Promise<Notification> {
    try {
      const notification = await prisma.notification.findFirst({
        where: {
          id: notificationId,
          user_id: userId,
        },
      });

      if (!notification) {
        throw new Error('Notification not found or does not belong to user');
      }

      return await prisma.notification.update({
        where: { id: notificationId },
        data: { is_read: true },
      });
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async markAsRead(id: string): Promise<Notification> {
    return await this.update({
      where: { id },
      data: {
        is_read: true,
      },
    });
  }

  async markAllAsRead(user_id: string): Promise<void> {
    await this.updateMany({
      where: {
        user_id,
        is_read: false,
      },
      data: {
        is_read: true,
      },
    });
  }

  /**
   * Delete a notification
   * @param notificationId The notification ID
   * @param userId The user ID (for verification)
   * @returns The deleted notification
   */
  async deleteNotification(
    notificationId: string,
    userId: string,
  ): Promise<Notification> {
    try {
      const notification = await prisma.notification.findFirst({
        where: {
          id: notificationId,
          user_id: userId,
        },
      });

      if (!notification) {
        throw new Error('Notification not found or does not belong to user');
      }

      return await prisma.notification.delete({
        where: { id: notificationId },
      });
    } catch (error) {
      logger.error('Error deleting notification:', error);
      throw error;
    }
  }

  /**
   * Delete all notifications for a user
   * @param userId The user ID
   * @returns The number of notifications deleted
   */
  async deleteAllNotifications(userId: string): Promise<number> {
    try {
      const result = await prisma.notification.deleteMany({
        where: {
          user_id: userId,
        },
      });

      return result.count;
    } catch (error) {
      logger.error('Error deleting all notifications:', error);
      throw error;
    }
  }
}
