import * as http from 'http';
import fs from 'fs';
import path from 'path';
import { v2 as cloudinary } from 'cloudinary';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import express, { Application } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import swaggerUi from 'swagger-ui-express';
import 'module-alias/register';
import 'tsconfig-paths/register';

import {
  CLOUDINARY_API_KEY,
  CLOUDINARY_API_SECRET,
  CLOUDINARY_CLOUD_NAME,
  CORS_ORIGIN,
  PORT,
} from './config';
import { RATE_LIMITS } from './config/rateLimitConstants';
import prisma from './lib/prisma';
import { errorHandler } from './utils/errorHandler';
import { AppRoutes } from './routes/routes';
import logger from './utils/logger';
import socketService from './services/socket';
import { requestContextMiddleware } from './utils/requestContext';
import { metricsMiddleware, metricsRegister } from './middlewares/metricsMiddleware';
import * as Sentry from '@sentry/node';

declare const require: any;

type MaybeServer = ReturnType<Application['listen']>;

export class App {
  public readonly app: Application;

  constructor() {
    this.app = express();
    this.initializeCloudinary();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeSwagger();
    this.initializeErrorHandling();
  }

  private initializeCloudinary(): void {
    cloudinary.config({
      cloud_name: CLOUDINARY_CLOUD_NAME,
      api_key: CLOUDINARY_API_KEY,
      api_secret: CLOUDINARY_API_SECRET,
    });
  }

  private initializeMiddlewares(): void {
    this.app.use(compression()); // Add compression
    this.app.use(express.json());
    // Correlation IDs
    this.app.use(requestContextMiddleware);
    // Prometheus metrics timing
    this.app.use(metricsMiddleware);
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(cookieParser());
    this.app.use(
      cors({
        origin: CORS_ORIGIN,
        credentials: true,
      }),
    );
    this.app.use(
      helmet({
        contentSecurityPolicy:
          process.env.NODE_ENV === 'production' ? undefined : false,
        crossOriginEmbedderPolicy:
          process.env.NODE_ENV === 'production' ? undefined : false,
      }),
    );

    const limiter = rateLimit({
      windowMs: RATE_LIMITS.GLOBAL.windowMs,
      max: RATE_LIMITS.GLOBAL.max,
      standardHeaders: true,
      legacyHeaders: false,
      message: 'Too many requests from this IP, please try again later.',
    });
    this.app.use(limiter);
  }

  private initializeRoutes(): void {
    const appRoutes = new AppRoutes();
    this.app.use('/api/v1', appRoutes.getRouter());
  }

  private initializeSwagger(): void {
    try {
      const swaggerPath = path.join(process.cwd(), 'swagger', 'index.json');
      if (fs.existsSync(swaggerPath)) {
        const swaggerData = fs.readFileSync(swaggerPath, 'utf8');
        const swaggerJSON = JSON.parse(swaggerData);
        this.app.use(
          '/api-docs',
          swaggerUi.serve,
          swaggerUi.setup(swaggerJSON, { customSiteTitle: 'API Documentation' }),
        );
        logger.info('Swagger docs mounted at /api-docs');
      } else {
        logger.warn('Swagger not enabled: swagger/index.json not found');
      }
    } catch (err) {
      logger.error('Failed to initialize Swagger:', err);
    }
  }

  private initializeErrorHandling(): void {
    // Add default 404 handler
    this.app.use((req, res, next) => {
      res.status(404).json({ message: 'Route not found' });
    });

    // Add existing error handler
    this.app.use(errorHandler);

    // Expose Prometheus metrics endpoint (unauthenticated; secure via infra)
    this.app.get('/metrics', async (_req, res) => {
      res.set('Content-Type', metricsRegister.contentType);
      res.end(await metricsRegister.metrics());
    });
  }

  private setupGracefulShutdown(server: MaybeServer): void {
    const shutdown = () => {
      logger.info(
        'Shutdown signal received. Closing server and database connections.',
      );
      server.close(async () => {
        logger.info('HTTP server closed.');
        await prisma.$disconnect();
        process.exit(0);
      });
    };

    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
  }

  public async start(): Promise<void> {
    try {
      // Validate required Supabase environment variables at startup
      const missingEnv: string[] = [];
      if (!process.env.SUPABASE_URL) missingEnv.push('SUPABASE_URL');
      if (!process.env.SUPABASE_ANON_KEY) missingEnv.push('SUPABASE_ANON_KEY');
      if (missingEnv.length) {
        logger.error(
          `Missing required environment variables: ${missingEnv.join(', ')}`,
        );
        process.exit(1);
      }
      await prisma.$connect();
      logger.info('Connected to PostgreSQL database');

      // Initialize Sentry if configured
      if (process.env.SENTRY_DSN) {
        Sentry.init({ dsn: process.env.SENTRY_DSN, environment: process.env.NODE_ENV });
        logger.info('Sentry initialized');
      }

      const server = http.createServer(this.app);
      // Initialize WebSocket server
      socketService.initialize(server);

      server.listen(PORT, () => {
        logger.info(`Server running on port ${PORT}`);
      });

      this.setupGracefulShutdown(server);
    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }
}

// Instantiate and conditionally start server
const appInstance = new App();

if (require.main === module) {
  appInstance.start();
}

// Export the Express application for serverless (Vercel) or testing
export default appInstance.app;
