import {
  NextFunction,
  Request,
  RequestHandler,
  Response,
  Router,
} from 'express';

import ChallengeDiscussionController from '../controllers/challengeDiscussionController';
import { BaseRouter } from './BaseRouter';

export class ChallengeDiscussionRoutes extends BaseRouter {
  private readonly discussionController: ChallengeDiscussionController;

  constructor() {
    super();
    this.discussionController = new ChallengeDiscussionController();
    // We need to recreate the router with mergeParams option
    // to access challengeId from parent router
    Object.defineProperty(this, 'router', {
      value: Router({ mergeParams: true }),
      writable: false,
    });
  }

  protected initializeRoutes(): void {
    // Get all discussions for a challenge
    this.router.get(
      '/',
      this.bindRoute(this.discussionController.getDiscussions),
    );

    // Get a specific discussion
    this.router.get(
      '/:id',
      this.bindRoute(this.discussionController.getDiscussion),
    );

    // Create a new discussion
    this.router.post(
      '/',
      this.bindRoute(this.discussionController.createDiscussion),
    );

    // Update a discussion
    this.router.put(
      '/:id',
      this.bindRoute(this.discussionController.updateDiscussion),
    );

    // Delete a discussion
    this.router.delete(
      '/:id',
      this.bindRoute(this.discussionController.deleteDiscussion),
    );

    // Upvote a discussion
    this.router.post(
      '/:id/upvote',
      this.bindRoute(this.discussionController.upvoteDiscussion),
    );

    // Downvote a discussion
    this.router.post(
      '/:id/downvote',
      this.bindRoute(this.discussionController.downvoteDiscussion),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.discussionController, req, res, next);
    };
  }
}

export default new ChallengeDiscussionRoutes().getRouter();
