import UserProgressController from '../controllers/userProgressController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  activityTimelineValidation,
  trackActivityValidation,
  updateProgressValidation,
} from '../validations/progressValidation';
import { BaseRouter } from './BaseRouter';

export class ProgressRoutes extends BaseRouter {
  private readonly progressController: UserProgressController;

  constructor() {
    super();
    this.progressController = new UserProgressController();
    this.initializeRoutes();
  }

  protected initializeRoutes(): void {
    // All routes require authentication
    this.router.use(authMiddleware);

    // Basic progress routes
    this.router.get('/', this.progressController.getProgress);
    this.router.post(
      '/update',
      validateRequest(updateProgressValidation),
      this.progressController.updateProgress,
    );

    // Streak and activity tracking
    this.router.get('/streak', this.progressController.getStreak);
    this.router.post(
      '/activity',
      validateRequest(trackActivityValidation),
      this.progressController.trackActivity,
    );

    // Advanced analytics
    this.router.get('/stats', this.progressController.getProgressStats);
    this.router.get(
      '/activity-timeline',
      validateRequest(activityTimelineValidation, 'query'),
      this.progressController.getActivityTimeline,
    );

    // Learning path
    this.router.get('/learning-path', this.progressController.getLearningPath);
  }
}

export default new ProgressRoutes().getRouter();
