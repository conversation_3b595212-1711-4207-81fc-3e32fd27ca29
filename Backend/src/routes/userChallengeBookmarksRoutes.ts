import { NextFunction, Request, RequestHandler, Response } from 'express';

import UserChallengeBookmarksController from '../controllers/userChallengeBookmarksController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { BaseRouter } from './BaseRouter';

export class UserChallengeBookmarksRoutes extends BaseRouter {
  private readonly bookmarksController: UserChallengeBookmarksController;

  constructor() {
    super();
    this.bookmarksController = new UserChallengeBookmarksController();
    this.initializeRoutes();
  }

  protected initializeRoutes(): void {
    // All routes require authentication
    this.router.use(authMiddleware);

    // Get all bookmarked challenges for the authenticated user
    this.router.get('/', this.bookmarksController.getBookmarkedChallenges);

    // Toggle bookmark for a challenge
    this.router.post(
      '/toggle/:challengeId',
      this.bookmarksController.toggleBookmark,
    );

    // Bookmark a challenge
    this.router.post(
      '/:challengeId',
      this.bookmarksController.bookmarkChallenge,
    );

    // Remove a bookmark
    this.router.delete(
      '/:challengeId',
      this.bookmarksController.removeBookmark,
    );

    // Batch operations
    this.router.post(
      '/batch/create',
      this.bookmarksController.batchCreateBookmarks,
    );
    this.router.post(
      '/batch/delete',
      this.bookmarksController.batchDeleteBookmarks,
    );

    // Update bookmark collection
    this.router.patch(
      '/:bookmarkId/collection',
      this.bookmarksController.updateBookmarkCollection,
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.bookmarksController, req, res, next);
    };
  }
}

export default new UserChallengeBookmarksRoutes().getRouter();
