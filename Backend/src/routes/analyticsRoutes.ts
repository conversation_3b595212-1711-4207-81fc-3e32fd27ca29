import { Router } from 'express';
import { query } from 'express-validator';
import <PERSON><PERSON> from 'joi';

import { RoleType } from '@prisma/client';

import AnalyticsController from '../controllers/analyticsController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';

export class AnalyticsRoutes {
  private readonly router: Router;
  private readonly analyticsController: AnalyticsController;

  constructor() {
    this.router = Router();
    this.analyticsController = new AnalyticsController();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // Apply auth middleware to all routes
    this.router.use(authMiddleware);

    // User analytics routes
    this.router.get(
      '/user/:userId',
      // authorizeRoles('admin', 'instructor'),
      this.analyticsController.getUserAnalytics,
    );

    this.router.get(
      '/user/me',
      this.analyticsController.getCurrentUserAnalytics,
    );

    // Platform analytics routes
    this.router.get(
      '/platform',
      authorizeRoles(RoleType.ADMIN),
      [
        query('startDate').optional().isISO8601(),
        query('endDate').optional().isISO8601(),
        validateRequest(
          Joi.object({
            startDate: Joi.date().iso(),
            endDate: Joi.date().iso().min(Joi.ref('startDate')),
          }),
          'query',
        ),
      ],
      this.analyticsController.getPlatformAnalytics,
    );

    // Report generation routes
    this.router.get(
      '/report/:reportType',
      authorizeRoles(RoleType.ADMIN),
      [
        query('startDate').optional().isISO8601(),
        query('endDate').optional().isISO8601(),
        query('userId').optional().isString(),
        query('type').optional().isString(),
        query('status').optional().isString(),
        validateRequest(
          Joi.object({
            startDate: Joi.date().iso(),
            endDate: Joi.date().iso().min(Joi.ref('startDate')),
            userId: Joi.string().optional(),
            type: Joi.string().optional(),
            status: Joi.string().optional(),
          }),
          'query',
        ),
      ],
      this.analyticsController.generateReport,
    );

    // Custom reports management routes
    this.router.get(
      '/reports',
      authorizeRoles(RoleType.ADMIN),
      this.analyticsController.getReports,
    );

    this.router.post(
      '/reports',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(
        Joi.object({
          name: Joi.string().required(),
          description: Joi.string().optional().allow(''),
          metrics: Joi.array().items(Joi.string()).required(),
          dimensions: Joi.array().items(Joi.string()).required(),
          visualizations: Joi.array()
            .items(
              Joi.object({
                type: Joi.string()
                  .valid('bar', 'line', 'pie', 'table')
                  .required(),
                title: Joi.string().required(),
                metrics: Joi.array().items(Joi.string()).required(),
                dimensions: Joi.array().items(Joi.string()).optional(),
              }),
            )
            .optional(),
          scheduleEnabled: Joi.boolean().optional(),
          scheduleFrequency: Joi.string()
            .valid('daily', 'weekly', 'monthly')
            .optional(),
          scheduleRecipients: Joi.array()
            .items(Joi.string().email())
            .optional(),
          scheduleExportFormat: Joi.string()
            .valid('pdf', 'csv', 'excel')
            .optional(),
        }),
        'body',
      ),
      this.analyticsController.createReport,
    );

    this.router.put(
      '/reports/:id',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(
        Joi.object({
          name: Joi.string().optional(),
          description: Joi.string().optional().allow(''),
          metrics: Joi.array().items(Joi.string()).optional(),
          dimensions: Joi.array().items(Joi.string()).optional(),
          visualizations: Joi.array()
            .items(
              Joi.object({
                type: Joi.string()
                  .valid('bar', 'line', 'pie', 'table')
                  .required(),
                title: Joi.string().required(),
                metrics: Joi.array().items(Joi.string()).required(),
                dimensions: Joi.array().items(Joi.string()).optional(),
              }),
            )
            .optional(),
          scheduleEnabled: Joi.boolean().optional(),
          scheduleFrequency: Joi.string()
            .valid('daily', 'weekly', 'monthly')
            .optional(),
          scheduleRecipients: Joi.array()
            .items(Joi.string().email())
            .optional(),
          scheduleExportFormat: Joi.string()
            .valid('pdf', 'csv', 'excel')
            .optional(),
        }),
        'body',
      ),
      this.analyticsController.updateReport,
    );

    this.router.delete(
      '/reports/:id',
      authorizeRoles(RoleType.ADMIN),
      this.analyticsController.deleteReport,
    );

    // Generate report in different formats
    this.router.get(
      '/reports/:id/generate/:format',
      authorizeRoles(RoleType.ADMIN),
      this.analyticsController.generateReportById,
    );

    // Get available metrics and dimensions
    this.router.get(
      '/metrics',
      authorizeRoles(RoleType.ADMIN),
      this.analyticsController.getMetrics,
    );

    this.router.get(
      '/dimensions',
      authorizeRoles(RoleType.ADMIN),
      this.analyticsController.getDimensions,
    );

    // Data retention settings
    this.router.put(
      '/settings/retention',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(
        Joi.object({
          period: Joi.string()
            .valid('30days', '90days', '180days', '1year', 'forever')
            .required(),
        }),
        'body',
      ),
      this.analyticsController.updateRetentionPeriod,
    );
  }

  public getRouter(): Router {
    return this.router;
  }
}
