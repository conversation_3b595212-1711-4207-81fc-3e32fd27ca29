import { NextFunction, Request, RequestHandler, Response } from 'express';

import SharedSolutionController from '../controllers/sharedSolutionController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  createSharedSolutionValidation,
  updateSharedSolutionValidation,
} from '../validations/sharedSolutionValidation';
import { BaseRouter } from './BaseRouter';

export class SharedSolutionRoutes extends BaseRouter {
  private readonly sharedSolutionController: SharedSolutionController;

  constructor() {
    super();
    this.sharedSolutionController = new SharedSolutionController();
  }

  protected initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.get(
      '/token/:token',
      this.bindRoute(this.sharedSolutionController.getSharedSolution),
    );

    this.router.get(
      '/token/:token/metadata',
      this.bindRoute(this.sharedSolutionController.getSharedSolutionMetadata),
    );

    // Protected routes (authentication required)
    this.router.use(authMiddleware);

    this.router.post(
      '/',
      validateRequest(createSharedSolutionValidation),
      this.bindRoute(this.sharedSolutionController.createSharedSolution),
    );

    this.router.get(
      '/:id/statistics',
      this.bindRoute(this.sharedSolutionController.getViewStatistics),
    );

    this.router.patch(
      '/:id',
      validateRequest(updateSharedSolutionValidation),
      this.bindRoute(this.sharedSolutionController.updateSharedSolution),
    );

    this.router.delete(
      '/:id',
      this.bindRoute(this.sharedSolutionController.deleteSharedSolution),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.sharedSolutionController, req, res, next);
    };
  }
}

export default new SharedSolutionRoutes().getRouter();
