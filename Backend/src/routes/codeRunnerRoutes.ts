import { RATE_LIMITS } from '../config/rateLimitConstants';
import CodeRunnerController from '../controllers/codeRunnerController';
import { rateLimiterMiddleware } from '../middlewares/rateLimiterMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import { executeCodeValidation } from '../validations/codeRunnerValidation';
import { BaseRouter } from './BaseRouter';

export class CodeRunnerRoutes extends BaseRouter {
  private readonly codeRunnerController: CodeRunnerController;

  constructor() {
    super();
    this.codeRunnerController = new CodeRunnerController();
    this.initializeRoutes();
  }

  protected initializeRoutes(): void {
    // Apply rate limiting to prevent abuse
    this.router.use(
      rateLimiterMiddleware(
        RATE_LIMITS.CODE_RUNNER.max,
        RATE_LIMITS.CODE_RUNNER.windowMs / 1000,
        'code-runner',
      ),
    );

    // Execute code endpoint
    this.router.post(
      '/execute',
      validateRequest(executeCodeValidation),
      this.codeRunnerController.executeCode,
    );

    // Run code with test cases endpoint
    this.router.post(
      '/test',
      validateRequest(executeCodeValidation),
      this.codeRunnerController.testCode,
    );

    // Get supported languages endpoint
    this.router.get(
      '/languages',
      this.codeRunnerController.getSupportedLanguages,
    );
  }
}

export default new CodeRunnerRoutes().getRouter();
