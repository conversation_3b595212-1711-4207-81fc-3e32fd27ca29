import BattleRuleController from '../controllers/battleRuleControllers';
import { authMiddleware } from '../middlewares/authMiddleware';
// import { isAdminOrModerator } from '../middlewares/rbacMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  associateRulesValidationSchema,
  battleRuleIdValidation,
  createBattleRuleValidationSchema,
  updateBattleRuleValidationSchema,
} from '../validations/battleRuleValidations';
import { BaseRouter } from './BaseRouter';

export class BattleRuleRoutes extends BaseRouter {
  private readonly battleRuleController: BattleRuleController;

  constructor() {
    super();
    this.battleRuleController = new BattleRuleController();
  }

  protected initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.get(
      '/battle/:id',
      this.battleRuleController.getBattleRulesByBattleId,
    );

    // Protected routes (authentication + admin/moderator required)
    this.router.get(
      '/',
      authMiddleware,
      // isAdminOrModerator,
      this.battleRuleController.getBattleRules,
    );

    this.router.get(
      '/:id',
      authMiddleware,
      //  isAdminOrModerator,
      validateRequest(battleRuleIdValidation, 'params'),
      this.battleRuleController.getBattleRule,
    );

    this.router.post(
      '/',
      authMiddleware,
      // isAdminOrModerator,
      validateRequest(createBattleRuleValidationSchema),
      this.battleRuleController.createBattleRule,
    );

    this.router.put(
      '/:id',
      authMiddleware,
      // isAdminOrModerator,
      validateRequest(battleRuleIdValidation, 'params'),
      validateRequest(updateBattleRuleValidationSchema),
      this.battleRuleController.updateBattleRule,
    );

    this.router.delete(
      '/:id',
      authMiddleware,
      // isAdminOrModerator,
      validateRequest(battleRuleIdValidation, 'params'),
      this.battleRuleController.deleteBattleRule,
    );

    this.router.post(
      '/associate',
      authMiddleware,
      // isAdminOrModerator,
      validateRequest(associateRulesValidationSchema),
      this.battleRuleController.associateRulesWithBattle,
    );
  }
}

export default new BattleRuleRoutes().getRouter();
