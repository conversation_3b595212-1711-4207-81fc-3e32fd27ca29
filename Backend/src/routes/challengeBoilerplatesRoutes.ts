import {
  NextFunction,
  Request,
  RequestHandler,
  Response,
  Router,
} from 'express';

import ChallengeBoilerplatesController from '../controllers/challengeBoilerplatesController';
import { validateRequest } from '../middlewares/validateRequest';
import { challengeBoilerplateValidation } from '../validations/challengeBoilerplatesValidation';
import { BaseRouter } from './BaseRouter';

export class ChallengeBoilerplatesRoutes extends BaseRouter {
  private readonly boilerplatesController: ChallengeBoilerplatesController;

  constructor() {
    super();
    this.boilerplatesController = new ChallengeBoilerplatesController();
    // We need to recreate the router with mergeParams option
    // to access challengeId from parent router
    Object.defineProperty(this, 'router', {
      value: Router({ mergeParams: true }),
      writable: false,
    });
  }

  protected initializeRoutes(): void {
    // Get all boilerplates for a challenge
    this.router.get(
      '/',
      this.bindRoute(this.boilerplatesController.getBoilerplates),
    );

    // Get a specific boilerplate by language
    this.router.get(
      '/:language',
      this.bindRoute(this.boilerplatesController.getBoilerplateByLanguage),
    );

    // Protected routes
    this.router.post(
      '/',
      validateRequest(challengeBoilerplateValidation),
      this.bindRoute(this.boilerplatesController.createBoilerplate),
    );

    this.router.put(
      '/:language',
      validateRequest(challengeBoilerplateValidation),
      this.bindRoute(this.boilerplatesController.updateBoilerplate),
    );

    this.router.delete(
      '/:language',
      this.bindRoute(this.boilerplatesController.deleteBoilerplate),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.boilerplatesController, req, res, next);
    };
  }
}

export default new ChallengeBoilerplatesRoutes().getRouter();
