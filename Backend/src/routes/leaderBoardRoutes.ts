import { RoleType } from '@prisma/client';

import { validateRequest } from '@/middlewares/validateRequest';
import { leaderboardQuerySchema } from '@/validations/leaderboardValidation';

import LeaderboardController from '../controllers/leaderBoardControllers';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { BaseRouter } from './BaseRouter';

export class LeaderboardRoutes extends BaseRouter {
  private readonly leaderboardController: LeaderboardController;

  constructor() {
    super();
    this.leaderboardController = new LeaderboardController();
    this.initializeRoutes();
  }

  protected initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.get('/weekly', this.leaderboardController.getWeeklyLeaderboard);

    // Routes that require authentication
    this.router.use(authMiddleware);

    // General leaderboard routes
    this.router.get(
      '/',
      validateRequest(leaderboardQuerySchema, 'query'),
      this.leaderboardController.getLeaderboardEntries,
    );

    // Time-based leaderboards
    this.router.get(
      '/time/:time_range',
      this.leaderboardController.getTimeBasedLeaderboard,
    );

    // Language-specific leaderboards for challenges
    this.router.get(
      '/challenges/:challenge_id/languages/:language',
      this.leaderboardController.getLanguageLeaderboard,
    );

    // Admin-only routes
    this.router.get(
      '/submissions/:submission_id/check',
      authorizeRoles(RoleType.ADMIN),
      this.leaderboardController.checkSubmission,
    );
  }
}

export default new LeaderboardRoutes().getRouter();
