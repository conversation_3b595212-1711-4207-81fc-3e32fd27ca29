import BookmarkCollectionController from '../controllers/bookmarkCollectionController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  addBookmarksValidation,
  createCollectionValidation,
  moveBookmarksValidation,
  updateCollectionValidation,
} from '../validations/bookmarkValidation';
import { BaseRouter } from './BaseRouter';

export class BookmarkCollectionRoutes extends BaseRouter {
  private readonly collectionController: BookmarkCollectionController;

  constructor() {
    super();
    this.collectionController = new BookmarkCollectionController();
    this.initializeRoutes();
  }

  protected initializeRoutes(): void {
    // All routes require authentication
    this.router.use(authMiddleware);

    // Collection management
    this.router.get('/', this.collectionController.getCollections);
    this.router.get('/:id', this.collectionController.getCollection);
    this.router.post(
      '/',
      validateRequest(createCollectionValidation),
      this.collectionController.createCollection,
    );
    this.router.patch(
      '/:id',
      validateRequest(updateCollectionValidation),
      this.collectionController.updateCollection,
    );
    this.router.delete('/:id', this.collectionController.deleteCollection);

    // Bookmark management within collections
    this.router.post(
      '/:id/bookmarks',
      validateRequest(addBookmarksValidation),
      this.collectionController.addBookmarksToCollection,
    );
    this.router.delete(
      '/:id/bookmarks',
      validateRequest(addBookmarksValidation),
      this.collectionController.removeBookmarksFromCollection,
    );
    this.router.post(
      '/:sourceId/move/:targetId',
      validateRequest(moveBookmarksValidation),
      this.collectionController.moveBookmarks,
    );
  }
}

export default new BookmarkCollectionRoutes().getRouter();
