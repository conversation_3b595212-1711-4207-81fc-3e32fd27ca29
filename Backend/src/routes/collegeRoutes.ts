import CollegeController from '../controllers/collegeController';
import { BaseRouter } from './BaseRouter';

export class CollegeRoutes extends BaseRouter {
  private readonly collegeController: CollegeController;

  constructor() {
    super();
    this.collegeController = new CollegeController();
  }

  protected initializeRoutes(): void {
    this.router.get('/', this.collegeController.getColleges);
  }
}

export default new CollegeRoutes().getRouter();
