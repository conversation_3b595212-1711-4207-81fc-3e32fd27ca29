import { NextFunction, Request, RequestHandler, Response } from 'express';

import { RoleType } from '@prisma/client';

import AchievementController from '../controllers/achievementController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  awardAchievementValidation,
  createAchievementValidation,
  markNotificationsAsReadValidation,
  updateAchievementValidation,
} from '../validations/achievementValidation';
import { BaseRouter } from './BaseRouter';

export class AchievementRoutes extends BaseRouter {
  private readonly achievementController: AchievementController;

  constructor() {
    super();
    this.achievementController = new AchievementController();
  }

  protected initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.get(
      '/popular',
      this.bindRoute(this.achievementController.getMostPopularAchievements),
    );

    this.router.get(
      '/rarest',
      this.bindRoute(this.achievementController.getRarestAchievements),
    );

    // Protected routes (authentication required)
    this.router.use(authMiddleware);

    // User achievement routes
    this.router.get(
      '/user',
      this.bindRoute(this.achievementController.getUserAchievements),
    );

    this.router.get(
      '/notifications',
      this.bindRoute(this.achievementController.getAchievementNotifications),
    );

    this.router.post(
      '/notifications/read',
      validateRequest(markNotificationsAsReadValidation),
      this.bindRoute(this.achievementController.markNotificationsAsRead),
    );

    this.router.post(
      '/check',
      this.bindRoute(this.achievementController.checkAchievements),
    );

    // Admin routes
    this.router.get(
      '/',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.achievementController.getAllAchievements),
    );

    this.router.get(
      '/stats',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.achievementController.getAchievementStats),
    );

    this.router.get(
      '/:id',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.achievementController.getAchievement),
    );

    this.router.post(
      '/',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(createAchievementValidation),
      this.bindRoute(this.achievementController.createAchievement),
    );

    this.router.patch(
      '/:id',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(updateAchievementValidation),
      this.bindRoute(this.achievementController.updateAchievement),
    );

    this.router.post(
      '/award',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(awardAchievementValidation),
      this.bindRoute(this.achievementController.awardAchievement),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.achievementController, req, res, next);
    };
  }
}

export default new AchievementRoutes().getRouter();
