import RoleController from '@/controllers/roleController';

import UserController from '../controllers/userControllers';
import { authMiddleware } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import { userInsertionSchema } from '../validations/userValidations';
import { BaseRouter } from './BaseRouter';

export class UserRoutes extends BaseRouter {
  private readonly userController: UserController;
  private readonly roleController: RoleController;

  constructor() {
    super();
    this.userController = new UserController();
    this.roleController = new RoleController();
  }

  protected initializeRoutes(): void {
    this.router.get('/me', this.userController.getProfile);

    this.router.put(
      '/me',
      validateRequest(userInsertionSchema),
      this.userController.upsertUser,
    );

    this.router.get(
      '/progress',
      authMiddleware,
      this.userController.getUserProgress,
    );
    this.router.get(
      '/roadmap',
      authMiddleware,
      this.userController.getUserRoadmap,
    );
    this.router.post(
      '/roadmap',
      authMiddleware,
      this.userController.insertUserRoadmap,
    );
    this.router.delete(
      '/roadmap/:id',
      authMiddleware,
      this.userController.deleteUserRoadmap,
    );
    this.router.get('/check-username', this.userController.checkUsername);

    // Get roles for a specific user - Admin or the user themselves
    this.router.get(
      '/:userId/roles',
      authMiddleware,
      this.roleController.getRolesByUser,
    );
  }
}
