import express, { Router } from 'express';

import { RoleType } from '@prisma/client';

import RoleController from '../controllers/roleController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { BaseRouter } from './BaseRouter';

export class RoleRoutes extends BaseRouter {
  private readonly roleController: RoleController;

  constructor() {
    super();
    this.roleController = new RoleController();
  }

  protected initializeRoutes(): void {
    // Role management routes - Admin only
    this.router.get(
      '/',
      authorizeRoles(RoleType.ADMIN),
      this.roleController.getAllRoles,
    );
    this.router.get(
      '/:id',
      authorizeRoles(RoleType.ADMIN),
      this.roleController.getRoleById,
    );
    this.router.post(
      '/',
      authorizeRoles(RoleType.ADMIN),
      this.roleController.createRole,
    );
    this.router.put(
      '/:id',
      authorizeRoles(RoleType.ADMIN),
      this.roleController.updateRole,
    );
    this.router.delete(
      '/:id',
      authorizeRoles(RoleType.ADMIN),
      this.roleController.deleteRole,
    );

    // User-role management routes
    this.router.post(
      '/:roleId/users/:userId',
      authorizeRoles(RoleType.ADMIN),
      this.roleController.assignRoleToUser,
    );
    this.router.delete(
      '/:roleId/users/:userId',
      authorizeRoles(RoleType.ADMIN),
      this.roleController.removeRoleFromUser,
    );
    this.router.get(
      '/:roleId/users',
      authorizeRoles(RoleType.ADMIN),
      this.roleController.getUsersByRole,
    );
  }

  public getRouter(): Router {
    return this.router;
  }
}
