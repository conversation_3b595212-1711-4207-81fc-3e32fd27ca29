import ResourceController from '../controllers/resourceController';
import { authMiddleware } from '../middlewares/authMiddleware';
import paginationMiddleware from '../middlewares/paginationMiddleware';
import { BaseRouter } from './BaseRouter';

export class ResourceRoutes extends BaseRouter {
  private readonly resourceController: ResourceController;

  constructor() {
    super();
    this.resourceController = new ResourceController();
    this.router.use(authMiddleware);
  }

  protected initializeRoutes(): void {
    this.router.get(
      '/',
      paginationMiddleware,
      this.resourceController.getResources,
    );
    this.router.get('/:id', this.resourceController.getResource);
    this.router.post('/create-subject', this.resourceController.createSubjects);
    this.router.post(
      '/delete-subjects',
      this.resourceController.deleteSubjects,
    );
    this.router.get('/details/:id', this.resourceController.getResourceDetails);
    this.router.post('/create', this.resourceController.createResource);
    this.router.post('/save/:id', this.resourceController.saveResource);
  }
}
