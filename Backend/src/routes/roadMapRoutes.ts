import { NextFunction, Request, RequestHandler, Response } from 'express';

import { RoleType } from '@prisma/client';

import { RATE_LIMITS } from '../config/rateLimitConstants';
import RoadMapController from '../controllers/roadMapControllers';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { cacheResponse } from '../middlewares/cacheControl';
import { createRateLimiter } from '../middlewares/rateLimiter';
import { validateQuery, validateRequest } from '../middlewares/validateRequest';
import {
  addCommentValidation,
  createRoadmapValidation,
  enrollRoadmapValidation,
  roadmapQueryValidation,
  updateSubjectsOrderValidation,
} from '../validations/roadmapValidation';
import { BaseRouter } from './BaseRouter';

export class RoadMapRoutes extends BaseRouter {
  private readonly roadMapController: RoadMapController;
  private readonly roadmapLimiter: ReturnType<typeof createRateLimiter>;

  constructor() {
    super();
    this.roadMapController = new RoadMapController();
    this.roadmapLimiter = createRateLimiter({
      windowMs: RATE_LIMITS.ROADMAP.windowMs,
      max: RATE_LIMITS.ROADMAP.max,
      message: RATE_LIMITS.ROADMAP.message,
    });
  }

  protected initializeRoutes(): void {
    // Categories
    this.router.get(
      '/categories',
      authMiddleware,
      this.bindRoute(this.roadMapController.getRoadmapCategories),
    );

    // Public routes
    this.router.get(
      '/',
      authMiddleware,
      validateQuery(roadmapQueryValidation),
      this.roadmapLimiter,
      (req: Request, res: Response, next: NextFunction) => {
        const cacheMiddleware = cacheResponse({ duration: 60 });
        cacheMiddleware(req, res, next).catch(next);
      },
      this.bindRoute(this.roadMapController.getAllRoadmaps),
    );

    this.router.get(
      '/:id',
      authMiddleware,
      this.bindRoute(this.roadMapController.getRoadMap),
    );

    this.router.get(
      '/:id/main_concepts',
      authMiddleware,
      this.bindRoute(this.roadMapController.getMainConceptsInRoadmap),
    );

    // Social interaction routes
    this.router.post(
      '/:id/like',
      authMiddleware,
      this.bindRoute(this.roadMapController.likeRoadmap),
    );

    this.router.post(
      '/:id/bookmark',
      authMiddleware,
      this.bindRoute(this.roadMapController.bookmarkRoadmap),
    );

    // Comment routes
    this.router.get(
      '/:id/comments',
      authMiddleware,
      this.bindRoute(this.roadMapController.getRoadmapComments),
    );

    this.router.post(
      '/:id/comments',
      authMiddleware,
      validateRequest(addCommentValidation),
      this.bindRoute(this.roadMapController.addComment),
    );

    this.router.post(
      '/:roadmapId/comments/:commentId/like',
      authMiddleware,
      this.bindRoute(this.roadMapController.toggleCommentLike),
    );

    // Protected routes
    this.router.post(
      '/',
      authMiddleware,
      // authorizeRoles('admin', 'instructor'),
      validateRequest(createRoadmapValidation),
      this.bindRoute(this.roadMapController.createRoadMap),
    );

    this.router.post(
      '/enroll',
      authMiddleware,
      validateRequest(enrollRoadmapValidation),
      this.bindRoute(this.roadMapController.enrollRoadMap),
    );

    this.router.put(
      '/:id',
      authMiddleware,
      // authorizeRoles('admin', 'instructor'),
      validateRequest(createRoadmapValidation),
      this.bindRoute(this.roadMapController.updateRoadMap),
    );

    this.router.delete(
      '/:id',
      authMiddleware,
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.roadMapController.deleteRoadMap),
    );

    this.router.patch(
      '/:id/subjects-order',
      authMiddleware,
      // authorizeRoles('admin', 'instructor'),
      validateRequest(updateSubjectsOrderValidation),
      this.bindRoute(this.roadMapController.updateSubjectsOrder),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.roadMapController, req, res, next);
    };
  }
}

export default new RoadMapRoutes().getRouter();
