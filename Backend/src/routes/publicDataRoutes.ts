import { NextFunction, Request, Response } from 'express';

import PublicDataController from '../controllers/publicDataController';
import { cacheResponse } from '../middlewares/cacheControl';
import { BaseRouter } from './BaseRouter';

export class PublicDataRoutes extends BaseRouter {
  private readonly publicDataController: PublicDataController;

  constructor() {
    super();
    this.publicDataController = new PublicDataController();
    this.initializeRoutes();
  }

  protected initializeRoutes(): void {
    // Public routes that don't require authentication
    this.router.get(
      '/leaderboard',
      (req: Request, res: Response, next: NextFunction) => {
        const cacheMiddleware = cacheResponse({ duration: 60 }); // Cache for 1 minute
        cacheMiddleware(req, res, next).catch(next);
      },
      this.publicDataController.getWeeklyLeaderboard,
    );

    this.router.get(
      '/stats',
      (req: Request, res: Response, next: NextFunction) => {
        const cacheMiddleware = cacheResponse({ duration: 300 }); // Cache for 5 minutes
        cacheMiddleware(req, res, next).catch(next);
      },
      this.publicDataController.getPlatformStats,
    );

    this.router.get(
      '/colleges',
      (req: Request, res: Response, next: NextFunction) => {
        const cacheMiddleware = cacheResponse({ duration: 3600 }); // Cache for 1 hour
        cacheMiddleware(req, res, next).catch(next);
      },
      this.publicDataController.getTopColleges,
    );

    this.router.get(
      '/colleges/search',
      (req: Request, res: Response, next: NextFunction) => {
        const cacheMiddleware = cacheResponse({ duration: 3600 }); // Cache for 1 hour
        cacheMiddleware(req, res, next).catch(next);
      },
      this.publicDataController.searchColleges,
    );
  }
}

export default new PublicDataRoutes().getRouter();
