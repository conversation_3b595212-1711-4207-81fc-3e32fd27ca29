import { NextFunction, Request, RequestHandler, Response } from 'express';

import ChallengeController from '../controllers/challengeController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  createChallengeValidation,
  saveFilterPresetValidation,
  submitChallengeValidation,
} from '../validations/challengeValidation';
import { BaseRouter } from './BaseRouter';
import challengeBoilerplatesRoutes from './challengeBoilerplatesRoutes';
import challengeDiscussionRoutes from './challengeDiscussionRoutes';
import challengeExamplesRoutes from './challengeExamplesRoutes';
import userChallengeBookmarksRoutes from './userChallengeBookmarksRoutes';

export class ChallengeRoutes extends BaseRouter {
  private readonly challengeController: ChallengeController;

  constructor() {
    super();
    this.challengeController = new ChallengeController();
  }

  protected initializeRoutes(): void {
    // Apply authentication middleware to all routes
    this.router.use(authMiddleware);

    // Public routes
    this.router.get(
      '/',
      this.bindRoute(this.challengeController.getChallenges),
    );
    this.router.get(
      '/leaderboard',
      this.bindRoute(this.challengeController.getChallengeLeaderboard),
    );
    this.router.get(
      '/:id',
      this.bindRoute(this.challengeController.getChallenge),
    );

    // Filter preset routes
    this.router.get(
      '/filter-presets',
      this.bindRoute(this.challengeController.getFilterPresets),
    );
    this.router.post(
      '/filter-presets',
      validateRequest(saveFilterPresetValidation),
      this.bindRoute(this.challengeController.saveFilterPreset),
    );
    this.router.delete(
      '/filter-presets/:presetId',
      this.bindRoute(this.challengeController.deleteFilterPreset),
    );

    // Protected routes
    this.router.post(
      '/',
      // authorizeRoles('admin', 'instructor'),
      validateRequest(createChallengeValidation),
      this.bindRoute(this.challengeController.createNewChallenge),
    );

    this.router.patch(
      '/:id',
      // authorizeRoles('admin', 'instructor'),
      validateRequest(createChallengeValidation),
      this.bindRoute(this.challengeController.updateExistingChallenge),
    );

    this.router.post(
      '/:challengeId/submit',
      validateRequest(submitChallengeValidation),
      this.bindRoute(this.challengeController.submitChallengeAttempt),
    );

    // Challenge examples routes
    this.router.use('/:challengeId/examples', challengeExamplesRoutes);

    // Challenge boilerplates routes
    this.router.use('/:challengeId/boilerplates', challengeBoilerplatesRoutes);

    // User challenge bookmarks routes
    this.router.use('/bookmarks', userChallengeBookmarksRoutes);

    // Challenge discussions routes
    this.router.use('/:challengeId/discussions', challengeDiscussionRoutes);
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.challengeController, req, res, next);
    };
  }
}

export default new ChallengeRoutes().getRouter();
