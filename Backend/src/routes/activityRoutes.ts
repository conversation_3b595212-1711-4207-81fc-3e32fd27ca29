import ActivityController from '../controllers/activityControllers';
import { authMiddleware } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  activityIdValidation,
  activityQueryValidation,
  createActivityValidation,
} from '../validations/activityValidations';
import { BaseRouter } from './BaseRouter';

export class ActivityRoutes extends BaseRouter {
  private readonly activityController: ActivityController;

  constructor() {
    super();
    this.activityController = new ActivityController();
  }

  protected initializeRoutes(): void {
    // Get all activities (requires authentication)
    this.router.get(
      '/',
      authMiddleware,
      validateRequest(activityQueryValidation, 'query'),
      this.activityController.getActivities,
    );

    // Get a specific activity by ID (requires authentication)
    this.router.get(
      '/:id',
      authMiddleware,
      validateRequest(activityIdValidation, 'params'),
      this.activityController.getActivity,
    );

    // Create a new activity (requires authentication)
    this.router.post(
      '/',
      authMiddleware,
      validateRequest(createActivityValidation),
      this.activityController.createActivity,
    );

    // Delete an activity (requires authentication)
    this.router.delete(
      '/:id',
      authMiddleware,
      validateRequest(activityIdValidation, 'params'),
      this.activityController.deleteActivity,
    );
  }
}

export default new ActivityRoutes().getRouter();
