import { NextFunction, Request, RequestHandler, Response } from 'express';

import { RoleType } from '@prisma/client';

import RoadmapChallengeController from '../controllers/roadmapChallengeController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  addChallengeToRoadmapValidation,
  removeChallengeFromRoadmapValidation,
  updateRoadmapChallengeValidation,
  updateRoadmapProgressValidation,
} from '../validations/roadmapChallengeValidation';
import { BaseRouter } from './BaseRouter';

export class RoadmapChallengeRoutes extends BaseRouter {
  private readonly roadmapChallengeController: RoadmapChallengeController;

  constructor() {
    super();
    this.roadmapChallengeController = new RoadmapChallengeController();
  }

  protected initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.get(
      '/roadmaps/:id/challenges',
      this.bindRoute(this.roadmapChallengeController.getChallengesByRoadmap),
    );

    this.router.get(
      '/challenges/:id/roadmaps',
      this.bindRoute(this.roadmapChallengeController.getRoadmapsByChallenge),
    );

    // Protected routes (authentication required)
    this.router.use(authMiddleware);

    // User routes
    this.router.get(
      '/roadmaps/:id/suggested-challenges',
      this.bindRoute(this.roadmapChallengeController.getSuggestedChallenges),
    );

    this.router.post(
      '/progress',
      validateRequest(updateRoadmapProgressValidation),
      this.bindRoute(this.roadmapChallengeController.updateRoadmapProgress),
    );

    // Admin routes
    this.router.post(
      '/',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(addChallengeToRoadmapValidation),
      this.bindRoute(this.roadmapChallengeController.addChallengeToRoadmap),
    );

    this.router.delete(
      '/',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(removeChallengeFromRoadmapValidation),
      this.bindRoute(
        this.roadmapChallengeController.removeChallengeFromRoadmap,
      ),
    );

    this.router.patch(
      '/',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(updateRoadmapChallengeValidation),
      this.bindRoute(this.roadmapChallengeController.updateRoadmapChallenge),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.roadmapChallengeController, req, res, next);
    };
  }
}

export default new RoadmapChallengeRoutes().getRouter();
