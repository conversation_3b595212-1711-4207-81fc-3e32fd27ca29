import { NextFunction, Request, RequestHandler, Response } from 'express';

import { RoleType } from '@prisma/client';

import DiscussionController from '../controllers/discussionController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  createDiscussionValidation,
  flagDiscussionValidation,
  markNotificationsAsReadValidation,
  moderateDiscussionValidation,
  updateDiscussionValidation,
  voteDiscussionValidation,
} from '../validations/discussionValidation';
import { BaseRouter } from './BaseRouter';

export class DiscussionRoutes extends BaseRouter {
  private readonly discussionController: DiscussionController;

  constructor() {
    super();
    this.discussionController = new DiscussionController();
  }

  protected initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.get(
      '/challenges/:challengeId',
      this.bindRoute(this.discussionController.getDiscussionsForChallenge),
    );

    this.router.get(
      '/:id',
      this.bindRoute(this.discussionController.getDiscussion),
    );

    // Protected routes (authentication required)
    this.router.use(authMiddleware);

    // User routes
    this.router.post(
      '/',
      validateRequest(createDiscussionValidation),
      this.bindRoute(this.discussionController.createDiscussion),
    );

    this.router.patch(
      '/:id',
      validateRequest(updateDiscussionValidation),
      this.bindRoute(this.discussionController.updateDiscussion),
    );

    this.router.delete(
      '/:id',
      this.bindRoute(this.discussionController.deleteDiscussion),
    );

    this.router.post(
      '/:id/vote',
      validateRequest(voteDiscussionValidation),
      this.bindRoute(this.discussionController.voteDiscussion),
    );

    this.router.post(
      '/:id/flag',
      validateRequest(flagDiscussionValidation),
      this.bindRoute(this.discussionController.flagDiscussion),
    );

    this.router.get(
      '/notifications',
      this.bindRoute(this.discussionController.getNotifications),
    );

    this.router.post(
      '/notifications/read',
      validateRequest(markNotificationsAsReadValidation),
      this.bindRoute(this.discussionController.markNotificationsAsRead),
    );

    // Admin routes
    this.router.get(
      '/moderation/flagged',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.discussionController.getFlaggedDiscussions),
    );

    this.router.post(
      '/:id/moderate',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(moderateDiscussionValidation),
      this.bindRoute(this.discussionController.moderateDiscussion),
    );

    this.router.get(
      '/stats',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.discussionController.getDiscussionStats),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.discussionController, req, res, next);
    };
  }
}

export default new DiscussionRoutes().getRouter();
