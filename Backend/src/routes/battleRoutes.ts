import { Router } from 'express';
import { battleRoutes as newBattleRoutes } from '../modules/battle';

export class BattleRoutes {
  private readonly router: Router;

  constructor() {
    this.router = Router();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // Mount the new battle routes at the root so parent prefix '/battles' applies once
    this.router.use('/', newBattleRoutes);
    
    // Legacy placeholders removed; all battle endpoints are handled by the module router.
  }

  public getRouter(): Router {
    return this.router;
  }
}

export default new BattleRoutes().getRouter();
