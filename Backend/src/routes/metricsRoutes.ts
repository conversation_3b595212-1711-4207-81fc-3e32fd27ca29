import { NextFunction, Request, RequestHandler, Response } from 'express';

import { RoleType } from '@prisma/client';

import MetricsController from '../controllers/metricsController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { BaseRouter } from './BaseRouter';

export class MetricsRoutes extends BaseRouter {
  private readonly metricsController: MetricsController;

  constructor() {
    super();
    this.metricsController = new MetricsController();
  }

  protected initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.get(
      '/challenges/:id',
      this.bindRoute(this.metricsController.getChallengeMetrics),
    );

    // Protected routes (authentication required)
    this.router.use(authMiddleware);

    // Submission metrics routes
    this.router.get(
      '/submissions/:id',
      this.bindRoute(this.metricsController.getSubmissionMetrics),
    );

    // Admin routes
    this.router.post(
      '/submissions/:id/suggestions',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.metricsController.generateOptimizationSuggestions),
    );

    this.router.post(
      '/submissions/:id/percentiles',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.metricsController.updatePercentiles),
    );

    this.router.post(
      '/submissions/:id',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.metricsController.recordMetrics),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.metricsController, req, res, next);
    };
  }
}

export default new MetricsRoutes().getRouter();
