import * as rateLimiter from '../middlewares/rateLimiter';
import { RATE_LIMITS } from '../config/rateLimitConstants';
import { StreakController } from '../controllers/streakController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import { updateStreakSchema } from '../validations/streakValidations';
import { BaseRouter } from './BaseRouter';

export class StreakRoutes extends BaseRouter {
  private readonly streakController: StreakController;
  private readonly streakUpdateLimiter: ReturnType<
    typeof rateLimiter.createRateLimiter
  >;

  constructor() {
    super();
    this.streakController = new StreakController();
    this.streakUpdateLimiter = rateLimiter.createRateLimiter({
      windowMs: RATE_LIMITS.STREAK.windowMs,
      max: RATE_LIMITS.STREAK.max,
      message: RATE_LIMITS.STREAK.message,
    });
    this.initializeRoutes();
  }

  protected initializeRoutes(): void {
    // Apply authentication to all routes
    this.router.use(authMiddleware);

    this.router.post(
      '/update',
      this.streakUpdateLimiter,
      validateRequest(updateStreakSchema),
      this.streakController.updateStreak,
    );

    this.router.get('/stats', this.streakController.getStreakStats);
    this.router.get(
      '/weekly-activity',
      this.streakController.getWeeklyActivity,
    );
  }
}
