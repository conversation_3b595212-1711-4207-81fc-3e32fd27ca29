import { NextFunction, Request, RequestHandler, Response } from 'express';

import { RoleType } from '@prisma/client';

import HintController from '../controllers/hintController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { validateRequest } from '../middlewares/validateRequest';
import {
  createHintValidation,
  updateHintValidation,
} from '../validations/hintValidation';
import { BaseRouter } from './BaseRouter';

export class HintRoutes extends BaseRouter {
  private readonly hintController: HintController;

  constructor() {
    super();
    this.hintController = new HintController();
  }

  protected initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.get(
      '/challenges/:challengeId',
      this.bindRoute(this.hintController.getHintsForChallenge),
    );

    // Protected routes (authentication required)
    this.router.use(authMiddleware);

    // User routes
    this.router.post(
      '/:id/unlock',
      this.bindRoute(this.hintController.unlockHint),
    );

    this.router.get(
      '/user/usage',
      this.bindRoute(this.hintController.getUserHintUsage),
    );

    // Admin routes
    this.router.post(
      '/',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(createHintValidation),
      this.bindRoute(this.hintController.createHint),
    );

    this.router.patch(
      '/:id',
      authorizeRoles(RoleType.ADMIN),
      validateRequest(updateHintValidation),
      this.bindRoute(this.hintController.updateHint),
    );

    this.router.delete(
      '/:id',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.hintController.deleteHint),
    );

    this.router.get(
      '/stats',
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.hintController.getHintUsageStats),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.hintController, req, res, next);
    };
  }
}

export default new HintRoutes().getRouter();
