import {
  NextFunction,
  Request,
  RequestHandler,
  Response,
  Router,
} from 'express';

import ChallengeExamplesController from '../controllers/challengeExamplesController';
import { validateRequest } from '../middlewares/validateRequest';
import { challengeExampleValidation } from '../validations/challengeExamplesValidation';
import { BaseRouter } from './BaseRouter';

export class ChallengeExamplesRoutes extends BaseRouter {
  private readonly examplesController: ChallengeExamplesController;

  constructor() {
    super();
    this.examplesController = new ChallengeExamplesController();
    // We need to recreate the router with mergeParams option
    // to access challengeId from parent router
    Object.defineProperty(this, 'router', {
      value: Router({ mergeParams: true }),
      writable: false,
    });
  }

  protected initializeRoutes(): void {
    // Get all examples for a challenge
    this.router.get('/', this.bindRoute(this.examplesController.getExamples));

    // Protected routes
    this.router.post(
      '/',
      validateRequest(challengeExampleValidation),
      this.bindRoute(this.examplesController.createExample),
    );

    this.router.put(
      '/:id',
      validateRequest(challengeExampleValidation),
      this.bindRoute(this.examplesController.updateExample),
    );

    this.router.delete(
      '/:id',
      this.bindRoute(this.examplesController.deleteExample),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.examplesController, req, res, next);
    };
  }
}

export default new ChallengeExamplesRoutes().getRouter();
