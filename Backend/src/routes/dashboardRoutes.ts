import { RATE_LIMITS } from '../config/rateLimitConstants';
import { DashboardController } from '../controllers/dashboardController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { createRateLimiter } from '../middlewares/rateLimiter';
import { BaseRouter } from './BaseRouter';

export class DashboardRoutes extends BaseRouter {
  private readonly dashboardController: DashboardController;
  private readonly dashboardLimiter: ReturnType<typeof createRateLimiter>;

  constructor() {
    super();
    this.dashboardController = new DashboardController();
    this.dashboardLimiter = createRateLimiter({
      windowMs: RATE_LIMITS.DASHBOARD.windowMs,
      max: RATE_LIMITS.DASHBOARD.max,
      message: RATE_LIMITS.DASHBOARD.message,
    });
    this.initializeRoutes();
  }

  protected initializeRoutes(): void {
    // Apply authentication to all routes
    this.router.use(authMiddleware);

    // Apply rate limiter to all dashboard routes
    this.router.use(this.dashboardLimiter);

    this.router.get('/stats', this.dashboardController.getDashboardStats);
    this.router.get(
      '/activities',
      this.dashboardController.getRecentActivities,
    );
    this.router.get('/progress', this.dashboardController.getLearningProgress);
    this.router.get('/achievements', this.dashboardController.getAchievements);
  }
}
