import { NextFunction, Request, RequestHandler, Response } from 'express';

import { RoleType } from '@prisma/client';

import { RATE_LIMITS } from '../config/rateLimitConstants';
import RoadmapAdminController from '../controllers/roadmapAdminController';
import { authMiddleware, authorizeRoles } from '../middlewares/authMiddleware';
import { createRateLimiter } from '../middlewares/rateLimiter';
import { validateQuery, validateRequest } from '../middlewares/validateRequest';
import {
  createRoadmapValidation,
  roadmapQueryValidation,
  toggleFeaturedValidation,
} from '../validations/roadmapValidation';
import { BaseRouter } from './BaseRouter';

export class AdminRoadmapRoutes extends BaseRouter {
  private readonly roadmapAdminController: RoadmapAdminController;
  private readonly adminRoadmapLimiter: ReturnType<typeof createRateLimiter>;

  constructor() {
    super();
    this.roadmapAdminController = new RoadmapAdminController();
    // Using CONTENT rate limits for admin roadmap operations
    this.adminRoadmapLimiter = createRateLimiter({
      windowMs: RATE_LIMITS.CONTENT.windowMs,
      max: RATE_LIMITS.CONTENT.max,
      message: RATE_LIMITS.CONTENT.message,
    });
  }

  protected initializeRoutes(): void {
    // Admin routes for roadmaps
    this.router.get(
      '/',
      authMiddleware,
      authorizeRoles(RoleType.ADMIN, RoleType.MODERATOR),
      validateQuery(roadmapQueryValidation),
      this.adminRoadmapLimiter,
      this.bindRoute(this.roadmapAdminController.getAllRoadmaps),
    );

    this.router.get(
      '/:id',
      authMiddleware,
      authorizeRoles(RoleType.ADMIN, RoleType.MODERATOR),
      this.bindRoute(this.roadmapAdminController.getRoadmapById),
    );

    this.router.post(
      '/',
      authMiddleware,
      authorizeRoles(RoleType.ADMIN),
      validateRequest(createRoadmapValidation),
      this.bindRoute(this.roadmapAdminController.createRoadmap),
    );

    this.router.put(
      '/:id',
      authMiddleware,
      authorizeRoles(RoleType.ADMIN),
      validateRequest(createRoadmapValidation),
      this.bindRoute(this.roadmapAdminController.updateRoadmap),
    );

    this.router.delete(
      '/:id',
      authMiddleware,
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.roadmapAdminController.deleteRoadmap),
    );

    // Route for toggling featured status
    this.router.patch(
      '/:id/featured',
      authMiddleware,
      authorizeRoles(RoleType.ADMIN),
      validateRequest(toggleFeaturedValidation),
      this.bindRoute(this.roadmapAdminController.toggleFeatured),
    );

    // Route for bulk status updates
    this.router.patch(
      '/bulk/status',
      authMiddleware,
      authorizeRoles(RoleType.ADMIN),
      this.bindRoute(this.roadmapAdminController.bulkUpdateStatus),
    );
  }

  private bindRoute(
    routeHandler: (
      req: Request,
      res: Response,
      next: NextFunction,
    ) => Promise<void> | void,
  ): RequestHandler {
    return (req: Request, res: Response, next: NextFunction) => {
      return routeHandler.call(this.roadmapAdminController, req, res, next);
    };
  }
}

export default new AdminRoadmapRoutes().getRouter();
