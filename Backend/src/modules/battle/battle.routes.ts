import { Router } from 'express';
import battleController from './controllers/battle.controller';
import battleValidator from './validators/battle.validator';
import { authMiddleware } from '@/middlewares/authMiddleware';
import { battleAntiCheatMiddleware } from '@/middlewares/battleAntiCheatMiddleware';

const router = Router();

// Apply auth middleware to all battle routes
router.use(authMiddleware);

// Battle routes
// List
router.get('/', battleController.listBattles);

// Create
router.post(
  '/',
  battleValidator.validateCreateBattle,
  battleController.createBattle
);

// Update
router.put(
  '/:id',
  battleValidator.validateBattleId,
  battleController.updateBattle
);

// Delete
router.delete(
  '/:id',
  battleValidator.validateBattleId,
  battleController.deleteBattle
);

// Statistics (global or user-specific via query)
router.get('/statistics', battleController.getStatistics);

router.get(
  '/:id',
  battleValidator.validateBattleId,
  battleController.getBattle
);

// Leaderboard
router.get(
  '/:id/leaderboard',
  battleValidator.validateBattleId,
  battleController.getLeaderboard
);

// Questions
router.get(
  '/:id/questions',
  battleValidator.validateBattleId,
  battleController.getBattleQuestions
);

// Submit answer (with anti-cheat middleware)
router.post(
  '/:id/answer',
  battleValidator.validateBattleId,
  battleAntiCheatMiddleware,
  battleController.submitAnswer
);

router.post(
  '/:id/join',
  battleValidator.validateJoinBattle,
  battleController.joinBattle
);

// Leave battle
router.post(
  '/:id/leave',
  battleValidator.validateBattleId,
  battleController.leaveBattle
);

// Select & associate questions
router.post(
  '/:id/questions/select',
  battleValidator.validateBattleId,
  battleController.selectQuestions
);

// Regenerate questions
router.post(
  '/:id/questions/regenerate',
  battleValidator.validateBattleId,
  battleController.regenerateQuestions
);

router.post(
  '/:id/start',
  battleValidator.validateBattleId,
  battleController.startBattle
);

// Update generic progress (optional)
router.patch(
  '/:id/progress',
  battleValidator.validateBattleId,
  battleController.updateProgress
);

// Add more routes as needed

export { router as battleRoutes };
