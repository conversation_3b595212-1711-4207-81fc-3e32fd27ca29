import { body, param } from 'express-validator';
import { BattleType, Difficulty, Length } from '@prisma/client';
import { BaseValidator } from './baseValidator';

class BattleValidator extends BaseValidator {
  public validateCreateBattle = this.validate([
    body('title')
      .trim()
      .isLength({ min: 3, max: 100 })
      .withMessage('Title must be between 3 and 100 characters'),
    body('description')
      .optional()
      .isString()
      .isLength({ max: 500 })
      .withMessage('Description must be less than 500 characters'),
    body('type')
      .isIn(Object.values(BattleType))
      .withMessage('Invalid battle type'),
    body('difficulty')
      .isIn(Object.values(Difficulty))
      .withMessage('Invalid difficulty level'),
    body('topic_id')
      .isUUID()
      .withMessage('topic_id is required and must be a valid UUID'),
    body('maxParticipants')
      .optional()
      .isInt({ min: 2, max: 10 })
      .withMessage('Max participants must be between 2 and 10'),
    body('time_per_question')
      .optional()
      .isInt({ min: 5, max: 600 })
      .withMessage('time_per_question must be between 5 and 600 seconds'),
    body('length')
      .optional()
      .isIn(Object.values(Length))
      .withMessage('Invalid length value'),
    body('points_per_question')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('points_per_question must be between 1 and 1000'),
    body('start_time')
      .optional()
      .isISO8601()
      .withMessage('start_time must be a valid ISO date'),
    body('end_time')
      .optional()
      .isISO8601()
      .withMessage('end_time must be a valid ISO date')
  ]);

  public validateBattleId = this.validate([
    param('id')
      .isUUID()
      .withMessage('Invalid battle ID')
  ]);

  public validateJoinBattle = this.validate([
    param('id')
      .isUUID()
      .withMessage('Invalid battle ID'),
    body('userId')
      .isUUID()
      .withMessage('Invalid user ID')
  ]);
}

export default new BattleValidator();
