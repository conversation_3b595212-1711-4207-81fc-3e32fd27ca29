import { NextFunction, Request, Response } from 'express';
import { validationResult, Validation<PERSON>hain } from 'express-validator';
import { sendError } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';

export abstract class BaseValidator {
  protected validate = (validations: ValidationChain[]) => {
    return async (req: Request, res: Response, next: NextFunction) => {
      await Promise.all(validations.map(validation => validation.run(req)));

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const error = createAppError('Validation failed', 400, {
          validationErrors: errors.array(),
        });
        return sendError(res, error);
      }
      
      return next();
    };
  };

  // Common validation rules can be added here
  protected notEmptyString = (field: string) => {
    return (value: string) => {
      if (!value || value.trim().length === 0) {
        throw new Error(`${field} cannot be empty`);
      }
      return true;
    };
  };
}
