import { Request, Response } from 'express';
import { BattleStatus, BattleType, Difficulty, Length } from '@prisma/client';
import { BattleRepository } from '@/repositories/battleRepository';
import prisma from '@/lib/prisma';
import { BaseController } from './base.controller';
import { createAppError } from '@/utils/errorHandler';

export class BattleController extends BaseController {
  private battleRepository: BattleRepository;

  constructor() {
    super();
    this.battleRepository = new BattleRepository();
  }

  public createBattle = this.handleAsync(async (req: Request, res: Response) => {
    // Map DTO to repository/prisma fields
    const lengthValue = (req.body.length as Length) || Length.medium;

    const battle = await this.battleRepository.createBattle({
      title: req.body.title as string,
      description: req.body.description as string | undefined,
      type: req.body.type as BattleType,
      difficulty: req.body.difficulty as Difficulty,
      topic_id: req.body.topic_id as string,
      user_id: req.user.id as string,
      max_participants: (req.body.maxParticipants as number | undefined) ?? undefined,
      time_per_question: (req.body.time_per_question as number | undefined) ?? undefined,
      length: lengthValue,
      points_per_question: (req.body.points_per_question as number | undefined) ?? undefined,
      start_time: req.body.start_time ? new Date(req.body.start_time) : undefined,
      end_time: req.body.end_time ? new Date(req.body.end_time) : undefined,
    });

    this.handleSuccess(res, battle, 201, 'Battle created successfully');
  });

  public getBattle = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const battle = await this.battleRepository.getBattleDetails(id);
    
    if (!battle) {
      throw createAppError('Battle not found', 404);
    }

    this.handleSuccess(res, battle);
  });

  public joinBattle = this.handleAsync(async (req: Request, res: Response) => {
    const { id: battleId } = req.params;
    const { userId } = req.body;

    const battle = await this.battleRepository.joinBattle(battleId, userId);
    
    // Notify other participants via WebSocket
    // socketService.notifyBattleUpdate(battleId, 'PARTICIPANT_JOINED', { userId });

    this.handleSuccess(res, battle, 200, 'Successfully joined the battle');
  });

  public startBattle = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const requesterId = req.user.id as string;

    // Verify user has permission to start the battle
    const battle = await this.battleRepository.findUnique({ where: { id }, select: { user_id: true } });
    if (!battle) {
      throw createAppError('Battle not found', 404);
    }
    if (battle.user_id !== requesterId) {
      throw createAppError('Only the battle creator can start the battle', 403);
    }

    const updatedBattle = await this.battleRepository.updateBattleStatus(id, BattleStatus.IN_PROGRESS);

    this.handleSuccess(res, updatedBattle, 200, 'Battle started successfully');
  });

  // List battles with pagination and filters
  public listBattles = this.handleAsync(async (req: Request, res: Response) => {
    const {
      page = '1',
      limit = '10',
      search,
      status,
      difficulty,
      type,
      length,
      topic_id,
      user_id,
      sort_by = 'created_at',
      sort_order = 'desc',
    } = req.query as Record<string, string>;

    const result = await this.battleRepository.getBattles({
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      search,
      status: status as any,
      difficulty: difficulty as any,
      type: type as any,
      length: length as any,
      topic_id,
      user_id,
      sort_by,
      sort_order: (sort_order === 'asc' ? 'asc' : 'desc'),
    });

    this.handleSuccess(res, result);
  });

  // Update battle (creator only)
  public updateBattle = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user.id as string;

    // Map allowed fields
    const data: any = {};
    const mapIfDefined = (key: string, val: unknown) => {
      if (val !== undefined) data[key] = val;
    };

    mapIfDefined('title', req.body.title);
    mapIfDefined('description', req.body.description);
    mapIfDefined('type', req.body.type);
    mapIfDefined('difficulty', req.body.difficulty);
    mapIfDefined('max_participants', req.body.max_participants ?? req.body.maxParticipants);
    mapIfDefined('time_per_question', req.body.time_per_question);
    mapIfDefined('length', req.body.length);
    mapIfDefined('points_per_question', req.body.points_per_question);
    if (req.body.start_time) data.start_time = new Date(req.body.start_time);
    if (req.body.end_time) data.end_time = new Date(req.body.end_time);

    const updated = await this.battleRepository.updateBattle(id, data, userId);
    this.handleSuccess(res, updated, 200, 'Battle updated successfully');
  });

  // Delete battle (creator only)
  public deleteBattle = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user.id as string;
    const deleted = await this.battleRepository.deleteBattle(id, userId);
    this.handleSuccess(res, deleted, 200, 'Battle deleted successfully');
  });

  // Get battle questions
  public getBattleQuestions = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const userId = req.user.id as string;
    const questions = await this.battleRepository.getBattleQuestions(id, userId);
    this.handleSuccess(res, questions);
  });

  // Submit an answer
  public submitAnswer = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params; // battle id
    const userId = req.user.id as string;
    const { question_id, answer, time_taken } = req.body as {
      question_id: string; answer: string; time_taken: number;
    };

    if (!question_id || !answer || typeof time_taken !== 'number') {
      throw createAppError('Missing required fields for submission', 400);
    }

    const result = await this.battleRepository.submitAnswer(
      id,
      question_id,
      userId,
      answer,
      time_taken,
    );

    this.handleSuccess(res, result, 200, 'Answer submitted');
  });

  // Leave battle
  public leaveBattle = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params; // battle id
    const userId = req.user.id as string;

    const participant = await prisma.battleParticipant.findUnique({
      where: { battle_id_user_id: { battle_id: id, user_id: userId } },
      select: { id: true },
    });
    if (!participant) {
      throw createAppError('You are not a participant in this battle', 403);
    }

    await prisma.$transaction([
      prisma.battleParticipant.delete({
        where: { battle_id_user_id: { battle_id: id, user_id: userId } },
      }),
      prisma.battle.update({
        where: { id },
        data: { current_participants: { decrement: 1 } },
      }),
    ]);

    this.handleSuccess(res, { left: true }, 200, 'Left battle successfully');
  });

  // Select and associate questions
  public selectQuestions = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params; // battle id
    const requesterId = req.user.id as string;
    const { topic_id, difficulty, count } = req.body as {
      topic_id: string; difficulty: Difficulty; count: number;
    };

    const battle = await this.battleRepository.findUnique({ where: { id }, select: { user_id: true } });
    if (!battle) throw createAppError('Battle not found', 404);
    if (battle.user_id !== requesterId) throw createAppError('Only the battle creator can modify questions', 403);

    const selected = await this.battleRepository.selectQuestionsForBattle(
      topic_id,
      difficulty,
      count,
      requesterId,
    );
    const associated = await this.battleRepository.associateQuestionsWithBattle(id, selected);
    this.handleSuccess(res, { questions: associated, total: associated.length }, 200, 'Questions selected');
  });

  // Regenerate questions (only if UPCOMING)
  public regenerateQuestions = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const requesterId = req.user.id as string;

    const battle = await this.battleRepository.findUnique({ where: { id }, select: { user_id: true, status: true, topic_id: true, difficulty: true, total_questions: true } });
    if (!battle) throw createAppError('Battle not found', 404);
    if (battle.user_id !== requesterId) throw createAppError('Only the battle creator can modify questions', 403);
    if (battle.status !== BattleStatus.UPCOMING) throw createAppError(`Cannot regenerate questions when battle is ${battle.status}`, 400);

    const selected = await this.battleRepository.selectQuestionsForBattle(
      battle.topic_id,
      battle.difficulty,
      Math.max(1, battle.total_questions || 10),
      requesterId,
    );
    const associated = await this.battleRepository.associateQuestionsWithBattle(id, selected);
    this.handleSuccess(res, { questions: associated, total: associated.length }, 200, 'Questions regenerated');
  });

  // Generic progress update (optional)
  public updateProgress = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params; // battle id
    const userId = req.user.id as string;
    const { question_id, is_completed, time_taken } = req.body as {
      question_id: string; is_completed: boolean; time_taken: number;
    };

    const result = await this.battleRepository.updateBattleProgress(
      id,
      userId,
      question_id,
      is_completed,
      time_taken,
    );

    this.handleSuccess(res, result, 200, 'Progress updated');
  });

  // Get leaderboard
  public getLeaderboard = this.handleAsync(async (req: Request, res: Response) => {
    const { id } = req.params;
    const page = req.query.page ? parseInt(String(req.query.page), 10) : 1;
    const limit = req.query.limit ? parseInt(String(req.query.limit), 10) : 10;
    const leaderboard = await this.battleRepository.getBattleLeaderboard(id, limit, page);
    this.handleSuccess(res, leaderboard);
  });

  // Get battle zone statistics (global or per-user)
  public getStatistics = this.handleAsync(async (req: Request, res: Response) => {
    const userId = (req.query.userId as string) || undefined;
    const stats = await this.battleRepository.getBattleZoneStatistics(userId);
    this.handleSuccess(res, stats);
  });
}

export default new BattleController();
