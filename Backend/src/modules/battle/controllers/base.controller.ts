import { NextFunction, Request, Response } from 'express';

import { sendError, sendResponse } from '@/utils/apiResponse';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

type ResponseType = 'USER_CREATED' | 'USER_UPDATED' | 'CODE_EXECUTION_ERROR';

type AsyncRequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction,
) => Promise<void>;

export abstract class BaseController {
  protected handleAsync = (fn: AsyncRequestHandler) => {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        logger.info(
          `[${this.constructor.name}] ${req.method} ${req.originalUrl}`,
          {
            body: req.body,
            params: req.params,
            query: req.query,
            user: (req as any).user?.id || 'anonymous',
          },
        );

        await fn(req, res, next);
      } catch (error: any) {
        logger.error(`[${this.constructor.name}] Error: ${error.message}`, {
          stack: error.stack,
          url: req.originalUrl,
          method: req.method,
          userId: (req as any).user?.id || 'anonymous',
        });

        // Convert to AppError if it's not already one
        if (!error.statusCode) {
          error = createAppError(
            error.message || 'An unexpected error occurred',
            error.statusCode || 500,
            {
              stack:
                process.env.NODE_ENV === 'development'
                  ? error.stack
                  : undefined,
            },
          );
        }

        // Use the centralized error handler
        sendError(res, error);
      }
    };
  };

  protected handleSuccess = (
    res: Response,
    data: any,
    statusCode = 200,
    message = 'Success',
  ) => {
    logger.info(`[${this.constructor.name}] Success: ${message}`, {
      statusCode,
      data: data ? 'data-present' : 'no-data',
    });

    return sendResponse(
      res,
      'USER_UPDATED',
      {
        data,
        message,
      }
    );
  };

  protected handleError = (res: Response, error: any, statusCode = 500) => {
    // If it's already an AppError, use it directly
    if (!error.statusCode) {
      const appError = createAppError(
        error.message || 'An unexpected error occurred',
        statusCode,
        { stack: process.env.NODE_ENV === 'development' ? error.stack : undefined }
      );
      return sendError(res, appError);
    }
    
    // Use the centralized error handler
    return sendError(res, error);
  };
}
