import { NextFunction, Request, Response } from 'express';

import { createAppError } from '../utils/errorHandler';
import logger from '../utils/logger';

// Global flag to bypass RBAC checks
// TODO: Remove this bypass flag when RBAC implementation is ready for production

export const requirePermission = () =>
  // resource: string, action: string
  {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.user) {
          throw createAppError('Unauthorized', 401);
        }

        // RBAC bypass - always grant permission
        // TODO: Remove this bypass and implement proper RBAC checks when ready for production
        // Original implementation:
        // const hasPermission = await checkPermission(
        //   req.user.id,
        //   resource,
        //   action
        // );
        // if (!hasPermission) {
        //   throw createAppError('Forbidden', 403);
        // }

        logger.info('RBAC check bypassed: Permission granted automatically');
        next();
      } catch (error) {
        logger.error('Permission check failed:', error);
        next(error);
      }
    };
  };

export const requireRole = () =>
  // role: string
  {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        if (!req.user) {
          throw createAppError('Unauthorized', 401);
        }

        // RBAC bypass - always grant role access
        // TODO: Remove this bypass and implement proper RBAC role checks when ready for production
        // Original implementation:
        // const hasRole = await checkRole(req.user.id, role);
        // if (!hasRole) {
        //   throw createAppError('Forbidden', 403);
        // }

        logger.info(
          'RBAC check bypassed: Role requirement satisfied automatically',
        );
        next();
      } catch (error) {
        logger.error('Role check failed:', error);
        next(error);
      }
    };
  };
