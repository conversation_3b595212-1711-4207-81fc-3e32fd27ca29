import { NextFunction, Request, Response } from 'express';
import { Request<PERSON><PERSON><PERSON> } from 'express';
import Redis from 'ioredis';

import { REDIS_URL } from '../config';
import { RATE_LIMITS } from '../config/rateLimitConstants';
import logger from '../utils/logger';

let redisClient: Redis | null = null;
try {
  redisClient = new Redis(REDIS_URL || 'redis://localhost:6379', {
    enableOfflineQueue: false,
    maxRetriesPerRequest: 3,
  });

  redisClient.on('error', (err) => {
    logger.error('Redis connection error:', err);
    redisClient = null;
  });

  redisClient.on('connect', () => {
    logger.info('Redis connected successfully');
  });
} catch (err) {
  logger.error('Failed to initialize Redis:', err);
}

interface RateLimitOptions {
  windowMs?: number;
  max?: number;
  message?: string;
}

export const createRateLimiter = (
  options: RateLimitOptions = {},
): RequestHandler => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // 100 requests per window
    message = 'Too many requests, please try again later',
  } = options;

  return (req: Request, res: Response, next: NextFunction): void => {
    if (!redisClient) {
      next();
      return;
    }

    const key = `rate-limit:${req.ip}`;
    const windowInSeconds = Math.floor(windowMs / 1000);

    redisClient
      .multi()
      .incr(key)
      .expire(key, windowInSeconds)
      .exec()
      .then((result) => {
        if (!result) {
          next();
          return;
        }

        const [[incrErr, requestCount], [expireErr]] = result;

        if (incrErr || expireErr) {
          logger.error('Redis operation error:', { incrErr, expireErr });
          next();
          return;
        }

        const count = typeof requestCount === 'number' ? requestCount : 1;

        res.setHeader('X-RateLimit-Limit', max.toString());
        res.setHeader(
          'X-RateLimit-Remaining',
          Math.max(0, max - count).toString(),
        );

        if (count > max) {
          res.status(429).json({
            status: 429,
            message,
          });
          return;
        }

        next();
      })
      .catch((err) => {
        logger.error('Rate limiting error:', err);
        next();
      });
  };
};

// Different rate limits for different routes
export const authLimiter = createRateLimiter({
  windowMs: RATE_LIMITS.AUTH.windowMs,
  max: RATE_LIMITS.AUTH.max,
  message: RATE_LIMITS.AUTH.message,
});

export const apiLimiter = createRateLimiter({
  windowMs: RATE_LIMITS.API.windowMs,
  max: RATE_LIMITS.API.max,
});

export const uploadLimiter = createRateLimiter({
  windowMs: RATE_LIMITS.UPLOAD.windowMs,
  max: RATE_LIMITS.UPLOAD.max,
  message: RATE_LIMITS.UPLOAD.message,
});
