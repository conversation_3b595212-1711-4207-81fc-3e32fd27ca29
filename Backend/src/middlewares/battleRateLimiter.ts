import { RATE_LIMITS } from '../config/rateLimitConstants';
import { createRateLimiter } from './rateLimiter';

// Rate limiter for battle creation
export const battleCreationLimiter = createRateLimiter({
  windowMs: RATE_LIMITS.BATTLE.CREATION.windowMs,
  max: RATE_LIMITS.BATTLE.CREATION.max,
  message: RATE_LIMITS.BATTLE.CREATION.message,
});

// Rate limiter for battle joins
export const battleJoinLimiter = createRateLimiter({
  windowMs: RATE_LIMITS.BATTLE.JOIN.windowMs,
  max: RATE_LIMITS.BATTLE.JOIN.max,
  message: RATE_LIMITS.BATTLE.JOIN.message,
});

// Rate limiter for battle submissions
export const battleSubmissionLimiter = createRateLimiter({
  windowMs: RATE_LIMITS.BATTLE.SUBMISSION.windowMs,
  max: RATE_LIMITS.BATTLE.SUBMISSION.max,
  message: RATE_LIMITS.BATTLE.SUBMISSION.message,
});
