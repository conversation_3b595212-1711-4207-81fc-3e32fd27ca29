import { NextFunction, Request, Response } from 'express';
import { RateLimiterMemory, RateLimiterRes } from 'rate-limiter-flexible';

import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

import { RATE_LIMITS } from '../config/rateLimitConstants';

/**
 * Create a rate limiter middleware
 * @param points Maximum number of points (requests) within duration
 * @param duration Duration in seconds
 * @param keyPrefix Prefix for rate limiter keys
 * @returns Express middleware function
 */
export const rateLimiterMiddleware = (
  points: number,
  duration: number,
  keyPrefix: string = 'code-runner',
) => {
  const limiterOptions = {
    points,
    duration,
    keyPrefix,
  };

  const rateLimiter = new RateLimiterMemory(limiterOptions);

  return async (req: Request, res: Response, next: NextFunction) => {
    // TODO: Temporarily disabled rate limiting for development purposes
    // Re-enable before deploying to production by removing this condition
    if (process.env.NODE_ENV === 'development') {
      // Skip rate limiting entirely in development mode
      return next();
    }

    try {
      // Use IP address as the key
      const key = req.ip || 'unknown';

      // If user is authenticated, use user ID as the key
      if (req.user && req.user.id) {
        await rateLimiter.consume(`${req.user.id}`);
      } else {
        await rateLimiter.consume(key);
      }

      next();
    } catch (error) {
      if (error instanceof RateLimiterRes) {
        // Add retry-after header
        res.set('Retry-After', String(Math.round(error.msBeforeNext / 1000)));
        res.status(429).json({
          success: false,
          message: 'Too many requests, please try again later',
        });
      } else {
        next(error);
      }
    }
  };
};
