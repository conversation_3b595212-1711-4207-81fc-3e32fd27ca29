import { Request, Response } from 'express';
import swaggerUi from 'swagger-ui-express';

import { swaggerSpec } from '../config/swagger';

export const serveSwaggerDocs = swaggerUi.serve;
export const setupSwaggerDocs = swaggerUi.setup(swaggerSpec, {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'MR Engineers API Documentation',
});

export const serveSwaggerJson = (req: Request, res: Response) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
};
