import type { NextFunction, Request, Response } from 'express';
import client from 'prom-client';

// Create a Registry to register the metrics
export const metricsRegister = new client.Registry();

// Add a default label which is added to all metrics
metricsRegister.setDefaultLabels({
  app: 'mrengineer-backend',
});

// Enable the collection of default metrics (process, heap, event loop, etc.)
client.collectDefaultMetrics({ register: metricsRegister });

// Create a Histogram metric to track request duration
const httpRequestDuration = new client.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'] as const,
  buckets: [0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
});

metricsRegister.registerMetric(httpRequestDuration);

export const metricsMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const end = httpRequestDuration.startTimer();

  res.on('finish', () => {
    const route = (req.route && req.route.path) || req.path || 'unknown';
    end({
      method: req.method,
      route,
      status_code: res.statusCode,
    });
  });

  next();
};
