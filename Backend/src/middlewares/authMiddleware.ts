import { createClient, User } from '@supabase/supabase-js';
import { NextFunction, Request, Response } from 'express';

import { RoleType } from '@prisma/client';

import prisma from '@/lib/prisma';

import { createAppError } from '../utils/errorHandler';
import logger from '../utils/logger';

// Extend Request type to include user, roles and role checking functionality
declare global {
  namespace Express {
    interface Request {
      user?: any;
      userRoles?: RoleType[];
      hasRole?: (role: RoleType | RoleType[]) => boolean;
    }
  }
}

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_ANON_KEY!,
);

export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];

    if (!token) {
      return next(createAppError('Authorization token required', 401));
    }

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser(token);

    if (error || !user) {
      logger.warn('Invalid authentication attempt', { error });
      return next(createAppError('Invalid authentication token', 401));
    }

    // Fetch user data with their roles
    const userData = await prisma?.user.findUnique({
      where: { supabase_id: user.id },
      include: {
        user_roles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!userData) {
      return next(createAppError('User not found', 401));
    }

    // Extract role types from user_roles
    const userRoles = userData.user_roles.map((ur) => ur.role.type);

    // Add user, roles and hasRole helper to request object
    req.user = userData;
    req.userRoles = userRoles;
    req.hasRole = (roles) => {
      if (Array.isArray(roles)) {
        return roles.some((role) => userRoles.includes(role));
      }
      return userRoles.includes(roles);
    };

    next();
  } catch (error) {
    logger.error('Authentication failed', { error });
    next(createAppError('Authentication failed', 401));
  }
};

/**
 * Middleware to authorize users based on their roles
 * @param allowedRoles - Array of roles that are allowed to access the route
 */
export const authorizeRoles = (...allowedRoles: RoleType[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createAppError('Unauthorized', 401));
    }

    if (!req.userRoles || !req.hasRole) {
      return next(createAppError('User roles not available', 401));
    }

    if (!req.hasRole(allowedRoles)) {
      logger.warn('Insufficient permissions', {
        userId: req.user.id,
        requiredRoles: allowedRoles,
        userRoles: req.userRoles,
      });
      return next(createAppError('Insufficient permissions', 403));
    }

    next();
  };
};

/**
 * Middleware to check if user has a specific permission
 * This is a more granular approach than role-based authorization
 */
export const authorizePermission = (permissionKey: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createAppError('Unauthorized', 401));
    }

    try {
      // Check if user has the permission directly
      const userPermission = await prisma.userPermission.findFirst({
        where: {
          user_id: req.user.id,
          permission: {
            key: permissionKey,
          },
        },
      });

      if (userPermission) {
        return next();
      }

      // Check if user has the permission through their roles
      const rolePermission = await prisma.rolePermission.findFirst({
        where: {
          permission: {
            key: permissionKey,
          },
          role: {
            user_roles: {
              some: {
                user_id: req.user.id,
              },
            },
          },
        },
      });

      if (rolePermission) {
        return next();
      }

      logger.warn('Insufficient permissions', {
        userId: req.user.id,
        requiredPermission: permissionKey,
      });
      return next(createAppError('Insufficient permissions', 403));
    } catch (error) {
      logger.error('Permission check failed', { error });
      return next(createAppError('Permission check failed', 500));
    }
  };
};
