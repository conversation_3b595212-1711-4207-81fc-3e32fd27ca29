import { sendResponse, sendError } from '../apiResponse';
import type { Response } from 'express';

const createRes = () => {
  const res: Partial<Response> = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
  };
  return res as Response;
};

describe('apiResponse utilities', () => {
  it('sendResponse should send a known response type', () => {
    const res = createRes();
    sendResponse(res, 'METRICS_FETCHED', { data: { metrics: [] } });
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(
      expect.objectContaining({ success: true, data: { metrics: [] } }),
    );
  });

  it('sendError should format unknown errors', () => {
    const res = createRes();
    const err = new Error('Oops');
    // @ts-ignore augmenting
    err.statusCode = 500;
    sendError(res, err);
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith(
      expect.objectContaining({ error: true, message: 'Internal server error' }),
    );
  });
});
