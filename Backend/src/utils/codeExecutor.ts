import axios from 'axios';
import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';

import { COMPILER_CLIENT_ID, COMPILER_CLIENT_SECRET } from '../config';
import { PerformanceMonitor } from '../services/monitoring/performanceMonitor';
import logger from './logger';

const execAsync = promisify(exec);

/**
 * Interface for code execution options
 */
export interface CodeExecutionOptions {
  code: string;
  language: string;
  input?: string;
  timeLimit?: number;
  memoryLimit?: number;
  testMode?: boolean;
}

/**
 * Interface for code execution result
 */
export interface CodeExecutionResult {
  output: string;
  executionTime: number;
  memoryUsed: number;
  compilationTime?: number;
  cpuTime?: number;
  ioTime?: number;
  error?: string;
  status?: 'success' | 'error' | 'timeout' | 'memory_limit_exceeded';
}

/**
 * Language configuration for code execution
 */
interface LanguageConfig {
  extension: string;
  compileCommand?: string;
  runCommand: string;
  timeout: number;
  memoryLimit: number;
}

/**
 * Language configurations
 */
const languageConfigs: Record<string, LanguageConfig> = {
  javascript: {
    extension: 'js',
    runCommand: 'node {file}',
    timeout: 5000,
    memoryLimit: 512,
  },
  python: {
    extension: 'py',
    runCommand: 'python3 {file}',
    timeout: 5000,
    memoryLimit: 512,
  },
  java: {
    extension: 'java',
    compileCommand: 'javac {file}',
    runCommand: 'java -cp {dir} Main',
    timeout: 10000,
    memoryLimit: 1024,
  },
  cpp: {
    extension: 'cpp',
    compileCommand: 'g++ -std=c++17 {file} -o {dir}/a.out',
    runCommand: '{dir}/a.out',
    timeout: 5000,
    memoryLimit: 512,
  },
  c: {
    extension: 'c',
    compileCommand: 'gcc {file} -o {dir}/a.out',
    runCommand: '{dir}/a.out',
    timeout: 5000,
    memoryLimit: 512,
  },
  csharp: {
    extension: 'cs',
    compileCommand: 'csc {file}',
    runCommand: 'mono {dir}/Main.exe',
    timeout: 10000,
    memoryLimit: 1024,
  },
  go: {
    extension: 'go',
    runCommand: 'go run {file}',
    timeout: 5000,
    memoryLimit: 512,
  },
  ruby: {
    extension: 'rb',
    runCommand: 'ruby {file}',
    timeout: 5000,
    memoryLimit: 512,
  },
  rust: {
    extension: 'rs',
    compileCommand: 'rustc {file} -o {dir}/a.out',
    runCommand: '{dir}/a.out',
    timeout: 10000,
    memoryLimit: 512,
  },
  kotlin: {
    extension: 'kt',
    compileCommand: 'kotlinc {file} -include-runtime -d {dir}/Main.jar',
    runCommand: 'java -jar {dir}/Main.jar',
    timeout: 10000,
    memoryLimit: 1024,
  },
  typescript: {
    extension: 'ts',
    compileCommand: 'tsc {file}',
    runCommand: 'node {dir}/Main.js',
    timeout: 5000,
    memoryLimit: 512,
  },
};

/**
 * Execute code in a sandbox environment
 * @param options - Code execution options
 * @returns Code execution result
 */
export async function executeCode(
  options: CodeExecutionOptions,
): Promise<CodeExecutionResult> {
  const startTime = Date.now();
  try {
    logger.info(`Executing ${options.language} code`);

    // In test mode, simulate execution with random metrics
    if (options.testMode) {
      return simulateExecution(options);
    }

    // In production, use the external code execution service
    if (COMPILER_CLIENT_ID && COMPILER_CLIENT_SECRET) {
      return await executeWithExternalService(options);
    }

    // Fallback to local execution (not recommended for production)
    return await executeLocally(options);
  } catch (error) {
    logger.error('Error executing code:', error);
    return {
      output: '',
      executionTime: 0,
      memoryUsed: 0,
      status: 'error',
      error: (error as Error).message,
    };
  } finally {
    const duration = Date.now() - startTime;
    PerformanceMonitor.trackRequest('POST', '/execute-code', duration, 200);
  }
}

/**
 * Simulate code execution for testing
 */
function simulateExecution(options: CodeExecutionOptions): CodeExecutionResult {
  // Simulate compilation time (higher for compiled languages)
  const compilationTime = [
    'java',
    'cpp',
    'c',
    'csharp',
    'rust',
    'kotlin',
  ].includes(options.language)
    ? Math.floor(Math.random() * 200) + 50
    : Math.floor(Math.random() * 20);

  // Simulate execution time
  const executionTime = Math.floor(Math.random() * 500) + 10;

  // Simulate memory usage
  const memoryUsed = Math.floor(Math.random() * 10000) + 1000;

  // Simulate CPU time (slightly less than execution time)
  const cpuTime = Math.floor(executionTime * 0.8);

  // Simulate I/O time (the rest of execution time)
  const ioTime = executionTime - cpuTime;

  // Check if time limit is exceeded
  if (options.timeLimit && executionTime > options.timeLimit) {
    return {
      output: 'Time limit exceeded',
      executionTime: options.timeLimit || 0,
      memoryUsed,
      status: 'timeout',
      error: 'Time limit exceeded',
    };
  }

  // Check if memory limit is exceeded
  if (options.memoryLimit && memoryUsed > options.memoryLimit * 1024) {
    return {
      output: 'Memory limit exceeded',
      executionTime,
      memoryUsed: options.memoryLimit * 1024,
      status: 'memory_limit_exceeded',
      error: 'Memory limit exceeded',
    };
  }

  // Simulate output
  const output = `Output for input: ${options.input || 'none'}`;

  return {
    output,
    executionTime,
    memoryUsed,
    compilationTime,
    cpuTime,
    ioTime,
    status: 'success',
  };
}

/**
 * Execute code using an external service (JDoodle)
 */
async function executeWithExternalService(
  options: CodeExecutionOptions,
): Promise<CodeExecutionResult> {
  const apiUrl = 'https://api.jdoodle.com/v1/execute';

  try {
    const response = await axios.post(apiUrl, {
      script: options.code,
      language: options.language,
      versionIndex: options.language === 'cpp' ? '5' : '0', // Use C++17 for cpp
      stdin: options.input || '',
      clientId: COMPILER_CLIENT_ID,
      clientSecret: COMPILER_CLIENT_SECRET,
    });

    const { output, memory, cpuTime } = response.data;

    // Convert memory from KB to bytes
    const memoryUsed = (memory || 0) * 1024;

    // Convert CPU time from seconds to milliseconds
    const executionTime = (cpuTime || 0) * 1000;

    return {
      output,
      executionTime,
      memoryUsed,
      cpuTime: executionTime,
      status: 'success',
    };
  } catch (error) {
    logger.error('Error executing code with external service:', error);
    return {
      output: '',
      executionTime: 0,
      memoryUsed: 0,
      status: 'error',
      error: (error as Error).message,
    };
  }
}

/**
 * Execute code locally (not recommended for production)
 */
async function executeLocally(
  options: CodeExecutionOptions,
): Promise<CodeExecutionResult> {
  const config = languageConfigs[options.language];
  if (!config) {
    throw new Error(`Unsupported language: ${options.language}`);
  }

  // Create a temporary directory for the code
  const tempDir = path.join(process.cwd(), 'temp', uuidv4());
  fs.mkdirSync(tempDir, { recursive: true });

  try {
    // Create the source file
    const fileName = options.language === 'java' ? 'Main' : 'main';
    const filePath = path.join(tempDir, `${fileName}.${config.extension}`);
    fs.writeFileSync(filePath, options.code);

    // Create input file if input is provided
    if (options.input) {
      const inputPath = path.join(tempDir, 'input.txt');
      fs.writeFileSync(inputPath, options.input);
    }

    // Compile the code if necessary
    let compilationTime = 0;
    if (config.compileCommand) {
      const compileStart = Date.now();
      const compileCommand = config.compileCommand
        .replace('{file}', filePath)
        .replace('{dir}', tempDir);

      try {
        await execAsync(compileCommand, { timeout: 10000 });
      } catch (error) {
        return {
          output: (error as any).stderr || 'Compilation error',
          executionTime: 0,
          memoryUsed: 0,
          status: 'error',
          error: 'Compilation error',
        };
      }

      compilationTime = Date.now() - compileStart;
    }

    // Run the code
    const runCommand = config.runCommand
      .replace('{file}', filePath)
      .replace('{dir}', tempDir);

    const inputRedirect = options.input
      ? ` < ${path.join(tempDir, 'input.txt')}`
      : '';

    const timeLimit = options.timeLimit || config.timeout;

    const runStart = Date.now();
    try {
      const { stdout, stderr } = await execAsync(
        `${runCommand}${inputRedirect}`,
        { timeout: timeLimit },
      );

      const executionTime = Date.now() - runStart;

      // Estimate memory usage (this is not accurate)
      const memoryUsed =
        Math.min(
          options.memoryLimit || config.memoryLimit,
          Math.floor(Math.random() * 100) + 50,
        ) * 1024;

      return {
        output: stdout || stderr,
        executionTime,
        memoryUsed,
        compilationTime,
        cpuTime: executionTime * 0.8,
        ioTime: executionTime * 0.2,
        status: 'success',
      };
    } catch (error: any) {
      if (error.killed) {
        return {
          output: 'Time limit exceeded',
          executionTime: timeLimit,
          memoryUsed: 0,
          status: 'timeout',
          error: 'Time limit exceeded',
        };
      }

      return {
        output: error.stderr || 'Runtime error',
        executionTime: Date.now() - runStart,
        memoryUsed: 0,
        status: 'error',
        error: 'Runtime error',
      };
    }
  } finally {
    // Clean up temporary files
    try {
      fs.rmSync(tempDir, { recursive: true, force: true });
    } catch (error) {
      logger.error('Error cleaning up temporary files:', error);
    }
  }
}
