import fs from 'fs';
import os from 'os';
import path from 'path';
import winston, { format } from 'winston';
import type TransportStream from 'winston-transport';

import { NODE_ENV } from '../config';
import { requestContext } from './requestContext';

// Determine base directory for logs
const isServerless = !!process.env.VERCEL;
const baseDir = isServerless
  ? path.join(os.tmpdir(), 'logs')
  : path.join(process.cwd(), 'logs');

// Ensure local log directory exists
if (!isServerless && !fs.existsSync(baseDir)) {
  fs.mkdirSync(baseDir, { recursive: true });
}

// Define file transports
const fileTransports: TransportStream[] = [
  new winston.transports.File({
    filename: path.join(baseDir, 'error.log'),
    level: 'error',
  }),
  new winston.transports.File({
    filename: path.join(baseDir, 'combined.log'),
    level: 'info',
  }),
];

// Define console transport
const consoleTransport: TransportStream = new winston.transports.Console({
  format: format.combine(format.colorize(), format.simple()),
});

// Custom format to inject correlation data
const correlationFormat = format((info) => {
  const store = requestContext.getStore();
  if (store) {
    info.requestId = store.requestId;
    if (store.userId) info.userId = store.userId;
  }
  return info;
});

// Choose transports based on environment
const transports: TransportStream[] =
  NODE_ENV === 'production'
    ? isServerless
      ? [consoleTransport]
      : [...fileTransports, consoleTransport]
    : [consoleTransport, ...fileTransports];

// Create logger
const logger = winston.createLogger({
  level: NODE_ENV === 'production' ? 'info' : 'debug',
  format: format.combine(
    format.timestamp(),
    correlationFormat(),
    format.json(),
  ),
  defaultMeta: {
    service: 'backend',
    env: NODE_ENV,
  },
  transports,
});

export default logger;
