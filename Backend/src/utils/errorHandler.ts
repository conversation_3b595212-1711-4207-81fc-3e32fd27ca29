import { ErrorRequestHandler, Request, Response } from 'express';

import { sendError } from './apiResponse';
import logger from './logger';

export interface AppError extends Error {
  statusCode: number;
  details?: Record<string, unknown>;
  validationErrors?: Array<{
    field: string;
    message: string;
    type?: string;
    context?: Record<string, any>;
  }>;
  name: string;
}

export const errorHandler: ErrorRequestHandler = (
  err,
  req: Request,
  res: Response,
) => {
  // Log the error details
  logger.error(err.message, {
    stack: err.stack,
    path: req.path,
    method: req.method,
    status: err.statusCode || 500,
    details: err.details ? JSON.stringify(err.details) : undefined,
  });

  // Handle validation errors with more details
  if (err.statusCode === 400 && err.details?.errors) {
    return sendError(res, {
      message: err.message,
      statusCode: err.statusCode,
      details: err.details,
      name: 'ValidationError',
      validationErrors: err.details.errors,
    } as AppError);
  }

  // Handle other known errors
  if (err.statusCode && err.statusCode < 500) {
    return sendError(res, {
      message: err.message,
      statusCode: err.statusCode,
      details: err.details,
      name: err.name || 'AppError',
    } as AppError);
  }

  // Handle unknown errors
  sendError(res, {
    message: 'Internal server error',
    statusCode: 500,
    name: 'InternalServerError',
  } as AppError);
};

export const createAppError = (
  message: string,
  statusCode: number,
  details?: Record<string, unknown>,
): AppError => {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.details = details;
  error.name = 'AppError';
  return error;
};
