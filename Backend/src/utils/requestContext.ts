import { AsyncLocalStorage } from 'node:async_hooks';
import type { Request, Response, NextFunction } from 'express';
import { randomUUID } from 'node:crypto';

interface ContextStore {
  requestId: string;
  userId?: string;
}

export const requestContext = new AsyncLocalStorage<ContextStore>();

export const requestContextMiddleware = (
  req: Request,
  _res: Response,
  next: NextFunction,
) => {
  const requestId = (req.headers['x-request-id'] as string) || randomUUID();

  const userId = (req as any).user?.id as string | undefined;

  requestContext.run({ requestId, userId }, () => {
    // Also expose on req for downstream use
    (req as any).requestId = requestId;
    next();
  });
};
