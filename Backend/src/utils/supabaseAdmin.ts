/**
 * @file supabaseAdmin.ts
 * @description Utility functions for Supabase admin operations
 */
import { createClient } from '@supabase/supabase-js';

import logger from './logger';

// Create a Supabase client with admin privileges
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  },
);

/**
 * Validates if a string is a valid UUID
 *
 * @param id - The string to validate
 * @returns True if the string is a valid UUID, false otherwise
 */
const isValidUUID = (id: string): boolean => {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
};

/**
 * Update a user's app_metadata in Supabase with their roles
 *
 * @param supabaseId - The Supabase user ID
 * @param roles - Array of role types (e.g., ['ADMIN', 'USER'])
 * @returns Success status and error message if any
 */
export const updateUserRolesInSupabase = async (
  supabaseId: string,
  roles: string[],
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Validate the supabaseId is a valid UUID
    if (!supabaseId || !isValidUUID(supabaseId)) {
      const errorMsg = `Invalid Supabase ID format: ${supabaseId}`;
      logger.error(errorMsg);
      return { success: false, error: errorMsg };
    }

    // Validate roles array
    if (!Array.isArray(roles) || roles.length === 0) {
      const errorMsg = 'Roles must be a non-empty array';
      logger.error(errorMsg);
      return { success: false, error: errorMsg };
    }

    // Update the user's app_metadata with roles
    const { error } = await supabaseAdmin.auth.admin.updateUserById(
      supabaseId,
      {
        app_metadata: { roles },
      },
    );

    if (error) {
      logger.error('Error updating user roles in Supabase:', error);
      return { success: false, error: error.message };
    }

    logger.info(
      `Updated roles for user ${supabaseId} in Supabase: ${roles.join(', ')}`,
    );
    return { success: true };
  } catch (error: any) {
    logger.error('Unexpected error updating user roles in Supabase:', error);
    return { success: false, error: error.message };
  }
};

export default supabaseAdmin;
