import { Response } from 'express';

import logger from './logger';

/* eslint-disable @typescript-eslint/no-explicit-any */

type ResponseType =
  | 'TOPICS_FETCHED'
  | 'TOPIC_NOT_FOUND'
  | 'ROADMAP_FEATURED_UPDATED'
  | 'ROADMAP_FEATURED_TOGGLED'
  | 'ROADMAPS_STATUS_UPDATED'
  | 'ARTICLE_FETCHED'
  | 'ARTICLE_NOT_FOUND'
  | 'ARTICLES_FETCHED'
  | 'QUIZ_FETCHED'
  | 'QUIZ_NOT_FOUND'
  | 'QUIZ_SUBMITTED'
  | 'QUIZ_PASSED'
  | 'QUIZ_FAILED'
  | 'SUBJECTS_FETCHED'
  | 'TOPICS_NOT_FOUND'
  | 'SUBJECT_NOT_FOUND'
  | 'USER_NOT_CREATED'
  | 'PROFILE_FETCHED'
  | 'PROGRESS_FETCHED'
  | 'USER_CREATED'
  | 'USER_UPDATED'
  | 'USERNAME_CHECKED'
  | 'INVALID_PAGE_NUMBER'
  | 'ROADMAPS_FETCHED'
  | 'MAIN_CONCEPTS_FETCHED'
  | 'ROADMAP_NOT_FOUND'
  | 'ROADMAP_ENROLLED'
  | 'INVALID_ROADMAP_ID'
  | 'ROADMAP_ALREADY_ENROLLED'
  | 'USERS_FETCHED'
  | 'USER_ROLE_UPDATED'
  | 'ROLES_FETCHED'
  | 'ROLE_FETCHED'
  | 'ROLE_NOT_FOUND'
  | 'PENDING_CONTENT_FETCHED'
  | 'CONTENT_MODERATED'
  | 'CONFIG_UPDATED'
  | 'CONFIGS_FETCHED'
  | 'RESOURCES_ALLOCATED'
  | 'REPORT_GENERATED'
  | 'AUDIT_LOGS_FETCHED'
  | 'FORUMS_FETCHED'
  | 'FORUM_FETCHED'
  | 'FORUM_NOT_FOUND'
  | 'INVALID_PAYLOAD'
  | 'FORUM_CREATED'
  | 'FORUM_UPDATED'
  | 'FORUM_DELETED'
  | 'COURSES_FETCHED'
  | 'COURSE_FETCHED'
  | 'COURSE_NOT_FOUND'
  | 'COURSE_ENROLLED'
  | 'COURSE_ALREADY_ENROLLED'
  | 'USER_NOT_FOUND'
  | 'JOBS_FETCHED'
  | 'JOB_FETCHED'
  | 'JOB_NOT_FOUND'
  | 'JOB_CREATED'
  | 'JOB_UPDATED'
  | 'JOB_DELETED'
  | 'CHALLENGES_FETCHED'
  | 'CHALLENGE_FETCHED'
  | 'CHALLENGE_NOT_FOUND'
  | 'CHALLENGE_CREATED'
  | 'CHALLENGE_UPDATED'
  | 'CHALLENGE_SUBMITTED'
  | 'LEADERBOARD_FETCHED'
  | 'ROADMAP_FETCHED'
  | 'ROADMAP_CREATED'
  | 'SUBJECT_ORDER_UPDATED'
  | 'ROADMAP_UPDATED'
  | 'ANALYTICS_FETCHED'
  | 'USER_ANALYTICS_FETCHED'
  | 'PLATFORM_ANALYTICS_FETCHED'
  | 'REPORT_FETCHED'
  | 'METRICS_FETCHED'
  | 'ROLE_CREATED'
  | 'ROLE_UPDATED'
  | 'ROLE_DELETED'
  | 'ROLE_HIERARCHY_FETCHED'
  | 'PERMISSION_CREATED'
  | 'PERMISSION_UPDATED'
  | 'PERMISSION_DELETED'
  | 'ROLE_ASSIGNED'
  | 'ROLE_REMOVED'
  | 'PERMISSION_CHECKED'
  | 'TICKET_STATUS_UPDATED'
  | 'FEATURE_REQUEST_VOTED'
  | 'HELP_ARTICLES_FETCHED'
  | 'CACHE_HIT'
  | 'CACHE_SET'
  | 'CACHE_CLEARED'
  | 'ARTICLE_UPDATED'
  | 'BATTLES_FETCHED'
  | 'BATTLE_FETCHED'
  | 'BATTLE_NOT_FOUND'
  | 'BATTLE_CREATED'
  | 'BATTLE_UPDATED'
  | 'BATTLE_DELETED'
  | 'BATTLE_JOINED'
  | 'ANSWER_SUBMITTED'
  | 'CHATS_FETCHED'
  | 'CHAT_FETCHED'
  | 'CHAT_NOT_FOUND'
  | 'CHAT_CREATED'
  | 'CHAT_MESSAGE_SENT'
  | 'CHAT_DELETED'
  | 'MAIN_CONCEPT_CREATED'
  | 'MAIN_CONCEPT_UPDATED'
  | 'MAIN_CONCEPT_DELETED'
  | 'MAIN_CONCEPT_NOT_FOUND'
  | 'MAIN_CONCEPT_FETCHED'
  | 'RESOURCES_FETCHED'
  | 'BOOKS_FETCHED'
  | 'PREDICTED'
  | 'QUESTIONS_FETCHED'
  | 'QUESTION_CREATED'
  | 'QUESTION_UPDATED'
  | 'QUESTION_DELETED'
  | 'QUESTIONS_SUBMITTED'
  | 'QUIZ_CREATED'
  | 'QUIZ_UPDATED'
  | 'QUIZ_DELETED'
  | 'USER_PROGRESS_FETCHED'
  | 'TOPIC_ADDED'
  | 'TOPIC_UPDATED'
  | 'TOPIC_DELETED'
  | 'RESOURCE_FETCHED'
  | 'SUBJECTS_CREATED'
  | 'ARTICLE_CREATED'
  | 'SUBJECT_UPDATED'
  | 'SUBJECT_DELETED'
  | 'SUBJECTS_UPDATED'
  | 'SUBJECTS_DELETED'
  | 'ARTICLE_DELETED'
  | 'INTERVIEW_QUESTIONS_FETCHED'
  | 'INTERVIEW_QUESTION_CREATED'
  | 'INTERVIEW_QUESTION_UPDATED'
  | 'RESOURCE_CREATED'
  | 'RESOURCE_DETAILS_FETCHED'
  | 'UNAUTHORIZED'
  | 'FORBIDDEN'
  | 'NOT_FOUND'
  | 'ROADMAP_DELETED'
  | 'ROADMAP_CATEGORIES_FETCHED'
  | 'SUBJECT_CREATED'
  | 'TICKET_CREATED'
  | 'TICKET_RESPONSE_ADDED'
  | 'BUG_REPORT_CREATED'
  | 'FEATURE_REQUEST_CREATED'
  | 'HELP_ARTICLE_CREATED'
  | 'BATTLE_ZONE_STATISTICS_FETCHED'
  | 'USERNAME_AVAILABILITY_CHECKED'
  | 'ROADMAP_REMOVED'
  | 'PROGRESS_UPDATED'
  | 'ROADMAP_LIKED'
  | 'ROADMAP_UNLIKED'
  | 'ROADMAP_BOOKMARKED'
  | 'ROADMAP_UNBOOKMARKED'
  | 'SOMETHING_WRONG'
  | 'MODERATION_QUEUE_FETCHED'
  | 'AUDIT_LOGS_FETCHED'
  | 'CONTENT_MODERATED'
  | 'DASHBOARD_STATS_FETCHED'
  | 'RECENT_ACTIVITIES_FETCHED'
  | 'LEARNING_PROGRESS_FETCHED'
  | 'ACHIEVEMENTS_FETCHED'
  | 'STREAK_UPDATED'
  | 'STREAK_STATS_FETCHED'
  | 'WEEKLY_ACTIVITY_FETCHED'
  | 'COMMENTS_FETCHED'
  | 'COMMENT_ADDED'
  | 'COMMENT_LIKED'
  | 'COMMENT_UNLIKED'
  | 'BATTLE_STATUS_UPDATED'
  | 'BATTLE_ARCHIVED'
  | 'PLATFORM_STATS_FETCHED'
  | 'COLLEGES_FETCHED'
  | 'ACTIVITIES_FETCHED'
  | 'ACTIVITY_FETCHED'
  | 'ACTIVITY_CREATED'
  | 'ACTIVITY_DELETED'
  | 'ACHIEVEMENT_FETCHED'
  | 'ACHIEVEMENT_CREATED'
  | 'ACHIEVEMENT_DELETED'
  | 'DISCUSSIONS_FETCHED'
  | 'DISCUSSION_CREATED'
  | 'DISCUSSION_NOT_FOUND'
  | 'DISCUSSION_FETCHED'
  | 'INVALID_INPUT'
  | 'DISCUSSION_UPDATED'
  | 'DISCUSSION_DELETED'
  | 'DISCUSSION_UPVOTED'
  | 'DISCUSSION_DOWNVOTED'
  | 'FILTER_PRESET_SAVED'
  | 'FILTER_PRESETS_FETCHED'
  | 'FILTER_PRESET_DELETED'
  | 'SOLUTION_SHARED'
  | 'SHARED_SOLUTION_FETCHED'
  | 'SHARED_SOLUTION_NOT_FOUND'
  | 'SHARED_SOLUTION_EXPIRED'
  | 'SHARED_SOLUTION_PRIVATE'
  | 'SHARED_SOLUTION_METADATA_FETCHED'
  | 'VIEW_STATISTICS_FETCHED'
  | 'SHARED_SOLUTION_UPDATED'
  | 'SHARED_SOLUTION_DELETED'
  | 'SUBMISSION_NOT_FOUND'
  | 'ACHIEVEMENTS_FETCHED'
  | 'ACHIEVEMENT_FETCHED'
  | 'ACHIEVEMENT_NOT_FOUND'
  | 'ACHIEVEMENT_CREATED'
  | 'ACHIEVEMENT_UPDATED'
  | 'USER_ACHIEVEMENTS_FETCHED'
  | 'ACHIEVEMENT_NOTIFICATIONS_FETCHED'
  | 'NOTIFICATIONS_MARKED_AS_READ'
  | 'ACHIEVEMENTS_CHECKED'
  | 'ACHIEVEMENT_AWARDED'
  | 'ACHIEVEMENT_STATS_FETCHED'
  | 'POPULAR_ACHIEVEMENTS_FETCHED'
  | 'RAREST_ACHIEVEMENTS_FETCHED'
  | 'CHALLENGE_METRICS_FETCHED'
  | 'SUBMISSION_METRICS_FETCHED'
  | 'OPTIMIZATION_SUGGESTIONS_GENERATED'
  | 'PERCENTILES_UPDATED'
  | 'METRICS_RECORDED'
  | 'ROADMAP_CHALLENGES_FETCHED'
  | 'CHALLENGE_ROADMAPS_FETCHED'
  | 'CHALLENGE_ADDED_TO_ROADMAP'
  | 'CHALLENGE_REMOVED_FROM_ROADMAP'
  | 'ROADMAP_CHALLENGE_UPDATED'
  | 'SUGGESTED_CHALLENGES_FETCHED'
  | 'ROADMAP_PROGRESS_UPDATED'
  | 'DISCUSSIONS_FETCHED'
  | 'DISCUSSION_FETCHED'
  | 'DISCUSSION_CREATED'
  | 'DISCUSSION_UPDATED'
  | 'DISCUSSION_DELETED'
  | 'DISCUSSION_VOTED'
  | 'DISCUSSION_FLAGGED'
  | 'DISCUSSION_MODERATED'
  | 'FLAGGED_DISCUSSIONS_FETCHED'
  | 'DISCUSSION_NOTIFICATIONS_FETCHED'
  | 'DISCUSSION_STATS_FETCHED'
  | 'HINTS_FETCHED'
  | 'HINT_CREATED'
  | 'HINT_UPDATED'
  | 'HINT_DELETED'
  | 'HINT_UNLOCKED'
  | 'HINT_USAGE_STATS_FETCHED'
  | 'USER_HINT_USAGE_FETCHED'
  | 'ACHIEVEMENTS_FETCHED'
  | 'COLLECTIONS_FETCHED'
  | 'COLLECTION_FETCHED'
  | 'COLLECTION_CREATED'
  | 'COLLECTION_UPDATED'
  | 'COLLECTION_DELETED'
  | 'BOOKMARKS_ADDED_TO_COLLECTION'
  | 'BOOKMARKS_REMOVED_FROM_COLLECTION'
  | 'BOOKMARKS_MOVED'
  | 'CODE_EXECUTION_ERROR'
  | 'CODE_EXECUTED'
  | 'CODE_TESTED'
  | 'LANGUAGES_FETCHED'
  | 'LANGUAGE_LEADERBOARD_FETCHED'
  | 'TIME_BASED_LEADERBOARD_FETCHED'
  | 'WEEKLY_LEADERBOARD_FETCHED'
  | 'SUBMISSION_CHECKED'
  | 'STREAK_FETCHED'
  | 'ACTIVITY_TRACKED'
  | 'PROGRESS_STATS_FETCHED'
  | 'ACTIVITY_TIMELINE_FETCHED'
  | 'LEARNING_PATH_FETCHED'
  | 'BOOKMARKS_FETCHED'
  | 'CHALLENGE_BOOKMARKED'
  | 'CHALLENGE_UNBOOKMARKED'
  | 'CHALLENGES_BOOKMARKED'
  | 'CHALLENGES_UNBOOKMARKED'
  | 'BOOKMARK_UPDATED'
  | 'REPORTS_FETCHED'
  | 'REPORT_CREATED'
  | 'REPORT_UPDATED'
  | 'REPORT_DELETED'
  | 'REPORT_GENERATED'
  | 'DIMENSIONS_FETCHED'
  | 'METRICS_FETCHED'
  | 'CONFIG_UPDATED'
  | 'CONFIG_FETCHED'
  | 'BATTLE_RULES_FETCHED'
  | 'BATTLE_RULE_FETCHED'
  | 'BATTLE_RULE_CREATED'
  | 'BATTLE_RULE_UPDATED'
  | 'BATTLE_RULE_DELETED'
  | 'BATTLE_RULES_ASSOCIATED';

interface ResponseConfig {
  status: number;
  success: boolean;
  message: string;
}

const RESPONSE_MESSAGES: Record<ResponseType, ResponseConfig> = {
  BATTLE_RULES_ASSOCIATED: {
    status: 200,
    success: true,
    message: 'Battle rules associated successfully',
  },
  REPORT_CREATED: {
    status: 201,
    success: true,
    message: 'Report created successfully',
  },
  REPORT_UPDATED: {
    status: 200,
    success: true,
    message: 'Report updated successfully',
  },
  REPORT_DELETED: {
    status: 200,
    success: true,
    message: 'Report deleted successfully',
  },
  REPORT_GENERATED: {
    status: 200,
    success: true,
    message: 'Report generated successfully',
  },
  DIMENSIONS_FETCHED: {
    status: 200,
    success: true,
    message: 'Dimensions retrieved successfully',
  },
  METRICS_FETCHED: {
    status: 200,
    success: true,
    message: 'Metrics retrieved successfully',
  },
  CONFIG_UPDATED: {
    status: 200,
    success: true,
    message: 'Configuration updated successfully',
  },
  CONFIG_FETCHED: {
    status: 200,
    success: true,
    message: 'Configuration retrieved successfully',
  },
  ROLES_FETCHED: {
    status: 200,
    success: true,
    message: 'Roles retrieved successfully',
  },
  ROLE_FETCHED: {
    status: 200,
    success: true,
    message: 'Role retrieved successfully',
  },
  ROLE_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Role not found',
  },
  BATTLE_ZONE_STATISTICS_FETCHED: {
    status: 200,
    success: true,
    message: 'Battle zone statistics retrieved successfully',
  },
  TOPICS_FETCHED: {
    status: 200,
    success: true,
    message: 'Unpublished topics retrieved successfully',
  },
  TOPIC_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Topic not found',
  },
  ARTICLE_FETCHED: {
    status: 200,
    success: true,
    message: 'Article retrieved successfully',
  },
  ARTICLE_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Article not found',
  },
  ARTICLES_FETCHED: {
    status: 200,
    success: true,
    message: 'Articles retrieved successfully',
  },
  QUIZ_FETCHED: {
    status: 200,
    success: true,
    message: 'Quiz retrieved successfully',
  },
  QUIZ_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Quiz not found',
  },
  QUIZ_SUBMITTED: {
    status: 200,
    success: true,
    message: 'Quiz submitted successfully',
  },
  QUIZ_PASSED: {
    status: 200,
    success: true,
    message: 'Quiz passed!',
  },
  QUIZ_FAILED: {
    status: 200,
    success: false,
    message: 'Quiz failed',
  },
  SUBJECTS_FETCHED: {
    status: 200,
    success: true,
    message: 'Subjects retrieved successfully',
  },
  SUBJECT_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Subject not found',
  },
  TOPICS_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Topic not found',
  },
  USER_NOT_CREATED: {
    status: 200,
    success: false,
    message: 'User not created',
  },
  PROFILE_FETCHED: {
    status: 200,
    success: true,
    message: 'Profile retrieved successfully',
  },
  PROGRESS_FETCHED: {
    status: 200,
    success: true,
    message: 'Progress retrieved successfully',
  },
  USER_CREATED: {
    status: 201,
    success: true,
    message: 'User created successfully',
  },
  USER_UPDATED: {
    status: 200,
    success: true,
    message: 'User updated successfully',
  },
  USERNAME_CHECKED: {
    status: 200,
    success: true,
    message: 'Username checked successfully',
  },
  INVALID_PAGE_NUMBER: {
    status: 400,
    success: false,
    message: 'Invalid page number',
  },
  ROADMAPS_FETCHED: {
    status: 200,
    success: true,
    message: 'Roadmaps retrieved successfully',
  },
  MAIN_CONCEPTS_FETCHED: {
    status: 200,
    success: true,
    message: 'Main concepts retrieved successfully',
  },
  ROADMAP_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Roadmap not found',
  },
  ROADMAP_ENROLLED: {
    status: 200,
    success: true,
    message: 'Roadmap enrolled successfully',
  },
  INVALID_ROADMAP_ID: {
    status: 400,
    success: false,
    message: 'Invalid roadmap ID',
  },
  ROADMAP_ALREADY_ENROLLED: {
    status: 200,
    success: true,
    message: 'Roadmap already enrolled',
  },
  USERS_FETCHED: {
    status: 200,
    success: true,
    message: 'Users fetched successfully',
  },
  USER_ROLE_UPDATED: {
    status: 200,
    success: true,
    message: 'User role updated successfully',
  },
  PENDING_CONTENT_FETCHED: {
    status: 200,
    success: true,
    message: 'Pending content fetched successfully',
  },
  CONTENT_MODERATED: {
    status: 200,
    success: true,
    message: 'Content moderated successfully',
  },
  CONFIGS_FETCHED: {
    status: 200,
    success: true,
    message: 'Configurations fetched successfully',
  },
  RESOURCES_ALLOCATED: {
    status: 200,
    success: true,
    message: 'Resources allocated successfully',
  },
  AUDIT_LOGS_FETCHED: {
    status: 200,
    success: true,
    message: 'Audit logs fetched successfully',
  },
  FORUMS_FETCHED: {
    status: 200,
    success: true,
    message: 'Forums fetched successfully',
  },
  FORUM_FETCHED: {
    status: 200,
    success: true,
    message: 'Forum fetched successfully',
  },
  FORUM_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Forum not found',
  },
  INVALID_PAYLOAD: {
    status: 400,
    success: false,
    message: 'Invalid payload provided',
  },
  FORUM_CREATED: {
    status: 201,
    success: true,
    message: 'Forum created successfully',
  },
  FORUM_UPDATED: {
    status: 200,
    success: true,
    message: 'Forum updated successfully',
  },
  FORUM_DELETED: {
    status: 200,
    success: true,
    message: 'Forum deleted successfully',
  },
  COURSES_FETCHED: {
    status: 200,
    success: true,
    message: 'Courses retrieved successfully',
  },
  COURSE_FETCHED: {
    status: 200,
    success: true,
    message: 'Course retrieved successfully',
  },
  COURSE_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Course not found',
  },
  COURSE_ENROLLED: {
    status: 201,
    success: true,
    message: 'Enrolled in course successfully',
  },
  COURSE_ALREADY_ENROLLED: {
    status: 400,
    success: false,
    message: 'Already enrolled in this course',
  },
  USER_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'User not found',
  },
  JOBS_FETCHED: {
    status: 200,
    success: true,
    message: 'Jobs retrieved successfully',
  },
  JOB_FETCHED: {
    status: 200,
    success: true,
    message: 'Job retrieved successfully',
  },
  JOB_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Job not found',
  },
  JOB_CREATED: {
    status: 201,
    success: true,
    message: 'Job created successfully',
  },
  JOB_UPDATED: {
    status: 200,
    success: true,
    message: 'Job updated successfully',
  },
  JOB_DELETED: {
    status: 200,
    success: true,
    message: 'Job deleted successfully',
  },
  CHALLENGES_FETCHED: {
    status: 200,
    success: true,
    message: 'Challenges retrieved successfully',
  },
  CHALLENGE_FETCHED: {
    status: 200,
    success: true,
    message: 'Challenge retrieved successfully',
  },
  CHALLENGE_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Challenge not found',
  },
  CHALLENGE_CREATED: {
    status: 201,
    success: true,
    message: 'Challenge created successfully',
  },
  CHALLENGE_UPDATED: {
    status: 200,
    success: true,
    message: 'Challenge updated successfully',
  },
  CHALLENGE_SUBMITTED: {
    status: 200,
    success: true,
    message: 'Challenge submitted successfully',
  },
  LEADERBOARD_FETCHED: {
    status: 200,
    success: true,
    message: 'Leaderboard retrieved successfully',
  },
  ROADMAP_FETCHED: {
    status: 200,
    success: true,
    message: 'Roadmap retrieved successfully',
  },
  ROADMAP_CREATED: {
    status: 201,
    success: true,
    message: 'Roadmap created successfully',
  },
  SUBJECT_ORDER_UPDATED: {
    status: 200,
    success: true,
    message: 'Subject order updated successfully',
  },
  ROADMAP_UPDATED: {
    status: 200,
    success: true,
    message: 'Roadmap updated successfully',
  },
  ROADMAP_FEATURED_UPDATED: {
    status: 200,
    success: true,
    message: 'Roadmap featured status updated successfully',
  },
  ROADMAP_FEATURED_TOGGLED: {
    status: 200,
    success: true,
    message: 'Roadmap featured status toggled successfully',
  },
  ROADMAPS_STATUS_UPDATED: {
    status: 200,
    success: true,
    message: 'Roadmaps status updated successfully',
  },
  ANALYTICS_FETCHED: {
    status: 200,
    success: true,
    message: 'Analytics data retrieved successfully',
  },
  USER_ANALYTICS_FETCHED: {
    status: 200,
    success: true,
    message: 'User analytics retrieved successfully',
  },
  PLATFORM_ANALYTICS_FETCHED: {
    status: 200,
    success: true,
    message: 'Platform analytics retrieved successfully',
  },
  REPORT_FETCHED: {
    status: 200,
    success: true,
    message: 'Report data retrieved successfully',
  },
  ROLE_CREATED: {
    status: 201,
    success: true,
    message: 'Role created successfully',
  },
  ROLE_UPDATED: {
    status: 200,
    success: true,
    message: 'Role updated successfully',
  },
  ROLE_DELETED: {
    status: 200,
    success: true,
    message: 'Role deleted successfully',
  },
  ROLE_HIERARCHY_FETCHED: {
    status: 200,
    success: true,
    message: 'Role hierarchy fetched successfully',
  },
  PERMISSION_CREATED: {
    status: 201,
    success: true,
    message: 'Permission created successfully',
  },
  PERMISSION_UPDATED: {
    status: 200,
    success: true,
    message: 'Permission updated successfully',
  },
  PERMISSION_DELETED: {
    status: 200,
    success: true,
    message: 'Permission deleted successfully',
  },
  ROLE_ASSIGNED: {
    status: 200,
    success: true,
    message: 'Role assigned successfully',
  },
  ROLE_REMOVED: {
    status: 200,
    success: true,
    message: 'Role removed successfully',
  },
  PERMISSION_CHECKED: {
    status: 200,
    success: true,
    message: 'Permission checked successfully',
  },
  TICKET_STATUS_UPDATED: {
    status: 200,
    success: true,
    message: 'Ticket status updated successfully',
  },
  FEATURE_REQUEST_VOTED: {
    status: 200,
    success: true,
    message: 'Feature request voted successfully',
  },
  HELP_ARTICLES_FETCHED: {
    status: 200,
    success: true,
    message: 'Help articles fetched successfully',
  },
  CACHE_HIT: {
    status: 200,
    success: true,
    message: 'Cache hit: Data retrieved from cache',
  },
  CACHE_SET: {
    status: 200,
    success: true,
    message: 'Cache set: Data stored successfully',
  },
  CACHE_CLEARED: {
    status: 200,
    success: true,
    message: 'Cache cleared successfully',
  },
  ARTICLE_UPDATED: {
    status: 200,
    success: true,
    message: 'Article updated successfully',
  },
  BATTLES_FETCHED: {
    status: 200,
    success: true,
    message: 'Battles retrieved successfully',
  },
  BATTLE_FETCHED: {
    status: 200,
    success: true,
    message: 'Battle retrieved successfully',
  },
  BATTLE_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Battle not found',
  },
  BATTLE_CREATED: {
    status: 201,
    success: true,
    message: 'Battle created successfully',
  },
  BATTLE_UPDATED: {
    status: 200,
    success: true,
    message: 'Battle updated successfully',
  },
  BATTLE_DELETED: {
    status: 200,
    success: true,
    message: 'Battle deleted successfully',
  },
  BATTLE_JOINED: {
    status: 200,
    success: true,
    message: 'Battle joined successfully',
  },
  ANSWER_SUBMITTED: {
    status: 200,
    success: true,
    message: 'Answer submitted successfully',
  },
  CHATS_FETCHED: {
    status: 200,
    success: true,
    message: 'Chats retrieved successfully',
  },
  CHAT_FETCHED: {
    status: 200,
    success: true,
    message: 'Chat retrieved successfully',
  },
  CHAT_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Chat not found',
  },
  CHAT_CREATED: {
    status: 201,
    success: true,
    message: 'Chat created successfully',
  },
  CHAT_MESSAGE_SENT: {
    status: 201,
    success: true,
    message: 'Message sent successfully',
  },
  CHAT_DELETED: {
    status: 200,
    success: true,
    message: 'Chat deleted successfully',
  },
  MAIN_CONCEPT_CREATED: {
    status: 201,
    success: true,
    message: 'Main concept created successfully',
  },
  MAIN_CONCEPT_UPDATED: {
    status: 200,
    success: true,
    message: 'Main concept updated successfully',
  },
  MAIN_CONCEPT_DELETED: {
    status: 200,
    success: true,
    message: 'Main concept deleted successfully',
  },
  MAIN_CONCEPT_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Main concept not found',
  },
  MAIN_CONCEPT_FETCHED: {
    status: 200,
    success: true,
    message: 'Main concept fetched successfully',
  },
  RESOURCES_FETCHED: {
    status: 200,
    success: true,
    message: 'Resources fetched successfully',
  },
  BOOKS_FETCHED: {
    status: 200,
    success: true,
    message: 'Books fetched successfully',
  },

  PREDICTED: {
    status: 200,
    success: true,
    message: 'Prediction made successfully',
  },
  QUESTIONS_FETCHED: {
    status: 200,
    success: true,
    message: 'Questions fetched successfully',
  },
  QUESTION_CREATED: {
    status: 201,
    success: true,
    message: 'Question created successfully',
  },
  QUESTION_UPDATED: {
    status: 200,
    success: true,
    message: 'Question updated successfully',
  },
  QUESTION_DELETED: {
    status: 200,
    success: true,
    message: 'Question deleted successfully',
  },
  QUESTIONS_SUBMITTED: {
    status: 200,
    success: true,
    message: 'Questions submitted successfully',
  },
  QUIZ_CREATED: {
    status: 201,
    success: true,
    message: 'Quiz created successfully',
  },
  QUIZ_UPDATED: {
    status: 200,
    success: true,
    message: 'Quiz updated successfully',
  },
  QUIZ_DELETED: {
    status: 200,
    success: true,
    message: 'Quiz deleted successfully',
  },
  USER_PROGRESS_FETCHED: {
    status: 200,
    success: true,
    message: 'User progress fetched successfully',
  },
  TOPIC_ADDED: {
    status: 201,
    success: true,
    message: 'Topic added successfully',
  },
  TOPIC_UPDATED: {
    status: 200,
    success: true,
    message: 'Topic updated successfully',
  },
  TOPIC_DELETED: {
    status: 200,
    success: true,
    message: 'Topic deleted successfully',
  },
  RESOURCE_FETCHED: {
    status: 200,
    success: true,
    message: 'Resource fetched successfully',
  },
  SUBJECTS_CREATED: {
    status: 201,
    success: true,
    message: 'Subjects created successfully',
  },
  SUBJECTS_UPDATED: {
    status: 200,
    success: true,
    message: 'Subjects updated successfully',
  },
  SUBJECTS_DELETED: {
    status: 200,
    success: true,
    message: 'Subjects deleted successfully',
  },
  ARTICLE_CREATED: {
    status: 201,
    success: true,
    message: 'Article created successfully',
  },
  ARTICLE_DELETED: {
    status: 200,
    success: true,
    message: 'Article deleted successfully',
  },
  INTERVIEW_QUESTIONS_FETCHED: {
    status: 200,
    success: true,
    message: 'Interview questions fetched successfully',
  },
  INTERVIEW_QUESTION_CREATED: {
    status: 201,
    success: true,
    message: 'Interview question created successfully',
  },
  INTERVIEW_QUESTION_UPDATED: {
    status: 200,
    success: true,
    message: 'Interview question updated successfully',
  },
  SUBJECT_UPDATED: {
    status: 200,
    success: true,
    message: 'Subject updated successfully',
  },
  SUBJECT_DELETED: {
    status: 200,
    success: true,
    message: 'Subject deleted successfully',
  },
  RESOURCE_CREATED: {
    status: 201,
    success: true,
    message: 'Resource created successfully',
  },
  RESOURCE_DETAILS_FETCHED: {
    status: 200,
    success: true,
    message: 'Resource details fetched successfully',
  },
  UNAUTHORIZED: {
    status: 401,
    success: false,
    message: 'Unauthorized',
  },
  FORBIDDEN: {
    status: 403,
    success: false,
    message: 'Forbidden',
  },
  NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Not found',
  },
  ROADMAP_DELETED: {
    status: 200,
    success: true,
    message: 'Roadmap deleted successfully',
  },
  ROADMAP_CATEGORIES_FETCHED: {
    status: 200,
    success: true,
    message: 'Roadmap categories fetched successfully',
  },
  SUBJECT_CREATED: {
    status: 201,
    success: true,
    message: 'Subject created successfully',
  },
  TICKET_CREATED: {
    status: 201,
    success: true,
    message: 'Ticket created successfully',
  },
  TICKET_RESPONSE_ADDED: {
    status: 200,
    success: true,
    message: 'Ticket response added successfully',
  },
  BUG_REPORT_CREATED: {
    status: 201,
    success: true,
    message: 'Bug report created successfully',
  },
  FEATURE_REQUEST_CREATED: {
    status: 201,
    success: true,
    message: 'Feature request created successfully',
  },
  HELP_ARTICLE_CREATED: {
    status: 201,
    success: true,
    message: 'Help article created successfully',
  },
  USERNAME_AVAILABILITY_CHECKED: {
    status: 200,
    success: true,
    message: 'Username availability checked successfully',
  },
  ROADMAP_REMOVED: {
    status: 200,
    success: true,
    message: 'Roadmap removed successfully',
  },
  PROGRESS_UPDATED: {
    status: 200,
    success: true,
    message: 'Progress updated successfully',
  },
  ROADMAP_LIKED: {
    status: 200,
    success: true,
    message: 'Roadmap liked successfully',
  },
  ROADMAP_UNLIKED: {
    status: 200,
    success: true,
    message: 'Roadmap unliked successfully',
  },
  ROADMAP_BOOKMARKED: {
    status: 200,
    success: true,
    message: 'Roadmap bookmarked successfully',
  },
  ROADMAP_UNBOOKMARKED: {
    status: 200,
    success: true,
    message: 'Roadmap unbookmarked successfully',
  },
  SOMETHING_WRONG: {
    status: 500,
    success: false,
    message: 'Something went wrong',
  },
  MODERATION_QUEUE_FETCHED: {
    status: 200,
    success: true,
    message: 'Moderation queue fetched successfully',
  },
  DASHBOARD_STATS_FETCHED: {
    status: 200,
    success: true,
    message: 'Dashboard stats fetched successfully',
  },
  RECENT_ACTIVITIES_FETCHED: {
    status: 200,
    success: true,
    message: 'Recent activities fetched successfully',
  },
  LEARNING_PROGRESS_FETCHED: {
    status: 200,
    success: true,
    message: 'Learning progress fetched successfully',
  },
  ACHIEVEMENTS_FETCHED: {
    status: 200,
    success: true,
    message: 'Achievements fetched successfully',
  },
  STREAK_UPDATED: {
    status: 200,
    success: true,
    message: 'Streak updated successfully',
  },
  STREAK_STATS_FETCHED: {
    status: 200,
    success: true,
    message: 'Streak stats fetched successfully',
  },
  WEEKLY_ACTIVITY_FETCHED: {
    status: 200,
    success: true,
    message: 'Weekly activity fetched successfully',
  },
  COMMENTS_FETCHED: {
    status: 200,
    success: true,
    message: 'Comments fetched successfully',
  },
  COMMENT_ADDED: {
    status: 200,
    success: true,
    message: 'Comment added successfully',
  },
  COMMENT_LIKED: {
    status: 200,
    success: true,
    message: 'Comment liked successfully',
  },
  COMMENT_UNLIKED: {
    status: 200,
    success: true,
    message: 'Comment unliked successfully',
  },
  BATTLE_STATUS_UPDATED: {
    status: 200,
    success: true,
    message: 'Battle status updated successfully',
  },
  BATTLE_ARCHIVED: {
    status: 200,
    success: true,
    message: 'Battle archived successfully',
  },
  PLATFORM_STATS_FETCHED: {
    status: 200,
    success: true,
    message: 'Platform statistics retrieved successfully',
  },
  COLLEGES_FETCHED: {
    status: 200,
    success: true,
    message: 'Colleges retrieved successfully',
  },
  ACTIVITIES_FETCHED: {
    status: 200,
    success: true,
    message: 'Activities fetched successfully',
  },
  ACTIVITY_FETCHED: {
    status: 200,
    success: true,
    message: 'Activity fetched successfully',
  },
  ACTIVITY_CREATED: {
    status: 201,
    success: true,
    message: 'Activity created successfully',
  },
  ACTIVITY_DELETED: {
    status: 200,
    success: true,
    message: 'Activity deleted successfully',
  },
  ACHIEVEMENT_FETCHED: {
    status: 200,
    success: true,
    message: 'Achievement fetched successfully',
  },
  ACHIEVEMENT_CREATED: {
    status: 201,
    success: true,
    message: 'Achievement created successfully',
  },
  ACHIEVEMENT_DELETED: {
    status: 200,
    success: true,
    message: 'Achievement deleted successfully',
  },
  DISCUSSIONS_FETCHED: {
    status: 200,
    success: true,
    message: 'Discussions fetched successfully',
  },
  DISCUSSION_CREATED: {
    status: 201,
    success: true,
    message: 'Discussion created successfully',
  },
  DISCUSSION_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Discussion not found',
  },
  DISCUSSION_FETCHED: {
    status: 200,
    success: true,
    message: 'Discussion fetched successfully',
  },
  INVALID_INPUT: {
    status: 400,
    success: false,
    message: 'Invalid input provided',
  },
  DISCUSSION_UPDATED: {
    status: 200,
    success: true,
    message: 'Discussion updated successfully',
  },
  DISCUSSION_DELETED: {
    status: 200,
    success: true,
    message: 'Discussion deleted successfully',
  },
  DISCUSSION_UPVOTED: {
    status: 200,
    success: true,
    message: 'Discussion upvoted successfully',
  },
  DISCUSSION_DOWNVOTED: {
    status: 200,
    success: true,
    message: 'Discussion downvoted successfully',
  },
  FILTER_PRESET_SAVED: {
    status: 200,
    success: true,
    message: 'Filter preset saved successfully',
  },
  FILTER_PRESETS_FETCHED: {
    status: 200,
    success: true,
    message: 'Filter presets fetched successfully',
  },
  FILTER_PRESET_DELETED: {
    status: 200,
    success: true,
    message: 'Filter preset deleted successfully',
  },
  SOLUTION_SHARED: {
    status: 201,
    success: true,
    message: 'Solution shared successfully',
  },
  SHARED_SOLUTION_FETCHED: {
    status: 200,
    success: true,
    message: 'Shared solution fetched successfully',
  },
  SHARED_SOLUTION_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Shared solution not found',
  },
  SHARED_SOLUTION_EXPIRED: {
    status: 410,
    success: false,
    message: 'This shared solution has expired',
  },
  SHARED_SOLUTION_PRIVATE: {
    status: 403,
    success: false,
    message: 'This shared solution is private',
  },
  SHARED_SOLUTION_METADATA_FETCHED: {
    status: 200,
    success: true,
    message: 'Shared solution metadata fetched successfully',
  },
  VIEW_STATISTICS_FETCHED: {
    status: 200,
    success: true,
    message: 'View statistics fetched successfully',
  },
  SHARED_SOLUTION_UPDATED: {
    status: 200,
    success: true,
    message: 'Shared solution updated successfully',
  },
  SHARED_SOLUTION_DELETED: {
    status: 200,
    success: true,
    message: 'Shared solution deleted successfully',
  },
  SUBMISSION_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Submission not found',
  },
  ACHIEVEMENT_NOT_FOUND: {
    status: 404,
    success: false,
    message: 'Achievement not found',
  },
  ACHIEVEMENT_UPDATED: {
    status: 200,
    success: true,
    message: 'Achievement updated successfully',
  },
  USER_ACHIEVEMENTS_FETCHED: {
    status: 200,
    success: true,
    message: 'User achievements fetched successfully',
  },
  ACHIEVEMENT_NOTIFICATIONS_FETCHED: {
    status: 200,
    success: true,
    message: 'Achievement notifications fetched successfully',
  },
  NOTIFICATIONS_MARKED_AS_READ: {
    status: 200,
    success: true,
    message: 'Notifications marked as read successfully',
  },
  ACHIEVEMENTS_CHECKED: {
    status: 200,
    success: true,
    message: 'Achievements checked successfully',
  },
  ACHIEVEMENT_AWARDED: {
    status: 200,
    success: true,
    message: 'Achievement awarded successfully',
  },
  ACHIEVEMENT_STATS_FETCHED: {
    status: 200,
    success: true,
    message: 'Achievement statistics fetched successfully',
  },
  POPULAR_ACHIEVEMENTS_FETCHED: {
    status: 200,
    success: true,
    message: 'Popular achievements fetched successfully',
  },
  RAREST_ACHIEVEMENTS_FETCHED: {
    status: 200,
    success: true,
    message: 'Rarest achievements fetched successfully',
  },
  CHALLENGE_METRICS_FETCHED: {
    status: 200,
    success: true,
    message: 'Challenge metrics fetched successfully',
  },
  SUBMISSION_METRICS_FETCHED: {
    status: 200,
    success: true,
    message: 'Submission metrics fetched successfully',
  },
  OPTIMIZATION_SUGGESTIONS_GENERATED: {
    status: 200,
    success: true,
    message: 'Optimization suggestions generated successfully',
  },
  PERCENTILES_UPDATED: {
    status: 200,
    success: true,
    message: 'Percentiles updated successfully',
  },
  METRICS_RECORDED: {
    status: 200,
    success: true,
    message: 'Metrics recorded successfully',
  },
  ROADMAP_CHALLENGES_FETCHED: {
    status: 200,
    success: true,
    message: 'Roadmap challenges fetched successfully',
  },
  CHALLENGE_ROADMAPS_FETCHED: {
    status: 200,
    success: true,
    message: 'Challenge roadmaps fetched successfully',
  },
  CHALLENGE_ADDED_TO_ROADMAP: {
    status: 201,
    success: true,
    message: 'Challenge added to roadmap successfully',
  },
  CHALLENGE_REMOVED_FROM_ROADMAP: {
    status: 200,
    success: true,
    message: 'Challenge removed from roadmap successfully',
  },
  ROADMAP_CHALLENGE_UPDATED: {
    status: 200,
    success: true,
    message: 'Roadmap challenge updated successfully',
  },
  SUGGESTED_CHALLENGES_FETCHED: {
    status: 200,
    success: true,
    message: 'Suggested challenges fetched successfully',
  },
  ROADMAP_PROGRESS_UPDATED: {
    status: 200,
    success: true,
    message: 'Roadmap progress updated successfully',
  },
  DISCUSSION_VOTED: {
    status: 200,
    success: true,
    message: 'Vote recorded successfully',
  },
  DISCUSSION_FLAGGED: {
    status: 200,
    success: true,
    message: 'Discussion flagged successfully',
  },
  DISCUSSION_MODERATED: {
    status: 200,
    success: true,
    message: 'Discussion moderated successfully',
  },
  FLAGGED_DISCUSSIONS_FETCHED: {
    status: 200,
    success: true,
    message: 'Flagged discussions fetched successfully',
  },
  DISCUSSION_NOTIFICATIONS_FETCHED: {
    status: 200,
    success: true,
    message: 'Discussion notifications fetched successfully',
  },
  DISCUSSION_STATS_FETCHED: {
    status: 200,
    success: true,
    message: 'Discussion statistics fetched successfully',
  },
  HINTS_FETCHED: {
    status: 200,
    success: true,
    message: 'Hints fetched successfully',
  },
  HINT_CREATED: {
    status: 201,
    success: true,
    message: 'Hint created successfully',
  },
  HINT_UPDATED: {
    status: 200,
    success: true,
    message: 'Hint updated successfully',
  },
  HINT_DELETED: {
    status: 200,
    success: true,
    message: 'Hint deleted successfully',
  },
  HINT_UNLOCKED: {
    status: 200,
    success: true,
    message: 'Hint unlocked successfully',
  },
  HINT_USAGE_STATS_FETCHED: {
    status: 200,
    success: true,
    message: 'Hint usage statistics fetched successfully',
  },
  USER_HINT_USAGE_FETCHED: {
    status: 200,
    success: true,
    message: 'User hint usage fetched successfully',
  },
  COLLECTIONS_FETCHED: {
    status: 200,
    success: true,
    message: 'Collections fetched successfully',
  },
  COLLECTION_FETCHED: {
    status: 200,
    success: true,
    message: 'Collection fetched successfully',
  },
  COLLECTION_CREATED: {
    status: 201,
    success: true,
    message: 'Collection created successfully',
  },
  COLLECTION_UPDATED: {
    status: 200,
    success: true,
    message: 'Collection updated successfully',
  },
  COLLECTION_DELETED: {
    status: 200,
    success: true,
    message: 'Collection deleted successfully',
  },
  BOOKMARKS_ADDED_TO_COLLECTION: {
    status: 200,
    success: true,
    message: 'Bookmarks added to collection successfully',
  },
  BOOKMARKS_REMOVED_FROM_COLLECTION: {
    status: 200,
    success: true,
    message: 'Bookmarks removed from collection successfully',
  },
  BOOKMARKS_MOVED: {
    status: 200,
    success: true,
    message: 'Bookmarks moved successfully',
  },
  CODE_EXECUTION_ERROR: {
    status: 400,
    success: false,
    message: 'Code execution error',
  },
  CODE_EXECUTED: {
    status: 200,
    success: true,
    message: 'Code executed successfully',
  },
  CODE_TESTED: {
    status: 200,
    success: true,
    message: 'Code tested successfully',
  },
  LANGUAGES_FETCHED: {
    status: 200,
    success: true,
    message: 'Languages fetched successfully',
  },
  LANGUAGE_LEADERBOARD_FETCHED: {
    status: 200,
    success: true,
    message: 'Language leaderboard fetched successfully',
  },
  TIME_BASED_LEADERBOARD_FETCHED: {
    status: 200,
    success: true,
    message: 'Time-based leaderboard fetched successfully',
  },
  WEEKLY_LEADERBOARD_FETCHED: {
    status: 200,
    success: true,
    message: 'Weekly leaderboard fetched successfully',
  },
  SUBMISSION_CHECKED: {
    status: 200,
    success: true,
    message: 'Submission checked successfully',
  },
  STREAK_FETCHED: {
    status: 200,
    success: true,
    message: 'Streak fetched successfully',
  },
  ACTIVITY_TRACKED: {
    status: 200,
    success: true,
    message: 'Activity tracked successfully',
  },
  PROGRESS_STATS_FETCHED: {
    status: 200,
    success: true,
    message: 'Progress stats fetched successfully',
  },
  ACTIVITY_TIMELINE_FETCHED: {
    status: 200,
    success: true,
    message: 'Activity timeline fetched successfully',
  },
  LEARNING_PATH_FETCHED: {
    status: 200,
    success: true,
    message: 'Learning path fetched successfully',
  },
  BOOKMARKS_FETCHED: {
    status: 200,
    success: true,
    message: 'Bookmarks fetched successfully',
  },
  CHALLENGE_BOOKMARKED: {
    status: 200,
    success: true,
    message: 'Challenge bookmarked successfully',
  },
  CHALLENGE_UNBOOKMARKED: {
    status: 200,
    success: true,
    message: 'Challenge unbookmarked successfully',
  },
  CHALLENGES_BOOKMARKED: {
    status: 200,
    success: true,
    message: 'Challenges bookmarked successfully',
  },
  CHALLENGES_UNBOOKMARKED: {
    status: 200,
    success: true,
    message: 'Challenges unbookmarked successfully',
  },
  BOOKMARK_UPDATED: {
    status: 200,
    success: true,
    message: 'Bookmark updated successfully',
  },
  REPORTS_FETCHED: {
    status: 200,
    success: true,
    message: 'Reports fetched successfully',
  },
  BATTLE_RULES_FETCHED: {
    status: 200,
    success: true,
    message: 'Battle rules fetched successfully',
  },
  BATTLE_RULE_FETCHED: {
    status: 200,
    success: true,
    message: 'Battle rule fetched successfully',
  },
  BATTLE_RULE_CREATED: {
    status: 201,
    success: true,
    message: 'Battle rule created successfully',
  },
  BATTLE_RULE_UPDATED: {
    status: 200,
    success: true,
    message: 'Battle rule updated successfully',
  },
  BATTLE_RULE_DELETED: {
    status: 200,
    success: true,
    message: 'Battle rule deleted successfully',
  },
};

export const sendResponse = (
  res: Response,
  type: ResponseType,
  options?: {
    data?: any;
    error?: any;
    meta?: any;
    toast?: boolean;
    message?: string; // Allow custom message override
  },
) => {
  const config = RESPONSE_MESSAGES[type];
  return res.status(config.status).json({
    success: config.success,
    message: options?.message || config.message,
    data: options?.data || null,
    error: options?.error || null,
    meta: options?.meta || null,
    toast: options?.toast || false, // Include toast flag if provided
  });
};

// Enhanced type guard for AppError with additional properties
const isAppError = (
  error: unknown,
): error is {
  statusCode: number;
  message: string;
  details?: Record<string, any>;
  validationErrors?: any;
  name?: string;
} => {
  return error instanceof Error && 'statusCode' in error;
};

export const sendError = (res: Response, error: Error) => {
  logger.error(error);

  const statusCode = isAppError(error) ? error.statusCode : 500;
  const message = isAppError(error) ? error.message : 'Internal server error';

  // Check if this is a validation error with detailed field errors
  const validationErrors =
    isAppError(error) && 'validationErrors' in error
      ? error.validationErrors
      : null;

  // Check if we have other details to include
  const details = isAppError(error) && error.details ? error.details : null;

  res.status(statusCode).json({
    status: statusCode,
    message,
    error: true,
    toast: statusCode < 500,
    validationErrors,
    details: validationErrors ? null : details, // Only include details if no validation errors
  });
};
