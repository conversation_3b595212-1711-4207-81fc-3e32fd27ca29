import crypto from 'crypto';

/**
 * Generate a random token of specified length
 * @param length - The length of the token to generate
 * @returns A random token string
 */
export function generateRandomToken(length: number = 32): string {
  const bytes = crypto.randomBytes(Math.ceil(length / 2));
  return bytes.toString('hex').slice(0, length);
}

/**
 * Generate a URL-friendly random token
 * @param length - The length of the token to generate
 * @returns A URL-friendly random token string
 */
export function generateUrlFriendlyToken(length: number = 12): string {
  // Use characters that are URL-friendly (no need for URL encoding)
  const chars =
    'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_';
  const randomBytes = crypto.randomBytes(length);
  let result = '';

  for (let i = 0; i < length; i++) {
    // Use modulo to map the random byte to a character in our charset
    result += chars[randomBytes[i] % chars.length];
  }

  return result;
}
