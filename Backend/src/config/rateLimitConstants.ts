/**
 * @file rateLimitConstants.ts
 * @description Centralized constants for rate limiting across the application
 */

// Time windows in milliseconds
export const TIME_WINDOWS = {
  ONE_MINUTE: 60 * 1000,
  FIVE_MINUTES: 5 * 60 * 1000,
  FIFTEEN_MINUTES: 15 * 60 * 1000,
  ONE_HOUR: 60 * 60 * 1000,
  ONE_DAY: 24 * 60 * 60 * 1000,
};

// Default rate limits
export const RATE_LIMITS = {
  // Global API rate limit (applied to all routes)
  GLOBAL: {
    windowMs: TIME_WINDOWS.FIFTEEN_MINUTES,
    max: 5000, // TODO: Temporarily increased for development, reduce before production
  },

  // General API endpoints
  API: {
    windowMs: TIME_WINDOWS.ONE_MINUTE,
    max: 2000, // TODO: Temporarily increased for development, reduce before production
  },

  // Authentication related limits
  AUTH: {
    windowMs: TIME_WINDOWS.FIFTEEN_MINUTES,
    max: 100, // TODO: Temporarily increased for development, reduce before production
    message: 'Too many login attempts, please try again later',
  },

  // User registration
  REGISTRATION: {
    windowMs: TIME_WINDOWS.ONE_DAY,
    max: 10, // Increased from 3
    message: 'Too many accounts created, please try again later',
  },

  // Content creation/modification
  CONTENT: {
    windowMs: TIME_WINDOWS.FIFTEEN_MINUTES,
    max: 150, // Increased from 50
    message: 'Content creation limit reached, please try again later',
  },

  // File uploads
  UPLOAD: {
    windowMs: TIME_WINDOWS.ONE_HOUR,
    max: 30, // Increased from 10
    message: 'Upload limit exceeded, please try again later',
  },

  // Dashboard requests
  DASHBOARD: {
    windowMs: TIME_WINDOWS.ONE_MINUTE,
    max: 1000, // TODO: Temporarily increased for development, reduce before production
    message: 'Too many dashboard requests, please try again later',
  },

  // Roadmap related requests
  ROADMAP: {
    windowMs: TIME_WINDOWS.ONE_MINUTE,
    max: 100, // Increased from 30
    message: 'Too many roadmap requests, please try again later',
  },

  // Streak updates
  STREAK: {
    windowMs: TIME_WINDOWS.ONE_MINUTE,
    max: 30, // Increased from 10
    message: 'Too many streak updates, please try again later',
  },

  // Code execution
  CODE_RUNNER: {
    windowMs: TIME_WINDOWS.ONE_MINUTE,
    max: 30, // Increased from 10
    message: 'Too many code execution requests, please try again later',
  },

  // Battle related limits
  BATTLE: {
    CREATION: {
      windowMs: TIME_WINDOWS.FIFTEEN_MINUTES,
      max: 15, // Increased from 5
      message: 'Too many battle creation attempts. Please try again later.',
    },
    JOIN: {
      windowMs: TIME_WINDOWS.ONE_HOUR,
      max: 30, // Increased from 10
      message: 'Too many battle join attempts. Please try again later.',
    },
    SUBMISSION: {
      windowMs: TIME_WINDOWS.FIVE_MINUTES,
      max: 60, // Increased from 20
      message: 'Too many submission attempts. Please try again later.',
    },
  },
};
