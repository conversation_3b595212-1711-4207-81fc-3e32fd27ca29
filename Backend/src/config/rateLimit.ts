import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';

import { redisClient } from '../services/redis';
import { RATE_LIMITS } from './rateLimitConstants';

// Base rate limit configuration
const baseConfig = {
  windowMs: RATE_LIMITS.GLOBAL.windowMs,
  standardHeaders: true,
  legacyHeaders: false,
  store: new RedisStore({
    prefix: 'rate-limit:',
    // @ts-expect-error - Correct property is sendClient but types are outdated
    sendClient: redisClient,
  }),
};

// Different rate limit configurations
export const rateLimits = {
  // API endpoints rate limit
  api: rateLimit({
    ...baseConfig,
    max: RATE_LIMITS.API.max,
    message: 'Too many API requests, please try again later',
  }),

  // Authentication endpoints rate limit
  auth: rateLimit({
    ...baseConfig,
    windowMs: RATE_LIMITS.AUTH.windowMs,
    max: RATE_LIMITS.AUTH.max,
    message: RATE_LIMITS.AUTH.message,
  }),

  // User creation rate limit
  registration: rateLimit({
    ...baseConfig,
    windowMs: RATE_LIMITS.REGISTRATION.windowMs,
    max: RATE_LIMITS.REGISTRATION.max,
    message: RATE_LIMITS.REGISTRATION.message,
  }),

  // Content creation rate limit
  content: rateLimit({
    ...baseConfig,
    max: RATE_LIMITS.CONTENT.max,
    message: RATE_LIMITS.CONTENT.message,
  }),
};
