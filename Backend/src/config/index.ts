import * as process from 'process';
import { config } from 'dotenv';

config();

const {
  MAIL_ADDRESS,
  MAIL_PASSWORD,
  ACCESS_TOKEN_SECRET,
  RESET_TOKEN_SECRET,
  NODE_ENV,
  COMPILER_CLIENT_ID,
  COMPILER_CLIENT_SECRET,
  LOG_LEVEL,
  JWT_SECRET,
  PORT,
  DATABASE_URL,
  JWT_EXPIRES_IN,
  CLOUDINARY_CLOUD_NAME,
  CLOUDINARY_API_KEY,
  CLOUDINARY_API_SECRET,
  CORS_ORIGIN,
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  REDIS_URL,
  WS_URL,
} = process.env as { [key: string]: string };

export {
  MAIL_ADDRESS,
  MAIL_PASSWORD,
  ACCESS_TOKEN_SECRET,
  RESET_TOKEN_SECRET,
  NODE_ENV,
  COMPILER_CLIENT_ID,
  COMPILER_CLIENT_SECRET,
  LOG_LEVEL,
  JWT_SECRET,
  PORT,
  DATABASE_URL,
  JWT_EXPIRES_IN,
  CLOUDINARY_CLOUD_NAME,
  CLOUDINARY_API_KEY,
  CLOUDINARY_API_SECRET,
  CORS_ORIGIN,
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  REDIS_URL,
  WS_URL,
};
