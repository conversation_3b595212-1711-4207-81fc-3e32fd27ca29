import { ChallengeSubmission, PrismaClient } from '@prisma/client';

import prisma from '@/lib/prisma';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

/**
 * Service for handling code execution metrics
 */
export class MetricsService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  /**
   * Record detailed metrics for a submission
   * @param submissionId - The ID of the submission
   * @param metrics - The metrics to record
   * @returns The created metrics record
   */
  async recordMetrics(
    submissionId: string,
    metrics: {
      memory_peak_kb?: number;
      cpu_time_ms?: number;
      io_time_ms?: number;
      compilation_time_ms?: number;
      execution_details?: any;
    },
  ) {
    try {
      // Check if the submission exists
      const submission = await this.prisma.challengeSubmission.findUnique({
        where: { id: submissionId },
      });

      if (!submission) {
        throw createAppError('Submission not found', 404);
      }

      // Create or update metrics
      const existingMetrics = await this.prisma.submissionMetrics.findUnique({
        where: { submission_id: submissionId },
      });

      if (existingMetrics) {
        return this.prisma.submissionMetrics.update({
          where: { id: existingMetrics.id },
          data: {
            ...metrics,
            updated_at: new Date(),
          },
        });
      }

      // Calculate percentiles after creating metrics
      const createdMetrics = await this.prisma.submissionMetrics.create({
        data: {
          submission_id: submissionId,
          ...metrics,
        },
      });

      // Update percentiles
      await this.updatePercentiles(submissionId);

      // Update language metrics
      await this.updateLanguageMetrics(
        submission.challenge_id,
        submission.language,
      );

      return createdMetrics;
    } catch (error) {
      logger.error('Error recording metrics:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to record metrics', 500);
    }
  }

  /**
   * Update percentiles for a submission
   * @param submissionId - The ID of the submission
   */
  async updatePercentiles(submissionId: string) {
    try {
      const submission = await this.prisma.challengeSubmission.findUnique({
        where: { id: submissionId },
        include: { metrics: true },
      });

      if (!submission || !submission.metrics) {
        throw createAppError('Submission or metrics not found', 404);
      }

      // Get all successful submissions for this challenge with the same language
      const allSubmissions = await this.prisma.challengeSubmission.findMany({
        where: {
          challenge_id: submission.challenge_id,
          language: submission.language,
          status: 'accepted',
          metrics: { isNot: null },
        },
        include: { metrics: true },
      });

      if (allSubmissions.length === 0) {
        return;
      }

      // Calculate runtime percentile
      const runtimePercentile = this.calculatePercentile(
        submission.runtime_ms || 0,
        allSubmissions.map((s) => s.runtime_ms || 0),
      );

      // Calculate memory percentile
      const memoryPercentile = this.calculatePercentile(
        submission.memory_used_kb || 0,
        allSubmissions.map((s) => s.memory_used_kb || 0),
      );

      // Calculate optimization score (higher is better)
      const optimizationScore = Math.round(
        (runtimePercentile + memoryPercentile) / 2,
      );

      // Update metrics with percentiles
      await this.prisma.submissionMetrics.update({
        where: { submission_id: submissionId },
        data: {
          percentile_runtime: runtimePercentile,
          percentile_memory: memoryPercentile,
          optimization_score: optimizationScore,
        },
      });
    } catch (error) {
      logger.error('Error updating percentiles:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to update percentiles', 500);
    }
  }

  /**
   * Update language metrics for a challenge
   * @param challengeId - The ID of the challenge
   * @param language - The programming language
   */
  async updateLanguageMetrics(challengeId: string, language: string) {
    try {
      // Get all successful submissions for this challenge with the same language
      const submissions = await this.prisma.challengeSubmission.findMany({
        where: {
          challenge_id: challengeId,
          language,
          status: 'accepted',
        },
      });

      if (submissions.length === 0) {
        return;
      }

      // Calculate metrics
      const runtimes = submissions
        .map((s) => s.runtime_ms)
        .filter((r): r is number => r !== null && r !== undefined);

      const memories = submissions
        .map((s) => s.memory_used_kb)
        .filter((m): m is number => m !== null && m !== undefined);

      const avgRuntime =
        runtimes.length > 0
          ? runtimes.reduce((sum, r) => sum + r, 0) / runtimes.length
          : 0;

      const avgMemory =
        memories.length > 0
          ? memories.reduce((sum, m) => sum + m, 0) / memories.length
          : 0;

      const minRuntime = runtimes.length > 0 ? Math.min(...runtimes) : 0;

      const minMemory = memories.length > 0 ? Math.min(...memories) : 0;

      // Update or create language metrics
      await this.prisma.languageMetrics.upsert({
        where: {
          challenge_id_language: {
            challenge_id: challengeId,
            language,
          },
        },
        update: {
          avg_runtime_ms: avgRuntime,
          avg_memory_kb: avgMemory,
          min_runtime_ms: minRuntime,
          min_memory_kb: minMemory,
          submission_count: submissions.length,
          updated_at: new Date(),
        },
        create: {
          challenge_id: challengeId,
          language,
          avg_runtime_ms: avgRuntime,
          avg_memory_kb: avgMemory,
          min_runtime_ms: minRuntime,
          min_memory_kb: minMemory,
          submission_count: submissions.length,
        },
      });
    } catch (error) {
      logger.error('Error updating language metrics:', error);
      throw createAppError('Failed to update language metrics', 500);
    }
  }

  /**
   * Generate optimization suggestions for a submission
   * @param submissionId - The ID of the submission
   * @returns Array of optimization suggestions
   */
  async generateOptimizationSuggestions(submissionId: string) {
    try {
      const submission = await this.prisma.challengeSubmission.findUnique({
        where: { id: submissionId },
        include: {
          metrics: true,
          challenge: true,
        },
      });

      if (!submission || !submission.metrics) {
        throw createAppError('Submission or metrics not found', 404);
      }

      const suggestions: Array<{
        suggestion_type: string;
        suggestion: string;
        code_snippet?: string;
        line_start?: number;
        line_end?: number;
        priority: number;
      }> = [];

      // Get language metrics for comparison
      const languageMetrics = await this.prisma.languageMetrics.findUnique({
        where: {
          challenge_id_language: {
            challenge_id: submission.challenge_id,
            language: submission.language,
          },
        },
      });

      // Runtime optimization suggestions
      if (
        submission.metrics.percentile_runtime !== null &&
        submission.metrics.percentile_runtime < 50
      ) {
        suggestions.push({
          suggestion_type: 'time',
          suggestion:
            `Your solution is slower than ${Math.round(100 - (submission.metrics.percentile_runtime || 0))}% of submissions. ` +
            `Consider optimizing your algorithm for better time complexity.`,
          priority: 1,
        });

        // Add language-specific suggestions
        if (submission.language === 'python') {
          suggestions.push({
            suggestion_type: 'time',
            suggestion:
              'Consider using list comprehensions instead of loops for better performance.',
            priority: 2,
          });
        } else if (submission.language === 'javascript') {
          suggestions.push({
            suggestion_type: 'time',
            suggestion:
              'Consider using Map or Set instead of objects or arrays for lookups.',
            priority: 2,
          });
        } else if (submission.language === 'java') {
          suggestions.push({
            suggestion_type: 'time',
            suggestion:
              'Consider using more efficient data structures like HashSet for lookups.',
            priority: 2,
          });
        }
      }

      // Memory optimization suggestions
      if (
        submission.metrics.percentile_memory !== null &&
        submission.metrics.percentile_memory < 50
      ) {
        suggestions.push({
          suggestion_type: 'memory',
          suggestion:
            `Your solution uses more memory than ${Math.round(100 - (submission.metrics.percentile_memory || 0))}% of submissions. ` +
            `Consider optimizing your memory usage.`,
          priority: 1,
        });

        // Add language-specific suggestions
        if (submission.language === 'python') {
          suggestions.push({
            suggestion_type: 'memory',
            suggestion:
              'Consider using generators instead of lists to reduce memory usage.',
            priority: 2,
          });
        } else if (submission.language === 'javascript') {
          suggestions.push({
            suggestion_type: 'memory',
            suggestion:
              'Consider using primitive types instead of objects where possible.',
            priority: 2,
          });
        } else if (submission.language === 'java') {
          suggestions.push({
            suggestion_type: 'memory',
            suggestion:
              'Consider using primitive arrays instead of ArrayList for better memory efficiency.',
            priority: 2,
          });
        }
      }

      // Code quality suggestions
      const codeLines = submission.code.split('\n');
      if (codeLines.length > 100) {
        suggestions.push({
          suggestion_type: 'code_quality',
          suggestion:
            'Your solution is quite long. Consider refactoring for better readability.',
          priority: 3,
        });
      }

      // Check for nested loops (simple heuristic)
      const nestedLoopPattern =
        /for.*\s*for|while.*\s*while|for.*\s*while|while.*\s*for/;
      if (nestedLoopPattern.test(submission.code)) {
        suggestions.push({
          suggestion_type: 'time',
          suggestion:
            "Your solution contains nested loops, which might lead to O(n²) time complexity. Consider if there's a more efficient approach.",
          priority: 2,
        });
      }

      // Compare with best solution
      if (languageMetrics) {
        const runtimeRatio = submission.runtime_ms
          ? submission.runtime_ms / languageMetrics.min_runtime_ms
          : 0;
        const memoryRatio = submission.memory_used_kb
          ? submission.memory_used_kb / languageMetrics.min_memory_kb
          : 0;

        if (runtimeRatio > 2) {
          suggestions.push({
            suggestion_type: 'time',
            suggestion: `Your solution is ${runtimeRatio.toFixed(1)}x slower than the fastest solution. There might be room for optimization.`,
            priority: 2,
          });
        }

        if (memoryRatio > 2) {
          suggestions.push({
            suggestion_type: 'memory',
            suggestion: `Your solution uses ${memoryRatio.toFixed(1)}x more memory than the most efficient solution.`,
            priority: 2,
          });
        }
      }

      // Save suggestions to database
      await this.prisma.optimizationSuggestion.deleteMany({
        where: { submission_id: submissionId },
      });

      for (const suggestion of suggestions) {
        await this.prisma.optimizationSuggestion.create({
          data: {
            submission_id: submissionId,
            ...suggestion,
          },
        });
      }

      return suggestions;
    } catch (error) {
      logger.error('Error generating optimization suggestions:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to generate optimization suggestions', 500);
    }
  }

  /**
   * Get metrics for a challenge
   * @param challengeId - The ID of the challenge
   * @returns Challenge metrics
   */
  async getChallengeMetrics(challengeId: string) {
    try {
      // Get challenge details
      const challenge = await this.prisma.challenge.findUnique({
        where: { id: challengeId },
      });

      if (!challenge) {
        throw createAppError('Challenge not found', 404);
      }

      // Get language metrics
      const languageMetrics = await this.prisma.languageMetrics.findMany({
        where: { challenge_id: challengeId },
      });

      // Get submission statistics
      const submissionStats = await this.prisma.challengeSubmission.groupBy({
        by: ['status', 'language'],
        where: { challenge_id: challengeId },
        _count: { id: true },
      });

      // Calculate acceptance rate by language
      const languages = [
        ...new Set(submissionStats.map((stat) => stat.language)),
      ];
      const acceptanceRates = languages.map((language) => {
        const totalSubmissions = submissionStats
          .filter((stat) => stat.language === language)
          .reduce((sum, stat) => sum + stat._count.id, 0);

        const acceptedSubmissions = submissionStats
          .filter(
            (stat) => stat.language === language && stat.status === 'accepted',
          )
          .reduce((sum, stat) => sum + stat._count.id, 0);

        return {
          language,
          total_submissions: totalSubmissions,
          accepted_submissions: acceptedSubmissions,
          acceptance_rate:
            totalSubmissions > 0
              ? (acceptedSubmissions / totalSubmissions) * 100
              : 0,
        };
      });

      // Get runtime distribution
      const runtimeDistribution = await this.prisma.$queryRaw<any[]>`
        SELECT
          language,
          CASE
            WHEN runtime_ms < 100 THEN '< 100ms'
            WHEN runtime_ms < 200 THEN '100-200ms'
            WHEN runtime_ms < 500 THEN '200-500ms'
            WHEN runtime_ms < 1000 THEN '500-1000ms'
            ELSE '> 1000ms'
          END as runtime_range,
          COUNT(*) as count
        FROM "ChallengeSubmission"
        WHERE challenge_id = ${challengeId}
          AND status = 'ACCEPTED'
          AND runtime_ms IS NOT NULL
        GROUP BY language, runtime_range
        ORDER BY language, runtime_range
      `;

      // Get memory distribution
      const memoryDistribution = await this.prisma.$queryRaw<any[]>`
        SELECT
          language,
          CASE
            WHEN memory_used_kb < 1000 THEN '< 1MB'
            WHEN memory_used_kb < 5000 THEN '1-5MB'
            WHEN memory_used_kb < 10000 THEN '5-10MB'
            WHEN memory_used_kb < 50000 THEN '10-50MB'
            ELSE '> 50MB'
          END as memory_range,
          COUNT(*) as count
        FROM "ChallengeSubmission"
        WHERE challenge_id = ${challengeId}
          AND status = 'ACCEPTED'
          AND memory_used_kb IS NOT NULL
        GROUP BY language, memory_range
        ORDER BY language, memory_range
      `;

      return {
        challenge_id: challengeId,
        language_metrics: languageMetrics,
        acceptance_rates: acceptanceRates,
        runtime_distribution: runtimeDistribution,
        memory_distribution: memoryDistribution,
      };
    } catch (error) {
      logger.error('Error getting challenge metrics:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to get challenge metrics', 500);
    }
  }

  /**
   * Get metrics for a submission
   * @param submissionId - The ID of the submission
   * @returns Submission metrics with optimization suggestions
   */
  async getSubmissionMetrics(submissionId: string) {
    try {
      const submission = await this.prisma.challengeSubmission.findUnique({
        where: { id: submissionId },
        include: {
          metrics: true,
          optimization_suggestions: {
            orderBy: { priority: 'asc' },
          },
          challenge: {
            select: {
              id: true,
              title: true,
              difficulty: true,
            },
          },
        },
      });

      if (!submission) {
        throw createAppError('Submission not found', 404);
      }

      // Get language metrics for comparison
      const languageMetrics = await this.prisma.languageMetrics.findUnique({
        where: {
          challenge_id_language: {
            challenge_id: submission.challenge_id,
            language: submission.language,
          },
        },
      });

      return {
        submission_id: submissionId,
        challenge: submission.challenge,
        language: submission.language,
        status: submission.status,
        runtime_ms: submission.runtime_ms,
        memory_used_kb: submission.memory_used_kb,
        metrics: submission.metrics,
        optimization_suggestions: submission.optimization_suggestions,
        language_metrics: languageMetrics,
      };
    } catch (error) {
      logger.error('Error getting submission metrics:', error);
      if (error.statusCode) throw error;
      throw createAppError('Failed to get submission metrics', 500);
    }
  }

  /**
   * Calculate percentile of a value in a dataset
   * @param value - The value to calculate percentile for
   * @param dataset - The dataset to compare against
   * @returns The percentile (0-100)
   */
  private calculatePercentile(value: number, dataset: number[]): number {
    if (dataset.length === 0) return 0;

    // For runtime and memory, lower is better
    // So we want to know what percentage of values are higher than this value
    const countHigher = dataset.filter((d) => d > value).length;
    return (countHigher / dataset.length) * 100;
  }
}

export default new MetricsService();
