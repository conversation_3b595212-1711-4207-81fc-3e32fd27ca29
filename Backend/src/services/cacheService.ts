import Redis from 'ioredis';

import { REDIS_URL } from '../config';
import logger from '../utils/logger';

type CacheOptions = {
  ttl?: number;
  prefix?: string;
};

const redis = new Redis(REDIS_URL);

export async function getCache<T>(key: string): Promise<T | null> {
  try {
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    logger.error('Cache get error:', error);
    return null;
  }
}

export async function setCache<T>(
  key: string,
  value: T,
  options: CacheOptions = {},
): Promise<void> {
  try {
    const { ttl = 3600, prefix = '' } = options;
    const fullKey = prefix ? `${prefix}:${key}` : key;
    const serializedValue = JSON.stringify(value);

    if (ttl > 0) {
      await redis.setex(fullKey, ttl, serializedValue);
    } else {
      await redis.set(fullKey, serializedValue);
    }
  } catch (error) {
    logger.error('Cache set error:', error);
  }
}

export async function deleteCache(key: string): Promise<void> {
  try {
    if (key.endsWith('*')) {
      const prefix = key.slice(0, -1);
      await deleteByPrefix(prefix);
      return;
    }
    await redis.del(key);
  } catch (error) {
    logger.error('Cache delete error:', error);
  }
}

export async function deleteByPrefix(prefix: string): Promise<number> {
  try {
    const stream = redis.scanStream({ match: `${prefix}*`, count: 100 });
    let totalDeleted = 0;
    const pipeline = redis.pipeline();

    return await new Promise<number>((resolve, reject) => {
      stream.on('data', (keys: string[]) => {
        if (keys.length) keys.forEach((k) => pipeline.del(k));
      });
      stream.on('end', async () => {
        if (pipeline.length) {
          const results = await pipeline.exec();
          totalDeleted = results?.filter((r) => r[0] === null).length || 0;
        }
        resolve(totalDeleted);
      });
      stream.on('error', (err) => reject(err));
    });
  } catch (error) {
    logger.error('Cache prefix delete error:', error);
    return 0;
  }
}

export async function getOrSetCache<T>(
  key: string,
  callback: () => Promise<T>,
  options: CacheOptions = {},
): Promise<T> {
  const cached = await getCache<T>(key);
  if (cached) return cached;

  const fresh = await callback();
  await setCache(key, fresh, options);
  return fresh;
}

export async function getWithLock<T>(
  key: string,
  callback: () => Promise<T>,
  options: CacheOptions = {},
): Promise<T> {
  const lockKey = `lock:${key}`;
  const lockTtl = 5; // 5 seconds lock timeout

  try {
    const cached = await getCache<T>(key);
    if (cached) return cached;

    const acquired = await redis.set(lockKey, '1', 'EX', lockTtl, 'NX');

    if (!acquired) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      return getWithLock(key, callback, options);
    }

    const fresh = await callback();
    await setCache(key, fresh, options);
    return fresh;
  } finally {
    await redis.del(lockKey);
  }
}
