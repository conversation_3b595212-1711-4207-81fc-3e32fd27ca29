import {
  Achievement,
  AchievementCategory,
  AchievementTriggerType,
  Prisma,
  PrismaClient,
} from '@prisma/client';

import prisma from '@/lib/prisma';
import { createAppError } from '@/utils/errorHandler';
import logger from '@/utils/logger';

/**
 * Service for handling achievements and their triggers
 */
export class AchievementService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  /**
   * Check if a user has unlocked any achievements based on a trigger
   * @param userId - The ID of the user
   * @param triggerType - The type of trigger
   * @param value - The value to check against (e.g., number of challenges completed)
   * @returns Array of newly unlocked achievements
   */
  async checkAchievements(
    userId: string,
    triggerType: AchievementTriggerType,
    value: number = 1,
  ): Promise<Achievement[]> {
    try {
      // Get all active achievements for this trigger type
      const achievements = await this.prisma.achievement.findMany({
        where: {
          trigger_type: triggerType,
          is_active: true,
        },
      });

      // Get user's already unlocked achievements
      const userAchievements = await this.prisma.userAchievement.findMany({
        where: {
          user_id: userId,
          achievement: {
            trigger_type: triggerType,
          },
        },
        select: {
          achievement_id: true,
        },
      });

      const unlockedAchievementIds = userAchievements.map(
        (ua) => ua.achievement_id,
      );

      // Filter achievements that are not yet unlocked and meet the trigger value
      const newlyUnlockedAchievements = achievements.filter(
        (achievement) =>
          !unlockedAchievementIds.includes(achievement.id) &&
          value >= achievement.trigger_value,
      );

      // If there are newly unlocked achievements, record them
      if (newlyUnlockedAchievements.length > 0) {
        await this.recordAchievements(userId, newlyUnlockedAchievements);
      }

      return newlyUnlockedAchievements;
    } catch (error) {
      logger.error('Error checking achievements:', error);
      throw createAppError('Failed to check achievements', 500);
    }
  }

  /**
   * Record newly unlocked achievements for a user
   * @param userId - The ID of the user
   * @param achievements - Array of achievements to record
   */
  private async recordAchievements(
    userId: string,
    achievements: Achievement[],
  ): Promise<void> {
    try {
      // Create user achievements and notifications in a transaction
      await this.prisma.$transaction(async (tx) => {
        for (const achievement of achievements) {
          // Create user achievement
          const userAchievement = await tx.userAchievement.create({
            data: {
              user_id: userId,
              achievement_id: achievement.id,
              is_seen: false,
            },
          });

          // Create notification
          await tx.achievementNotification.create({
            data: {
              user_id: userId,
              user_achievement_id: userAchievement.id,
              message: `Congratulations! You've unlocked the "${achievement.name}" achievement.`,
              is_read: false,
            },
          });

          // Update user points
          await tx.userPoints.upsert({
            where: { user_id: userId },
            update: {
              points: { increment: achievement.points },
            },
            create: {
              user_id: userId,
              points: achievement.points,
            },
          });

          // Log the achievement
          await tx.userActivityLog.create({
            data: {
              user_id: userId,
              action: 'ACHIEVEMENT_UNLOCKED',
              details: {
                achievement_id: achievement.id,
                achievement_name: achievement.name,
                points: achievement.points,
              },
            },
          });
        }
      });
    } catch (error) {
      logger.error('Error recording achievements:', error);
      throw createAppError('Failed to record achievements', 500);
    }
  }

  /**
   * Manually award an achievement to a user
   * @param userId - The ID of the user
   * @param achievementId - The ID of the achievement
   * @returns The unlocked achievement
   */
  async awardAchievement(
    userId: string,
    achievementId: string,
  ): Promise<Achievement> {
    try {
      // Check if the achievement exists
      const achievement = await this.prisma.achievement.findUnique({
        where: { id: achievementId },
      });

      if (!achievement) {
        throw createAppError('Achievement not found', 404);
      }

      // Check if the user already has this achievement
      const existingUserAchievement =
        await this.prisma.userAchievement.findUnique({
          where: {
            user_id_achievement_id: {
              user_id: userId,
              achievement_id: achievementId,
            },
          },
        });

      if (existingUserAchievement) {
        throw createAppError('User already has this achievement', 400);
      }

      // Award the achievement
      await this.recordAchievements(userId, [achievement]);

      return achievement;
    } catch (error) {
      if (error.statusCode) throw error;
      logger.error('Error awarding achievement:', error);
      throw createAppError('Failed to award achievement', 500);
    }
  }

  /**
   * Get all achievements for a user
   * @param userId - The ID of the user
   * @returns Object containing unlocked and available achievements
   */
  async getUserAchievements(userId: string): Promise<{
    unlocked: Array<Achievement & { unlocked_at: Date }>;
    available: Achievement[];
  }> {
    try {
      // Get user's unlocked achievements
      const userAchievements = await this.prisma.userAchievement.findMany({
        where: { user_id: userId },
        include: { achievement: true },
      });

      // Get all active achievements
      const allAchievements = await this.prisma.achievement.findMany({
        where: {
          is_active: true,
          // Don't include hidden achievements that the user hasn't unlocked yet
          OR: [
            { is_hidden: false },
            {
              is_hidden: true,
              id: { in: userAchievements.map((ua) => ua.achievement_id) },
            },
          ],
        },
      });

      // Split into unlocked and available
      const unlockedAchievementIds = userAchievements.map(
        (ua) => ua.achievement_id,
      );

      const unlocked = userAchievements.map((ua) => ({
        ...ua.achievement,
        unlocked_at: ua.unlocked_at,
      }));

      const available = allAchievements.filter(
        (achievement) =>
          !unlockedAchievementIds.includes(achievement.id) &&
          !achievement.is_hidden,
      );

      return { unlocked, available };
    } catch (error) {
      logger.error('Error getting user achievements:', error);
      throw createAppError('Failed to get user achievements', 500);
    }
  }

  /**
   * Get achievement notifications for a user
   * @param userId - The ID of the user
   * @param unreadOnly - Whether to only return unread notifications
   * @returns Array of achievement notifications
   */
  async getAchievementNotifications(
    userId: string,
    unreadOnly: boolean = false,
  ): Promise<any[]> {
    try {
      return this.prisma.achievementNotification.findMany({
        where: {
          user_id: userId,
          ...(unreadOnly ? { is_read: false } : {}),
        },
        include: {
          user_achievement: {
            include: {
              achievement: true,
            },
          },
        },
        orderBy: {
          created_at: 'desc',
        },
      });
    } catch (error) {
      logger.error('Error getting achievement notifications:', error);
      throw createAppError('Failed to get achievement notifications', 500);
    }
  }

  /**
   * Mark achievement notifications as read
   * @param userId - The ID of the user
   * @param notificationIds - Array of notification IDs to mark as read (if empty, mark all as read)
   */
  async markNotificationsAsRead(
    userId: string,
    notificationIds: string[] = [],
  ): Promise<void> {
    try {
      if (notificationIds.length === 0) {
        // Mark all notifications as read
        await this.prisma.achievementNotification.updateMany({
          where: { user_id: userId },
          data: { is_read: true },
        });
      } else {
        // Mark specific notifications as read
        await this.prisma.achievementNotification.updateMany({
          where: {
            user_id: userId,
            id: { in: notificationIds },
          },
          data: { is_read: true },
        });
      }
    } catch (error) {
      logger.error('Error marking notifications as read:', error);
      throw createAppError('Failed to mark notifications as read', 500);
    }
  }

  /**
   * Check for challenge-related achievements
   * @param userId - The ID of the user
   */
  async checkChallengeAchievements(userId: string): Promise<Achievement[]> {
    try {
      // Count completed challenges
      const completedChallengesCount =
        await this.prisma.challengeSubmission.count({
          where: {
            user_id: userId,
            status: 'accepted',
          },
        });

      // Check for achievements based on challenge count
      return this.checkAchievements(
        userId,
        AchievementTriggerType.CHALLENGE_COUNT,
        completedChallengesCount,
      );
    } catch (error) {
      logger.error('Error checking challenge achievements:', error);
      throw createAppError('Failed to check challenge achievements', 500);
    }
  }

  /**
   * Check for streak-related achievements
   * @param userId - The ID of the user
   * @param streakDays - The number of streak days
   */
  async checkStreakAchievements(
    userId: string,
    streakDays: number,
  ): Promise<Achievement[]> {
    try {
      return this.checkAchievements(
        userId,
        AchievementTriggerType.STREAK_DAYS,
        streakDays,
      );
    } catch (error) {
      logger.error('Error checking streak achievements:', error);
      throw createAppError('Failed to check streak achievements', 500);
    }
  }

  /**
   * Check for social-related achievements
   * @param userId - The ID of the user
   */
  async checkSocialAchievements(userId: string): Promise<Achievement[]> {
    try {
      // Count discussions
      const discussionsCount = await this.prisma.challengeDiscussion.count({
        where: { user_id: userId },
      });

      // Count solution views
      const solutionViews = await this.prisma.sharedSolution.aggregate({
        where: {
          submission: {
            user_id: userId,
          },
        },
        _sum: {
          view_count: true,
        },
      });

      const totalViews = solutionViews._sum.view_count || 0;

      // Check for achievements based on discussions
      const discussionAchievements = await this.checkAchievements(
        userId,
        AchievementTriggerType.DISCUSSION_COUNT,
        discussionsCount,
      );

      // Check for achievements based on solution views
      const viewAchievements = await this.checkAchievements(
        userId,
        AchievementTriggerType.SOLUTION_VIEWS,
        totalViews,
      );

      return [...discussionAchievements, ...viewAchievements];
    } catch (error) {
      logger.error('Error checking social achievements:', error);
      throw createAppError('Failed to check social achievements', 500);
    }
  }

  /**
   * Check for profile completion achievements
   * @param userId - The ID of the user
   */
  async checkProfileAchievements(userId: string): Promise<Achievement[]> {
    try {
      // Get user profile
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          full_name: true,
          bio: true,
          avatar_url: true,
        },
      });

      if (!user) {
        throw createAppError('User not found', 404);
      }

      // Calculate profile completion percentage
      const fields = Object.values(user);
      const filledFields = fields.filter(
        (field) => field !== null && field !== '',
      ).length;
      const totalFields = fields.length;
      const completionPercentage = Math.floor(
        (filledFields / totalFields) * 100,
      );

      // Check for achievements based on profile completion
      return this.checkAchievements(
        userId,
        AchievementTriggerType.PROFILE_COMPLETION,
        completionPercentage,
      );
    } catch (error) {
      if (error.statusCode) throw error;
      logger.error('Error checking profile achievements:', error);
      throw createAppError('Failed to check profile achievements', 500);
    }
  }

  /**
   * Check for roadmap completion achievements
   * @param userId - The ID of the user
   */
  async checkRoadmapAchievements(userId: string): Promise<Achievement[]> {
    try {
      // Count completed roadmaps
      const userRoadmaps = await this.prisma.userRoadmap.findMany({
        where: { user_id: userId },
        include: {
          roadmap: {
            include: {
              topics: true,
            },
          },
        },
      });

      let completedRoadmaps = 0;

      for (const userRoadmap of userRoadmaps) {
        const topicIds = userRoadmap.roadmap.topics.map((topic) => topic.id);

        // Count completed topics for this roadmap
        const completedTopics = await this.prisma.userProgress.count({
          where: {
            user_id: userId,
            topic_id: { in: topicIds },
            is_completed: true,
          },
        });

        // If all topics are completed, count the roadmap as completed
        if (completedTopics === topicIds.length && topicIds.length > 0) {
          completedRoadmaps++;
        }
      }

      // Check for achievements based on roadmap completions
      return this.checkAchievements(
        userId,
        AchievementTriggerType.ROADMAP_COMPLETION,
        completedRoadmaps,
      );
    } catch (error) {
      logger.error('Error checking roadmap achievements:', error);
      throw createAppError('Failed to check roadmap achievements', 500);
    }
  }

  /**
   * Check for battle-related achievements
   * @param userId - The ID of the user
   */
  async checkBattleAchievements(userId: string): Promise<Achievement[]> {
    try {
      // Count battle wins
      const battleWins = await this.prisma.battleParticipant.count({
        where: {
          user_id: userId,
          rank: 1, // First place
        },
      });

      // Check for achievements based on battle wins
      return this.checkAchievements(
        userId,
        AchievementTriggerType.BATTLE_WINS,
        battleWins,
      );
    } catch (error) {
      logger.error('Error checking battle achievements:', error);
      throw createAppError('Failed to check battle achievements', 500);
    }
  }

  /**
   * Check all achievement types for a user
   * @param userId - The ID of the user
   * @returns Array of newly unlocked achievements
   */
  async checkAllAchievements(userId: string): Promise<Achievement[]> {
    try {
      const challengeAchievements =
        await this.checkChallengeAchievements(userId);

      // Get user streak
      const userStreak = await this.prisma.userStreak.findUnique({
        where: { user_id: userId },
        select: { current_streak: true },
      });

      const streakAchievements = userStreak
        ? await this.checkStreakAchievements(userId, userStreak.current_streak)
        : [];

      const socialAchievements = await this.checkSocialAchievements(userId);
      const profileAchievements = await this.checkProfileAchievements(userId);
      const roadmapAchievements = await this.checkRoadmapAchievements(userId);
      const battleAchievements = await this.checkBattleAchievements(userId);

      return [
        ...challengeAchievements,
        ...streakAchievements,
        ...socialAchievements,
        ...profileAchievements,
        ...roadmapAchievements,
        ...battleAchievements,
      ];
    } catch (error) {
      logger.error('Error checking all achievements:', error);
      throw createAppError('Failed to check all achievements', 500);
    }
  }
}

export default new AchievementService();
