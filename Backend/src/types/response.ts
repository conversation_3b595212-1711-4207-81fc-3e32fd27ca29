export type ResponseType =
  | 'SUCCESS'
  | 'ERROR'
  | 'UNAUTHORIZED'
  | 'DASHBOARD_STATS_FETCHED'
  | 'NOT_FOUND'
  | 'VALIDATION_ERROR'
  | 'FORBIDDEN'
  | 'METRICS_FETCHED'
  | 'USERS_FETCHED'
  | 'USER_UPDATED'
  | 'CONFIG_UPDATED'
  | 'CONFIGS_FETCHED'
  | 'RESOURCES_ALLOCATED'
  | 'REPORT_GENERATED'
  | 'AUDIT_LOGS_FETCHED'
  | 'MODERATION_QUEUE_FETCHED'
  | 'CONTENT_MODERATED'
  | 'BATTLE_STATUS_UPDATED'
  | 'ACTIVITIES_FETCHED'
  | 'ACTIVITY_FETCHED'
  | 'ACTIVITY_CREATED'
  | 'ACTIVITY_DELETED'
  | 'ACHIEVEMENTS_FETCHED'
  | 'ACHIEVEMENT_FETCHED'
  | 'ACHIEVEMENT_CREATED'
  | 'ACHIEVEMENT_DELETED'
  | 'ROADMAP_FEATURED_TOGGLED'
  | 'ROADMAPS_STATUS_UPDATED'
  | 'ROADMAPS_FETCHED'
  | 'ROADMAP_UPDATED'
  | 'ROADMAP_DELETED';

export interface ApiResponse<T = any> {
  status: ResponseType;
  message?: string;
  data?: T;
  error?: any;
}
