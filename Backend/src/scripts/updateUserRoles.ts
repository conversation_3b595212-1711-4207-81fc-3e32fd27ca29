/**
 * <PERSON><PERSON><PERSON> to update all users in Supabase with their correct roles from the database
 * This script should be run after migrating to the new role-based system
 */
import { RoleType } from '@prisma/client';

import prisma from '@/lib/prisma';
import logger from '@/utils/logger';
import { updateUserRolesInSupabase } from '@/utils/supabaseAdmin';

/**
 * Updates all users in Supabase with their roles from the database
 */
async function updateAllUserRoles() {
  try {
    logger.info('Starting user role update process...');

    // Get all users with their roles
    const users = await prisma.user.findMany({
      include: {
        user_roles: {
          include: {
            role: true,
          },
        },
      },
    });

    logger.info(`Found ${users.length} users to update`);

    let successCount = 0;
    let failureCount = 0;

    // Process each user
    for (const user of users) {
      if (!user.supabase_id) {
        logger.warn(
          `User ${user.id} (${user.email}) has no Supabase ID, skipping`,
        );
        failureCount++;
        continue;
      }

      // Validate that the supabase_id is a valid UUID
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(user.supabase_id)) {
        logger.warn(
          `User ${user.id} (${user.email}) has invalid Supabase ID format: ${user.supabase_id}, skipping`,
        );
        failureCount++;
        continue;
      }

      // Extract role types from user_roles
      const roleTypes = user.user_roles.map((ur) => ur.role.type as RoleType);

      try {
        // Update user roles in Supabase
        await updateUserRolesInSupabase(user.supabase_id, roleTypes);
        logger.info(
          `Updated roles for user ${user.email}: ${roleTypes.join(', ')}`,
        );
        successCount++;
      } catch (error) {
        logger.error(`Failed to update roles for user ${user.email}:`, error);
        failureCount++;
      }
    }

    logger.info(
      `Role update completed. Success: ${successCount}, Failed: ${failureCount}`,
    );
  } catch (error) {
    logger.error('Error updating user roles:', error);
    throw error;
  }
}

// Run the script if this file is executed directly
if (require.main === module) {
  updateAllUserRoles()
    .then(() => {
      logger.info('User role update script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('User role update script failed:', error);
      process.exit(1);
    });
}

export { updateAllUserRoles };
