import { AchievementCategory, PrismaClient } from '@prisma/client';
import { AchievementTriggerType } from '@prisma/client';

import logger from '../utils/logger';

const prisma = new PrismaClient();

async function main() {
  try {
    // Get a user to associate with the activities and achievements
    const user = await prisma.user.findFirst();

    if (!user) {
      logger.error(
        'No users found in the database. Please create a user first.',
      );
      return;
    }

    // Create sample activities
    const activities = [
      {
        user_id: user.id,
        type: 'completed_topic',
        description: 'Completed the JavaScript Basics topic',
        roadmap_id: null,
        roadmap_title: null,
      },
      {
        user_id: user.id,
        type: 'enrolled_roadmap',
        description: 'Enrolled in the Full Stack Web Development roadmap',
        roadmap_id: 'sample-roadmap-id',
        roadmap_title: 'Full Stack Web Development',
      },
      {
        user_id: user.id,
        type: 'liked_roadmap',
        description: 'Liked the Machine Learning roadmap',
        roadmap_id: 'sample-roadmap-id-2',
        roadmap_title: 'Machine Learning',
      },
      {
        user_id: user.id,
        type: 'commented',
        description: 'Commented on the Data Science roadmap',
        roadmap_id: 'sample-roadmap-id-3',
        roadmap_title: 'Data Science',
      },
      {
        user_id: user.id,
        type: 'bookmarked',
        description: 'Bookmarked the Mobile App Development roadmap',
        roadmap_id: 'sample-roadmap-id-4',
        roadmap_title: 'Mobile App Development',
      },
    ];

    // Create sample achievements
    const achievements = [
      {
        user_id: user.id,
        title: 'First Steps',
        description: 'Completed your first topic',
        icon: 'trophy',
        rarity: 'common',
        type: 'completed_topic',
        criteria: 'JavaScript Basics',
        earned_at: new Date(),
      },
      {
        user_id: user.id,
        title: 'Knowledge Seeker',
        description: 'Enrolled in 5 roadmaps',
        icon: 'book',
        rarity: 'uncommon',
        type: 'enrolled_roadmap',
        criteria: '5 roadmaps',
        earned_at: new Date(),
      },
      {
        user_id: user.id,
        title: 'Coding Ninja',
        description: 'Completed 10 coding challenges',
        icon: 'award',
        rarity: 'rare',
        type: 'completed_challenge',
        criteria: '10 challenges',
        earned_at: new Date(),
      },
      {
        user_id: user.id,
        title: 'Community Leader',
        description: 'Helped 50 other learners',
        icon: 'star',
        rarity: 'epic',
        type: 'helped_learner',
        criteria: '50 learners',
        earned_at: new Date(),
      },
      {
        user_id: user.id,
        title: 'Master of All',
        description: 'Completed all roadmaps',
        icon: 'fire',
        rarity: 'legendary',
        type: 'completed_roadmap',
        criteria: '5 roadmaps',
        earned_at: new Date(),
      },
    ];

    // Insert activities
    for (const activity of activities) {
      await prisma.activity.create({
        data: activity,
      });
    }

    // Insert achievements
    for (const achievement of achievements) {
      await prisma.achievement.create({
        data: {
          name: achievement.title,
          description: achievement.description,
          category: mapTypeToCategory(achievement.type) as AchievementCategory,
          icon_url: `https://example.com/icons/${achievement.icon}.png`,
          points: mapRarityToPoints(achievement.rarity),
          tier: mapRarityToTier(achievement.rarity),
          trigger_type: mapTypeToTriggerType(achievement.type),
          trigger_value: extractTriggerValue(achievement.criteria),
          is_hidden: false,
        },
      });
    }

    logger.info('Sample activities and achievements created successfully!');
  } catch (error) {
    logger.error('Error seeding activities and achievements:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
function mapTypeToCategory(type: string): string {
  switch (type) {
    case 'completed_topic':
    case 'completed_roadmap':
      return 'learning';
    case 'enrolled_roadmap':
      return 'engagement';
    case 'liked_roadmap':
    case 'commented':
    case 'bookmarked':
      return 'social';
    case 'completed_challenge':
      return 'skills';
    case 'helped_learner':
      return 'community';
    default:
      return 'other';
  }
}

function mapRarityToPoints(rarity: string): number {
  switch (rarity.toLowerCase()) {
    case 'common':
      return 10;
    case 'uncommon':
      return 25;
    case 'rare':
      return 50;
    case 'epic':
      return 100;
    case 'legendary':
      return 250;
    default:
      return 5;
  }
}
function mapRarityToTier(rarity: string): number {
  switch (rarity.toLowerCase()) {
    case 'common':
      return 1;
    case 'uncommon':
      return 2;
    case 'rare':
      return 3;
    case 'epic':
      return 4;
    case 'legendary':
      return 5;
    default:
      return 1;
  }
}

function mapTypeToTriggerType(type: string): AchievementTriggerType {
  switch (type) {
    case 'completed_topic':
      return AchievementTriggerType.ROADMAP_COMPLETION;
    case 'completed_roadmap':
      return AchievementTriggerType.ROADMAP_COMPLETION;
    case 'enrolled_roadmap':
      return AchievementTriggerType.ROADMAP_COMPLETION;
    case 'liked_roadmap':
      return AchievementTriggerType.ROADMAP_COMPLETION;
    case 'commented':
      return AchievementTriggerType.DISCUSSION_COUNT;
    case 'bookmarked':
      return AchievementTriggerType.ROADMAP_COMPLETION;
    case 'completed_challenge':
      return AchievementTriggerType.CHALLENGE_COMPLETION;
    case 'helped_learner':
      return AchievementTriggerType.MANUAL;
    default:
      return AchievementTriggerType.MANUAL;
  }
}
function extractTriggerValue(criteria: string): number {
  // Extract the first number found in the criteria string
  const match = criteria.match(/\d+/);
  if (match) {
    return parseInt(match[0], 10);
  }
  // Default value if no number is found
  return 1;
}
