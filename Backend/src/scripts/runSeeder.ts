/**
 * <PERSON><PERSON><PERSON> to run the user role seeder
 * This script will update all users in Supabase with their correct roles from the database
 */
import logger from '@/utils/logger';

import { updateAllUserRoles } from './updateUserRoles';

async function runSeeder() {
  try {
    logger.info('Starting role seeder...');

    // Run the user role update seeder
    await updateAllUserRoles();

    logger.info('Role seeder completed successfully');
  } catch (error) {
    logger.error('Role seeder failed:', error);
    process.exit(1);
  }
}

// Run the script if this file is executed directly
if (require.main === module) {
  runSeeder()
    .then(() => {
      logger.info('Seeder script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Seeder script failed:', error);
      process.exit(1);
    });
}
