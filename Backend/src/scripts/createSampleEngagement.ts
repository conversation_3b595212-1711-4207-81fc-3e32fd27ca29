import { PrismaClient } from '@prisma/client';

import logger from '../utils/logger';

const prisma = new PrismaClient();

async function main() {
  try {
    // Get all roadmaps
    const roadmaps = await prisma.roadmap.findMany({
      include: {
        user: true,
      },
    });

    if (roadmaps.length === 0) {
      logger.error('No roadmaps found in the database.');
      return;
    }

    // Get all users
    const users = await prisma.user.findMany({
      take: 5,
    });

    if (users.length === 0) {
      logger.error('No users found in the database.');
      return;
    }

    // Create sample likes, comments, and bookmarks for each roadmap
    for (const roadmap of roadmaps) {
      // Create likes
      for (const user of users) {
        // Skip if the user is the roadmap creator
        if (user.id === roadmap.user_id) continue;

        // 70% chance to create a like
        if (Math.random() < 0.7) {
          await prisma.like.upsert({
            where: {
              user_id_roadmap_id: {
                user_id: user.id,
                roadmap_id: roadmap.id,
              },
            },
            update: {},
            create: {
              user_id: user.id,
              roadmap_id: roadmap.id,
            },
          });
        }

        // 50% chance to create a comment
        if (Math.random() < 0.5) {
          await prisma.comment.create({
            data: {
              user_id: user.id,
              roadmap_id: roadmap.id,
              content: `This is a great roadmap! I'm learning a lot from it.`,
            },
          });
        }

        // 30% chance to bookmark (enroll)
        if (Math.random() < 0.3) {
          await prisma.userRoadmap.upsert({
            where: {
              user_id_roadmap_id: {
                user_id: user.id,
                roadmap_id: roadmap.id,
              },
            },
            update: {},
            create: {
              user_id: user.id,
              roadmap_id: roadmap.id,
            },
          });
        }
      }

      // Update roadmap popularity based on engagement
      const likesCount = await prisma.like.count({
        where: { roadmap_id: roadmap.id },
      });

      const commentsCount = await prisma.comment.count({
        where: { roadmap_id: roadmap.id },
      });

      const bookmarksCount = await prisma.userRoadmap.count({
        where: { roadmap_id: roadmap.id },
      });

      // Calculate popularity score
      const popularityScore =
        likesCount * 3 + commentsCount * 2 + bookmarksCount * 1;

      // Update roadmap popularity
      await prisma.roadmap.update({
        where: { id: roadmap.id },
        data: {
          popularity: popularityScore,
          // Make one roadmap featured and one trending
          is_public: true,
        },
      });
    }

    logger.info('Sample engagement data created successfully!');
  } catch (error) {
    logger.error('Error creating sample engagement data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
