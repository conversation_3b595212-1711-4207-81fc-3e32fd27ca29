import { PrismaClient } from '@prisma/client';

import logger from '../utils/logger';

const prisma = new PrismaClient();

async function main() {
  try {
    // Update all roadmaps to be public
    const result = await prisma.roadmap.updateMany({
      where: {},
      data: {
        is_public: true,
        popularity: 150, // Set popularity high enough to be featured
      },
    });

    logger.info(`Updated ${result.count} roadmaps to be public`);
  } catch (error) {
    logger.error('Error updating roadmaps:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
