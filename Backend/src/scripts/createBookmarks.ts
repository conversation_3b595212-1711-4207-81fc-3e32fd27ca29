import { PrismaClient } from '@prisma/client';

import logger from '../utils/logger';

const prisma = new PrismaClient();

async function main() {
  try {
    // Get all users
    const users = await prisma.user.findMany({
      take: 5,
    });

    if (users.length === 0) {
      logger.error('No users found in the database.');
      return;
    }

    // Get all roadmaps
    const roadmaps = await prisma.roadmap.findMany({
      where: {
        is_public: true,
      },
      take: 10,
    });

    if (roadmaps.length === 0) {
      logger.error('No roadmaps found in the database.');
      return;
    }

    // Create bookmarks for each user
    for (const user of users) {
      // Select 3 random roadmaps to bookmark
      const roadmapsToBookmark = roadmaps
        .sort(() => 0.5 - Math.random())
        .slice(0, 3);

      for (const roadmap of roadmapsToBookmark) {
        // Check if bookmark already exists
        const existingBookmark = await prisma.userRoadmap.findFirst({
          where: {
            user_id: user.id,
            roadmap_id: roadmap.id,
          },
        });

        if (!existingBookmark) {
          await prisma.userRoadmap.create({
            data: {
              user_id: user.id,
              roadmap_id: roadmap.id,
            },
          });
          logger.info(
            `Created bookmark for user ${user.id} on roadmap ${roadmap.id}`,
          );
        } else {
          logger.info(
            `Bookmark already exists for user ${user.id} on roadmap ${roadmap.id}`,
          );
        }
      }
    }

    logger.info('Bookmarks created successfully!');
  } catch (error) {
    logger.error('Error creating bookmarks:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
