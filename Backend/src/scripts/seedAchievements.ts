import {
  AchievementCategory,
  AchievementTriggerType,
  PrismaClient,
} from '@prisma/client';

import logger from '@/utils/logger';

const prisma = new PrismaClient();

const achievements = [
  // Challenge achievements
  {
    name: 'First Steps',
    description: 'Complete your first coding challenge',
    category: AchievementCategory.CHALLENGE,
    icon_url: 'https://example.com/icons/first_steps.png',
    points: 10,
    tier: 1,
    trigger_type: AchievementTriggerType.CHALLENGE_COUNT,
    trigger_value: 1,
    is_hidden: false,
  },
  {
    name: 'Getting Started',
    description: 'Complete 5 coding challenges',
    category: AchievementCategory.CHALLENGE,
    icon_url: 'https://example.com/icons/getting_started.png',
    points: 20,
    tier: 1,
    trigger_type: AchievementTriggerType.CHALLENGE_COUNT,
    trigger_value: 5,
    is_hidden: false,
  },
  {
    name: 'Code Warrior',
    description: 'Complete 25 coding challenges',
    category: AchievementCategory.CHALLENGE,
    icon_url: 'https://example.com/icons/code_warrior.png',
    points: 50,
    tier: 2,
    trigger_type: AchievementTriggerType.CHALLENGE_COUNT,
    trigger_value: 25,
    is_hidden: false,
  },
  {
    name: 'Code Master',
    description: 'Complete 100 coding challenges',
    category: AchievementCategory.CHALLENGE,
    icon_url: 'https://example.com/icons/code_master.png',
    points: 100,
    tier: 3,
    trigger_type: AchievementTriggerType.CHALLENGE_COUNT,
    trigger_value: 100,
    is_hidden: false,
  },

  // Streak achievements
  {
    name: 'Consistent Coder',
    description: 'Maintain a 3-day coding streak',
    category: AchievementCategory.STREAK,
    icon_url: 'https://example.com/icons/consistent_coder.png',
    points: 15,
    tier: 1,
    trigger_type: AchievementTriggerType.STREAK_DAYS,
    trigger_value: 3,
    is_hidden: false,
  },
  {
    name: 'Code Habit',
    description: 'Maintain a 7-day coding streak',
    category: AchievementCategory.STREAK,
    icon_url: 'https://example.com/icons/code_habit.png',
    points: 30,
    tier: 2,
    trigger_type: AchievementTriggerType.STREAK_DAYS,
    trigger_value: 7,
    is_hidden: false,
  },
  {
    name: 'Code Lifestyle',
    description: 'Maintain a 30-day coding streak',
    category: AchievementCategory.STREAK,
    icon_url: 'https://example.com/icons/code_lifestyle.png',
    points: 100,
    tier: 3,
    trigger_type: AchievementTriggerType.STREAK_DAYS,
    trigger_value: 30,
    is_hidden: false,
  },

  // Social achievements
  {
    name: 'Discussion Starter',
    description: 'Create your first discussion',
    category: AchievementCategory.SOCIAL,
    icon_url: 'https://example.com/icons/discussion_starter.png',
    points: 10,
    tier: 1,
    trigger_type: AchievementTriggerType.DISCUSSION_COUNT,
    trigger_value: 1,
    is_hidden: false,
  },
  {
    name: 'Active Participant',
    description: 'Create 10 discussions',
    category: AchievementCategory.SOCIAL,
    icon_url: 'https://example.com/icons/active_participant.png',
    points: 30,
    tier: 2,
    trigger_type: AchievementTriggerType.DISCUSSION_COUNT,
    trigger_value: 10,
    is_hidden: false,
  },
  {
    name: 'Solution Influencer',
    description: 'Get 100 views on your shared solutions',
    category: AchievementCategory.SOCIAL,
    icon_url: 'https://example.com/icons/solution_influencer.png',
    points: 50,
    tier: 2,
    trigger_type: AchievementTriggerType.SOLUTION_VIEWS,
    trigger_value: 100,
    is_hidden: false,
  },

  // Profile achievements
  {
    name: 'Profile Starter',
    description: 'Complete 25% of your profile',
    category: AchievementCategory.PROFILE,
    icon_url: 'https://example.com/icons/profile_starter.png',
    points: 10,
    tier: 1,
    trigger_type: AchievementTriggerType.PROFILE_COMPLETION,
    trigger_value: 25,
    is_hidden: false,
  },
  {
    name: 'Profile Builder',
    description: 'Complete 50% of your profile',
    category: AchievementCategory.PROFILE,
    icon_url: 'https://example.com/icons/profile_builder.png',
    points: 20,
    tier: 1,
    trigger_type: AchievementTriggerType.PROFILE_COMPLETION,
    trigger_value: 50,
    is_hidden: false,
  },
  {
    name: 'Profile Master',
    description: 'Complete 100% of your profile',
    category: AchievementCategory.PROFILE,
    icon_url: 'https://example.com/icons/profile_master.png',
    points: 50,
    tier: 2,
    trigger_type: AchievementTriggerType.PROFILE_COMPLETION,
    trigger_value: 100,
    is_hidden: false,
  },

  // Roadmap achievements
  {
    name: 'Path Starter',
    description: 'Complete your first roadmap',
    category: AchievementCategory.ROADMAP,
    icon_url: 'https://example.com/icons/path_starter.png',
    points: 50,
    tier: 2,
    trigger_type: AchievementTriggerType.ROADMAP_COMPLETION,
    trigger_value: 1,
    is_hidden: false,
  },
  {
    name: 'Path Explorer',
    description: 'Complete 3 roadmaps',
    category: AchievementCategory.ROADMAP,
    icon_url: 'https://example.com/icons/path_explorer.png',
    points: 100,
    tier: 3,
    trigger_type: AchievementTriggerType.ROADMAP_COMPLETION,
    trigger_value: 3,
    is_hidden: false,
  },

  // Battle achievements
  {
    name: 'Battle Winner',
    description: 'Win your first coding battle',
    category: AchievementCategory.BATTLE,
    icon_url: 'https://example.com/icons/battle_winner.png',
    points: 50,
    tier: 2,
    trigger_type: AchievementTriggerType.BATTLE_WINS,
    trigger_value: 1,
    is_hidden: false,
  },
  {
    name: 'Battle Champion',
    description: 'Win 5 coding battles',
    category: AchievementCategory.BATTLE,
    icon_url: 'https://example.com/icons/battle_champion.png',
    points: 100,
    tier: 3,
    trigger_type: AchievementTriggerType.BATTLE_WINS,
    trigger_value: 5,
    is_hidden: false,
  },

  // Hidden achievements
  {
    name: 'Night Owl',
    description: 'Complete a challenge between midnight and 5 AM',
    category: AchievementCategory.SPECIAL,
    icon_url: 'https://example.com/icons/night_owl.png',
    points: 25,
    tier: 1,
    trigger_type: AchievementTriggerType.MANUAL,
    trigger_value: 1,
    is_hidden: true,
  },
  {
    name: 'Speed Demon',
    description: 'Solve a hard challenge in under 10 minutes',
    category: AchievementCategory.SPECIAL,
    icon_url: 'https://example.com/icons/speed_demon.png',
    points: 50,
    tier: 2,
    trigger_type: AchievementTriggerType.MANUAL,
    trigger_value: 1,
    is_hidden: true,
  },
];

async function seedAchievements() {
  try {
    logger.info('Starting achievement seeding...');

    // Create achievements
    for (const achievement of achievements) {
      // Check if achievement already exists
      const existingAchievement = await prisma.achievement.findUnique({
        where: { name: achievement.name },
      });

      if (!existingAchievement) {
        await prisma.achievement.create({
          data: achievement,
        });
        logger.info(`Created achievement: ${achievement.name}`);
      } else {
        logger.info(`Achievement already exists: ${achievement.name}`);
      }
    }

    logger.info('Achievement seeding completed successfully!');
  } catch (error) {
    logger.error('Error seeding achievements:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedAchievements();
