import Joi from 'joi';

export const challengeExampleValidation = Joi.object({
  input: Joi.string().required().min(1).messages({
    'string.empty': 'Input is required',
    'string.min': 'Input must be at least 1 character long',
    'any.required': 'Input is required',
  }),
  output: Joi.string().required().min(1).messages({
    'string.empty': 'Output is required',
    'string.min': 'Output must be at least 1 character long',
    'any.required': 'Output is required',
  }),
  explanation: Joi.string().optional(),
  order_index: Joi.number().integer().min(0).messages({
    'number.base': 'Order index must be a number',
    'number.integer': 'Order index must be an integer',
    'number.min': 'Order index must be a non-negative integer',
  }),
});
