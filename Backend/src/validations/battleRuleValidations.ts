import Joi from 'joi';

import { RuleCategory } from '@prisma/client';

// Validation schema for battle rule ID
export const battleRuleIdValidation = Joi.object({
  id: Joi.string().uuid().required().messages({
    'string.guid': 'Invalid battle rule ID format',
  }),
});

// Validation schema for creating a battle rule
export const createBattleRuleValidationSchema = Joi.object({
  name: Joi.string().min(3).max(100).required().messages({
    'string.min': 'Name must be at least 3 characters long',
  }),
  description: Joi.string().min(10).max(1000).required().messages({
    'string.min': 'Description must be at least 10 characters long',
  }),
  category: Joi.string()
    .valid(...Object.values(RuleCategory))
    .required()
    .messages({
      'any.only': 'Invalid rule category',
    }),
  is_active: Joi.boolean().default(true),
  is_default: Joi.boolean().default(false),
});

// Validation schema for updating a battle rule
export const updateBattleRuleValidationSchema = Joi.object({
  name: Joi.string().min(3).max(100).messages({
    'string.min': 'Name must be at least 3 characters long',
  }),
  description: Joi.string().min(10).max(1000).messages({
    'string.min': 'Description must be at least 10 characters long',
  }),
  category: Joi.string()
    .valid(...Object.values(RuleCategory))
    .messages({
      'any.only': 'Invalid rule category',
    }),
  is_active: Joi.boolean(),
  is_default: Joi.boolean(),
}).min(1); // Require at least one field to be present for updates

// Validation schema for associating rules with a battle
export const associateRulesValidationSchema = Joi.object({
  battle_id: Joi.string().uuid().required().messages({
    'string.guid': 'Invalid battle ID format',
  }),
  rule_ids: Joi.array()
    .items(
      Joi.string().uuid().messages({
        'string.guid': 'Invalid rule ID format',
      }),
    )
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one rule must be provided',
    }),
});
