import Joi from 'joi';

export const createHintValidation = Joi.object({
  challenge_id: Joi.string().required().messages({
    'any.required': 'Challenge ID is required',
  }),
  content: Joi.string().required().max(5000).messages({
    'any.required': 'Hint content is required',
    'string.max': 'Hint content cannot exceed 5000 characters',
  }),
  difficulty: Joi.string()
    .valid('BEGINNER', 'INTERMEDIATE', 'ADVANCED')
    .required()
    .messages({
      'any.required': 'Hint difficulty is required',
      'any.only':
        'Hint difficulty must be one of: BEGINNER, INTERMEDIATE, ADVANCED',
    }),
  order: Joi.number().integer().min(0).messages({
    'number.base': 'Order must be a number',
    'number.integer': 'Order must be an integer',
    'number.min': 'Order must be at least 0',
  }),
  point_penalty: Joi.number().integer().min(1).messages({
    'number.base': 'Point penalty must be a number',
    'number.integer': 'Point penalty must be an integer',
    'number.min': 'Point penalty must be at least 1',
  }),
});

export const updateHintValidation = Joi.object({
  content: Joi.string().max(5000).messages({
    'string.max': 'Hint content cannot exceed 5000 characters',
  }),
  difficulty: Joi.string()
    .valid('BEGINNER', 'INTERMEDIATE', 'ADVANCED')
    .messages({
      'any.only':
        'Hint difficulty must be one of: BEGINNER, INTERMEDIATE, ADVANCED',
    }),
  order: Joi.number().integer().min(0).messages({
    'number.base': 'Order must be a number',
    'number.integer': 'Order must be an integer',
    'number.min': 'Order must be at least 0',
  }),
  point_penalty: Joi.number().integer().min(1).messages({
    'number.base': 'Point penalty must be a number',
    'number.integer': 'Point penalty must be an integer',
    'number.min': 'Point penalty must be at least 1',
  }),
});
