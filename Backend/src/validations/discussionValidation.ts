import Joi from 'joi';

export const createDiscussionValidation = Joi.object({
  challenge_id: Joi.string().required().messages({
    'any.required': 'Challenge ID is required',
  }),
  parent_id: Joi.string().allow(null, '').messages({
    'string.base': 'Parent ID must be a string',
  }),
  content: Joi.string().required().max(5000).messages({
    'any.required': 'Content is required',
    'string.max': 'Content cannot exceed 5000 characters',
  }),
  code_snippet: Joi.string().allow(null, '').max(10000).messages({
    'string.max': 'Code snippet cannot exceed 10000 characters',
  }),
  code_language: Joi.string().allow(null, '').messages({
    'string.base': 'Code language must be a string',
  }),
});

export const updateDiscussionValidation = Joi.object({
  content: Joi.string().max(5000).messages({
    'string.max': 'Content cannot exceed 5000 characters',
  }),
  code_snippet: Joi.string().allow(null, '').max(10000).messages({
    'string.max': 'Code snippet cannot exceed 10000 characters',
  }),
  code_language: Joi.string().allow(null, '').messages({
    'string.base': 'Code language must be a string',
  }),
});

export const voteDiscussionValidation = Joi.object({
  vote_type: Joi.string().valid('UPVOTE', 'DOWNVOTE').required().messages({
    'any.required': 'Vote type is required',
    'any.only': 'Vote type must be either UPVOTE or DOWNVOTE',
  }),
});

export const flagDiscussionValidation = Joi.object({
  reason: Joi.string()
    .valid('SPAM', 'INAPPROPRIATE', 'OFFENSIVE', 'INCORRECT', 'OTHER')
    .required()
    .messages({
      'any.required': 'Flag reason is required',
      'any.only':
        'Flag reason must be one of: SPAM, INAPPROPRIATE, OFFENSIVE, INCORRECT, OTHER',
    }),
  details: Joi.string().allow(null, '').max(1000).messages({
    'string.max': 'Details cannot exceed 1000 characters',
  }),
});

export const moderateDiscussionValidation = Joi.object({
  action: Joi.string().valid('HIDE', 'APPROVE').required().messages({
    'any.required': 'Moderation action is required',
    'any.only': 'Moderation action must be either HIDE or APPROVE',
  }),
});

export const markNotificationsAsReadValidation = Joi.object({
  notification_ids: Joi.array().items(Joi.string()).optional(),
});
