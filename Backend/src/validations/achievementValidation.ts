import Joi from 'joi';

import { AchievementCategory, AchievementTriggerType } from '@prisma/client';

export const createAchievementValidation = Joi.object({
  name: Joi.string().required().max(100).messages({
    'any.required': 'Achievement name is required',
    'string.max': 'Achievement name cannot exceed 100 characters',
  }),
  description: Joi.string().required().max(500).messages({
    'any.required': 'Achievement description is required',
    'string.max': 'Achievement description cannot exceed 500 characters',
  }),
  category: Joi.string()
    .valid(...Object.values(AchievementCategory))
    .required()
    .messages({
      'any.required': 'Achievement category is required',
      'any.only': 'Invalid achievement category',
    }),
  icon_url: Joi.string().required().uri().messages({
    'any.required': 'Achievement icon URL is required',
    'string.uri': 'Achievement icon URL must be a valid URI',
  }),
  points: Joi.number().integer().min(1).required().messages({
    'any.required': 'Achievement points are required',
    'number.base': 'Achievement points must be a number',
    'number.integer': 'Achievement points must be an integer',
    'number.min': 'Achievement points must be at least 1',
  }),
  tier: Joi.number().integer().min(1).max(5).default(1).messages({
    'number.base': 'Achievement tier must be a number',
    'number.integer': 'Achievement tier must be an integer',
    'number.min': 'Achievement tier must be at least 1',
    'number.max': 'Achievement tier cannot exceed 5',
  }),
  trigger_type: Joi.string()
    .valid(...Object.values(AchievementTriggerType))
    .required()
    .messages({
      'any.required': 'Achievement trigger type is required',
      'any.only': 'Invalid achievement trigger type',
    }),
  trigger_value: Joi.number().integer().min(1).default(1).messages({
    'number.base': 'Achievement trigger value must be a number',
    'number.integer': 'Achievement trigger value must be an integer',
    'number.min': 'Achievement trigger value must be at least 1',
  }),
  is_hidden: Joi.boolean().default(false),
});

export const updateAchievementValidation = Joi.object({
  name: Joi.string().max(100).messages({
    'string.max': 'Achievement name cannot exceed 100 characters',
  }),
  description: Joi.string().max(500).messages({
    'string.max': 'Achievement description cannot exceed 500 characters',
  }),
  category: Joi.string()
    .valid(...Object.values(AchievementCategory))
    .messages({
      'any.only': 'Invalid achievement category',
    }),
  icon_url: Joi.string().uri().messages({
    'string.uri': 'Achievement icon URL must be a valid URI',
  }),
  points: Joi.number().integer().min(1).messages({
    'number.base': 'Achievement points must be a number',
    'number.integer': 'Achievement points must be an integer',
    'number.min': 'Achievement points must be at least 1',
  }),
  tier: Joi.number().integer().min(1).max(5).messages({
    'number.base': 'Achievement tier must be a number',
    'number.integer': 'Achievement tier must be an integer',
    'number.min': 'Achievement tier must be at least 1',
    'number.max': 'Achievement tier cannot exceed 5',
  }),
  trigger_type: Joi.string()
    .valid(...Object.values(AchievementTriggerType))
    .messages({
      'any.only': 'Invalid achievement trigger type',
    }),
  trigger_value: Joi.number().integer().min(1).messages({
    'number.base': 'Achievement trigger value must be a number',
    'number.integer': 'Achievement trigger value must be an integer',
    'number.min': 'Achievement trigger value must be at least 1',
  }),
  is_hidden: Joi.boolean(),
  is_active: Joi.boolean(),
});

export const awardAchievementValidation = Joi.object({
  user_id: Joi.string().required().messages({
    'any.required': 'User ID is required',
  }),
  achievement_id: Joi.string().required().messages({
    'any.required': 'Achievement ID is required',
  }),
});

export const markNotificationsAsReadValidation = Joi.object({
  notification_ids: Joi.array().items(Joi.string()).optional(),
});
