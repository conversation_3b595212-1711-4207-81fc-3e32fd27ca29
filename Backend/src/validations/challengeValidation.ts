import Joi from 'joi';

import { ChallengeCategory, Difficulty } from '@prisma/client';

export const createChallengeValidation = Joi.object({
  title: Joi.string().required(),
  description: Joi.string().required(),
  points: Joi.number().integer().min(1).required(),
  difficulty: Joi.string()
    .valid(...Object.values(Difficulty))
    .required(),
  category: Joi.string()
    .valid(...Object.values(ChallengeCategory))
    .required(),
  input_format: Joi.string().required(),
  output_format: Joi.string().required(),
  example_input: Joi.string().required(),
  example_output: Joi.string().required(),
  constraints: Joi.string().required(),
  function_signature: Joi.string().required(),
  time_limit: Joi.number().integer().min(1).optional(),
  memory_limit: Joi.number().integer().min(1).optional(),
  tags: Joi.array().items(Joi.string()).optional(),
  topic_id: Joi.string().required(),
  explanation: Joi.string().optional(),
  test_cases: Joi.array()
    .items(
      Joi.object({
        input: Joi.string().required(),
        output: Joi.string().required(),
        is_hidden: Joi.boolean().optional(),
        order_index: Joi.number().integer().min(0).optional(),
      }),
    )
    .optional(),
  examples: Joi.array()
    .items(
      Joi.object({
        input: Joi.string().required(),
        output: Joi.string().required(),
        explanation: Joi.string().optional(),
        order_index: Joi.number().integer().min(0).required(),
      }),
    )
    .optional(),
  boilerplates: Joi.array()
    .items(
      Joi.object({
        language: Joi.string().required(),
        boilerplate_code: Joi.string().required(),
      }),
    )
    .optional(),
});

export const submitChallengeValidation = Joi.object({
  code: Joi.string().required(),
  language: Joi.string().required(),
});

export const saveFilterPresetValidation = Joi.object({
  name: Joi.string().required().max(50),
  filters: Joi.object({
    difficulty: Joi.string()
      .valid(...Object.values(Difficulty), 'all')
      .optional(),
    category: Joi.string()
      .valid(...Object.values(ChallengeCategory), 'all')
      .optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    status: Joi.string()
      .valid('completed', 'in_progress', 'not_started')
      .optional(),
    sort: Joi.string()
      .valid('newest', 'most_popular', 'highest_rated', 'most_submissions')
      .optional(),
  }).required(),
});
