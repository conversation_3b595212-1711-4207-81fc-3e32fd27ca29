import Joi from 'joi';

export const createSharedSolutionValidation = Joi.object({
  submission_id: Joi.string().required().messages({
    'any.required': 'Submission ID is required',
  }),
  title: Joi.string().max(100).optional().messages({
    'string.max': 'Title cannot exceed 100 characters',
  }),
  description: Joi.string().max(500).optional().messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  is_public: Joi.boolean().optional(),
  expires_at: Joi.date().iso().min('now').optional().messages({
    'date.min': 'Expiration date must be in the future',
    'date.format': 'Expiration date must be a valid ISO date',
  }),
});

export const updateSharedSolutionValidation = Joi.object({
  title: Joi.string().max(100).optional().messages({
    'string.max': 'Title cannot exceed 100 characters',
  }),
  description: Joi.string().max(500).optional().messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
  is_public: Joi.boolean().optional(),
  expires_at: Joi.date().iso().min('now').allow(null).optional().messages({
    'date.min': 'Expiration date must be in the future',
    'date.format': 'Expiration date must be a valid ISO date',
  }),
});
