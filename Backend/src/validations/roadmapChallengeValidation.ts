import Joi from 'joi';

export const addChallengeToRoadmapValidation = Joi.object({
  roadmap_id: Joi.string().required().messages({
    'any.required': 'Roadmap ID is required',
  }),
  challenge_id: Joi.string().required().messages({
    'any.required': 'Challenge ID is required',
  }),
  order: Joi.number().integer().min(0).default(0).messages({
    'number.base': 'Order must be a number',
    'number.integer': 'Order must be an integer',
    'number.min': 'Order must be at least 0',
  }),
  is_required: Joi.boolean().default(true),
});

export const removeChallengeFromRoadmapValidation = Joi.object({
  roadmap_id: Joi.string().required().messages({
    'any.required': 'Roadmap ID is required',
  }),
  challenge_id: Joi.string().required().messages({
    'any.required': 'Challenge ID is required',
  }),
});

export const updateRoadmapChallengeValidation = Joi.object({
  roadmap_id: Joi.string().required().messages({
    'any.required': 'Roadmap ID is required',
  }),
  challenge_id: Joi.string().required().messages({
    'any.required': 'Challenge ID is required',
  }),
  order: Joi.number().integer().min(0).messages({
    'number.base': 'Order must be a number',
    'number.integer': 'Order must be an integer',
    'number.min': 'Order must be at least 0',
  }),
  is_required: Joi.boolean(),
});

export const updateRoadmapProgressValidation = Joi.object({
  challenge_id: Joi.string().required().messages({
    'any.required': 'Challenge ID is required',
  }),
});
