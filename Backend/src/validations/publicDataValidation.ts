import Joi from 'joi';

export const publicDataValidation = {
  leaderboardSchema: Joi.object({
    limit: Joi.number().integer().min(1).max(100).default(10),
  }),

  collegeSchema: Joi.object({
    limit: Joi.number().integer().min(1).max(100).default(10),
  }),

  collegeSearchSchema: Joi.object({
    query: Joi.string().required().min(2),
    limit: Joi.number().integer().min(1).max(100).default(10),
  }),
};
