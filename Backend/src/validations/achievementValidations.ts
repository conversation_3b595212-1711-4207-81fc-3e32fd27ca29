import Joi from 'joi';

export const createAchievementValidation = Joi.object({
  title: Joi.string().required(),
  description: Joi.string().required(),
  icon: Joi.string()
    .valid('trophy', 'star', 'fire', 'award', 'target', 'book')
    .required(),
  rarity: Joi.string()
    .valid('common', 'uncommon', 'rare', 'epic', 'legendary')
    .default('common'),
});

export const achievementIdValidation = Joi.object({
  id: Joi.string().required(),
});

export const achievementQueryValidation = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(10),
  page: Joi.number().integer().min(1).default(1),
  rarity: Joi.string()
    .valid('common', 'uncommon', 'rare', 'epic', 'legendary')
    .optional(),
});
