import Joi from 'joi';

export const leaderboardQuerySchema = Joi.object({
  subject_id: Joi.string().uuid(),
  challenge_id: Joi.string().uuid(),
  language: Joi.string().valid(
    'javascript',
    'python',
    'java',
    'cpp',
    'c',
    'csharp',
    'go',
    'ruby',
    'swift',
    'kotlin',
    'rust',
    'typescript',
  ),
  time_range: Joi.string()
    .valid('daily', 'weekly', 'monthly', 'all')
    .default('all'),
  limit: Joi.number().integer().min(1).max(100).default(10),
  page: Joi.number().integer().min(1).default(1),
}).or('subject_id', 'challenge_id');

export const languageLeaderboardSchema = Joi.object({
  challenge_id: Joi.string().uuid().required(),
  language: Joi.string()
    .required()
    .valid(
      'javascript',
      'python',
      'java',
      'cpp',
      'c',
      'csharp',
      'go',
      'ruby',
      'swift',
      'kotlin',
      'rust',
      'typescript',
    ),
  limit: Joi.number().integer().min(1).max(100).default(10),
  page: Joi.number().integer().min(1).default(1),
});

export const timeBasedLeaderboardSchema = Joi.object({
  time_range: Joi.string()
    .valid('daily', 'weekly', 'monthly', 'all')
    .default('all'),
  limit: Joi.number().integer().min(1).max(100).default(10),
  page: Joi.number().integer().min(1).default(1),
});

export const weeklyLeaderboardSchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(10),
});
