import Joi from 'joi';

export const createActivityValidation = Joi.object({
  type: Joi.string()
    .valid(
      'completed_topic',
      'enrolled_roadmap',
      'completed_roadmap',
      'created_roadmap',
      'liked_roadmap',
      'commented',
      'bookmarked',
      'friend_activity',
    )
    .required(),
  description: Joi.string().required(),
  roadmap_id: Joi.string().optional(),
  roadmap_title: Joi.string().optional(),
});

export const activityIdValidation = Joi.object({
  id: Joi.string().required(),
});

export const activityQueryValidation = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(10),
  page: Joi.number().integer().min(1).default(1),
  type: Joi.string()
    .valid(
      'completed_topic',
      'enrolled_roadmap',
      'completed_roadmap',
      'created_roadmap',
      'liked_roadmap',
      'commented',
      'bookmarked',
      'friend_activity',
    )
    .optional(),
});
