import Joi from 'joi';

export const challengeBoilerplateValidation = Joi.object({
  language: Joi.string().required().min(1).messages({
    'string.empty': 'Language is required',
    'string.min': 'Language must be at least 1 character long',
    'any.required': 'Language is required',
  }),
  boilerplate_code: Joi.string().required().min(1).messages({
    'string.empty': 'Boilerplate code is required',
    'string.min': 'Boilerplate code must be at least 1 character long',
    'any.required': 'Boilerplate code is required',
  }),
});
