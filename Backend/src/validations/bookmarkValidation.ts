import Joi from 'joi';

// Bookmark validation schemas
export const bookmarkChallengeValidation = Joi.object({
  collectionId: Joi.string().uuid().optional().allow(null),
});

export const toggleBookmarkValidation = Joi.object({
  collectionId: Joi.string().uuid().optional().allow(null),
});

export const batchCreateBookmarksValidation = Joi.object({
  challengeIds: Joi.array()
    .items(Joi.string().uuid())
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one challenge ID is required',
      'any.required': 'Challenge IDs are required',
    }),
  collectionId: Joi.string().uuid().optional().allow(null),
});

export const batchDeleteBookmarksValidation = Joi.object({
  challengeIds: Joi.array()
    .items(Joi.string().uuid())
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one challenge ID is required',
      'any.required': 'Challenge IDs are required',
    }),
});

export const updateBookmarkCollectionValidation = Joi.object({
  collectionId: Joi.string().uuid().allow(null).required().messages({
    'any.required': 'Collection ID is required',
  }),
});

// Collection validation schemas
export const createCollectionValidation = Joi.object({
  name: Joi.string().min(1).max(100).required().messages({
    'string.empty': 'Collection name is required',
    'string.min': 'Collection name must be at least 1 character',
    'string.max': 'Collection name cannot exceed 100 characters',
    'any.required': 'Collection name is required',
  }),
  description: Joi.string().max(500).optional().allow('').messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
});

export const updateCollectionValidation = Joi.object({
  name: Joi.string().min(1).max(100).optional().messages({
    'string.empty': 'Collection name cannot be empty',
    'string.min': 'Collection name must be at least 1 character',
    'string.max': 'Collection name cannot exceed 100 characters',
  }),
  description: Joi.string().max(500).optional().allow('').messages({
    'string.max': 'Description cannot exceed 500 characters',
  }),
});

export const addBookmarksValidation = Joi.object({
  bookmarkIds: Joi.array()
    .items(Joi.string().uuid())
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one bookmark ID is required',
      'any.required': 'Bookmark IDs are required',
    }),
});

export const moveBookmarksValidation = Joi.object({
  bookmarkIds: Joi.array()
    .items(Joi.string().uuid())
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one bookmark ID is required',
      'any.required': 'Bookmark IDs are required',
    }),
});
