import Joi from 'joi';

export const executeCodeValidation = Joi.object({
  code: Joi.string().required().messages({
    'any.required': 'Code is required',
    'string.empty': 'Code cannot be empty',
  }),
  language: Joi.string()
    .valid(
      'javascript',
      'python',
      'java',
      'cpp',
      'c',
      'csharp',
      'go',
      'ruby',
      'swift',
      'kotlin',
      'rust',
      'typescript',
    )
    .required()
    .messages({
      'any.required': 'Language is required',
      'any.only': 'Invalid language',
    }),
  input: Joi.string().allow('').optional(),
  timeLimit: Joi.number().integer().min(100).max(10000).optional().messages({
    'number.base': 'Time limit must be a number',
    'number.min': 'Time limit must be at least 100ms',
    'number.max': 'Time limit cannot exceed 10000ms',
  }),
  memoryLimit: Joi.number().integer().min(1).max(1024).optional().messages({
    'number.base': 'Memory limit must be a number',
    'number.min': 'Memory limit must be at least 1MB',
    'number.max': 'Memory limit cannot exceed 1024MB',
  }),
  testCases: Joi.array()
    .items(
      Joi.object({
        input: Joi.string().allow('').required(),
        expectedOutput: Joi.string().allow('').required(),
      }),
    )
    .optional(),
});

export const supportedLanguages = [
  { id: 'javascript', name: 'JavaScript', version: 'Node.js 16' },
  { id: 'python', name: 'Python', version: '3.9' },
  { id: 'java', name: 'Java', version: '17' },
  { id: 'cpp', name: 'C++', version: 'C++17' },
  { id: 'c', name: 'C', version: 'C11' },
  { id: 'csharp', name: 'C#', version: '10' },
  { id: 'go', name: 'Go', version: '1.18' },
  { id: 'ruby', name: 'Ruby', version: '3.1' },
  { id: 'swift', name: 'Swift', version: '5.6' },
  { id: 'kotlin', name: 'Kotlin', version: '1.7' },
  { id: 'rust', name: 'Rust', version: '1.60' },
  { id: 'typescript', name: 'TypeScript', version: '4.7' },
];
