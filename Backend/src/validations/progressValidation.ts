import Joi from 'joi';

export const updateProgressValidation = Joi.object({
  topicId: Joi.string().required().messages({
    'any.required': 'Topic ID is required',
  }),
  status: Joi.string().valid('completed', 'in_progress').required().messages({
    'any.required': 'Status is required',
    'any.only': 'Invalid status',
  }),
  score: Joi.number().optional().integer().min(0).max(100).messages({
    'number.base': 'Score must be a number',
    'number.min': 'Score must be between 0 and 100',
    'number.max': 'Score must be between 0 and 100',
  }),
  timeSpent: Joi.number().optional().integer().min(0).messages({
    'number.base': 'Time spent must be a number',
    'number.min': 'Time spent must be a positive number',
  }),
});

export const trackActivityValidation = Joi.object({
  activity_type: Joi.string()
    .valid(
      'TOPIC_COMPLETION',
      'QUIZ_COMPLETION',
      'CODE_CHALLENGE',
      'RESOURCE_STUDY',
      'PRACTICE_SESSION',
    )
    .required()
    .messages({
      'any.required': 'Activity type is required',
      'any.only': 'Invalid activity type',
    }),
  minutes_spent: Joi.number().integer().min(1).required().messages({
    'any.required': 'Minutes spent is required',
    'number.base': 'Minutes spent must be a number',
    'number.min': 'Minutes spent must be at least 1',
  }),
});

export const activityTimelineValidation = Joi.object({
  days: Joi.number().integer().min(1).max(365).default(30).messages({
    'number.base': 'Days must be a number',
    'number.min': 'Days must be at least 1',
    'number.max': 'Days cannot exceed 365',
  }),
});
