import Joi from 'joi';

export const userInsertionSchema = Joi.object({
  username: Joi.string().required(),
  full_name: Joi.string().trim().min(2).max(100).required().messages({
    'string.empty': 'Full name is required',
    'string.min': 'Full name must be at least 2 characters',
    'string.max': 'Full name cannot exceed 100 characters',
  }),
  dob: Joi.date().iso().max('now').optional().messages({
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
    'date.max': 'Date cannot be in the future',
  }),
  gender: Joi.string().valid('male', 'female', 'other').optional().messages({
    'any.only': 'Gender must be either male, female, or other',
  }),
  mobile: Joi.string()
    .pattern(/^(?:\+91|0)?[6-9]\d{9}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Must be a valid 10-digit Indian phone number',
    }),
  address: Joi.string().optional().allow(''),
  university: Joi.string().optional(),
  college: Joi.string().optional().allow(''),
  branch: Joi.string().optional(),
  semester: Joi.number().integer().min(1).max(8).optional().messages({
    'number.base': 'Semester must be a number between 1 and 8',
    'number.min': 'Semester cannot be less than 1',
    'number.max': 'Semester cannot exceed 8',
  }),
  skills: Joi.array()
    .items(Joi.string().trim().min(2).max(50))
    .optional()
    .allow(null)
    .default([])
    .messages({
      'array.base': 'Skills must be an array of strings',
      'string.min': 'Each skill must be at least 2 characters',
      'string.max': 'Each skill cannot exceed 50 characters',
    }),
}).options({ abortEarly: false, allowUnknown: false });
