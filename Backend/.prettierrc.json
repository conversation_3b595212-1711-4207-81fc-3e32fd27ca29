{"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "all", "printWidth": 80, "endOfLine": "lf", "jsxSingleQuote": true, "arrowParens": "always", "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^@prisma/(.*)$", "^@/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderParserPlugins": ["typescript", "decorators-legacy"], "importOrderGroupNamespaceSpecifiers": true, "importOrderCaseInsensitive": true}