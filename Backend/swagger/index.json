{"openapi": "3.0.3", "info": {"title": "MrEngineer API", "version": "1.0.0", "description": "Production API for MrEngineer platform"}, "servers": [{"url": "/api/v1", "description": "Base API path"}], "paths": {"/battles": {"get": {"summary": "List battles", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 10}}, {"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "string", "enum": ["UPCOMING", "IN_PROGRESS", "COMPLETED", "CANCELLED", "ARCHIVED"]}}], "responses": {"200": {"description": "OK"}}}, "post": {"summary": "Create battle", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["INSTANT", "SCHEDULED", "TOURNAMENT", "PRACTICE"]}, "difficulty": {"type": "string", "enum": ["EASY", "MEDIUM", "HARD"]}, "topic_id": {"type": "string", "format": "uuid"}, "maxParticipants": {"type": "integer"}, "time_per_question": {"type": "integer"}, "length": {"type": "string", "enum": ["short", "medium", "long"]}, "points_per_question": {"type": "integer"}, "start_time": {"type": "string", "format": "date-time"}, "end_time": {"type": "string", "format": "date-time"}}, "required": ["title", "type", "difficulty", "topic_id"]}}}}, "responses": {"201": {"description": "Created"}}}}, "/battles/{id}": {"get": {"summary": "Get battle details", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "put": {"summary": "Update battle", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"summary": "Delete battle", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}, "/battles/{id}/leaderboard": {"get": {"summary": "Get battle leaderboard", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/battles/statistics": {"get": {"summary": "Get battle zone statistics", "parameters": [{"name": "userId", "in": "query", "required": false, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK"}}}}}}