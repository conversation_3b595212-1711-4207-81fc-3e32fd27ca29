{"compilerOptions": {"outDir": "dist", "rootDir": "src", "module": "commonjs", "target": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "alwaysStrict": false, "skipLibCheck": true, "moduleResolution": "node", "experimentalDecorators": true, "sourceMap": true, "resolveJsonModule": true, "baseUrl": "./src", "paths": {"@/*": ["*"]}}, "ts-node": {"esm": true, "experimentalSpecifierResolution": "node", "transpileOnly": true, "require": ["tsconfig-paths/register"], "compilerOptions": {"module": "commonjs"}, "files": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}