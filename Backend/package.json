{"name": "mr_engineers_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "format:check": "prettier --check .", "start": "node -r tsconfig-paths/register dist/index.js", "dev": "nodemon --watch 'src/**/*.ts' --exec 'tsx' src/index.ts", "build": "npm run format:check && tsc && tsc-alias", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write .", "sync:roles": "tsx ./src/scripts/runSeeder.ts", "seed:all": "npm run seed:roles && npm run seed:features && npm run seed:permissions", "seed:roles": "tsx ./prisma/seeders/role.seeder.ts", "seed:features": "tsx ./prisma/seeders/feature.seeder.ts", "seed:permissions": "tsx ./prisma/seeders/permission.seeder.ts", "seed:users": "tsx ./prisma/seeders/user.seeder.ts", "seed:subjects": "tsx ./prisma/seeders/subject.seeder.ts", "seed:topics": "tsx ./prisma/seeders/topic.seeder.ts", "seed:challenges": "tsx ./prisma/seeders/challenge.seeder.ts", "seed:challengeExamples": "tsx ./prisma/seeders/challengeExample.seeder.ts", "seed:challengeTestCases": "tsx ./prisma/seeders/challengeTestCase.seeder.ts", "seed:challengeBoilerplates": "tsx ./prisma/seeders/challengeBoilerplate.seeder.ts", "seed:articles": "tsx ./prisma/seeders/article.seeder.ts", "seed:resources": "tsx ./prisma/seeders/resource.seeder.ts", "seed:achievements": "tsx ./prisma/seeders/achievement.seeder.ts", "seed:dailyTopics": "tsx ./prisma/seeders/dailyTopic.seeder.ts", "seed:battles": "tsx ./prisma/seeders/battle.seeder.ts", "seed:roadmaps": "tsx ./prisma/seeders/roadmap.seeder.ts", "seed:roadmapMainConceptSubjectTopics": "tsx ./prisma/seeders/roadmapMainConceptSubjectTopics.seeder.ts", "seed:colleges": "tsx ./prisma/seeders/college.seeder.ts", "postinstall": "prisma generate", "start:prod": "node -r module-alias/register -r dotenv/config dist/index.js", "build:clean": "rm -rf dist && npm run build", "prisma:reset": "prisma migrate reset && npm run seed:all", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "verify": "npm run lint && npm run test", "deploy:prepare": "npm run build:clean && npm run verify"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "_moduleAliases": {"@": "dist", "@utils": "dist/utils", "@types": "dist/types", "@controllers": "dist/controllers", "@repositories": "dist/repositories", "@middlewares": "dist/middlewares", "@services": "dist/services", "@config": "dist/config", "@lib": "dist/lib", "@routes": "dist/routes"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.8.2", "@radix-ui/react-separator": "^1.1.2", "@sentry/node": "^9.9.0", "@supabase/supabase-js": "^2.48.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/compression": "^1.7.5", "@types/luxon": "^3.6.2", "@types/request-ip": "^0.0.41", "@types/socket.io": "^3.0.2", "axios": "^1.7.2", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "bull": "^4.16.5", "cloudinary": "^2.2.0", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "email-validator": "^2.0.4", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.0", "express-validator": "^7.1.0", "framer-motion": "^12.6.3", "helmet": "^8.0.0", "hpp": "^0.2.3", "http-status-codes": "^2.3.0", "ioredis": "^5.4.2", "jest-mock-extended": "^4.0.0-beta1", "joi": "^17.13.3", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "luxon": "^3.6.1", "ml": "^6.0.0", "ml-logistic-regression": "^2.0.0", "module-alias": "^2.2.3", "mongodb": "^6.5.0", "mongoose": "^8.3.3", "multer": "^1.4.5-lts.1", "mysql": "^2.18.1", "mysql2": "^3.11.0", "node-schedule": "^2.1.1", "nodemailer": "^6.10.0", "openapi-types": "^12.1.3", "pg": "^8.13.0", "prom-client": "^15.1.3", "rate-limit-redis": "^4.2.0", "rate-limiter-flexible": "^7.0.0", "request-ip": "^3.3.0", "sharp": "^0.33.5", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0", "winston": "^3.13.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@eslint/js": "^9.18.0", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/csurf": "^1.11.5", "@types/express": "^5.0.0", "@types/hpp": "^0.2.6", "@types/ioredis": "^5.0.0", "@types/jest": "^29.5.14", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.13.5", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "globals": "^15.14.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "prettier": "^3.4.2", "prima": "^0.0.1", "prisma": "^6.8.2", "supertest": "^6.3.4", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsc-alias": "^1.8.15", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "typescript": "^5.7.3", "typescript-eslint": "^8.21.0"}}