# Public API Documentation

This document outlines the public API endpoints available for the landing page and other public-facing components of the application.

## Overview

The public API provides access to non-sensitive data that can be displayed to unauthenticated users. These endpoints are:

- Cached to improve performance
- Do not require authentication
- Return only aggregated or non-sensitive data

## Base URL

All API endpoints are relative to the base URL:

```
/api/public
```

## Endpoints

### 1. Weekly Leaderboard

Retrieves the top performers on the platform for the current week.

#### Request

```
GET /api/public/leaderboard
```

#### Query Parameters

| Parameter | Type    | Required | Default | Description                                |
|-----------|---------|----------|---------|--------------------------------------------|
| limit     | integer | No       | 10      | Number of leaderboard entries to return    |

#### Response

```json
{
  "success": true,
  "message": "Leaderboard retrieved successfully",
  "data": [
    {
      "id": "user-uuid-1",
      "name": "<PERSON><PERSON>",
      "college": "IIT Bombay",
      "points": 9850,
      "rank": 1,
      "avatar_url": "/avatars/user1.png"
    },
    {
      "id": "user-uuid-2",
      "name": "Priya Patel",
      "college": "BITS Pilani",
      "points": 9200,
      "rank": 2,
      "avatar_url": "/avatars/user2.png"
    },
    {
      "id": "user-uuid-3",
      "name": "Amit Kumar",
      "college": "IIT Delhi",
      "points": 8750,
      "rank": 3,
      "avatar_url": "/avatars/user3.png"
    }
    // ... more entries
  ],
  "error": null,
  "meta": null
}
```

#### Caching

This endpoint is cached for 60 seconds (1 minute) to improve performance.

### 2. Platform Statistics

Retrieves aggregated statistics about the platform.

#### Request

```
GET /api/public/stats
```

#### Response

```json
{
  "success": true,
  "message": "Platform statistics retrieved successfully",
  "data": {
    "learningPaths": {
      "stats": [
        {
          "label": "Active Users",
          "value": "10,000+",
          "icon": "FaUsers"
        },
        {
          "label": "Roadmaps",
          "value": "50+",
          "icon": "FaRoad"
        },
        {
          "label": "Company Guides",
          "value": "200+",
          "icon": "FaLaptopCode"
        },
        {
          "label": "Achievements",
          "value": "100+",
          "icon": "FaTrophy"
        }
      ]
    },
    "community": {
      "stats": [
        {
          "label": "Active Users",
          "value": "10,000+",
          "icon": "FaUsers"
        },
        {
          "label": "Challenges",
          "value": "500+",
          "icon": "FaGamepad"
        },
        {
          "label": "Articles",
          "value": "300+",
          "icon": "FaBook"
        },
        {
          "label": "Daily Streaks",
          "value": "500+",
          "icon": "FaFire"
        }
      ]
    },
    "battleZone": {
      "stats": [
        {
          "label": "Battles",
          "value": "1,000+",
          "icon": "FaGamepad"
        },
        {
          "label": "Participants",
          "value": "5,000+",
          "icon": "FaUsers"
        },
        {
          "label": "Challenges",
          "value": "500+",
          "icon": "FaCode"
        },
        {
          "label": "Winners",
          "value": "300+",
          "icon": "FaTrophy"
        }
      ]
    }
  },
  "error": null,
  "meta": null
}
```

#### Caching

This endpoint is cached for 300 seconds (5 minutes) to improve performance.

## Error Responses

### Invalid Request

```json
{
  "success": false,
  "message": "Invalid limit parameter. Must be a number between 1 and 100.",
  "data": null,
  "error": "Invalid request",
  "meta": null
}
```

### Server Error

```json
{
  "success": false,
  "message": "Internal server error",
  "data": null,
  "error": "An unexpected error occurred.",
  "meta": null
}
```

## Usage in Frontend

These endpoints are designed to be used by the landing page and other public-facing components of the application. The frontend should implement proper error handling and fallback mechanisms in case the API is unavailable.

Example usage in React:

```typescript
import { publicDataService } from '@/services/publicDataService';

// In a React component
const [leaderboard, setLeaderboard] = useState([]);
const [loading, setLoading] = useState(true);
const [error, setError] = useState(null);

useEffect(() => {
  const fetchLeaderboard = async () => {
    try {
      setLoading(true);
      const data = await publicDataService.getWeeklyLeaderboard(10);
      setLeaderboard(data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch leaderboard:', err);
      setError('Failed to load leaderboard data');
    } finally {
      setLoading(false);
    }
  };

  fetchLeaderboard();
}, []);
```
