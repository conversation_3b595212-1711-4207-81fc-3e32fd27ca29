# Mr Engineers

## Overview

Mr Engineers is a platform for engineers to learn, practice, and showcase their skills. The platform includes roadmaps, challenges, articles, and a community of engineers.

## Features

### Dynamic Landing Page

The landing page now features dynamic content that is fetched from the backend:

- **Weekly Leaderboard**: Displays the top performers on the platform for the current week
- **Platform Statistics**: Shows real-time statistics about the platform, including user count, roadmap count, and more

These dynamic elements enhance the user experience by providing up-to-date information about the platform and its community.

### Public API

The platform now includes a public API that provides access to non-sensitive data for unauthenticated users. This API is used by the landing page to fetch dynamic content.

For more information about the public API, see the [Public API Documentation](Backend/docs/PUBLIC_API.md).

## Architecture

The application is built with:

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript, Prisma
- **Database**: PostgreSQL
- **Caching**: Redis

## Development

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- PostgreSQL
- Redis

### Setup

1. Clone the repository
2. Install dependencies for both frontend and backend
3. Set up environment variables
4. Run the development servers

```bash
# Install dependencies
cd Frontend && npm install
cd ../Backend && npm install

# Run development servers
# Terminal 1
cd Frontend && npm run dev

# Terminal 2
cd Backend && npm run dev
```
