/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    typedRoutes: false,
  },
  devIndicators: {
    autoPrerender: false,
  },
  images: {
    domains: [
      'assets.aceternity.com',
      'images.unsplash.com',
      'www.python.org',
      'example.com',
      'anotherdomain.com',
      'v17.angular.io',
      'developer.mozilla.org',
      'www.php.net',
      'www.java.com',
      'flutter.dev',
    ],
  },
};

export default nextConfig;
