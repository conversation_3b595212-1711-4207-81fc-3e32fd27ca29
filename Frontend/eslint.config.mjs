import { FlatCompat } from '@eslint/eslintrc';

// Use the project root as the base directory for ESLint compatibility
const baseDirectory = process.cwd();

const compat = new FlatCompat({
  baseDirectory,
});

const eslintConfig = [
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  {
    rules: {
      // We'll keep this off for now, but consider enabling it in the future
      'react-hooks/exhaustive-deps': 'off',

      // Enforce naming conventions
      '@typescript-eslint/naming-convention': [
        'error',
        {
          selector: 'variable',
          format: ['camelCase', 'UPPER_CASE', 'PascalCase'],
        },
        {
          selector: 'function',
          format: ['camelCase', 'PascalCase'],
        },
        {
          selector: 'typeLike',
          format: ['PascalCase'],
        },
        {
          selector: 'interface',
          format: ['PascalCase'],
          prefix: ['I'],
        },
      ],

      // Disable ESLint import ordering in favor of <PERSON><PERSON><PERSON>'s @trivago/prettier-plugin-sort-imports
      'import/order': 'off',

      // Enforce consistent component structure
      'react/function-component-definition': [
        'error',
        {
          namedComponents: 'function-declaration',
          unnamedComponents: 'arrow-function',
        },
      ],

      // Enforce consistent JSX usage
      'react/jsx-pascal-case': 'error',
      'react/jsx-no-duplicate-props': 'error',
      'react/jsx-key': 'error',

      // Enforce consistent TypeScript usage
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unused-vars': 'warn',
    },
  },
];

export default eslintConfig;
