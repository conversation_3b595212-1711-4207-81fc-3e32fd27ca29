/**
 * @file cn.ts
 * @description Utility for merging Tailwind CSS classes with proper conflict resolution
 */
import { ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Merges multiple class values into a single className string
 *
 * This utility combines the functionality of clsx (for conditional classes)
 * with tailwind-merge (for handling Tailwind CSS class conflicts).
 *
 * @example
 * // Basic usage
 * cn('text-red-500', 'bg-blue-500') // => 'text-red-500 bg-blue-500'
 *
 * // With conditional classes
 * cn('text-white', isActive && 'bg-blue-500', !isActive && 'bg-gray-500')
 *
 * // With conflicting classes (last one wins)
 * cn('px-2 py-1', 'px-4') // => 'py-1 px-4'
 *
 * @param inputs - Class values to merge
 * @returns Merged className string with conflicts resolved
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}
