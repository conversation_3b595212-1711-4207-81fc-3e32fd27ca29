/**
 * @file apiUtils.ts
 * @description Utility functions for API response handling and null checks
 */

/**
 * Safely extracts data from an API response with proper null checks
 *
 * @param response - The API response object
 * @param dataPath - The path to the data in the response object (e.g., 'data.users')
 * @param defaultValue - The default value to return if the data is not found
 * @returns The extracted data or the default value
 */
export function safelyExtractData<T, D>(
  response: Record<string, unknown> | null | undefined,
  dataPath: string,
  defaultValue: D,
): T | D {
  if (!response) {
    return defaultValue;
  }

  try {
    // Split the path into parts
    const parts = dataPath.split('.');

    // Navigate through the response object
    let result: unknown = response;
    for (const part of parts) {
      // Check if the current part exists in the result
      if (
        result === null ||
        result === undefined ||
        typeof result !== 'object'
      ) {
        return defaultValue;
      }

      result = (result as Record<string, unknown>)[part];

      // If at any point the result becomes null or undefined, return the default value
      if (result === null || result === undefined) {
        return defaultValue;
      }
    }

    return result as T;
  } catch (error) {
    // If any error occurs during extraction, return the default value
    console.error('Error extracting data from API response:', error);
    return defaultValue;
  }
}

/**
 * Safely extracts error message from an API response
 *
 * @param response - The API response object
 * @param defaultMessage - The default message to return if no error message is found
 * @returns The error message
 */
export function extractErrorMessage(
  response: Record<string, unknown> | null | undefined,
  defaultMessage = 'An unexpected error occurred',
): string {
  if (!response) {
    return defaultMessage;
  }

  // Check for error message in common locations
  const message =
    safelyExtractData<string, string>(response, 'message', '') ||
    safelyExtractData<string, string>(response, 'error.message', '') ||
    safelyExtractData<string, string>(response, 'data.message', '') ||
    safelyExtractData<string, string>(response, 'errors.0.message', '');

  return message || defaultMessage;
}

/**
 * Checks if an API response is successful
 *
 * @param response - The API response object
 * @returns Whether the response is successful
 */
export function isSuccessResponse(
  response: Record<string, unknown> | null | undefined,
): boolean {
  if (!response) {
    return false;
  }

  // Check for success flag in common locations
  return (
    safelyExtractData<boolean, boolean>(response, 'success', false) ||
    safelyExtractData<number, number>(response, 'status', 0) === 200 ||
    (safelyExtractData<number, number>(response, 'statusCode', 0) >= 200 &&
      safelyExtractData<number, number>(response, 'statusCode', 0) < 300)
  );
}

/**
 * Safely extracts pagination metadata from an API response
 *
 * @param response - The API response object
 * @returns The pagination metadata or default values
 */
export function extractPaginationMeta(
  response: Record<string, unknown> | null | undefined,
) {
  if (!response) {
    return {
      total: 0,
      currentPage: 1,
      totalPages: 1,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }

  // Try to extract pagination from different possible locations
  const meta =
    safelyExtractData<Record<string, unknown>, null>(
      response,
      'meta.pagination',
      null,
    ) ||
    safelyExtractData<Record<string, unknown>, null>(response, 'meta', null) ||
    safelyExtractData<Record<string, unknown>, null>(
      response,
      'pagination',
      null,
    );

  if (!meta) {
    return {
      total: 0,
      currentPage: 1,
      totalPages: 1,
      hasNextPage: false,
      hasPreviousPage: false,
    };
  }

  // Extract pagination values with fallbacks
  const total = safelyExtractData<number, number>(meta, 'total', 0);
  const currentPage =
    safelyExtractData<number, number>(meta, 'currentPage', 1) ||
    safelyExtractData<number, number>(meta, 'current_page', 1) ||
    1;
  const totalPages =
    safelyExtractData<number, number>(meta, 'totalPages', 1) ||
    safelyExtractData<number, number>(meta, 'total_pages', 1) ||
    safelyExtractData<number, number>(meta, 'lastPage', 1) ||
    safelyExtractData<number, number>(meta, 'last_page', 1) ||
    1;

  // Calculate hasNextPage and hasPreviousPage
  const hasNextPage =
    safelyExtractData<boolean, boolean>(
      meta,
      'hasNextPage',
      currentPage < totalPages,
    ) ||
    safelyExtractData<boolean, boolean>(
      meta,
      'has_next_page',
      currentPage < totalPages,
    ) ||
    currentPage < totalPages;

  const hasPreviousPage =
    safelyExtractData<boolean, boolean>(
      meta,
      'hasPreviousPage',
      currentPage > 1,
    ) ||
    safelyExtractData<boolean, boolean>(
      meta,
      'has_previous_page',
      currentPage > 1,
    ) ||
    currentPage > 1;

  return {
    total,
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
  };
}

/**
 * Safely checks if a response contains data
 *
 * @param response - The API response object
 * @param dataPath - The path to check for data
 * @returns Whether the response contains data at the specified path
 */
export function hasData(
  response: Record<string, unknown> | null | undefined,
  dataPath: string,
): boolean {
  if (!response) {
    return false;
  }

  const data = safelyExtractData<unknown, null>(response, dataPath, null);

  if (data === null || data === undefined) {
    return false;
  }

  if (Array.isArray(data)) {
    return data.length > 0;
  }

  if (typeof data === 'object') {
    return Object.keys(data).length > 0;
  }

  return Boolean(data);
}
