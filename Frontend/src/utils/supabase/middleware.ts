/**
 * @file middleware.ts
 * @description Utility functions for middleware operations
 */
import { type NextRequest, NextResponse } from 'next/server';

import { createServerClient } from '@supabase/ssr';
import axios from 'axios';

// Define role types for better type safety
type UserRole = 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'USER';

// RBAC bypass flag
// TODO: Remove this bypass when RBAC implementation is ready for production
const BYPASS_RBAC = true;

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value),
          );
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options),
          );
        },
      },
    },
  );

  // Do not run code between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  // IMPORTANT: DO NOT REMOVE auth.getUser()

  const {
    data: { session },
  } = await supabase.auth.getSession();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Handle non-authenticated users
  if (!user) {
    // Allow access to landing page, auth pages, and other public pages
    if (
      request.nextUrl.pathname === '/' ||
      request.nextUrl.pathname.startsWith('/auth') ||
      request.nextUrl.pathname === '/about-us' ||
      request.nextUrl.pathname === '/contact-us' ||
      request.nextUrl.pathname === '/faq' ||
      request.nextUrl.pathname === '/blogs' ||
      request.nextUrl.pathname === '/support'
    ) {
      return supabaseResponse;
    } else {
      // Redirect to login for all other routes
      const url = request.nextUrl.clone();
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }
  }

  // Handle authenticated users
  // Enhanced user role extraction with detailed logging
  let determinedUserRoles: UserRole[] = [];
  const currentPath = request.nextUrl.pathname;

  console.log(
    `[Middleware] Path: ${currentPath} - Starting role determination.`,
  );
  console.log(
    `[Middleware] Path: ${currentPath} - User object:`,
    user
      ? { id: user.id, app_metadata: user.app_metadata, email: user.email }
      : null,
  );
  console.log(
    `[Middleware] Path: ${currentPath} - Session object:`,
    session
      ? {
          expires_at: session.expires_at,
          access_token_exists: !!session.access_token,
        }
      : null,
  );

  if (
    user?.app_metadata?.roles &&
    Array.isArray(user.app_metadata.roles) &&
    user.app_metadata.roles.length > 0
  ) {
    determinedUserRoles = user.app_metadata.roles;
    console.log(
      `[Middleware] Path: ${currentPath} - Roles from user.app_metadata:`,
      determinedUserRoles,
    );
  } else if (session?.access_token) {
    console.log(
      `[Middleware] Path: ${currentPath} - user.app_metadata.roles not found or empty, attempting to decode access token.`,
    );
    try {
      const decodedToken = JSON.parse(
        Buffer.from(session.access_token.split('.')[1], 'base64').toString(),
      );
      if (
        decodedToken?.app_metadata?.roles &&
        Array.isArray(decodedToken.app_metadata.roles)
      ) {
        determinedUserRoles = decodedToken.app_metadata.roles;
        console.log(
          `[Middleware] Path: ${currentPath} - Roles from decoded token:`,
          determinedUserRoles,
        );
      } else {
        console.log(
          `[Middleware] Path: ${currentPath} - No valid roles array found in decoded token's app_metadata.`,
        );
      }
    } catch (e: any) {
      console.error(
        `[Middleware] Path: ${currentPath} - Failed to parse access token for roles:`,
        e.message,
      );
    }
  } else {
    console.log(
      `[Middleware] Path: ${currentPath} - No user.app_metadata.roles and no session.access_token to decode for roles.`,
    );
  }

  console.log(
    `[Middleware] Path: ${currentPath} - Final Determined User Roles:`,
    determinedUserRoles,
  );

  const isAdmin = determinedUserRoles.includes('ADMIN');
  const isModerator = determinedUserRoles.includes('MODERATOR');
  const isContributor = determinedUserRoles.includes('CONTRIBUTOR');

  console.log(
    `[Middleware] Path: ${currentPath} - isAdmin: ${isAdmin}, isModerator: ${isModerator}, isContributor: ${isContributor}`,
  );

  console.log('🚀 --------------------------------🚀');
  console.log('🚀 ~ updateSession ~ user:', user);
  console.log('🚀 --------------------------------🚀');
  // Redirect from landing page based on user role
  if (currentPath === '/') {
    console.log(
      `[Middleware] Path: ${currentPath} - Evaluating redirect. isAdmin: ${isAdmin}`,
    );
    const url = request.nextUrl.clone();

    // Role-based redirection
    if (isAdmin) {
      url.pathname = '/admin';
    } else if (isModerator) {
      url.pathname = '/moderator';
    } else if (isContributor) {
      url.pathname = '/contributor';
    } else {
      // Default to regular dashboard for regular users
      url.pathname = '/dashboard';
    }

    return NextResponse.redirect(url);
  }

  // Prevent authenticated users from accessing auth pages
  if (currentPath.startsWith('/auth')) {
    console.log(
      `[Middleware] Path: ${currentPath} - Authenticated user accessing auth page. isAdmin: ${isAdmin}. Redirecting.`,
    );
    const url = request.nextUrl.clone();

    // Role-based redirection
    if (isAdmin) {
      url.pathname = '/admin';
    } else if (isModerator) {
      url.pathname = '/moderator';
    } else if (isContributor) {
      url.pathname = '/contributor';
    } else {
      url.pathname = '/dashboard';
    }

    return NextResponse.redirect(url);
  }

  // Handle role-specific route access
  if (currentPath.startsWith('/admin')) {
    console.log(
      `[Middleware] Path: ${currentPath} - Accessing admin route. isAdmin: ${isAdmin}`,
    );
    // Only admins can access admin routes
    if (!isAdmin && !BYPASS_RBAC) {
      // TODO: Remove BYPASS_RBAC condition when RBAC implementation is ready for production
      console.log(`[Middleware] RBAC bypass enabled: Allowing access to admin route`);
      const url = request.nextUrl.clone();

      // Redirect to appropriate dashboard based on role
      if (isModerator) {
        url.pathname = '/moderator';
      } else if (isContributor) {
        url.pathname = '/contributor';
      } else {
        url.pathname = '/dashboard';
      }

      return NextResponse.redirect(url);
    }
  }

  // Handle moderator route access
  if (currentPath.startsWith('/moderator')) {
    console.log(
      `[Middleware] Path: ${currentPath} - Accessing moderator route. isAdmin: ${isAdmin}, isModerator: ${isModerator}`,
    );
    // Only admins and moderators can access moderator routes
    if (!isAdmin && !isModerator && !BYPASS_RBAC) {
      // TODO: Remove BYPASS_RBAC condition when RBAC implementation is ready for production
      console.log(`[Middleware] RBAC bypass enabled: Allowing access to moderator route`);
      const url = request.nextUrl.clone();

      // Redirect to appropriate dashboard based on role
      if (isContributor) {
        url.pathname = '/contributor';
      } else {
        url.pathname = '/dashboard';
      }

      return NextResponse.redirect(url);
    }
  }

  // Handle contributor route access
  if (currentPath.startsWith('/contributor')) {
    console.log(
      `[Middleware] Path: ${currentPath} - Accessing contributor route. isAdmin: ${isAdmin}, isModerator: ${isModerator}, isContributor: ${isContributor}`,
    );
    // Only admins, moderators, and contributors can access contributor routes
    if (!isAdmin && !isModerator && !isContributor && !BYPASS_RBAC) {
      // TODO: Remove BYPASS_RBAC condition when RBAC implementation is ready for production
      console.log(`[Middleware] RBAC bypass enabled: Allowing access to contributor route`);
      const url = request.nextUrl.clone();
      url.pathname = '/dashboard';
      return NextResponse.redirect(url);
    }
  }
  // Redirect to details page if user data is incomplete
  if (currentPath !== '/details') {
    console.log(
      `[Middleware] Path: ${currentPath} - Checking user data completeness from /users/me.`,
    );
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/users/me`,
        {
          headers: {
            Authorization: `Bearer ${session?.access_token}`,
          },
        },
      );

      if (!response?.data?.data?.user) {
        // This structure depends on your API's response
        console.log(
          `[Middleware] Path: ${currentPath} - User data from /users/me is INCOMPLETE. Redirecting to /details. Response data:`,
          response?.data,
        );
        const url = request.nextUrl.clone();
        url.pathname = '/details';
        return NextResponse.redirect(url);
      }
    } catch (error: any) {
      console.error(
        `[Middleware] Path: ${currentPath} - Error fetching user data from /users/me:`,
        error.message,
      );
      // console.error('Original error fetching user data:', error); // Keep original if needed for more detail
      // If API call fails, still allow the user to continue
    }
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse;
}
