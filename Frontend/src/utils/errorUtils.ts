/**
 * @file errorUtils.ts
 * @description Utility functions for error handling and error message extraction
 */

/**
 * Extract error message from an error object or response
 *
 * @param error - The error object or response
 * @param defaultMessage - Default message to return if no error message is found
 * @returns The extracted error message or the default message
 */
export function extractErrorMessage(
  error: unknown,
  defaultMessage = 'An unexpected error occurred. Please try again.',
): string {
  if (!error) {
    return defaultMessage;
  }

  try {
    // Handle axios error responses
    if (typeof error === 'object' && error !== null) {
      // Check for axios error response format
      if (
        'response' in error &&
        error.response &&
        typeof error.response === 'object'
      ) {
        const response = error.response as Record<string, unknown>;

        // Check for error message in response data
        if (
          'data' in response &&
          response.data &&
          typeof response.data === 'object'
        ) {
          const data = response.data as Record<string, unknown>;

          // Check for message in data
          if ('message' in data && typeof data.message === 'string') {
            return data.message;
          }

          // Check for error in data
          if ('error' in data && typeof data.error === 'string') {
            return data.error;
          }
        }

        // Check for status text
        if (
          'statusText' in response &&
          typeof response.statusText === 'string'
        ) {
          return response.statusText;
        }
      }

      // Check for message property directly on error
      if ('message' in error && typeof error.message === 'string') {
        return error.message;
      }
    }

    // Handle string errors
    if (typeof error === 'string') {
      return error;
    }

    // Handle error as Record<string, unknown>
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>;

      if ('message' in errorObj && typeof errorObj.message === 'string') {
        return errorObj.message;
      }

      if ('error' in errorObj && typeof errorObj.error === 'string') {
        return errorObj.error;
      }
    }

    return defaultMessage;
  } catch (e) {
    console.error('Error extracting error message:', e);
    return defaultMessage;
  }
}
