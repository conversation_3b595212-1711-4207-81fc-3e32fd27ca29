/**
 * @file retryUtils.ts
 * @description Utility functions for handling API retries
 */

/**
 * Interface for retry configuration
 */
export interface IRetryConfig {
  /** Maximum number of retry attempts */
  maxRetries: number;
  /** Initial delay in milliseconds before the first retry */
  initialDelay: number;
  /** Factor by which to increase delay with each retry attempt */
  backoffFactor: number;
  /** Maximum delay in milliseconds between retries */
  maxDelay: number;
  /** Optional callback to determine if a retry should be attempted based on the error */
  shouldRetry?: (error: unknown) => boolean;
  /** Optional callback to execute before each retry attempt */
  onRetry?: (attempt: number, delay: number, error: unknown) => void;
}

/**
 * Default retry configuration
 */
export const DEFAULT_RETRY_CONFIG: IRetryConfig = {
  maxRetries: 3,
  initialDelay: 1000, // 1 second
  backoffFactor: 2,
  maxDelay: 30000, // 30 seconds
  shouldRetry: (error: unknown) => {
    // By default, retry on network errors or 5xx server errors
    if (error instanceof Error) {
      // Network errors
      if (
        error.message.includes('Network Error') ||
        error.message.includes('timeout') ||
        error.message.includes('connection')
      ) {
        return true;
      }
    }

    // Check for HTTP status codes (assuming error might be an Axios error)
    if (error && typeof error === 'object' && 'status' in error) {
      const status = (error as { status: number }).status;
      // Retry on server errors (5xx) but not on client errors (4xx)
      return status >= 500 && status < 600;
    }

    return false;
  },
  onRetry: (attempt, delay, error) => {
    console.warn(
      `Retry attempt ${attempt} after ${delay}ms due to error:`,
      error,
    );
  },
};

/**
 * Calculate the delay for the next retry attempt using exponential backoff
 *
 * @param attempt - Current retry attempt (1-based)
 * @param config - Retry configuration
 * @returns Delay in milliseconds
 */
export function calculateBackoffDelay(
  attempt: number,
  config: IRetryConfig,
): number {
  const delay = Math.min(
    config.initialDelay * Math.pow(config.backoffFactor, attempt - 1),
    config.maxDelay,
  );

  // Add some jitter to prevent all clients retrying simultaneously
  const jitter = Math.random() * 0.2 * delay; // 20% jitter
  return Math.floor(delay + jitter);
}

/**
 * Sleep for a specified duration
 *
 * @param ms - Duration in milliseconds
 * @returns Promise that resolves after the specified duration
 */
export function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Retry a function with exponential backoff
 *
 * @param fn - Function to retry
 * @param config - Retry configuration
 * @returns Promise that resolves with the result of the function or rejects with the last error
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  config: Partial<IRetryConfig> = {},
): Promise<T> {
  // Merge with default config
  const retryConfig: IRetryConfig = {
    ...DEFAULT_RETRY_CONFIG,
    ...config,
  };

  let lastError: unknown;
  let isCancelled = false;

  // Create a cancel function that can be called from outside
  const cancel = () => {
    isCancelled = true;
  };

  // Add cancel function to the promise
  const promise = new Promise<T>(async (resolve, reject) => {
    for (let attempt = 1; attempt <= retryConfig.maxRetries + 1; attempt++) {
      // Check if retry operation was cancelled
      if (isCancelled) {
        const cancelError = new Error('Retry operation cancelled');
        reject(cancelError);
        return;
      }

      try {
        // Attempt to execute the function
        const result = await fn();
        resolve(result);
        return;
      } catch (error) {
        lastError = error;

        // Check if we've exhausted all retry attempts
        if (attempt > retryConfig.maxRetries) {
          reject(error);
          return;
        }

        // Check if we should retry based on the error
        if (retryConfig.shouldRetry && !retryConfig.shouldRetry(error)) {
          reject(error);
          return;
        }

        // Calculate delay for this retry attempt
        const delay = calculateBackoffDelay(attempt, retryConfig);

        // Execute onRetry callback if provided
        if (retryConfig.onRetry) {
          retryConfig.onRetry(attempt, delay, error);
        }

        // Wait before retrying
        await sleep(delay);
      }
    }

    // This should never be reached due to the reject in the loop,
    // but TypeScript requires a return statement
    reject(lastError);
  }) as Promise<T> & { cancel?: () => void };

  // Attach the cancel function to the promise
  promise.cancel = cancel;

  return promise;
}

/**
 * Create a retryable version of an API function
 *
 * @param apiFn - API function to make retryable
 * @param config - Retry configuration
 * @returns A new function that will retry the API call on failure
 */
export function createRetryableApi<
  T extends (...args: unknown[]) => Promise<unknown>,
>(apiFn: T, config: Partial<IRetryConfig> = {}): T {
  // Create a function with the same signature as the original
  const retryableFn = (...args: Parameters<T>): ReturnType<T> => {
    return retryWithBackoff(() => apiFn(...args), config) as ReturnType<T>;
  };

  // Cast is necessary because TypeScript can't verify that the signatures match exactly
  return retryableFn as unknown as T;
}

// Create a default export object
const retryUtils = {
  retryWithBackoff,
  createRetryableApi,
  DEFAULT_RETRY_CONFIG,
};

export default retryUtils;
