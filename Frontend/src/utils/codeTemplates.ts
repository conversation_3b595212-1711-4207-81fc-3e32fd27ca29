/**
 * @file codeTemplates.ts
 * @description Utility functions for codetemplates operations
 */
// Default code templates for different languages
export const getDefaultCode = (language: string): string => {
  const templates: Record<string, string> = {
    javascript: `/**
 * @param {*} input - The input parameter for your function
 * @return {*} - Your result
 */
function solution(input) {
  // Write your code here
  
  return input;
}

// Don't modify this line
export default solution;`,

    python: `def solution(input):
    """
    :param input: The input parameter for your function
    :return: Your result
    """
    # Write your code here
    
    return input`,

    java: `public class Solution {
    /**
     * @param input The input parameter for your function
     * @return Your result
     */
    public static Object solution(Object input) {
        // Write your code here
        
        return input;
    }
}`,

    cpp: `#include <iostream>
#include <vector>
#include <string>
using namespace std;

/**
 * @param input The input parameter for your function
 * @return Your result
 */
auto solution(auto input) {
    // Write your code here
    
    return input;
}`,

    c: `#include <stdio.h>
#include <stdlib.h>

/**
 * @param input The input parameter for your function
 * @return Your result
 */
void* solution(void* input) {
    // Write your code here
    
    return input;
}`,

    csharp: `using System;
using System.Collections.Generic;

public class Solution {
    /**
     * @param input The input parameter for your function
     * @return Your result
     */
    public static object solution(object input) {
        // Write your code here
        
        return input;
    }
}`,

    go: `package main

import (
    "fmt"
)

/**
 * @param input The input parameter for your function
 * @return Your result
 */
func solution(input interface{}) interface{} {
    // Write your code here
    
    return input
}`,

    ruby: `# @param input The input parameter for your function
# @return Your result
def solution(input)
  # Write your code here
  
  return input
end`,

    rust: `/**
 * @param input The input parameter for your function
 * @return Your result
 */
fn solution<T>(input: T) -> T {
    // Write your code here
    
    input
}`,

    typescript: `/**
 * @param input - The input parameter for your function
 * @return - Your result
 */
function solution(input: any): any {
  // Write your code here
  
  return input;
}

// Don't modify this line
export default solution;`,

    php: `<?php
/**
 * @param mixed $input The input parameter for your function
 * @return mixed Your result
 */
function solution($input) {
    // Write your code here
    
    return $input;
}
?>`,

    swift: `/**
 * @param input The input parameter for your function
 * @return Your result
 */
func solution(input: Any) -> Any {
    // Write your code here
    
    return input
}`,

    kotlin: `/**
 * @param input The input parameter for your function
 * @return Your result
 */
fun solution(input: Any): Any {
    // Write your code here
    
    return input
}`,
  };

  return templates[language.toLowerCase()] || templates.javascript;
};

// Get language display name
export const getLanguageDisplayName = (language: string): string => {
  const displayNames: Record<string, string> = {
    javascript: 'JavaScript',
    python: 'Python',
    java: 'Java',
    cpp: 'C++',
    c: 'C',
    csharp: 'C#',
    go: 'Go',
    ruby: 'Ruby',
    rust: 'Rust',
    typescript: 'TypeScript',
    php: 'PHP',
    swift: 'Swift',
    kotlin: 'Kotlin',
  };

  return displayNames[language.toLowerCase()] || language;
};

// Get file extension for each language
export const getFileExtension = (language: string): string => {
  const extensions: Record<string, string> = {
    javascript: 'js',
    python: 'py',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    csharp: 'cs',
    go: 'go',
    ruby: 'rb',
    rust: 'rs',
    typescript: 'ts',
    php: 'php',
    swift: 'swift',
    kotlin: 'kt',
  };

  return extensions[language.toLowerCase()] || 'txt';
};

// Get supported languages
export const getSupportedLanguages = (): string[] => {
  return [
    'javascript',
    'python',
    'java',
    'cpp',
    'c',
    'csharp',
    'go',
    'ruby',
    'rust',
    'typescript',
    'php',
    'swift',
    'kotlin',
  ];
};
