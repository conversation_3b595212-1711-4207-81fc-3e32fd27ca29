/**
 * @file axiosInterceptor.ts
 * @description Axios interceptors for handling common API responses like toast messages
 */

import axios from 'axios';
import { toast } from '@/components/ui/use-toast';

/**
 * Setup response interceptor for handling toast messages from the backend
 */
export const setupAxiosInterceptors = () => {
  // Add a response interceptor
  axios.interceptors.response.use(
    (response) => {
      // Check if the response contains toast flag
      if (response.data?.toast === true) {
        const isSuccess = response.data?.success === true;
        const message = response.data?.message || '';
        
        // Show toast based on success status
        toast({
          variant: isSuccess ? 'default' : 'destructive',
          title: isSuccess ? 'Success' : 'Error',
          description: message || (isSuccess ? 'Operation completed successfully' : 'Something went wrong'),
        });
      }
      return response;
    },
    (error) => {
      // Handle error cases
      if (error.response?.data?.toast === true) {
        const message = error.response.data?.message || 'Something went wrong';
        
        toast({
          variant: 'destructive',
          title: 'Error',
          description: message,
        });
      }
      return Promise.reject(error);
    }
  );
};
