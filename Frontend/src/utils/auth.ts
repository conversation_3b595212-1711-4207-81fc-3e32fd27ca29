/**
 * @file auth.ts
 * @description Utility functions for auth operations
 */
import { toast } from 'react-toastify';

import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

import { clearUser } from '@/lib/features/user/userSlice';
import { AppDispatch } from '@/lib/store';
import { createClient } from '@/utils/supabase/client';

/**
 * Handles user logout functionality
 * @param dispatch Redux dispatch function
 * @param router Next.js router
 * @returns Promise that resolves when logout is complete
 */
export const handleLogout = async (
  dispatch: AppDispatch,
  router: AppRouterInstance,
): Promise<void> => {
  const supabase = createClient();

  try {
    // Sign out from Supabase
    const { error } = await supabase.auth.signOut();
    if (error) throw error;

    // Clear Redux state
    dispatch(clearUser());

    // Clear any cookies
    document.cookie = 'token=; Max-Age=0; path=/;';

    // Redirect to login page
    router.push('/auth/login');

    // Show success message
    toast.success('Logged out successfully');
  } catch (error) {
    console.error('Error during logout:', error);
    toast.error('Error during logout. Please try again.');
  }
};
