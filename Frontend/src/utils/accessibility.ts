/**
 * @file accessibility.ts
 * @description Utility functions for accessibility operations
 */
/**
 * Utility functions for accessibility
 */

/**
 * Creates an object with ARIA attributes for a button that controls a collapsible section
 * @param id - The ID of the controlled element
 * @param expanded - Whether the controlled element is expanded
 * @returns Object with ARIA attributes
 */
export function getAriaControlProps(id: string, expanded: boolean) {
  return {
    'aria-controls': id,
    'aria-expanded': expanded,
  };
}

/**
 * Creates an object with ARIA attributes for a tab
 * @param id - The ID of the tab panel
 * @param selected - Whether the tab is selected
 * @returns Object with ARIA attributes
 */
export function getAriaTabProps(id: string, selected: boolean) {
  return {
    role: 'tab',
    'aria-selected': selected,
    'aria-controls': id,
    id: `tab-${id}`,
    tabIndex: selected ? 0 : -1,
  };
}

/**
 * Creates an object with ARIA attributes for a tab panel
 * @param id - The ID of the tab panel
 * @param selected - Whether the tab panel is selected
 * @returns Object with ARIA attributes
 */
export function getAriaTabPanelProps(id: string, selected: boolean) {
  return {
    role: 'tabpanel',
    'aria-labelledby': `tab-${id}`,
    id,
    tabIndex: 0,
    hidden: !selected,
  };
}

/**
 * Creates an object with ARIA attributes for a dialog
 * @param title - The title of the dialog
 * @returns Object with ARIA attributes
 */
export function getAriaDialogProps(title: string) {
  return {
    role: 'dialog',
    'aria-modal': true,
    'aria-labelledby': title,
  };
}

/**
 * Creates an object with ARIA attributes for a live region
 * @param atomic - Whether the entire region should be considered as a whole
 * @param relevant - What types of changes are relevant
 * @returns Object with ARIA attributes
 */
export function getAriaLiveRegionProps(
  atomic: boolean = false,
  relevant: 'additions' | 'removals' | 'text' | 'all' = 'additions',
) {
  return {
    'aria-live': 'polite',
    'aria-atomic': atomic,
    'aria-relevant': relevant,
  };
}

/**
 * Creates an object with ARIA attributes for a required form field
 * @param id - The ID of the form field
 * @param invalid - Whether the form field is invalid
 * @param errorId - The ID of the error message element
 * @returns Object with ARIA attributes
 */
export function getAriaFormFieldProps(
  id: string,
  invalid: boolean = false,
  errorId?: string,
) {
  return {
    id,
    'aria-required': true,
    'aria-invalid': invalid,
    ...(invalid && errorId && { 'aria-describedby': errorId }),
  };
}

/**
 * Creates an object with ARIA attributes for a button that toggles a feature
 * @param pressed - Whether the button is pressed
 * @param label - The accessible label for the button
 * @returns Object with ARIA attributes
 */
export function getAriaToggleProps(pressed: boolean, label: string) {
  return {
    'aria-pressed': pressed,
    'aria-label': label,
  };
}

/**
 * Creates an object with ARIA attributes for a button that has a visible label
 * @param label - The accessible label for the button
 * @returns Object with ARIA attributes
 */
export function getAriaLabelProps(label: string) {
  return {
    'aria-label': label,
  };
}

/**
 * Creates an object with keyboard event handlers for accessibility
 * @param onActivate - Function to call when the element is activated
 * @returns Object with keyboard event handlers
 */
export function getKeyboardHandlers(onActivate: () => void) {
  return {
    onKeyDown: (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        onActivate();
      }
    },
  };
}

/**
 * Creates an object with keyboard event handlers for escape key
 * @param onEscape - Function to call when escape key is pressed
 * @returns Object with keyboard event handlers
 */
export function getEscapeKeyHandler(onEscape: () => void) {
  return {
    onKeyDown: (e: React.KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        onEscape();
      }
    },
  };
}

/**
 * Creates an object with keyboard event handlers for arrow key navigation
 * @param onNext - Function to call when down/right arrow is pressed
 * @param onPrevious - Function to call when up/left arrow is pressed
 * @param horizontal - Whether navigation is horizontal (true) or vertical (false)
 * @returns Object with keyboard event handlers
 */
export function getArrowKeyHandlers(
  onNext: () => void,
  onPrevious: () => void,
  horizontal: boolean = true,
) {
  return {
    onKeyDown: (e: React.KeyboardEvent) => {
      if (horizontal) {
        if (e.key === 'ArrowRight') {
          e.preventDefault();
          onNext();
        } else if (e.key === 'ArrowLeft') {
          e.preventDefault();
          onPrevious();
        }
      } else {
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          onNext();
        } else if (e.key === 'ArrowUp') {
          e.preventDefault();
          onPrevious();
        }
      }
    },
  };
}

/**
 * Creates a unique ID for accessibility purposes
 * @param prefix - Prefix for the ID
 * @returns A unique ID
 */
export function generateAccessibleId(prefix: string): string {
  return `${prefix}-${Math.random().toString(36).substring(2, 9)}`;
}
