/**
 * @file battle.ts
 * @description Type definitions for battle features
 */
export type BattleStatus =
  | 'pending'
  | 'active'
  | 'completed'
  | 'cancelled'
  | 'UPCOMING'
  | 'IN_PROGRESS';
export type BattleType = 'INSTANT' | 'SCHEDULED' | 'TOURNAMENT' | 'PRACTICE';
export type Difficulty = 'EASY' | 'MEDIUM' | 'HARD';
export type Length = 'short' | 'medium' | 'long';

/**
 * User information for battle participants and creators
 */
export interface IUser {
  /**
   * Unique identifier for the user
   */
  id: string;

  /**
   * Username for display purposes
   */
  username: string;

  /**
   * URL to the user's avatar image
   */
  avatar_url?: string;
}

/**
 * Topic information for battle categorization
 */
export interface ITopic {
  /**
   * Unique identifier for the topic
   */
  id: string;

  /**
   * Display title of the topic
   */
  title: string;

  /**
   * Optional detailed description of the topic
   */
  description?: string;

  /**
   * Optional icon identifier for the topic
   */
  icon?: string;
}

/**
 * Information about a participant in a battle
 */
export interface IBattleParticipant {
  /**
   * Unique identifier for the participant record
   */
  id: string;

  /**
   * ID of the user participating
   */
  userId: string;

  /**
   * ID of the battle being participated in
   */
  battleId: string;

  /**
   * Timestamp when the user joined the battle
   */
  joinedAt: string;

  /**
   * Detailed user information
   */
  user: {
    /**
     * User ID
     */
    id: string;

    /**
     * User's display name
     */
    name: string;

    /**
     * User's email address
     */
    email: string;

    /**
     * User's avatar URL
     */
    avatar?: string;
  };
}

export interface IBattle {
  id: string;
  title: string;
  description: string;
  type: BattleType;
  status: BattleStatus;
  topic_id: string;
  user_id: string;
  difficulty: 'easy' | 'medium' | 'hard';
  length: Length;
  max_participants: number; // Backend uses snake_case
  maxParticipants?: number; // Frontend uses camelCase (for compatibility)
  current_participants: number; // Backend uses snake_case
  currentParticipants?: number; // Frontend uses camelCase (for compatibility)
  prize?: number; // Optional as it might not be set
  start_time: string; // Backend uses snake_case
  startDate?: string; // Frontend uses camelCase (for compatibility)
  end_time: string; // Backend uses snake_case
  endDate?: string; // Frontend uses camelCase (for compatibility)
  points_per_question: number;
  time_per_question: number;
  total_questions: number;
  created_at?: string; // Backend uses snake_case
  createdAt?: string; // Frontend uses camelCase (for compatibility)
  updated_at?: string; // Backend uses snake_case
  updatedAt?: string; // Frontend uses camelCase (for compatibility)
  topic: ITopic;
  user: IUser;
  participants: IBattleParticipant[];
  _count?: {
    participants: number;
    questions: number;
  };
  category?: string; // Optional as it might not be set
  tags?: string[]; // Optional as it might not be set
  creatorId?: string; // Optional as it might be redundant with user_id
}

export interface IBattleQuestion {
  id: string;
  battle_id: string;
  question: string;
  options: string[];
  correct_answer?: string;
  points: number;
  time_limit: number;
  order: number;
}

export interface IBattleFilters {
  search?: string;
  status?: BattleStatus | 'all';
  difficulty?: 'all' | 'easy' | 'medium' | 'hard';
  category?: string;
  length?: 'short' | 'medium' | 'long';
  sortBy?: 'createdAt' | 'participants' | 'prize';
  sortOrder?: 'asc' | 'desc';
  page: number;
  limit: number;
  user_id?: string;
}
