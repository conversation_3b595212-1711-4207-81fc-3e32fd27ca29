/**
 * @file index.ts
 * @description Type definitions for index features
 */
export interface IRole {
  id: string;
  name: string;
  description?: string;
  type: 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'USER';
}

export interface IUser {
  id: string;
  username: string;
  fullName: string;
  email: string;
  avatarUrl: string;
  bio: string;
  address: string;
  githubUrl: string;
  linkedinUrl: string;
  twitterUrl: string;
  websiteUrl: string;
  specialization: string;
  college: string;
  graduationYear: number;
  skills: string[];
  experienceLevel: string;
  roles?: IRole[];
}

export interface IComment {
  id: string;
  content: string;
  created_at: string;
  updated_at: string;
  isLiked: boolean;
  parent_id: string | null;
  roadmap_id: string;
  article_id: string | null;
  user_id: string;
  user: {
    id: string;
    username: string;
    full_name: string;
    avatar_url: string | null;
  };
  replies: Comment[];
  _count: {
    likes: number;
    replies: number;
  };
  depth?: number;
}

export interface IPaginationMeta {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

/**
 * Standard API response interface for all API calls
 */
export interface IApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  meta?: IPaginationMeta;
}

/**
 * Type for APIs that return data directly without the success/message/data wrapper
 * This is a type alias rather than an interface to avoid TypeScript errors
 */
export type IDirectApiResponse<T> = T;
