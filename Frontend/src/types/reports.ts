/**
 * @file reports.ts
 * @description Types for custom reports and report generation
 */

/**
 * Metric interface for report building
 */
export interface IMetric {
  id: string;
  name: string;
  category: string;
  description: string;
}

/**
 * Dimension interface for report building
 */
export interface IDimension {
  id: string;
  name: string;
  category: string;
  description: string;
}

/**
 * Visualization types for reports
 */
export type VisualizationType = 'bar' | 'line' | 'pie' | 'table';

/**
 * Export format types
 */
export type ExportFormat = 'pdf' | 'csv' | 'excel';

/**
 * Schedule frequency types
 */
export type ScheduleFrequency = 'daily' | 'weekly' | 'monthly' | 'quarterly';

/**
 * Visualization interface for reports
 */
export interface IVisualization {
  id: string;
  type: VisualizationType;
  title: string;
  metrics: string[];
  dimensions: string[];
}

/**
 * Schedule interface for reports
 */
export interface IReportSchedule {
  enabled: boolean;
  frequency: ScheduleFrequency;
  recipients: string[];
  exportFormat: ExportFormat;
}

/**
 * Report interface
 */
export interface IReport {
  id: string;
  name: string;
  description: string;
  metrics: string[];
  dimensions: string[];
  visualizations: IVisualization[];
  schedule: IReportSchedule;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

/**
 * Report creation request
 */
export interface IReportCreateRequest {
  name: string;
  description: string;
  metrics: string[];
  dimensions: string[];
  visualizations: Omit<IVisualization, 'id'>[];
  schedule: IReportSchedule;
}

/**
 * Report update request
 */
export interface IReportUpdateRequest {
  id: string;
  name?: string;
  description?: string;
  metrics?: string[];
  dimensions?: string[];
  visualizations?: Omit<IVisualization, 'id'>[];
  schedule?: Partial<IReportSchedule>;
}

/**
 * Report API response
 */
export interface IReportResponse {
  id: string;
  name: string;
  description: string;
  metrics: string[];
  dimensions: string[];
  visualizations: IVisualization[];
  schedule: IReportSchedule;
  created_at: string;
  updated_at: string;
  created_by: {
    id: string;
    name: string;
    email: string;
  };
}

/**
 * Reports list API response
 */
export interface IReportsListResponse {
  reports: IReportResponse[];
  total: number;
}

/**
 * Transform report API response to frontend format
 */
export function transformReportResponse(data: IReportResponse): IReport {
  return {
    id: data.id,
    name: data.name,
    description: data.description,
    metrics: data.metrics,
    dimensions: data.dimensions,
    visualizations: data.visualizations,
    schedule: data.schedule,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
    createdBy: data.created_by.id,
  };
}

/**
 * Generate a new visualization ID
 */
export function generateVisualizationId(): string {
  return `viz_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Available metrics for reports
 */
export const AVAILABLE_METRICS: IMetric[] = [
  {
    id: 'total_users',
    name: 'Total Users',
    category: 'Users',
    description: 'Total number of registered users',
  },
  {
    id: 'active_users',
    name: 'Active Users',
    category: 'Users',
    description: 'Number of users active in the selected period',
  },
  {
    id: 'new_users',
    name: 'New Users',
    category: 'Users',
    description: 'Number of new user registrations',
  },
  {
    id: 'retention_rate',
    name: 'Retention Rate',
    category: 'Users',
    description: 'Percentage of users who return after their first visit',
  },
  {
    id: 'total_enrollments',
    name: 'Total Enrollments',
    category: 'Roadmaps',
    description: 'Total number of roadmap enrollments',
  },
  {
    id: 'completion_rate',
    name: 'Completion Rate',
    category: 'Roadmaps',
    description: 'Percentage of users who complete enrolled roadmaps',
  },
  {
    id: 'challenge_attempts',
    name: 'Challenge Attempts',
    category: 'Challenges',
    description: 'Number of challenge attempts',
  },
  {
    id: 'challenge_success_rate',
    name: 'Challenge Success Rate',
    category: 'Challenges',
    description: 'Percentage of successful challenge completions',
  },
  {
    id: 'resource_views',
    name: 'Resource Views',
    category: 'Resources',
    description: 'Number of resource views',
  },
  {
    id: 'resource_completion_rate',
    name: 'Resource Completion Rate',
    category: 'Resources',
    description: 'Percentage of resources marked as completed',
  },
];

/**
 * Available dimensions for reports
 */
export const AVAILABLE_DIMENSIONS: IDimension[] = [
  {
    id: 'date',
    name: 'Date',
    category: 'Time',
    description: 'Group data by date',
  },
  {
    id: 'week',
    name: 'Week',
    category: 'Time',
    description: 'Group data by week',
  },
  {
    id: 'month',
    name: 'Month',
    category: 'Time',
    description: 'Group data by month',
  },
  {
    id: 'quarter',
    name: 'Quarter',
    category: 'Time',
    description: 'Group data by quarter',
  },
  {
    id: 'year',
    name: 'Year',
    category: 'Time',
    description: 'Group data by year',
  },
  {
    id: 'user_role',
    name: 'User Role',
    category: 'Users',
    description: 'Group data by user role',
  },
  {
    id: 'user_status',
    name: 'User Status',
    category: 'Users',
    description: 'Group data by user status (active, inactive)',
  },
  {
    id: 'roadmap_category',
    name: 'Roadmap Category',
    category: 'Roadmaps',
    description: 'Group data by roadmap category',
  },
  {
    id: 'challenge_difficulty',
    name: 'Challenge Difficulty',
    category: 'Challenges',
    description: 'Group data by challenge difficulty (easy, medium, hard)',
  },
  {
    id: 'resource_type',
    name: 'Resource Type',
    category: 'Resources',
    description: 'Group data by resource type (article, video, etc.)',
  },
];

/**
 * Get metric name by ID
 */
export function getMetricName(metricId: string): string {
  const metric = AVAILABLE_METRICS.find((m) => m.id === metricId);
  return metric ? metric.name : metricId;
}

/**
 * Get dimension name by ID
 */
export function getDimensionName(dimensionId: string): string {
  const dimension = AVAILABLE_DIMENSIONS.find((d) => d.id === dimensionId);
  return dimension ? dimension.name : dimensionId;
}
