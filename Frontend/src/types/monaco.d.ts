/**
 * @file monaco.d.ts
 * @description Type definitions for monaco.d features
 */
declare module '@monaco-editor/react' {
  import { editor } from 'monaco-editor';
  import * as React from 'react';

  export interface Monaco {
    editor: typeof editor;
    KeyCode: {
      [key: string]: number;
    };
    KeyMod: {
      [key: string]: number;
    };
  }

  export interface EditorProps {
    /**
     * The initial value of the auto created model in the editor.
     */
    value?: string;
    /**
     * The initial language of the auto created model in the editor.
     */
    language?: string;
    /**
     * The theme for the monaco editor.
     */
    theme?: string | 'vs-dark' | 'light';
    /**
     * The height of the editor wrapper.
     */
    height?: string | number;
    /**
     * The width of the editor wrapper.
     */
    width?: string | number;
    /**
     * The default value of the auto created model in the editor.
     */
    defaultValue?: string;
    /**
     * The default language of the auto created model in the editor.
     */
    defaultLanguage?: string;
    /**
     * The line to jump on it.
     */
    line?: number;
    /**
     * The loading screen before the editor will be mounted.
     */
    loading?: React.ReactNode;
    /**
     * IStandaloneEditorConstructionOptions
     */
    options?: editor.IStandaloneEditorConstructionOptions;
    /**
     * Indicator whether to save the models' view states between model changes or not.
     */
    saveViewState?: boolean;
    /**
     * Indicator whether to dispose the current model when the Editor is unmounted or not.
     */
    keepCurrentModel?: boolean;
    /**
     * Width in px used by the editor when rendering.
     */
    fixedWidth?: number;
    /**
     * Height in px used by the editor when rendering.
     */
    fixedHeight?: number;
    /**
     * Callback invoked when the editor is mounted.
     */
    onMount?: (editor: editor.IStandaloneCodeEditor, monaco: Monaco) => void;
    /**
     * Callback invoked when the editor's value changes.
     */
    onChange?: (
      value: string | undefined,
      event: editor.IModelContentChangedEvent,
    ) => void;
    /**
     * Callback invoked when the editor is validated.
     */
    onValidate?: (markers: editor.IMarker[]) => void;
  }

  declare const Editor: React.FC<EditorProps>;
  export default Editor;
}
