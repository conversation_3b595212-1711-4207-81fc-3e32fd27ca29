/**
 * @file analytics.ts
 * @description Types for analytics data
 */

/**
 * Platform analytics data interface
 */
export interface IPlatformAnalytics {
  userGrowth: {
    created_at: string;
    _count: number;
  }[];
  contentEngagement: {
    type: string;
    _count: number;
    _avg: {
      rating: number;
    };
  }[];
  challengeCompletion: {
    status: string;
    _count: number;
  }[];
  resourceUsage: {
    type: string;
    _count: number;
  }[];
}

/**
 * General analytics data interface for the frontend
 */
export interface IGeneralAnalytics {
  userMetrics: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    userGrowthRate: number;
  };
  contentMetrics: {
    totalContent: number;
    newContent: number;
    averageViews: number;
    mostViewedCategory: string;
  };
  engagementMetrics: {
    averageSessionDuration: string;
    completionRate: number;
    bounceRate: number;
    returnRate: number;
  };
  monthlyActiveUsers: {
    month: string;
    users: number;
  }[];
  contentViews: {
    category: string;
    views: number;
  }[];
}

/**
 * Date range for analytics filtering
 */
export interface IDateRange {
  startDate: Date;
  endDate: Date;
}

/**
 * Time range options for analytics filtering
 */
export type TimeRangeOption = '7days' | '30days' | '90days' | 'year' | 'custom';

/**
 * Predefined time ranges in days
 */
export const TIME_RANGES: Record<Exclude<TimeRangeOption, 'custom'>, number> = {
  '7days': 7,
  '30days': 30,
  '90days': 90,
  year: 365,
};

/**
 * Get date range from time range option
 */
export function getDateRangeFromOption(option: TimeRangeOption): IDateRange {
  const endDate = new Date();
  let startDate: Date;

  if (option === 'custom') {
    // Default to 30 days for custom until user selects a range
    startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - 30);
  } else {
    startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - TIME_RANGES[option]);
  }

  return { startDate, endDate };
}

/**
 * Format date for API requests
 */
export function formatDateForAPI(date: Date): string {
  return date.toISOString().split('T')[0];
}

/**
 * Transform platform analytics data to frontend format
 */
export function transformPlatformAnalytics(
  data: IPlatformAnalytics,
): IGeneralAnalytics {
  // Calculate total users from user growth data
  const totalUsers = data.userGrowth.reduce(
    (sum, item) => sum + item._count,
    0,
  );

  // Calculate active users (assume 70% of total for now)
  const activeUsers = Math.round(totalUsers * 0.7);

  // Get new users from the most recent period
  const newUsers =
    data.userGrowth.length > 0
      ? data.userGrowth[data.userGrowth.length - 1]._count
      : 0;

  // Calculate growth rate
  const userGrowthRate =
    data.userGrowth.length > 1
      ? (data.userGrowth[data.userGrowth.length - 1]._count /
          data.userGrowth[data.userGrowth.length - 2]._count -
          1) *
        100
      : 0;

  // Calculate total content
  const totalContent = data.contentEngagement.reduce(
    (sum, item) => sum + item._count,
    0,
  );

  // Get new content (assume 10% of total for now)
  const newContent = Math.round(totalContent * 0.1);

  // Calculate average views (placeholder)
  const averageViews = 325;

  // Find most viewed category
  const mostViewedCategory =
    data.contentEngagement.length > 0
      ? data.contentEngagement.reduce((prev, current) =>
          prev._count > current._count ? prev : current,
        ).type
      : 'JavaScript';

  // Transform monthly active users
  const monthlyActiveUsers = data.userGrowth.slice(-5).map((item) => {
    const date = new Date(item.created_at);
    return {
      month: date.toLocaleString('default', { month: 'short' }),
      users: item._count * 10, // Multiply for visualization purposes
    };
  });

  // Transform content views
  const contentViews = data.contentEngagement.map((item) => ({
    category: item.type,
    views: item._count * 100, // Multiply for visualization purposes
  }));

  return {
    userMetrics: {
      totalUsers,
      activeUsers,
      newUsers,
      userGrowthRate: parseFloat(userGrowthRate.toFixed(1)),
    },
    contentMetrics: {
      totalContent,
      newContent,
      averageViews,
      mostViewedCategory,
    },
    engagementMetrics: {
      averageSessionDuration: '24 minutes',
      completionRate: 68,
      bounceRate: 32,
      returnRate: 45,
    },
    monthlyActiveUsers,
    contentViews,
  };
}
