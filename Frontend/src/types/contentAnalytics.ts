/**
 * @file contentAnalytics.ts
 * @description Types for content analytics data
 */

/**
 * Content analytics data from the backend API
 */
export interface IContentAnalyticsResponse {
  roadmaps: {
    id: string;
    title: string;
    enrollments: number;
    completions: number;
    created_at: string;
  }[];
  challenges: {
    id: string;
    title: string;
    difficulty: string;
    attempts: number;
    completions: number;
    created_at: string;
  }[];
  resources: {
    id: string;
    title: string;
    type: string;
    views: number;
    completions: number;
    rating: number;
    created_at: string;
  }[];
  timeSeriesData: {
    date: string;
    roadmap_enrollments: number;
    challenge_attempts: number;
    resource_views: number;
  }[];
}

/**
 * Content analytics data for the frontend
 */
export interface IContentAnalytics {
  overview: {
    totalRoadmaps: number;
    totalChallenges: number;
    totalResources: number;
    totalEnrollments: number;
    totalCompletions: number;
    completionRate: number;
  };
  roadmaps: {
    enrollments: {
      name: string;
      count: number;
    }[];
    completionRates: {
      name: string;
      rate: number;
    }[];
    weeklyEnrollments: {
      date: string;
      count: number;
    }[];
  };
  challenges: {
    attempts: {
      difficulty: string;
      count: number;
      successRate: number;
    }[];
    topChallenges: {
      name: string;
      attempts: number;
      successRate: number;
    }[];
    weeklyAttempts: {
      date: string;
      count: number;
    }[];
  };
  resources: {
    usage: {
      type: string;
      views: number;
      completions: number;
    }[];
    topResources: {
      name: string;
      views: number;
      rating: number;
    }[];
    weeklyViews: {
      date: string;
      count: number;
    }[];
  };
}

/**
 * Transform backend content analytics data to frontend format
 */
export function transformContentAnalytics(
  data: IContentAnalyticsResponse,
): IContentAnalytics {
  // Calculate total roadmaps
  const totalRoadmaps = data.roadmaps.length;

  // Calculate total challenges
  const totalChallenges = data.challenges.length;

  // Calculate total resources
  const totalResources = data.resources.length;

  // Calculate total enrollments
  const totalEnrollments = data.roadmaps.reduce(
    (sum, roadmap) => sum + roadmap.enrollments,
    0,
  );

  // Calculate total completions
  const totalCompletions = data.roadmaps.reduce(
    (sum, roadmap) => sum + roadmap.completions,
    0,
  );

  // Calculate completion rate
  const completionRate =
    totalEnrollments > 0
      ? parseFloat(((totalCompletions / totalEnrollments) * 100).toFixed(1))
      : 0;

  // Transform roadmap enrollments
  const roadmapEnrollments = data.roadmaps
    .sort((a, b) => b.enrollments - a.enrollments)
    .slice(0, 5)
    .map((roadmap) => ({
      name: roadmap.title,
      count: roadmap.enrollments,
    }));

  // Transform roadmap completion rates
  const roadmapCompletionRates = data.roadmaps
    .map((roadmap) => ({
      name: roadmap.title,
      rate:
        roadmap.enrollments > 0
          ? Math.round((roadmap.completions / roadmap.enrollments) * 100)
          : 0,
    }))
    .sort((a, b) => b.rate - a.rate)
    .slice(0, 5);

  // Transform weekly enrollments from time series data
  const weeklyEnrollments = data.timeSeriesData.slice(-7).map((item) => ({
    date: item.date,
    count: item.roadmap_enrollments,
  }));

  // Transform challenge attempts by difficulty
  const challengeAttempts = [
    {
      difficulty: 'Easy',
      count: data.challenges
        .filter((c) => c.difficulty === 'Easy')
        .reduce((sum, c) => sum + c.attempts, 0),
      successRate: calculateSuccessRate(
        data.challenges.filter((c) => c.difficulty === 'Easy'),
      ),
    },
    {
      difficulty: 'Medium',
      count: data.challenges
        .filter((c) => c.difficulty === 'Medium')
        .reduce((sum, c) => sum + c.attempts, 0),
      successRate: calculateSuccessRate(
        data.challenges.filter((c) => c.difficulty === 'Medium'),
      ),
    },
    {
      difficulty: 'Hard',
      count: data.challenges
        .filter((c) => c.difficulty === 'Hard')
        .reduce((sum, c) => sum + c.attempts, 0),
      successRate: calculateSuccessRate(
        data.challenges.filter((c) => c.difficulty === 'Hard'),
      ),
    },
  ];

  // Transform top challenges
  const topChallenges = data.challenges
    .sort((a, b) => b.attempts - a.attempts)
    .slice(0, 5)
    .map((challenge) => ({
      name: challenge.title,
      attempts: challenge.attempts,
      successRate:
        challenge.attempts > 0
          ? Math.round((challenge.completions / challenge.attempts) * 100)
          : 0,
    }));

  // Transform weekly challenge attempts
  const weeklyAttempts = data.timeSeriesData.slice(-7).map((item) => ({
    date: item.date,
    count: item.challenge_attempts,
  }));

  // Transform resource usage by type
  const resourceUsageMap = data.resources.reduce(
    (acc, resource) => {
      if (!acc[resource.type]) {
        acc[resource.type] = { views: 0, completions: 0 };
      }
      acc[resource.type].views += resource.views;
      acc[resource.type].completions += resource.completions;
      return acc;
    },
    {} as Record<string, { views: number; completions: number }>,
  );

  const resourceUsage = Object.entries(resourceUsageMap)
    .map(([type, data]) => ({
      type,
      views: data.views,
      completions: data.completions,
    }))
    .sort((a, b) => b.views - a.views);

  // Transform top resources
  const topResources = data.resources
    .sort((a, b) => b.views - a.views)
    .slice(0, 5)
    .map((resource) => ({
      name: resource.title,
      views: resource.views,
      rating: resource.rating,
    }));

  // Transform weekly resource views
  const weeklyViews = data.timeSeriesData.slice(-7).map((item) => ({
    date: item.date,
    count: item.resource_views,
  }));

  return {
    overview: {
      totalRoadmaps,
      totalChallenges,
      totalResources,
      totalEnrollments,
      totalCompletions,
      completionRate,
    },
    roadmaps: {
      enrollments: roadmapEnrollments,
      completionRates: roadmapCompletionRates,
      weeklyEnrollments,
    },
    challenges: {
      attempts: challengeAttempts,
      topChallenges,
      weeklyAttempts,
    },
    resources: {
      usage: resourceUsage,
      topResources,
      weeklyViews,
    },
  };
}

// Helper function to calculate success rate for challenges
function calculateSuccessRate(
  challenges: IContentAnalyticsResponse['challenges'],
) {
  const totalAttempts = challenges.reduce((sum, c) => sum + c.attempts, 0);
  const totalCompletions = challenges.reduce(
    (sum, c) => sum + c.completions,
    0,
  );
  return totalAttempts > 0
    ? Math.round((totalCompletions / totalAttempts) * 100)
    : 0;
}

/**
 * Generate mock content analytics data for development
 */
export function generateMockContentAnalytics(): IContentAnalyticsResponse {
  const today = new Date();
  const dates = Array.from({ length: 30 }, (_, i) => {
    const date = new Date(today);
    date.setDate(date.getDate() - (29 - i));
    return date.toISOString().split('T')[0];
  });

  return {
    roadmaps: [
      {
        id: '1',
        title: 'Frontend Developer',
        enrollments: 780,
        completions: 483,
        created_at: dates[0],
      },
      {
        id: '2',
        title: 'Backend Developer',
        enrollments: 650,
        completions: 377,
        created_at: dates[1],
      },
      {
        id: '3',
        title: 'Full Stack Developer',
        enrollments: 920,
        completions: 414,
        created_at: dates[2],
      },
      {
        id: '4',
        title: 'DevOps Engineer',
        enrollments: 420,
        completions: 218,
        created_at: dates[3],
      },
      {
        id: '5',
        title: 'Mobile Developer',
        enrollments: 480,
        completions: 230,
        created_at: dates[4],
      },
      {
        id: '6',
        title: 'Data Scientist',
        enrollments: 380,
        completions: 190,
        created_at: dates[5],
      },
      {
        id: '7',
        title: 'Machine Learning Engineer',
        enrollments: 320,
        completions: 150,
        created_at: dates[6],
      },
    ],
    challenges: [
      {
        id: '1',
        title: 'Binary Search Implementation',
        difficulty: 'Medium',
        attempts: 245,
        completions: 167,
        created_at: dates[7],
      },
      {
        id: '2',
        title: 'Linked List Reversal',
        difficulty: 'Medium',
        attempts: 220,
        completions: 158,
        created_at: dates[8],
      },
      {
        id: '3',
        title: 'Dynamic Programming: Fibonacci',
        difficulty: 'Easy',
        attempts: 310,
        completions: 264,
        created_at: dates[9],
      },
      {
        id: '4',
        title: 'Graph Traversal: BFS',
        difficulty: 'Hard',
        attempts: 180,
        completions: 101,
        created_at: dates[10],
      },
      {
        id: '5',
        title: 'Hash Table Implementation',
        difficulty: 'Medium',
        attempts: 195,
        completions: 121,
        created_at: dates[11],
      },
      {
        id: '6',
        title: 'Quick Sort Algorithm',
        difficulty: 'Medium',
        attempts: 210,
        completions: 134,
        created_at: dates[12],
      },
      {
        id: '7',
        title: 'Tree Traversal',
        difficulty: 'Medium',
        attempts: 230,
        completions: 147,
        created_at: dates[13],
      },
      {
        id: '8',
        title: 'Merge Sort Algorithm',
        difficulty: 'Easy',
        attempts: 280,
        completions: 238,
        created_at: dates[14],
      },
      {
        id: '9',
        title: 'Dijkstra Algorithm',
        difficulty: 'Hard',
        attempts: 150,
        completions: 68,
        created_at: dates[15],
      },
      {
        id: '10',
        title: 'Two Sum Problem',
        difficulty: 'Easy',
        attempts: 350,
        completions: 298,
        created_at: dates[16],
      },
    ],
    resources: [
      {
        id: '1',
        title: 'Introduction to Data Structures',
        type: 'Articles',
        views: 845,
        completions: 580,
        rating: 4.7,
        created_at: dates[17],
      },
      {
        id: '2',
        title: 'Advanced JavaScript Patterns',
        type: 'Articles',
        views: 780,
        completions: 520,
        rating: 4.5,
        created_at: dates[18],
      },
      {
        id: '3',
        title: 'System Design Interview Guide',
        type: 'Documents',
        views: 920,
        completions: 640,
        rating: 4.9,
        created_at: dates[19],
      },
      {
        id: '4',
        title: 'Machine Learning Fundamentals',
        type: 'Videos',
        views: 650,
        completions: 390,
        rating: 4.6,
        created_at: dates[20],
      },
      {
        id: '5',
        title: 'Cloud Computing Best Practices',
        type: 'Articles',
        views: 580,
        completions: 350,
        rating: 4.3,
        created_at: dates[21],
      },
      {
        id: '6',
        title: 'React Hooks Deep Dive',
        type: 'Videos',
        views: 720,
        completions: 430,
        rating: 4.8,
        created_at: dates[22],
      },
      {
        id: '7',
        title: 'Node.js Performance Tuning',
        type: 'Articles',
        views: 540,
        completions: 320,
        rating: 4.4,
        created_at: dates[23],
      },
      {
        id: '8',
        title: 'Docker and Kubernetes Guide',
        type: 'Documents',
        views: 610,
        completions: 370,
        rating: 4.7,
        created_at: dates[24],
      },
      {
        id: '9',
        title: 'TypeScript Best Practices',
        type: 'Videos',
        views: 590,
        completions: 350,
        rating: 4.5,
        created_at: dates[25],
      },
      {
        id: '10',
        title: 'GraphQL API Design',
        type: 'Articles',
        views: 520,
        completions: 310,
        rating: 4.6,
        created_at: dates[26],
      },
    ],
    timeSeriesData: dates.slice(-30).map((date, index) => {
      // Generate some realistic data with a slight upward trend
      const baseFactor = 0.8 + (index / 30) * 0.4; // Increases from 0.8 to 1.2 over the period
      const dayVariation = Math.sin(index * 0.9) * 0.2 + 1; // Adds some daily variation

      return {
        date,
        roadmap_enrollments: Math.round(
          40 * baseFactor * dayVariation * (1 + Math.random() * 0.3),
        ),
        challenge_attempts: Math.round(
          120 * baseFactor * dayVariation * (1 + Math.random() * 0.4),
        ),
        resource_views: Math.round(
          300 * baseFactor * dayVariation * (1 + Math.random() * 0.5),
        ),
      };
    }),
  };
}
