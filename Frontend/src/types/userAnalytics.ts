/**
 * @file userAnalytics.ts
 * @description Types for user analytics data
 */

/**
 * User analytics data from the backend API
 */
export interface IUserAnalyticsResponse {
  courseProgress: {
    id: string;
    user_id: string;
    topic_id: string;
    status: string;
    progress: number;
    completed_at: string | null;
    created_at: string;
    updated_at: string;
    topic: {
      title: string;
      subjects: {
        subject: {
          title: string;
        };
      }[];
    };
  }[];
  challengeStats: {
    status: string;
    _count: number;
  }[];
  resourceUsage: {
    type: string;
    _count: number;
  }[];
}

/**
 * User analytics data for the frontend
 */
export interface IUserAnalytics {
  overview: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    churnRate: number;
    retentionRate: number;
    averageSessionDuration: string;
    averageSessionsPerUser: number;
  };
  registration: {
    daily: {
      date: string;
      count: number;
    }[];
    sources: {
      source: string;
      count: number;
      percentage: number;
    }[];
    conversionRate: number;
  };
  retention: {
    byWeek: {
      week: string;
      rate: number;
    }[];
    byUserType: {
      type: string;
      rate: number;
    }[];
  };
  engagement: {
    activeUsersByDay: {
      date: string;
      count: number;
    }[];
    activityDistribution: {
      activity: string;
      percentage: number;
    }[];
    userSegments: {
      segment: string;
      percentage: number;
    }[];
  };
  learning: {
    completionRates: {
      category: string;
      rate: number;
    }[];
    averageProgress: number;
    certificatesEarned: number;
    skillDistribution: {
      skill: string;
      percentage: number;
    }[];
  };
}

/**
 * User growth data for the frontend
 */
export interface IUserGrowthData {
  dates: string[];
  newUsers: number[];
  totalUsers: number[];
  growthRate: number[];
}

/**
 * User churn data for the frontend
 */
export interface IUserChurnData {
  months: string[];
  churnRate: number[];
  retentionRate: number[];
}

/**
 * Transform backend user analytics data to frontend format
 */
export function transformUserAnalytics(
  data: IUserAnalyticsResponse,
): IUserAnalytics {
  // Generate user growth data internally
  const userGrowthData = generateUserGrowthData();
  const userChurnData = generateUserChurnData();

  // Calculate total users based on user growth data
  const totalUsers =
    userGrowthData.totalUsers.length > 0
      ? userGrowthData.totalUsers[userGrowthData.totalUsers.length - 1]
      : 1248;

  // Calculate active users based on challenge stats (users with active challenges)
  const activeUsers = Math.round(totalUsers * 0.7); // Placeholder calculation

  // Calculate new users from user growth data
  const newUsers =
    userGrowthData.newUsers.length > 0
      ? userGrowthData.newUsers[userGrowthData.newUsers.length - 1]
      : 42;

  // Get churn rate from user churn data
  const churnRate =
    userChurnData.churnRate.length > 0
      ? userChurnData.churnRate[userChurnData.churnRate.length - 1]
      : 3.2;

  // Calculate retention rate (100 - churn rate)
  const retentionRate = 100 - churnRate;

  // Calculate completion rates by category from course progress
  const completionRates = data.courseProgress.reduce(
    (acc, progress) => {
      const category = progress.topic.subjects[0]?.subject.title || 'Other';

      if (!acc[category]) {
        acc[category] = { completed: 0, total: 0 };
      }

      acc[category].total += 1;
      if (progress.status === 'COMPLETED') {
        acc[category].completed += 1;
      }

      return acc;
    },
    {} as Record<string, { completed: number; total: number }>,
  );

  // Transform completion rates into the expected format
  const completionRatesArray = Object.entries(completionRates).map(
    ([category, stats]) => ({
      category,
      rate: Math.round((stats.completed / stats.total) * 100) || 0,
    }),
  );

  // Calculate activity distribution from challenge stats and resource usage
  const totalActivities =
    data.challengeStats.reduce((sum, stat) => sum + stat._count, 0) +
    data.resourceUsage.reduce((sum, usage) => sum + usage._count, 0);

  const activityDistribution = [
    {
      activity: 'Challenges',
      percentage:
        Math.round(
          (data.challengeStats.reduce((sum, stat) => sum + stat._count, 0) /
            totalActivities) *
            100,
        ) || 35,
    },
    {
      activity: 'Learning Resources',
      percentage:
        Math.round(
          (data.resourceUsage.reduce((sum, usage) => sum + usage._count, 0) /
            totalActivities) *
            100,
        ) || 25,
    },
    {
      activity: 'Roadmaps',
      percentage: 20, // Placeholder
    },
    {
      activity: 'Battles',
      percentage: 15, // Placeholder
    },
    {
      activity: 'Forums',
      percentage: 5, // Placeholder
    },
  ];

  // Generate daily registration data (last 7 days)
  const today = new Date();
  const daily = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(today);
    date.setDate(date.getDate() - (6 - i));
    const dateString = date.toISOString().split('T')[0];

    // Find the corresponding date in user growth data or use a random value
    const growthIndex = userGrowthData.dates.findIndex(
      (d: string) => d === dateString,
    );
    const count =
      growthIndex >= 0
        ? userGrowthData.newUsers[growthIndex]
        : Math.floor(Math.random() * 10) + 1;

    return {
      date: dateString,
      count,
    };
  });

  // Generate active users by day (last 7 days)
  const activeUsersByDay = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(today);
    date.setDate(date.getDate() - (6 - i));
    const dateString = date.toISOString().split('T')[0];

    // Calculate a value that's a percentage of total users
    const percentage = 0.5 + Math.random() * 0.2; // Between 50% and 70%
    const count = Math.round(totalUsers * percentage);

    return {
      date: dateString,
      count,
    };
  });

  return {
    overview: {
      totalUsers,
      activeUsers,
      newUsers,
      churnRate,
      retentionRate,
      averageSessionDuration: '24 minutes', // Placeholder
      averageSessionsPerUser: 3.5, // Placeholder
    },
    registration: {
      daily,
      sources: [
        {
          source: 'Direct',
          count: Math.round(totalUsers * 0.37),
          percentage: 37,
        },
        {
          source: 'Search',
          count: Math.round(totalUsers * 0.23),
          percentage: 23,
        },
        {
          source: 'Social Media',
          count: Math.round(totalUsers * 0.2),
          percentage: 20,
        },
        {
          source: 'Referral',
          count: Math.round(totalUsers * 0.13),
          percentage: 13,
        },
        {
          source: 'Other',
          count: Math.round(totalUsers * 0.07),
          percentage: 7,
        },
      ],
      conversionRate: 3.8, // Placeholder
    },
    retention: {
      byWeek: [
        { week: 'Week 1', rate: 100 },
        { week: 'Week 2', rate: 82 },
        { week: 'Week 3', rate: 76 },
        { week: 'Week 4', rate: 72 },
        { week: 'Week 5', rate: 68 },
        { week: 'Week 6', rate: 65 },
        { week: 'Week 7', rate: 62 },
        { week: 'Week 8', rate: 60 },
      ],
      byUserType: [
        { type: 'Free Users', rate: 58 },
        { type: 'Premium Users', rate: 84 },
      ],
    },
    engagement: {
      activeUsersByDay,
      activityDistribution,
      userSegments: [
        { segment: 'Highly Active', percentage: 22 },
        { segment: 'Regular', percentage: 45 },
        { segment: 'Occasional', percentage: 18 },
        { segment: 'Inactive', percentage: 15 },
      ],
    },
    learning: {
      completionRates:
        completionRatesArray.length > 0
          ? completionRatesArray
          : [
              { category: 'JavaScript', rate: 72 },
              { category: 'React', rate: 68 },
              { category: 'Node.js', rate: 65 },
              { category: 'CSS', rate: 78 },
              { category: 'TypeScript', rate: 62 },
            ],
      averageProgress: 43, // Placeholder
      certificatesEarned: 287, // Placeholder
      skillDistribution: [
        { skill: 'Frontend', percentage: 45 },
        { skill: 'Backend', percentage: 30 },
        { skill: 'DevOps', percentage: 15 },
        { skill: 'Mobile', percentage: 10 },
      ],
    },
  };
}

/**
 * Generate user growth data for the past 12 months
 */
export function generateUserGrowthData(): IUserGrowthData {
  const dates: string[] = [];
  const newUsers: number[] = [];
  const totalUsers: number[] = [];
  const growthRate: number[] = [];

  let cumulativeUsers = 800; // Starting with 800 users

  // Generate data for the past 12 months
  const today = new Date();
  for (let i = 11; i >= 0; i--) {
    const date = new Date(today);
    date.setMonth(date.getMonth() - i);
    const dateString = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;

    // Generate random new users with an increasing trend
    const baseNewUsers = 30 + Math.floor(Math.random() * 20); // Base between 30-50
    const trendFactor = 1 + i / 24; // Trend factor decreases as we get closer to present
    const monthNewUsers = Math.round(baseNewUsers * trendFactor);

    // Calculate growth rate
    const previousTotal = cumulativeUsers;
    cumulativeUsers += monthNewUsers;
    const monthGrowthRate =
      previousTotal > 0 ? (cumulativeUsers / previousTotal - 1) * 100 : 0;

    dates.push(dateString);
    newUsers.push(monthNewUsers);
    totalUsers.push(cumulativeUsers);
    growthRate.push(parseFloat(monthGrowthRate.toFixed(1)));
  }

  return { dates, newUsers, totalUsers, growthRate };
}

/**
 * Generate user churn data for the past 12 months
 */
export function generateUserChurnData(): IUserChurnData {
  const months: string[] = [];
  const churnRate: number[] = [];
  const retentionRate: number[] = [];

  // Generate data for the past 12 months
  const today = new Date();
  for (let i = 11; i >= 0; i--) {
    const date = new Date(today);
    date.setMonth(date.getMonth() - i);
    const monthString = date.toLocaleString('default', { month: 'short' });

    // Generate random churn rate with a decreasing trend (improving over time)
    const baseChurnRate = 5 + Math.random() * 3; // Base between 5-8%
    const trendFactor = 1 - (11 - i) / 50; // Trend factor decreases as we get closer to present
    const monthChurnRate = parseFloat((baseChurnRate * trendFactor).toFixed(1));

    months.push(monthString);
    churnRate.push(monthChurnRate);
    retentionRate.push(100 - monthChurnRate);
  }

  return { months, churnRate, retentionRate };
}
