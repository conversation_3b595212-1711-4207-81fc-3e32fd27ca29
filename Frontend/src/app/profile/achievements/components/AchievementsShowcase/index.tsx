/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */
'use client';

import { useEffect, useState } from 'react';

import {
  Award,
  Brain,
  Calendar,
  CheckCircle,
  Code,
  Cpu,
  Database,
  Filter,
  Lock,
  MessageCircle,
  Rocket,
  Search,
  Trophy,
  Zap,
} from 'lucide-react';

import {
  Achievement<PERSON>ategory,
  AchievementTier,
  IAchievement,
} from '@/app/coding-challenges/types/achievements';
import AchievementBadge from '@/components/AchievementBadge';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAchievements } from '@/hooks/useAchievements';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

/**
 * @file index.tsx
 * @description React component for AchievementsShowcase
 */

export default function AchievementsShowcase() {
  const {
    achievements,
    isLoading,
    error,
    getUnlockedAchievements,
    getLockedAchievements,
    getAchievementsByCategory,
    getAchievementsByTier,
  } = useAchievements();

  const [activeTab, setActiveTab] = useState<'all' | 'unlocked' | 'locked'>(
    'all',
  );
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [tierFilter, setTierFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredAchievements, setFilteredAchievements] = useState<
    IAchievement[]
  >([]);

  // Calculate total stats
  const totalAchievements = achievements.length;
  const unlockedAchievements = getUnlockedAchievements().length;
  const totalPoints = achievements.reduce(
    (sum, achievement) =>
      achievement.unlocked ? sum + achievement.points : sum,
    0,
  );
  const completionPercentage =
    totalAchievements > 0
      ? Math.round((unlockedAchievements / totalAchievements) * 100)
      : 0;

  // Filter achievements based on active filters
  useEffect(() => {
    let filtered: IAchievement[] = [];

    // Filter by tab (unlocked/locked status)
    switch (activeTab) {
      case 'unlocked':
        filtered = getUnlockedAchievements();
        break;
      case 'locked':
        filtered = getLockedAchievements();
        break;
      default:
        filtered = achievements;
    }

    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(
        (achievement) => achievement.category === categoryFilter,
      );
    }

    // Filter by tier
    if (tierFilter !== 'all') {
      filtered = filtered.filter(
        (achievement) => achievement.tier === tierFilter,
      );
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (achievement) =>
          achievement.title.toLowerCase().includes(term) ||
          achievement.description.toLowerCase().includes(term),
      );
    }

    setFilteredAchievements(filtered);
  }, [
    activeTab,
    categoryFilter,
    tierFilter,
    searchTerm,
    achievements,
    getUnlockedAchievements,
    getLockedAchievements,
  ]);

  // Get category icon
  const getCategoryIcon = (category: AchievementCategory) => {
    switch (category) {
      case 'challenges':
        return <Code className="h-4 w-4" />;
      case 'solutions':
        return <Zap className="h-4 w-4" />;
      case 'streaks':
        return <Calendar className="h-4 w-4" />;
      case 'community':
        return <MessageCircle className="h-4 w-4" />;
      case 'special':
        return <Award className="h-4 w-4" />;
      default:
        return <Trophy className="h-4 w-4" />;
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-8 p-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className="h-24 animate-pulse rounded-lg bg-muted"
            ></div>
          ))}
        </div>
        <div className="space-y-4">
          <div className="h-10 animate-pulse rounded-lg bg-muted"></div>
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((i) => (
              <div
                key={i}
                className="h-32 animate-pulse rounded-lg bg-muted"
              ></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center">
        <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
          Failed to load achievements
        </h2>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <div className="rounded-lg border bg-card p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-muted-foreground">
              Total Achievements
            </h3>
            <Trophy className="h-5 w-5 text-primary" />
          </div>
          <p className="mt-2 text-2xl font-bold">{totalAchievements}</p>
        </div>

        <div className="rounded-lg border bg-card p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-muted-foreground">
              Unlocked
            </h3>
            <CheckCircle className="h-5 w-5 text-green-500" />
          </div>
          <p className="mt-2 text-2xl font-bold">{unlockedAchievements}</p>
          <div className="mt-2 flex items-center gap-2">
            <Progress value={completionPercentage} className="h-2" />
            <span className="text-xs text-muted-foreground">
              {completionPercentage}%
            </span>
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-muted-foreground">
              Locked
            </h3>
            <Lock className="h-5 w-5 text-muted-foreground" />
          </div>
          <p className="mt-2 text-2xl font-bold">
            {totalAchievements - unlockedAchievements}
          </p>
        </div>

        <div className="rounded-lg border bg-card p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-muted-foreground">
              Total Points
            </h3>
            <Award className="h-5 w-5 text-yellow-500" />
          </div>
          <p className="mt-2 text-2xl font-bold">{totalPoints}</p>
        </div>
      </div>

      {/* Achievements List */}
      <div className="space-y-4">
        <Tabs
          defaultValue="all"
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as any)}
        >
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <TabsList>
              <TabsTrigger value="all">All Achievements</TabsTrigger>
              <TabsTrigger value="unlocked" className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4" />
                <span>Unlocked</span>
              </TabsTrigger>
              <TabsTrigger value="locked" className="flex items-center gap-1">
                <Lock className="h-4 w-4" />
                <span>Locked</span>
              </TabsTrigger>
            </TabsList>

            <div className="flex flex-wrap gap-2">
              <div className="relative flex-grow sm:w-auto">
                <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search achievements..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>

              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem
                    value="challenges"
                    className="flex items-center gap-2"
                  >
                    <Code className="h-4 w-4" />
                    <span>Challenges</span>
                  </SelectItem>
                  <SelectItem
                    value="solutions"
                    className="flex items-center gap-2"
                  >
                    <Zap className="h-4 w-4" />
                    <span>Solutions</span>
                  </SelectItem>
                  <SelectItem
                    value="streaks"
                    className="flex items-center gap-2"
                  >
                    <Calendar className="h-4 w-4" />
                    <span>Streaks</span>
                  </SelectItem>
                  <SelectItem
                    value="community"
                    className="flex items-center gap-2"
                  >
                    <MessageCircle className="h-4 w-4" />
                    <span>Community</span>
                  </SelectItem>
                  <SelectItem
                    value="special"
                    className="flex items-center gap-2"
                  >
                    <Award className="h-4 w-4" />
                    <span>Special</span>
                  </SelectItem>
                </SelectContent>
              </Select>

              <Select value={tierFilter} onValueChange={setTierFilter}>
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Tier" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tiers</SelectItem>
                  <SelectItem value="bronze">Bronze</SelectItem>
                  <SelectItem value="silver">Silver</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                  <SelectItem value="diamond">Diamond</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <TabsContent value="all" className="mt-6">
            {renderAchievementGrid(filteredAchievements)}
          </TabsContent>

          <TabsContent value="unlocked" className="mt-6">
            {renderAchievementGrid(filteredAchievements)}
          </TabsContent>

          <TabsContent value="locked" className="mt-6">
            {renderAchievementGrid(filteredAchievements)}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );

  // Helper function to render the achievement grid
  function renderAchievementGrid(achievements: IAchievement[]) {
    if (achievements.length === 0) {
      return (
        <div className="rounded-lg border border-dashed p-8 text-center">
          <h3 className="text-lg font-medium">No achievements found</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {activeTab === 'unlocked'
              ? "You haven't unlocked any achievements yet. Keep coding!"
              : activeTab === 'locked'
                ? 'No locked achievements match your filters.'
                : 'No achievements match your filters.'}
          </p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
        {achievements.map((achievement) => (
          <div
            key={achievement.id}
            className={cn(
              'flex flex-col items-center rounded-lg border p-4 text-center transition-all hover:shadow-md',
              achievement.unlocked ? 'bg-card' : 'bg-muted/30',
            )}
          >
            <AchievementBadge
              achievement={achievement}
              size="md"
              className="mb-3"
            />

            <h3 className="line-clamp-1 text-sm font-medium">
              {achievement.title}
            </h3>

            <div className="mt-1 flex items-center gap-1">
              <Badge variant="outline" className="capitalize">
                {achievement.tier}
              </Badge>

              <Badge variant="secondary" className="flex items-center gap-1">
                {getCategoryIcon(achievement.category as AchievementCategory)}
                <span className="capitalize">{achievement.category}</span>
              </Badge>
            </div>

            <p className="mt-2 line-clamp-2 text-xs text-muted-foreground">
              {achievement.description}
            </p>

            <div className="mt-auto pt-3 text-sm">
              <div className="flex items-center justify-center gap-1">
                <Trophy className="h-4 w-4 text-yellow-500" />
                <span>{achievement.points} points</span>
              </div>

              {achievement.progress && !achievement.unlocked && (
                <div className="mt-2 space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>
                      {achievement.progress.current}/
                      {achievement.progress.target}
                    </span>
                    <span>{achievement.progress.percentage}%</span>
                  </div>
                  <Progress
                    value={achievement.progress.percentage}
                    className="h-1"
                  />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  }
}
