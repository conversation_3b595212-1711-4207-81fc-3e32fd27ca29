/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */
'use client';

import { motion } from 'framer-motion';
import { Trophy } from 'lucide-react';

import AchievementsShowcase from './components/AchievementsShowcase';

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

/**
 * @file page.tsx
 * @description Next.js page for profile/achievements route
 */

export default function AchievementsPage() {
  return (
    <div className="container py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center gap-2">
          <Trophy className="h-6 w-6 text-yellow-500" />
          <h1 className="text-3xl font-bold tracking-tight md:text-4xl">
            My Achievements
          </h1>
        </div>
        <p className="mt-2 text-muted-foreground">
          Track your progress and showcase your coding accomplishments.
        </p>
      </motion.div>

      <AchievementsShowcase />
    </div>
  );
}
