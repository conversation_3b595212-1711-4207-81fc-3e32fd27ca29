/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */
'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import { motion } from 'framer-motion';
import {
  ArrowR<PERSON>,
  BookOpen,
  CheckCircle2,
  Clock,
  Code,
  Lock,
  Trophy,
  Unlock,
} from 'lucide-react';

import {
  IRoadmapChallenge,
  IRoadmapTopic,
} from '@/app/coding-challenges/types/roadmap';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useRoadmapChallenges } from '@/hooks/useRoadmapChallenges';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

/**
 * @file index.tsx
 * @description React component for RoadmapChallenges
 */

interface RoadmapChallengesProps {
  roadmapId: string;
  topicId: string;
  className?: string;
}

export default function RoadmapChallenges({
  roadmapId,
  topicId,
  className = '',
}: RoadmapChallengesProps) {
  const { roadmap, isLoading, error, getChallengesForTopic, updateProgress } =
    useRoadmapChallenges(roadmapId);

  const [topic, setTopic] = useState<IRoadmapTopic | null>(null);
  const [challenges, setChallenges] = useState<IRoadmapChallenge[]>([]);

  // Get topic and challenges when roadmap data is loaded
  useEffect(() => {
    if (roadmap) {
      const foundTopic = roadmap.topics.find((t) => t.id === topicId) || null;
      setTopic(foundTopic);

      if (foundTopic) {
        setChallenges(foundTopic.challenges || []);
      }
    }
  }, [roadmap, topicId]);

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'HARD':
        return 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  // Get status icon
  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-blue-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  // Calculate topic progress
  const calculateProgress = () => {
    if (!challenges.length) return 0;

    const completedChallenges = challenges.filter(
      (c) => c.status === 'completed',
    ).length;
    return (completedChallenges / challenges.length) * 100;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-8 w-24" />
        </div>
        <Skeleton className="h-4 w-full max-w-md" />
        <Skeleton className="h-2 w-full" />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={cn(
          'border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center',
          className,
        )}
      >
        <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
          Failed to load roadmap challenges
        </h2>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  // No topic found
  if (!topic) {
    return (
      <div
        className={cn(
          'rounded-lg border border-dashed p-6 text-center',
          className,
        )}
      >
        <h2 className="mb-2 text-lg font-medium">Topic not found</h2>
        <p className="text-muted-foreground">
          The topic you're looking for doesn't exist in this roadmap.
        </p>
      </div>
    );
  }

  // No challenges found
  if (!challenges.length) {
    return (
      <div className={cn('space-y-4', className)}>
        <div>
          <h2 className="text-2xl font-bold">{topic.title}</h2>
          <p className="text-muted-foreground">{topic.description}</p>
        </div>
        <div className="rounded-lg border border-dashed p-6 text-center">
          <h3 className="mb-2 text-lg font-medium">No challenges available</h3>
          <p className="text-muted-foreground">
            There are no challenges associated with this topic yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">{topic.title}</h2>
          <Badge variant="outline" className="text-sm">
            Level {topic.level}
          </Badge>
        </div>
        <p className="text-muted-foreground">{topic.description}</p>

        <div className="mt-4 space-y-1">
          <div className="flex items-center justify-between text-sm">
            <span>Progress</span>
            <span>
              {challenges.filter((c) => c.status === 'completed').length}/
              {challenges.length} completed
            </span>
          </div>
          <Progress value={calculateProgress()} className="h-2" />
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {challenges.map((challenge, index) => (
          <motion.div
            key={challenge.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card
              className={cn(
                'h-full transition-all hover:shadow-md',
                challenge.status === 'completed' &&
                  'border-green-200 dark:border-green-900/50',
              )}
            >
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="mb-1 flex items-center">
                      {getStatusIcon(challenge.status)}
                      <Badge
                        variant="outline"
                        className={cn(
                          'ml-2',
                          getDifficultyColor(challenge.difficulty),
                        )}
                      >
                        {challenge.difficulty}
                      </Badge>
                      {challenge.is_required && (
                        <Badge variant="secondary" className="ml-2">
                          Required
                        </Badge>
                      )}
                    </div>
                    <CardTitle className="line-clamp-1 text-xl">
                      {challenge.title}
                    </CardTitle>
                  </div>
                  <div className="flex items-center">
                    <Trophy className="mr-1 h-5 w-5 text-yellow-500" />
                    <span className="font-medium">{challenge.points} pts</span>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pb-2">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Code className="h-4 w-4" />
                  <span>Part of {topic.title}</span>
                </div>

                {challenge.status === 'in_progress' && (
                  <div className="mt-2 rounded-md bg-blue-50 p-2 text-sm text-blue-800 dark:bg-blue-950/30 dark:text-blue-300">
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4 text-blue-500" />
                      <span>You've started this challenge</span>
                    </div>
                  </div>
                )}

                {challenge.status === 'completed' && (
                  <div className="mt-2 rounded-md bg-green-50 p-2 text-sm text-green-800 dark:bg-green-950/30 dark:text-green-300">
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-1 h-4 w-4 text-green-500" />
                      <span>You've completed this challenge</span>
                    </div>
                  </div>
                )}
              </CardContent>

              <CardFooter>
                <Button asChild className="w-full">
                  <Link
                    href={`/coding-challenges/${challenge.id}`}
                    className="flex items-center justify-center"
                  >
                    {challenge.status === 'completed' ? (
                      <>
                        <BookOpen className="mr-2 h-4 w-4" />
                        Review Challenge
                      </>
                    ) : (
                      <>
                        <ArrowRight className="mr-2 h-4 w-4" />
                        {challenge.status === 'in_progress'
                          ? 'Continue Challenge'
                          : 'Start Challenge'}
                      </>
                    )}
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
