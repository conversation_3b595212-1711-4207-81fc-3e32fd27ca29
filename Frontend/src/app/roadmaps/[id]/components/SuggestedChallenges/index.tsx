/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */
'use client';

import { useState } from 'react';

import Link from 'next/link';

import { motion } from 'framer-motion';
import {
  ArrowRight,
  BookOpen,
  ChevronLeft,
  ChevronRight,
  Lightbulb,
  Tag,
  Trophy,
} from 'lucide-react';

import { IChallenge } from '@/app/coding-challenges/types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useRoadmapChallenges } from '@/hooks/useRoadmapChallenges';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

/**
 * @file index.tsx
 * @description React component for SuggestedChallenges
 */

interface SuggestedChallengesProps {
  roadmapId: string;
  maxDisplay?: number;
  className?: string;
}

export default function SuggestedChallenges({
  roadmapId,
  maxDisplay = 3,
  className = '',
}: SuggestedChallengesProps) {
  const {
    roadmap,
    suggestedChallenges,
    isLoading,
    error,
    getNextRecommendedChallenge,
  } = useRoadmapChallenges(roadmapId);

  const [currentPage, setCurrentPage] = useState(0);

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'HARD':
        return 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  // Get next recommended challenge
  const nextRecommended = getNextRecommendedChallenge();

  // Calculate total pages
  const totalPages = Math.ceil(suggestedChallenges.length / maxDisplay);

  // Get current page challenges
  const currentChallenges = suggestedChallenges.slice(
    currentPage * maxDisplay,
    (currentPage + 1) * maxDisplay,
  );

  // Handle page navigation
  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-full max-w-md" />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={cn(
          'border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center',
          className,
        )}
      >
        <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
          Failed to load suggested challenges
        </h2>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div className="space-y-2">
        <h2 className="flex items-center text-2xl font-bold">
          <Lightbulb className="mr-2 h-6 w-6 text-yellow-500" />
          Suggested Challenges
        </h2>
        <p className="text-muted-foreground">
          Challenges recommended based on your progress in this roadmap
        </p>
      </div>

      {/* Next recommended challenge */}
      {nextRecommended && (
        <div className="mb-6">
          <Card className="border-blue-200 bg-blue-50 dark:border-blue-900/50 dark:bg-blue-950/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <BookOpen className="h-3 w-3" />
                  <span>Next in Roadmap</span>
                </Badge>
                <Badge
                  variant="outline"
                  className={getDifficultyColor(
                    nextRecommended.challenge.difficulty,
                  )}
                >
                  {nextRecommended.challenge.difficulty}
                </Badge>
              </div>
              <CardTitle className="mt-2 text-xl">
                {nextRecommended.challenge.title}
              </CardTitle>
              <CardDescription>
                From {nextRecommended.topic.title}
              </CardDescription>
            </CardHeader>

            <CardContent className="pb-2">
              <div className="flex items-center text-sm text-muted-foreground">
                <Trophy className="mr-1 h-4 w-4 text-yellow-500" />
                <span>{nextRecommended.challenge.points} points</span>
              </div>
            </CardContent>

            <CardFooter>
              <Button asChild className="w-full">
                <Link
                  href={`/coding-challenges/${nextRecommended.challenge.id}`}
                  className="flex items-center justify-center"
                >
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Start Challenge
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}

      {/* Additional suggested challenges */}
      {suggestedChallenges.length > 0 ? (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">More Challenges</h3>
            {totalPages > 1 && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={prevPage}
                  disabled={totalPages <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-muted-foreground">
                  {currentPage + 1}/{totalPages}
                </span>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={nextPage}
                  disabled={totalPages <= 1}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {currentChallenges.map((challenge, index) => (
              <motion.div
                key={challenge.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="h-full transition-all hover:shadow-md">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <Badge
                        variant="outline"
                        className={getDifficultyColor(challenge.difficulty)}
                      >
                        {challenge.difficulty}
                      </Badge>
                      <div className="flex items-center">
                        <Trophy className="mr-1 h-4 w-4 text-yellow-500" />
                        <span className="font-medium">
                          {challenge.points} pts
                        </span>
                      </div>
                    </div>
                    <CardTitle className="mt-2 line-clamp-1 text-lg">
                      {challenge.title}
                    </CardTitle>
                  </CardHeader>

                  <CardContent className="pb-2">
                    <p className="line-clamp-2 text-sm text-muted-foreground">
                      {challenge.description}
                    </p>

                    {challenge.tags && challenge.tags.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {challenge.tags.slice(0, 3).map((tag, i) => (
                          <Badge
                            key={i}
                            variant="secondary"
                            className="flex items-center gap-1 text-xs"
                          >
                            <Tag className="h-3 w-3" />
                            <span>{tag}</span>
                          </Badge>
                        ))}
                        {challenge.tags.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{challenge.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}
                  </CardContent>

                  <CardFooter>
                    <Button asChild variant="outline" className="w-full">
                      <Link
                        href={`/coding-challenges/${challenge.id}`}
                        className="flex items-center justify-center"
                      >
                        <ArrowRight className="mr-2 h-4 w-4" />
                        View Challenge
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      ) : (
        <div className="rounded-lg border border-dashed p-6 text-center">
          <h3 className="mb-2 text-lg font-medium">No additional challenges</h3>
          <p className="text-muted-foreground">
            We don't have any more challenges to suggest at this time.
          </p>
        </div>
      )}
    </div>
  );
}
