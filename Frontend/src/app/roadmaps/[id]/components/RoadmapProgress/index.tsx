/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */
'use client';

import { useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import {
  Award,
  Bar<PERSON>hart,
  BookOpen,
  Calendar,
  CheckCircle2,
  ChevronRight,
  Clock,
  Trophy,
} from 'lucide-react';

import { IRoadmapTopic } from '@/app/coding-challenges/types/roadmap';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useRoadmapChallenges } from '@/hooks/useRoadmapChallenges';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

/**
 * @file index.tsx
 * @description React component for RoadmapProgress
 */

interface RoadmapProgressProps {
  roadmapId: string;
  className?: string;
}

export default function RoadmapProgress({
  roadmapId,
  className = '',
}: RoadmapProgressProps) {
  const { roadmap, isLoading, error, getNextRecommendedChallenge } =
    useRoadmapChallenges(roadmapId);

  // Get next recommended challenge
  const nextRecommended = getNextRecommendedChallenge();

  // Calculate topic completion stats
  const getTopicStats = () => {
    if (!roadmap)
      return { completed: 0, inProgress: 0, notStarted: 0, total: 0 };

    const stats = {
      completed: 0,
      inProgress: 0,
      notStarted: 0,
      total: roadmap.topics.length,
    };

    roadmap.topics.forEach((topic) => {
      if (topic.status === 'completed') {
        stats.completed++;
      } else if (topic.status === 'in_progress') {
        stats.inProgress++;
      } else {
        stats.notStarted++;
      }
    });

    return stats;
  };

  // Calculate challenge completion stats
  const getChallengeStats = () => {
    if (!roadmap) return { completed: 0, total: 0, percentage: 0 };

    let completed = 0;
    let total = 0;

    roadmap.topics.forEach((topic) => {
      if (topic.challenges) {
        total += topic.challenges.length;
        completed += topic.challenges.filter(
          (c) => c.status === 'completed',
        ).length;
      }
    });

    return {
      completed,
      total,
      percentage: total > 0 ? (completed / total) * 100 : 0,
    };
  };

  const topicStats = getTopicStats();
  const challengeStats = getChallengeStats();

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-full max-w-md" />
        <Skeleton className="h-2 w-full" />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-48 w-full" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={cn(
          'border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center',
          className,
        )}
      >
        <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
          Failed to load roadmap progress
        </h2>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  // No roadmap found
  if (!roadmap) {
    return (
      <div
        className={cn(
          'rounded-lg border border-dashed p-6 text-center',
          className,
        )}
      >
        <h2 className="mb-2 text-lg font-medium">Roadmap not found</h2>
        <p className="text-muted-foreground">
          The roadmap you're looking for doesn't exist.
        </p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">{roadmap.title}</h2>
        <p className="text-muted-foreground">{roadmap.description}</p>

        <div className="mt-4 space-y-1">
          <div className="flex items-center justify-between text-sm">
            <span>Overall Progress</span>
            <span>
              {challengeStats.completed}/{challengeStats.total} challenges
              completed
            </span>
          </div>
          <Progress value={challengeStats.percentage} className="h-2" />
        </div>
      </div>

      {/* Progress stats */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Topics Completed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
              <span className="text-2xl font-bold">
                {topicStats.completed}/{topicStats.total}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Challenges Completed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Trophy className="mr-2 h-5 w-5 text-yellow-500" />
              <span className="text-2xl font-bold">
                {challengeStats.completed}/{challengeStats.total}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Completion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <BarChart className="mr-2 h-5 w-5 text-blue-500" />
              <span className="text-2xl font-bold">
                {Math.round(challengeStats.percentage)}%
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Topic progress */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Topic Progress</h3>

        <div className="space-y-3">
          {roadmap.topics.map((topic, index) => (
            <motion.div
              key={topic.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Card
                className={cn(
                  'transition-all hover:shadow-sm',
                  topic.status === 'completed' &&
                    'border-green-200 dark:border-green-900/50',
                )}
              >
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="mb-1 flex items-center">
                        {topic.status === 'completed' ? (
                          <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
                        ) : topic.status === 'in_progress' ? (
                          <Clock className="mr-2 h-5 w-5 text-blue-500" />
                        ) : (
                          <Clock className="mr-2 h-5 w-5 text-gray-400" />
                        )}
                        <h4 className="font-medium">{topic.title}</h4>
                        <Badge variant="outline" className="ml-2">
                          Level {topic.level}
                        </Badge>
                      </div>

                      <div className="mt-2 space-y-1">
                        <div className="flex items-center justify-between text-xs">
                          <span>
                            {topic.challenges
                              ? `${topic.challenges.filter((c) => c.status === 'completed').length}/${topic.challenges.length} challenges`
                              : 'No challenges'}
                          </span>
                          <span>
                            {topic.progress !== undefined
                              ? `${Math.round(topic.progress)}%`
                              : '0%'}
                          </span>
                        </div>
                        <Progress
                          value={topic.progress || 0}
                          className="h-1.5"
                        />
                      </div>
                    </div>

                    <Button variant="ghost" size="icon" asChild>
                      <a href={`/roadmaps/${roadmapId}/topics/${topic.id}`}>
                        <ChevronRight className="h-5 w-5" />
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Next challenge recommendation */}
      {nextRecommended && (
        <div className="mt-8">
          <Card className="border-blue-200 bg-blue-50 dark:border-blue-900/50 dark:bg-blue-950/30">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="mr-2 h-5 w-5 text-blue-500" />
                Next Challenge
              </CardTitle>
              <CardDescription>
                Continue your progress with the next recommended challenge
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">
                    {nextRecommended.challenge.title}
                  </h4>
                  <Badge variant="outline">
                    {nextRecommended.challenge.difficulty}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  From: {nextRecommended.topic.title}
                </p>
                <div className="flex items-center text-sm">
                  <Trophy className="mr-1 h-4 w-4 text-yellow-500" />
                  <span>{nextRecommended.challenge.points} points</span>
                </div>
              </div>
            </CardContent>

            <CardFooter>
              <Button asChild className="w-full">
                <a
                  href={`/coding-challenges/${nextRecommended.challenge.id}`}
                  className="flex items-center justify-center"
                >
                  <ChevronRight className="mr-2 h-4 w-4" />
                  Start Challenge
                </a>
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
    </div>
  );
}
