/**
 * @file QuestionProgressBar.tsx
 * @description Custom progress bar for question timer with warning state
 */
'use client';

import * as React from 'react';

import * as ProgressPrimitive from '@radix-ui/react-progress';

import { cn } from '@/lib/utils';

interface IQuestionProgressBarProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  value: number;
  isWarning?: boolean;
}

const QuestionProgressBar = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  IQuestionProgressBarProps
>(({ className, value, isWarning = false, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      'relative h-2 w-full overflow-hidden rounded-full',
      isWarning ? 'bg-destructive/20' : 'bg-primary/20',
      className,
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={cn(
        'h-full w-full flex-1 transition-all',
        isWarning ? 'bg-destructive' : 'bg-primary',
      )}
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
));

QuestionProgressBar.displayName = 'QuestionProgressBar';

export { QuestionProgressBar };
