import { IScheduleSettingsFormProps } from '../../types';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { CalendarIcon } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { FormField, FormItem, FormLabel } from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { format } from 'date-fns';
import { Clock, Info, Sword, BookOpen } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { FormMessage, FormControl } from '@/components/ui/form';
import { HelpCircle } from 'lucide-react';
import { Users } from 'lucide-react';
import { difficulties } from '@/constants';
import BattleRuleSelector from '../BattleRuleSelector';

// Schedule and Settings Form Component (Step 3)
export default function ScheduleSettingsForm({
  control,
  cardVariants,
  formValues,
  showAdvancedOptions,
  setShowAdvancedOptions,
}: IScheduleSettingsFormProps) {
  return (
    <motion.div variants={cardVariants} initial="hidden" animate="visible">
      <Card className="border-primary/20 hover:border-primary/40 overflow-hidden border-2 transition-all duration-300">
        <div className="from-primary/10 to-primary/5 border-primary/20 border-b bg-gradient-to-r p-4">
          <div className="flex items-center gap-2">
            <h3 className="flex items-center text-lg font-medium">
              <CalendarIcon className="mr-2 h-5 w-5 text-primary" />
              Schedule and Settings
            </h3>
            <Badge
              variant="outline"
              className="bg-primary/10 border-primary/30 ml-auto text-primary"
            >
              Required
            </Badge>
          </div>
        </div>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <FormField
                control={control}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground',
                            )}
                          >
                            {field.value && field.value !== '' ? (
                              format(new Date(field.value), 'PPP')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          selected={
                            field.value ? new Date(field.value) : undefined
                          }
                          onChange={(date: Date | null) =>
                            field.onChange(date ? date.toISOString() : '')
                          }
                          minDate={new Date()}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="time"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Time</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground',
                            )}
                          >
                            {field.value && field.value !== '' ? (
                              format(new Date(field.value), 'h:mm a')
                            ) : (
                              <span>Select time</span>
                            )}
                            <Clock className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-3" align="start">
                        <div className="space-y-3">
                          {/* Common time periods */}
                          <div className="flex flex-col gap-1">
                            <div className="mb-1 text-xs font-medium text-muted-foreground">
                              Common times
                            </div>
                            <div className="grid grid-cols-3 gap-1">
                              {[
                                { label: 'Morning', time: 9 },
                                { label: 'Noon', time: 12 },
                                { label: 'Afternoon', time: 15 },
                                { label: 'Evening', time: 19 },
                                { label: 'Night', time: 21 },
                                { label: 'Now', time: new Date().getHours() },
                              ].map((option) => {
                                const date = new Date();
                                date.setHours(option.time, 0, 0, 0);
                                return (
                                  <Button
                                    key={option.label}
                                    variant="outline"
                                    size="sm"
                                    className="h-8 justify-start text-xs font-normal"
                                    onClick={() => {
                                      field.onChange(date.toISOString());

                                      // Auto-scroll to the selected time
                                      setTimeout(() => {
                                        const timeGrid = document.querySelector(
                                          '.time-grid-container',
                                        );
                                        const selectedButton =
                                          timeGrid?.querySelector(
                                            '.selected-time',
                                          );
                                        if (timeGrid && selectedButton) {
                                          selectedButton.scrollIntoView({
                                            behavior: 'smooth',
                                            block: 'center',
                                          });
                                        }
                                      }, 50); // Small delay to ensure DOM updates
                                    }}
                                  >
                                    {option.label}
                                  </Button>
                                );
                              })}
                            </div>
                          </div>

                          {/* Time grid */}
                          <div className="flex flex-col gap-1">
                            <div className="mb-1 text-xs font-medium text-muted-foreground">
                              Select specific time
                            </div>
                            <div className="time-grid-container grid max-h-[200px] grid-cols-4 gap-1 overflow-y-auto pr-1">
                              {Array.from({ length: 36 }).map((_, index) => {
                                // Start at 6:00 AM (6 hours * 60 minutes = 360 minutes from midnight)
                                // Each step is 30 minutes
                                const minutes = 360 + index * 30;
                                const hours = Math.floor(minutes / 60);
                                const mins = minutes % 60;

                                // Create a date object for this time
                                const date = new Date();
                                date.setHours(hours, mins, 0, 0);

                                // Format for display
                                const timeDisplay = date.toLocaleTimeString(
                                  'en-US',
                                  {
                                    hour: 'numeric',
                                    minute: '2-digit',
                                    hour12: true,
                                  },
                                );

                                return (
                                  <Button
                                    key={`${hours}-${mins}`}
                                    variant="ghost"
                                    size="sm"
                                    className={cn(
                                      'h-8 justify-center text-xs font-normal',
                                      field.value &&
                                        new Date(field.value).getHours() ===
                                          hours &&
                                        new Date(field.value).getMinutes() ===
                                          mins &&
                                        'hover:bg-primary/90 selected-time bg-primary text-white',
                                    )}
                                    onClick={() => {
                                      field.onChange(date.toISOString());
                                    }}
                                  >
                                    {timeDisplay}
                                  </Button>
                                );
                              })}
                            </div>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <FormLabel className="text-base">Battle Settings</FormLabel>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  className="text-xs"
                >
                  {showAdvancedOptions ? 'Hide' : 'Show'} Options
                </Button>
              </div>

              <div className="col-span-1 mt-2 md:col-span-2">
                <FormField
                  control={control}
                  name="maxParticipants"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="flex items-center gap-1 text-base">
                        <Users className="mr-1.5 h-4 w-4 text-primary" />
                        Max Participants
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Maximum number of participants allowed</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Users className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
                          <Input
                            type="number"
                            min="1"
                            max="100"
                            value={field.value ?? ''}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value, 10) || 10)
                            }
                            onBlur={field.onBlur}
                            name={field.name}
                            ref={field.ref}
                            className="focus:ring-primary/20 pl-10 transition-all duration-200 focus:border-primary focus:ring-2"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="mt-8">
                <div className="mb-4 flex items-center justify-between border-b border-border pb-2">
                  <div className="flex items-center gap-2">
                    <Sword className="h-5 w-5 text-primary" />
                    <FormLabel className="m-0 text-base font-medium">
                      Battle Settings
                    </FormLabel>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                    className="hover:bg-primary/10 flex items-center gap-1 text-xs transition-all duration-200 hover:text-primary"
                  >
                    {showAdvancedOptions ? 'Hide' : 'Show'} Options
                    <Info className="ml-1 h-3 w-3" />
                  </Button>
                </div>
                <div className="space-y-6">
                  <FormField
                    control={control}
                    name="difficulty"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="flex items-center gap-1 text-base">
                          Difficulty
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Set the difficulty level of questions</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <div className="flex w-full space-x-2">
                            {Object.values(difficulties).map((difficulty) => {
                              const isSelected = field.value === difficulty;
                              const difficultyColor =
                                difficulty === 'easy'
                                  ? 'bg-green-500/90 hover:bg-green-500'
                                  : difficulty === 'medium'
                                    ? 'bg-amber-500/90 hover:bg-amber-500'
                                    : 'bg-red-500/90 hover:bg-red-500';

                              return (
                                <Button
                                  key={difficulty}
                                  type="button"
                                  variant={isSelected ? 'default' : 'outline'}
                                  className={cn(
                                    'flex-1 transition-all duration-200',
                                    isSelected
                                      ? `${difficultyColor} border-transparent text-white`
                                      : 'hover:border-primary/40',
                                  )}
                                  onClick={() => field.onChange(difficulty)}
                                >
                                  {difficulty === 'easy' && (
                                    <span className="mr-1.5 text-xs">🟢</span>
                                  )}
                                  {difficulty === 'medium' && (
                                    <span className="mr-1.5 text-xs">🟠</span>
                                  )}
                                  {difficulty === 'hard' && (
                                    <span className="mr-1.5 text-xs">🔴</span>
                                  )}
                                  {difficulty.charAt(0).toUpperCase() +
                                    difficulty.slice(1)}
                                </Button>
                              );
                            })}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
                    <FormField
                      control={control}
                      name="totalQuestions"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="flex items-center gap-1">
                            Total Questions
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <HelpCircle className="ml-1 h-3 w-3 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Number of questions in the battle</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 -translate-y-1/2 transform font-medium text-muted-foreground">
                                Q
                              </span>
                              <Input
                                type="number"
                                min="1"
                                max="50"
                                value={field.value ?? ''}
                                onChange={(e) =>
                                  field.onChange(
                                    parseInt(e.target.value, 10) || 10,
                                  )
                                }
                                onBlur={field.onBlur}
                                name={field.name}
                                ref={field.ref}
                                className="focus:ring-primary/20 pl-8 transition-all duration-200 focus:border-primary focus:ring-2"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Battle Rules Section */}
              <div className="mt-8">
                <div className="mb-4 flex items-center justify-between border-b border-border pb-2">
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-primary" />
                    <FormLabel className="m-0 text-base font-medium">
                      Battle Rules
                    </FormLabel>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Select rules that will apply to this battle</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="space-y-2">
                  <p className="mb-4 text-sm text-muted-foreground">
                    Choose rules that will apply to this battle. Default rules
                    are automatically applied.
                  </p>
                  <BattleRuleSelector
                    control={control}
                    setValue={(_name, _value) => {
                      // This is a workaround since we don't have direct access to the form's setValue function
                      // The component will internally update the form control value through the Controller
                    }}
                    selectedRuleIds={(formValues.rule_ids as string[]) || []}
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
