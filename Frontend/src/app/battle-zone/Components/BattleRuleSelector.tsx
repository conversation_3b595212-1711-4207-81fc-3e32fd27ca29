'use client';

import { useEffect, useState } from 'react';
import { Controller } from 'react-hook-form';
import { Award, Clock, Filter, Shield, Users } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';

import { IBattleRule, RuleCategory } from './BattleRules';
import { IBattleRuleSelectorProps } from '../types';

export default function BattleRuleSelector({
  control,
  setValue,
  selectedRuleIds = [],
}: IBattleRuleSelectorProps) {
  const { toast } = useToast();
  const [rules, setRules] = useState<IBattleRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRules, setSelectedRules] = useState<string[]>(selectedRuleIds);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch rules on component mount
  useEffect(() => {
    const fetchRules = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/battle-rules');

        if (!response.ok) {
          throw new Error('Failed to fetch battle rules');
        }

        const data = await response.json();
        const activeRules =
          data.data?.filter((rule: IBattleRule) => rule.is_active) || [];
        setRules(activeRules);
      } catch (error) {
        console.error('Error fetching battle rules:', error);
        toast({
          title: 'Error',
          description: 'Failed to load battle rules. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchRules();
  }, [toast]);

  // Update form value when selected rules change
  useEffect(() => {
    setValue('rule_ids', selectedRules);
  }, [selectedRules, setValue]);

  // Toggle rule selection
  const toggleRuleSelection = (ruleId: string) => {
    setSelectedRules((prev) => {
      if (prev.includes(ruleId)) {
        return prev.filter((id) => id !== ruleId);
      } else {
        return [...prev, ruleId];
      }
    });
  };

  // Filter rules based on active tab
  const filteredRules = rules.filter((rule) => {
    if (activeTab === 'all') return true;
    if (activeTab === 'default') return rule.is_default;
    return rule.category === activeTab;
  });

  // Get selected rules for display
  const getSelectedRules = () => {
    return rules.filter((rule) => selectedRules.includes(rule.id));
  };

  // Helper function to get category badge color
  const getCategoryColor = (category: RuleCategory) => {
    switch (category) {
      case 'SCORING':
        return 'bg-blue-500 hover:bg-blue-600';
      case 'TIMING':
        return 'bg-yellow-500 hover:bg-yellow-600';
      case 'PARTICIPATION':
        return 'bg-green-500 hover:bg-green-600';
      case 'QUESTION':
        return 'bg-purple-500 hover:bg-purple-600';
      case 'MODERATION':
        return 'bg-red-500 hover:bg-red-600';
      case 'OTHER':
        return 'bg-gray-500 hover:bg-gray-600';
      default:
        return 'bg-gray-500 hover:bg-gray-600';
    }
  };

  // Get icon based on rule category
  const getRuleIcon = (category: RuleCategory) => {
    switch (category) {
      case 'SCORING':
        return <Award className="h-4 w-4" />;
      case 'TIMING':
        return <Clock className="h-4 w-4" />;
      case 'PARTICIPATION':
        return <Users className="h-4 w-4" />;
      case 'QUESTION':
        return <Filter className="h-4 w-4" />;
      case 'MODERATION':
        return <Shield className="h-4 w-4" />;
      default:
        return <Filter className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-4">
      <Controller
        name="rule_ids"
        control={control}
        defaultValue={selectedRuleIds}
        render={({ field }) => (
          <>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <span className="mr-2">
                    <Filter className="h-4 w-4" />
                  </span>
                  <span>Select Battle Rules</span>
                  {selectedRules.length > 0 && (
                    <Badge variant="secondary" className="ml-auto">
                      {selectedRules.length} selected
                    </Badge>
                  )}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Select Battle Rules</DialogTitle>
                  <DialogDescription>
                    Choose rules that will apply to this battle. Default rules
                    are automatically applied.
                  </DialogDescription>
                </DialogHeader>

                <Tabs
                  defaultValue="all"
                  value={activeTab}
                  onValueChange={setActiveTab}
                >
                  <TabsList className="mb-4">
                    <TabsTrigger value="all">All Rules</TabsTrigger>
                    <TabsTrigger value="default">Default</TabsTrigger>
                    <TabsTrigger value="SCORING">Scoring</TabsTrigger>
                    <TabsTrigger value="TIMING">Timing</TabsTrigger>
                    <TabsTrigger value="PARTICIPATION">
                      Participation
                    </TabsTrigger>
                    <TabsTrigger value="QUESTION">Question</TabsTrigger>
                    <TabsTrigger value="MODERATION">Moderation</TabsTrigger>
                  </TabsList>

                  <TabsContent value={activeTab} className="space-y-4">
                    {loading ? (
                      <div className="flex justify-center py-8">
                        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
                      </div>
                    ) : filteredRules.length === 0 ? (
                      <div className="py-8 text-center">
                        <p className="text-muted-foreground">
                          No rules found in this category.
                        </p>
                      </div>
                    ) : (
                      <ScrollArea className="h-[300px] pr-4">
                        <div className="space-y-2">
                          {filteredRules.map((rule) => (
                            <div
                              key={rule.id}
                              className="flex items-center space-x-2 rounded-md border p-3"
                            >
                              <Checkbox
                                id={`rule-${rule.id}`}
                                checked={selectedRules.includes(rule.id)}
                                onCheckedChange={() =>
                                  toggleRuleSelection(rule.id)
                                }
                              />
                              <div className="flex-1 space-y-1">
                                <div className="flex items-center">
                                  <label
                                    htmlFor={`rule-${rule.id}`}
                                    className="flex cursor-pointer items-center text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                  >
                                    <span className="mr-2">
                                      {getRuleIcon(rule.category)}
                                    </span>
                                    {rule.name}
                                  </label>
                                  <div className="ml-auto flex items-center space-x-2">
                                    <Badge
                                      className={`${getCategoryColor(rule.category)}`}
                                    >
                                      {rule.category}
                                    </Badge>
                                    {rule.is_default && (
                                      <Badge variant="outline">Default</Badge>
                                    )}
                                  </div>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {rule.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    )}
                  </TabsContent>
                </Tabs>

                <DialogFooter>
                  <Button
                    type="button"
                    onClick={() => {
                      field.onChange(selectedRules);
                      setIsDialogOpen(false);
                    }}
                  >
                    Apply Rules
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Selected Rules Preview */}
            {selectedRules.length > 0 && (
              <Card className="mt-2">
                <CardContent className="p-4">
                  <h4 className="mb-2 text-sm font-medium">Selected Rules:</h4>
                  <div className="space-y-2">
                    {getSelectedRules().map((rule) => (
                      <div key={rule.id} className="flex items-center gap-2">
                        <Badge
                          className={`${getCategoryColor(rule.category)} flex h-6 w-6 items-center justify-center p-0`}
                        >
                          {getRuleIcon(rule.category)}
                        </Badge>
                        <span className="text-sm">{rule.name}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}
      />
    </div>
  );
}
