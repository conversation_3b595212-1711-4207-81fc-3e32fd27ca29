import { Card } from '@/components/CardHoverEffect';
import { IBasicInformationFormProps } from '../../types';
import { motion } from 'framer-motion';
import { CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Input } from '@/components/ui/input';
import { HelpCircle } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';

// Basic Information Form Component (Step 1)
export default function BasicInformationForm({
  control,
  cardVariants,
}: IBasicInformationFormProps) {
  return (
    <motion.div variants={cardVariants} initial="hidden" animate="visible">
      <Card className="border-primary/20 hover:border-primary/40 border-2 transition-all duration-300">
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-medium">Basic Information</h3>
              <Badge variant="outline" className="ml-auto">
                Required
              </Badge>
            </div>

            <FormField
              control={control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Battle Title
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            Choose a catchy title that describes your battle
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter a catchy title"
                      {...field}
                      className="focus:ring-primary/20 transition-all duration-200 focus:ring-2"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Description
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Provide details about what this battle is about</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe what this battle is about"
                      rows={4}
                      {...field}
                      className="focus:ring-primary/20 transition-all duration-200 focus:ring-2"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
