import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { HelpCircle } from 'lucide-react';
import { ISubjectTopicFormProps } from '../../types';
import { Card, CardContent } from '@/components/ui/card';
import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { FormControl } from '@/components/ui/form';
import { PaginatedSelect } from '@/components/ui/paginated-select';

// Subject and Topic Form Component (Step 2)
export default function SubjectTopicForm({
  control,
  cardVariants,
  formValues,
  selectedSubjectOption,
  setSelectedSubjectOption,
  selectedTopicOption,
  setSelectedTopicOption,
  getSubjectsForSelect,
  getTopicsForSelect,
  setFormValue,
}: ISubjectTopicFormProps) {
  return (
    <motion.div variants={cardVariants} initial="hidden" animate="visible">
      <Card className="border-primary/20 hover:border-primary/40 border-2 transition-all duration-300">
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-medium">Subject and Topic</h3>
              <Badge variant="outline" className="ml-auto">
                Required
              </Badge>
            </div>

            <FormField
              control={control}
              name="subjectId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Subject
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Select the subject area for your battle</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormLabel>
                  <FormControl>
                    <PaginatedSelect
                      getOptions={getSubjectsForSelect}
                      value={selectedSubjectOption}
                      onChange={(option) => {
                        setSelectedSubjectOption(option);
                        if (option) {
                          field.onChange(option.value);
                          setFormValue('topicId', '');
                          setSelectedTopicOption(null);
                        } else {
                          field.onChange('');
                        }
                      }}
                      placeholder="Select a subject"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="topicId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-1">
                    Topic
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <HelpCircle className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Select a specific topic within the subject</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </FormLabel>
                  <FormControl>
                    <PaginatedSelect
                      key={`topic-select-${formValues.subjectId}`}
                      getOptions={getTopicsForSelect}
                      value={selectedTopicOption}
                      onChange={(option) => {
                        setSelectedTopicOption(option);
                        if (option) {
                          field.onChange(option.value);
                        } else {
                          field.onChange('');
                        }
                      }}
                      placeholder={
                        !formValues.subjectId
                          ? 'Select a subject first'
                          : 'Select a topic'
                      }
                      isDisabled={!formValues.subjectId}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
