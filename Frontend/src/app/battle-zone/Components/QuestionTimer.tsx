/**
 * @file QuestionTimer.tsx
 * @description Countdown timer component specifically for battle questions
 */
import React, { useEffect, useState } from 'react';

import { AlertCircle, Clock } from 'lucide-react';

import { QuestionProgressBar } from './QuestionProgressBar';

interface IQuestionTimerProps {
  duration: number; // Duration in seconds
  onTimeUp: () => void;
  paused?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showWarning?: boolean; // Show warning when time is running low
  warningThreshold?: number; // Percentage threshold for warning (e.g., 25 means show warning at 25% time left)
}

export function QuestionTimer({
  duration,
  onTimeUp,
  paused = false,
  size = 'md',
  showWarning = true,
  warningThreshold = 25,
}: IQuestionTimerProps) {
  const [timeLeft, setTimeLeft] = useState<number>(duration);
  const [isWarning, setIsWarning] = useState<boolean>(false);

  // Reset timer when duration changes
  useEffect(() => {
    setTimeLeft(duration);
  }, [duration]);

  // Timer logic
  useEffect(() => {
    if (paused) return;

    const interval = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 0) {
          clearInterval(interval);
          onTimeUp();
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [duration, onTimeUp, paused]);

  // Check for warning threshold
  useEffect(() => {
    const percentageLeft = (timeLeft / duration) * 100;
    setIsWarning(showWarning && percentageLeft <= warningThreshold);
  }, [timeLeft, duration, showWarning, warningThreshold]);

  // Calculate progress percentage
  const progressPercentage = (timeLeft / duration) * 100;

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Determine size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'lg':
        return 'text-3xl';
      case 'md':
        return 'text-2xl';
      case 'sm':
        return 'text-xl';
      default:
        return 'text-2xl';
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-center gap-2">
        {isWarning ? (
          <AlertCircle className={`h-5 w-5 animate-pulse text-destructive`} />
        ) : (
          <Clock className={`h-5 w-5 text-muted-foreground`} />
        )}
        <span
          className={`font-mono font-bold ${getSizeClasses()} ${isWarning ? 'text-destructive' : ''}`}
        >
          {formatTime(timeLeft)}
        </span>
      </div>
      <QuestionProgressBar value={progressPercentage} isWarning={isWarning} />
    </div>
  );
}

export default QuestionTimer;
