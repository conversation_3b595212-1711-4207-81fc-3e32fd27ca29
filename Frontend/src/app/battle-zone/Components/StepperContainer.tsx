import { Stepper } from '@/app/details/components/Stepper';

interface IStepperContainerProps {
  stepTitles: string[];
  activeStep: number;
  setActiveStep: (step: number) => void;
}

/**
 * Container component for the stepper
 */
export function StepperContainer({
  stepTitles,
  activeStep,
  setActiveStep,
}: IStepperContainerProps) {
  return (
    <div className="mb-8">
      <Stepper
        steps={stepTitles}
        currentStep={activeStep - 1} // The Stepper component is 0-indexed
        onStepClick={(stepIndex) => {
          // Only allow going back to previous steps
          if (stepIndex + 1 < activeStep) {
            setActiveStep(stepIndex + 1);
          }
        }}
      />
    </div>
  );
}
