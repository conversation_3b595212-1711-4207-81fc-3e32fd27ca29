/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */
'use client';

import React from 'react';

import { But<PERSON> } from '@/components/ui/button';

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

/**
 * @file index.tsx
 * @description Next.js page for battle-zone/Components/ChallengeCard route
 */

export default function ChallengeCard() {
  return (
    <div className="w-max rounded-md bg-lightSecondary p-4">
      <div className="flex w-max flex-col gap-8">
        <span>Challenge your opponents</span>
        <p>Conquer Challenges and Rise as a Champion in our BattleZone.</p>
        <Button
          onClick={() => {}}
          className="bg-primary text-white hover:bg-primary2"
        >
          Challenge
        </Button>
      </div>
    </div>
  );
}
