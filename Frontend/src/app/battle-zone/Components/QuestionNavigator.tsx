/**
 * @file QuestionNavigator.tsx
 * @description Navigation component for battle questions with visual indicators
 */
import React from 'react';

import { ChevronLeft, ChevronRight, Check, HelpCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface IQuestionNavigatorProps {
  currentIndex: number;
  totalQuestions: number;
  answeredQuestions: number[];
  onNavigate: (index: number) => void;
  compact?: boolean;
  maxVisible?: number;
}

export function QuestionNavigator({
  currentIndex,
  totalQuestions,
  answeredQuestions,
  onNavigate,
  compact = false,
  maxVisible = 10,
}: IQuestionNavigatorProps) {
  // Logic to determine which question indicators to show
  const getVisibleIndicators = () => {
    if (totalQuestions <= maxVisible) {
      // If total questions is less than max visible, show all
      return Array.from({ length: totalQuestions }, (_, i) => i);
    }

    // Calculate how many indicators to show on each side of current
    const sideCount = Math.floor((maxVisible - 1) / 2);

    let start = Math.max(0, currentIndex - sideCount);
    const end = Math.min(totalQuestions - 1, start + maxVisible - 1);

    // Adjust start if end is at max
    if (end === totalQuestions - 1) {
      start = Math.max(0, end - maxVisible + 1);
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  };

  const visibleIndicators = getVisibleIndicators();

  return (
    <div className="flex flex-col space-y-4">
      {/* Question indicators */}
      <div className="flex items-center justify-center space-x-2">
        {/* Previous button */}
        <Button
          variant="outline"
          size="icon"
          disabled={currentIndex === 0}
          onClick={() => onNavigate(currentIndex - 1)}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Question indicators */}
        <div className="flex items-center space-x-2">
          {visibleIndicators[0] > 0 && (
            <span className="text-xs text-muted-foreground">...</span>
          )}

          {visibleIndicators.map((index) => {
            const isAnswered = answeredQuestions.includes(index);
            const isCurrent = index === currentIndex;

            return (
              <TooltipProvider key={index}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={isCurrent ? 'default' : 'outline'}
                      size="icon"
                      className={`h-8 w-8 ${isAnswered && !isCurrent ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : ''}`}
                      onClick={() => onNavigate(index)}
                    >
                      {isAnswered ? (
                        <Check
                          className={`h-4 w-4 ${isCurrent ? '' : 'text-green-600 dark:text-green-400'}`}
                        />
                      ) : (
                        <span className="text-xs">{index + 1}</span>
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      Question {index + 1}:{' '}
                      {isAnswered ? 'Answered' : 'Not answered'}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            );
          })}

          {visibleIndicators[visibleIndicators.length - 1] <
            totalQuestions - 1 && (
            <span className="text-xs text-muted-foreground">...</span>
          )}
        </div>

        {/* Next button */}
        <Button
          variant="outline"
          size="icon"
          disabled={currentIndex === totalQuestions - 1}
          onClick={() => onNavigate(currentIndex + 1)}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Navigation buttons for non-compact mode */}
      {!compact && (
        <div className="flex justify-between">
          <Button
            variant="outline"
            disabled={currentIndex === 0}
            onClick={() => onNavigate(currentIndex - 1)}
            className="w-28"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>

          <div className="flex items-center gap-2">
            <HelpCircle className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              Question {currentIndex + 1} of {totalQuestions}
            </span>
          </div>

          <Button
            variant="outline"
            disabled={currentIndex === totalQuestions - 1}
            onClick={() => onNavigate(currentIndex + 1)}
            className="w-28"
          >
            Next
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}

export default QuestionNavigator;
