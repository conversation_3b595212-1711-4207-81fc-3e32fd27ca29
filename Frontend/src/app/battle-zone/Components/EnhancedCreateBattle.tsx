/**
 * @file EnhancedCreateBattle.tsx
 * @description Next.js page for battle-zone/Components route
 * @note This file has been refactored to separate concerns using custom hooks
 */
import { useState } from 'react';
import { FormProvider } from 'react-hook-form';
import { motion } from 'framer-motion';

import { useToast } from '@/hooks/use-toast';
import { useBattleCreation } from '@/hooks/useBattleCreation';

// Import custom hooks
import { useBattleForm } from '../hooks/useBattleForm';
import { useSubjectsAndTopics } from '../hooks/useSubjectsAndTopics';
import { useStepNavigation } from '../hooks/useStepNavigation';
import { useBattleSubmission } from '../hooks/useBattleSubmission';

import { IEnhancedCreateBattleFormProps } from '../types';
import PreviewMode from './Preview';
import BasicInformationForm from './BasicInfo';
import SubjectTopicForm from './SubjectTopic';
import ScheduleSettingsForm from './ScheduleSettings';
import FormNavigation from './FormNavigation';

// Create file: /src/app/battle-zone/Components/BattleFormHeader.tsx
import { BattleFormHeader } from './BattleFormHeader';
// Create file: /src/app/battle-zone/Components/StepperContainer.tsx
import { StepperContainer } from './StepperContainer';

export function EnhancedCreateBattleForm({
  onSuccess,
  onCancel,
}: IEnhancedCreateBattleFormProps) {
  const { toast } = useToast();
  const { createBattle, isSubmitting, validationErrors } = useBattleCreation();
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Use custom hooks to separate concerns
  const { form, formValues, control, setValue, handleSubmit, isValid } =
    useBattleForm();

  const {
    selectedSubjectOption,
    setSelectedSubjectOption,
    selectedTopicOption,
    setSelectedTopicOption,
    getSubjectsForSelect,
    getTopicsForSelect,
    currentSubjectName,
    currentTopicName,
  } = useSubjectsAndTopics(formValues, setValue, toast);

  const {
    activeStep,
    setActiveStep,
    nextStep,
    prevStep,
    isStepValid,
    stepTitles,
  } = useStepNavigation(formValues);

  // These functions have been moved to useSubjectsAndTopics hook
  // Create file: /src/app/battle-zone/hooks/useSubjectsAndTopics.ts

  // Handle form submission - show preview
  const onSubmit = () => {
    setShowPreview(true);
  };

  // Use the battle submission hook
  const { prepareBattleData } = useBattleSubmission();

  // Handle actual form submission after preview
  const handleFinalSubmit = async () => {
    try {
      // Prepare data according to backend validation schema
      const battleData = prepareBattleData(formValues);

      // Send the formatted data to the backend
      const response = await createBattle(battleData);

      if (response && response.data) {
        toast({
          title: 'Battle created successfully!',
          description: 'Your battle has been created and is now available.',
        });

        if (onSuccess && response.data.id) {
          onSuccess(response.data.id);
        }
      }
    } catch (error: unknown) {
      console.error('Error creating battle:', error);

      // Display validation errors if available
      if (validationErrors && validationErrors.length > 0) {
        // Create a formatted error message with all validation errors
        const errorMessages = validationErrors
          .map((err) => `• ${err.field}: ${err.message}`)
          .join('\n');

        toast({
          title: 'Validation Error',
          description: `Please fix the following issues:\n${errorMessages}`,
          variant: 'destructive',
        });

        // Go back to edit mode to fix the errors
        setShowPreview(false);

        // Try to set the active step based on the field that failed validation
        const firstErrorField = validationErrors[0]?.field;
        if (firstErrorField) {
          // Basic information fields (step 1)
          if (
            ['title', 'description', 'difficulty', 'type'].includes(
              firstErrorField,
            )
          ) {
            setActiveStep(1);
          }
          // Subject/topic fields (step 2)
          else if (['topic_id'].includes(firstErrorField)) {
            setActiveStep(2);
          }
          // Schedule and settings fields (step 3)
          else if (
            [
              'start_time',
              'end_time',
              'max_participants',
              'points_per_question',
              'time_per_question',
              'total_questions',
            ].includes(firstErrorField)
          ) {
            setActiveStep(3);
          }
        }
      } else {
        // Generic error handling
        toast({
          title: 'Failed to create battle',
          description:
            'There was an error creating your battle. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  // Function to go back from preview to form editing
  const backToEdit = () => {
    setShowPreview(false);
  };

  // Animation variants - could be moved to a separate constants file
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="w-full space-y-6"
    >
      {/* Title - Extracted to BattleFormHeader component */}
      <BattleFormHeader title="Create Battle" />

      {/* Common Stepper Component - Extracted to StepperContainer component */}
      <StepperContainer
        stepTitles={stepTitles}
        activeStep={activeStep}
        setActiveStep={setActiveStep}
      />

      {!showPreview ? (
        // Use FormProvider with a key to force remount and prevent serialization issues
        <FormProvider key={`form-provider-${activeStep}`} {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Step 1: Basic Information */}
            {activeStep === 1 && (
              <BasicInformationForm
                control={control}
                cardVariants={cardVariants}
              />
            )}

            {/* Step 2: Subject and Topic */}
            {activeStep === 2 && (
              <SubjectTopicForm
                control={control}
                cardVariants={cardVariants}
                formValues={formValues}
                selectedSubjectOption={selectedSubjectOption}
                setSelectedSubjectOption={setSelectedSubjectOption}
                selectedTopicOption={selectedTopicOption}
                setSelectedTopicOption={setSelectedTopicOption}
                getSubjectsForSelect={getSubjectsForSelect}
                getTopicsForSelect={getTopicsForSelect}
                setFormValue={(name, value) => setValue(name, value)}
              />
            )}

            {/* Step 3: Schedule and Settings */}
            {activeStep === 3 && (
              <ScheduleSettingsForm
                control={control}
                cardVariants={cardVariants}
                formValues={formValues}
                showAdvancedOptions={showAdvancedOptions}
                setShowAdvancedOptions={setShowAdvancedOptions}
              />
            )}

            {/* Navigation buttons */}
            <FormNavigation
              activeStep={activeStep}
              prevStep={prevStep}
              nextStep={nextStep}
              isCurrentStepValid={isStepValid()}
              onCancel={onCancel}
              isSubmitting={isSubmitting}
              isValid={isValid}
            />
          </form>
        </FormProvider>
      ) : (
        <PreviewMode
          formValues={formValues}
          currentSubjectName={currentSubjectName}
          currentTopicName={currentTopicName}
          backToEdit={backToEdit}
          handleFinalSubmit={handleFinalSubmit}
          isSubmitting={isSubmitting}
        />
      )}
    </motion.div>
  );
}

export default EnhancedCreateBattleForm;
