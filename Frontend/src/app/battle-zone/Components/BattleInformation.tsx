/**
 * @file BattleInformation.tsx
 * @description Next.js page for battle-zone/Components route
 */
import React from 'react';

import { motion } from 'framer-motion';
import {
  Award,
  Brain,
  Clock,
  HelpCircle,
  Target,
  Timer,
  Trophy,
  Users,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { IBattle } from '@/types/battle';

type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline';

const getDifficultyVariant = (difficulty: string): BadgeVariant => {
  switch (difficulty.toLowerCase()) {
    case 'easy':
      return 'default';
    case 'medium':
      return 'secondary';
    default:
      return 'destructive';
  }
};

interface IBattleInformationProps {
  battle: IBattle;
  isLoading?: boolean;
}

export default function BattleInformation({
  battle,
  isLoading = false,
}: IBattleInformationProps) {
  // If battle is null or undefined, show a placeholder
  if (!battle && !isLoading) {
    return (
      <Card className="p-6">
        <div className="py-8 text-center">
          <h3 className="mb-2 text-lg font-semibold">Battle Not Found</h3>
          <p className="text-muted-foreground">
            The battle information could not be loaded.
          </p>
        </div>
      </Card>
    );
  }

  // If still loading, show skeleton
  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="space-y-4">
          <div className="h-6 w-1/3 animate-pulse rounded bg-muted"></div>
          <div className="h-4 w-full animate-pulse rounded bg-muted"></div>
          <div className="h-4 w-full animate-pulse rounded bg-muted"></div>
          <div className="mt-6 grid grid-cols-1 gap-4 md:grid-cols-2">
            {Array(8)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="flex items-center gap-3">
                  <div className="h-10 w-10 animate-pulse rounded-full bg-muted"></div>
                  <div className="space-y-2">
                    <div className="h-3 w-20 animate-pulse rounded bg-muted"></div>
                    <div className="h-4 w-24 animate-pulse rounded bg-muted"></div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </Card>
    );
  }
  // Format dates for better display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not specified';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';

      return date.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid date';
    }
  };

  // Format difficulty for display
  const formatDifficulty = (difficulty?: string) => {
    if (!difficulty) return 'Not specified';
    return (
      difficulty.charAt(0).toUpperCase() + difficulty.slice(1).toLowerCase()
    );
  };

  // Calculate duration in minutes
  const calculateDuration = (
    timePerQuestion?: number,
    totalQuestions?: number,
  ) => {
    if (
      timePerQuestion === undefined ||
      totalQuestions === undefined ||
      timePerQuestion <= 0 ||
      totalQuestions <= 0
    ) {
      return 'Not specified';
    }

    try {
      const durationMinutes = Math.round(
        (timePerQuestion * totalQuestions) / 60,
      );
      return `${durationMinutes} minute${durationMinutes !== 1 ? 's' : ''}`;
    } catch (error) {
      console.error('Error calculating duration:', error);
      return 'Not specified';
    }
  };

  // Safely access battle properties with fallbacks
  const infoItems = [
    {
      icon: Trophy,
      label: 'Prize',
      value: battle?.prize ? `${battle.prize} points` : 'Not specified',
    },
    {
      icon: Clock,
      label: 'Duration',
      value: calculateDuration(
        battle?.time_per_question,
        battle?.total_questions,
      ),
    },
    {
      icon: Users,
      label: 'Participants',
      value: `${battle?.current_participants ?? 0} / ${battle?.max_participants ?? 'unlimited'}`,
    },
    {
      icon: Brain,
      label: 'Difficulty',
      value: formatDifficulty(battle?.difficulty),
      badge: true,
      variant: battle?.difficulty
        ? getDifficultyVariant(battle.difficulty)
        : 'outline',
    },
    {
      icon: Timer,
      label: 'Time per Question',
      value: battle?.time_per_question
        ? `${battle.time_per_question} seconds`
        : 'Not specified',
    },
    {
      icon: HelpCircle,
      label: 'Total Questions',
      value: battle?.total_questions
        ? `${battle.total_questions} questions`
        : 'Not specified',
    },
    {
      icon: Target,
      label: 'Points per Question',
      value: battle?.points_per_question
        ? `${battle.points_per_question} points`
        : 'Not specified',
    },
    {
      icon: Award,
      label: 'Category',
      value:
        battle?.category ||
        (battle?.topic?.title ? battle.topic.title : 'General'),
      badge: true,
      variant: 'default' as BadgeVariant,
    },
    // Add start and end time
    {
      icon: Clock,
      label: 'Start Time',
      value: formatDate(battle?.start_time),
    },
    {
      icon: Clock,
      label: 'End Time',
      value: formatDate(battle?.end_time),
    },
  ];

  return (
    <Card className="p-6">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Battle Description */}
        <div>
          <h3 className="font-medium">Description</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {battle?.description || 'No description available for this battle.'}
          </p>
        </div>

        <Separator />

        {/* Battle Details */}
        <div className="grid gap-6 sm:grid-cols-2">
          {infoItems.map((item, index) => (
            <motion.div
              key={item.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.3,
                delay: index * 0.1,
              }}
              className="flex items-center gap-4"
            >
              <div
                className={cn(
                  'flex h-10 w-10 items-center justify-center rounded-lg',
                  isLoading ? 'animate-pulse bg-muted' : 'bg-primary/10',
                )}
              >
                {!isLoading && <item.icon className="h-5 w-5 text-primary" />}
              </div>

              <div className="flex-1">
                <p className="text-sm text-muted-foreground">{item.label}</p>
                {item.badge ? (
                  <Badge
                    variant={item.variant || 'default'}
                    className={cn(
                      'mt-1',
                      isLoading && 'animate-pulse bg-muted',
                    )}
                  >
                    {item.value}
                  </Badge>
                ) : (
                  <p
                    className={cn(
                      'font-medium',
                      isLoading && 'animate-pulse text-muted',
                    )}
                  >
                    {item.value}
                  </p>
                )}
              </div>
            </motion.div>
          ))}
        </div>

        <Separator />

        {/* Battle Rules */}
        <div>
          <h3 className="font-medium">Rules</h3>
          <ul className="mt-2 list-inside list-disc space-y-2 text-sm text-muted-foreground">
            <motion.li
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              Each question has a time limit of {battle.time_per_question}{' '}
              seconds
            </motion.li>
            <motion.li
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              You can earn up to {battle.points_per_question} points per
              question
            </motion.li>
            <motion.li
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              The faster you answer correctly, the more points you earn
            </motion.li>
            <motion.li
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              Wrong answers will not deduct points
            </motion.li>
            <motion.li
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              The participant with the most points at the end wins
            </motion.li>
          </ul>
        </div>
      </motion.div>
    </Card>
  );
}
