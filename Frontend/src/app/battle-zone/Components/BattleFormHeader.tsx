import { motion } from 'framer-motion';

interface IBattleFormHeaderProps {
  title: string;
}

/**
 * Header component for the battle form
 */
export function BattleFormHeader({ title }: IBattleFormHeaderProps) {
  return (
    <div className="mb-4">
      <motion.h2
        initial={{ x: -20 }}
        animate={{ x: 0 }}
        className="text-xl font-semibold"
      >
        {title}
      </motion.h2>
    </div>
  );
}
