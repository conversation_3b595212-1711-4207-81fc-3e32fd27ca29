/**
 * @file BattlePreview.tsx
 * @description Next.js page for battle-zone/Components route
 */
import { format } from 'date-fns';
import { Award, Bar<PERSON><PERSON>, Timer, Users } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

import { BattleFormValues } from './battleFormValidation';

interface IBattlePreviewProps {
  formValues: BattleFormValues;
  subjectName: string;
  topicName: string;
}

export function BattlePreview({
  formValues,
  subjectName,
  topicName,
}: IBattlePreviewProps) {
  const {
    title,
    description,
    difficulty,
    length,
    date,
    time,
    maxParticipants = 10,
    pointsPerQuestion = 10,
    timePerQuestion = 30,
    totalQuestions = 10,
  } = formValues;

  // Safely create date objects with validation
  let startTime: Date;
  let endTime: Date;
  let duration = 0;

  try {
    // Make sure we have valid date and time strings
    if (date && time && date.trim() !== '' && time.trim() !== '') {
      startTime = new Date(`${date}T${time}`);

      // Check if the date is valid
      if (isNaN(startTime.getTime())) {
        // Fallback to current date/time if invalid
        startTime = new Date();
      }

      endTime = new Date(startTime);
      duration =
        timePerQuestion && totalQuestions
          ? Math.round((timePerQuestion * totalQuestions) / 60)
          : 0;
      endTime.setMinutes(endTime.getMinutes() + duration);
    } else {
      // Default to current date/time if missing
      startTime = new Date();
      endTime = new Date();
      endTime.setMinutes(endTime.getMinutes() + 30); // Default 30 min duration
    }
  } catch (error) {
    // Fallback in case of any errors
    console.error('Error parsing date/time:', error);
    startTime = new Date();
    endTime = new Date();
    endTime.setMinutes(endTime.getMinutes() + 30);
  }

  // Calculate difficulty level for progress bar
  const getDifficultyValue = () => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 33;
      case 'medium':
        return 66;
      case 'hard':
        return 100;
      default:
        return 50;
    }
  };

  // Calculate length value for progress bar
  const getLengthValue = () => {
    switch (length?.toLowerCase()) {
      case 'short':
        return 33;
      case 'medium':
        return 66;
      case 'long':
        return 100;
      default:
        return 50;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{title || 'Untitled Battle'}</span>
          <div className="flex gap-2">
            <Badge variant="outline" className="capitalize">
              {difficulty}
            </Badge>
            <Badge variant="outline" className="capitalize">
              {length}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-medium">Description</h4>
          <p className="text-sm text-muted-foreground">
            {description || 'No description provided'}
          </p>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <h4 className="font-medium">Subject & Topic</h4>
            <p className="text-sm text-muted-foreground">
              {subjectName} {topicName && `• ${topicName}`}
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Schedule</h4>
            <p className="text-sm text-muted-foreground">
              Starts:{' '}
              {startTime && !isNaN(startTime.getTime())
                ? format(startTime, 'PPP p')
                : 'Not scheduled yet'}
              <br />
              Ends:{' '}
              {endTime && !isNaN(endTime.getTime())
                ? format(endTime, 'PPP p')
                : 'Not scheduled yet'}
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Battle Settings</h4>
            <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>Max Participants:</span>
              </div>
              <div>{maxParticipants}</div>
              <div className="flex items-center gap-2">
                <Award className="h-4 w-4" />
                <span>Points per Question:</span>
              </div>
              <div>{pointsPerQuestion}</div>
              <div className="flex items-center gap-2">
                <Timer className="h-4 w-4" />
                <span>Time per Question:</span>
              </div>
              <div>{timePerQuestion ?? 30}s</div>
              <div className="flex items-center gap-2">
                <BarChart className="h-4 w-4" />
                <span>Total Questions:</span>
              </div>
              <div>{totalQuestions ?? 10}</div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Total Duration</h4>
            <p className="text-sm text-muted-foreground">
              {Math.round(duration / 60)} minutes
            </p>
          </div>
        </div>

        <div className="mt-6 space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Difficulty Level</h4>
              <span className="text-sm capitalize text-muted-foreground">
                {difficulty}
              </span>
            </div>
            <Progress value={getDifficultyValue()} className="h-2" />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Battle Length</h4>
              <span className="text-sm capitalize text-muted-foreground">
                {length}
              </span>
            </div>
            <Progress value={getLengthValue()} className="h-2" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default BattlePreview;
