import { IFormNavigationProps } from '../../types';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON>, Loader2, Sword } from 'lucide-react';
import { cn } from '@/lib/utils';

// Form Navigation Component
export default function FormNavigation({
  activeStep,
  prevStep,
  nextStep,
  isCurrentStepValid,
  onCancel,
  isSubmitting,
  isValid,
}: IFormNavigationProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="mt-6 flex items-center justify-between"
    >
      <div className="flex space-x-2">
        {activeStep > 1 && (
          <Button
            type="button"
            variant="outline"
            onClick={prevStep}
            className="group transition-all duration-300"
          >
            <motion.span
              initial={{ x: 10 }}
              animate={{ x: 0 }}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
              Back
            </motion.span>
          </Button>
        )}
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
            className="transition-all duration-300 hover:bg-destructive/10 hover:text-destructive"
          >
            Cancel
          </Button>
        )}
      </div>
      <div className="flex space-x-2">
        {activeStep < 3 ? (
          <Button
            type="button"
            onClick={nextStep}
            disabled={!isCurrentStepValid}
            className="group min-w-[120px] transition-all duration-300"
          >
            <motion.span
              initial={{ x: -10 }}
              animate={{ x: 0 }}
              className="flex items-center gap-2"
            >
              Next
              <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
            </motion.span>
          </Button>
        ) : (
          <Button
            type="submit"
            disabled={!isValid || isSubmitting}
            className={cn(
              'min-w-[120px] transition-all duration-300',
              isValid && !isSubmitting && 'animate-pulse',
            )}
          >
            {isSubmitting ? (
              <motion.div
                className="flex items-center gap-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Creating...</span>
              </motion.div>
            ) : (
              <motion.span
                initial={{ scale: 0.9 }}
                animate={{ scale: 1 }}
                className="flex items-center gap-2"
              >
                <span>Create Battle</span>
                <Sword className="h-4 w-4" />
              </motion.span>
            )}
          </Button>
        )}
      </div>
    </motion.div>
  );
}
