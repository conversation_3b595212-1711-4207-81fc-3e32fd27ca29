import { IPreviewModeProps } from '../../types';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Eye, Loader2, Sword } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import BattlePreview from '../BattlePreview';

// Preview Mode Component
export default function PreviewMode({
  formValues,
  currentSubjectName,
  currentTopicName,
  backToEdit,
  handleFinalSubmit,
  isSubmitting,
}: IPreviewModeProps) {
  return (
    <div className="space-y-6">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-xl font-bold">Battle Preview</h3>
        <Badge variant="outline" className="px-3 py-1">
          <Eye className="mr-1 h-4 w-4" />
          Preview Mode
        </Badge>
      </div>
      <BattlePreview
        formValues={formValues}
        subjectName={currentSubjectName}
        topicName={currentTopicName}
      />
      <div className="mt-6 flex justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={backToEdit}
          className="min-w-[120px] transition-all duration-200 hover:bg-muted"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Edit
        </Button>
        <Button
          type="button"
          onClick={handleFinalSubmit}
          disabled={isSubmitting}
          className="hover:bg-primary/90 min-w-[120px] bg-primary text-white transition-all duration-200"
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Creating...</span>
            </div>
          ) : (
            <>
              <Sword className="mr-2 h-4 w-4" />
              Create Battle
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
