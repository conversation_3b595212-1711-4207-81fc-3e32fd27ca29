/**
 * @file BattleProgressTracker.tsx
 * @description Progress tracker component for battle questions
 */
import React from 'react';

import { CheckCircle, Clock, HelpCircle, Trophy } from 'lucide-react';

import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface IBattleProgressTrackerProps {
  currentQuestionIndex: number;
  totalQuestions: number;
  answeredQuestions: number[];
  timeRemaining?: number;
  battleEndTime?: string;
}

export function BattleProgressTracker({
  currentQuestionIndex,
  totalQuestions,
  answeredQuestions,
  timeRemaining,
  battleEndTime,
}: IBattleProgressTrackerProps) {
  // Calculate completion percentage
  const completionPercentage =
    (answeredQuestions.length / totalQuestions) * 100;

  // Calculate estimated time remaining for the whole battle
  const getBattleTimeRemaining = () => {
    if (!battleEndTime) return null;

    const endTime = new Date(battleEndTime).getTime();
    const now = new Date().getTime();
    const difference = endTime - now;

    if (difference <= 0) return '00:00';

    const minutes = Math.floor(difference / (1000 * 60));
    const seconds = Math.floor((difference % (1000 * 60)) / 1000);

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const battleTimeRemaining = getBattleTimeRemaining();

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 text-lg font-medium">
          <Trophy className="h-5 w-5 text-muted-foreground" />
          <span>Battle Progress</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Completion</span>
            <span className="text-sm text-muted-foreground">
              {Math.round(completionPercentage)}%
            </span>
          </div>
          <Progress value={completionPercentage} className="h-2" />
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2 rounded-md border p-3">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <div>
              <div className="text-xs text-muted-foreground">Answered</div>
              <div className="font-medium">
                {answeredQuestions.length} of {totalQuestions}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2 rounded-md border p-3">
            <HelpCircle className="h-4 w-4 text-blue-500" />
            <div>
              <div className="text-xs text-muted-foreground">Current</div>
              <div className="font-medium">
                Question {currentQuestionIndex + 1}
              </div>
            </div>
          </div>

          {timeRemaining && (
            <div className="flex items-center gap-2 rounded-md border p-3">
              <Clock className="h-4 w-4 text-amber-500" />
              <div>
                <div className="text-xs text-muted-foreground">
                  Question Time
                </div>
                <div className="font-medium">{timeRemaining}s</div>
              </div>
            </div>
          )}

          {battleTimeRemaining && (
            <div className="flex items-center gap-2 rounded-md border p-3">
              <Clock className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-xs text-muted-foreground">Battle Time</div>
                <div className="font-medium">{battleTimeRemaining}</div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default BattleProgressTracker;
