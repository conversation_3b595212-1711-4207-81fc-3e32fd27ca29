/**
 * @file BattleRules.tsx
 * @description Component for displaying battle rules
 */
import React, { useEffect, useState } from 'react';

import {
  AlertTriangle,
  Award,
  BookOpen,
  CheckCircle2,
  Clock,
  Shield,
  Users,
  XCircle,
  Loader2,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import {
  IBattleRule,
  IBattleRulesProps,
  IRuleWithIcon,
  RuleCategory,
} from '../types';
import { donts, dos } from '../constants';

export function BattleRules({ battle }: IBattleRulesProps) {
  const { toast } = useToast();
  const [dynamicRules, setDynamicRules] = useState<IBattleRule[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch rules for this battle
  useEffect(() => {
    const fetchRules = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/battle-rules/battle/${battle.id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch battle rules');
        }

        const data = await response.json();
        setDynamicRules(data.data || []);
      } catch (error) {
        console.error('Error fetching battle rules:', error);
        toast({
          title: 'Error',
          description:
            'Failed to load battle rules. Using default rules instead.',
          variant: 'destructive',
        });

        // If we can't fetch rules, use default ones based on battle properties
        setDynamicRules([]);
      } finally {
        setLoading(false);
      }
    };

    if (battle?.id) {
      fetchRules();
    } else {
      setLoading(false);
    }
  }, [battle?.id, toast]);

  // Generate default rules based on battle properties if no rules are fetched
  const defaultRules: IRuleWithIcon[] = [
    {
      id: 'time-limit',
      name: 'Time Limit',
      description: `Each question has a ${battle.time_per_question} second time limit. Answer before time runs out.`,
      category: 'TIMING' as RuleCategory,
      is_active: true,
      is_default: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      icon: <Clock className="h-5 w-5 text-blue-500" />,
    },
    {
      id: 'scoring',
      name: 'Scoring',
      description: `Each question is worth ${battle.points_per_question} points. Faster answers receive bonus points.`,
      category: 'SCORING' as RuleCategory,
      is_active: true,
      is_default: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      icon: <Award className="h-5 w-5 text-yellow-500" />,
    },
    {
      id: 'participation',
      name: 'Participation',
      description: `Up to ${battle.max_participants} participants can join this battle.`,
      category: 'PARTICIPATION' as RuleCategory,
      is_active: true,
      is_default: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      icon: <Users className="h-5 w-5 text-green-500" />,
    },
    {
      id: 'fair-play',
      name: 'Fair Play',
      description:
        'Use of external resources or assistance is not allowed during the battle.',
      category: 'MODERATION' as RuleCategory,
      is_active: true,
      is_default: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      icon: <Shield className="h-5 w-5 text-purple-500" />,
    },
    {
      id: 'answers',
      name: 'Answers',
      description:
        'Once submitted, answers cannot be changed. Choose carefully!',
      category: 'QUESTION' as RuleCategory,
      is_active: true,
      is_default: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      icon: <AlertTriangle className="h-5 w-5 text-orange-500" />,
    },
  ];

  // Get icon based on rule category
  const getRuleIcon = (category: RuleCategory): React.ReactNode => {
    switch (category) {
      case 'SCORING':
        return <Award className="h-5 w-5 text-yellow-500" />;
      case 'TIMING':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'PARTICIPATION':
        return <Users className="h-5 w-5 text-green-500" />;
      case 'QUESTION':
        return <BookOpen className="h-5 w-5 text-orange-500" />;
      case 'MODERATION':
        return <Shield className="h-5 w-5 text-purple-500" />;
      case 'OTHER':
        return <AlertTriangle className="h-5 w-5 text-gray-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />;
    }
  };

  // Display rules from API if available, otherwise use default rules
  const displayRules: IRuleWithIcon[] =
    dynamicRules.length > 0
      ? dynamicRules.map((rule) => ({
          ...rule,
          icon: getRuleIcon(rule.category),
        }))
      : defaultRules;

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">
          <div className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-muted-foreground" />
            <span>Battle Rules & Guidelines</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <ScrollArea className="max-h-[400px] pr-4">
            <div className="space-y-6">
              {/* Main Rules */}
              <div className="space-y-4">
                <h3 className="font-medium">Main Rules</h3>
                {displayRules.map((rule, index) => (
                  <div key={rule.id || index} className="flex gap-3">
                    <div className="mt-0.5 flex-shrink-0">{rule.icon}</div>
                    <div>
                      <h4 className="font-medium">{rule.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {rule.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <Separator />

              {/* Dos and Don'ts */}
              <div className="grid gap-6 md:grid-cols-2">
                {/* Dos */}
                <div className="space-y-3">
                  <h3 className="flex items-center gap-2 font-medium text-green-600 dark:text-green-400">
                    <CheckCircle2 className="h-4 w-4" />
                    <span>Do&apos;s</span>
                  </h3>
                  <ul className="space-y-2">
                    {dos.map((item, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <CheckCircle2 className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Don'ts */}
                <div className="space-y-3">
                  <h3 className="text-red-600 dark:text-red-400 flex items-center gap-2 font-medium">
                    <XCircle className="h-4 w-4" />
                    <span>Don&apos;ts</span>
                  </h3>
                  <ul className="space-y-2">
                    {donts.map((item, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-sm"
                      >
                        <XCircle className="text-red-500 mt-0.5 h-4 w-4 flex-shrink-0" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <Separator />

              {/* Additional Information */}
              <div className="rounded-md bg-muted p-4">
                <h3 className="mb-2 font-medium">Additional Information</h3>
                <p className="text-sm text-muted-foreground">
                  This battle is of{' '}
                  <strong>{battle.difficulty.toLowerCase()}</strong> difficulty
                  and
                  <strong> {battle.length.toLowerCase()}</strong> length. It
                  contains a total of
                  <strong> {battle.total_questions}</strong> questions on the
                  topic of
                  <strong> {battle.topic.title}</strong>. The battle will start
                  at the scheduled time and all participants must join before
                  the start time.
                </p>
              </div>
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}

export default BattleRules;

// Export the BattleRule type for use in other components
export type { IBattleRule, RuleCategory };
