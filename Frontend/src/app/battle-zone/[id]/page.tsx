/**
 * @file page.tsx
 * @description Next.js page for battle-zone/[id] route
 */
'use client';

import { useEffect, useState, useCallback } from 'react';
import { toast } from 'react-toastify';

import { usePara<PERSON>, useRouter } from 'next/navigation';

import { format } from 'date-fns';
import {
  AlertCircle,
  Clock,
  Info,
  Loader2,
  MessageSquare,
  Play,
  Send,
  Swords,
  Trophy,
} from 'lucide-react';

import BattleZoneLayout from '@/components/Battle/BattleZoneLayout';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import useBattleApi from '@/hooks/useBattleApi';
import {
  useBattleChat,
  useBattleLeaderboard,
  useBattleTimer,
  useBattleWebSocket,
} from '@/hooks/useBattleWebSocket';
import { IBattle, IBattleQuestion } from '@/types/battle';

// Import custom components
import BattleInformation from '../Components/BattleInformation';
import BattleProgressTracker from '../Components/BattleProgressTracker';
import BattleRules from '../Components/BattleRules';
import CountdownTimer from '../Components/CountdownTimer';
import ParticipantList from '../Components/ParticipantList';
import QuestionNavigator from '../Components/QuestionNavigator';
import QuestionPreview from '../Components/QuestionPreview';
import QuestionTimer from '../Components/QuestionTimer';

export default function BattleDetailPage() {
  const params = useParams();
  const router = useRouter();
  const battleId = params.id as string;

  const {
    fetchBattle,
    fetchBattleQuestions,
    joinExistingBattle,
    submitBattleAnswer,
    error,
  } = useBattleApi();
  const [battle, setBattle] = useState<IBattle | null>(null);
  const [questions, setQuestions] = useState<IBattleQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [hasJoined, setHasJoined] = useState(false);
  const [customError, setCustomError] = useState<string | null>(null);
  const [answeredQuestions, setAnsweredQuestions] = useState<number[]>([]);
  const [questionTimerExpired, setQuestionTimerExpired] = useState(false);

  // Battle state tracking
  const [battleState, setBattleState] = useState<
    'upcoming' | 'active' | 'completed'
  >('upcoming');
  const [battleDuration, setBattleDuration] = useState<number | null>(null);

  // WebSocket hooks
  const { timeRemaining } = useBattleTimer(battleId);
  const leaderboard = useBattleLeaderboard(battleId);
  const { messages, sendMessage } = useBattleChat(battleId);
  const { sendToBattle } = useBattleWebSocket(
    'battle:state_update',
    battleId,
    () => {},
  );

  // Load battle details
  useEffect(() => {
    const loadBattle = async () => {
      try {
        const response = await fetchBattle(battleId);

        if (response) {
          // Handle the complex API response structure with proper type handling
          let battleData: IBattle | null = null;

          // Case 1: Standard API response format with data structure
          if (response && response.data && typeof response.data === 'object') {
            // Check if response.data is an array or a single object
            if (Array.isArray(response.data)) {
              // If it's an array, take the first item if it exists
              battleData = response.data.length > 0 ? response.data[0] : null;
            } else {
              // If it's a single object, use it directly
              battleData = response.data as IBattle;
            }
          }
          // Case 2: Response has a data property that is an array
          else if (response.data && Array.isArray(response.data)) {
            battleData =
              response.data.length > 0 ? (response.data[0] as IBattle) : null;
          }
          // Case 3: Response has a data property that has a nested data property
          else if (
            response.data &&
            typeof response.data === 'object' &&
            'data' in response.data
          ) {
            const nestedData = (response.data as { data: unknown }).data;

            if (Array.isArray(nestedData)) {
              battleData =
                nestedData.length > 0 ? (nestedData[0] as IBattle) : null;
            } else {
              battleData = nestedData as IBattle;
            }
          }
          // Case 4: Response has a data property that is a single object
          else if (response.data) {
            battleData = response.data as IBattle;
          }
          // Case 5: Response itself is an array
          else if (Array.isArray(response)) {
            battleData = response.length > 0 ? (response[0] as IBattle) : null;
          }
          // Case 6: Response is a single object
          else {
            battleData = response as unknown as IBattle;
          }

          if (battleData) {
            // Create a sanitized version of battle data with fallbacks for all properties
            const sanitizedBattleData = {
              // Keep original data
              ...battleData,
              // Ensure all required properties have fallbacks
              id: battleData.id || '',
              title: battleData.title || 'Untitled Battle',
              description: battleData.description || 'No description available',
              status: battleData.status || 'UPCOMING',
              type: battleData.type || 'SCHEDULED',
              topic_id: battleData.topic_id || '',
              user_id: battleData.user_id || '',
              difficulty: battleData.difficulty || 'medium',
              length: battleData.length || 'medium',

              // Use nullish coalescing for numeric values to handle 0 correctly
              max_participants: battleData.max_participants ?? 10,
              current_participants: battleData.current_participants ?? 0,
              points_per_question: battleData.points_per_question ?? 10,
              time_per_question: battleData.time_per_question ?? 30,
              total_questions: battleData.total_questions ?? 0,

              // Ensure date fields have valid values
              start_time: battleData.start_time || '',
              end_time: battleData.end_time || '',
              created_at: battleData.created_at || new Date().toISOString(),
              updated_at: battleData.updated_at || new Date().toISOString(),

              // Ensure nested objects have fallbacks
              topic: battleData.topic || {
                id: '',
                title: 'Unknown Topic',
                description: '',
              },
              user: battleData.user || {
                id: '',
                username: 'Unknown User',
                avatar_url: null,
              },
              participants: battleData.participants || [],
            };

            // Set the sanitized battle data
            setBattle(sanitizedBattleData);

            // Determine battle state based on status and times
            const now = new Date().getTime();
            const startTime = new Date(
              sanitizedBattleData.start_time,
            ).getTime();
            const endTime = new Date(sanitizedBattleData.end_time).getTime();

            if (now < startTime) {
              setBattleState('upcoming');
            } else if (now >= startTime && now < endTime) {
              setBattleState('active');
              // Calculate remaining battle duration in seconds
              const remainingDuration = Math.floor((endTime - now) / 1000);
              setBattleDuration(remainingDuration);
            } else {
              setBattleState('completed');
            }

            // Check if user has already joined
            const userParticipant = sanitizedBattleData.participants?.find(
              (p: { userId: string }) => p.userId === 'current_user', // Replace with actual user ID
            );
            setHasJoined(!!userParticipant);
          } else {
            setCustomError('Battle not found or data in unexpected format');
          }
        } else {
          setCustomError('Failed to retrieve battle data');
        }
      } catch (err) {
        setCustomError(
          err instanceof Error ? err.message : 'Failed to load battle data',
        );
      }
    };

    loadBattle();
  }, [battleId, fetchBattle]);

  // Load battle questions
  useEffect(() => {
    const loadQuestions = async () => {
      if (hasJoined) {
        try {
          const response = await fetchBattleQuestions(battleId);

          if (response) {
            // Handle different response structures
            let questionsData;

            // Handle the complex API response structure with proper type handling
            if (response) {
              // Case 1: Response has a data property that is an array
              if (response.data && Array.isArray(response.data)) {
                questionsData = response.data as IBattleQuestion[];
              }
              // Case 2: Response has a data property that has a nested data property
              else if (
                response.data &&
                typeof response.data === 'object' &&
                'data' in response.data
              ) {
                const nestedData = (response.data as { data: unknown }).data;

                if (Array.isArray(nestedData)) {
                  questionsData = nestedData as IBattleQuestion[];
                } else {
                  questionsData = [nestedData as IBattleQuestion];
                }
              }
              // Case 3: Response has a data property that is a single object
              else if (response.data) {
                questionsData = [response.data as IBattleQuestion];
              }
              // Case 4: Response itself is an array
              else if (Array.isArray(response)) {
                questionsData = response as IBattleQuestion[];
              }
              // Case 5: Response is a single object
              else {
                // First convert to unknown then to IBattleQuestion to avoid type errors
                questionsData = [response as unknown as IBattleQuestion];
              }
            }

            if (questionsData && questionsData.length > 0) {
              setQuestions(questionsData);
            } else {
              setCustomError('No questions available for this battle');
            }
          } else {
            setCustomError('Failed to retrieve battle questions');
          }
        } catch (err) {
          setCustomError(
            err instanceof Error
              ? err.message
              : 'Failed to load battle questions',
          );
        }
      }
    };

    if (hasJoined) {
      loadQuestions();
    }
  }, [battleId, fetchBattleQuestions, hasJoined]);

  // Handle join battle
  const handleJoinBattle = async () => {
    try {
      const response = await joinExistingBattle(battleId);
      if (response) {
        // If battle is upcoming, redirect to lobby
        if (battle?.status === 'UPCOMING') {
          toast.success('Joining battle lobby...');
          router.push(`/battle-zone/${battleId}/lobby`);
          return;
        }

        setHasJoined(true);
        toast.success('Successfully joined the battle!');

        // Notify other participants via WebSocket
        sendToBattle('battle:join', { battle_id: battleId });
      }
    } catch {
      toast.error('Failed to join battle');
    }
  };

  // Handle navigating between questions
  const handleNavigateQuestion = useCallback(
    (index: number) => {
      if (index >= 0 && index < questions.length) {
        setCurrentQuestionIndex(index);
        setSelectedAnswer(null);
        setQuestionTimerExpired(false);
      }
    },
    [questions.length],
  );

  // Handle question timer expiration
  const handleQuestionTimeUp = useCallback(() => {
    setQuestionTimerExpired(true);
    toast.warning('Time is up for this question!');

    // Automatically move to next question if not the last one
    if (currentQuestionIndex < questions.length - 1) {
      setTimeout(() => {
        handleNavigateQuestion(currentQuestionIndex + 1);
      }, 1500); // Give user a moment to see the time's up message
    }
  }, [currentQuestionIndex, questions.length, handleNavigateQuestion]);

  // Handle submit answer
  const handleSubmitAnswer = async () => {
    if (
      !selectedAnswer ||
      submitting ||
      !questions[currentQuestionIndex] ||
      questionTimerExpired
    )
      return;

    setSubmitting(true);
    try {
      const response = await submitBattleAnswer({
        battle_id: battleId,
        question_id: questions[currentQuestionIndex].id,
        answer: selectedAnswer,
        time_taken:
          questions[currentQuestionIndex].time_limit - (timeRemaining || 0),
      });

      if (response) {
        toast.success('Answer submitted!');

        // Add current question to answered questions
        setAnsweredQuestions((prev) => {
          if (!prev.includes(currentQuestionIndex)) {
            return [...prev, currentQuestionIndex];
          }
          return prev;
        });

        setSelectedAnswer(null);

        // Move to next question if not the last one
        if (currentQuestionIndex < questions.length - 1) {
          handleNavigateQuestion(currentQuestionIndex + 1);
        }
      }
    } catch (_error) {
      toast.error('Failed to submit answer');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle send chat message
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const input = form.elements.namedItem('message') as HTMLInputElement;
    const message = input.value.trim();

    if (message) {
      sendMessage(message);
      input.value = '';
    }
  };

  // Render loading state
  if (!battle) {
    return (
      <BattleZoneLayout>
        <div className="space-y-6">
          <Skeleton className="h-12 w-3/4" />
          <Skeleton className="h-6 w-1/2" />
          <div className="grid gap-6 md:grid-cols-3">
            <Skeleton className="h-[300px]" />
            <Skeleton className="h-[300px]" />
            <Skeleton className="h-[300px]" />
          </div>
        </div>
      </BattleZoneLayout>
    );
  }

  // Check if battle data is complete
  if (!battle.title || !battle.description || !battle.topic || !battle.user) {
    return (
      <BattleZoneLayout>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="mb-4 h-12 w-12 text-destructive" />
          <h2 className="mb-2 text-2xl font-bold">Incomplete Battle Data</h2>
          <p className="mb-6 text-muted-foreground">
            The battle information is incomplete or in an unexpected format.
          </p>
          <div className="space-y-4">
            <Button onClick={() => router.back()}>Go Back</Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Reload Page
            </Button>
          </div>
          <div className="mt-8 rounded-md bg-muted p-4 text-left">
            <p className="mb-2 font-semibold">Debug Information:</p>
            <pre className="overflow-x-auto text-xs">
              {JSON.stringify(battle, null, 2)}
            </pre>
          </div>
        </div>
      </BattleZoneLayout>
    );
  }

  // Render error state - use both the error from the API hook and our custom error
  if (error || customError) {
    const errorMessage = customError || error;
    return (
      <BattleZoneLayout>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="mb-4 h-12 w-12 text-destructive" />
          <h2 className="mb-2 text-2xl font-bold">Error Loading Battle</h2>
          <p className="mb-6 text-muted-foreground">{errorMessage}</p>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </BattleZoneLayout>
    );
  }

  // Format date helper function used in the component
  const formatMessageTime = (dateValue: string | number) => {
    try {
      return format(new Date(dateValue), 'h:mm a');
    } catch (_error) {
      return 'Invalid time';
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'UPCOMING':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'IN_PROGRESS':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'COMPLETED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  // Render current question
  const renderCurrentQuestion = () => {
    // Safety check for battle object
    if (!battle) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="mb-4 h-12 w-12 animate-spin text-primary" />
          <h2 className="mb-2 text-2xl font-bold">Loading Battle</h2>
          <p className="text-muted-foreground">
            Please wait while we load the battle data...
          </p>
        </div>
      );
    }

    if (!hasJoined) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Info className="mb-4 h-12 w-12 text-primary" />
          <h2 className="mb-2 text-2xl font-bold">Join to See Questions</h2>
          <p className="mb-6 text-muted-foreground">
            You need to join this battle to see and answer questions.
          </p>
          <Button
            onClick={handleJoinBattle}
            disabled={battle?.status !== 'UPCOMING'}
          >
            Join Battle
          </Button>
        </div>
      );
    }

    if (!questions || questions.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <AlertCircle className="mb-4 h-12 w-12 text-muted-foreground" />
          <h2 className="mb-2 text-2xl font-bold">No Questions Available</h2>
          <p className="text-muted-foreground">
            Questions will be available when the battle starts.
          </p>
        </div>
      );
    }

    const currentQuestion = questions[currentQuestionIndex];

    return (
      <div className="space-y-6">
        {/* Battle Progress Tracker */}
        <BattleProgressTracker
          currentQuestionIndex={currentQuestionIndex}
          totalQuestions={questions.length}
          answeredQuestions={answeredQuestions}
          timeRemaining={timeRemaining || currentQuestion.time_limit}
          battleEndTime={battle.end_time}
        />

        {/* Question Timer */}
        <div className="flex justify-center">
          <div className="w-1/2">
            <QuestionTimer
              duration={currentQuestion.time_limit}
              onTimeUp={handleQuestionTimeUp}
              paused={questionTimerExpired}
              size="lg"
            />
          </div>
        </div>

        {/* Question Navigator */}
        <QuestionNavigator
          currentIndex={currentQuestionIndex}
          totalQuestions={questions.length}
          answeredQuestions={answeredQuestions}
          onNavigate={handleNavigateQuestion}
        />

        {/* Question Content */}
        <div className="rounded-lg border bg-card p-6">
          <h3 className="mb-4 text-xl font-semibold">
            {currentQuestion.question}
          </h3>

          <div className="space-y-3">
            {currentQuestion.options.map((option, index) => (
              <div
                key={index}
                className={`cursor-pointer rounded-md border p-4 transition-colors ${
                  selectedAnswer === option
                    ? 'bg-primary/10 border-primary'
                    : 'hover:border-primary/50'
                } ${questionTimerExpired ? 'pointer-events-none opacity-70' : ''}`}
                onClick={() =>
                  !questionTimerExpired && setSelectedAnswer(option)
                }
              >
                <div className="flex items-center gap-3">
                  <div
                    className={`flex h-6 w-6 items-center justify-center rounded-full border ${
                      selectedAnswer === option
                        ? 'border-primary bg-primary text-primary-foreground'
                        : ''
                    }`}
                  >
                    {String.fromCharCode(65 + index)}
                  </div>
                  <span>{option}</span>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-center">
            <Button
              onClick={handleSubmitAnswer}
              disabled={!selectedAnswer || submitting || questionTimerExpired}
              className="w-full max-w-xs"
            >
              {submitting
                ? 'Submitting...'
                : questionTimerExpired
                  ? 'Time Expired'
                  : 'Submit Answer'}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Render leaderboard
  const renderLeaderboard = () => {
    if (!leaderboard || leaderboard.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Trophy className="mb-4 h-12 w-12 text-muted-foreground" />
          <h2 className="mb-2 text-xl font-bold">No Participants Yet</h2>
          <p className="text-muted-foreground">
            Leaderboard will be available when participants join and submit
            answers.
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {leaderboard.map((entry, index) => (
          <div
            key={entry.user_id}
            className={`flex items-center justify-between rounded-lg border p-4 ${
              index === 0
                ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
                : ''
            }`}
          >
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
                {entry.rank}
              </div>
              <Avatar className="h-8 w-8">
                <AvatarImage src={undefined} alt={entry.username} />
                <AvatarFallback>
                  {entry.username.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className="font-medium">{entry.username}</span>
            </div>
            <div className="font-bold">{entry.score} pts</div>
          </div>
        ))}
      </div>
    );
  };

  // Render chat
  const renderChat = () => {
    if (!messages || messages.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <MessageSquare className="mb-4 h-12 w-12 text-muted-foreground" />
          <h2 className="mb-2 text-xl font-bold">No Messages Yet</h2>
          <p className="text-muted-foreground">
            Chat will be available when participants join the battle.
          </p>
        </div>
      );
    }

    return (
      <div className="flex h-[400px] flex-col rounded-lg border">
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message, index) => (
              <div key={index} className="flex gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage
                    src={message.avatar_url}
                    alt={message.username}
                  />
                  <AvatarFallback>
                    {message.username.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{message.username}</span>
                    <span className="text-xs text-muted-foreground">
                      {formatMessageTime(message.timestamp)}
                    </span>
                  </div>
                  <p className="text-sm">{message.message}</p>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        <Separator />

        <form onSubmit={handleSendMessage} className="flex p-4">
          <Input
            name="message"
            placeholder="Type a message..."
            className="flex-1"
            disabled={!hasJoined}
          />
          <Button type="submit" size="icon" disabled={!hasJoined}>
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    );
  };

  return (
    <BattleZoneLayout>
      <div className="space-y-8">
        {/* Battle Header */}
        <div className="space-y-4">
          <div className="flex flex-wrap items-center justify-between">
            <div className="flex flex-wrap gap-2">
              <Badge className={getStatusColor(battle?.status || 'UPCOMING')}>
                {battle?.status || 'UPCOMING'}
              </Badge>
              <Badge variant="outline">{battle?.type || 'SCHEDULED'}</Badge>
              <Badge variant="outline">
                {battle?.difficulty?.toUpperCase() || 'MEDIUM'}
              </Badge>
            </div>

            {/* Battle State Indicator */}
            <div className="flex items-center gap-2">
              {battleState === 'upcoming' && (
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300"
                >
                  <Clock className="mr-1 h-3 w-3" /> Upcoming
                </Badge>
              )}
              {battleState === 'active' && (
                <Badge
                  variant="outline"
                  className="bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                >
                  <Play className="mr-1 h-3 w-3" /> In Progress
                </Badge>
              )}
              {battleState === 'completed' && (
                <Badge
                  variant="outline"
                  className="bg-gray-50 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300"
                >
                  <Trophy className="mr-1 h-3 w-3" /> Completed
                </Badge>
              )}
            </div>
          </div>

          <h1 className="text-3xl font-bold">
            {battle?.title || 'Untitled Battle'}
          </h1>
          <p className="text-muted-foreground">
            {battle?.description || 'No description available'}
          </p>

          {/* Battle Countdown Timer */}
          <div className="mt-4">
            {battleState === 'upcoming' && (
              <CountdownTimer
                targetTime={battle.start_time}
                title="Battle starts in"
                showProgress={true}
                size="md"
                onComplete={() => {
                  setBattleState('active');
                  toast.info('Battle has started!');
                  // Calculate battle duration in seconds
                  const endTime = new Date(battle.end_time).getTime();
                  const now = new Date().getTime();
                  const duration = Math.floor((endTime - now) / 1000);
                  setBattleDuration(duration > 0 ? duration : 0);
                }}
              />
            )}

            {battleState === 'active' && battleDuration !== null && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Battle ends in</span>
                  <span className="text-sm text-muted-foreground">
                    {Math.floor(battleDuration / 3600)}h{' '}
                    {Math.floor((battleDuration % 3600) / 60)}m{' '}
                    {battleDuration % 60}s
                  </span>
                </div>
                <Progress
                  value={
                    ((battle.time_per_question * battle.total_questions -
                      battleDuration) /
                      (battle.time_per_question * battle.total_questions)) *
                    100
                  }
                  className="h-2"
                />
              </div>
            )}

            {battleState === 'completed' && (
              <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-800 dark:bg-gray-900/50">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Battle completed</span>
                  <Trophy className="h-5 w-5 text-yellow-500" />
                </div>
                <p className="mt-2 text-sm text-muted-foreground">
                  This battle has ended. Check the leaderboard to see the final
                  results.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Battle Progress */}
        {battle.status === 'IN_PROGRESS' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Battle Progress</span>
              <span className="text-sm text-muted-foreground">
                Question {currentQuestionIndex + 1} of {questions.length}
              </span>
            </div>
            <Progress
              value={(answeredQuestions.length / questions.length) * 100}
            />
          </div>
        )}

        {/* Join Button */}
        {!hasJoined && battle.status === 'UPCOMING' && (
          <Button size="lg" onClick={handleJoinBattle} className="w-full">
            <Swords className="mr-2 h-5 w-5" />
            Join Battle
          </Button>
        )}

        {/* Battle Content */}
        <Tabs defaultValue="questions">
          <TabsList className="w-full">
            <TabsTrigger value="questions" className="flex-1">
              <Play className="mr-2 h-4 w-4" />
              Questions
            </TabsTrigger>
            <TabsTrigger value="leaderboard" className="flex-1">
              <Trophy className="mr-2 h-4 w-4" />
              Leaderboard
            </TabsTrigger>
            <TabsTrigger value="chat" className="flex-1">
              <MessageSquare className="mr-2 h-4 w-4" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="details" className="flex-1">
              <Info className="mr-2 h-4 w-4" />
              Details
            </TabsTrigger>
          </TabsList>

          <TabsContent value="questions" className="mt-6">
            {renderCurrentQuestion()}
          </TabsContent>

          <TabsContent value="leaderboard" className="mt-6">
            {renderLeaderboard()}
          </TabsContent>

          <TabsContent value="chat" className="mt-6">
            {renderChat()}
          </TabsContent>

          <TabsContent value="details" className="mt-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-6">
                {/* Pass battle with proper error handling */}
                <BattleInformation battle={battle} />
                <ParticipantList
                  battleId={battleId}
                  maxParticipants={
                    battle?.max_participants ??
                    battle?.maxParticipants ??
                    battle?.total_questions ??
                    10
                  }
                  showRank={battle?.status !== 'UPCOMING'}
                />
              </div>

              <div className="space-y-6">
                <QuestionPreview
                  questions={questions || []}
                  totalQuestions={battle.total_questions ?? 0}
                  timePerQuestion={battle.time_per_question ?? 30}
                  pointsPerQuestion={battle.points_per_question ?? 10}
                  isPreview={true}
                />
                <BattleRules battle={battle} />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </BattleZoneLayout>
  );
}
