import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { difficulties, lengths } from '@/constants';
import {
  battleFormSchema,
  BattleFormValues,
} from '../Components/battleFormValidation';

/**
 * Custom hook to handle battle form state and validation
 */
export const useBattleForm = () => {
  // Form setup with validation
  const form = useForm<BattleFormValues>({
    resolver: yupResolver(battleFormSchema),
    defaultValues: {
      title: '',
      description: '',
      subjectId: '',
      topicId: '',
      difficulty: difficulties.MEDIUM,
      length: lengths.MEDIUM,
      date: '',
      time: '',
      maxParticipants: 10,
      pointsPerQuestion: 10,
      timePerQuestion: 30,
      totalQuestions: 10,
    },
    mode: 'onChange',
  });

  const {
    watch,
    setValue,
    control,
    handleSubmit,
    formState: { isValid },
  } = form;

  // Create a serializable copy of form values to prevent DataCloneError
  const rawFormValues = watch();
  const formValues = JSON.parse(JSON.stringify(rawFormValues));

  return {
    form,
    formValues,
    control,
    setValue,
    handleSubmit,
    isValid,
  };
};
