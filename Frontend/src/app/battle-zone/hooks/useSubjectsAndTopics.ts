import { useState, useEffect } from 'react';
import { useAxiosGet } from '@/hooks/useAxios';
import { IOption } from '@/components/ui/paginated-select';
import { ISubject, ITopic } from '../types';
import { UseFormSetValue } from 'react-hook-form';
import { BattleFormValues } from '../Components/battleFormValidation';
import { IPaginationMeta } from '@/types';

// Define the structure for paginated results
interface IPaginatedResult {
  data: { id: string; title: string }[];
  meta:
    | IPaginationMeta
    | {
        total: number;
        currentPage: number;
        totalPages: number;
        limit: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
      };
}

// Define a type for the select option that we'll use in our formatted results
interface ISelectOption {
  id: string;
  title: string;
}

/**
 * Custom hook to handle subjects and topics data fetching and state management
 */
export const useSubjectsAndTopics = (
  formValues: BattleFormValues,
  setValue: UseFormSetValue<BattleFormValues>,
  // Use a more generic type for toast to ensure compatibility
  toast: any,
) => {
  const [subjects, setSubjects] = useState<ISubject[]>([]);
  const [topics, setTopics] = useState<ITopic[]>([]);

  // State for paginated select options
  const [selectedSubjectOption, setSelectedSubjectOption] =
    useState<IOption | null>(null);
  const [selectedTopicOption, setSelectedTopicOption] =
    useState<IOption | null>(null);

  // API hooks
  const [getSubjects] = useAxiosGet<{ data: ISubject[] }>('/subjects');
  const [getTopicsBySubjectId] = useAxiosGet<{ topics: ITopic[] }>(
    '/subjects/{{subjectId}}/topics',
  );

  // Get current subject and topic names for preview
  const currentSubjectName =
    subjects.find((s) => s.id === formValues.subjectId)?.title || '';
  const currentTopicName =
    topics.find((t) => t.topic.id === formValues.topicId)?.topic.title || '';

  // Function to fetch subjects with pagination for PaginatedSelect
  const getSubjectsForSelect = async ({
    page,
    limit,
  }: {
    page: number;
    limit: number;
  }): Promise<IPaginatedResult> => {
    try {
      const response = await getSubjects({ params: { page, limit } });
      if (response.data) {
        const subjects = response.data.data || [];

        // Format subjects for PaginatedSelect
        const formattedSubjects: ISelectOption[] = subjects.map((subject) => ({
          id: subject.id,
          title: subject.title,
        }));

        // Use the meta information directly from the backend response if available
        const responseData = response.data;
        const meta = responseData.meta ?? {
          total: subjects.length,
          currentPage: page,
          totalPages: Math.ceil(subjects.length / limit) || 1,
          limit,
          hasNextPage: subjects.length > page * limit,
          hasPreviousPage: page > 1,
        };

        // Return in the exact format expected by PaginatedSelect
        return {
          data: formattedSubjects,
          meta,
        };
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }

    // Return empty result in the expected format
    return {
      data: [],
      meta: {
        total: 0,
        currentPage: page,
        totalPages: 0,
        limit,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };
  };

  // Function to fetch topics with pagination for PaginatedSelect
  const getTopicsForSelect = async ({
    page,
    limit,
  }: {
    page: number;
    limit: number;
  }): Promise<IPaginatedResult> => {
    if (!formValues.subjectId) {
      return {
        data: [],
        meta: {
          total: 0,
          currentPage: page,
          totalPages: 0,
          limit,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };
    }

    try {
      const response = await getTopicsBySubjectId(
        { params: { page, limit } },
        { subjectId: formValues.subjectId },
      );
      if (response.data) {
        const topicsArray = response.data.topics || [];

        // Format topics for PaginatedSelect
        const formattedTopics: ISelectOption[] = topicsArray.map((topic) => ({
          id: topic.topic.id,
          title: topic.topic.title,
        }));

        // Use the meta information directly from the backend response if available
        const responseData = response.data;
        const meta = responseData.meta || {
          total: topicsArray.length,
          currentPage: page,
          totalPages: Math.ceil(topicsArray.length / limit) || 1,
          limit,
          hasNextPage: topicsArray.length > page * limit,
          hasPreviousPage: page > 1,
        };

        // Return in the exact format expected by PaginatedSelect
        return {
          data: formattedTopics,
          meta,
        };
      }
    } catch (error) {
      console.error('Error fetching topics:', error);
    }

    // Return empty result in the expected format
    return {
      data: [],
      meta: {
        total: 0,
        currentPage: page,
        totalPages: 0,
        limit,
        hasNextPage: false,
        hasPreviousPage: false,
      },
    };
  };

  // Fetch subjects on component mount
  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const response = await getSubjects();

        if (response.data) {
          setSubjects(response?.data?.data ?? []);
        }
      } catch (error) {
        console.error('Error fetching subjects:', error);
        toast({
          title: 'Error',
          description: 'Failed to load subjects',
          variant: 'destructive',
        });
      }
    };

    fetchSubjects();
  }, []);

  // Fetch topics when subject changes
  useEffect(() => {
    const fetchTopics = async () => {
      if (!formValues.subjectId) {
        setTopics([]);
        return;
      }

      try {
        const response = await getTopicsBySubjectId(
          {},
          { subjectId: formValues.subjectId },
        );

        if (response?.data) {
          setTopics(response.data?.topics ?? []);
          // Reset topic selection when subject changes
          setValue('topicId', '');
        }
      } catch (error) {
        console.error('Error fetching topics:', error);
        toast({
          title: 'Error',
          description: 'Failed to load topics',
          variant: 'destructive',
        });
        setTopics([]);
      }
    };

    fetchTopics();
  }, [formValues.subjectId]);

  return {
    subjects,
    topics,
    selectedSubjectOption,
    setSelectedSubjectOption,
    selectedTopicOption,
    setSelectedTopicOption,
    getSubjectsForSelect,
    getTopicsForSelect,
    currentSubjectName,
    currentTopicName,
  };
};
