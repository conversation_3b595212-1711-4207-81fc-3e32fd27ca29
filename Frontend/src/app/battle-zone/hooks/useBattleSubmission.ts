import { addHours } from 'date-fns';
import { IBattleCreationRequest } from '@/hooks/useBattleCreation';
import { BattleFormValues } from '../Components/battleFormValidation';

// Define battle types to match backend enum
export enum BattleType {
  INSTANT = 'INSTANT',
  SCHEDULED = 'SCHEDULED',
  TOURNAMENT = 'TOURNAMENT',
  PRACTICE = 'PRACTICE',
}

/**
 * Custom hook to handle battle form submission
 */
export const useBattleSubmission = () => {
  /**
   * Prepares battle data for submission to the API according to backend validation schema
   */
  const prepareBattleData = (
    formValues: BattleFormValues,
  ): IBattleCreationRequest => {
    // Create a fresh serializable copy of form data to prevent DataCloneError
    const data = JSON.parse(JSON.stringify(formValues));

    // Calculate current time and minimum future time (now + 10 minutes)
    const now = new Date();
    // Add a 10-minute buffer to ensure the time is sufficiently in the future for backend validation
    const minimumFutureTime = new Date(now.getTime() + 10 * 60 * 1000);
    // Default start time is 10 minutes from now
    let startDate = new Date(minimumFutureTime);
    // Default end time is 1 hour after start time
    let endDate = addHours(startDate, 1);

    try {
      // Safely parse the date
      let dateObj;
      if (data.date) {
        if (typeof data.date === 'string') {
          // Handle string date format
          const [year, month, day] = data.date.split('-').map(Number);
          if (year && month && day) {
            // JavaScript months are 0-indexed
            dateObj = new Date(year, month - 1, day);
          } else {
            dateObj = new Date(data.date);
          }
        } else {
          // Handle Date object
          dateObj = new Date(data.date);
        }
      }

      // Safely parse the time
      let timeObj;
      if (data.time) {
        if (typeof data.time === 'string') {
          // Handle string time format
          const [hours, minutes] = data.time.split(':').map(Number);
          if (hours !== undefined && minutes !== undefined) {
            timeObj = new Date();
            timeObj.setHours(hours, minutes, 0);
          } else {
            timeObj = new Date(data.time);
          }
        } else {
          // Handle Date object
          timeObj = new Date(data.time);
        }
      }

      // If we have valid date and time, create a combined date
      if (
        dateObj &&
        !isNaN(dateObj.getTime()) &&
        timeObj &&
        !isNaN(timeObj.getTime())
      ) {
        startDate = new Date(dateObj);
        startDate.setHours(timeObj.getHours(), timeObj.getMinutes(), 0);

        // Ensure the date is in the future with a buffer of at least 10 minutes
        const minimumFutureTime = new Date(now.getTime() + 10 * 60 * 1000); // 10 minutes in the future

        if (startDate <= minimumFutureTime) {
          // If the date is not sufficiently in the future, set it to now + 1 hour
          startDate = new Date(now.getTime() + 60 * 60 * 1000);
          console.log(
            'Adjusted start time to be 1 hour in the future:',
            startDate.toISOString(),
          );
        } else {
          console.log(
            'User-selected date is sufficiently in the future:',
            startDate.toISOString(),
          );
        }

        // Calculate end time based on battle length
        let durationHours = 1; // Default 1 hour

        if (data.length === 'short') {
          durationHours = 0.5; // 30 minutes
        } else if (data.length === 'medium') {
          durationHours = 1; // 1 hour
        } else if (data.length === 'long') {
          durationHours = 2; // 2 hours
        }

        // Add duration to start time to get end time
        endDate = addHours(startDate, durationHours);
      }
    } catch (error) {
      console.error('Error formatting date/time:', error);
      // Keep the default values (current time + 1 hour)
    }

    // Final validation to ensure we have valid dates and they are sufficiently in the future
    const minimumRequiredFutureTime = new Date(now.getTime() + 10 * 60 * 1000); // 10 minutes in future

    if (
      isNaN(startDate.getTime()) ||
      isNaN(endDate.getTime()) ||
      startDate <= minimumRequiredFutureTime
    ) {
      console.warn(
        'Invalid or insufficiently future date detected, adjusting to ensure validation passes',
      );
      // Set start time to be 1 hour in the future
      startDate = new Date(now.getTime() + 60 * 60 * 1000);
      // Set end time to be 2 hours in the future
      endDate = new Date(now.getTime() + 2 * 60 * 60 * 1000);

      console.log('Adjusted dates to ensure future validation:', {
        newStartTime: startDate.toISOString(),
        newEndTime: endDate.toISOString(),
        minutesInFuture: (startDate.getTime() - now.getTime()) / (60 * 1000),
      });
    }

    // Ensure title meets minimum length requirement (5 characters)
    const title =
      data.title && data.title.length >= 5
        ? data.title
        : data.title
          ? data.title.padEnd(5, '0') // Pad short titles
          : 'Untitled Battle';

    // Ensure description meets minimum length requirement (10 characters)
    const description =
      data.description && data.description.length >= 10
        ? data.description
        : data.description
          ? data.description.padEnd(10, '.') // Pad short descriptions
          : 'No description provided for this battle. Please add details.';

    // Ensure dates are properly formatted as ISO strings
    const startTimeISO = startDate.toISOString();
    const endTimeISO = endDate.toISOString();

    // Log date information for debugging
    console.log('Date debugging info:', {
      originalDate: data.date,
      originalTime: data.time,
      currentTime: new Date().toISOString(),
      calculatedStartDate: startDate.toISOString(),
      calculatedEndDate: endDate.toISOString(),
      startDateTimestamp: startDate.getTime(),
      nowTimestamp: now.getTime(),
      isStartInFuture: startDate > now,
      minutesInFuture: (startDate.getTime() - now.getTime()) / (60 * 1000),
    });

    // Format the data according to backend schema (snake_case)
    const battleData = {
      title,
      description,
      topic_id: data.topicId, // Convert to snake_case
      difficulty: (data.difficulty || 'medium').toUpperCase(), // Ensure uppercase for backend enum
      length: data.length || 'medium',
      type: BattleType.SCHEDULED, // Default to SCHEDULED type
      max_participants: Math.max(2, data.maxParticipants || 10), // Ensure at least 2 participants
      start_time: startTimeISO,
      end_time: endTimeISO,
      points_per_question: Math.max(1, data.pointsPerQuestion || 10), // Ensure at least 1 point
      time_per_question: Math.max(10, data.timePerQuestion || 60), // Ensure at least 10 seconds
      total_questions: Math.max(1, data.totalQuestions || 10), // Ensure at least 1 question
    };

    console.log('Prepared battle data:', battleData);

    return battleData;
  };

  return {
    prepareBattleData,
  };
};
