import { useState } from 'react';
import { BattleFormValues } from '../Components/battleFormValidation';

/**
 * Custom hook to handle multi-step form navigation
 */
export const useStepNavigation = (formValues: BattleFormValues) => {
  const [activeStep, setActiveStep] = useState(1);

  // Define steps for the stepper
  const stepTitles = ['Basic Information', 'Subject & Topic', 'Schedule'];

  // Handle step navigation
  const nextStep = () => {
    if (activeStep < 3) {
      setActiveStep(activeStep + 1);
    }
  };

  const prevStep = () => {
    if (activeStep > 1) {
      setActiveStep(activeStep - 1);
    }
  };

  // Check if current step is valid
  const isStepValid = () => {
    if (activeStep === 1) {
      return !!formValues.title && !!formValues.description;
    } else if (activeStep === 2) {
      return !!formValues.subjectId && !!formValues.topicId;
    } else if (activeStep === 3) {
      return !!formValues.date && !!formValues.time;
    }
    return true;
  };

  return {
    activeStep,
    setActiveStep,
    nextStep,
    prevStep,
    isStepValid,
    stepTitles,
  };
};
