'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON>, Plus, Pencil, Trash, Star } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

// Define types for battle rules
type RuleCategory =
  | 'SCORING'
  | 'TIMING'
  | 'PARTICIPATION'
  | 'QUESTION'
  | 'MODERATION'
  | 'OTHER';

interface IBattleRule {
  id: string;
  name: string;
  description: string;
  category: RuleCategory;
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

// Form schema for creating/editing rules
const ruleFormSchema = yup.object({
  name: yup
    .string()
    .min(3, 'Name must be at least 3 characters long')
    .max(100, 'Name must not exceed 100 characters')
    .required('Name is required'),
  description: yup
    .string()
    .min(10, 'Description must be at least 10 characters long')
    .max(1000, 'Description must not exceed 1000 characters')
    .required('Description is required'),
  category: yup
    .string()
    .oneOf([
      'SCORING',
      'TIMING',
      'PARTICIPATION',
      'QUESTION',
      'MODERATION',
      'OTHER',
    ])
    .required('Category is required'),
  is_active: yup.boolean().default(true),
  is_default: yup.boolean().default(false),
});

export default function BattleRulesAdmin() {
  const router = useRouter();
  const [rules, setRules] = useState<IBattleRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentRule, setCurrentRule] = useState<IBattleRule | null>(null);
  const [activeTab, setActiveTab] = useState<string>('all');

  // Initialize form
  const form = useForm<yup.InferType<typeof ruleFormSchema>>({
    resolver: yupResolver(ruleFormSchema),
    defaultValues: {
      name: '',
      description: '',
      category: 'OTHER',
      is_active: true,
      is_default: false,
    },
  });

  // Fetch rules on component mount
  useEffect(() => {
    fetchRules();
  }, []);

  // Fetch rules from API
  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/battle-rules');

      if (!response.ok) {
        throw new Error('Failed to fetch battle rules');
      }

      const data = await response.json();
      setRules(data.data || []);
    } catch (error) {
      console.error('Error fetching battle rules:', error);
      toast({
        title: 'Error',
        description: 'Failed to load battle rules. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Open dialog for creating a new rule
  const handleCreateRule = () => {
    form.reset({
      name: '',
      description: '',
      category: 'OTHER',
      is_active: true,
      is_default: false,
    });
    setCurrentRule(null);
    setIsDialogOpen(true);
  };

  // Open dialog for editing an existing rule
  const handleEditRule = (rule: IBattleRule) => {
    form.reset({
      name: rule.name,
      description: rule.description,
      category: rule.category,
      is_active: rule.is_active,
      is_default: rule.is_default,
    });
    setCurrentRule(rule);
    setIsDialogOpen(true);
  };

  // Open dialog for deleting a rule
  const handleDeleteClick = (rule: IBattleRule) => {
    setCurrentRule(rule);
    setIsDeleteDialogOpen(true);
  };

  // Handle form submission for creating/editing rules
  const onSubmit = async (values: yup.InferType<typeof ruleFormSchema>) => {
    try {
      if (currentRule) {
        // Update existing rule
        const response = await fetch(`/api/battle-rules/${currentRule.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values),
        });

        if (!response.ok) {
          throw new Error('Failed to update battle rule');
        }

        toast({
          title: 'Success',
          description: 'Battle rule updated successfully!',
        });
      } else {
        // Create new rule
        const response = await fetch('/api/battle-rules', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(values),
        });

        if (!response.ok) {
          throw new Error('Failed to create battle rule');
        }

        toast({
          title: 'Success',
          description: 'Battle rule created successfully!',
        });
      }

      // Close dialog and refresh rules
      setIsDialogOpen(false);
      fetchRules();
    } catch (error) {
      console.error('Error saving battle rule:', error);
      toast({
        title: 'Error',
        description: 'Failed to save battle rule. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle rule deletion
  const handleDeleteRule = async () => {
    if (!currentRule) return;

    try {
      const response = await fetch(`/api/battle-rules/${currentRule.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete battle rule');
      }

      toast({
        title: 'Success',
        description: 'Battle rule deleted successfully!',
      });

      // Close dialog and refresh rules
      setIsDeleteDialogOpen(false);
      fetchRules();
    } catch (error) {
      console.error('Error deleting battle rule:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete battle rule. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Filter rules based on active tab
  const filteredRules = rules.filter((rule) => {
    if (activeTab === 'all') return true;
    if (activeTab === 'active') return rule.is_active;
    if (activeTab === 'inactive') return !rule.is_active;
    if (activeTab === 'default') return rule.is_default;
    return rule.category === activeTab;
  });

  // Helper function to get category badge color
  const getCategoryColor = (category: RuleCategory) => {
    switch (category) {
      case 'SCORING':
        return 'bg-blue-500';
      case 'TIMING':
        return 'bg-yellow-500';
      case 'PARTICIPATION':
        return 'bg-green-500';
      case 'QUESTION':
        return 'bg-purple-500';
      case 'MODERATION':
        return 'bg-red-500';
      case 'OTHER':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="container mx-auto max-w-6xl py-8">
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">Battle Rules Management</h1>
        </div>
        <Button onClick={handleCreateRule} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create New Rule
        </Button>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Rules</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="inactive">Inactive</TabsTrigger>
          <TabsTrigger value="default">Default</TabsTrigger>
          <TabsTrigger value="SCORING">Scoring</TabsTrigger>
          <TabsTrigger value="TIMING">Timing</TabsTrigger>
          <TabsTrigger value="PARTICIPATION">Participation</TabsTrigger>
          <TabsTrigger value="QUESTION">Question</TabsTrigger>
          <TabsTrigger value="MODERATION">Moderation</TabsTrigger>
          <TabsTrigger value="OTHER">Other</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
            </div>
          ) : filteredRules.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">
                No rules found. Create a new rule to get started.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {filteredRules.map((rule) => (
                <Card key={rule.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {rule.name}
                          {rule.is_default && (
                            <Star className="h-4 w-4 text-yellow-500" />
                          )}
                        </CardTitle>
                        <Badge
                          className={`mt-2 ${getCategoryColor(rule.category)}`}
                        >
                          {rule.category}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        {rule.is_active ? (
                          <Badge
                            variant="outline"
                            className="border-green-200 bg-green-50 text-green-700"
                          >
                            Active
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="bg-red-50 text-red-700 border-red-200"
                          >
                            Inactive
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="mb-4 text-sm text-muted-foreground">
                      {rule.description}
                    </p>
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditRule(rule)}
                        className="flex items-center gap-1"
                      >
                        <Pencil className="h-3 w-3" />
                        Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteClick(rule)}
                        className="flex items-center gap-1"
                      >
                        <Trash className="h-3 w-3" />
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Create/Edit Rule Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {currentRule ? 'Edit Rule' : 'Create New Rule'}
            </DialogTitle>
            <DialogDescription>
              {currentRule
                ? 'Update the details of the battle rule.'
                : 'Create a new rule for battles. Rules define how battles work and are applied to battles.'}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rule Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter rule name" {...field} />
                    </FormControl>
                    <FormDescription>
                      A short, descriptive name for the rule.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a detailed description of the rule"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Explain what this rule does and how it affects battles.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="SCORING">Scoring</SelectItem>
                        <SelectItem value="TIMING">Timing</SelectItem>
                        <SelectItem value="PARTICIPATION">
                          Participation
                        </SelectItem>
                        <SelectItem value="QUESTION">Question</SelectItem>
                        <SelectItem value="MODERATION">Moderation</SelectItem>
                        <SelectItem value="OTHER">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Categorize the rule for better organization.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Active</FormLabel>
                        <FormDescription>
                          Whether this rule is currently active and can be
                          applied to battles.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="is_default"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          Default Rule
                        </FormLabel>
                        <FormDescription>
                          If enabled, this rule will be applied to all battles
                          by default.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <DialogFooter>
                <Button type="submit">
                  {currentRule ? 'Update Rule' : 'Create Rule'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Rule</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this rule? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {currentRule && (
              <div className="space-y-2">
                <p className="font-medium">{currentRule.name}</p>
                <p className="text-sm text-muted-foreground">
                  {currentRule.description}
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteRule}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
