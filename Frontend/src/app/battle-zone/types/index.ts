import { IOption } from '@/components/ui/paginated-select';
import { BattleFormValues } from '../Components/battleFormValidation';
import { Control, UseFormSetValue } from 'react-hook-form';
import { Variants } from 'framer-motion';
import { IPaginationMeta } from '@/types';
import { IBattle } from '@/types/battle';

// Component interfaces
export interface IBasicInformationFormProps {
  control: Control<BattleFormValues>;
  cardVariants: Variants;
}

export interface ISubject {
  id: string;
  title: string;
}

export interface ITopic {
  topic: {
    id: string;
    title: string;
  };
}

export interface IEnhancedCreateBattleFormProps {
  onSuccess?: (battleId: string) => void;
  onCancel?: () => void;
}

export interface IBattleCreationData {
  title: string;
  description: string;
  subjectId: string;
  topicId: string;
  difficulty: 'easy' | 'medium' | 'hard';
  length: 'short' | 'medium' | 'long';
  time: string;
  date: string;
  startTime: string;
  endTime: string;
  totalQuestions: number | null;
  category: string;
  tags: string[];
  maxParticipants: number;
}

export interface ISubjectTopicFormProps {
  control: Control<BattleFormValues>;
  cardVariants: Variants;
  formValues: BattleFormValues;
  selectedSubjectOption: IOption | null;
  setSelectedSubjectOption: (option: IOption | null) => void;
  selectedTopicOption: IOption | null;
  setSelectedTopicOption: (option: IOption | null) => void;
  getSubjectsForSelect: (params: { page: number; limit: number }) => Promise<{
    data: Array<{ id: string; title: string }>;
    meta: IPaginationMeta;
  }>;
  getTopicsForSelect: (params: { page: number; limit: number }) => Promise<{
    data: Array<{ id: string; title: string }>;
    meta: IPaginationMeta;
  }>;
  // Instead of passing the entire form object, only pass the specific methods needed
  setFormValue: (
    name: keyof BattleFormValues,
    value: string | number | null,
  ) => void;
}

export interface IScheduleSettingsFormProps {
  control: Control<BattleFormValues>;
  cardVariants: Variants;
  formValues: BattleFormValues;
  showAdvancedOptions: boolean;
  setShowAdvancedOptions: (show: boolean) => void;
}

export interface IFormNavigationProps {
  activeStep: number;
  prevStep: () => void;
  nextStep: () => void;
  isCurrentStepValid: boolean; // Changed from function to boolean
  onCancel?: () => void;
  isSubmitting: boolean;
  isValid: boolean;
}

export interface IPreviewModeProps {
  formValues: BattleFormValues;
  currentSubjectName: string;
  currentTopicName: string;
  backToEdit: () => void;
  handleFinalSubmit: () => void;
  isSubmitting: boolean;
}

export interface IFormStepIndicatorProps {
  steps: string[];
  currentStep: number;
  onStepClick?: (stepIndex: number) => void;
}

// Define types for battle rules
export type RuleCategory =
  | 'SCORING'
  | 'TIMING'
  | 'PARTICIPATION'
  | 'QUESTION'
  | 'MODERATION'
  | 'OTHER';

export interface IBattleRule {
  id: string;
  name: string;
  description: string;
  category: RuleCategory;
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface IRuleWithIcon extends IBattleRule {
  icon: React.ReactNode;
}

export interface IBattleRulesProps {
  battle: IBattle;
}

export interface IBattleRuleSelectorProps {
  control: Control<any>;
  setValue: UseFormSetValue<any>;
  selectedRuleIds?: string[];
}
