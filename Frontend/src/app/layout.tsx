/**
 * @file layout.tsx
 * @description Next.js page for  route
 */
import { ReactNode, Suspense } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { Inter } from 'next/font/google';

import { Toaster } from 'sonner';

import ClientAchievementManager from '@/components/ClientAchievementManager';
import Loader from '@/components/Loader';
import { ThemeProvider } from '@/components/ThemeProvider';
import { ReduxProvider } from '@/contexts/ReduxContext';
import { WebSocketProvider } from '@/contexts/WebSocketContext';

import App from './App';
import './globals.css';

const inter = Inter({ subsets: ['latin'], fallback: [] });

export const metadata = {
  title: 'Mr. Engineers',
  description: 'Generated by Mr. Engineers Inc.',
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.className} bg-bgColor text-dark`}
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <ToastContainer limit={3} />
          <Toaster position="bottom-right" />
          <WebSocketProvider>
            <ReduxProvider>
              <App>
                <Suspense fallback={<Loader type="SiteLoader" />}>
                  {children}
                </Suspense>
              </App>
              <ClientAchievementManager />
            </ReduxProvider>
          </WebSocketProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
