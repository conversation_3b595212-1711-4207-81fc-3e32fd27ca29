/**
 * @file page.tsx
 * @description Next.js page for support route
 */
'use client';

import { useState } from 'react';

import Link from 'next/link';

import { motion } from 'framer-motion';

import { companyInfo } from '@/constants';

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

/**
 * @file page.tsx
 * @description Next.js page for support route
 */

export default function SupportPage() {
  const [category, setCategory] = useState('general');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitted(true);
    // Reset form
    setCategory('general');
    setName('');
    setEmail('');
    setMessage('');
  };

  const supportCategories = [
    { id: 'general', label: 'General Inquiry' },
    { id: 'technical', label: 'Technical Support' },
    { id: 'billing', label: 'Billing Questions' },
    { id: 'feedback', label: 'Feedback & Suggestions' },
  ];

  const faqs = [
    {
      question: 'How do I reset my password?',
      answer:
        'You can reset your password by clicking on the "Forgot Password" link on the login page. You will receive an email with instructions to reset your password.',
    },
    {
      question: 'How can I track my progress?',
      answer:
        'You can track your progress through your dashboard where all your activities, completed challenges, and achievements are displayed.',
    },
    {
      question: 'Is there a mobile app available?',
      answer:
        'Currently, we offer a responsive web application that works well on mobile devices. A dedicated mobile app is in our development roadmap.',
    },
    {
      question: 'How do I join a battle?',
      answer:
        'You can join battles from the Battle Zone section. Browse available battles and click "Join" on any open battle that interests you.',
    },
    {
      question: 'Can I create my own roadmap?',
      answer:
        'Yes, you can create custom roadmaps tailored to your learning goals from the Career Roadmap section after logging in.',
    },
  ];

  return (
    <main className="bg-background py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <motion.div
          className="mb-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">
            Support Center
          </h1>
          <p className="mx-auto max-w-3xl text-lg text-muted-foreground">
            We&apos;re here to help you with any questions or issues you might
            have. Browse our FAQs or reach out to our support team.
          </p>
        </motion.div>

        {/* FAQ Section */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <h2 className="mb-8 text-center text-3xl font-bold text-foreground">
            Frequently Asked Questions
          </h2>
          <div className="mx-auto max-w-3xl divide-y divide-border rounded-xl border border-border bg-card">
            {faqs.map((faq, index) => (
              <details
                key={index}
                className="group p-6 [&_summary::-webkit-details-marker]:hidden"
              >
                <summary className="flex cursor-pointer items-center justify-between gap-1.5 text-lg font-medium text-foreground">
                  <h3>{faq.question}</h3>
                  <svg
                    className="h-5 w-5 shrink-0 transition duration-300 group-open:-rotate-180"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </summary>
                <p className="mt-4 text-muted-foreground">{faq.answer}</p>
              </details>
            ))}
          </div>
        </motion.section>

        {/* Contact Form Section */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="mx-auto max-w-3xl rounded-xl border border-border bg-card p-8 shadow-sm">
            <h2 className="mb-6 text-center text-3xl font-bold text-foreground">
              Contact Support
            </h2>
            {submitted ? (
              <motion.div
                className="rounded-lg bg-green-50 p-6 text-center dark:bg-green-900/20"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
              >
                <h3 className="mb-2 text-xl font-bold text-green-800 dark:text-green-300">
                  Thank You!
                </h3>
                <p className="text-green-700 dark:text-green-400">
                  Your message has been submitted. Our support team will get
                  back to you shortly.
                </p>
                <button
                  onClick={() => setSubmitted(false)}
                  className="mt-4 rounded-full bg-green-600 px-4 py-2 text-white hover:bg-green-700"
                >
                  Send Another Message
                </button>
              </motion.div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="mb-6">
                  <label
                    htmlFor="category"
                    className="mb-2 block text-sm font-medium text-foreground"
                  >
                    Category
                  </label>
                  <select
                    id="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    required
                  >
                    {supportCategories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="mb-6 grid gap-6 md:grid-cols-2">
                  <div>
                    <label
                      htmlFor="name"
                      className="mb-2 block text-sm font-medium text-foreground"
                    >
                      Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      required
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="email"
                      className="mb-2 block text-sm font-medium text-foreground"
                    >
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      required
                    />
                  </div>
                </div>
                <div className="mb-6">
                  <label
                    htmlFor="message"
                    className="mb-2 block text-sm font-medium text-foreground"
                  >
                    Message
                  </label>
                  <textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    rows={5}
                    required
                  ></textarea>
                </div>
                <div className="text-center">
                  <button
                    type="submit"
                    className="rounded-full bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-primary2"
                  >
                    Submit Request
                  </button>
                </div>
              </form>
            )}
          </div>
        </motion.section>

        {/* Additional Support Options */}
        <motion.section
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <h2 className="mb-8 text-center text-3xl font-bold text-foreground">
            Other Ways to Get Help
          </h2>
          <div className="grid gap-8 md:grid-cols-3">
            <div className="rounded-xl border border-border bg-card p-6 text-center shadow-sm transition-all hover:shadow-md">
              <div className="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full text-primary">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  className="h-8 w-8"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-bold text-foreground">
                Email Support
              </h3>
              <p className="mb-4 text-muted-foreground">
                Send us an email and we&apos;ll get back to you within 24 hours.
              </p>
              <a
                href={`mailto:support@${companyInfo.name.toLowerCase()}.com`}
                className="text-primary hover:underline"
              >
                support@{companyInfo.name.toLowerCase()}.com
              </a>
            </div>
            <div className="rounded-xl border border-border bg-card p-6 text-center shadow-sm transition-all hover:shadow-md">
              <div className="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full text-primary">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  className="h-8 w-8"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-bold text-foreground">
                Community Forum
              </h3>
              <p className="mb-4 text-muted-foreground">
                Join our community forum to get help from other users and our
                team.
              </p>
              <a href="/community" className="text-primary hover:underline">
                Visit Community Forum
              </a>
            </div>
            <div className="rounded-xl border border-border bg-card p-6 text-center shadow-sm transition-all hover:shadow-md">
              <div className="bg-primary/10 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full text-primary">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  className="h-8 w-8"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-bold text-foreground">
                Knowledge Base
              </h3>
              <p className="mb-4 text-muted-foreground">
                Browse our extensive documentation and tutorials for self-help.
              </p>
              <Link href="/resources" className="text-primary hover:underline">
                Explore Knowledge Base
              </Link>
            </div>
          </div>
        </motion.section>
      </div>
    </main>
  );
}
