import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Plus } from 'lucide-react';
import Link from 'next/link';

export default function EmptyState({
  activeTab,
  searchTerm,
}: {
  activeTab: string;
  searchTerm: string;
}) {
  return (
    <Card className="mt-6 w-full text-center">
      <CardContent className="pb-10 pt-10">
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="rounded-full bg-gray-100 p-6 dark:bg-gray-800">
            <svg
              className="h-10 w-10 text-gray-500 dark:text-gray-400"
              fill="none"
              height="24"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M12 20h9" />
              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium">No articles found</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {activeTab !== 'all'
              ? `You don't have any ${activeTab} articles yet.`
              : searchTerm
                ? `No articles matching "${searchTerm}"`
                : "You haven't created any articles yet."}
          </p>
          <Button asChild className="mt-4">
            <Link href="/create-article">
              <Plus className="mr-2 h-4 w-4" /> Create New Article
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
