'use client';

import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import { useAxiosGet } from '@/hooks/useAxios';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Search, Edit, MessageSquare, Plus } from 'lucide-react';
import ArticleEmptyState from './components/EmptyState';
import Link from 'next/link';

interface IArticle {
  id: string;
  title: string;
  status: string;
  createdAt?: string;
  updatedAt?: string;
}

export default function MyArticles() {
  const [articles, setArticles] = useState<IArticle[]>([]);
  const [filteredArticles, setFilteredArticles] = useState<IArticle[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const router = useRouter();

  const [getArticles] = useAxiosGet<{
    success?: boolean;
    articles: IArticle[];
    message?: string;
  }>('/articles/my-articles');

  useEffect(() => {
    const fetchMyArticles = async () => {
      setIsLoading(true);
      try {
        const response = await getArticles();
        if (response.data?.success) {
          setArticles(response.data.articles);
          setFilteredArticles(response.data.articles);
        } else {
          toast.error('Failed to load articles.');
        }
      } catch (error) {
        console.error('Error fetching articles:', error);
        toast.error('Error fetching articles.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMyArticles();
  }, []);

  useEffect(() => {
    // Filter articles based on search term and active tab
    const filtered = articles.filter((article) => {
      const matchesSearch = article.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesTab =
        activeTab === 'all' ||
        article.status.toLowerCase() === activeTab.toLowerCase();
      return matchesSearch && matchesTab;
    });

    setFilteredArticles(filtered);
  }, [searchTerm, activeTab, articles]);

  const handleEdit = (id: string) => {
    router.push(`/edit-article/${id}`);
  };

  const handleViewComments = (id: string) => {
    router.push(`/viewComments/${id}`);
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'published':
        return 'bg-green-500';
      case 'draft':
        return 'bg-yellow-500';
      case 'pending':
        return 'bg-blue-500';
      case 'rejected':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const renderEmptyState = () => {
    return <ArticleEmptyState activeTab={activeTab} searchTerm={searchTerm} />;
  };

  return (
    <div className="container mx-auto space-y-6 p-6">
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <h1 className="text-3xl font-bold">My Articles</h1>
        <Button asChild variant="default">
          <Link href="/create-article">
            <Plus className="mr-2 h-4 w-4" /> Create New Article
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
              <Input
                type="search"
                placeholder="Search articles..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Tabs
              defaultValue="all"
              className="w-full sm:w-auto"
              onValueChange={setActiveTab}
            >
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="published">Published</TabsTrigger>
                <TabsTrigger value="draft">Drafts</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>

        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-10">
              <div className="h-10 w-10 animate-spin rounded-full border-b-2 border-primary"></div>
            </div>
          ) : filteredArticles.length === 0 ? (
            renderEmptyState()
          ) : (
            <div className="overflow-x-auto rounded-md border">
              <table className="w-full table-auto">
                <thead>
                  <tr className="border-b bg-muted/50">
                    <th className="px-4 py-3 text-left font-medium">Title</th>
                    <th className="px-4 py-3 text-left font-medium">Status</th>
                    <th className="px-4 py-3 text-right font-medium">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredArticles.map((article) => (
                    <tr
                      key={article.id}
                      className="border-b transition-colors hover:bg-muted/50"
                    >
                      <td className="px-4 py-3">
                        <div className="font-medium">{article.title}</div>
                        {article.updatedAt && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Updated{' '}
                            {new Date(article.updatedAt).toLocaleDateString()}
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-3">
                        <Badge
                          className={`${getStatusBadgeColor(article.status)}`}
                        >
                          {article.status}
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(article.id)}
                          >
                            <Edit className="mr-1 h-4 w-4" /> Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewComments(article.id)}
                          >
                            <MessageSquare className="mr-1 h-4 w-4" /> Comments
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
