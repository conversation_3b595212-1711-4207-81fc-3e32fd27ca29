import { createClient } from '@/utils/supabase/server';
import { NextResponse, type NextRequest } from 'next/server';

export async function GET(request: NextRequest): Promise<NextResponse> {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const next = requestUrl.searchParams.get('next') || '/';

  if (!code) {
    return NextResponse.redirect(
      new URL(
        `/auth/login?error=${encodeURIComponent('No authentication code provided')}`,
        requestUrl.origin
      )
    );
  }

  try {
    const supabase = await createClient();
    
    // Exchange the code for a session
    const { error } = await supabase.auth.exchangeCodeForSession(code);
    
    if (error) {
      console.error('Error exchanging code for session:', error);
      return NextResponse.redirect(
        new URL(
          `/auth/login?error=${encodeURIComponent(error.message)}`,
          requestUrl.origin
        )
      );
    }

    // Get the user's session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session?.user) {
      return NextResponse.redirect(
        new URL(
          '/auth/login?error=no-session',
          requestUrl.origin
        )
      );
    }

    // Check if user needs to complete their profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_complete')
      .eq('id', session.user.id)
      .single();

    // If there's an error fetching profile or profile is not complete
    if (profileError || !profile?.is_complete) {
      return NextResponse.redirect(
        new URL('/auth/complete-profile', requestUrl.origin)
      );
    }

    // Successful authentication, redirect to the intended page or home
    return NextResponse.redirect(new URL(next, requestUrl.origin));
    
  } catch (error) {
    console.error('Error in OAuth callback:', error);
    return NextResponse.redirect(
      new URL(
        `/auth/login?error=${encodeURIComponent('authentication-failed')}`,
        requestUrl.origin
      )
    );
  }
}
