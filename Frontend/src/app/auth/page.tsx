/**
 * @file page.tsx
 * @description Next.js page for auth route
 */
'use client';

import { redirect } from 'next/navigation';

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth route
 */

export default function AuthPage() {
  redirect('/auth/login');
}
