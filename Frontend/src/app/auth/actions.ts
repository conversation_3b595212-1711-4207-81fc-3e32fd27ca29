'use server';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { createClient } from '@/utils/supabase/server';

export async function login(formData: FormData) {
  try {
    const supabase = await createClient();

    const data = {
      email: formData.get('email') as string,
      password: formData.get('password') as string,
    };

    // Validate input
    if (!data.email || !data.password) {
      return { error: 'Email and password are required' };
    }

    // Attempt to sign in
    const { error, data: authData } =
      await supabase.auth.signInWithPassword(data);

    if (error) {
      // Return error information instead of redirecting
      return { error: error.message };
    }

    // Ensure we have a session
    if (!authData?.session) {
      return { error: 'Failed to create session. Please try again.' };
    }

    // Success - update the page and return success
    // In Next.js 15 with Supabase, we should NOT use redirect() in server actions
    // for authentication flows as it causes the NEXT_REDIRECT error
    revalidatePath('/', 'layout');
    return { success: true };
  } catch (error) {
    console.error('Login error:', error);
    return {
      error: 'Network error. Please check your connection and try again.',
    };
  }
}

export async function signup(formData: FormData) {
  try {
    const supabase = await createClient();

    const data = {
      email: formData.get('email') as string,
      password: formData.get('password') as string,
      metadata: {
        full_name: formData.get('name'),
      },
    };

    // Validate input
    if (!data.email || !data.password) {
      return;
    }

    // Attempt to sign up
    const { error, data: authData } = await supabase.auth.signUp(data);

    if (error) {
      return;
    }

    // Check if email confirmation is required
    if (authData?.user?.identities?.length === 0) {
      return;
    }

    revalidatePath('/', 'layout');
    redirect('/dashboard');
  } catch (error) {
    console.error('Signup error:', error);
    return;
  }
}
