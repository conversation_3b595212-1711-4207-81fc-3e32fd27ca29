/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */
'use client';

// import Link from 'next/link';
import { motion } from 'framer-motion';

import LoginForm from '../components/LoginForm';

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

// import OAuthProviders from '../components/OAuthProviders';

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/login route
 */

export default function LoginPage() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="mx-auto w-full max-w-md space-y-6"
    >
      <h1 className="text-center text-3xl font-bold">Welcome Back!</h1>
      <LoginForm />

      {/* <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div> */}

      {/* <OAuthProviders /> */}

      {/* <div className="text-center text-sm text-muted-foreground">
        Don&apos;t have an account?{' '}
        <Link href="/auth/register" className="text-primary hover:underline">
          Sign up
        </Link>
      </div> */}
    </motion.div>
  );
}
