/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */
'use client';

import type React from 'react';

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

/**
 * @file layout.tsx
 * @description Next.js page for auth route
 */

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="w-full max-w-md overflow-hidden rounded-lg bg-lightSecondary shadow-2xl">
        <div className="p-8">{children}</div>
      </div>
    </div>
  );
}
