/**
 * @file new-verify-email.tsx
 * @description Enhanced email verification page with better error handling and user feedback
 */
'client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loader2, CheckCircle, XCircle, MailCheck } from 'lucide-react';
import { toast } from 'react-toastify';

import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

type VerificationStatus = 
  | 'idle' 
  | 'verifying' 
  | 'success' 
  | 'error' 
  | 'expired' 
  | 'already_verified';

export default function VerifyEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tokenHash = searchParams.get('token_hash');
  const type = searchParams.get('type');
  const next = searchParams.get('next') || '/';
  const email = searchParams.get('email') || '';
  
  const [status, setStatus] = useState<VerificationStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [resendCountdown, setResendCountdown] = useState(0);

  // Handle email verification
  useEffect(() => {
    const verifyEmail = async () => {
      if (!tokenHash || type !== 'signup') {
        setStatus('error');
        setError('Invalid verification link');
        return;
      }

      setStatus('verifying');
      
      try {
        const supabase = createClient();
        const { error, data } = await supabase.auth.verifyOtp({
          token_hash: tokenHash,
          type: 'signup',
        });

        if (error) {
          if (error.message.includes('already verified')) {
            setStatus('already_verified');
          } else if (error.message.includes('expired')) {
            setStatus('expired');
          } else {
            throw error;
          }
          return;
        }

        if (data?.session) {
          setStatus('success');
          // Redirect after a short delay to show success message
          setTimeout(() => {
            router.push(next);
          }, 2000);
        }
      } catch (error) {
        console.error('Email verification error:', error);
        setStatus('error');
        setError('Failed to verify email. Please try again.');
      }
    };

    verifyEmail();
  }, [tokenHash, type, next, router]);

  // Handle resend verification email
  const handleResendVerification = async () => {
    if (resendCountdown > 0) return;
    
    try {
      setStatus('verifying');
      const supabase = createClient();
      
      // First, check if user exists and is not verified
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error(userError?.message || 'User not found');
      }
      
      if (user.email_confirmed_at) {
        setStatus('already_verified');
        return;
      }
      
      // Resend verification email
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: user.email || email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/verify-email`,
        },
      });
      
      if (error) throw error;
      
      // Start resend countdown (60 seconds)
      setResendCountdown(60);
      const timer = setInterval(() => {
        setResendCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      toast.success('Verification email resent successfully!');
      setStatus('success');
    } catch (error) {
      console.error('Failed to resend verification:', error);
      setStatus('error');
      setError('Failed to resend verification email. Please try again.');
    }
  };

  // Render different states
  const renderContent = () => {
    switch (status) {
      case 'verifying':
        return (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
            <h2 className="text-2xl font-bold">Verifying your email</h2>
            <p className="text-muted-foreground text-center">
              Please wait while we verify your email address...
            </p>
          </div>
        );
        
      case 'success':
        return (
          <div className="flex flex-col items-center space-y-4">
            <CheckCircle className="h-12 w-12 text-green-500" />
            <h2 className="text-2xl font-bold">Email Verified!</h2>
            <p className="text-muted-foreground text-center">
              Your email has been successfully verified. Redirecting you now...
            </p>
            <Button onClick={() => router.push(next)} className="mt-4">
              Go to Dashboard
            </Button>
          </div>
        );
        
      case 'already_verified':
        return (
          <div className="flex flex-col items-center space-y-4">
            <MailCheck className="h-12 w-12 text-blue-500" />
            <h2 className="text-2xl font-bold">Email Already Verified</h2>
            <p className="text-muted-foreground text-center">
              Your email has already been verified. You can now sign in to your account.
            </p>
            <Button onClick={() => router.push('/auth/login')} className="mt-4">
              Sign In
            </Button>
          </div>
        );
        
      case 'expired':
        return (
          <div className="flex flex-col items-center space-y-4">
            <XCircle className="h-12 w-12 text-yellow-500" />
            <h2 className="text-2xl font-bold">Verification Link Expired</h2>
            <p className="text-muted-foreground text-center">
              The verification link has expired. Please request a new one.
            </p>
            <Button 
              onClick={handleResendVerification} 
              disabled={resendCountdown > 0}
              className="mt-4"
            >
              {resendCountdown > 0 
                ? `Resend in ${resendCountdown}s` 
                : 'Resend Verification Email'}
            </Button>
          </div>
        );
        
      case 'error':
        return (
          <div className="flex flex-col items-center space-y-4">
            <XCircle className="h-12 w-12 text-red-500" />
            <h2 className="text-2xl font-bold">Verification Failed</h2>
            <p className="text-red-500 text-center">
              {error || 'An error occurred during verification.'}
            </p>
            <div className="flex gap-2 mt-4">
              <Button variant="outline" onClick={() => window.location.reload()}>
                Try Again
              </Button>
              <Button onClick={() => router.push('/auth/login')}>
                Go to Login
              </Button>
            </div>
          </div>
        );
        
      default:
        return (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
            <h2 className="text-2xl font-bold">Loading...</h2>
          </div>
        );
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">
            {status === 'success' 
              ? 'Email Verified!'
              : status === 'error'
              ? 'Verification Failed'
              : 'Verifying Email'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {renderContent()}
            
            {status !== 'success' && (
              <div className="mt-6 text-center text-sm text-muted-foreground">
                <p>
                  Need help?{' '}
                  <Link 
                    href="/support" 
                    className="font-medium text-blue-600 hover:underline"
                  >
                    Contact Support
                  </Link>
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
