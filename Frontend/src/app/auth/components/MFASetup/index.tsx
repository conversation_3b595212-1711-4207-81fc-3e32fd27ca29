'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { toast } from 'react-toastify';
import {
  Loader2,
  ShieldCheck,
  Smartphone,
  CheckCircle,
  XCircle,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

type MFASetupStep = 'start' | 'verify' | 'complete';

export default function MFASetup() {
  const router = useRouter();
  const supabase = createClient();
  const [step, setStep] = useState<MFASetupStep>('start');
  const [loading, setLoading] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [qrCode, setQrCode] = useState('');
  const [secret, setSecret] = useState('');
  const [error, setError] = useState('');

  // Start MFA setup
  const startMfaSetup = async () => {
    setLoading(true);
    setError('');

    try {
      const { data, error: mfaError } = await supabase.auth.mfa.enroll({
        factorType: 'totp', // Time-based One-Time Password
        issuer: 'MrEngineer',
      });

      if (mfaError) throw mfaError;

      setQrCode(data.totp.qr_code);
      setSecret(data.totp.secret);
      setStep('verify');
    } catch (error) {
      console.error('Error starting MFA setup:', error);
      setError('Failed to start MFA setup. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Verify MFA setup
  const verifyMfaSetup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!verificationCode.trim()) {
      setError('Please enter the verification code');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const { error: verifyError } = await supabase.auth.mfa.challengeAndVerify(
        {
          factorId: secret,
          code: verificationCode,
        },
      );

      if (verifyError) throw verifyError;

      // MFA verified successfully
      setStep('complete');

      // Update user profile to indicate MFA is enabled
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('profiles')
          .update({ mfa_enabled: true })
          .eq('id', user.id);
      }

      toast.success('MFA has been successfully enabled for your account!');
    } catch (error) {
      console.error('Error verifying MFA setup:', error);
      setError('Invalid verification code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle completion
  const handleComplete = () => {
    router.push('/account/security');
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="mb-4 flex items-center justify-center">
            <ShieldCheck className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-center text-2xl font-bold">
            {step === 'start' && 'Enable Two-Factor Authentication'}
            {step === 'verify' && 'Verify Setup'}
            {step === 'complete' && 'Setup Complete'}
          </CardTitle>
          <CardDescription className="text-center">
            {step === 'start' &&
              'Add an extra layer of security to your account'}
            {step === 'verify' &&
              'Scan the QR code with your authenticator app'}
            {step === 'complete' && 'Two-factor authentication is now enabled'}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {step === 'start' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">How it works</h3>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                    <span>
                      Download an authenticator app like Google Authenticator or
                      Authy
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                    <span>Scan the QR code or enter the setup key</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                    <span>Enter the verification code from the app</span>
                  </li>
                </ul>
              </div>

              {error && (
                <div className="bg-red-50 text-red-700 flex items-start gap-2 rounded-md p-3 text-sm">
                  <XCircle className="mt-0.5 h-5 w-5 flex-shrink-0" />
                  <span>{error}</span>
                </div>
              )}
            </div>
          )}

          {step === 'verify' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex flex-col items-center">
                  {qrCode ? (
                    <div
                      className="rounded-lg border bg-white p-4"
                      dangerouslySetInnerHTML={{ __html: qrCode }}
                    />
                  ) : (
                    <div className="flex h-48 w-48 items-center justify-center rounded-lg bg-gray-100">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  )}

                  <div className="mt-4 text-center">
                    <p className="text-sm text-muted-foreground">
                      Or enter this code manually:
                    </p>
                    <p className="mt-1 rounded-md bg-gray-100 px-3 py-1.5 font-mono text-lg font-bold">
                      {secret || 'Loading...'}
                    </p>
                  </div>
                </div>

                <form onSubmit={verifyMfaSetup} className="space-y-4">
                  <div>
                    <Label htmlFor="verificationCode" className="mb-2 block">
                      Verification Code
                    </Label>
                    <Input
                      id="verificationCode"
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      placeholder="Enter 6-digit code"
                      value={verificationCode}
                      onChange={(e) =>
                        setVerificationCode(
                          e.target.value.replace(/[^0-9]/g, ''),
                        )
                      }
                      className="text-center text-lg tracking-widest"
                      maxLength={6}
                      autoComplete="one-time-code"
                      required
                    />
                  </div>

                  {error && (
                    <div className="bg-red-50 text-red-700 flex items-start gap-2 rounded-md p-3 text-sm">
                      <XCircle className="mt-0.5 h-5 w-5 flex-shrink-0" />
                      <span>{error}</span>
                    </div>
                  )}

                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Verifying...
                      </>
                    ) : (
                      'Verify and Enable'
                    )}
                  </Button>
                </form>
              </div>
            </div>
          )}

          {step === 'complete' && (
            <div className="space-y-6 text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-medium">
                  Two-factor authentication enabled
                </h3>
                <p className="text-sm text-muted-foreground">
                  Your account is now protected with an extra layer of security.
                </p>
              </div>

              <div className="space-y-2 rounded-md bg-blue-50 p-4 text-left">
                <h4 className="flex items-center gap-2 text-sm font-medium">
                  <Smartphone className="h-4 w-4" />
                  Save your backup codes
                </h4>
                <p className="text-sm text-muted-foreground">
                  Make sure to save your backup codes in a safe place. You can
                  use them to access your account if you lose access to your
                  authenticator app.
                </p>
                <Button variant="outline" size="sm" className="mt-2">
                  Download Backup Codes
                </Button>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex flex-col space-y-2">
          {step === 'start' && (
            <Button
              onClick={startMfaSetup}
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Starting setup...
                </>
              ) : (
                'Set up authenticator app'
              )}
            </Button>
          )}

          {step === 'complete' && (
            <Button onClick={handleComplete} className="w-full">
              Back to Security Settings
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            className="mt-2"
            onClick={() => router.back()}
          >
            Go back
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
