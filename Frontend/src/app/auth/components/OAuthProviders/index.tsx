/**
 * @file index.tsx
 * @description React component for OAuthProviders
 * Handles social login with Google, GitHub, and other providers
 */
'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { FaGithub, FaGoogle, FaMicrosoft } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { createClient } from '@/utils/supabase/client';

type Provider = 'google' | 'github' | 'azure';

interface IOAuthProvider {
  id: Provider;
  name: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  textColor: string;
  hoverBgColor: string;
}

export default function OAuthProviders() {
  const searchParams = useSearchParams();
  const supabase = createClient();
  const [loadingProvider, setLoadingProvider] = useState<Provider | null>(null);
  const redirectTo = searchParams.get('redirectTo') || '/dashboard';

  const providers: IOAuthProvider[] = [
    {
      id: 'google',
      name: 'Google',
      icon: <FaGoogle className="h-4 w-4" />,
      color: 'white',
      bgColor: 'bg-white',
      textColor: 'text-gray-800',
      hoverBgColor: 'hover:bg-gray-50',
    },
    {
      id: 'github',
      name: 'GitHub',
      icon: <FaGithub className="h-4 w-4" />,
      color: 'white',
      bgColor: 'bg-gray-800',
      textColor: 'text-white',
      hoverBgColor: 'hover:bg-gray-700',
    },
    {
      id: 'azure',
      name: 'Microsoft',
      icon: <FaMicrosoft className="h-4 w-4" />,
      color: 'white',
      bgColor: 'bg-blue-600',
      textColor: 'text-white',
      hoverBgColor: 'hover:bg-blue-700',
    },
  ];

  const handleOAuth = async (provider: Provider) => {
    setLoadingProvider(provider);
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback?next=${encodeURIComponent(redirectTo)}`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (error) throw error;
      
      // Show success message (in case of popup flow)
      toast.success(`Signing in with ${provider}...`);
    } catch (error: unknown) {
      const authError = error as { message?: string; error_description?: string };
      console.error('OAuth error:', error);
      
      // More specific error messages based on error type
      if (authError?.message?.includes('popup_closed_by_user')) {
        toast.info('Sign in was cancelled');
      } else if (authError?.error_description) {
        toast.error(`Error: ${authError.error_description}`);
      } else {
        toast.error(`Error signing in with ${provider}. Please try again.`);
      }
    } finally {
      setLoadingProvider(null);
    }
  };

  // Handle errors from OAuth callback
  const error = searchParams.get('error');
  if (error) {
    toast.error(
      error === 'access_denied'
        ? 'Access was denied. Please try again or use another method.'
        : 'An error occurred during sign in. Please try again.'
    );
  }

  return (
    <div className="space-y-4">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="bg-white px-2 text-gray-500">
            Or continue with
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3">
        {providers.map((provider) => (
          <Button
            key={provider.id}
            variant="outline"
            onClick={() => handleOAuth(provider.id)}
            disabled={!!loadingProvider}
            className={`${provider.bgColor} ${provider.textColor} ${provider.hoverBgColor} flex items-center justify-center gap-2 border border-gray-300`}
          >
            {loadingProvider === provider.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <>
                {provider.icon}
                {provider.name}
              </>
            )}
          </Button>
        ))}
      </div>
    </div>
  );
}
