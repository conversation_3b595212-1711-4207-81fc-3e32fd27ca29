/**
 * @file index.tsx
 * @description React component for LoginForm
 */
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';

import Link from 'next/link';

import { yupResolver } from '@hookform/resolvers/yup';
import { Loader2 } from 'lucide-react';

import { login } from '@/app/auth/actions';
import PasswordInput from '@/components/PasswordInput';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { NetworkErrorModal } from '@/components/ui/network-error-modal';
import { loginSchema } from '@/lib/validations';

export default function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showNetworkError, setShowNetworkError] = useState(false);

  const {
    register,
    handleSubmit: handleFormSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // This function handles form submission using react-hook-form
  const onSubmit = handleFormSubmit(async (data) => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Create a FormData object from the form values
      const formData = new FormData();
      formData.append('email', data.email);
      formData.append('password', data.password);

      // Call the server action to authenticate
      const result = await login(formData);

      if (result?.error) {
        // Handle error cases
        if (result.error.includes('Network error')) {
          setShowNetworkError(true);
        } else {
          setErrorMessage(result.error);
        }
      } else if (result?.success) {
        // Success case - manually redirect to dashboard
        window.location.href = '/dashboard';
      }
    } catch (error: unknown) {
      console.error('Login error:', error);

      // Show network error modal for unexpected errors
      if (error instanceof Error) {
        setErrorMessage(`Authentication error: ${error.message}`);
      } else {
        setShowNetworkError(true);
      }
    } finally {
      setIsLoading(false);
    }
  });

  return (
    <>
      <form className="space-y-4" onSubmit={onSubmit}>
        {errorMessage && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}

        <div>
          <Input
            {...register('email')}
            placeholder="Email"
            className="w-full"
            name="email"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-destructive">
              {errors.email.message as string}
            </p>
          )}
        </div>
        <div>
          <PasswordInput
            register={register}
            name="password"
            placeholder="Password"
            error={errors.password?.message as string}
          />
        </div>
        <div className="flex items-center justify-between">
          <Link
            href="/auth/forgot-password"
            className="text-sm text-primary hover:underline"
          >
            Forgot password?
          </Link>
        </div>
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Logging in...
            </>
          ) : (
            'Log in'
          )}
        </Button>
      </form>

      <NetworkErrorModal
        isOpen={showNetworkError}
        onClose={() => setShowNetworkError(false)}
        onRetry={() => {
          setShowNetworkError(false);
          // We don't need to do anything else here as the user will retry by clicking the login button again
        }}
      />
    </>
  );
}
