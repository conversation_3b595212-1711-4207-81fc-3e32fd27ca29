/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */
'use client';

import { useEffect } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { motion } from 'framer-motion';

import OAuthProviders from '../../auth/components/OAuthProviders';
import RegisterForm from '../../auth/components/RegisterForm';

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

/**
 * @file page.tsx
 * @description Next.js page for auth/register route
 */

export default function RegisterPage() {
  const router = useRouter();

  useEffect(() => {
    router.push('/auth/login');
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="mx-auto w-full max-w-md space-y-6"
    >
      <h1 className="text-center text-3xl font-bold">
        Join Our Learning Community
      </h1>
      <RegisterForm />

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            Or continue with
          </span>
        </div>
      </div>

      <OAuthProviders />

      <div className="text-center text-sm text-muted-foreground">
        Already have an account?{' '}
        <Link href="/auth/login" className="text-primary hover:underline">
          Log in
        </Link>
      </div>
    </motion.div>
  );
}
