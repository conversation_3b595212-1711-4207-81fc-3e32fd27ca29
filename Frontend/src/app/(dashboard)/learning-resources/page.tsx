'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

type ResourceType = {
  id: string;
  title: string;
  description: string;
  type: 'video' | 'article' | 'tutorial';
  duration?: string;
  url: string;
  tags: string[];
};

const mockResources: ResourceType[] = [
  {
    id: '1',
    title: 'Introduction to Data Structures',
    description: 'Learn the fundamentals of data structures including arrays, linked lists, and trees.',
    type: 'video',
    duration: '45 min',
    url: '#',
    tags: ['beginner', 'data-structures', 'algorithms']
  },
  {
    id: '2',
    title: 'System Design Basics',
    description: 'Understand the core concepts of system design and architecture.',
    type: 'article',
    url: '#',
    tags: ['system-design', 'architecture']
  },
  {
    id: '3',
    title: 'Advanced JavaScript Patterns',
    description: 'Master advanced JavaScript patterns and best practices.',
    type: 'tutorial',
    duration: '2h 15min',
    url: '#',
    tags: ['javascript', 'advanced', 'patterns']
  }
];

export default function LearningResourcesPage() {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredResources = mockResources.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    if (activeTab === 'all') return matchesSearch;
    return resource.type === activeTab && matchesSearch;
  });

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Learning Resources</h1>
        <p className="text-muted-foreground">Access curated learning materials to enhance your skills</p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="video">Videos</TabsTrigger>
              <TabsTrigger value="article">Articles</TabsTrigger>
              <TabsTrigger value="tutorial">Tutorials</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="w-64">
            <input
              type="text"
              placeholder="Search resources..."
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredResources.map((resource) => (
            <Card key={resource.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{resource.title}</CardTitle>
                  <span className="text-xs px-2 py-1 bg-secondary text-secondary-foreground rounded-full capitalize">
                    {resource.type}
                  </span>
                </div>
                {resource.duration && (
                  <div className="text-sm text-muted-foreground">
                    {resource.duration}
                  </div>
                )}
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-4">
                  {resource.description}
                </CardDescription>
                <div className="flex flex-wrap gap-2 mt-2">
                  {resource.tags.map((tag) => (
                    <span 
                      key={tag} 
                      className="text-xs px-2 py-1 bg-muted rounded-full text-muted-foreground"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
