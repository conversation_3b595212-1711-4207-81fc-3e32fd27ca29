/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */
'use client';

import Link from 'next/link';

import { motion } from 'framer-motion';

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

/**
 * @file page.tsx
 * @description Next.js page for blogs route
 */

const blogs = [
  {
    id: 1,
    title: 'Understanding JavaScript Closures',
    description:
      'A deep dive into closures in JavaScript and how to use them effectively in your applications.',
    author: 'Shailesh Chaudhari',
    date: 'June 15, 2023',
    category: 'JavaScript',
    readTime: '8 min read',
    link: '/blogs/js-closures',
  },
  {
    id: 2,
    title: 'A Guide to Responsive Web Design',
    description:
      'Learn how to make your websites look great on all devices with responsive design techniques and best practices.',
    author: 'Priya Sharma',
    date: 'July 22, 2023',
    category: 'Web Development',
    readTime: '10 min read',
    link: '/blogs/responsive-web-design',
  },
  {
    id: 3,
    title: 'Top 10 CSS Tricks for Beginners',
    description:
      'Improve your CSS skills with these 10 essential tricks every beginner should know to create beautiful websites.',
    author: 'Rahul Patel',
    date: 'August 5, 2023',
    category: 'CSS',
    readTime: '6 min read',
    link: '/blogs/css-tricks',
  },
  {
    id: 4,
    title: 'Getting Started with React Hooks',
    description:
      'A comprehensive guide to React Hooks and how they can simplify your functional components.',
    author: 'Ananya Desai',
    date: 'September 12, 2023',
    category: 'React',
    readTime: '12 min read',
    link: '/blogs/react-hooks',
  },
  {
    id: 5,
    title: 'Data Structures Every Developer Should Know',
    description:
      'Learn about essential data structures that will help you write more efficient and optimized code.',
    author: 'Shailesh Chaudhari',
    date: 'October 3, 2023',
    category: 'Data Structures',
    readTime: '15 min read',
    link: '/blogs/essential-data-structures',
  },
  {
    id: 6,
    title: 'Mastering Git for Team Collaboration',
    description:
      'Improve your Git skills and learn advanced techniques for better team collaboration and version control.',
    author: 'Rahul Patel',
    date: 'November 18, 2023',
    category: 'Tools',
    readTime: '9 min read',
    link: '/blogs/git-collaboration',
  },
];

const categories = [
  { id: 'all', name: 'All Posts' },
  { id: 'javascript', name: 'JavaScript' },
  { id: 'react', name: 'React' },
  { id: 'css', name: 'CSS' },
  { id: 'web', name: 'Web Development' },
  { id: 'data', name: 'Data Structures' },
];

export default function Blogs() {
  return (
    <main className="bg-background py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <motion.div
          className="mb-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">
            Our <span className="text-primary">Blog</span>
          </h1>
          <p className="mx-auto max-w-3xl text-lg text-muted-foreground">
            Discover our latest articles, tutorials, and resources to help you
            on your engineering journey. Stay updated with the latest trends and
            best practices.
          </p>
        </motion.div>

        {/* Categories */}
        <motion.div
          className="mb-8 flex flex-wrap justify-center gap-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {categories.map((category) => (
            <button
              key={category.id}
              className="rounded-full bg-card px-4 py-2 text-sm font-medium text-foreground transition-colors hover:bg-muted"
            >
              {category.name}
            </button>
          ))}
        </motion.div>

        {/* Blog Posts */}
        <motion.div
          className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          {blogs.map((blog, index) => (
            <motion.div
              key={blog.id}
              className="overflow-hidden rounded-xl border border-border bg-card shadow-sm transition-all hover:shadow-md"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
            >
              <div className="h-48 bg-muted">
                {/* Placeholder for blog image */}
                <div className="bg-primary/10 flex h-full items-center justify-center text-primary">
                  <span className="text-lg font-medium">{blog.category}</span>
                </div>
              </div>
              <div className="p-6">
                <div className="mb-2 flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {blog.date}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {blog.readTime}
                  </span>
                </div>
                <h2 className="mb-2 text-xl font-bold text-foreground">
                  {blog.title}
                </h2>
                <p className="mb-4 text-muted-foreground">{blog.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">
                    By {blog.author}
                  </span>
                  <Link
                    href={blog.link}
                    className="font-semibold text-primary transition-colors hover:text-primary2 hover:underline"
                  >
                    Read more
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Newsletter Section */}
        <motion.div
          className="mt-16 rounded-xl border border-border bg-card p-8 shadow-sm"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="grid gap-8 md:grid-cols-2">
            <div>
              <h2 className="mb-4 text-2xl font-bold text-foreground">
                Subscribe to Our Newsletter
              </h2>
              <p className="text-muted-foreground">
                Stay updated with our latest articles, tutorials, and resources.
                We&apos;ll send you a weekly digest of our best content directly
                to your inbox.
              </p>
            </div>
            <div className="flex items-center">
              <form className="w-full">
                <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="flex-1 rounded-md border border-input bg-background px-4 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                    required
                  />
                  <button
                    type="submit"
                    className="rounded-md bg-primary px-4 py-2 font-medium text-white transition-colors hover:bg-primary2"
                  >
                    Subscribe
                  </button>
                </div>
              </form>
            </div>
          </div>
        </motion.div>
      </div>
    </main>
  );
}
