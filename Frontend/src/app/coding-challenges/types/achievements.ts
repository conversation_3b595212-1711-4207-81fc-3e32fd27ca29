/**
 * @file achievements.ts
 * @description Type definitions for achievements features
 */
export interface IAchievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  tier: AchievementTier;
  points: number;
  progress?: {
    current: number;
    target: number;
    percentage: number;
  };
  unlocked: boolean;
  unlocked_at?: string;
  hidden?: boolean;
}

export type AchievementCategory =
  | 'challenges'
  | 'solutions'
  | 'streaks'
  | 'community'
  | 'special';

export type AchievementTier =
  | 'bronze'
  | 'silver'
  | 'gold'
  | 'platinum'
  | 'diamond';

export interface IAchievementNotification {
  id: string;
  achievement_id: string;
  achievement: IAchievement;
  user_id: string;
  read: boolean;
  created_at: string;
}

// Achievement tier colors
export const achievementTierColors = {
  bronze: {
    bg: 'bg-amber-100 dark:bg-amber-950/30',
    text: 'text-amber-800 dark:text-amber-400',
    border: 'border-amber-300 dark:border-amber-900/50',
    icon: 'text-amber-600 dark:text-amber-500',
  },
  silver: {
    bg: 'bg-slate-100 dark:bg-slate-950/30',
    text: 'text-slate-800 dark:text-slate-400',
    border: 'border-slate-300 dark:border-slate-900/50',
    icon: 'text-slate-600 dark:text-slate-500',
  },
  gold: {
    bg: 'bg-yellow-100 dark:bg-yellow-950/30',
    text: 'text-yellow-800 dark:text-yellow-400',
    border: 'border-yellow-300 dark:border-yellow-900/50',
    icon: 'text-yellow-600 dark:text-yellow-500',
  },
  platinum: {
    bg: 'bg-cyan-100 dark:bg-cyan-950/30',
    text: 'text-cyan-800 dark:text-cyan-400',
    border: 'border-cyan-300 dark:border-cyan-900/50',
    icon: 'text-cyan-600 dark:text-cyan-500',
  },
  diamond: {
    bg: 'bg-violet-100 dark:bg-violet-950/30',
    text: 'text-violet-800 dark:text-violet-400',
    border: 'border-violet-300 dark:border-violet-900/50',
    icon: 'text-violet-600 dark:text-violet-500',
  },
};
