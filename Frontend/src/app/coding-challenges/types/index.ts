/**
 * @file index.ts
 * @description Type definitions for index features
 */
export interface IChallenge {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  category?: string;
  points?: number;
  tags?: string[];
  status?: string;
  created_at?: string;
  updated_at?: string;
  topic_id?: string;
  input_format?: string;
  output_format?: string;
  example_input?: string;
  example_output?: string;
  constraints?: string;
  function_signature?: string;
  time_limit?: number;
  memory_limit?: number;
  solutions?: Record<string, string>;
}
