/**
 * @file battle.ts
 * @description Type definitions for battle features
 */
export interface IBattle {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'active' | 'completed';
  start_time: string;
  end_time: string;
  participants_count: number;
  max_participants?: number;
  challenges: IBattleChallenge[];
  leaderboard?: IBattleLeaderboardEntry[];
  created_at: string;
  updated_at: string;
  is_enrolled?: boolean;
}

export interface IBattleChallenge {
  id: string;
  title: string;
  description: string;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  points: number;
  order: number;
  is_completed?: boolean;
  is_locked?: boolean;
}

export interface IBattleLeaderboardEntry {
  user_id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  points: number;
  challenges_completed: number;
  rank: number;
  is_current_user?: boolean;
}

export interface IBattleProgress {
  battle_id: string;
  user_id: string;
  challenges_completed: number;
  total_points: number;
  last_activity: string;
}
