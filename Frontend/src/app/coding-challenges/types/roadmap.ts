/**
 * @file roadmap.ts
 * @description Type definitions for roadmap features
 */
export interface IRoadmapTopic {
  id: string;
  title: string;
  description: string;
  parent_id?: string;
  level: number;
  order: number;
  status?: 'not_started' | 'in_progress' | 'completed';
  progress?: number;
  challenges?: IRoadmapChallenge[];
}

export interface IRoadmapChallenge {
  id: string;
  title: string;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  points: number;
  status?: 'not_started' | 'in_progress' | 'completed';
  is_required?: boolean;
  order?: number;
}

export interface IRoadmap {
  id: string;
  title: string;
  description: string;
  image_url?: string;
  topics: IRoadmapTopic[];
  progress?: {
    completed: number;
    total: number;
    percentage: number;
  };
  challenges_count: number;
  enrolled_count?: number;
  created_at: string;
  updated_at: string;
}

export interface IRoadmapProgress {
  roadmap_id: string;
  topic_id: string;
  challenge_id: string;
  status: 'not_started' | 'in_progress' | 'completed';
  progress?: number;
  last_activity_date?: string;
}
