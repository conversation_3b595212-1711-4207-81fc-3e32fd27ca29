/**
 * @file index.tsx
 * @description React component for ChallengeCard
 */
import { motion } from 'framer-motion';
import { ArrowRight, Award, Share2, Tag, Trophy } from 'lucide-react';

import BookmarkButton from '@/components/BookmarkButton';
import ChallengeProgressIndicator from '@/components/ChallengeProgressIndicator';
import ShareButton from '@/components/ShareButton';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useLocalChallengeProgress } from '@/hooks/useChallengeProgress';

import { IChallenge } from '../../types';

// Define difficulty color mapping
const difficultyColors = {
  EASY: 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400',
  MEDIUM:
    'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400',
  HARD: 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400',
};

interface ChallengeCardProps {
  challenge: IChallenge;
  showProgress?: boolean;
}

export default function ChallengeCard({
  challenge,
  showProgress = false,
}: ChallengeCardProps) {
  // Use local storage fallback for progress tracking
  const { getChallengeProgress } = useLocalChallengeProgress();
  const progress = getChallengeProgress(challenge.id);
  const status = progress?.status || 'not_started';
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="h-full"
    >
      <Card className="flex h-full flex-col">
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="mb-1 flex items-center justify-between">
                {showProgress && (
                  <ChallengeProgressIndicator
                    status={status}
                    progress={progress === null ? undefined : progress}
                    size="sm"
                  />
                )}
                <BookmarkButton
                  challengeId={challenge.id}
                  variant="icon"
                  size="sm"
                  className="ml-auto"
                />
              </div>
              <CardTitle className="line-clamp-1 text-xl font-bold">
                {challenge.title}
              </CardTitle>
            </div>
            <div className="flex flex-col items-end gap-2">
              <Badge
                variant="outline"
                className={`${difficultyColors[challenge.difficulty as keyof typeof difficultyColors] || 'bg-gray-100'} ml-2 whitespace-nowrap`}
              >
                {challenge.difficulty || 'Unknown'}
              </Badge>

              {/* Achievement badge - shown if challenge is completed */}
              {status === 'completed' && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                        <Award className="h-3.5 w-3.5" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p className="text-sm">Challenge completed!</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-grow">
          <p className="mb-4 line-clamp-3 text-muted-foreground">
            {challenge.description || 'No description available'}
          </p>
          {challenge.tags && challenge.tags.length > 0 && (
            <div className="mb-3 flex flex-wrap gap-2">
              {challenge.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  <Tag className="mr-1 h-3 w-3" />
                  {tag}
                </Badge>
              ))}
              {challenge.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{challenge.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
          {challenge.points !== undefined && (
            <div className="flex items-center text-sm text-muted-foreground">
              <Trophy className="mr-1 h-4 w-4" />
              <span>{challenge.points} points</span>
            </div>
          )}
        </CardContent>
        <CardFooter className="mt-auto flex gap-2 pt-0">
          <ShareButton
            url={`/coding-challenges/${challenge.id}`}
            title={challenge.title}
            description={`A ${challenge.difficulty.toLowerCase()} level coding challenge worth ${challenge.points} points.`}
            variant="outline"
            size="icon"
            showText={false}
            className="flex-shrink-0"
          />
          <Button asChild className="flex-grow" variant="default">
            <a
              href={`/coding-challenges/${challenge.id}`}
              className="flex items-center justify-center"
            >
              Solve Challenge
              <ArrowRight className="ml-2 h-4 w-4" />
            </a>
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
