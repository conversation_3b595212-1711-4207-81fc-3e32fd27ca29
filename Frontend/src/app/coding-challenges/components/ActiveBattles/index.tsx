/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */
'use client';

import { useState } from 'react';

import Link from 'next/link';

import { motion } from 'framer-motion';
import {
  <PERSON>R<PERSON>,
  ChevronLeft,
  ChevronRight,
  Clock,
  Swords,
  Trophy,
  Users,
} from 'lucide-react';

import { IBattle } from '@/app/coding-challenges/types/battle';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useBattles } from '@/hooks/useBattles';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

/**
 * @file index.tsx
 * @description React component for ActiveBattles
 */

interface ActiveBattlesProps {
  maxDisplay?: number;
  className?: string;
}

export default function ActiveBattles({
  maxDisplay = 3,
  className = '',
}: ActiveBattlesProps) {
  const { isLoading, error, getActiveBattles, getEnrolledBattles, enroll } =
    useBattles();

  const [currentPage, setCurrentPage] = useState(0);

  // Get active battles
  const activeBattles = getActiveBattles();
  const enrolledBattles = getEnrolledBattles().filter(
    (battle) => battle.status === 'active',
  );

  // Prioritize enrolled active battles, then other active battles
  const displayBattles = [
    ...enrolledBattles,
    ...activeBattles.filter((battle) => !battle.is_enrolled),
  ];

  // Calculate total pages
  const totalPages = Math.ceil(displayBattles.length / maxDisplay);

  // Get current page battles
  const currentBattles = displayBattles.slice(
    currentPage * maxDisplay,
    (currentPage + 1) * maxDisplay,
  );

  // Handle page navigation
  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  // Format time remaining
  const formatTimeRemaining = (endTime: string) => {
    const end = new Date(endTime).getTime();
    const now = new Date().getTime();
    const diff = end - now;

    if (diff <= 0) return 'Ended';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h remaining`;
    }

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    }

    return `${minutes}m remaining`;
  };

  // Calculate progress for enrolled battles
  const calculateProgress = (battle: IBattle) => {
    if (!battle.challenges) return 0;

    const completedChallenges = battle.challenges.filter(
      (c) => c.is_completed,
    ).length;
    return (completedChallenges / battle.challenges.length) * 100;
  };

  // Handle enrollment
  const handleEnroll = async (battleId: string) => {
    await enroll(battleId);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-full max-w-md" />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={cn(
          'border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center',
          className,
        )}
      >
        <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
          Failed to load battles
        </h2>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  // No active battles
  if (displayBattles.length === 0) {
    return null;
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="flex items-center text-2xl font-bold">
            <Swords className="mr-2 h-6 w-6 text-primary" />
            Active Battles
          </h2>
          <p className="text-muted-foreground">
            Join coding battles to compete with others and win prizes
          </p>
        </div>

        {totalPages > 1 && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={prevPage}
              disabled={totalPages <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-muted-foreground">
              {currentPage + 1}/{totalPages}
            </span>
            <Button
              variant="outline"
              size="icon"
              onClick={nextPage}
              disabled={totalPages <= 1}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        {currentBattles.map((battle, index) => (
          <motion.div
            key={battle.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card
              className={cn(
                'h-full transition-all hover:shadow-md',
                battle.is_enrolled && 'border-primary/50',
              )}
            >
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {battle.is_enrolled && (
                      <Badge className="mb-2 bg-primary text-primary-foreground">
                        Enrolled
                      </Badge>
                    )}
                    <CardTitle className="line-clamp-1 text-xl">
                      {battle.title}
                    </CardTitle>
                    <CardDescription className="line-clamp-2">
                      {battle.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pb-2">
                <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <Users className="mr-1 h-4 w-4" />
                    <span>{battle.participants_count} participants</span>
                  </div>

                  <div className="flex items-center">
                    <Trophy className="mr-1 h-4 w-4 text-yellow-500" />
                    <span>{battle.challenges.length} challenges</span>
                  </div>

                  <div className="flex items-center">
                    <Clock className="mr-1 h-4 w-4 text-blue-500" />
                    <span>{formatTimeRemaining(battle.end_time)}</span>
                  </div>
                </div>

                {battle.is_enrolled && (
                  <div className="mt-4 space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span>Your progress</span>
                      <span>
                        {battle.challenges.filter((c) => c.is_completed).length}
                        /{battle.challenges.length} completed
                      </span>
                    </div>
                    <Progress
                      value={calculateProgress(battle)}
                      className="h-1.5"
                    />
                  </div>
                )}
              </CardContent>

              <CardFooter>
                {battle.is_enrolled ? (
                  <Button asChild className="w-full">
                    <Link
                      href={`/coding-challenges/battles/${battle.id}`}
                      className="flex items-center justify-center"
                    >
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Continue Battle
                    </Link>
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleEnroll(battle.id)}
                  >
                    <Swords className="mr-2 h-4 w-4" />
                    Enroll in Battle
                  </Button>
                )}
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="flex justify-center">
        <Button asChild variant="outline">
          <Link href="/coding-challenges/battles" className="flex items-center">
            <Swords className="mr-2 h-4 w-4" />
            View All Battles
          </Link>
        </Button>
      </div>
    </div>
  );
}
