/**
 * @file index.tsx
 * @description React component for Fixed
 */
'use client';

import { useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import { ArrowRight, Search, Tag, Trophy } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAxiosGet } from '@/hooks/useAxios';

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

/**
 * @file index.tsx
 * @description React component for Fixed
 */

// Define the challenge interface based on the actual API response
interface IChallenge {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  category?: string;
  points?: number;
  tags?: string[];
  status?: string;
  created_at?: string;
  updated_at?: string;
  topic_id?: string;
  input_format?: string;
  output_format?: string;
  example_input?: string;
  example_output?: string;
  constraints?: string;
  function_signature?: string;
  time_limit?: number;
  memory_limit?: number;
  solutions?: Record<string, string>;
}

const difficultyColors = {
  EASY: 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400',
  MEDIUM:
    'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400',
  HARD: 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400',
};

const ChallengeSkeleton = () => (
  <div className="h-full">
    <Card className="flex h-full flex-col">
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <Skeleton className="h-7 w-3/4" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <Skeleton className="mb-2 h-4 w-full" />
        <Skeleton className="mb-2 h-4 w-full" />
        <Skeleton className="mb-4 h-4 w-2/3" />
        <div className="mb-3 flex gap-2">
          <Skeleton className="h-5 w-16 rounded-full" />
          <Skeleton className="h-5 w-16 rounded-full" />
        </div>
        <Skeleton className="h-4 w-24" />
      </CardContent>
      <CardFooter className="pt-0">
        <Skeleton className="h-10 w-full rounded-md" />
      </CardFooter>
    </Card>
  </div>
);

export default function FixedCodingChallengesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [challenges, setChallenges] = useState<IChallenge[]>([]);
  const [page, setPage] = useState(1);
  const [isFetching, setIsFetching] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');

  const [getChallenges] = useAxiosGet<{
    data: { challenges: IChallenge[] };
    meta: { hasNextPage?: boolean; totalPages: number };
  }>('/challenges');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(1);
    setChallenges([]);
  };

  const handleDifficultyChange = (value: string) => {
    setDifficultyFilter(value);
    setPage(1);
    setChallenges([]);
  };

  const fetchChallenges = async () => {
    try {
      setIsFetching(true);
      setError(null);

      const response = await getChallenges({
        params: {
          page,
          category: activeTab !== 'all' ? activeTab : undefined,
          difficulty:
            difficultyFilter !== 'all'
              ? difficultyFilter.toUpperCase()
              : undefined,
          search: searchTerm || undefined,
        },
      });

      // Extract challenges from the response
      let challengesData: IChallenge[] = [];
      let totalPagesData = 0;

      // Check if the response has the expected structure
      if (
        response.data &&
        response.data.data &&
        response.data.data.challenges
      ) {
        challengesData = response.data.data.challenges;
        totalPagesData = response.data.meta?.totalPages || 1;
      } else {
        console.error('Could not find challenges in the response structure');
        setError('Failed to load challenges. Unexpected response format.');
      }

      // Update the state with the extracted challenges
      setChallenges((prevChallenges) => {
        return page === 1
          ? challengesData
          : [...prevChallenges, ...challengesData];
      });

      setHasMore(page < totalPagesData);
      setIsFetching(false);
    } catch (error) {
      console.error('Error fetching challenges:', error);
      setIsFetching(false);
      setError((error as { message: string }).message);
    }
  };

  useEffect(() => {
    fetchChallenges();
  }, [page, activeTab, difficultyFilter]);

  useEffect(() => {
    const handler = setTimeout(() => {
      if (searchTerm !== '') {
        fetchChallenges();
      }
    }, 500);

    return () => clearTimeout(handler);
  }, [searchTerm]);

  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + window.scrollY >=
          document.body.offsetHeight - 500 &&
        hasMore &&
        !isFetching &&
        !error
      ) {
        setPage((prevPage) => prevPage + 1);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, isFetching, error]);

  return (
    <div className="container mx-auto p-4 pb-20">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8 text-center"
      >
        <h1 className="mb-2 text-4xl font-bold">Coding Challenges</h1>
        <p className="mx-auto max-w-2xl text-muted-foreground">
          Sharpen your programming skills with our collection of coding
          challenges across various difficulty levels and topics.
        </p>
      </motion.div>

      <div className="mb-8">
        <div className="mb-6 flex flex-col gap-4 md:flex-row">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search challenges..."
              value={searchTerm}
              onChange={handleSearch}
              className="pl-10"
            />
          </div>
          <div className="w-full md:w-48">
            <Select
              value={difficultyFilter}
              onValueChange={handleDifficultyChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="Difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Difficulties</SelectItem>
                <SelectItem value="easy">Easy</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="hard">Hard</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs
          defaultValue="all"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="w-full md:w-auto">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="algorithms">Algorithms</TabsTrigger>
            <TabsTrigger value="data_structures">Data Structures</TabsTrigger>
            <TabsTrigger value="system_design">System Design</TabsTrigger>
            <TabsTrigger value="databases">Databases</TabsTrigger>
            <TabsTrigger value="web_development">Web Dev</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {error && (
        <div className="border-red-400 bg-red-100 text-red-700 mb-6 rounded border px-4 py-3">
          <p>{error}</p>
        </div>
      )}

      {/* Debug panel */}
      <div className="mb-4 rounded border border-yellow-400 bg-yellow-100 p-4">
        <h3 className="font-bold">Debug Info:</h3>
        <p>Challenges array length: {challenges.length}</p>
        <p>Is fetching: {isFetching ? 'Yes' : 'No'}</p>
        <p>Has error: {error ? 'Yes: ' + error : 'No'}</p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {isFetching && challenges.length === 0 ? (
          // Show skeletons when initially loading
          Array(6)
            .fill(0)
            .map((_, index) => <ChallengeSkeleton key={index} />)
        ) : challenges.length > 0 ? (
          // Map through challenges and render cards
          challenges.map((challenge) => (
            <div key={challenge.id || 'no-id'}>
              <Card className="flex h-full flex-col">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <CardTitle className="line-clamp-1 text-xl font-bold">
                      {challenge.title}
                    </CardTitle>
                    <Badge
                      variant="outline"
                      className={`${difficultyColors[challenge.difficulty as keyof typeof difficultyColors] || 'bg-gray-100'} ml-2 whitespace-nowrap`}
                    >
                      {challenge.difficulty || 'Unknown'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="flex-grow">
                  <p className="mb-4 line-clamp-3 text-muted-foreground">
                    {challenge.description || 'No description available'}
                  </p>
                  {challenge.tags && challenge.tags.length > 0 && (
                    <div className="mb-3 flex flex-wrap gap-2">
                      {challenge.tags.slice(0, 3).map((tag, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="text-xs"
                        >
                          <Tag className="mr-1 h-3 w-3" />
                          {tag}
                        </Badge>
                      ))}
                      {challenge.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{challenge.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}
                  {challenge.points !== undefined && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Trophy className="mr-1 h-4 w-4" />
                      <span>{challenge.points} points</span>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="mt-auto pt-0">
                  <Button asChild className="w-full" variant="default">
                    <a
                      href={`/coding-challenges/${challenge.id}`}
                      className="flex items-center justify-center"
                    >
                      Solve Challenge
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </a>
                  </Button>
                </CardFooter>
              </Card>
            </div>
          ))
        ) : (
          <div className="col-span-full py-12 text-center">
            <div className="mb-4 inline-flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
              <Search className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="mb-1 text-lg font-medium">No challenges found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </div>

      {isFetching && challenges.length > 0 && (
        <div className="mt-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array(3)
            .fill(0)
            .map((_, index) => (
              <ChallengeSkeleton key={`loading-${index}`} />
            ))}
        </div>
      )}

      {hasMore && !isFetching && challenges.length > 0 && (
        <div className="mt-8 text-center">
          <Button
            variant="outline"
            onClick={() => setPage((prev) => prev + 1)}
            className="mx-auto"
          >
            Load More Challenges
          </Button>
        </div>
      )}
    </div>
  );
}
