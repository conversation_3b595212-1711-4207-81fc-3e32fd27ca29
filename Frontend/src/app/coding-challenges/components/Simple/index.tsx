/**
 * @file index.tsx
 * @description React component for Simple
 */
'use client';

import { useEffect, useState } from 'react';

import { useAxiosGet } from '@/hooks/useAxios';

import { IChallenge } from '../../types';

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

/**
 * @file index.tsx
 * @description React component for Simple
 */

interface Challenge {
  id: string;
  title: string;
  description: string;
  difficulty: string;
}

export default function SimpleChallengesPage() {
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [getChallenges] = useAxiosGet<{ challenges: IChallenge[] }>(
    '/challenges',
  );

  useEffect(() => {
    async function fetchChallenges() {
      try {
        setIsLoading(true);
        const response = await getChallenges();

        // Extract challenges from the response
        let challengesData: Challenge[] = [];

        if (response.data && response.data.challenges) {
          challengesData = response.data.challenges;
        } else {
          console.error('Could not find challenges in the response');
        }

        setChallenges(challengesData);
      } catch (err) {
        console.error('Error fetching challenges:', err);
        setError('Failed to load challenges');
      } finally {
        setIsLoading(false);
      }
    }

    fetchChallenges();
  }, []);

  return (
    <div className="container mx-auto p-4">
      <h1 className="mb-4 text-2xl font-bold">Coding Challenges</h1>

      {isLoading && <p>Loading challenges...</p>}

      {error && <p className="text-red-500">{error}</p>}

      <div className="mb-4 rounded border border-yellow-400 bg-yellow-100 p-4">
        <h3 className="font-bold">Debug Info:</h3>
        <p>Challenges array length: {challenges.length}</p>
        <p>Is loading: {isLoading ? 'Yes' : 'No'}</p>
        <p>Has error: {error ? 'Yes: ' + error : 'No'}</p>
      </div>

      {!isLoading && challenges.length === 0 && !error && (
        <p>No challenges found.</p>
      )}

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {challenges.map((challenge) => (
          <div key={challenge.id} className="rounded border p-4 shadow">
            <h2 className="text-xl font-bold">{challenge.title}</h2>
            <p className="text-gray-700">{challenge.description}</p>
            <div className="mt-2">
              <span className="inline-block rounded bg-blue-100 px-2 py-1 text-sm text-blue-800">
                {challenge.difficulty}
              </span>
            </div>
            <a
              href={`/coding-challenges/${challenge.id}`}
              className="mt-4 inline-block rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
            >
              View Challenge
            </a>
          </div>
        ))}
      </div>
    </div>
  );
}
