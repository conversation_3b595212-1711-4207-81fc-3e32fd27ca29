/**
 * @file index.tsx
 * @description React component for EmptyChallenges
 */
import { Search } from 'lucide-react';

import { EmptyState } from '@/components/LoadingStates';

interface EmptyChallengesProps {
  title?: string;
  description?: string;
  actionText?: string;
  actionHref?: string;
}

export default function EmptyChallenges({
  title = 'No challenges found',
  description = 'Try adjusting your search or filter criteria',
  actionText,
  actionHref,
}: EmptyChallengesProps) {
  return (
    <div className="col-span-full py-12">
      <EmptyState
        icon={Search}
        title={title}
        description={description}
        actionText={actionText}
        actionHref={actionHref}
      />
    </div>
  );
}
