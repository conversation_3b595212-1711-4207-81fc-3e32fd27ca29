/**
 * @file index.tsx
 * @description React component for ChallengeFilters
 */
import { useEffect, useState } from 'react';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';

import {
  Bookmark,
  Check,
  CheckCircle2,
  Clock,
  Filter,
  Plus,
  RotateCcw,
  Save,
  Search,
  SlidersHorizontal,
  Tag,
  Trash2,
  TrendingUp,
  User,
  X,
} from 'lucide-react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

export interface FilterPreset {
  id: string;
  name: string;
  filters: {
    searchTerm: string;
    difficulty: string;
    category: string;
    tags: string[];
    status: string;
    sortBy: string;
  };
}

export interface ChallengeFiltersProps {
  searchTerm: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  difficultyFilter: string;
  onDifficultyChange: (value: string) => void;
  activeTab: string;
  onTabChange: (value: string) => void;
  sortBy?: string;
  onSortChange?: (value: string) => void;
  tags?: string[];
  onTagsChange?: (tags: string[]) => void;
  statusFilter?: string;
  onStatusChange?: (value: string) => void;
  onResetFilters?: () => void;
  availableTags?: string[];
}

export function ChallengeFilters({
  searchTerm,
  onSearchChange,
  difficultyFilter,
  onDifficultyChange,
  activeTab,
  onTabChange,
  sortBy = 'newest',
  onSortChange,
  tags = [],
  onTagsChange,
  statusFilter = 'all',
  onStatusChange,
  onResetFilters,
  availableTags = [
    'arrays',
    'strings',
    'linked-lists',
    'trees',
    'graphs',
    'dynamic-programming',
    'recursion',
    'sorting',
    'searching',
    'hash-tables',
    'binary-search',
    'dfs',
    'bfs',
    'greedy',
    'backtracking',
    'math',
    'bit-manipulation',
    'stack',
    'queue',
    'heap',
  ],
}: ChallengeFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // State for filter presets
  const [presets, setPresets] = useState<FilterPreset[]>([]);
  const [showSavePresetDialog, setShowSavePresetDialog] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>(tags);
  const [showTagsPopover, setShowTagsPopover] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  // Load presets from localStorage on mount
  useEffect(() => {
    const savedPresets = localStorage.getItem('challenge-filter-presets');
    if (savedPresets) {
      try {
        setPresets(JSON.parse(savedPresets));
      } catch (error) {
        console.error('Error loading filter presets:', error);
      }
    }
  }, []);

  // Update active filters count
  useEffect(() => {
    let count = 0;
    if (difficultyFilter !== 'all') count++;
    if (statusFilter !== 'all') count++;
    if (tags.length > 0) count++;
    if (sortBy !== 'newest') count++;
    if (searchTerm) count++;
    setActiveFiltersCount(count);
  }, [difficultyFilter, statusFilter, tags, sortBy, searchTerm]);

  // Save presets to localStorage
  const savePresets = (updatedPresets: FilterPreset[]) => {
    localStorage.setItem(
      'challenge-filter-presets',
      JSON.stringify(updatedPresets),
    );
    setPresets(updatedPresets);
  };

  // Handle saving a new preset
  const handleSavePreset = () => {
    if (!presetName.trim()) {
      toast.error('Please enter a name for your preset');
      return;
    }

    const newPreset: FilterPreset = {
      id: Date.now().toString(),
      name: presetName.trim(),
      filters: {
        searchTerm,
        difficulty: difficultyFilter,
        category: activeTab,
        tags: tags,
        status: statusFilter,
        sortBy: sortBy,
      },
    };

    const updatedPresets = [...presets, newPreset];
    savePresets(updatedPresets);
    setPresetName('');
    setShowSavePresetDialog(false);
    toast.success('Filter preset saved');
  };

  // Handle applying a preset
  const applyPreset = (preset: FilterPreset) => {
    if (onSearchChange) {
      const event = {
        target: { value: preset.filters.searchTerm },
      } as React.ChangeEvent<HTMLInputElement>;
      onSearchChange(event);
    }
    if (onDifficultyChange) onDifficultyChange(preset.filters.difficulty);
    if (onTabChange) onTabChange(preset.filters.category);
    if (onSortChange) onSortChange(preset.filters.sortBy);
    if (onTagsChange) onTagsChange(preset.filters.tags);
    if (onStatusChange) onStatusChange(preset.filters.status);

    toast.success(`Applied preset: ${preset.name}`);
  };

  // Handle deleting a preset
  const deletePreset = (id: string) => {
    const updatedPresets = presets.filter((preset) => preset.id !== id);
    savePresets(updatedPresets);
    toast.success('Preset deleted');
  };

  // Handle tag selection
  const handleTagSelection = (tag: string) => {
    const updatedTags = selectedTags.includes(tag)
      ? selectedTags.filter((t) => t !== tag)
      : [...selectedTags, tag];

    setSelectedTags(updatedTags);
  };

  // Apply selected tags
  const applyTags = () => {
    if (onTagsChange) {
      onTagsChange(selectedTags);
    }
    setShowTagsPopover(false);
  };

  // Reset filters
  const handleResetFilters = () => {
    if (onResetFilters) {
      onResetFilters();
    }
    setSelectedTags([]);
  };

  return (
    <div className="mb-8 space-y-4">
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search challenges..."
            value={searchTerm}
            onChange={onSearchChange}
            className="pl-10"
          />
        </div>
        <div className="flex flex-wrap gap-2">
          <div className="w-full sm:w-auto">
            <Select value={difficultyFilter} onValueChange={onDifficultyChange}>
              <SelectTrigger className="w-full sm:w-[140px]">
                <SelectValue placeholder="Difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Difficulties</SelectItem>
                <SelectItem value="easy">Easy</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="hard">Hard</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="w-full sm:w-auto">
            <Select
              value={statusFilter}
              onValueChange={(value) => onStatusChange?.(value)}
            >
              <SelectTrigger className="w-full sm:w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">
                  <div className="flex items-center">
                    <CheckCircle2 className="mr-2 h-4 w-4 text-green-500" />
                    Completed
                  </div>
                </SelectItem>
                <SelectItem value="in_progress">
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-blue-500" />
                    In Progress
                  </div>
                </SelectItem>
                <SelectItem value="not_started">
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-gray-500" />
                    Not Started
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Sort Options */}
          <div className="w-full sm:w-auto">
            <Select
              value={sortBy}
              onValueChange={(value) => onSortChange?.(value)}
            >
              <SelectTrigger className="w-full sm:w-[140px]">
                <SelectValue placeholder="Sort By" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest</SelectItem>
                <SelectItem value="oldest">Oldest</SelectItem>
                <SelectItem value="most_popular">Most Popular</SelectItem>
                <SelectItem value="highest_rated">Highest Rated</SelectItem>
                <SelectItem value="most_submissions">
                  Most Submissions
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tags Filter */}
          <Popover open={showTagsPopover} onOpenChange={setShowTagsPopover}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto">
                <Tag className="mr-2 h-4 w-4" />
                Tags
                {tags.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {tags.length}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-4">
                <h4 className="font-medium">Filter by Tags</h4>
                <div className="max-h-[200px] space-y-2 overflow-y-auto">
                  {availableTags.map((tag) => (
                    <div key={tag} className="flex items-center space-x-2">
                      <Checkbox
                        id={`tag-${tag}`}
                        checked={selectedTags.includes(tag)}
                        onCheckedChange={() => handleTagSelection(tag)}
                      />
                      <Label
                        htmlFor={`tag-${tag}`}
                        className="flex-grow cursor-pointer"
                      >
                        {tag.replace(/-/g, ' ')}
                      </Label>
                    </div>
                  ))}
                </div>
                <div className="flex justify-between border-t pt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedTags([])}
                  >
                    Clear
                  </Button>
                  <Button size="sm" onClick={applyTags}>
                    Apply
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Filter Presets */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto">
                <SlidersHorizontal className="mr-2 h-4 w-4" />
                Presets
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Saved Filter Presets</h4>
                  <Dialog
                    open={showSavePresetDialog}
                    onOpenChange={setShowSavePresetDialog}
                  >
                    <DialogTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <Plus className="mr-1 h-4 w-4" />
                        Save Current
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Save Filter Preset</DialogTitle>
                        <DialogDescription>
                          Save your current filters as a preset for quick access
                          later.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="py-4">
                        <Label htmlFor="preset-name" className="mb-2 block">
                          Preset Name
                        </Label>
                        <Input
                          id="preset-name"
                          value={presetName}
                          onChange={(e) => setPresetName(e.target.value)}
                          placeholder="e.g., My Favorite Filters"
                        />
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setShowSavePresetDialog(false)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleSavePreset}>Save Preset</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>

                {presets.length > 0 ? (
                  <div className="max-h-[200px] space-y-2 overflow-y-auto">
                    {presets.map((preset) => (
                      <div
                        key={preset.id}
                        className="flex items-center justify-between rounded-md border p-2 hover:bg-muted/50"
                      >
                        <div
                          className="flex-grow cursor-pointer"
                          onClick={() => applyPreset(preset)}
                        >
                          <div className="font-medium">{preset.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {preset.filters.difficulty !== 'all' &&
                              `${preset.filters.difficulty} • `}
                            {preset.filters.category !== 'all' &&
                              `${preset.filters.category} • `}
                            {preset.filters.tags.length > 0 &&
                              `${preset.filters.tags.length} tags • `}
                            {preset.filters.sortBy}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deletePreset(preset.id)}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="rounded-md border border-dashed p-4 text-center text-sm text-muted-foreground">
                    No saved presets yet. Save your current filters to create
                    one.
                  </div>
                )}
              </div>
            </PopoverContent>
          </Popover>

          {/* My Challenges Button */}
          <Button variant="outline" asChild className="w-full sm:w-auto">
            <Link
              href="/coding-challenges/my-challenges"
              className="flex items-center justify-center gap-2"
            >
              <User className="h-4 w-4" />
              <span>My Challenges</span>
            </Link>
          </Button>
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-2">
            {difficultyFilter !== 'all' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Difficulty: {difficultyFilter}
                <X
                  className="ml-1 h-3 w-3 cursor-pointer"
                  onClick={() => onDifficultyChange?.('all')}
                />
              </Badge>
            )}
            {statusFilter !== 'all' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Status: {statusFilter.replace('_', ' ')}
                <X
                  className="ml-1 h-3 w-3 cursor-pointer"
                  onClick={() => onStatusChange?.('all')}
                />
              </Badge>
            )}
            {sortBy !== 'newest' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Sort: {sortBy.replace('_', ' ')}
                <X
                  className="ml-1 h-3 w-3 cursor-pointer"
                  onClick={() => onSortChange?.('newest')}
                />
              </Badge>
            )}
            {tags.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Tags: {tags.length}
                <X
                  className="ml-1 h-3 w-3 cursor-pointer"
                  onClick={() => onTagsChange?.([])}
                />
              </Badge>
            )}
            {searchTerm && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search:{' '}
                {searchTerm.length > 15
                  ? `${searchTerm.substring(0, 15)}...`
                  : searchTerm}
                <X
                  className="ml-1 h-3 w-3 cursor-pointer"
                  onClick={() => {
                    const event = {
                      target: { value: '' },
                    } as React.ChangeEvent<HTMLInputElement>;
                    onSearchChange?.(event);
                  }}
                />
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleResetFilters}
            className="h-8 px-2 text-xs"
          >
            <RotateCcw className="mr-1 h-3 w-3" />
            Reset Filters
          </Button>
        </div>
      )}

      <Tabs
        defaultValue="all"
        value={activeTab}
        onValueChange={onTabChange}
        className="w-full"
      >
        <div className="overflow-x-auto pb-2">
          <TabsList className="w-full flex-nowrap md:w-auto">
            <TabsTrigger value="all" className="whitespace-nowrap">
              All
            </TabsTrigger>
            <TabsTrigger value="bookmarked" className="whitespace-nowrap">
              <Bookmark className="mr-1 h-4 w-4" />
              Bookmarked
            </TabsTrigger>
            <TabsTrigger value="algorithms" className="whitespace-nowrap">
              Algorithms
            </TabsTrigger>
            <TabsTrigger value="data_structures" className="whitespace-nowrap">
              Data Structures
            </TabsTrigger>
            <TabsTrigger value="system_design" className="whitespace-nowrap">
              System Design
            </TabsTrigger>
            <TabsTrigger value="databases" className="whitespace-nowrap">
              Databases
            </TabsTrigger>
            <TabsTrigger value="web_development" className="whitespace-nowrap">
              Web Dev
            </TabsTrigger>
          </TabsList>
        </div>
      </Tabs>
    </div>
  );
}
