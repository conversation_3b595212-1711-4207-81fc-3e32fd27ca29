/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */
'use client';

import { useCallback, useEffect, useState } from 'react';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';

import { motion } from 'framer-motion';
import { ListChecks } from 'lucide-react';

import { ErrorMessage } from '@/components/LoadingStates';
import { Button } from '@/components/ui/button';
import { useAxiosGet } from '@/hooks/useAxios';
import { useBookmarks } from '@/hooks/useBookmarks';
import { debounce } from '@/lib/utils';

import ActiveBattles from './components/ActiveBattles';
import ChallengeCard from './components/ChallengeCard';
import { ChallengeFilters } from './components/ChallengeFilters';
import ChallengeSkeleton from './components/ChallengeSkeleton';
import EmptyChallenges from './components/EmptyChallenges';
import { IChallenge } from './types';

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges route
 */

// Moved interface and components to separate files

export default function CodingChallengesPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize state from URL parameters
  const [searchTerm, setSearchTerm] = useState(
    searchParams.get('search') || '',
  );
  const [challenges, setChallenges] = useState<IChallenge[]>([]);
  const [page, setPage] = useState(1);
  const [isFetching, setIsFetching] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(
    searchParams.get('category') || 'all',
  );
  const [difficultyFilter, setDifficultyFilter] = useState(
    searchParams.get('difficulty') || 'all',
  );
  const [sortBy, setSortBy] = useState(searchParams.get('sort_by') || 'newest');
  const [tags, setTags] = useState<string[]>(
    searchParams.get('tags')?.split(',') || [],
  );
  const [statusFilter, setStatusFilter] = useState(
    searchParams.get('status') || 'all',
  );

  // Get bookmarks
  const { bookmarks, isLoading: isLoadingBookmarks } = useBookmarks();

  // Update URL with current filters
  const updateUrlParams = useCallback(() => {
    const params = new URLSearchParams();

    if (searchTerm) params.set('search', searchTerm);
    if (activeTab !== 'all') params.set('category', activeTab);
    if (difficultyFilter !== 'all') params.set('difficulty', difficultyFilter);
    if (sortBy !== 'newest') params.set('sort_by', sortBy);
    if (tags.length > 0) params.set('tags', tags.join(','));
    if (statusFilter !== 'all') params.set('status', statusFilter);

    const queryString = params.toString();
    const url = queryString ? `?${queryString}` : '';

    // Update URL without refreshing the page
    router.push(`/coding-challenges${url}`, { scroll: false });
  }, [
    router,
    searchTerm,
    activeTab,
    difficultyFilter,
    sortBy,
    tags,
    statusFilter,
  ]);

  // Handler for tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setPage(1);
    setChallenges([]);
    // Update URL after state change
    setTimeout(() => updateUrlParams(), 0);
  };

  // Use 'any' type to avoid TypeScript errors with the response structure
  const [getChallenges] = useAxiosGet<{ challenges: IChallenge[] }>(
    '/challenges',
  );

  const handleDifficultyChange = (value: string) => {
    setDifficultyFilter(value);
    setPage(1);
    setChallenges([]);
    // Update URL after state change
    setTimeout(() => updateUrlParams(), 0);
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
    setPage(1);
    setChallenges([]);
    // Update URL after state change
    setTimeout(() => updateUrlParams(), 0);
  };

  const handleTagsChange = (selectedTags: string[]) => {
    setTags(selectedTags);
    setPage(1);
    setChallenges([]);
    // Update URL after state change
    setTimeout(() => updateUrlParams(), 0);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    setPage(1);
    setChallenges([]);
    // Update URL after state change
    setTimeout(() => updateUrlParams(), 0);
  };

  const handleResetFilters = () => {
    setSearchTerm('');
    setDifficultyFilter('all');
    setActiveTab('all');
    setSortBy('newest');
    setTags([]);
    setStatusFilter('all');
    setPage(1);
    setChallenges([]);
    // Clear URL params
    router.push('/coding-challenges');
  };

  const fetchChallenges = useCallback(async () => {
    try {
      setIsFetching(true);
      setError(null);

      // Prepare query parameters
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const params: Record<string, any> = {
        page,
      };

      // Handle special filters
      if (activeTab === 'bookmarked') {
        // For bookmarked challenges, we'll filter client-side
        // No need to add a parameter to the API call
      } else if (activeTab !== 'all') {
        // For category filters, add the category parameter
        params.category = activeTab;
      }

      // Only add difficulty filter if not 'all'
      if (difficultyFilter !== 'all') {
        params.difficulty = difficultyFilter.toUpperCase();
      }

      // Only add search term if it's not empty
      if (searchTerm && searchTerm.trim() !== '') {
        params.search = searchTerm.trim();
      }

      // Add sort parameter
      if (sortBy !== 'newest') {
        params.sort_by = sortBy;
      }

      // Add tags filter
      if (tags.length > 0) {
        params.tags = tags.join(',');
      }

      // Add status filter
      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      // Make the API request with the prepared parameters
      const response = await getChallenges({ params });

      // Extract challenges from the response
      let challengesData: IChallenge[] = [];
      let totalPagesData = 0;

      // Check if the response has the expected structure
      if (response.data && response.data.challenges) {
        challengesData = response.data.challenges;
        totalPagesData = response.meta?.totalPages || 1;
        // Challenges found successfully
      } else {
        setError('Failed to load challenges. Please try again later.');
      }

      // Process the challenges data

      // Filter for bookmarked challenges if needed
      if (activeTab === 'bookmarked' && bookmarks.length > 0) {
        challengesData = challengesData.filter((challenge) =>
          bookmarks.includes(challenge.id),
        );
      }

      setChallenges((prevChallenges) => {
        // If it's the first page, replace challenges; otherwise, append to existing
        return page === 1
          ? challengesData
          : [...prevChallenges, ...challengesData];
      });

      setHasMore(page < totalPagesData);
      setIsFetching(false);
    } catch (error) {
      setIsFetching(false);
      setError((error as { message: string }).message);
    }
  }, [
    page,
    activeTab,
    difficultyFilter,
    searchTerm,
    sortBy,
    tags,
    statusFilter,
    bookmarks,
  ]);

  // Create a debounced search handler using the utility function
  const debouncedSearch = useCallback(
    debounce(() => {
      // Reset to first page and clear current challenges when searching
      setPage(1);
      setChallenges([]);
      fetchChallenges();
    }, 500),
    [fetchChallenges],
  );

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    debouncedSearch();
    // Update URL after state change
    setTimeout(() => updateUrlParams(), 0);
  };

  useEffect(() => {
    fetchChallenges();
  }, [fetchChallenges]);

  // Removed debug logging

  useEffect(() => {
    const handleScroll = () => {
      if (
        window.innerHeight + window.scrollY >=
          document.body.offsetHeight - 500 &&
        hasMore &&
        !isFetching &&
        !error
      ) {
        setPage((prevPage) => prevPage + 1);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, isFetching, error]);

  return (
    <div className="container mx-auto p-4 pb-20">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8 text-center"
      >
        <h1 className="mb-2 text-4xl font-bold">Coding Challenges</h1>
        <p className="mx-auto max-w-2xl text-muted-foreground">
          Sharpen your programming skills with our collection of coding
          challenges across various difficulty levels and topics.
        </p>
        <div className="mt-4 flex justify-center">
          <Button variant="outline" asChild>
            <Link
              href="/coding-challenges/collections"
              className="flex items-center gap-2"
            >
              <ListChecks className="h-4 w-4" />
              Browse Challenge Collections
            </Link>
          </Button>
        </div>
      </motion.div>

      <ChallengeFilters
        searchTerm={searchTerm}
        onSearchChange={handleSearch}
        difficultyFilter={difficultyFilter}
        onDifficultyChange={handleDifficultyChange}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        sortBy={sortBy}
        onSortChange={handleSortChange}
        tags={tags}
        onTagsChange={handleTagsChange}
        statusFilter={statusFilter}
        onStatusChange={handleStatusChange}
        onResetFilters={handleResetFilters}
      />

      {error && (
        <ErrorMessage
          title="Failed to load challenges"
          message={error}
          onRetry={() => fetchChallenges()}
          className="mb-6"
        />
      )}

      {/* Active Battles Section */}
      <ActiveBattles className="mb-12" />

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {isFetching && challenges.length === 0 ? (
          // Show skeletons when initially loading
          Array(6)
            .fill(0)
            .map((_, index) => <ChallengeSkeleton key={index} />)
        ) : challenges.length > 0 ? (
          challenges.map((challenge) => (
            <ChallengeCard key={challenge.id} challenge={challenge} />
          ))
        ) : (
          <EmptyChallenges
            title={
              activeTab === 'bookmarked'
                ? 'No bookmarked challenges'
                : 'No challenges found'
            }
            description={
              activeTab === 'bookmarked'
                ? 'Bookmark challenges to see them here'
                : 'Try adjusting your search or filter criteria'
            }
            actionText={
              activeTab === 'bookmarked'
                ? 'Explore All Challenges'
                : 'Reset Filters'
            }
            actionHref="/coding-challenges?reset=true"
          />
        )}
      </div>

      {isFetching && challenges.length > 0 && (
        <div className="mt-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array(3)
            .fill(0)
            .map((_, index) => (
              <ChallengeSkeleton key={`loading-${index}`} />
            ))}
        </div>
      )}

      {hasMore && !isFetching && challenges.length > 0 && (
        <div className="mt-8 text-center">
          <Button
            variant="outline"
            onClick={() => setPage((prev) => prev + 1)}
            className="mx-auto"
          >
            Load More Challenges
          </Button>
        </div>
      )}
    </div>
  );
}
