/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */
'use client';

import { useEffect, useState } from 'react';

import Image from 'next/image';
import Link from 'next/link';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';

import { motion } from 'framer-motion';
import {
  ArrowLef<PERSON>,
  BarChart,
  BookOpen,
  Briefcase,
  CheckCircle2,
  Clock,
  ListChecks,
  Trophy,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCollection } from '@/hooks/useCollections';

import ChallengeCard from '../../components/ChallengeCard';
import { IChallenge } from '../../types';

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections/[id] route
 */

export default function CollectionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;
  const [activeTab, setActiveTab] = useState<
    'all' | 'completed' | 'incomplete'
  >('all');
  const [filteredChallenges, setFilteredChallenges] = useState<IChallenge[]>(
    [],
  );

  // Get collection
  const { collection, isLoading, error } = useCollection(id);

  // Filter challenges based on active tab
  useEffect(() => {
    if (!collection || !collection.challenges) {
      setFilteredChallenges([]);
      return;
    }

    switch (activeTab) {
      case 'completed':
        setFilteredChallenges(
          collection.challenges.filter(
            (challenge) =>
              collection.progress?.completed > 0 && Math.random() > 0.5, // Mock completion status for demo
          ),
        );
        break;
      case 'incomplete':
        setFilteredChallenges(
          collection.challenges.filter(
            (challenge) =>
              collection.progress?.completed < collection.progress?.total &&
              Math.random() <= 0.5, // Mock completion status for demo
          ),
        );
        break;
      default:
        setFilteredChallenges(collection.challenges);
    }
  }, [activeTab, collection]);

  // Get type icon
  const getTypeIcon = () => {
    if (!collection) return null;

    switch (collection.type) {
      case 'company':
        return <Briefcase className="h-5 w-5" />;
      case 'topic':
        return <BookOpen className="h-5 w-5" />;
      case 'difficulty':
        return <BarChart className="h-5 w-5" />;
      case 'series':
        return <ListChecks className="h-5 w-5" />;
      default:
        return <BookOpen className="h-5 w-5" />;
    }
  };

  // Get type label
  const getTypeLabel = () => {
    if (!collection) return 'Collection';

    switch (collection.type) {
      case 'company':
        return 'Company Collection';
      case 'topic':
        return 'Topic Collection';
      case 'difficulty':
        return 'Difficulty Collection';
      case 'series':
        return 'Challenge Series';
      default:
        return 'Collection';
    }
  };

  // Loading skeleton
  if (isLoading) {
    return (
      <div className="container py-8">
        <div className="mb-4">
          <Skeleton className="h-10 w-32" />
        </div>

        <div className="mb-8 space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-4 w-full max-w-2xl" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-6 w-24" />
          </div>
        </div>

        <div className="mb-6">
          <Skeleton className="h-10 w-64" />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container py-8">
        <Button variant="ghost" className="mb-8" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Collections
        </Button>

        <div className="border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center">
          <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
            Failed to load collection
          </h2>
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  if (!collection) {
    return (
      <div className="container py-8">
        <Button variant="ghost" className="mb-8" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Collections
        </Button>

        <div className="rounded-lg border border-dashed p-8 text-center">
          <h2 className="text-lg font-medium">Collection not found</h2>
          <p className="mt-2 text-sm text-muted-foreground">
            The collection you're looking for doesn't exist or has been removed.
          </p>
          <Button
            className="mt-4"
            onClick={() => router.push('/coding-challenges/collections')}
          >
            View All Collections
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <Link href="/coding-challenges/collections" className="inline-block">
        <Button variant="ghost" className="mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Collections
        </Button>
      </Link>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="mb-4 flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1 text-sm">
            {getTypeIcon()}
            <span>{getTypeLabel()}</span>
          </Badge>

          {collection.company && (
            <Badge
              variant="secondary"
              className="flex items-center gap-1 text-sm"
            >
              {collection.company.logo_url ? (
                <Image
                  src={collection.company.logo_url}
                  alt={collection.company.name}
                  width={16}
                  height={16}
                  className="h-4 w-4 rounded-full"
                />
              ) : (
                <Briefcase className="h-4 w-4" />
              )}
              <span>{collection.company.name}</span>
            </Badge>
          )}
        </div>

        <h1 className="mb-2 text-3xl font-bold tracking-tight md:text-4xl">
          {collection.title}
        </h1>

        <p className="mb-4 text-muted-foreground">{collection.description}</p>

        {collection.progress && (
          <div className="mb-4 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <span>
                  {collection.progress.completed}/{collection.progress.total}{' '}
                  Challenges Completed
                </span>
              </div>
              <span className="font-medium">
                {collection.progress.percentage}% Complete
              </span>
            </div>
            <Progress
              value={collection.progress.percentage}
              className="h-2"
              aria-label={`${collection.progress.percentage}% completed`}
            />
          </div>
        )}

        <div className="flex flex-wrap gap-3">
          <div className="flex items-center rounded-md bg-muted px-3 py-1 text-sm">
            <Trophy className="mr-1.5 h-4 w-4 text-yellow-500" />
            <span>{collection.challenge_count} Challenges</span>
          </div>

          <div className="flex items-center rounded-md bg-muted px-3 py-1 text-sm">
            <Clock className="mr-1.5 h-4 w-4 text-blue-500" />
            <span>
              Updated {new Date(collection.updated_at).toLocaleDateString()}
            </span>
          </div>
        </div>
      </motion.div>

      <div className="mb-6">
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as any)}
          className="w-full sm:w-auto"
        >
          <TabsList className="grid w-full grid-cols-3 sm:w-auto">
            <TabsTrigger value="all">All Challenges</TabsTrigger>
            <TabsTrigger value="completed" className="flex items-center gap-1">
              <CheckCircle2 className="h-4 w-4" />
              <span>Completed</span>
            </TabsTrigger>
            <TabsTrigger value="incomplete" className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>Incomplete</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {filteredChallenges.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
        >
          {filteredChallenges.map((challenge) => (
            <ChallengeCard
              key={challenge.id}
              challenge={challenge}
              showProgress={true}
            />
          ))}
        </motion.div>
      ) : (
        <div className="rounded-lg border border-dashed p-8 text-center">
          <h3 className="text-lg font-medium">No challenges found</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {activeTab === 'completed'
              ? "You haven't completed any challenges in this collection yet."
              : activeTab === 'incomplete'
                ? "You've completed all challenges in this collection!"
                : "This collection doesn't have any challenges yet."}
          </p>
        </div>
      )}
    </div>
  );
}
