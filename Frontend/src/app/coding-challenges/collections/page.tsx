/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */
'use client';

import { useEffect, useState } from 'react';

import { useSearchParams } from 'next/navigation';

import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  BookO<PERSON>,
  Briefcase,
  ListChe<PERSON>,
  Search,
} from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useCollections } from '@/hooks/useCollections';

import { ICollection } from '../types';
import CollectionCard from './components/CollectionCard';

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/collections route
 */

export default function CollectionsPage() {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCollections, setFilteredCollections] = useState<ICollection[]>(
    [],
  );

  // Get collections
  const {
    collections,
    isLoading,
    error,
    getCompanyCollections,
    getTopicCollections,
    getDifficultyCollections,
    getSeriesCollections,
  } = useCollections();

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Filter collections based on active tab and search term
  useEffect(() => {
    let filtered: ICollection[] = [];

    // Filter by type
    switch (activeTab) {
      case 'company':
        filtered = getCompanyCollections();
        break;
      case 'topic':
        filtered = getTopicCollections();
        break;
      case 'difficulty':
        filtered = getDifficultyCollections();
        break;
      case 'series':
        filtered = getSeriesCollections();
        break;
      default:
        filtered = collections;
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (collection) =>
          collection.title.toLowerCase().includes(term) ||
          collection.description.toLowerCase().includes(term) ||
          (collection.company &&
            collection.company.name.toLowerCase().includes(term)),
      );
    }

    setFilteredCollections(filtered);
  }, [
    activeTab,
    searchTerm,
    collections,
    getCompanyCollections,
    getTopicCollections,
    getDifficultyCollections,
    getSeriesCollections,
  ]);

  // Set initial tab from URL
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (
      tab &&
      ['all', 'company', 'topic', 'difficulty', 'series'].includes(tab)
    ) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Loading skeleton
  if (isLoading) {
    return (
      <div className="container py-8">
        <div className="mb-8 space-y-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-full max-w-2xl" />
        </div>

        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-full sm:w-64" />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container py-8">
        <div className="border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center">
          <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
            Failed to load collections
          </h2>
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <h1 className="mb-2 text-3xl font-bold tracking-tight md:text-4xl">
          Challenge Collections
        </h1>
        <p className="text-muted-foreground">
          Explore curated collections of coding challenges grouped by companies,
          topics, and more.
        </p>
      </motion.div>

      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full sm:w-auto"
        >
          <TabsList className="grid w-full grid-cols-5 sm:w-auto sm:grid-cols-5">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="company" className="flex items-center gap-1">
              <Briefcase className="h-4 w-4" />
              <span className="hidden sm:inline">Companies</span>
            </TabsTrigger>
            <TabsTrigger value="topic" className="flex items-center gap-1">
              <BookOpen className="h-4 w-4" />
              <span className="hidden sm:inline">Topics</span>
            </TabsTrigger>
            <TabsTrigger value="difficulty" className="flex items-center gap-1">
              <BarChart className="h-4 w-4" />
              <span className="hidden sm:inline">Difficulty</span>
            </TabsTrigger>
            <TabsTrigger value="series" className="flex items-center gap-1">
              <ListChecks className="h-4 w-4" />
              <span className="hidden sm:inline">Series</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search collections..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {filteredCollections.length > 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
        >
          {filteredCollections.map((collection) => (
            <CollectionCard key={collection.id} collection={collection} />
          ))}
        </motion.div>
      ) : (
        <div className="rounded-lg border border-dashed p-8 text-center">
          <h3 className="text-lg font-medium">No collections found</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            {searchTerm
              ? `No collections matching "${searchTerm}"`
              : `No ${activeTab !== 'all' ? activeTab : ''} collections available`}
          </p>
        </div>
      )}
    </div>
  );
}
