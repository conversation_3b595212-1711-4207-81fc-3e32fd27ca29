/**
 * @file index.tsx
 * @description React component for CollectionCard
 */
'use client';

import Image from 'next/image';
import Link from 'next/link';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>O<PERSON>,
  Briefcase,
  ListChecks,
} from 'lucide-react';

import { ICollection } from '@/app/coding-challenges/types';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

/**
 * @file index.tsx
 * @description React component for CollectionCard
 */

interface CollectionCardProps {
  collection: ICollection;
  className?: string;
}

export default function CollectionCard({
  collection,
  className,
}: CollectionCardProps) {
  // Get icon based on collection type
  const getTypeIcon = () => {
    switch (collection.type) {
      case 'company':
        return <Briefcase className="h-4 w-4" />;
      case 'topic':
        return <BookOpen className="h-4 w-4" />;
      case 'difficulty':
        return <BarChart className="h-4 w-4" />;
      case 'series':
        return <ListChecks className="h-4 w-4" />;
      default:
        return <BookOpen className="h-4 w-4" />;
    }
  };

  // Get type label
  const getTypeLabel = () => {
    switch (collection.type) {
      case 'company':
        return 'Company';
      case 'topic':
        return 'Topic';
      case 'difficulty':
        return 'Difficulty';
      case 'series':
        return 'Series';
      default:
        return 'Collection';
    }
  };

  return (
    <Link href={`/coding-challenges/collections/${collection.id}`}>
      <Card
        className={cn(
          'hover:border-primary/50 h-full transition-all hover:shadow-md',
          className,
        )}
      >
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {collection.company && (
                <div className="mb-2 flex items-center">
                  {collection.company.logo_url ? (
                    <div className="mr-2 h-6 w-6 overflow-hidden rounded-full">
                      <Image
                        src={collection.company.logo_url}
                        alt={collection.company.name}
                        width={24}
                        height={24}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ) : (
                    <Briefcase className="mr-2 h-5 w-5 text-muted-foreground" />
                  )}
                  <span className="text-sm font-medium text-muted-foreground">
                    {collection.company.name}
                  </span>
                </div>
              )}
              <CardTitle className="line-clamp-1 text-xl font-bold">
                {collection.title}
              </CardTitle>
            </div>
            <Badge
              variant="outline"
              className="flex items-center gap-1 whitespace-nowrap"
            >
              {getTypeIcon()}
              <span>{getTypeLabel()}</span>
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="pb-2">
          <p className="line-clamp-2 text-sm text-muted-foreground">
            {collection.description}
          </p>

          <div className="mt-4 flex items-center justify-between text-sm">
            <span className="font-medium">
              {collection.challenge_count} Challenges
            </span>
            {collection.progress && (
              <span className="text-muted-foreground">
                {collection.progress.completed}/{collection.progress.total}{' '}
                Completed
              </span>
            )}
          </div>

          {collection.progress && (
            <Progress
              value={collection.progress.percentage}
              className="mt-2 h-2"
              aria-label={`${collection.progress.percentage}% completed`}
            />
          )}
        </CardContent>

        <CardFooter className="pt-2">
          <div className="flex w-full items-center justify-between text-sm">
            <span className="text-muted-foreground">
              Updated {new Date(collection.updated_at).toLocaleDateString()}
            </span>
            <span className="flex items-center font-medium text-primary">
              View Collection
              <ArrowRight className="ml-1 h-4 w-4" />
            </span>
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
}
