/**
 * @file metadata.ts
 * @description Next.js page for coding-challenges/[id] route
 */
import { Metadata } from 'next';

import { getChallengeById } from '@/lib/api/challenges';

// Generate metadata for the challenge page
export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  try {
    // Fetch challenge data
    const challenge = await getChallengeById(params.id);

    if (!challenge) {
      return {
        title: 'Challenge Not Found',
        description: 'The challenge you are looking for does not exist.',
      };
    }

    // Create a description that includes difficulty and points
    const description = `A ${challenge.difficulty.toLowerCase()} level coding challenge worth ${challenge.points} points. ${challenge.description.substring(0, 150)}${challenge.description.length > 150 ? '...' : ''}`;

    return {
      title: `${challenge.title} | Coding Challenge`,
      description,
      keywords: [
        'coding challenge',
        'programming',
        challenge.difficulty.toLowerCase(),
        ...(challenge.tags || []),
      ],
      openGraph: {
        title: `${challenge.title} | Coding Challenge`,
        description,
        type: 'article',
        url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://mrengineer.io'}/coding-challenges/${params.id}`,
        images: [
          {
            url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://mrengineer.io'}/coding-challenges/${params.id}/opengraph-image`,
            width: 1200,
            height: 630,
            alt: challenge.title,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: `${challenge.title} | Coding Challenge`,
        description,
        images: [
          `${process.env.NEXT_PUBLIC_SITE_URL || 'https://mrengineer.io'}/coding-challenges/${params.id}/opengraph-image`,
        ],
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);

    // Return default metadata if there's an error
    return {
      title: 'Coding Challenge',
      description:
        'Sharpen your programming skills with our coding challenges.',
    };
  }
}
