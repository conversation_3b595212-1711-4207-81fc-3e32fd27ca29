/**
 * @file opengraph-image.tsx
 * @description Next.js page for coding-challenges/[id] route
 */
import { ImageResponse } from 'next/og';

import { getChallengeById } from '@/lib/api/challenges';

// Route segment config
export const runtime = 'edge';

// Image metadata
export const alt = 'Coding Challenge';
export const size = {
  width: 1200,
  height: 630,
};

export const contentType = 'image/png';

// Image generation
export default async function Image({ params }: { params: { id: string } }) {
  try {
    // Fetch challenge data
    const challenge = await getChallengeById(params.id);

    if (!challenge) {
      return new ImageResponse(
        (
          <div
            style={{
              fontSize: 48,
              background: 'linear-gradient(to bottom, #1a202c, #2d3748)',
              color: 'white',
              width: '100%',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: 48,
            }}
          >
            <div style={{ fontSize: 64, fontWeight: 'bold', marginBottom: 24 }}>
              Challenge Not Found
            </div>
            <div style={{ fontSize: 36, opacity: 0.8 }}>
              The challenge you're looking for doesn't exist
            </div>
          </div>
        ),
        { ...size },
      );
    }

    // Determine difficulty color
    const difficultyColors = {
      EASY: '#48BB78',
      MEDIUM: '#ECC94B',
      HARD: '#F56565',
    };

    const difficultyColor =
      difficultyColors[challenge.difficulty as keyof typeof difficultyColors] ||
      '#A0AEC0';

    return new ImageResponse(
      (
        <div
          style={{
            fontSize: 48,
            background: 'linear-gradient(to bottom, #1a202c, #2d3748)',
            color: 'white',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 48,
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 24,
            }}
          >
            <div
              style={{
                backgroundColor: difficultyColor,
                color: '#1A202C',
                padding: '8px 16px',
                borderRadius: 9999,
                fontSize: 32,
                fontWeight: 'bold',
                textTransform: 'uppercase',
              }}
            >
              {challenge.difficulty}
            </div>
            <div
              style={{
                marginLeft: 16,
                backgroundColor: '#4A5568',
                color: 'white',
                padding: '8px 16px',
                borderRadius: 9999,
                fontSize: 32,
                display: 'flex',
                alignItems: 'center',
                gap: 8,
              }}
            >
              <span>🏆</span>
              <span>{challenge.points} points</span>
            </div>
          </div>

          <div
            style={{
              fontSize: 64,
              fontWeight: 'bold',
              marginBottom: 24,
              textAlign: 'center',
              maxWidth: '80%',
            }}
          >
            {challenge.title}
          </div>

          <div
            style={{
              fontSize: 32,
              opacity: 0.8,
              textAlign: 'center',
              maxWidth: '80%',
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              marginBottom: 32,
            }}
          >
            {challenge.description.substring(0, 200)}
            {challenge.description.length > 200 ? '...' : ''}
          </div>

          <div
            style={{
              display: 'flex',
              gap: 16,
              marginTop: 'auto',
            }}
          >
            {challenge.tags &&
              challenge.tags.slice(0, 5).map((tag: string, i: number) => (
                <div
                  key={i}
                  style={{
                    backgroundColor: '#4A5568',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: 9999,
                    fontSize: 24,
                  }}
                >
                  #{tag}
                </div>
              ))}
          </div>

          <div
            style={{
              marginTop: 48,
              fontSize: 24,
              opacity: 0.6,
            }}
          >
            MrEngineer • Coding Challenges
          </div>
        </div>
      ),
      { ...size },
    );
  } catch (error) {
    console.error('Error generating OG image:', error);

    // Return a fallback image
    return new ImageResponse(
      (
        <div
          style={{
            fontSize: 48,
            background: 'linear-gradient(to bottom, #1a202c, #2d3748)',
            color: 'white',
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: 48,
          }}
        >
          <div style={{ fontSize: 64, fontWeight: 'bold', marginBottom: 24 }}>
            Coding Challenge
          </div>
          <div style={{ fontSize: 36, opacity: 0.8 }}>
            Sharpen your programming skills with our coding challenges
          </div>
        </div>
      ),
      { ...size },
    );
  }
}
