/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */
'use client';

import { useEffect, useState } from 'react';

import Image from 'next/image';
import Link from 'next/link';

import { motion } from 'framer-motion';
import {
  ArrowRight,
  BookOpen,
  CheckCircle2,
  Map,
  Route,
  Users,
} from 'lucide-react';

import { IRoadmap } from '@/app/coding-challenges/types/roadmap';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useAxiosGet } from '@/hooks/useAxios';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

/**
 * @file index.tsx
 * @description React component for RelatedRoadmaps
 */

interface RelatedRoadmapsProps {
  challengeId: string;
  className?: string;
}

export default function RelatedRoadmaps({
  challengeId,
  className = '',
}: RelatedRoadmapsProps) {
  const [relatedRoadmaps, setRelatedRoadmaps] = useState<IRoadmap[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getRelatedRoadmaps] = useAxiosGet<{ roadmaps: IRoadmap[] }>(
    `/challenges/${challengeId}/roadmaps`,
  );

  // Fetch related roadmaps
  useEffect(() => {
    const fetchRelatedRoadmaps = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await getRelatedRoadmaps();
        if (response.data && response.data.roadmaps) {
          setRelatedRoadmaps(response.data.roadmaps);
        }
      } catch (err) {
        console.error('Error fetching related roadmaps:', err);
        setError('Failed to load related roadmaps');

        // Use mock data for development
        if (process.env.NODE_ENV === 'development') {
          setRelatedRoadmaps(getMockRelatedRoadmaps());
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchRelatedRoadmaps();
  }, [challengeId, getRelatedRoadmaps]);

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-full max-w-md" />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {[1, 2].map((i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={cn(
          'border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center',
          className,
        )}
      >
        <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
          Failed to load related roadmaps
        </h2>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  // No related roadmaps
  if (relatedRoadmaps.length === 0) {
    return null;
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div className="space-y-2">
        <h2 className="flex items-center text-2xl font-bold">
          <Route className="mr-2 h-6 w-6 text-blue-500" />
          Related Learning Paths
        </h2>
        <p className="text-muted-foreground">
          This challenge is part of the following learning paths
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {relatedRoadmaps.map((roadmap, index) => (
          <motion.div
            key={roadmap.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="h-full transition-all hover:shadow-md">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="line-clamp-1 text-xl font-bold">
                      {roadmap.title}
                    </CardTitle>
                    <CardDescription className="line-clamp-2">
                      {roadmap.description}
                    </CardDescription>
                  </div>
                  {roadmap.image_url && (
                    <div className="ml-4 h-12 w-12 overflow-hidden rounded-md">
                      <Image
                        src={roadmap.image_url}
                        alt={roadmap.title}
                        width={48}
                        height={48}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="pb-2">
                <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <BookOpen className="mr-1 h-4 w-4" />
                    <span>{roadmap.challenges_count} challenges</span>
                  </div>

                  {roadmap.enrolled_count && (
                    <div className="flex items-center">
                      <Users className="mr-1 h-4 w-4" />
                      <span>
                        {roadmap.enrolled_count.toLocaleString()} enrolled
                      </span>
                    </div>
                  )}
                </div>

                {roadmap.progress && (
                  <div className="mt-4 space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span>Your progress</span>
                      <span>
                        <CheckCircle2 className="mr-1 inline-block h-3 w-3 text-green-500" />
                        {roadmap.progress.completed}/{roadmap.progress.total}{' '}
                        completed
                      </span>
                    </div>
                    <Progress
                      value={roadmap.progress.percentage}
                      className="h-1.5"
                    />
                  </div>
                )}
              </CardContent>

              <CardFooter>
                <Button asChild variant="outline" className="w-full">
                  <Link
                    href={`/roadmaps/${roadmap.id}`}
                    className="flex items-center justify-center"
                  >
                    <Map className="mr-2 h-4 w-4" />
                    View Learning Path
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  );
}

// Mock data for development
function getMockRelatedRoadmaps(): IRoadmap[] {
  return [
    {
      id: 'roadmap1',
      title: 'Data Structures and Algorithms',
      description: 'Master the fundamentals of data structures and algorithms',
      image_url: 'https://example.com/roadmap1.jpg',
      topics: [],
      progress: {
        completed: 3,
        total: 10,
        percentage: 30,
      },
      challenges_count: 10,
      enrolled_count: 1250,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 'roadmap2',
      title: 'Frontend Development Mastery',
      description:
        'Learn modern frontend development with React, TypeScript and more',
      image_url: 'https://example.com/roadmap2.jpg',
      topics: [],
      progress: {
        completed: 5,
        total: 15,
        percentage: 33.33,
      },
      challenges_count: 15,
      enrolled_count: 980,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ];
}
