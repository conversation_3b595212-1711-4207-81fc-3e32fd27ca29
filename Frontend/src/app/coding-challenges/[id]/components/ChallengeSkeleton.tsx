/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */
'use client';

import { SkeletonLoader } from '@/components/LoadingStates';
import { Card } from '@/components/ui/card';

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

/**
 * @file ChallengeSkeleton.tsx
 * @description React component for ChallengeSkeleton
 */

export default function ChallengeSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <SkeletonLoader type="detail" />
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-5">
        <div className="lg:col-span-3">
          <Card className="overflow-hidden border-none p-6 shadow-lg">
            <SkeletonLoader type="detail" />
          </Card>
        </div>

        <div className="lg:col-span-2">
          <Card className="overflow-hidden border-none p-6 shadow-lg">
            <SkeletonLoader type="detail" />
          </Card>
        </div>
      </div>
    </div>
  );
}
