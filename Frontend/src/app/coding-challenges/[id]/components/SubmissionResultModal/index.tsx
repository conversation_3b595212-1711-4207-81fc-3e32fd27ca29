/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */
'use client';

import { useRef } from 'react';

import {
  Award,
  BarChart,
  CheckCircle,
  Clock,
  Database,
  XCircle,
  Zap,
} from 'lucide-react';

import ExecutionMetrics, {
  ExecutionMetric,
} from '@/components/ExecutionMetrics';
import OptimizationSuggestions, {
  OptimizationTip,
} from '@/components/OptimizationSuggestions';
import SolutionComparison, { Solution } from '@/components/SolutionComparison';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { useFocusTrap } from '@/hooks/useFocusTrap';
import { getAriaLabelProps } from '@/utils/accessibility';

import TestCases from '../TestCases';

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

/**
 * @file index.tsx
 * @description React component for SubmissionResultModal
 */

interface SubmissionResult {
  id: string;
  status: string;
  runtime_ms: number;
  memory_used_kb: number;
  feedback: string;
  score: number;
  created_at: string;
  test_results?: {
    passed: boolean;
    name?: string;
    input?: string;
    expected_output?: string;
    actual_output?: string;
    error?: string;
    is_hidden?: boolean;
  }[];
}

interface SubmissionResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  result: SubmissionResult | null;
  language?: string;
  similarSolutions?: Solution[];
  optimizationTips?: OptimizationTip[];
  showMetrics?: boolean;
  showComparison?: boolean;
  showOptimizationTips?: boolean;
  onViewSolution?: (solutionId: string) => void;
}

export default function SubmissionResultModal({
  isOpen,
  onClose,
  result,
  language = 'javascript',
  similarSolutions = [],
  optimizationTips = [],
  showMetrics = true,
  showComparison = true,
  showOptimizationTips = true,
  onViewSolution,
}: SubmissionResultModalProps) {
  const isAccepted = result?.status === 'accepted';
  const formattedDate = new Date(result?.created_at ?? '').toLocaleString();

  // Calculate test case statistics
  const testResults = result?.test_results || [];
  const totalTests = testResults.length;
  const passedTests = testResults.filter((test) => test.passed).length;
  const passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

  // Create refs for focus management
  const closeButtonRef = useRef<HTMLButtonElement>(null);
  const dialogRef = useFocusTrap(isOpen, closeButtonRef);

  // Generate unique IDs for accessibility
  // const dialogId = 'submission-result-modal';
  const titleId = 'submission-result-title';
  const descriptionId = 'submission-result-description';

  if (!result) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-3xl"
        ref={dialogRef}
        aria-labelledby={titleId}
        aria-describedby={descriptionId}
      >
        <DialogHeader>
          <DialogTitle id={titleId} className="flex items-center">
            {isAccepted ? (
              <CheckCircle
                className="mr-2 h-5 w-5 text-green-500"
                aria-hidden="true"
              />
            ) : (
              <XCircle
                className="text-red-500 mr-2 h-5 w-5"
                aria-hidden="true"
              />
            )}
            Submission Result: {isAccepted ? 'Success' : 'Failed'}
          </DialogTitle>
          <DialogDescription id={descriptionId}>
            Submitted on {formattedDate}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div
            className="flex items-center justify-between"
            role="group"
            aria-labelledby="status-label"
          >
            <span id="status-label" className="text-sm font-medium">
              Status:
            </span>
            <Badge
              variant={isAccepted ? 'default' : 'destructive'}
              className="capitalize"
            >
              {result.status.replace('_', ' ')}
            </Badge>
          </div>

          <div
            className="flex items-center justify-between"
            role="group"
            aria-labelledby="runtime-label"
          >
            <span id="runtime-label" className="text-sm font-medium">
              Runtime:
            </span>
            <div className="flex items-center">
              <Clock
                className="mr-1 h-4 w-4 text-blue-500"
                aria-hidden="true"
              />
              <span>{result.runtime_ms} ms</span>
            </div>
          </div>

          <div
            className="flex items-center justify-between"
            role="group"
            aria-labelledby="memory-label"
          >
            <span id="memory-label" className="text-sm font-medium">
              Memory:
            </span>
            <div className="flex items-center">
              <Database
                className="mr-1 h-4 w-4 text-purple-500"
                aria-hidden="true"
              />
              <span>{(result.memory_used_kb / 1024).toFixed(2)} MB</span>
            </div>
          </div>

          <div
            className="flex items-center justify-between"
            role="group"
            aria-labelledby="score-label"
          >
            <span id="score-label" className="text-sm font-medium">
              Score:
            </span>
            <div className="flex items-center">
              <Award
                className="mr-1 h-4 w-4 text-yellow-500"
                aria-hidden="true"
              />
              <span>{result.score} points</span>
            </div>
          </div>

          {totalTests > 0 && (
            <div
              className="space-y-2"
              role="group"
              aria-labelledby="test-cases-label"
            >
              <div className="flex items-center justify-between">
                <span id="test-cases-label" className="text-sm font-medium">
                  Test Cases:
                </span>
                <span aria-live="polite">
                  {passedTests}/{totalTests} passed
                </span>
              </div>
              <Progress
                value={passRate}
                className="h-2"
                aria-valuemin={0}
                aria-valuemax={100}
                aria-valuenow={Math.round(passRate)}
                aria-label={`${Math.round(passRate)}% of tests passed`}
              />
            </div>
          )}

          {result.feedback && (
            <div
              className="space-y-2"
              role="group"
              aria-labelledby="feedback-label"
            >
              <span id="feedback-label" className="text-sm font-medium">
                Feedback:
              </span>
              <div
                className="rounded-md bg-muted p-3 text-sm"
                aria-live="polite"
              >
                {result.feedback}
              </div>
            </div>
          )}

          {/* Execution Metrics */}
          {showMetrics && (
            <ExecutionMetrics
              runtime={{
                value: result.runtime_ms,
                unit: 'ms',
                percentile: Math.min(Math.round(Math.random() * 100), 100), // Mock percentile for demo
                ranking:
                  result.runtime_ms < 50
                    ? 'excellent'
                    : result.runtime_ms < 100
                      ? 'good'
                      : result.runtime_ms < 200
                        ? 'average'
                        : 'poor',
              }}
              memory={{
                value: parseFloat((result.memory_used_kb / 1024).toFixed(2)),
                unit: 'MB',
                percentile: Math.min(Math.round(Math.random() * 100), 100), // Mock percentile for demo
                ranking:
                  result.memory_used_kb < 5000
                    ? 'excellent'
                    : result.memory_used_kb < 10000
                      ? 'good'
                      : result.memory_used_kb < 15000
                        ? 'average'
                        : 'poor',
              }}
              language={language}
              showComparison={true}
              showOptimizationTips={true}
              className="mt-4"
            />
          )}

          {testResults.length > 0 && (
            <div
              className="space-y-2"
              role="group"
              aria-labelledby="test-results-label"
            >
              <span id="test-results-label" className="text-sm font-medium">
                Test Results:
              </span>
              <div aria-live="polite">
                <TestCases
                  challengeId={result.id.split('-')[0]}
                  submissionResults={testResults}
                  isSubmissionView={true}
                />
              </div>
            </div>
          )}

          {/* Solution Comparison */}
          {showComparison && similarSolutions.length > 0 && (
            <div className="mt-6">
              <SolutionComparison
                solutions={similarSolutions}
                currentSolutionId={result.id}
                onViewSolution={onViewSolution}
              />
            </div>
          )}

          {/* Optimization Suggestions */}
          {showOptimizationTips && optimizationTips.length > 0 && (
            <div className="mt-6">
              <OptimizationSuggestions
                tips={optimizationTips}
                language={language}
              />
            </div>
          )}
        </div>

        <DialogFooter className="sm:justify-center">
          <Button
            onClick={onClose}
            className="w-full"
            ref={closeButtonRef}
            {...getAriaLabelProps('Close submission result dialog')}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
