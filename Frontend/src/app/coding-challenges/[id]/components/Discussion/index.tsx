/**
 * @file index.tsx
 * @description React component for Discussion
 */
'use client';

import { useEffect, useState } from 'react';

import { MessageSquare } from 'lucide-react';
import { toast } from 'sonner';

import { ErrorMessage } from '@/components/LoadingStates';
import { Button } from '@/components/ui/button';
import { useAxiosGet, useAxiosPost, useAxiosPut } from '@/hooks/useAxios';

import DiscussionForm from './DiscussionForm';
import DiscussionItem, {
  Discussion,
  DiscussionItemSkeleton,
} from './DiscussionItem';

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

/**
 * @file index.tsx
 * @description React component for Discussion
 */

interface DiscussionProps {
  challengeId: string;
  currentUserId?: string;
}

export default function DiscussionSection({
  challengeId,
  currentUserId,
}: DiscussionProps) {
  const [discussions, setDiscussions] = useState<Discussion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'newest' | 'popular'>('newest');

  // API hooks
  const [getDiscussions] = useAxiosGet<{ discussions: Discussion[] }>(
    `/challenges/${challengeId}/discussions`,
  );
  const [createDiscussion] = useAxiosPost<{ discussion: Discussion }>(
    `/challenges/${challengeId}/discussions`,
  );
  const [createReply] = useAxiosPost<{ reply: Discussion }>(
    `/challenges/${challengeId}/discussions/:id/replies`,
  );
  const [updateVotes] = useAxiosPut<{ success: boolean }>(
    `/challenges/${challengeId}/discussions/:id/votes`,
  );

  // Fetch discussions
  const fetchDiscussions = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getDiscussions({
        params: { sort_by: sortBy },
      });
      if (response.data && response.data.discussions) {
        setDiscussions(response.data.discussions);
      }
    } catch (err) {
      console.error('Error fetching discussions:', err);
      setError('Failed to load discussions');
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new discussion
  const handleCreateDiscussion = async (content: string) => {
    try {
      const response = await createDiscussion({
        content,
        has_code_snippet: content.includes('```'),
      });

      if (response.data && response.data.discussion) {
        setDiscussions((prev) => [response.data.discussion, ...prev]);
        toast.success('Discussion posted successfully');
      }
    } catch (err) {
      console.error('Error creating discussion:', err);
      toast.error('Failed to post discussion');
      throw err;
    }
  };

  // Create a reply to a discussion
  const handleCreateReply = async (discussionId: string, content: string) => {
    try {
      const response = await createReply(
        {
          content,
          has_code_snippet: content.includes('```'),
        },
        {
          url: `/challenges/${challengeId}/discussions/${discussionId}/replies`,
        },
      );

      if (response.data && response.data.reply) {
        setDiscussions((prev) =>
          prev.map((discussion) => {
            if (discussion.id === discussionId) {
              return {
                ...discussion,
                replies: [...(discussion.replies || []), response.data.reply],
              };
            }
            return discussion;
          }),
        );
        toast.success('Reply posted successfully');
      }
    } catch (err) {
      console.error('Error creating reply:', err);
      toast.error('Failed to post reply');
      throw err;
    }
  };

  // Handle upvote
  const handleUpvote = async (id: string, isReply = false) => {
    try {
      let parentId: string | undefined;
      let targetId = id;

      // If it's a reply, find the parent discussion
      if (isReply) {
        for (const discussion of discussions) {
          const reply = discussion.replies?.find((r) => r.id === id);
          if (reply) {
            parentId = discussion.id;
            break;
          }
        }

        if (!parentId) {
          throw new Error('Parent discussion not found');
        }
      }

      // Optimistic update
      setDiscussions((prev) =>
        prev.map((discussion) => {
          if (!isReply && discussion.id === id) {
            return { ...discussion, upvotes: discussion.upvotes + 1 };
          } else if (isReply && discussion.id === parentId) {
            return {
              ...discussion,
              replies: discussion.replies?.map((reply) =>
                reply.id === id
                  ? { ...reply, upvotes: reply.upvotes + 1 }
                  : reply,
              ),
            };
          }
          return discussion;
        }),
      );

      // API call
      const url = isReply
        ? `/challenges/${challengeId}/discussions/${parentId}/replies/${id}/votes`
        : `/challenges/${challengeId}/discussions/${id}/votes`;

      await updateVotes({ vote_type: 'upvote' }, { url });
    } catch (err) {
      console.error('Error upvoting:', err);
      toast.error('Failed to upvote');
      // Revert optimistic update if needed
      fetchDiscussions();
    }
  };

  // Handle downvote
  const handleDownvote = async (id: string, isReply = false) => {
    try {
      let parentId: string | undefined;
      let targetId = id;

      // If it's a reply, find the parent discussion
      if (isReply) {
        for (const discussion of discussions) {
          const reply = discussion.replies?.find((r) => r.id === id);
          if (reply) {
            parentId = discussion.id;
            break;
          }
        }

        if (!parentId) {
          throw new Error('Parent discussion not found');
        }
      }

      // Optimistic update
      setDiscussions((prev) =>
        prev.map((discussion) => {
          if (!isReply && discussion.id === id) {
            return { ...discussion, downvotes: discussion.downvotes + 1 };
          } else if (isReply && discussion.id === parentId) {
            return {
              ...discussion,
              replies: discussion.replies?.map((reply) =>
                reply.id === id
                  ? { ...reply, downvotes: reply.downvotes + 1 }
                  : reply,
              ),
            };
          }
          return discussion;
        }),
      );

      // API call
      const url = isReply
        ? `/challenges/${challengeId}/discussions/${parentId}/replies/${id}/votes`
        : `/challenges/${challengeId}/discussions/${id}/votes`;

      await updateVotes({ vote_type: 'downvote' }, { url });
    } catch (err) {
      console.error('Error downvoting:', err);
      toast.error('Failed to downvote');
      // Revert optimistic update if needed
      fetchDiscussions();
    }
  };

  // Handle sort change
  const handleSortChange = (newSortBy: 'newest' | 'popular') => {
    setSortBy(newSortBy);
  };

  // Load discussions on mount and when sort changes
  useEffect(() => {
    fetchDiscussions();
  }, [sortBy, challengeId]);

  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-card p-6 shadow-sm">
        <h3 className="mb-4 flex items-center text-xl font-medium">
          <MessageSquare className="mr-2 h-5 w-5 text-primary" />
          Discussions
        </h3>
        <p className="mb-6 text-muted-foreground">
          Share your approaches and discuss with other developers. Learn from
          the community and improve your skills.
        </p>

        <DiscussionForm onSubmit={handleCreateDiscussion} />

        <div className="mb-6 flex items-center justify-between">
          <h4 className="text-sm font-medium">
            {discussions.length}{' '}
            {discussions.length === 1 ? 'Discussion' : 'Discussions'}
          </h4>
          <div className="flex space-x-2">
            <Button
              variant={sortBy === 'newest' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSortChange('newest')}
            >
              Newest
            </Button>
            <Button
              variant={sortBy === 'popular' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSortChange('popular')}
            >
              Popular
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <DiscussionItemSkeleton key={i} />
              ))}
            </div>
          ) : error ? (
            <ErrorMessage
              title="Failed to load discussions"
              message={error}
              onRetry={fetchDiscussions}
            />
          ) : discussions.length > 0 ? (
            discussions.map((discussion) => (
              <DiscussionItem
                key={discussion.id}
                discussion={discussion}
                onUpvote={handleUpvote}
                onDownvote={handleDownvote}
                onReply={handleCreateReply}
                currentUserId={currentUserId}
              />
            ))
          ) : (
            <div className="rounded-lg border border-dashed p-6 text-center">
              <h4 className="text-lg font-medium">No discussions yet</h4>
              <p className="mt-2 text-sm text-muted-foreground">
                Be the first to start a discussion about this challenge!
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
