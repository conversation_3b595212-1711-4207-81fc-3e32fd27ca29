/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */
'use client';

import { useState } from 'react';

import { Code, MessageSquare } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';

import { CodeBlock } from './CodeBlock';

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

/**
 * @file DiscussionForm.tsx
 * @description React component for DiscussionForm
 */

interface DiscussionFormProps {
  onSubmit: (content: string) => void;
  placeholder?: string;
  buttonText?: string;
  isReply?: boolean;
  initialContent?: string;
}

export default function DiscussionForm({
  onSubmit,
  placeholder = 'Share your thoughts or ask a question...',
  buttonText = 'Post Discussion',
  isReply = false,
  initialContent = '',
}: DiscussionFormProps) {
  const [content, setContent] = useState(initialContent);
  const [activeTab, setActiveTab] = useState<'write' | 'preview'>('write');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form submission
  const handleSubmit = async () => {
    if (!content.trim()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(content);
      setContent('');
      setActiveTab('write');
    } catch (error) {
      console.error('Error submitting discussion:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if the content contains code blocks
  const hasCodeBlock = content.includes('```');

  // Parse content to render code blocks in preview
  const parseContent = (text: string) => {
    // Split by code blocks
    const parts = text.split(/(```[\s\S]*?```)/g);

    return parts.map((part, index) => {
      if (part.startsWith('```') && part.endsWith('```')) {
        // Extract language and code
        const match = part.match(/```(\w*)\n([\s\S]*?)```/);
        if (match) {
          const [, language, code] = match;
          return (
            <CodeBlock
              key={index}
              language={language || 'javascript'}
              code={code.trim()}
            />
          );
        }
      }

      // Regular text
      return (
        <p key={index} className="mb-4 whitespace-pre-wrap">
          {part}
        </p>
      );
    });
  };

  return (
    <div className={isReply ? '' : 'mb-6'}>
      <Tabs
        defaultValue="write"
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as 'write' | 'preview')}
        className="w-full"
      >
        <div className="mb-2 flex items-center justify-between">
          <TabsList className="h-9">
            <TabsTrigger
              value="write"
              className="flex items-center gap-1 data-[state=active]:bg-muted"
            >
              <MessageSquare className="h-4 w-4" />
              <span>Write</span>
            </TabsTrigger>
            <TabsTrigger
              value="preview"
              className="flex items-center gap-1 data-[state=active]:bg-muted"
              disabled={!content.trim()}
            >
              <Code className="h-4 w-4" />
              <span>Preview</span>
            </TabsTrigger>
          </TabsList>

          {!isReply && (
            <div className="text-xs text-muted-foreground">
              Use{' '}
              <code className="rounded bg-muted px-1 py-0.5">```code```</code>{' '}
              for code blocks
            </div>
          )}
        </div>

        <TabsContent value="write" className="mt-0">
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={placeholder}
            className={`min-h-[${isReply ? '80' : '120'}px] resize-none font-mono text-sm`}
          />
        </TabsContent>

        <TabsContent value="preview" className="mt-0">
          <div
            className={`min-h-[${isReply ? '80' : '120'}px] rounded-md border bg-muted/50 p-4`}
          >
            {content.trim() ? (
              <div className="prose prose-sm max-w-none dark:prose-invert">
                {parseContent(content)}
              </div>
            ) : (
              <p className="text-muted-foreground">Nothing to preview</p>
            )}
          </div>
        </TabsContent>
      </Tabs>

      <div className="mt-2 flex items-center justify-between">
        {hasCodeBlock && (
          <div className="text-xs text-muted-foreground">
            <Code className="mr-1 inline h-3 w-3" />
            Code block detected
          </div>
        )}
        <div className={hasCodeBlock ? '' : 'ml-auto'}>
          <Button
            onClick={handleSubmit}
            disabled={!content.trim() || isSubmitting}
            size={isReply ? 'sm' : 'default'}
          >
            {isSubmitting ? 'Posting...' : buttonText}
          </Button>
        </div>
      </div>
    </div>
  );
}
