/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */
'use client';

import { useState } from 'react';

import { formatDistanceToNow } from 'date-fns';
import { Code, MessageSquare, ThumbsDown, ThumbsUp, User } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

import DiscussionForm from './DiscussionForm';

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

/**
 * @file DiscussionItem.tsx
 * @description React component for DiscussionItem
 */

export interface DiscussionReply {
  id: string;
  user: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  content: string;
  created_at: string;
  upvotes: number;
  downvotes: number;
}

export interface Discussion {
  id: string;
  user: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  content: string;
  created_at: string;
  upvotes: number;
  downvotes: number;
  replies?: DiscussionReply[];
  has_code_snippet?: boolean;
}

interface DiscussionItemProps {
  discussion: Discussion;
  isReply?: boolean;
  onUpvote: (id: string, isReply?: boolean) => void;
  onDownvote: (id: string, isReply?: boolean) => void;
  onReply: (id: string, content: string) => void;
  currentUserId?: string;
}

export default function DiscussionItem({
  discussion,
  isReply = false,
  onUpvote,
  onDownvote,
  onReply,
  currentUserId,
}: DiscussionItemProps) {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [expanded, setExpanded] = useState(true);

  // Format the relative time
  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'Unknown date';
    }
  };

  // Check if the current user has authored this discussion
  const isAuthor = currentUserId && discussion.user.id === currentUserId;

  // Handle reply submission
  const handleReplySubmit = (content: string) => {
    onReply(discussion.id, content);
    setShowReplyForm(false);
  };

  return (
    <div className={cn('rounded-lg border p-4', isReply ? 'border-muted' : '')}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Avatar className={isReply ? 'h-8 w-8' : 'h-10 w-10'}>
            <AvatarImage
              src={discussion.user.avatar_url}
              alt={discussion.user.username}
            />
            <AvatarFallback>
              <User className={isReply ? 'h-4 w-4' : 'h-5 w-5'} />
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-2">
              <span className={cn('font-medium', isReply ? 'text-sm' : '')}>
                {discussion.user.username}
              </span>
              {isAuthor && (
                <Badge
                  variant="outline"
                  className="bg-primary/10 text-xs text-primary"
                >
                  You
                </Badge>
              )}
            </div>
            <div className="text-xs text-muted-foreground">
              {formatTime(discussion.created_at)}
            </div>
          </div>
        </div>

        {discussion.has_code_snippet && (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Code className="h-3 w-3" />
            <span>Code</span>
          </Badge>
        )}
      </div>

      <div className="mt-3 whitespace-pre-wrap text-sm">
        {discussion.content}
      </div>

      <div className="mt-3 flex items-center space-x-4 text-xs">
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2 text-muted-foreground hover:text-foreground"
          onClick={() => onUpvote(discussion.id, isReply)}
        >
          <ThumbsUp className="mr-1 h-4 w-4" />
          <span>{discussion.upvotes || 0}</span>
        </Button>

        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2 text-muted-foreground hover:text-foreground"
          onClick={() => onDownvote(discussion.id, isReply)}
        >
          <ThumbsDown className="mr-1 h-4 w-4" />
          <span>{discussion.downvotes || 0}</span>
        </Button>

        {!isReply && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-muted-foreground hover:text-foreground"
            onClick={() => setShowReplyForm(!showReplyForm)}
          >
            <MessageSquare className="mr-1 h-4 w-4" />
            <span>Reply</span>
          </Button>
        )}
      </div>

      {showReplyForm && (
        <div className="mt-4">
          <DiscussionForm
            onSubmit={handleReplySubmit}
            placeholder="Write a reply..."
            buttonText="Post Reply"
            isReply={true}
          />
        </div>
      )}

      {!isReply && discussion.replies && discussion.replies.length > 0 && (
        <div className="mt-4 space-y-3 pl-6">
          <div className="mb-2 flex items-center justify-between">
            <h4 className="text-sm font-medium">
              {discussion.replies.length}{' '}
              {discussion.replies.length === 1 ? 'Reply' : 'Replies'}
            </h4>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? 'Hide Replies' : 'Show Replies'}
            </Button>
          </div>

          {expanded &&
            discussion.replies.map((reply) => (
              <DiscussionItem
                key={reply.id}
                discussion={reply as Discussion}
                isReply={true}
                onUpvote={onUpvote}
                onDownvote={onDownvote}
                onReply={onReply}
                currentUserId={currentUserId}
              />
            ))}
        </div>
      )}
    </div>
  );
}

export function DiscussionItemSkeleton({
  isReply = false,
}: {
  isReply?: boolean;
}) {
  return (
    <div className={cn('rounded-lg border p-4', isReply ? 'border-muted' : '')}>
      <div className="flex items-center space-x-2">
        <Skeleton
          className={
            isReply ? 'h-8 w-8 rounded-full' : 'h-10 w-10 rounded-full'
          }
        />
        <div className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-3 w-16" />
        </div>
      </div>
      <div className="mt-3 space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
      <div className="mt-3 flex items-center space-x-4">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
      </div>
    </div>
  );
}
