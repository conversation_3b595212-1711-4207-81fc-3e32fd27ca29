/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */
'use client';

import { useState } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import {
  vs,
  vscDarkPlus,
} from 'react-syntax-highlighter/dist/esm/styles/prism';

import { useTheme } from 'next-themes';

import { Check, Copy } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

/**
 * @file CodeBlock.tsx
 * @description React component for CodeBlock
 */

interface CodeBlockProps {
  code: string;
  language: string;
  showLineNumbers?: boolean;
}

export function CodeBlock({
  code,
  language,
  showLineNumbers = true,
}: CodeBlockProps) {
  const [copied, setCopied] = useState(false);
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Normalize language
  const normalizedLanguage = language.toLowerCase();

  // Handle copy to clipboard
  const handleCopy = async () => {
    await navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative my-4 overflow-hidden rounded-md">
      <div className="flex items-center justify-between bg-muted px-4 py-2 font-mono text-xs">
        <span>{language || 'code'}</span>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-2 text-muted-foreground hover:text-foreground"
          onClick={handleCopy}
        >
          {copied ? (
            <>
              <Check className="mr-1 h-3 w-3" />
              <span>Copied!</span>
            </>
          ) : (
            <>
              <Copy className="mr-1 h-3 w-3" />
              <span>Copy</span>
            </>
          )}
        </Button>
      </div>
      <SyntaxHighlighter
        language={normalizedLanguage}
        style={isDark ? vscDarkPlus : vs}
        showLineNumbers={showLineNumbers}
        wrapLines={true}
        customStyle={{
          margin: 0,
          padding: '1rem',
          fontSize: '0.875rem',
          borderRadius: '0 0 0.375rem 0.375rem',
        }}
      >
        {code}
      </SyntaxHighlighter>
    </div>
  );
}
