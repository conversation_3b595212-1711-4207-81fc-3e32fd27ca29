/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */
'use client';

import { useEffect, useState } from 'react';

import {
  CheckCircle,
  Clock,
  Code,
  Database,
  Eye,
  Share2,
  XCircle,
} from 'lucide-react';
import { toast } from 'sonner';

import ShareSolutionButton from '@/components/ShareSolutionButton';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useSubmissionHistory } from '@/hooks/useChallengeService';

import SubmissionResultModal from '../SubmissionResultModal';

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

/**
 * @file index.tsx
 * @description React component for SubmissionHistory
 */

interface Submission {
  id: string;
  status: string;
  runtime_ms: number;
  memory_used_kb: number;
  code: string;
  language: string;
  feedback: string;
  score: number;
  created_at: string;
  test_results?: {
    passed: boolean;
    name?: string;
    input?: string;
    expected_output?: string;
    actual_output?: string;
    error?: string;
    is_hidden?: boolean;
  }[];
}

export default function SubmissionHistory({
  challengeId,
}: {
  challengeId: string;
}) {
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSubmission, setSelectedSubmission] =
    useState<Submission | null>(null);
  const [showModal, setShowModal] = useState(false);

  // Use the submission history hook
  const { fetchSubmissionHistory } = useSubmissionHistory(challengeId);

  const fetchSubmissions = async () => {
    setIsLoading(true);
    try {
      const submissionData = await fetchSubmissionHistory();
      setSubmissions(submissionData as Submission[]);
    } catch (error) {
      console.error('Error fetching submissions:', error);
      toast.error('Failed to load submission history');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSubmissions();
  }, [challengeId]);

  const handleViewSubmission = (submission: Submission) => {
    setSelectedSubmission(submission);
    setShowModal(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-5 w-24" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap items-center gap-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (submissions.length === 0) {
    return (
      <Card className="border-dashed bg-muted/50">
        <CardHeader>
          <CardTitle>No Submissions Yet</CardTitle>
          <CardDescription>
            You haven&apos;t submitted any solutions for this challenge yet.
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center pb-6">
          <Button
            variant="outline"
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          >
            Try Submitting a Solution
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold">Your Submission History</h3>

      {submissions.map((submission) => (
        <Card key={submission.id} className="overflow-hidden">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {submission.status === 'accepted' ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="text-red-500 h-5 w-5" />
                )}
                <span className="font-medium">
                  {new Date(submission.created_at).toLocaleString()}
                </span>
              </div>
              <Badge
                variant={
                  submission.status === 'accepted' ? 'default' : 'destructive'
                }
                className="capitalize"
              >
                {submission.status.replace('_', ' ')}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap items-center gap-4 text-sm">
              <div className="flex items-center">
                <Code className="mr-1 h-4 w-4 text-blue-500" />
                <span className="capitalize">{submission.language}</span>
              </div>
              <div className="flex items-center">
                <Clock className="mr-1 h-4 w-4 text-blue-500" />
                <span>{submission.runtime_ms} ms</span>
              </div>
              <div className="flex items-center">
                <Database className="mr-1 h-4 w-4 text-purple-500" />
                <span>{(submission.memory_used_kb / 1024).toFixed(2)} MB</span>
              </div>
              <div className="ml-auto flex gap-2">
                <ShareSolutionButton
                  challengeId={challengeId}
                  submissionId={submission.id}
                  language={submission.language}
                  variant="ghost"
                  size="sm"
                  showText={false}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleViewSubmission(submission)}
                >
                  <Eye className="mr-1 h-4 w-4" />
                  View Details
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      <SubmissionResultModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        result={selectedSubmission}
        language={selectedSubmission?.language || 'javascript'}
        similarSolutions={submissions.map((s) => ({
          id: s.id,
          user: {
            id: 'user1',
            name:
              s.id === selectedSubmission?.id
                ? 'You'
                : `User ${Math.floor(Math.random() * 1000)}`,
          },
          language: s.language,
          status: s.status,
          runtime_ms: s.runtime_ms || 0,
          memory_kb: s.memory_used_kb || 0,
          code_length: s.code?.length || 0,
          submitted_at: s.created_at,
          is_current: s.id === selectedSubmission?.id,
        }))}
        optimizationTips={[
          {
            id: '1',
            title: 'Use a more efficient data structure',
            description:
              'Consider using a hash map instead of an array for lookups to improve time complexity.',
            category: 'data_structure',
            difficulty: 'intermediate',
            language: selectedSubmission?.language || 'javascript',
            impact: 'high',
            code_example:
              'const map = new Map();\nfor (const item of items) {\n  map.set(item.id, item);\n}\n// O(1) lookup\nconst item = map.get(id);',
          },
          {
            id: '2',
            title: 'Optimize loop operations',
            description:
              'Avoid unnecessary operations inside loops to reduce time complexity.',
            category: 'algorithm',
            difficulty: 'beginner',
            language: selectedSubmission?.language || 'javascript',
            impact: 'medium',
            code_example:
              '// Before\nfor (let i = 0; i < arr.length; i++) {\n  // arr.length is calculated each iteration\n}\n\n// After\nconst len = arr.length;\nfor (let i = 0; i < len; i++) {\n  // length calculated only once\n}',
          },
          {
            id: '3',
            title: 'Reduce memory usage',
            description:
              'Avoid creating unnecessary copies of large data structures.',
            category: 'memory',
            difficulty: 'intermediate',
            language: selectedSubmission?.language || 'javascript',
            impact: 'high',
            code_example:
              '// Instead of copying\nconst copy = [...largeArray];\n\n// Use references when possible\nfunction process(arr) {\n  // Work with the original array\n}',
          },
        ]}
        showMetrics={true}
        showComparison={true}
        showOptimizationTips={true}
        onViewSolution={(solutionId) => {
          const submission = submissions.find((s) => s.id === solutionId);
          if (submission) {
            setSelectedSubmission(submission);
          }
        }}
      />
    </div>
  );
}
