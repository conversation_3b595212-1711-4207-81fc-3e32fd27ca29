/**
 * @file index.tsx
 * @description React component for Hints
 */
'use client';

import { useState } from 'react';

import { AlertTriangle, LightbulbIcon, Lock, Trophy } from 'lucide-react';
import { toast } from 'sonner';

import { ErrorMessage } from '@/components/LoadingStates';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Hint, useHints } from '@/hooks/useHints';

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

/**
 * @file index.tsx
 * @description React component for Hints
 */

// Hint interface is now imported from useHints

interface HintsProps {
  challengeId: string;
  totalPoints: number;
}

export default function Hints({ challengeId, totalPoints }: HintsProps) {
  const {
    hints,
    isLoading,
    error,
    fetchHints,
    unlockHint: unlockHintById,
    calculateTotalPenalty,
  } = useHints(challengeId);
  const [unlockingHintId, setUnlockingHintId] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedHint, setSelectedHint] = useState<Hint | null>(null);

  // Handle hint unlock
  const handleUnlockHint = async (hint: Hint) => {
    setSelectedHint(hint);
    setShowConfirmDialog(true);
  };

  // Confirm hint unlock
  const confirmUnlockHint = async () => {
    if (!selectedHint) return;

    setUnlockingHintId(selectedHint.id);
    try {
      const success = await unlockHintById(selectedHint.id);

      if (success) {
        toast.success(
          `Hint unlocked! (${selectedHint.points_penalty} points deducted)`,
        );
      }
    } catch (err) {
      console.error('Error unlocking hint:', err);
      toast.error('Failed to unlock hint');
    } finally {
      setUnlockingHintId(null);
      setShowConfirmDialog(false);
    }
  };

  // Calculate remaining points
  const calculateRemainingPoints = () => {
    const totalPenalty = calculateTotalPenalty();
    return Math.max(0, totalPoints - totalPenalty);
  };

  // Count unlocked hints
  const countUnlockedHints = () => {
    return hints.filter((hint) => hint.is_unlocked).length;
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-6 w-24" />
        </div>
        {[1, 2, 3].map((i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <ErrorMessage
        title="Failed to load hints"
        message={error}
        onRetry={fetchHints}
      />
    );
  }

  // Empty state
  if (hints.length === 0) {
    return (
      <div className="rounded-lg border border-dashed p-6 text-center">
        <LightbulbIcon className="mx-auto h-8 w-8 text-muted-foreground" />
        <h3 className="mt-2 text-lg font-medium">No hints available</h3>
        <p className="mt-1 text-sm text-muted-foreground">
          This challenge doesn't have any hints yet.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <h3 className="text-lg font-medium">
          Available Hints ({countUnlockedHints()}/{hints.length})
        </h3>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <Trophy className="h-3 w-3 text-yellow-500" />
            <span>
              {calculateRemainingPoints()} / {totalPoints} points remaining
            </span>
          </Badge>
        </div>
      </div>

      <div className="rounded-md border bg-card">
        <Accordion type="single" collapsible className="w-full">
          {hints.map((hint, index) => (
            <AccordionItem key={hint.id} value={hint.id}>
              <AccordionTrigger
                className="px-4 py-2"
                disabled={!hint.is_unlocked}
              >
                <div className="flex w-full items-center justify-between pr-4">
                  <div className="flex items-center gap-2">
                    <Badge variant={hint.is_unlocked ? 'default' : 'outline'}>
                      Hint {index + 1}
                    </Badge>
                    {!hint.is_unlocked && (
                      <Lock className="h-4 w-4 text-muted-foreground" />
                    )}
                  </div>
                  {!hint.is_unlocked && (
                    <Badge variant="destructive" className="ml-auto mr-2">
                      -{hint.points_penalty} points
                    </Badge>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                {hint.is_unlocked ? (
                  <div className="rounded-md bg-muted p-3 text-sm">
                    {hint.content}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center gap-2 py-4">
                    <Lock className="h-8 w-8 text-muted-foreground" />
                    <p className="text-center text-sm text-muted-foreground">
                      This hint is locked. Unlock it to view the content.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleUnlockHint(hint)}
                      disabled={unlockingHintId === hint.id}
                    >
                      {unlockingHintId === hint.id ? (
                        'Unlocking...'
                      ) : (
                        <>Unlock (-{hint.points_penalty} points)</>
                      )}
                    </Button>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Unlock Hint?</AlertDialogTitle>
            <AlertDialogDescription>
              <div className="space-y-2">
                <p>
                  Are you sure you want to unlock this hint? This will deduct{' '}
                  <strong className="text-destructive">
                    {selectedHint?.points_penalty} points
                  </strong>{' '}
                  from your total score for this challenge.
                </p>
                <div className="flex items-center rounded-md bg-amber-100 p-2 text-sm text-amber-800 dark:bg-amber-900/30 dark:text-amber-400">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  <p>
                    Your remaining points after unlocking:{' '}
                    {calculateRemainingPoints() -
                      (selectedHint?.points_penalty || 0)}
                  </p>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmUnlockHint}>
              Unlock Hint
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
