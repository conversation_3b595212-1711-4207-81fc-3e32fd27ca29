/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */
'use client';

import { useEffect, useState } from 'react';

import { formatDistanceToNow } from 'date-fns';
import { Award, Calendar, Clock, Code, Database, User } from 'lucide-react';

import {
  EmptyState,
  ErrorMessage,
  SkeletonLoader,
} from '@/components/LoadingStates';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAxiosGet } from '@/hooks/useAxios';
import { getAriaTabPanelProps, getAriaTabProps } from '@/utils/accessibility';

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for ChallengeLeaderboard
 */

export interface LeaderboardEntry {
  id: string;
  user_id: string;
  username: string;
  avatar_url?: string;
  language: string;
  runtime_ms: number;
  memory_used_kb: number;
  score: number;
  submitted_at: string;
  code_length: number;
}

interface ChallengeLeaderboardProps {
  challengeId: string;
}

export default function ChallengeLeaderboard({
  challengeId,
}: ChallengeLeaderboardProps) {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('runtime');
  const [languageFilter, setLanguageFilter] = useState('all');
  const [availableLanguages, setAvailableLanguages] = useState<string[]>([]);

  // Get leaderboard data
  const [getLeaderboard] = useAxiosGet<{ leaderboard: LeaderboardEntry[] }>(
    `/challenges/${challengeId}/leaderboard`,
  );

  // Fetch leaderboard data
  const fetchLeaderboard = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getLeaderboard();
      if (response.data && response.data.leaderboard) {
        setLeaderboard(response.data.leaderboard);

        // Extract available languages
        const languages = Array.from(
          new Set(response.data.leaderboard.map((entry) => entry.language)),
        );
        setAvailableLanguages(languages);
      }
    } catch (err) {
      console.error('Error fetching leaderboard:', err);
      setError('Failed to load leaderboard data');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter and sort leaderboard entries
  const getFilteredLeaderboard = () => {
    let filtered = [...leaderboard];

    // Apply language filter
    if (languageFilter !== 'all') {
      filtered = filtered.filter((entry) => entry.language === languageFilter);
    }

    // Sort based on active tab
    if (activeTab === 'runtime') {
      filtered.sort((a, b) => a.runtime_ms - b.runtime_ms);
    } else if (activeTab === 'memory') {
      filtered.sort((a, b) => a.memory_used_kb - b.memory_used_kb);
    } else if (activeTab === 'score') {
      filtered.sort((a, b) => b.score - a.score);
    } else if (activeTab === 'code_length') {
      filtered.sort((a, b) => a.code_length - b.code_length);
    }

    return filtered;
  };

  // Format the relative time
  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      return 'Unknown date';
    }
  };

  // Load leaderboard on mount
  useEffect(() => {
    fetchLeaderboard();
  }, [challengeId]);

  // Get filtered and sorted leaderboard
  const filteredLeaderboard = getFilteredLeaderboard();

  // Loading state
  if (isLoading) {
    return <SkeletonLoader type="table" />;
  }

  // Error state
  if (error) {
    return (
      <ErrorMessage
        title="Failed to load leaderboard"
        message={error}
        onRetry={fetchLeaderboard}
      />
    );
  }

  // Empty state
  if (leaderboard.length === 0) {
    return (
      <EmptyState
        icon={Award}
        title="No leaderboard entries yet"
        description="Be the first to submit a solution and claim the top spot!"
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full sm:w-auto"
        >
          <TabsList className="grid w-full grid-cols-4 sm:flex sm:w-auto">
            <TabsTrigger
              value="runtime"
              className="flex items-center gap-1"
              {...getAriaTabProps('runtime-tab', activeTab === 'runtime')}
            >
              <Clock className="h-4 w-4" />
              <span className="hidden sm:inline">Fastest</span>
            </TabsTrigger>
            <TabsTrigger
              value="memory"
              className="flex items-center gap-1"
              {...getAriaTabProps('memory-tab', activeTab === 'memory')}
            >
              <Database className="h-4 w-4" />
              <span className="hidden sm:inline">Memory</span>
            </TabsTrigger>
            <TabsTrigger
              value="score"
              className="flex items-center gap-1"
              {...getAriaTabProps('score-tab', activeTab === 'score')}
            >
              <Award className="h-4 w-4" />
              <span className="hidden sm:inline">Score</span>
            </TabsTrigger>
            <TabsTrigger
              value="code_length"
              className="flex items-center gap-1"
              {...getAriaTabProps(
                'code-length-tab',
                activeTab === 'code_length',
              )}
            >
              <Code className="h-4 w-4" />
              <span className="hidden sm:inline">Shortest</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="w-full sm:w-48">
          <Select value={languageFilter} onValueChange={setLanguageFilter}>
            <SelectTrigger aria-label="Filter by language">
              <SelectValue placeholder="Language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Languages</SelectItem>
              {availableLanguages.map((language) => (
                <SelectItem key={language} value={language}>
                  {language}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12 text-center">#</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Language</TableHead>
              <TableHead className="text-right">
                {activeTab === 'runtime' && 'Runtime'}
                {activeTab === 'memory' && 'Memory'}
                {activeTab === 'score' && 'Score'}
                {activeTab === 'code_length' && 'Code Length'}
              </TableHead>
              <TableHead className="hidden text-right md:table-cell">
                Submitted
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLeaderboard.slice(0, 10).map((entry, index) => (
              <TableRow key={entry.id}>
                <TableCell className="text-center font-medium">
                  {index === 0 ? (
                    <Badge
                      variant="default"
                      className="bg-yellow-500 hover:bg-yellow-600"
                    >
                      {index + 1}
                    </Badge>
                  ) : index === 1 ? (
                    <Badge
                      variant="outline"
                      className="border-gray-400 text-gray-400"
                    >
                      {index + 1}
                    </Badge>
                  ) : index === 2 ? (
                    <Badge
                      variant="outline"
                      className="border-amber-700 text-amber-700"
                    >
                      {index + 1}
                    </Badge>
                  ) : (
                    index + 1
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={entry.avatar_url}
                        alt={entry.username}
                      />
                      <AvatarFallback>
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{entry.username}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="font-mono">
                    {entry.language}
                  </Badge>
                </TableCell>
                <TableCell className="text-right font-mono">
                  {activeTab === 'runtime' && `${entry.runtime_ms} ms`}
                  {activeTab === 'memory' &&
                    `${(entry.memory_used_kb / 1024).toFixed(2)} MB`}
                  {activeTab === 'score' && `${entry.score} pts`}
                  {activeTab === 'code_length' && `${entry.code_length} chars`}
                </TableCell>
                <TableCell className="hidden text-right text-muted-foreground md:table-cell">
                  <div className="flex items-center justify-end gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{formatTime(entry.submitted_at)}</span>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {filteredLeaderboard.length > 10 && (
        <div className="flex justify-center">
          <Button variant="outline" size="sm">
            View All ({filteredLeaderboard.length})
          </Button>
        </div>
      )}
    </div>
  );
}
