/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */
'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { motion } from 'framer-motion';
import {
  AlertCircle,
  ArrowLeft,
  Award,
  Beaker,
  BookOpen,
  CheckCircle,
  Clock,
  Code,
  LightbulbIcon,
  Play,
  Tag,
  Terminal,
  Trophy,
} from 'lucide-react';
import { toast } from 'sonner';

import BookmarkButton from '@/components/BookmarkButton';
import ChallengeProgressIndicator from '@/components/ChallengeProgressIndicator';
import CodeEditor from '@/components/CodeEditor';
import { ErrorMessage } from '@/components/LoadingStates';
import ShareButton from '@/components/ShareButton';
import ShareSolutionButton from '@/components/ShareSolutionButton';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';
import { useLocalChallengeProgress } from '@/hooks/useChallengeProgress';
import {
  useAllBoilerplates,
  useBoilerplate,
  useSubmitSolution,
} from '@/hooks/useChallengeService';
import {
  getDefaultCode,
  getLanguageDisplayName,
  getSupportedLanguages,
} from '@/utils/codeTemplates';

import ChallengeLeaderboard from '../ChallengeLeaderboard';
import ChallengeSkeleton from '../ChallengeSkeleton';
import DiscussionSection from '../Discussion';
import Hints from '../Hints';
import RelatedRoadmaps from '../RelatedRoadmaps';
import SubmissionHistory from '../SubmissionHistory';
import SubmissionResultModal from '../SubmissionResultModal';
import TestCases from '../TestCases';

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

/**
 * @file index.tsx
 * @description React component for CodingChallenge
 */

interface IChallenge {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  category: string;
  points: number;
  input_format: string;
  output_format: string;
  example_input: string;
  example_output: string;
  constraints: string;
  function_signature: string;
  tags?: string[];
  time_limit?: number;
  memory_limit?: number;
  solutions?: {
    [key: string]: string;
  };
  status?: string;
  topic_id?: string;
  created_at?: string;
  updated_at?: string;
  // Add any other fields that might be in the existing API
  inputFormat?: string; // For backward compatibility
  outputFormat?: string; // For backward compatibility
  exampleInput?: string; // For backward compatibility
  exampleOutput?: string; // For backward compatibility
}

const difficultyColors = {
  EASY: 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400',
  MEDIUM:
    'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400',
  HARD: 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400',
};

export default function CodingChallenge({ id }: { id: string }) {
  const [challenge, setChallenge] = useState<IChallenge>();
  const [isLoading, setIsLoading] = useState(true);
  const [code, setCode] = useState('');
  const [output, setOutput] = useState('');
  const [language, setLanguage] = useState('javascript');
  const [solutionLanguage, setSolutionLanguage] = useState('javascript');
  const [isRunning, setIsRunning] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('problem');

  const [submissionResult, setSubmissionResult] = useState<{
    id: string;
    status: string;
    runtime_ms: number;
    memory_used_kb: number;
    feedback: string;
    score: number;
    created_at: string;
  } | null>(null);
  const [showSubmissionModal, setShowSubmissionModal] = useState(false);
  const [boilerplates, setBoilerplates] = useState<Record<string, string>>({});
  const router = useRouter();

  // Remove the automatic redirect
  // router.push('/coding-challenges');

  // Use hooks for boilerplates
  const { fetchAllBoilerplates } = useAllBoilerplates(id);
  const { fetchBoilerplate: fetchBoilerplateCode } = useBoilerplate(
    id,
    language,
  );
  const { submitChallengeSolution } = useSubmitSolution(id);

  // Fetch all boilerplates for the challenge
  const fetchBoilerplatesForChallenge = async () => {
    try {
      const boilerplateData = await fetchAllBoilerplates();
      setBoilerplates(boilerplateData);
    } catch (error) {
      console.error('Error fetching boilerplates:', error);
      toast.error('Failed to load code templates');
    }
  };

  // Fetch a specific boilerplate for the current language
  const fetchBoilerplateForLanguage = async (lang: string) => {
    try {
      // We need to use the hook directly for the current language
      // For other languages, we'd need to create a new hook instance
      const code =
        lang === language ? await fetchBoilerplateCode() : getDefaultCode(lang); // Fallback for now
      return code;
    } catch (error) {
      console.error(`Error fetching boilerplate for ${lang}:`, error);
      return getDefaultCode(lang);
    }
  };

  const [runCode] = useAxiosPost<{
    error?: string;
    output?: string;
  }>('/run-code');

  const [getChallenge] = useAxiosGet<{ challenge: IChallenge }>(
    `/challenges/${id}`,
  );

  // We now use the useSubmitSolution hook defined above

  // Use local storage fallback for progress tracking
  const { getChallengeProgress, updateChallengeProgress } =
    useLocalChallengeProgress();
  const progress = getChallengeProgress(id);
  const status = progress?.status || 'not_started';

  const handleLanguageChange = async (value: string) => {
    setLanguage(value);

    // Check if there's saved code in localStorage
    const savedCode =
      typeof window !== 'undefined'
        ? localStorage.getItem(`challenge-${id}-${value}`)
        : null;

    if (savedCode) {
      // Use saved code if available
      setCode(savedCode);
    } else if (boilerplates[value]) {
      // Use cached boilerplate if available
      setCode(boilerplates[value]);
    } else {
      // Fetch boilerplate from backend
      try {
        const boilerplate = await fetchBoilerplateForLanguage(value);
        // Update boilerplates cache
        setBoilerplates((prev) => ({ ...prev, [value]: boilerplate }));
        setCode(boilerplate);
      } catch {
        // Fallback to default template
        setCode(getDefaultCode(value));
      }
    }
  };

  const handleRunCode = async () => {
    if (!code.trim()) {
      toast.error('Please write some code before running');
      return;
    }

    setIsRunning(true);
    setOutput('');

    try {
      // Update progress to in_progress when user runs code
      if (status === 'not_started') {
        updateChallengeProgress(id, 'in_progress');
      }

      const { data } = await runCode({
        language,
        code,
      });
      if (data?.error) {
        setOutput(data.error);
      } else {
        setOutput(data?.output ?? '');
      }
      toast.success('Code executed successfully');
    } catch (error) {
      setOutput(`Error: ${(error as { message: string }).message}`);
      toast.error('Failed to execute code');
    } finally {
      setIsRunning(false);
    }
  };

  const handleSubmit = async () => {
    if (!code.trim()) {
      toast.error('Please write some code before submitting');
      return;
    }

    setIsSubmitting(true);
    setOutput('');

    try {
      const submission = await submitChallengeSolution(code, language);

      if (submission) {
        setSubmissionResult(submission);
        setShowSubmissionModal(true);

        // If the submission was successful, update the challenge status
        if (submission.status === 'accepted') {
          toast.success('Challenge completed successfully!');
          // Update progress to completed
          updateChallengeProgress(id, 'completed');
          // Optionally refresh the challenge data to update the UI
          fetchChallenge();
        } else {
          toast.error(`Submission failed: ${submission.status}`);
          // Update progress to in_progress
          updateChallengeProgress(id, 'in_progress');
        }
      } else {
        toast.error('Failed to submit solution');
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      toast.error(`Error: ${(error as { message: string }).message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchChallenge = async () => {
    setIsLoading(true);
    try {
      // Fetch challenge data
      const response = await getChallenge();
      if (response.data && response.data.challenge) {
        const challengeData = response.data.challenge;
        setChallenge(challengeData);

        // Fetch all boilerplates for this challenge
        await fetchBoilerplatesForChallenge();

        // Check if there's saved code in localStorage
        const savedCode =
          typeof window !== 'undefined'
            ? localStorage.getItem(`challenge-${id}-${language}`)
            : null;

        // Initialize code with saved code, backend boilerplate, or default template
        if (savedCode) {
          setCode(savedCode);
        } else if (boilerplates[language]) {
          // Use boilerplate from API if available
          setCode(boilerplates[language]);
        } else {
          // Fetch specific boilerplate for current language
          const boilerplate = await fetchBoilerplateForLanguage(language);
          setCode(boilerplate);
        }

        // Set solution language if solutions are available
        if (
          challengeData.solutions &&
          Object.keys(challengeData.solutions).length > 0
        ) {
          // Prefer to use the same language as the editor if available
          if (challengeData.solutions[language]) {
            setSolutionLanguage(language);
          } else {
            // Otherwise use the first available language
            setSolutionLanguage(Object.keys(challengeData.solutions)[0]);
          }
        }
      } else {
        throw new Error('Challenge data not found');
      }
    } catch (error) {
      console.error('Error fetching challenge:', error);
      toast.error('Failed to load challenge');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Fetch challenge data and boilerplates
    fetchChallenge();
  }, [id]);

  if (isLoading) {
    return <ChallengeSkeleton />;
  }

  if (!challenge) {
    return (
      <div className="container mx-auto p-6">
        <ErrorMessage
          title="Challenge Not Found"
          message="The challenge you're looking for doesn't exist or has been removed."
          onRetry={() => router.push('/coding-challenges')}
          className="mx-auto max-w-md"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Top Navigation Bar */}
      <div className="sticky top-0 z-10 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center px-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/coding-challenges')}
            className="mr-2"
            aria-label="Back to challenges"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
            <div className="w-full flex-1"></div>
            <div className="flex items-center gap-2">
              <div className="flex items-center rounded-full bg-muted px-3 py-1 text-sm">
                <Trophy className="mr-2 h-4 w-4 text-yellow-500" />
                <span className="font-medium">{challenge.points} points</span>
              </div>
              {challenge.time_limit && (
                <div className="flex items-center rounded-full bg-muted px-3 py-1 text-sm">
                  <Clock className="mr-2 h-4 w-4 text-blue-500" />
                  <span className="font-medium">{challenge.time_limit}ms</span>
                </div>
              )}
              <Badge
                variant="outline"
                className={`${difficultyColors[challenge.difficulty as keyof typeof difficultyColors] || 'bg-gray-100'} ml-1 px-3 py-1`}
              >
                {challenge.difficulty}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Challenge Title and Description */}
      <div className="container px-4 py-6">
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="mb-6"
        >
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-2">
              <ChallengeProgressIndicator
                status={status}
                progress={progress || undefined}
                size="md"
              />
              <h1 className="text-3xl font-bold tracking-tight md:text-4xl">
                {challenge.title}
              </h1>
            </div>
            <div className="flex items-center gap-2 self-end sm:self-auto">
              <ShareButton
                url={`/coding-challenges/${id}`}
                title={challenge.title}
                description={`A ${challenge.difficulty.toLowerCase()} level coding challenge worth ${challenge.points} points.`}
                variant="outline"
                size="md"
                showText={true}
                className="hidden md:flex"
              />
              <ShareButton
                url={`/coding-challenges/${id}`}
                title={challenge.title}
                description={`A ${challenge.difficulty.toLowerCase()} level coding challenge worth ${challenge.points} points.`}
                variant="outline"
                size="sm"
                showText={false}
                className="md:hidden"
              />
              <BookmarkButton
                challengeId={id}
                variant="outline"
                size="md"
                showText={true}
                className="hidden md:flex"
              />
              <BookmarkButton
                challengeId={id}
                variant="outline"
                size="sm"
                className="md:hidden"
              />
            </div>
          </div>
          {challenge.tags && challenge.tags.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-2">
              {challenge.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  <Tag className="mr-1 h-3 w-3" />
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-5">
          {/* Left Column - Problem Description */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="order-2 lg:order-1 lg:col-span-3"
          >
            <Card className="overflow-hidden border-none shadow-lg">
              <Tabs
                defaultValue="problem"
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <div className="overflow-x-auto border-b bg-muted/40">
                  <div className="container px-4">
                    <TabsList className="h-12 w-full flex-nowrap bg-transparent p-0 md:w-auto">
                      <TabsTrigger
                        value="problem"
                        className="h-12 rounded-none px-2 text-xs data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none md:px-4 md:text-sm"
                      >
                        Problem
                      </TabsTrigger>
                      <TabsTrigger
                        value="test-cases"
                        className="h-12 rounded-none px-2 text-xs data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none md:px-4 md:text-sm"
                      >
                        Tests
                      </TabsTrigger>
                      <TabsTrigger
                        value="submissions"
                        className="h-12 rounded-none px-2 text-xs data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none md:px-4 md:text-sm"
                      >
                        Submissions
                      </TabsTrigger>
                      <TabsTrigger
                        value="solution"
                        className="h-12 rounded-none px-2 text-xs data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none md:px-4 md:text-sm"
                      >
                        Solution
                      </TabsTrigger>
                      <TabsTrigger
                        value="hints"
                        className="h-12 rounded-none px-2 text-xs data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none md:px-4 md:text-sm"
                      >
                        Hints
                      </TabsTrigger>
                      <TabsTrigger
                        value="leaderboard"
                        className="h-12 rounded-none px-2 text-xs data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none md:px-4 md:text-sm"
                      >
                        Leaderboard
                      </TabsTrigger>
                      <TabsTrigger
                        value="discussion"
                        className="h-12 rounded-none px-2 text-xs data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none md:px-4 md:text-sm"
                      >
                        Discussion
                      </TabsTrigger>
                    </TabsList>
                  </div>
                </div>
                <CardContent className="p-6">
                  <TabsContent value="problem" className="mt-0 space-y-6">
                    <div className="prose max-w-none dark:prose-invert">
                      <div className="rounded-lg bg-card p-6 shadow-sm">
                        <p className="text-base leading-relaxed">
                          {challenge.description}
                        </p>
                      </div>

                      <div className="mt-6 space-y-6 md:mt-8 md:grid md:grid-cols-2 md:gap-6 md:space-y-0">
                        <div className="rounded-lg bg-card p-6 shadow-sm">
                          <h3 className="mb-3 flex items-center text-lg font-medium">
                            <Code className="mr-2 h-5 w-5 text-primary" />
                            Example Input
                          </h3>
                          <div className="overflow-x-auto whitespace-pre-wrap rounded-md bg-muted p-4 font-mono text-sm">
                            {challenge.example_input || challenge.exampleInput}
                          </div>
                        </div>
                        <div className="rounded-lg bg-card p-6 shadow-sm">
                          <h3 className="mb-3 flex items-center text-lg font-medium">
                            <Terminal className="mr-2 h-5 w-5 text-primary" />
                            Example Output
                          </h3>
                          <div className="overflow-x-auto whitespace-pre-wrap rounded-md bg-muted p-4 font-mono text-sm">
                            {challenge.example_output ||
                              challenge.exampleOutput}
                          </div>
                        </div>
                      </div>

                      <div className="mt-6 space-y-6">
                        <div className="rounded-lg bg-card p-6 shadow-sm">
                          <h3 className="mb-3 flex items-center text-lg font-medium">
                            <ArrowLeft className="mr-2 h-5 w-5 text-primary" />
                            Input Format
                          </h3>
                          <div className="overflow-x-auto rounded-md bg-muted p-4 font-mono text-sm">
                            {challenge.input_format || challenge.inputFormat}
                          </div>
                        </div>

                        <div className="rounded-lg bg-card p-6 shadow-sm">
                          <h3 className="mb-3 flex items-center text-lg font-medium">
                            <ArrowLeft className="mr-2 h-5 w-5 rotate-180 text-primary" />
                            Output Format
                          </h3>
                          <div className="overflow-x-auto rounded-md bg-muted p-4 font-mono text-sm">
                            {challenge.output_format || challenge.outputFormat}
                          </div>
                        </div>

                        <div className="rounded-lg bg-card p-6 shadow-sm">
                          <h3 className="mb-3 flex items-center text-lg font-medium">
                            <AlertCircle className="mr-2 h-5 w-5 text-primary" />
                            Constraints
                          </h3>
                          <div className="overflow-x-auto rounded-md bg-muted p-4 font-mono text-sm">
                            {challenge.constraints ||
                              'No constraints specified.'}
                          </div>
                        </div>

                        <div className="rounded-lg bg-card p-6 shadow-sm">
                          <h3 className="mb-3 flex items-center text-lg font-medium">
                            <Code className="mr-2 h-5 w-5 text-primary" />
                            Function Signature
                          </h3>
                          <div className="overflow-x-auto rounded-md bg-muted p-4 font-mono text-sm">
                            {challenge.function_signature ||
                              'No function signature specified.'}
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="test-cases" className="mt-0">
                    <div className="rounded-lg bg-card p-6 shadow-sm">
                      <h3 className="mb-4 flex items-center text-xl font-medium">
                        <Beaker className="mr-2 h-5 w-5 text-primary" />
                        Test Cases
                      </h3>
                      <p className="mb-6 text-muted-foreground">
                        These test cases will be used to validate your solution.
                        Your code must pass all test cases to be accepted.
                      </p>
                      <TestCases challengeId={id} />
                    </div>
                  </TabsContent>

                  <TabsContent value="submissions" className="mt-0">
                    <SubmissionHistory challengeId={id} />
                  </TabsContent>

                  <TabsContent value="solution" className="mt-0">
                    {challenge.solutions &&
                    Object.keys(challenge.solutions).length > 0 ? (
                      <div className="space-y-6">
                        <div className="rounded-lg bg-card p-6 shadow-sm">
                          <h3 className="mb-4 text-xl font-medium">
                            Official Solution
                          </h3>
                          <p className="mb-6 text-muted-foreground">
                            Here&apos;s the official solution to this challenge.
                            Remember that there are often multiple ways to solve
                            a problem.
                          </p>

                          <div className="space-y-4">
                            <Select
                              value={solutionLanguage}
                              onValueChange={(value) =>
                                setSolutionLanguage(value)
                              }
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Select Language" />
                              </SelectTrigger>
                              <SelectContent>
                                {Object.keys(challenge.solutions).map(
                                  (lang) => (
                                    <SelectItem key={lang} value={lang}>
                                      {lang.charAt(0).toUpperCase() +
                                        lang.slice(1)}
                                    </SelectItem>
                                  ),
                                )}
                              </SelectContent>
                            </Select>

                            <div className="mt-4">
                              <CodeEditor
                                value={
                                  challenge.solutions[solutionLanguage] ||
                                  'Solution not available in this language'
                                }
                                onChange={() => {}} // Read-only
                                language={solutionLanguage}
                                height="300px"
                                readOnly={true}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="rounded-lg bg-card p-6 shadow-sm">
                          <h3 className="mb-3 flex items-center text-lg font-medium">
                            <BookOpen className="mr-2 h-5 w-5 text-primary" />
                            Explanation
                          </h3>
                          <div className="prose max-w-none dark:prose-invert">
                            <p>
                              This solution uses a sliding window approach to
                              track requests within the time window:
                            </p>
                            <ul>
                              <li>
                                We store a list of timestamps for each client
                              </li>
                              <li>
                                When a new request comes in, we remove expired
                                timestamps
                              </li>
                              <li>
                                If the number of timestamps is less than the
                                limit, we allow the request
                              </li>
                              <li>Otherwise, we reject the request</li>
                            </ul>
                            <p>
                              The time complexity is O(n) where n is the number
                              of requests in the time window.
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex min-h-[300px] flex-col items-center justify-center rounded-lg bg-muted/30 p-8 text-center">
                        <div className="bg-primary/10 rounded-full p-4">
                          <BookOpen className="h-10 w-10 text-primary" />
                        </div>
                        <h3 className="mt-6 text-xl font-medium">
                          Solution will be available after you solve the
                          challenge
                        </h3>
                        <p className="mt-3 max-w-md text-muted-foreground">
                          Submit a correct solution to unlock the official
                          solution and explanations from our experts.
                        </p>
                        <Button
                          onClick={() => setActiveTab('problem')}
                          className="mt-6"
                        >
                          Back to Problem
                        </Button>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="hints" className="mt-0">
                    <div className="rounded-lg bg-card p-6 shadow-sm">
                      <h3 className="mb-4 flex items-center text-xl font-medium">
                        <LightbulbIcon className="mr-2 h-5 w-5 text-primary" />
                        Challenge Hints
                      </h3>
                      <p className="mb-6 text-muted-foreground">
                        Need help? Unlock hints to guide you toward the
                        solution, but be aware that using hints will reduce your
                        points for this challenge.
                      </p>
                      <Hints
                        challengeId={id}
                        totalPoints={challenge.points || 100}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="leaderboard" className="mt-0">
                    <div className="rounded-lg bg-card p-6 shadow-sm">
                      <h3 className="mb-4 flex items-center text-xl font-medium">
                        <Award className="mr-2 h-5 w-5 text-primary" />
                        Challenge Leaderboard
                      </h3>
                      <p className="mb-6 text-muted-foreground">
                        See how your solution compares to others. The
                        leaderboard shows the fastest, most memory-efficient,
                        and highest-scoring solutions.
                      </p>
                      <ChallengeLeaderboard challengeId={id} />
                    </div>
                  </TabsContent>

                  <TabsContent value="discussion" className="mt-0">
                    <DiscussionSection
                      challengeId={id}
                      currentUserId={user?.id}
                    />
                  </TabsContent>
                </CardContent>
              </Tabs>
            </Card>
          </motion.div>

          {/* Right Column - Code Editor */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="order-1 lg:order-2 lg:col-span-2"
          >
            <div className="lg:sticky lg:top-20">
              <Card className="overflow-hidden border-none shadow-lg">
                <CardHeader className="border-b bg-muted/40 px-6 py-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-medium">
                      Your Solution
                    </CardTitle>
                    <Select
                      value={language}
                      onValueChange={handleLanguageChange}
                    >
                      <SelectTrigger className="w-[140px]">
                        <SelectValue placeholder="Select Language" />
                      </SelectTrigger>
                      <SelectContent>
                        {getSupportedLanguages().map((lang) => (
                          <SelectItem key={lang} value={lang}>
                            {getLanguageDisplayName(lang)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardHeader>
                <CardContent className="p-0">
                  <CodeEditor
                    value={code}
                    onChange={setCode}
                    language={language}
                    height="400px"
                    autoSaveKey={`challenge-${id}-${language}`}
                    onRun={handleRunCode}
                    onSubmit={handleSubmit}
                    isRunning={isRunning}
                    isSubmitting={isSubmitting}
                  />

                  {output && (
                    <div className="border-t bg-muted/30 p-4">
                      <h3 className="mb-2 flex items-center text-sm font-medium">
                        <Terminal className="mr-2 h-4 w-4 text-primary" />
                        Output
                      </h3>
                      <div className="max-h-[150px] overflow-auto rounded-md bg-muted p-3 font-mono text-sm">
                        {output}
                      </div>
                    </div>
                  )}
                </CardContent>
                <CardContent className="border-t bg-muted/40 p-4">
                  <div className="flex w-full flex-col gap-3 sm:flex-row">
                    <Button
                      onClick={handleRunCode}
                      disabled={isRunning}
                      variant="secondary"
                      className="flex-1"
                      size="sm"
                    >
                      {isRunning ? (
                        <>
                          <span className="mr-2 animate-spin">⟳</span>
                          <span className="sm:inline">Running</span>
                        </>
                      ) : (
                        <>
                          <Play className="mr-2 h-4 w-4" />
                          <span>Run Code</span>
                          <span className="hidden sm:inline">
                            {' '}
                            (Ctrl+Enter)
                          </span>
                        </>
                      )}
                    </Button>
                    <Button
                      onClick={handleSubmit}
                      disabled={isSubmitting}
                      variant="default"
                      className="to-primary/80 hover:from-primary/90 flex-1 bg-gradient-to-r from-primary hover:to-primary"
                      size="sm"
                    >
                      {isSubmitting ? (
                        <>
                          <span className="mr-2 animate-spin">⟳</span>
                          <span className="sm:inline">Submitting</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          <span>Submit</span>
                          <span className="hidden sm:inline">
                            {' '}
                            (Ctrl+Shift+Enter)
                          </span>
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Related Roadmaps Section */}
      <div className="container mt-12">
        <RelatedRoadmaps challengeId={id} />
      </div>

      {/* Submission Result Modal */}
      <SubmissionResultModal
        isOpen={showSubmissionModal}
        onClose={() => setShowSubmissionModal(false)}
        result={submissionResult}
      />
    </div>
  );
}
