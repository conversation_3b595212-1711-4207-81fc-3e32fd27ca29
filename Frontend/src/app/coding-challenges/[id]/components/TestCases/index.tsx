/**
 * @file index.tsx
 * @description React component for TestCases
 */
'use client';

import { useEffect, useId, useState } from 'react';

import {
  AlertTriangle,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  EyeOff,
  XCircle,
} from 'lucide-react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TestCase, useTestCases } from '@/hooks/useChallengeService';
import { getAriaTabPanelProps, getAriaTabProps } from '@/utils/accessibility';

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

/**
 * @file index.tsx
 * @description React component for TestCases
 */

interface TestCasesProps {
  challengeId: string;
  submissionResults?: {
    passed: boolean;
    name?: string;
    input?: string;
    expected_output?: string;
    actual_output?: string;
    error?: string;
    is_hidden?: boolean;
    test_case_id?: string;
  }[];
  isSubmissionView?: boolean;
}

export default function TestCases({
  challengeId,
  submissionResults,
  isSubmissionView = false,
}: TestCasesProps) {
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('sample');
  const [expandedTestCases, setExpandedTestCases] = useState<
    Record<string, boolean>
  >({});

  // Generate unique IDs for accessibility
  const baseId = useId();
  const sampleTabId = `${baseId}-sample-tab`;
  const hiddenTabId = `${baseId}-hidden-tab`;

  // Use the test cases hook
  const { fetchTestCases } = useTestCases(challengeId);

  // Fetch test cases
  const loadTestCases = async () => {
    setIsLoading(true);
    try {
      const cases = await fetchTestCases();
      setTestCases(cases);
    } catch (error) {
      console.error('Error fetching test cases:', error);
      toast.error('Failed to load test cases');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!isSubmissionView) {
      loadTestCases();
    } else {
      setIsLoading(false);
    }
  }, [challengeId, isSubmissionView]);

  // Toggle test case expansion
  const toggleTestCase = (id: string) => {
    setExpandedTestCases((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Filter test cases by type
  const sampleTestCases = testCases.filter((tc) => !tc.is_hidden);
  const hiddenTestCases = testCases.filter((tc) => tc.is_hidden);

  // Render loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-5 w-32" />
                <Skeleton className="h-5 w-24" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Render submission results view
  if (isSubmissionView && submissionResults) {
    const passedTests = submissionResults.filter(
      (result) => result.passed,
    ).length;
    const totalTests = submissionResults.length;
    const passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

    // Generate unique IDs for each test case
    const testResultsId = `${baseId}-test-results`;

    return (
      <div className="space-y-4">
        <div
          className="flex items-center justify-between"
          role="status"
          aria-live="polite"
        >
          <h3 className="text-lg font-semibold" id={testResultsId}>
            Test Results: {passedTests}/{totalTests} Passed
          </h3>
          <Badge
            variant={passRate === 100 ? 'outline' : 'destructive'}
            aria-label={`${passRate.toFixed(0)}% success rate`}
          >
            {passRate.toFixed(0)}% Success
          </Badge>
        </div>

        <div className="space-y-2" aria-labelledby={testResultsId}>
          {submissionResults.map((result, index) => {
            const testCaseId = `${baseId}-test-case-${index}`;
            const isExpanded = expandedTestCases[testCaseId] || false;

            return (
              <Collapsible
                key={index}
                className={`rounded-md border p-2 ${
                  result.passed
                    ? 'border-green-200 bg-green-50 dark:border-green-900 dark:bg-green-900/20'
                    : 'border-red-200 bg-red-50 dark:border-red-900 dark:bg-red-900/20'
                }`}
                open={isExpanded}
                onOpenChange={(open) => {
                  setExpandedTestCases((prev) => ({
                    ...prev,
                    [testCaseId]: open,
                  }));
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {result.passed ? (
                      <CheckCircle
                        className="h-4 w-4 text-green-500"
                        aria-hidden="true"
                      />
                    ) : (
                      <XCircle
                        className="text-red-500 h-4 w-4"
                        aria-hidden="true"
                      />
                    )}
                    <span className="font-medium" id={testCaseId}>
                      {result.name || `Test Case ${index + 1}`}
                      {result.is_hidden && (
                        <span className="ml-2 text-xs text-muted-foreground">
                          (hidden)
                        </span>
                      )}
                    </span>
                  </div>
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 w-7 p-0"
                      aria-expanded={isExpanded}
                      aria-controls={`${testCaseId}-content`}
                      aria-label={`${isExpanded ? 'Hide' : 'Show'} details for ${result.name || `Test Case ${index + 1}`}`}
                    >
                      <ChevronDown className="h-4 w-4" aria-hidden="true" />
                    </Button>
                  </CollapsibleTrigger>
                </div>
                <CollapsibleContent
                  className="mt-2 space-y-1 text-xs"
                  id={`${testCaseId}-content`}
                >
                  {!result.is_hidden ? (
                    <>
                      {result.input && (
                        <div
                          role="group"
                          aria-labelledby={`${testCaseId}-input-label`}
                        >
                          <span
                            id={`${testCaseId}-input-label`}
                            className="font-medium"
                          >
                            Input:
                          </span>{' '}
                          <code className="rounded bg-muted px-1 py-0.5">
                            {result.input}
                          </code>
                        </div>
                      )}
                      {result.expected_output && (
                        <div
                          role="group"
                          aria-labelledby={`${testCaseId}-expected-label`}
                        >
                          <span
                            id={`${testCaseId}-expected-label`}
                            className="font-medium"
                          >
                            Expected:
                          </span>{' '}
                          <code className="rounded bg-muted px-1 py-0.5">
                            {result.expected_output}
                          </code>
                        </div>
                      )}
                      {result.actual_output && (
                        <div
                          role="group"
                          aria-labelledby={`${testCaseId}-actual-label`}
                        >
                          <span
                            id={`${testCaseId}-actual-label`}
                            className="font-medium"
                          >
                            Your output:
                          </span>{' '}
                          <code className="rounded bg-muted px-1 py-0.5">
                            {result.actual_output}
                          </code>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="flex items-center text-muted-foreground">
                      <EyeOff className="mr-1 h-3 w-3" aria-hidden="true" />
                      <span>Test case details are hidden</span>
                    </div>
                  )}
                  {result.error && (
                    <div className="text-red-600" role="alert">
                      <span className="font-medium">Error:</span> {result.error}
                    </div>
                  )}
                </CollapsibleContent>
              </Collapsible>
            );
          })}
        </div>
      </div>
    );
  }

  // Render empty state
  if (testCases.length === 0) {
    return (
      <Card className="border-dashed bg-muted/50">
        <CardHeader>
          <CardTitle>No Test Cases Available</CardTitle>
        </CardHeader>
        <CardContent className="text-center text-muted-foreground">
          No test cases have been defined for this challenge yet.
        </CardContent>
      </Card>
    );
  }

  // Render normal view
  return (
    <div className="space-y-4">
      <Tabs
        defaultValue="sample"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="mb-4" role="tablist" aria-label="Test case types">
          <TabsTrigger
            value="sample"
            className="relative"
            {...getAriaTabProps(sampleTabId, activeTab === 'sample')}
          >
            Sample Tests
            <Badge
              variant="secondary"
              className="ml-2"
              aria-label={`${sampleTestCases.length} sample tests`}
            >
              {sampleTestCases.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger
            value="hidden"
            className="relative"
            {...getAriaTabProps(hiddenTabId, activeTab === 'hidden')}
          >
            Hidden Tests
            <Badge
              variant="secondary"
              className="ml-2"
              aria-label={`${hiddenTestCases.length} hidden tests`}
            >
              {hiddenTestCases.length}
            </Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="sample"
          className="space-y-2"
          {...getAriaTabPanelProps(sampleTabId, activeTab === 'sample')}
        >
          {sampleTestCases.length > 0 ? (
            sampleTestCases.map((testCase) => {
              const isExpanded = expandedTestCases[testCase.id] || false;
              const testCaseContentId = `test-case-${testCase.id}-content`;

              return (
                <Card key={testCase.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle
                        className="text-base font-medium"
                        id={`test-case-${testCase.id}-title`}
                      >
                        {testCase.name || `Test Case ${testCase.id}`}
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleTestCase(testCase.id)}
                        aria-expanded={isExpanded}
                        aria-controls={testCaseContentId}
                        aria-label={`${isExpanded ? 'Hide' : 'Show'} details for ${testCase.name || `Test Case ${testCase.id}`}`}
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" aria-hidden="true" />
                        ) : (
                          <ChevronRight
                            className="h-4 w-4"
                            aria-hidden="true"
                          />
                        )}
                      </Button>
                    </div>
                  </CardHeader>
                  {isExpanded && (
                    <CardContent
                      className="space-y-2 text-sm"
                      id={testCaseContentId}
                    >
                      <div
                        role="group"
                        aria-labelledby={`test-case-${testCase.id}-input-label`}
                      >
                        <span
                          id={`test-case-${testCase.id}-input-label`}
                          className="font-medium"
                        >
                          Input:
                        </span>
                        <pre className="mt-1 overflow-x-auto rounded-md bg-muted p-2 text-xs">
                          {testCase.input}
                        </pre>
                      </div>
                      <div
                        role="group"
                        aria-labelledby={`test-case-${testCase.id}-output-label`}
                      >
                        <span
                          id={`test-case-${testCase.id}-output-label`}
                          className="font-medium"
                        >
                          Expected Output:
                        </span>
                        <pre className="mt-1 overflow-x-auto rounded-md bg-muted p-2 text-xs">
                          {testCase.expected_output}
                        </pre>
                      </div>
                    </CardContent>
                  )}
                </Card>
              );
            })
          ) : (
            <div
              className="rounded-md border border-dashed p-6 text-center"
              role="status"
            >
              <p className="text-muted-foreground">
                No sample test cases available
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent
          value="hidden"
          className="space-y-2"
          {...getAriaTabPanelProps(hiddenTabId, activeTab === 'hidden')}
        >
          {hiddenTestCases.length > 0 ? (
            <>
              <div
                className="mb-4 flex items-center rounded-md bg-muted p-3 text-sm"
                role="alert"
              >
                <AlertTriangle
                  className="mr-2 h-4 w-4 text-yellow-500"
                  aria-hidden="true"
                />
                <p>
                  Hidden test cases are used to validate your solution but their
                  details are not visible. Your solution must pass all test
                  cases to be accepted.
                </p>
              </div>
              {hiddenTestCases.map((testCase) => (
                <Card key={testCase.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle
                        className="flex items-center text-base font-medium"
                        id={`hidden-test-case-${testCase.id}-title`}
                      >
                        <EyeOff
                          className="mr-2 h-4 w-4 text-muted-foreground"
                          aria-hidden="true"
                        />
                        {testCase.name || `Hidden Test Case ${testCase.id}`}
                      </CardTitle>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </>
          ) : (
            <div
              className="rounded-md border border-dashed p-6 text-center"
              role="status"
            >
              <p className="text-muted-foreground">
                No hidden test cases available
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
