/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/[id] route
 */
import CodingChallenge from './components/CodingChallenge';
import { generateMetadata } from './metadata';

// Export the metadata for this page
export { generateMetadata };

export default async function ViewChallengePage({
  params,
}: {
  params: { id: string };
}) {
  const { id } = params;

  if (!id) {
    return <div className="container mx-auto p-4">Challenge not found.</div>;
  }

  return <CodingChallenge id={id} />;
}
