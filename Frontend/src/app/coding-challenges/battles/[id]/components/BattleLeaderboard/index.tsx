/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */
'use client';

import { useState } from 'react';

import Image from 'next/image';

import { motion } from 'framer-motion';
import { CheckCircle2, Medal, Search, Trophy, User } from 'lucide-react';

import { IBattleLeaderboardEntry } from '@/app/coding-challenges/types/battle';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useBattle } from '@/hooks/useBattles';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

/**
 * @file index.tsx
 * @description React component for BattleLeaderboard
 */

interface BattleLeaderboardProps {
  battleId: string;
  className?: string;
}

export default function BattleLeaderboard({
  battleId,
  className = '',
}: BattleLeaderboardProps) {
  const { battle, isLoading, error } = useBattle(battleId);

  const [searchTerm, setSearchTerm] = useState('');

  // Filter leaderboard entries by search term
  const filteredLeaderboard =
    battle?.leaderboard?.filter(
      (entry) =>
        entry.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        entry.display_name.toLowerCase().includes(searchTerm.toLowerCase()),
    ) || [];

  // Get rank badge
  const getRankBadge = (rank: number) => {
    if (rank === 1) {
      return (
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
          <Trophy className="h-3.5 w-3.5" />
        </div>
      );
    }

    if (rank === 2) {
      return (
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400">
          <Medal className="h-3.5 w-3.5" />
        </div>
      );
    }

    if (rank === 3) {
      return (
        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400">
          <Medal className="h-3.5 w-3.5" />
        </div>
      );
    }

    return (
      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-muted text-muted-foreground">
        <span className="text-xs font-medium">{rank}</span>
      </div>
    );
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-10 w-full max-w-xs" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={cn(
          'border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center',
          className,
        )}
      >
        <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
          Failed to load battle leaderboard
        </h2>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  // No battle found
  if (!battle) {
    return (
      <div
        className={cn(
          'rounded-lg border border-dashed p-6 text-center',
          className,
        )}
      >
        <h2 className="mb-2 text-lg font-medium">Battle not found</h2>
        <p className="text-muted-foreground">
          The battle you're looking for doesn't exist.
        </p>
      </div>
    );
  }

  // No leaderboard available
  if (!battle.leaderboard || battle.leaderboard.length === 0) {
    return (
      <div className={cn('space-y-4', className)}>
        <h2 className="text-2xl font-bold">Leaderboard</h2>
        <div className="rounded-lg border border-dashed p-6 text-center">
          <h3 className="mb-2 text-lg font-medium">No leaderboard available</h3>
          <p className="text-muted-foreground">
            The leaderboard for this battle is not available yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div className="space-y-2">
        <h2 className="flex items-center text-2xl font-bold">
          <Trophy className="mr-2 h-6 w-6 text-yellow-500" />
          Leaderboard
        </h2>
        <p className="text-muted-foreground">
          Current standings for this battle
        </p>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search participants..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[60px]">Rank</TableHead>
              <TableHead>Participant</TableHead>
              <TableHead className="text-right">Challenges</TableHead>
              <TableHead className="text-right">Points</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLeaderboard.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  No participants found
                </TableCell>
              </TableRow>
            ) : (
              filteredLeaderboard.map((entry, index) => (
                <motion.tr
                  key={entry.user_id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className={cn(
                    'group border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',
                    entry.is_current_user && 'bg-primary/5',
                  )}
                >
                  <TableCell className="py-3">
                    <div className="flex items-center">
                      {getRankBadge(entry.rank)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {entry.avatar_url ? (
                        <div className="h-8 w-8 overflow-hidden rounded-full">
                          <Image
                            src={entry.avatar_url}
                            alt={entry.display_name}
                            width={32}
                            height={32}
                            className="h-full w-full object-cover"
                          />
                        </div>
                      ) : (
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                          <User className="h-4 w-4 text-muted-foreground" />
                        </div>
                      )}
                      <div>
                        <div className="font-medium">
                          {entry.display_name}
                          {entry.is_current_user && (
                            <Badge variant="secondary" className="ml-2">
                              You
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          @{entry.username}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end">
                      <CheckCircle2 className="mr-1.5 h-4 w-4 text-green-500" />
                      <span>{entry.challenges_completed}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    <div className="flex items-center justify-end">
                      <Trophy className="mr-1.5 h-4 w-4 text-yellow-500" />
                      <span>{entry.points}</span>
                    </div>
                  </TableCell>
                </motion.tr>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
