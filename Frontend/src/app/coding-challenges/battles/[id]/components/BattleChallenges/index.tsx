/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */
'use client';

import { useState } from 'react';

import Link from 'next/link';

import { motion } from 'framer-motion';
import {
  Al<PERSON><PERSON>riangle,
  ArrowR<PERSON>,
  CheckCircle2,
  Clock,
  Lock,
  Trophy,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useBattle } from '@/hooks/useBattles';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

/**
 * @file index.tsx
 * @description React component for BattleChallenges
 */

interface BattleChallengesProps {
  battleId: string;
  className?: string;
}

export default function BattleChallenges({
  battleId,
  className = '',
}: BattleChallengesProps) {
  const { battle, isLoading, error, getNextChallenge } = useBattle(battleId);

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'HARD':
        return 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  // Format time remaining
  const formatTimeRemaining = (endTime: string) => {
    const end = new Date(endTime).getTime();
    const now = new Date().getTime();
    const diff = end - now;

    if (diff <= 0) return 'Ended';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h remaining`;
    }

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    }

    return `${minutes}m remaining`;
  };

  // Calculate progress
  const calculateProgress = () => {
    if (!battle || !battle.challenges) return 0;

    const completedChallenges = battle.challenges.filter(
      (c) => c.is_completed,
    ).length;
    return (completedChallenges / battle.challenges.length) * 100;
  };

  // Get next challenge
  const nextChallenge = getNextChallenge();

  // Loading state
  if (isLoading) {
    return (
      <div className={cn('space-y-4', className)}>
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-full max-w-md" />
        <Skeleton className="h-2 w-full" />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={cn(
          'border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 rounded-lg border p-6 text-center',
          className,
        )}
      >
        <h2 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
          Failed to load battle challenges
        </h2>
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  // No battle found
  if (!battle) {
    return (
      <div
        className={cn(
          'rounded-lg border border-dashed p-6 text-center',
          className,
        )}
      >
        <h2 className="mb-2 text-lg font-medium">Battle not found</h2>
        <p className="text-muted-foreground">
          The battle you're looking for doesn't exist.
        </p>
      </div>
    );
  }

  // Battle ended
  const isBattleEnded = new Date(battle.end_time) < new Date();

  return (
    <div className={cn('space-y-6', className)}>
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">{battle.title}</h2>
        <p className="text-muted-foreground">{battle.description}</p>

        <div className="mt-4 flex flex-wrap items-center gap-4">
          <div className="flex items-center rounded-md bg-muted px-3 py-1 text-sm">
            <Trophy className="mr-1.5 h-4 w-4 text-yellow-500" />
            <span>{battle.challenges.length} Challenges</span>
          </div>

          <div className="flex items-center rounded-md bg-muted px-3 py-1 text-sm">
            <Clock className="mr-1.5 h-4 w-4 text-blue-500" />
            <span>{formatTimeRemaining(battle.end_time)}</span>
          </div>
        </div>

        <div className="mt-4 space-y-1">
          <div className="flex items-center justify-between text-sm">
            <span>Your progress</span>
            <span>
              {battle.challenges.filter((c) => c.is_completed).length}/
              {battle.challenges.length} completed
            </span>
          </div>
          <Progress value={calculateProgress()} className="h-2" />
        </div>
      </div>

      {isBattleEnded && (
        <div className="flex items-start rounded-md border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-900/50 dark:bg-yellow-900/20">
          <AlertTriangle className="mr-3 mt-0.5 h-5 w-5 flex-shrink-0 text-yellow-600 dark:text-yellow-400" />
          <div>
            <h3 className="font-medium text-yellow-800 dark:text-yellow-300">
              Battle has ended
            </h3>
            <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-400">
              This battle has ended. You can still view the challenges and your
              submissions, but you cannot make new submissions.
            </p>
          </div>
        </div>
      )}

      {/* Next challenge highlight */}
      {nextChallenge && !isBattleEnded && (
        <div className="mb-6">
          <Card className="border-blue-200 bg-blue-50 dark:border-blue-900/50 dark:bg-blue-950/30">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>Next Challenge</span>
                </Badge>
                <Badge
                  variant="outline"
                  className={getDifficultyColor(nextChallenge.difficulty)}
                >
                  {nextChallenge.difficulty}
                </Badge>
              </div>
              <CardTitle className="mt-2 text-xl">
                {nextChallenge.title}
              </CardTitle>
              <CardDescription>{nextChallenge.description}</CardDescription>
            </CardHeader>

            <CardContent className="pb-2">
              <div className="flex items-center text-sm text-muted-foreground">
                <Trophy className="mr-1 h-4 w-4 text-yellow-500" />
                <span>{nextChallenge.points} points</span>
              </div>
            </CardContent>

            <CardFooter>
              <Button asChild className="w-full">
                <Link
                  href={`/coding-challenges/${nextChallenge.id}?battle=${battleId}`}
                  className="flex items-center justify-center"
                >
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Start Challenge
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}

      {/* All challenges */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">All Challenges</h3>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {battle.challenges.map((challenge, index) => (
            <motion.div
              key={challenge.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card
                className={cn(
                  'h-full transition-all hover:shadow-md',
                  challenge.is_completed &&
                    'border-green-200 dark:border-green-900/50',
                  challenge.is_locked &&
                    'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-900/30',
                )}
              >
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="mb-1 flex items-center">
                        {challenge.is_completed ? (
                          <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
                        ) : challenge.is_locked ? (
                          <Lock className="mr-2 h-5 w-5 text-gray-400" />
                        ) : (
                          <Badge variant="outline" className="mr-2">
                            #{challenge.order}
                          </Badge>
                        )}
                        <Badge
                          variant="outline"
                          className={getDifficultyColor(challenge.difficulty)}
                        >
                          {challenge.difficulty}
                        </Badge>
                      </div>
                      <CardTitle
                        className={cn(
                          'line-clamp-1 text-xl',
                          challenge.is_locked && 'text-muted-foreground',
                        )}
                      >
                        {challenge.title}
                      </CardTitle>
                    </div>
                    <div className="flex items-center">
                      <Trophy className="mr-1 h-5 w-5 text-yellow-500" />
                      <span className="font-medium">
                        {challenge.points} pts
                      </span>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pb-2">
                  <p
                    className={cn(
                      'line-clamp-2 text-sm',
                      challenge.is_locked ? 'text-muted-foreground' : '',
                    )}
                  >
                    {challenge.description}
                  </p>

                  {challenge.is_completed && (
                    <div className="mt-2 rounded-md bg-green-50 p-2 text-sm text-green-800 dark:bg-green-950/30 dark:text-green-300">
                      <div className="flex items-center">
                        <CheckCircle2 className="mr-1 h-4 w-4 text-green-500" />
                        <span>You've completed this challenge</span>
                      </div>
                    </div>
                  )}
                </CardContent>

                <CardFooter>
                  {challenge.is_locked ? (
                    <Button disabled variant="outline" className="w-full">
                      <Lock className="mr-2 h-4 w-4" />
                      Locked
                    </Button>
                  ) : (
                    <Button
                      asChild
                      variant={challenge.is_completed ? 'outline' : 'default'}
                      className="w-full"
                      disabled={isBattleEnded && !challenge.is_completed}
                    >
                      <Link
                        href={`/coding-challenges/${challenge.id}?battle=${battleId}`}
                        className="flex items-center justify-center"
                      >
                        {challenge.is_completed ? (
                          <>
                            <CheckCircle2 className="mr-2 h-4 w-4" />
                            View Solution
                          </>
                        ) : (
                          <>
                            <ArrowRight className="mr-2 h-4 w-4" />
                            Solve Challenge
                          </>
                        )}
                      </Link>
                    </Button>
                  )}
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
