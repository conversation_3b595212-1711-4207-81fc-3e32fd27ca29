/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */
'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { <PERSON><PERSON><PERSON><PERSON>, CheckCircle, Clock, Search } from 'lucide-react';

import ChallengeCard from '@/app/coding-challenges/components/ChallengeCard';
import ChallengeSkeleton from '@/app/coding-challenges/components/ChallengeSkeleton';
import EmptyChallenges from '@/app/coding-challenges/components/EmptyChallenges';
import ChallengeStats from '@/components/ChallengeStats';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAxiosGet } from '@/hooks/useAxios';
import { useCombinedChallengeProgress } from '@/hooks/useChallengeProgress';

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

/**
 * @file page.tsx
 * @description Next.js page for coding-challenges/my-challenges route
 */

interface Challenge {
  id: string;
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  points: number;
  created_at: string;
  completion_rate?: number;
  tags?: string[];
}

export default function MyChallengesPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [filteredChallenges, setFilteredChallenges] = useState<Challenge[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get user progress
  const {
    isLoading: isLoadingProgress,
    userProgress,
    userStats,
    getChallengesByStatus,
  } = useCombinedChallengeProgress();

  // Get challenges API
  const [getChallenges] = useAxiosGet<{ challenges: Challenge[] }>(
    '/challenges',
  );

  // Fetch challenges
  const fetchChallenges = async () => {
    setIsLoading(true);
    try {
      const response = await getChallenges();
      if (response.data && response.data.challenges) {
        setChallenges(response.data.challenges);
      }
    } catch (error) {
      console.error('Error fetching challenges:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter challenges based on active tab and search query
  useEffect(() => {
    if (challenges.length === 0) return;

    let filtered = [...challenges];

    // Filter by status
    if (activeTab !== 'all') {
      const challengeIds = getChallengesByStatus(
        activeTab as 'completed' | 'in_progress' | 'not_started',
      );
      filtered = filtered.filter((challenge) =>
        challengeIds.includes(challenge.id),
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (challenge) =>
          challenge.title.toLowerCase().includes(query) ||
          challenge.description.toLowerCase().includes(query) ||
          challenge.category.toLowerCase().includes(query) ||
          challenge.tags?.some((tag) => tag.toLowerCase().includes(query)),
      );
    }

    setFilteredChallenges(filtered);
  }, [challenges, activeTab, searchQuery, userProgress]);

  // Fetch challenges on mount
  useEffect(() => {
    fetchChallenges();
  }, []);

  // Loading state
  if (isLoading || isLoadingProgress) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-6 flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-4"
            onClick={() => router.push('/coding-challenges')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Challenges
          </Button>
          <h1 className="text-2xl font-bold">My Challenges</h1>
        </div>

        <div className="mb-8">
          <ChallengeStats stats={null} isLoading={true} />
        </div>

        <div className="mb-6 space-y-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <Tabs defaultValue="all" className="w-full sm:w-auto">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="completed">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Completed
                </TabsTrigger>
                <TabsTrigger value="in_progress">
                  <Clock className="mr-2 h-4 w-4" />
                  In Progress
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search challenges..."
                className="pl-8"
                disabled
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <ChallengeSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  // Empty state
  if (filteredChallenges.length === 0) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-6 flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-4"
            onClick={() => router.push('/coding-challenges')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Challenges
          </Button>
          <h1 className="text-2xl font-bold">My Challenges</h1>
        </div>

        <div className="mb-8">
          <ChallengeStats stats={userStats} />
        </div>

        <div className="mb-6 space-y-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full sm:w-auto"
            >
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="completed">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Completed
                </TabsTrigger>
                <TabsTrigger value="in_progress">
                  <Clock className="mr-2 h-4 w-4" />
                  In Progress
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search challenges..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>

        <EmptyChallenges
        // title={
        //   searchQuery
        //     ? 'No matching challenges found'
        //     : activeTab === 'all'
        //       ? 'No challenges found'
        //       : activeTab === 'completed'
        //         ? 'No completed challenges yet'
        //         : 'No challenges in progress'
        // }
        // description={
        //   searchQuery
        //     ? `Try adjusting your search query or filters`
        //     : activeTab === 'all'
        //       ? 'Start solving coding challenges to track your progress'
        //       : activeTab === 'completed'
        //         ? 'Complete challenges to see them here'
        //         : 'Start working on challenges to see them here'
        // }
        // actionText={
        //   activeTab !== 'all' ? 'Browse All Challenges' : 'Explore Challenges'
        // }
        // actionHref="/coding-challenges"
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center">
        <Button
          variant="ghost"
          size="sm"
          className="mr-4"
          onClick={() => router.push('/coding-challenges')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Challenges
        </Button>
        <h1 className="text-2xl font-bold">My Challenges</h1>
      </div>

      <div className="mb-8">
        <ChallengeStats stats={userStats} />
      </div>

      <div className="mb-6 space-y-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full sm:w-auto"
          >
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="completed">
                <CheckCircle className="mr-2 h-4 w-4" />
                Completed
              </TabsTrigger>
              <TabsTrigger value="in_progress">
                <Clock className="mr-2 h-4 w-4" />
                In Progress
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search challenges..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {filteredChallenges.length} challenge
            {filteredChallenges.length !== 1 ? 's' : ''}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredChallenges.map((challenge) => (
          <ChallengeCard
            key={challenge.id}
            challenge={challenge}
            showProgress={true}
          />
        ))}
      </div>
    </div>
  );
}
