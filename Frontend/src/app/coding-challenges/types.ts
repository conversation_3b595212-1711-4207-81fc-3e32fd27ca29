/**
 * @file types.ts
 * @description Next.js page for coding-challenges route
 */
export interface IChallenge {
  id: string;
  title: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  points: number;
  category: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  author?: {
    id: string;
    name: string;
    username?: string;
  };
  completion_rate?: number;
  total_submissions?: number;
  successful_submissions?: number;
}

export interface ICollection {
  id: string;
  title: string;
  description: string;
  type: 'company' | 'topic' | 'difficulty' | 'series';
  image_url?: string;
  challenge_count: number;
  challenges?: IChallenge[];
  company?: {
    name: string;
    logo_url?: string;
  };
  progress?: {
    completed: number;
    total: number;
    percentage: number;
  };
  created_at: string;
  updated_at: string;
}

export interface ISubmission {
  id: string;
  challenge_id: string;
  user_id: string;
  language: string;
  code: string;
  status:
    | 'accepted'
    | 'wrong_answer'
    | 'runtime_error'
    | 'time_limit_exceeded'
    | 'compilation_error';
  runtime_ms?: number;
  memory_used_kb?: number;
  created_at: string;
  test_results?: {
    passed: boolean;
    input: string;
    expected_output: string;
    actual_output: string;
    error_message?: string;
  }[];
}

export interface ITestCase {
  id: string;
  challenge_id: string;
  name?: string;
  input: string;
  expected_output: string;
  is_hidden: boolean;
}

export interface IBoilerplate {
  id: string;
  challenge_id: string;
  language: string;
  code: string;
}

export type ChallengeStatus = 'not_started' | 'in_progress' | 'completed';

export interface IChallengeProgress {
  challenge_id: string;
  status: ChallengeStatus;
  progress?: number;
  last_submission_id?: string;
  last_submission_date?: string;
}
