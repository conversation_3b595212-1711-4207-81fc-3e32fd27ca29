/**
 * @file page.tsx
 * @description Next.js page for faq route
 */
'use client';

import { useState } from 'react';

import { motion } from 'framer-motion';

import { companyInfo } from '@/constants';

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

/**
 * @file page.tsx
 * @description Next.js page for faq route
 */

export default function FAQ() {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const faqs = [
    {
      question: 'What is the purpose of this website?',
      answer:
        'This website is designed to help engineering students enhance their coding skills through various resources, challenges, and community interactions. We provide roadmaps, tutorials, practice problems, and a supportive community to help you succeed in your engineering career.',
    },
    {
      question: 'How can I join the community?',
      answer:
        'You can join the community by signing up for an account and participating in forums, events, and collaboration opportunities. Once registered, you can interact with other members, join battles, participate in challenges, and contribute to discussions.',
    },
    {
      question: 'What types of coding challenges are available?',
      answer:
        'We offer a variety of coding challenges ranging from beginner to advanced levels across different programming languages. Our challenges cover data structures, algorithms, web development, machine learning, and more. New challenges are added regularly to keep you engaged and learning.',
    },
    {
      question: 'How can I track my progress?',
      answer:
        'You can track your progress through your profile page where all your activities, completed challenges, and achievements are displayed. We provide detailed analytics on your performance, strengths, and areas for improvement to help you grow as a developer.',
    },
    {
      question: 'Is there any cost to join?',
      answer:
        'No, joining the community and accessing the basic resources on this website is completely free. We offer premium features and content for a subscription fee, but the core learning experience is available to everyone at no cost.',
    },
    {
      question: 'How do I prepare for technical interviews?',
      answer:
        'We offer comprehensive interview preparation resources including practice problems, mock interviews, company-specific guides, and tips from industry professionals. Our battle zone feature also helps you practice coding under time pressure, similar to real interview conditions.',
    },
    {
      question: 'Can I create my own learning roadmap?',
      answer:
        'Yes, you can create custom roadmaps tailored to your learning goals. We also provide curated roadmaps for popular career paths like web development, data science, mobile app development, and more.',
    },
    {
      question: 'How do I report bugs or suggest features?',
      answer: `You can report bugs or suggest features through our support page or by contacting us directly at support@${companyInfo.name.toLowerCase()}.com. We value your feedback and continuously work to improve the platform.`,
    },
  ];

  const categories = [
    { id: 'all', name: 'All Questions' },
    { id: 'account', name: 'Account & Profile' },
    { id: 'features', name: 'Features & Services' },
    { id: 'technical', name: 'Technical Issues' },
    { id: 'billing', name: 'Billing & Subscription' },
  ];

  const [activeCategory, setActiveCategory] = useState('all');

  const handleToggle = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  return (
    <main className="bg-background py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <motion.div
          className="mb-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">
            Frequently Asked <span className="text-primary">Questions</span>
          </h1>
          <p className="mx-auto max-w-3xl text-lg text-muted-foreground">
            Find answers to common questions about {companyInfo.name} and how to
            make the most of our platform. Can&apos;t find what you&apos;re
            looking for? Contact our support team.
          </p>
        </motion.div>

        {/* Categories */}
        <motion.div
          className="mb-8 flex flex-wrap justify-center gap-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {categories.map((category) => (
            <button
              key={category.id}
              className={`rounded-full px-4 py-2 text-sm font-medium transition-colors ${activeCategory === category.id ? 'bg-primary text-white' : 'bg-card text-foreground hover:bg-muted'}`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
        </motion.div>

        {/* FAQ Items */}
        <motion.div
          className="mx-auto max-w-3xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="divide-y divide-border rounded-xl border border-border bg-card shadow-sm">
            {faqs.map((faq, index) => (
              <div key={index} className="px-6 py-4">
                <button
                  className="flex w-full items-center justify-between text-left text-lg font-semibold text-foreground focus:outline-none"
                  onClick={() => handleToggle(index)}
                >
                  <span>{faq.question}</span>
                  <svg
                    className={`h-5 w-5 text-primary transition-transform ${activeIndex === index ? 'rotate-180 transform' : ''}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                <motion.div
                  initial={false}
                  animate={{
                    height: activeIndex === index ? 'auto' : 0,
                    opacity: activeIndex === index ? 1 : 0,
                  }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <p className="mt-4 text-muted-foreground">{faq.answer}</p>
                </motion.div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Contact Section */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <h2 className="mb-4 text-2xl font-bold text-foreground">
            Still have questions?
          </h2>
          <p className="mx-auto mb-8 max-w-2xl text-muted-foreground">
            If you couldn&apos;t find the answer to your question, feel free to
            contact our support team. We&apos;re here to help!
          </p>
          <a
            href="/support"
            className="inline-flex items-center rounded-full bg-primary px-6 py-3 text-white transition-colors hover:bg-primary2"
          >
            Contact Support
          </a>
        </motion.div>
      </div>
    </main>
  );
}
