/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */
'use client';

import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { ParallaxProvider } from 'react-scroll-parallax';
import { toast } from 'react-toastify';

import { useParams, useSearchParams } from 'next/navigation';

import {
  Award,
  Bookmark,
  BookOpen,
  Clock,
  Heart,
  MessageCircle,
  Share2,
  Users,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAxiosGet } from '@/hooks/useAxios';
import { RoadmapAuthor } from '@/hooks/useRoadmapApi';
import { useRoadmapEnrollment } from '@/hooks/useRoadmapEnrollment';
import { useRoadmapSocial } from '@/hooks/useRoadmapSocial';
import { hideLoader, showLoader } from '@/lib/features/loader/loaderSlice';
import { cn } from '@/lib/utils';

import { CommentSection } from './components/CommentSection';
import { RoadmapSection } from './components/RoadmapSection';
import { Timeline } from './components/Timeline';

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

/**
 * @file page.tsx
 * @description Next.js page for career-roadmap/[id] route
 */

interface IRoadmap {
  id: string;
  main_concept: {
    id: string;
    name: string;
    description: string;
    subjects: {
      id: string;
      subject: {
        id: string;
        title: string;
        description: string;
        icon: React.ElementType;
      };
    }[];
  };
}

interface RoadmapAuthorDetails extends RoadmapAuthor {
  username: string;
  full_name: string | null;
  avatar_url: string | null;
}

interface RoadmapDetails {
  id: string;
  title: string;
  description: string;
  user?: RoadmapAuthorDetails;
  thumbnail?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  createdAt: string;
  updatedAt: string;
  isEnrolled?: boolean;
  isFeatured?: boolean;
  likesCount: number;
  commentsCount: number;
  bookmarksCount: number;
  isLiked: boolean;
  isBookmarked: boolean;
  enrollmentCount?: number;
  estimatedTime?: string;
  progress?: number;
  tags: string;
}

const RoadmapSkeleton = () => {
  return (
    <div className="space-y-8">
      {/* Hero Section Skeleton */}
      <div className="from-primary/5 relative rounded-2xl bg-gradient-to-b to-transparent p-8">
        <div className="grid gap-8 lg:grid-cols-2">
          {/* Left Column Skeleton */}
          <div className="space-y-6 rounded-2xl bg-white/60 p-8 dark:bg-slate-900/60">
            <div className="space-y-4">
              <div className="flex gap-2">
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-6 w-24" />
              </div>
              <Skeleton className="h-12 w-3/4" />
              <Skeleton className="h-24 w-full" />
            </div>
            <div className="flex flex-wrap gap-2">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-6 w-20" />
              ))}
            </div>
            <div className="flex items-center space-x-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          </div>

          {/* Right Column Skeleton */}
          <div className="space-y-6">
            <div className="rounded-2xl bg-white/60 p-8 dark:bg-slate-900/60">
              <div className="grid grid-cols-2 gap-6 sm:grid-cols-4">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="rounded-xl bg-white p-4 dark:bg-slate-800"
                  >
                    <Skeleton className="mx-auto h-6 w-6" />
                    <Skeleton className="mx-auto mt-2 h-8 w-12" />
                    <Skeleton className="mx-auto mt-1 h-3 w-16" />
                  </div>
                ))}
              </div>
              <div className="mt-8">
                <Skeleton className="h-2 w-full" />
              </div>
              <div className="mt-8 flex gap-4">
                <Skeleton className="h-10 flex-1" />
                <Skeleton className="h-10 flex-1" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Roadmap Content Skeleton */}
      <div className="space-y-12">
        {[1, 2, 3].map((section) => (
          <div key={section} className="relative pl-8">
            <div className="absolute left-0 top-0 h-full w-px bg-slate-200 dark:bg-slate-800" />
            <div className="absolute -left-2 top-2 h-4 w-4 rounded-full border-2 border-primary bg-white dark:bg-slate-950" />
            <div className="space-y-4">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-20 w-full" />
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {[1, 2, 3].map((item) => (
                  <div
                    key={item}
                    className="rounded-lg border p-4 dark:border-slate-800"
                  >
                    <Skeleton className="h-6 w-32" />
                    <Skeleton className="mt-2 h-16 w-full" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default function CareerPathPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const careerId = (params?.id as string) || '';
  const dispatch = useDispatch();
  const showComments = searchParams.get('comments') === 'open';
  const { handleLike, handleBookmark } = useRoadmapSocial();
  const [isLoading, setIsLoading] = useState(true);

  const [activeTab, setActiveTab] = useState(
    showComments ? 'comments' : 'content',
  );
  const [roadmap, setRoadmap] = useState<IRoadmap[]>([]);
  const [roadmapDetails, setRoadmapDetails] = useState<RoadmapDetails | null>(
    null,
  );

  const [getRoadmapDetails] = useAxiosGet<{
    roadMap: RoadmapDetails & {
      main_concepts: IRoadmap[];
    };
  }>('roadmaps/{{careerId}}');

  const { enrollInRoadmapHandler, isEnrolling } = useRoadmapEnrollment();

  const [socialActionLoading, setSocialActionLoading] = useState<{
    like: boolean;
    bookmark: boolean;
    enroll: boolean;
  }>({
    like: false,
    bookmark: false,
    enroll: false,
  });

  const [optimisticState, setOptimisticState] = useState<{
    isLiked: boolean;
    likesCount: number;
    isBookmarked: boolean;
    bookmarksCount: number;
  } | null>(null);

  useEffect(() => {
    if (roadmapDetails) {
      setOptimisticState({
        isLiked: roadmapDetails.isLiked,
        likesCount: roadmapDetails.likesCount,
        isBookmarked: roadmapDetails.isBookmarked,
        bookmarksCount: roadmapDetails.bookmarksCount,
      });
    }
  }, [roadmapDetails]);

  const fetchResources = async () => {
    setIsLoading(true);
    dispatch(showLoader('fetching roadmap'));
    try {
      const detailsResponse = await getRoadmapDetails({}, { careerId });
      const roadmapData = detailsResponse.data?.roadMap;

      if (roadmapData) {
        setRoadmapDetails(roadmapData);
        setRoadmap(roadmapData.main_concepts || []);
      }

      if (showComments) {
        setActiveTab('comments');
      }
    } catch (error) {
      console.error(error);
      toast.error('Error fetching resources. Please try again');
    }
    dispatch(hideLoader('fetching roadmap'));
    setIsLoading(false);
  };

  useEffect(() => {
    fetchResources();
  }, [careerId, showComments]);

  const handleCommentClick = () => {
    setActiveTab('comments');
  };

  const handleSocialAction = async (
    action: (id: string) => Promise<void>,
    type: 'like' | 'bookmark',
  ) => {
    if (socialActionLoading[type]) return;

    try {
      setSocialActionLoading((prev) => ({ ...prev, [type]: true }));

      // Optimistic update
      if (type === 'like') {
        setOptimisticState((prev) =>
          prev
            ? {
                ...prev,
                isLiked: !prev.isLiked,
                likesCount: prev.isLiked
                  ? prev.likesCount - 1
                  : prev.likesCount + 1,
              }
            : null,
        );
      } else {
        setOptimisticState((prev) =>
          prev
            ? {
                ...prev,
                isBookmarked: !prev.isBookmarked,
                bookmarksCount: prev.isBookmarked
                  ? prev.bookmarksCount - 1
                  : prev.bookmarksCount + 1,
              }
            : null,
        );
      }

      await action(careerId);
      const detailsResponse = await getRoadmapDetails({}, { careerId });
      const roadmapData = detailsResponse.data?.roadMap;

      if (roadmapData) {
        setRoadmapDetails(roadmapData);
        // Maintain the roadmap state from the main_concepts in the response
        setRoadmap(roadmapData.main_concepts || []);
      }
    } catch {
      // Revert optimistic update on error
      if (roadmapDetails) {
        setOptimisticState({
          isLiked: roadmapDetails.isLiked,
          likesCount: roadmapDetails.likesCount,
          isBookmarked: roadmapDetails.isBookmarked,
          bookmarksCount: roadmapDetails.bookmarksCount,
        });
      }
      toast.error(`Failed to ${type} roadmap`);
    } finally {
      setSocialActionLoading((prev) => ({ ...prev, [type]: false }));
    }
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: roadmapDetails?.title,
        text: roadmapDetails?.description,
        url: window.location.href,
      });
    } catch {
      // Fallback to copying to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  if (!roadmapDetails) return null;

  const tags = roadmapDetails.tags ? roadmapDetails.tags.split(',') : [];

  const currentState = optimisticState ||
    roadmapDetails || {
      isLiked: false,
      likesCount: 0,
      isBookmarked: false,
      bookmarksCount: 0,
    };

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-950">
      <ParallaxProvider>
        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <Card className="relative mt-8 border-none bg-white shadow-xl dark:bg-slate-900">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              {/* Sticky Tabs Navigation */}
              <div className="sticky top-0 z-40 bg-white shadow-md dark:bg-slate-900">
                <div className="border-b border-slate-200 px-6 dark:border-slate-800">
                  <TabsList className="h-16 w-full justify-start gap-8 bg-transparent">
                    <TabsTrigger
                      value="content"
                      className="group relative h-full data-[state=active]:bg-transparent"
                    >
                      <span className="relative z-10 font-medium text-slate-600 transition-colors group-data-[state=active]:text-primary dark:text-slate-400 dark:group-data-[state=active]:text-primary">
                        Content
                      </span>
                      <span className="absolute bottom-0 left-0 h-0.5 w-full bg-transparent transition-all duration-300 group-data-[state=active]:h-1 group-data-[state=active]:bg-primary" />
                    </TabsTrigger>
                    <TabsTrigger
                      value="comments"
                      className="group relative h-full data-[state=active]:bg-transparent"
                      onClick={handleCommentClick}
                    >
                      <span className="relative z-10 flex items-center gap-2 font-medium text-slate-600 transition-colors group-data-[state=active]:text-primary dark:text-slate-400 dark:group-data-[state=active]:text-primary">
                        <MessageCircle className="h-4 w-4" />
                        Comments ({roadmapDetails.commentsCount})
                      </span>
                      <span className="absolute bottom-0 left-0 h-0.5 w-full bg-transparent transition-all duration-300 group-data-[state=active]:h-1 group-data-[state=active]:bg-primary" />
                    </TabsTrigger>
                  </TabsList>
                </div>
                {/* Gradient overlay for smooth transition */}
                <div className="absolute -bottom-8 left-0 right-0 h-8 bg-gradient-to-b from-white dark:from-slate-900" />
              </div>

              <div className="relative">
                <TabsContent
                  value="content"
                  className="focus-visible:outline-none"
                >
                  <div className="space-y-8 px-6">
                    {isLoading || !roadmapDetails ? (
                      <RoadmapSkeleton />
                    ) : (
                      <>
                        {/* Hero Section */}
                        <div className="relative mt-8">
                          <div className="from-primary/20 via-primary/5 relative rounded-2xl bg-gradient-to-b to-transparent p-8">
                            <div className="bg-grid-black/[0.02] dark:bg-grid-white/[0.02] absolute inset-0 rounded-2xl" />
                            <div className="relative">
                              <div className="grid gap-8 lg:grid-cols-2">
                                {/* Left Column - Title and Meta */}
                                <div className="flex flex-col justify-center space-y-6 rounded-2xl bg-white/60 p-8 backdrop-blur-sm dark:bg-slate-900/60">
                                  <div className="space-y-4">
                                    <div className="flex items-center space-x-2">
                                      <Badge
                                        variant="outline"
                                        className="border-primary/30 bg-primary/5 text-primary"
                                      >
                                        {roadmapDetails.difficulty?.toUpperCase()}
                                      </Badge>
                                      {roadmapDetails.isFeatured && (
                                        <Badge
                                          variant="secondary"
                                          className="bg-amber-500/10 text-amber-600 dark:bg-amber-500/20 dark:text-amber-400"
                                        >
                                          FEATURED
                                        </Badge>
                                      )}
                                    </div>
                                    <h1 className="text-4xl font-bold tracking-tight text-slate-900 dark:text-white">
                                      {roadmapDetails.title}
                                    </h1>
                                    <p className="text-lg text-slate-600 dark:text-slate-300">
                                      {roadmapDetails.description}
                                    </p>
                                  </div>

                                  <div className="flex flex-wrap gap-2">
                                    {tags.map((tag: string) => (
                                      <Badge
                                        key={tag}
                                        variant="secondary"
                                        className="bg-slate-100 text-slate-700 dark:bg-slate-800 dark:text-slate-300"
                                      >
                                        {tag}
                                      </Badge>
                                    ))}
                                  </div>

                                  <div className="flex items-center space-x-4 rounded-lg bg-white/80 p-4 shadow-sm dark:bg-slate-800/80">
                                    <Avatar className="ring-primary/10 h-12 w-12 ring-2">
                                      <AvatarImage
                                        src={
                                          roadmapDetails?.user?.avatar_url ||
                                          undefined
                                        }
                                      />
                                      <AvatarFallback className="bg-primary/5 text-primary">
                                        {(
                                          roadmapDetails?.user
                                            ?.full_name?.[0] ||
                                          roadmapDetails?.user?.username[0]
                                        )?.toUpperCase()}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <p className="font-medium text-slate-900 dark:text-white">
                                        {roadmapDetails?.user?.full_name ||
                                          roadmapDetails?.user?.username}
                                      </p>
                                      <p className="text-sm text-slate-500 dark:text-slate-400">
                                        Updated{' '}
                                        {new Date(
                                          roadmapDetails.updatedAt,
                                        ).toLocaleDateString()}
                                      </p>
                                    </div>
                                  </div>
                                </div>

                                {/* Right Column - Stats and Actions */}
                                <div className="flex flex-col justify-center space-y-6">
                                  <Card className="overflow-hidden border-none bg-white/60 p-8 shadow-xl backdrop-blur-sm dark:bg-slate-900/60">
                                    <div className="grid grid-cols-2 gap-6 sm:grid-cols-4">
                                      <div className="rounded-xl bg-white p-4 text-center shadow-sm dark:bg-slate-800">
                                        <BookOpen className="mx-auto h-6 w-6 text-primary" />
                                        <p className="mt-2 text-2xl font-semibold text-slate-900 dark:text-white">
                                          {roadmap.length}
                                        </p>
                                        <p className="text-sm text-slate-500 dark:text-slate-400">
                                          Steps
                                        </p>
                                      </div>
                                      <div className="rounded-xl bg-white p-4 text-center shadow-sm dark:bg-slate-800">
                                        <Users className="mx-auto h-6 w-6 text-primary" />
                                        <p className="mt-2 text-2xl font-semibold text-slate-900 dark:text-white">
                                          {roadmapDetails.enrollmentCount || 0}
                                        </p>
                                        <p className="text-sm text-slate-500 dark:text-slate-400">
                                          Enrolled
                                        </p>
                                      </div>
                                      <div className="rounded-xl bg-white p-4 text-center shadow-sm dark:bg-slate-800">
                                        <Clock className="mx-auto h-6 w-6 text-primary" />
                                        <p className="mt-2 text-2xl font-semibold text-slate-900 dark:text-white">
                                          {roadmapDetails.estimatedTime ||
                                            '---'}
                                        </p>
                                        <p className="text-sm text-slate-500 dark:text-slate-400">
                                          Duration
                                        </p>
                                      </div>
                                      <div className="rounded-xl bg-white p-4 text-center shadow-sm dark:bg-slate-800">
                                        <Award className="mx-auto h-6 w-6 text-primary" />
                                        <p className="mt-2 text-2xl font-semibold text-slate-900 dark:text-white">
                                          {roadmapDetails.progress || 0}%
                                        </p>
                                        <p className="text-sm text-slate-500 dark:text-slate-400">
                                          Complete
                                        </p>
                                      </div>
                                    </div>

                                    {roadmapDetails.progress !== undefined && (
                                      <div className="mt-8 rounded-lg bg-white p-4 dark:bg-slate-800">
                                        <Progress
                                          value={roadmapDetails.progress}
                                          className="h-2"
                                        />
                                      </div>
                                    )}

                                    {/* Enrollment Button */}
                                    {!roadmapDetails.isEnrolled && (
                                      <div className="mt-8">
                                        <Button
                                          className="hover:bg-primary/90 w-full bg-primary"
                                          size="lg"
                                          disabled={socialActionLoading.enroll}
                                          onClick={async () => {
                                            try {
                                              setSocialActionLoading(
                                                (prev) => ({
                                                  ...prev,
                                                  enroll: true,
                                                }),
                                              );
                                              dispatch(showLoader('enrolling'));
                                              const success =
                                                await enrollInRoadmapHandler(
                                                  careerId,
                                                );
                                              if (!success) return;
                                              // Refresh roadmap details to update enrollment status
                                              fetchResources();
                                            } catch (error) {
                                              console.error(
                                                'Error enrolling in roadmap:',
                                                error,
                                              );
                                              toast.error(
                                                'Failed to enroll in roadmap',
                                              );
                                            } finally {
                                              setSocialActionLoading(
                                                (prev) => ({
                                                  ...prev,
                                                  enroll: false,
                                                }),
                                              );
                                              dispatch(hideLoader('enrolling'));
                                            }
                                          }}
                                        >
                                          {socialActionLoading.enroll
                                            ? 'Enrolling...'
                                            : 'Enroll in Roadmap'}
                                        </Button>
                                      </div>
                                    )}

                                    {/* New Social Actions Design */}
                                    <div className="mt-8 flex items-center justify-between gap-4">
                                      <div className="flex items-center gap-6">
                                        <button
                                          onClick={() =>
                                            handleSocialAction(
                                              handleLike,
                                              'like',
                                            )
                                          }
                                          disabled={socialActionLoading.like}
                                          className={cn(
                                            'group flex items-center gap-2 transition-all duration-200',
                                            'disabled:opacity-70',
                                            socialActionLoading.like &&
                                              'scale-95',
                                          )}
                                        >
                                          <div
                                            className={cn(
                                              'flex h-10 w-10 items-center justify-center rounded-full transition-all duration-200',
                                              'bg-slate-100 dark:bg-slate-800',
                                              !currentState.isLiked &&
                                                'group-hover:bg-red-50 dark:group-hover:bg-red-900/20',
                                              currentState.isLiked &&
                                                'bg-red-50 dark:bg-red-900/20',
                                              socialActionLoading.like &&
                                                'scale-95',
                                            )}
                                          >
                                            <Heart
                                              fill={
                                                currentState.isLiked
                                                  ? 'currentColor'
                                                  : 'none'
                                              }
                                              className={cn(
                                                'h-5 w-5 transition-all duration-200',
                                                'text-slate-600 dark:text-slate-400',
                                                !currentState.isLiked &&
                                                  'group-hover:text-red-500 dark:group-hover:text-red-400',
                                                currentState.isLiked &&
                                                  'text-red-500 dark:text-red-400',
                                              )}
                                            />
                                          </div>
                                          <span
                                            className={cn(
                                              'text-sm font-medium transition-all duration-200',
                                              'text-slate-600 dark:text-slate-400',
                                              currentState.isLiked &&
                                                'text-red-500 dark:text-red-400',
                                            )}
                                          >
                                            {currentState.likesCount}
                                          </span>
                                        </button>

                                        <button
                                          onClick={() =>
                                            handleSocialAction(
                                              handleBookmark,
                                              'bookmark',
                                            )
                                          }
                                          disabled={
                                            socialActionLoading.bookmark
                                          }
                                          className={cn(
                                            'group flex items-center gap-2 transition-all duration-200',
                                            'disabled:opacity-70',
                                            socialActionLoading.bookmark &&
                                              'scale-95',
                                          )}
                                        >
                                          <div
                                            className={cn(
                                              'flex h-10 w-10 items-center justify-center rounded-full transition-all duration-200',
                                              'bg-slate-100 dark:bg-slate-800',
                                              !currentState.isBookmarked &&
                                                'group-hover:bg-amber-50 dark:group-hover:bg-amber-900/20',
                                              currentState.isBookmarked &&
                                                'bg-amber-50 dark:bg-amber-900/20',
                                              socialActionLoading.bookmark &&
                                                'scale-95',
                                            )}
                                          >
                                            <Bookmark
                                              fill={
                                                currentState.isBookmarked
                                                  ? 'currentColor'
                                                  : 'none'
                                              }
                                              className={cn(
                                                'h-5 w-5 transition-all duration-200',
                                                'text-slate-600 dark:text-slate-400',
                                                !currentState.isBookmarked &&
                                                  'group-hover:text-amber-500 dark:group-hover:text-amber-400',
                                                currentState.isBookmarked &&
                                                  'text-amber-500 dark:text-amber-400',
                                              )}
                                            />
                                          </div>
                                          <span
                                            className={cn(
                                              'text-sm font-medium transition-all duration-200',
                                              'text-slate-600 dark:text-slate-400',
                                              currentState.isBookmarked &&
                                                'text-amber-500 dark:text-amber-400',
                                            )}
                                          >
                                            {currentState.bookmarksCount}
                                          </span>
                                        </button>
                                      </div>

                                      <button
                                        onClick={handleShare}
                                        className="hover:bg-primary/10 dark:hover:bg-primary/20 group flex h-10 w-10 items-center justify-center rounded-full bg-slate-100 transition-all dark:bg-slate-800"
                                      >
                                        <Share2 className="h-5 w-5 text-slate-600 transition-colors group-hover:text-primary dark:text-slate-400 dark:group-hover:text-primary" />
                                      </button>
                                    </div>
                                  </Card>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Roadmap Content */}
                        <div className="pb-24">
                          <Timeline>
                            {roadmap?.map((section, index) => (
                              <RoadmapSection
                                key={section.id}
                                name={section.main_concept?.name}
                                description={section.main_concept?.description}
                                subjects={section.main_concept?.subjects}
                                index={index}
                              />
                            ))}
                          </Timeline>
                        </div>
                      </>
                    )}
                  </div>
                </TabsContent>

                <TabsContent
                  value="comments"
                  className="border-none focus-visible:outline-none"
                >
                  <div className="space-y-6 px-6 pb-24 pt-8">
                    <CommentSection roadmapId={careerId} />
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </Card>
        </div>
      </ParallaxProvider>
    </div>
  );
}
