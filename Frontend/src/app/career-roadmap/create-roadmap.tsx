/**
 * @file create-roadmap.tsx
 * @description Next.js page for career-roadmap route
 */
'use client';

import { useEffect, useState } from 'react';
import { useForm, useFormContext } from 'react-hook-form';
import type { Control as ControlType } from 'react-hook-form';
import { toast } from 'react-toastify';

import * as yup from 'yup';
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from '@hello-pangea/dnd';
import { yupResolver } from '@hookform/resolvers/yup';
import { GripVertical, Plus, X } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
// Label import is used in FormLabel
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/ui/modal';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';

interface IRoadmapCategory {
  id: string;
  name: string;
}

interface IMainConcept {
  id: string;
  name: string;
}

interface ISubject {
  id: string;
  name: string;
  title?: string;
}

interface ITopic {
  id: string;
  name: string;
}

interface ICreateRoadmapProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  categories: IRoadmapCategory[];
}

const formSchema = yup.object({
  title: yup
    .string()
    .min(5, 'Title must be at least 5 characters')
    .max(100, 'Title must not exceed 100 characters')
    .required('Title is required'),
  description: yup
    .string()
    .min(20, 'Description must be at least 20 characters')
    .max(500, 'Description must not exceed 500 characters')
    .required('Description is required'),
  categoryId: yup.string().required('Please select a category'),
  difficulty: yup
    .string()
    .oneOf(['BEGINNER', 'INTERMEDIATE', 'ADVANCED'])
    .required('Difficulty is required'),
  estimatedHours: yup
    .number()
    .min(1, 'Estimated hours must be at least 1')
    .required('Estimated hours is required'),
  isPublic: yup.boolean().default(false),
  version: yup.string().default('1.0'),
  tags: yup.array().of(yup.string()).default([]),
  mainConcepts: yup
    .array()
    .of(
      yup.object({
        main_concept_id: yup.string().required(),
        order: yup.number().required(),
        subjects: yup.array().of(
          yup.object({
            subject_id: yup.string().required(),
            order: yup.number().required(),
            topics: yup.array().of(
              yup.object({
                topic_id: yup.string().required(),
                order: yup.number().required(),
              }),
            ),
          }),
        ),
      }),
    )
    .min(1, 'At least one main concept is required'),
});

// Define a custom type for our form data to avoid conflict with browser's FormData
type RoadmapFormValues = yup.InferType<typeof formSchema>;

interface ITagsListProps {
  tags: string[];
  onRemove: (tag: string) => void;
}

export function TagsList({ tags, onRemove }: ITagsListProps) {
  return (
    <div className="flex flex-wrap gap-2">
      {tags.map((tag) => (
        <Badge key={tag} variant="secondary" className="gap-1">
          {tag}
          <button
            type="button"
            onClick={() => onRemove(tag)}
            className="ml-1 rounded-full p-1 hover:bg-destructive/20"
          >
            <X size={12} />
          </button>
        </Badge>
      ))}
    </div>
  );
}

interface ITopicFormProps {
  mainConceptIndex: number;
  subjectIndex: number;
  topicIndex: number;
  control: ControlType<RoadmapFormValues>;
  availableTopics: ITopic[];
}

export function TopicForm({
  mainConceptIndex,
  subjectIndex,
  topicIndex,
  control,
  availableTopics,
}: ITopicFormProps) {
  return (
    <div className="rounded-lg border bg-background p-3">
      <FormField
        control={control}
        name={`mainConcepts.${mainConceptIndex}.subjects.${subjectIndex}.topics.${topicIndex}.topic_id`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Topic</FormLabel>
            <FormControl>
              <Select onValueChange={field.onChange} value={field.value || ''}>
                <SelectTrigger className="bg-background">
                  <SelectValue placeholder="Select topic" />
                </SelectTrigger>
                <SelectContent>
                  {availableTopics.map((topic) => (
                    <SelectItem key={topic.id} value={topic.id}>
                      {topic.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

interface ISubjectFormProps {
  mainConceptIndex: number;
  subjectIndex: number;
  control: ControlType<RoadmapFormValues>;
  availableSubjects: ISubject[];
  availableTopics: ITopic[];
}

export function SubjectForm({
  mainConceptIndex,
  subjectIndex,
  control,
  availableSubjects,
  availableTopics,
}: ISubjectFormProps) {
  const { watch, setValue } = useFormContext<RoadmapFormValues>();
  const topics =
    watch(`mainConcepts.${mainConceptIndex}.subjects.${subjectIndex}.topics`) ||
    [];

  const handleAddTopic = () => {
    const mainConcepts = watch('mainConcepts') || [];
    const updatedConcepts = [...mainConcepts];

    // Ensure the path exists before accessing it
    if (!updatedConcepts[mainConceptIndex]) {
      updatedConcepts[mainConceptIndex] = {
        main_concept_id: '',
        order: mainConceptIndex,
        subjects: [],
      };
    }

    if (!updatedConcepts[mainConceptIndex].subjects) {
      updatedConcepts[mainConceptIndex].subjects = [];
    }

    if (!updatedConcepts[mainConceptIndex].subjects[subjectIndex]) {
      updatedConcepts[mainConceptIndex].subjects[subjectIndex] = {
        subject_id: '',
        order: subjectIndex,
        topics: [],
      };
    }

    if (!updatedConcepts[mainConceptIndex].subjects[subjectIndex].topics) {
      updatedConcepts[mainConceptIndex].subjects[subjectIndex].topics = [];
    }

    // Now safely add the new topic
    updatedConcepts[mainConceptIndex].subjects[subjectIndex].topics.push({
      topic_id: '',
      order:
        updatedConcepts[mainConceptIndex].subjects[subjectIndex].topics.length,
    });

    setValue('mainConcepts', updatedConcepts);
  };

  // Function to remove a topic from the form
  const handleRemoveTopic = (topicIndex: number) => {
    const currentTopics =
      watch(
        `mainConcepts.${mainConceptIndex}.subjects.${subjectIndex}.topics`,
      ) || [];
    const updatedTopics = currentTopics
      .filter((_, i) => i !== topicIndex)
      .map((topicItem, i) => ({ ...topicItem, order: i }));
    setValue(
      `mainConcepts.${mainConceptIndex}.subjects.${subjectIndex}.topics`,
      updatedTopics,
    );
  };

  return (
    <div className="mb-4 flex items-start gap-2">
      <div className="mt-2 cursor-move">
        <GripVertical size={16} className="text-muted-foreground" />
      </div>
      <div className="flex-1 space-y-4">
        <FormField
          control={control}
          name={`mainConcepts.${mainConceptIndex}.subjects.${subjectIndex}.subject_id`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Subject</FormLabel>
              <FormControl>
                <Select
                  onValueChange={field.onChange}
                  value={field.value || ''}
                >
                  <SelectTrigger className="bg-background">
                    <SelectValue placeholder="Select subject" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableSubjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.id}>
                        {subject.name || subject.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <FormLabel>Topics</FormLabel>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddTopic}
            >
              <Plus size={16} className="mr-1" /> Add Topic
            </Button>
          </div>

          {topics.map((topic, topicIndex) => (
            <TopicForm
              key={`topic-${mainConceptIndex}-${subjectIndex}-${topicIndex}`}
              mainConceptIndex={mainConceptIndex}
              subjectIndex={subjectIndex}
              topicIndex={topicIndex}
              control={control}
              availableTopics={availableTopics}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

interface IMainConceptFormProps {
  mainConceptIndex: number;
  control: ControlType<RoadmapFormValues>;
  availableMainConcepts: IMainConcept[];
  availableSubjects: ISubject[];
  availableTopics: ITopic[];
}

export function MainConceptForm({
  mainConceptIndex,
  control,
  availableMainConcepts,
  availableSubjects,
  availableTopics,
}: IMainConceptFormProps) {
  const { watch, setValue } = useFormContext<RoadmapFormValues>();
  const subjects = watch(`mainConcepts.${mainConceptIndex}.subjects`) || [];

  const handleAddSubject = (mainConceptIndex: number) => {
    const mainConcepts = watch('mainConcepts') || [];
    if (!mainConcepts[mainConceptIndex]) return;

    const updatedConcepts = [...mainConcepts];
    const subjects = updatedConcepts[mainConceptIndex].subjects || [];

    updatedConcepts[mainConceptIndex].subjects = [
      ...subjects,
      {
        subject_id: '',
        order: subjects.length,
        topics: [],
      },
    ];

    setValue('mainConcepts', updatedConcepts);
  };

  // This function is used by the parent component to remove topics
  const handleRemoveTopic = (
    mainConceptIndex: number,
    subjectIndex: number,
    topicIndex: number,
  ) => {
    const currentConcepts = watch('mainConcepts') || [];
    if (!currentConcepts[mainConceptIndex]) return;

    // Create a safe copy of the concepts array
    const updatedConcepts = [...currentConcepts];

    // Ensure the subjects array exists
    if (!updatedConcepts[mainConceptIndex].subjects) {
      updatedConcepts[mainConceptIndex].subjects = [];
      return; // No subjects to remove topics from
    }

    // Ensure the specific subject exists
    if (!updatedConcepts[mainConceptIndex].subjects[subjectIndex]) {
      return; // Subject doesn't exist, nothing to remove
    }

    // Safely get the topics array with a default empty array if undefined
    const topics =
      updatedConcepts[mainConceptIndex].subjects[subjectIndex].topics || [];

    // Update the topics array by filtering out the removed topic and updating order
    updatedConcepts[mainConceptIndex].subjects[subjectIndex].topics = topics
      .filter((_, i) => i !== topicIndex)
      .map((topicItem, i) => ({ ...topicItem, order: i }));

    setValue('mainConcepts', updatedConcepts);
  };

  return (
    <div className="space-y-4">
      <FormField
        control={control}
        name={`mainConcepts.${mainConceptIndex}.main_concept_id`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Main Concept</FormLabel>
            <FormControl>
              <Select onValueChange={field.onChange} value={field.value || ''}>
                <SelectTrigger className="bg-background">
                  <SelectValue placeholder="Select main concept" />
                </SelectTrigger>
                <SelectContent>
                  {availableMainConcepts.map((concept) => (
                    <SelectItem key={concept.id} value={concept.id}>
                      {concept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <FormLabel>Subjects</FormLabel>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => handleAddSubject(mainConceptIndex)}
          >
            <Plus size={16} className="mr-1" /> Add Subject
          </Button>
        </div>

        {subjects.map((subject, subjectIndex) => (
          <SubjectForm
            key={`subject-${mainConceptIndex}-${subjectIndex}`}
            mainConceptIndex={mainConceptIndex}
            subjectIndex={subjectIndex}
            control={control}
            availableSubjects={availableSubjects}
            availableTopics={availableTopics}
          />
        ))}
      </div>
    </div>
  );
}

export function CreateRoadmap({
  isOpen,
  onClose,
  onSuccess,
  categories,
}: ICreateRoadmapProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mainConcepts, setMainConcepts] = useState<IMainConcept[]>([]);
  const [subjects, setSubjects] = useState<ISubject[]>([]);
  const [topics, setTopics] = useState<ITopic[]>([]);
  const [tagInput, setTagInput] = useState('');

  const [createRoadmap] = useAxiosPost('/roadmaps');
  const [getMainConcepts] = useAxiosGet<{ data: IMainConcept[] }>(
    '/main-concepts',
  );
  const [getSubjects] = useAxiosGet<{ data: ISubject[] }>('/subjects');
  const [getTopics] = useAxiosGet<{ data: ITopic[] }>('/topics');

  const form = useForm<RoadmapFormValues>({
    resolver: yupResolver(formSchema),
    defaultValues: {
      title: '',
      description: '',
      categoryId: '',
      difficulty: 'BEGINNER',
      estimatedHours: 1,
      isPublic: false,
      version: '1.0',
      tags: [],
      mainConcepts: [],
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [mainConceptsResponse, subjectsResponse, topicsResponse] =
          await Promise.all([getMainConcepts(), getSubjects(), getTopics()]);

        setMainConcepts(mainConceptsResponse.data.data);
        setSubjects(subjectsResponse.data.data);
        setTopics(topicsResponse.data.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to fetch required data');
      }
    };

    if (isOpen) {
      fetchData();
    }
  }, [isOpen, getMainConcepts, getSubjects, getTopics]);

  const handleTagChange = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (tagInput.trim()) {
        const newTag = tagInput.trim();
        const currentTags = form.getValues('tags') || [];
        if (!currentTags.includes(newTag)) {
          form.setValue('tags', [...currentTags, newTag]);
        }
        setTagInput('');
      }
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags');
    form.setValue(
      'tags',
      currentTags.filter((tag) => tag !== tagToRemove),
    );
  };

  const handleAddMainConcept = () => {
    const currentConcepts = form.getValues('mainConcepts') || [];
    form.setValue('mainConcepts', [
      ...currentConcepts,
      {
        main_concept_id: '',
        order: currentConcepts.length,
        subjects: [],
      },
    ]);
  };

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    // Get the mainConcepts array with a default empty array if undefined
    const mainConcepts = form.getValues('mainConcepts') || [];

    // Create a new array to avoid mutating the original
    const items = [...mainConcepts];

    // Only proceed if there are items to reorder
    if (items.length === 0) return;

    // Perform the reordering
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order values
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index,
    }));
    form.setValue('mainConcepts', updatedItems);
  };

  const onSubmit = async (values: RoadmapFormValues) => {
    try {
      setIsSubmitting(true);
      await createRoadmap({ data: values });
      toast.success('Roadmap created successfully!');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating roadmap:', error);
      toast.error('Failed to create roadmap');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Create New Roadmap"
      maxWidth="2xl"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                // ... rest of the code remains the same ...
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Complete Frontend Development Path"
                      className="bg-background"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ''}
                    >
                      <SelectTrigger className="bg-background">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe your roadmap and what learners will achieve..."
                    className="h-32 resize-none bg-background"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid gap-6 md:grid-cols-3">
            <FormField
              control={form.control}
              name="difficulty"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Difficulty</FormLabel>
                  <FormControl>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ''}
                    >
                      <SelectTrigger className="bg-background">
                        <SelectValue placeholder="Select difficulty" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="BEGINNER">Beginner</SelectItem>
                        <SelectItem value="INTERMEDIATE">
                          Intermediate
                        </SelectItem>
                        <SelectItem value="ADVANCED">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="estimatedHours"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Estimated Hours</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      placeholder="e.g., 40"
                      className="bg-background"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isPublic"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between space-y-0 rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel>Public Roadmap</FormLabel>
                    <FormDescription>
                      Make this roadmap visible to everyone
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <FormItem>
            <FormLabel>Tags</FormLabel>
            <div className="flex flex-col space-y-2">
              <div className="flex items-center justify-between">
                <FormLabel htmlFor="tags">Tags</FormLabel>
                <div className="flex items-center space-x-2">
                  <Input
                    id="tag-input"
                    placeholder="Add a tag..."
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleTagChange}
                    className="w-40"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (tagInput.trim()) {
                        const newTag = tagInput.trim();
                        const currentTags = form.getValues('tags') || [];
                        if (!currentTags.includes(newTag)) {
                          form.setValue('tags', [...currentTags, newTag]);
                        }
                        setTagInput('');
                      }
                    }}
                  >
                    Add
                  </Button>
                </div>
              </div>
              <TagsList
                tags={form.watch('tags') || []}
                onRemove={handleRemoveTag}
              />
            </div>
          </FormItem>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <FormLabel>Main Concepts</FormLabel>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddMainConcept}
                className="mb-4"
              >
                <Plus size={16} className="mr-1" /> Add Main Concept
              </Button>
            </div>

            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="main-concepts">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="space-y-4"
                  >
                    {form.watch('mainConcepts').map((concept, conceptIndex) => (
                      <Draggable
                        key={`concept-${concept.main_concept_id}-${conceptIndex}`}
                        draggableId={`concept-${concept.main_concept_id}-${conceptIndex}`}
                        index={conceptIndex}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="rounded-lg border bg-card p-4"
                          >
                            <MainConceptForm
                              mainConceptIndex={conceptIndex}
                              control={form.control}
                              availableMainConcepts={mainConcepts}
                              availableSubjects={subjects}
                              availableTopics={topics}
                            />
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Roadmap'}
            </Button>
          </div>
        </form>
      </Form>
    </Modal>
  );
}
