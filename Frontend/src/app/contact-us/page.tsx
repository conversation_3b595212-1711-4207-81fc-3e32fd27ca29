/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */
'use client';

import { useState } from 'react';

import { motion } from 'framer-motion';

import { contactInfo } from '@/constants';

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

/**
 * @file page.tsx
 * @description Next.js page for contact-us route
 */

export default function Contact() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // In a real application, you would send this data to your backend
    setSubmitted(true);
    // Reset form
    setName('');
    setEmail('');
    setSubject('');
    setMessage('');
  };

  return (
    <main className="bg-background py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <motion.div
          className="mb-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="mb-4 text-4xl font-bold text-foreground md:text-5xl">
            Contact <span className="text-primary">Us</span>
          </h1>
          <p className="mx-auto max-w-3xl text-lg text-muted-foreground">
            Have questions or feedback? We&apos;d love to hear from you. Fill
            out the form below or reach out to us directly.
          </p>
        </motion.div>

        <div className="grid gap-12 md:grid-cols-3">
          {/* Contact Information */}
          <motion.div
            className="md:col-span-1"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="rounded-xl border border-border bg-card p-8 shadow-sm">
              <h2 className="mb-6 text-2xl font-bold text-foreground">
                Get In Touch
              </h2>

              <div className="space-y-6">
                <div>
                  <h3 className="mb-2 text-lg font-semibold text-foreground">
                    Email
                  </h3>
                  <p className="text-muted-foreground">{contactInfo[0].text}</p>
                </div>

                <div>
                  <h3 className="mb-2 text-lg font-semibold text-foreground">
                    Phone
                  </h3>
                  <p className="text-muted-foreground">{contactInfo[1].text}</p>
                </div>

                <div>
                  <h3 className="mb-2 text-lg font-semibold text-foreground">
                    Location
                  </h3>
                  <p className="text-muted-foreground">{contactInfo[2].text}</p>
                </div>

                <div>
                  <h3 className="mb-2 text-lg font-semibold text-foreground">
                    Hours
                  </h3>
                  <p className="text-muted-foreground">
                    Monday - Friday: 9AM - 6PM IST
                  </p>
                  <p className="text-muted-foreground">
                    Saturday: 10AM - 2PM IST
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            className="md:col-span-2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <div className="rounded-xl border border-border bg-card p-8 shadow-sm">
              <h2 className="mb-6 text-2xl font-bold text-foreground">
                Send Us a Message
              </h2>

              {submitted ? (
                <motion.div
                  className="rounded-lg bg-green-50 p-6 text-center dark:bg-green-900/20"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                >
                  <h3 className="mb-2 text-xl font-bold text-green-800 dark:text-green-300">
                    Thank You!
                  </h3>
                  <p className="text-green-700 dark:text-green-400">
                    Your message has been sent successfully. We&apos;ll get back
                    to you as soon as possible.
                  </p>
                  <button
                    onClick={() => setSubmitted(false)}
                    className="mt-4 rounded-full bg-green-600 px-4 py-2 text-white hover:bg-green-700"
                  >
                    Send Another Message
                  </button>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <div className="mb-6 grid gap-6 md:grid-cols-2">
                    <div>
                      <label
                        htmlFor="name"
                        className="mb-2 block text-sm font-medium text-foreground"
                      >
                        Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                        required
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="email"
                        className="mb-2 block text-sm font-medium text-foreground"
                      >
                        Email
                      </label>
                      <input
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full rounded-md border border-input bg-background px-3 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-6">
                    <label
                      htmlFor="subject"
                      className="mb-2 block text-sm font-medium text-foreground"
                    >
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      value={subject}
                      onChange={(e) => setSubject(e.target.value)}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      required
                    />
                  </div>

                  <div className="mb-6">
                    <label
                      htmlFor="message"
                      className="mb-2 block text-sm font-medium text-foreground"
                    >
                      Message
                    </label>
                    <textarea
                      id="message"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-foreground focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                      rows={5}
                      required
                    ></textarea>
                  </div>

                  <div className="text-right">
                    <button
                      type="submit"
                      className="rounded-full bg-primary px-6 py-3 font-medium text-white transition-colors hover:bg-primary2"
                    >
                      Send Message
                    </button>
                  </div>
                </form>
              )}
            </div>
          </motion.div>
        </div>

        {/* Map or Additional Information */}
        <motion.div
          className="mt-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="rounded-xl border border-border bg-card p-8 shadow-sm">
            <h2 className="mb-6 text-2xl font-bold text-foreground">
              Visit Our Office
            </h2>
            <div className="aspect-video w-full rounded-lg bg-muted">
              {/* In a real application, you would embed a map here */}
              <div className="flex h-full items-center justify-center">
                <p className="text-muted-foreground">
                  Map will be displayed here
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </main>
  );
}
