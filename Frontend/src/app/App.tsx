/**
 * @file App.tsx
 * @description Next.js page for  route
 */
'use client';

import { ReactNode, useEffect, useState } from 'react';
import { Provider } from 'react-redux';

import { usePathname, useRouter } from 'next/navigation';

import { PersistGate } from 'redux-persist/integration/react';

import Loader from '@/components/Loader';
import { useAxiosGet } from '@/hooks/useAxios';
import {
  clearUser,
  setAuthenticated,
  setUser,
} from '@/lib/features/user/userSlice';
import { useAppDispatch } from '@/lib/hooks';
import { persistor, store } from '@/lib/store';
import { IRole, IUser } from '@/types';
import { createClient } from '@/utils/supabase/client';

import Footer from '../components/Footer';
import Navbar from '../components/Navbar';

export default function App({ children }: { children: ReactNode }) {
  const [isClient, setIsClient] = useState<boolean>(false);
  const path = usePathname();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [getUserApi, { data, isLoading }] = useAxiosGet<{ user: IUser }>(
    '/users/me',
  );
  const [getRolesApi] = useAxiosGet<IRole[]>('/users/{{userId}}/roles');

  const isPublic =
    path === '/' ||
    path?.startsWith('/auth') ||
    path === '/about-us' ||
    path === '/contact-us' ||
    path === '/faq' ||
    path === '/blogs' ||
    path === '/support';
  const isDetailsPage = path === '/details';
  const isAdminPage = path?.startsWith('/admin');

  useEffect(() => {
    setIsClient(true);
    const savedTheme = localStorage.getItem('theme') ?? 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    // Initialize axios interceptors
    import('@/utils/axiosInterceptor').then(({ setupAxiosInterceptors }) => {
      setupAxiosInterceptors();
    });
  }, []);

  useEffect(() => {
    if (!isClient) return;

    // Skip user validation on public routes during initial load
    // This prevents unnecessary API calls that would result in 401 errors
    if (isPublic && path?.startsWith('/auth')) {
      dispatch(setAuthenticated(false));
      return;
    }

    const validateUser = async () => {
      try {
        // Check if we have a session before making the API call
        const supabase = createClient();
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session) {
          // No session, user is not authenticated
          dispatch(setAuthenticated(false));
          dispatch(clearUser());
          if (!isPublic) router.push('/auth/login');
          return;
        }

        // We have a session, get user data
        await getUserApi();
      } catch (error) {
        console.error('Authentication error:', error);
        dispatch(setAuthenticated(false));
        dispatch(clearUser());
        if (!isPublic) router.push('/auth/login');
      }
    };

    validateUser();
  }, [isClient, dispatch, router, isPublic, path]);

  // Fetch user roles when user data is available
  useEffect(() => {
    if (data) {
      if (data.user) {
        dispatch(setUser({ user: data.user }));
        dispatch(setAuthenticated(true));

        // Fetch user roles after user is authenticated
        const fetchUserRoles = async () => {
          try {
            const supabase = createClient();
            const {
              data: { session },
            } = await supabase.auth.getSession();
            if (!session) return;

            // Fetch user roles from the backend
            const response = await getRolesApi(
              {},
              {
                userId: data?.user?.id,
              },
            );

            if (!response.success) {
              throw new Error('Failed to fetch user roles');
            }

            if (response?.data) {
              // Update the user in Redux with the roles
              dispatch(
                setUser({
                  user: {
                    ...data.user,
                    roles: response?.data,
                  },
                  detailsComplete: true,
                }),
              );
            }
          } catch (error) {
            console.error('Error fetching user roles:', error);
          }
        };

        fetchUserRoles();
      } else {
        dispatch(setAuthenticated(false));
        dispatch(clearUser());
      }
    }
  }, [data]);

  if (!isClient) return null;

  if (isLoading) return <Loader type="SiteLoader" />;

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <div className="flex min-h-screen flex-col justify-between">
          {!isDetailsPage && !isAdminPage ? <Navbar isPublic={isPublic} /> : <></>}
          {children}
          {!isDetailsPage && !isAdminPage ? <Footer /> : <></>}
        </div>
      </PersistGate>
    </Provider>
  );
}
