/**
 * @file page.tsx
 * @description Next.js page for mastermind-forge route
 */
import Link from 'next/link';

const MastermindForge = () => (
  <div>
    <h1>Mastermind Forge</h1>
    <ul>
      <li>
        <Link href="/mastermind-forge/interview-presentation-training">
          Interview & Presentation Training
        </Link>
      </li>
      <li>
        <Link href="/mastermind-forge/resume-building">Resume Building</Link>
      </li>
      <li>
        <Link href="/mastermind-forge/projects-provision">
          Projects Provision
        </Link>
      </li>
      <li>
        <Link href="/mastermind-forge/aptitude-and-reasoning-training">
          Aptitude & Reasoning Training
        </Link>
      </li>
      <li>
        <Link href="/mastermind-forge/companies-names-list">
          Companies Names List
        </Link>
      </li>
      <li>
        <Link href="/mastermind-forge/internship-provision">
          Internship Provision
        </Link>
      </li>
      <li>
        <Link href="/mastermind-forge/can-work-in-our-company">
          Can Work in Our Company
        </Link>
      </li>
      <li>
        <Link href="/mastermind-forge/one-on-one-sessions">
          One-on-One Sessions
        </Link>
      </li>
      <li>
        <Link href="/mastermind-forge/limited-features-access">
          Limited Features Access
        </Link>
      </li>
    </ul>
  </div>
);

export default MastermindForge;
