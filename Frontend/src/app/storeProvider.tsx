/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */
'use client';

import { Provider } from 'react-redux';

import { store } from '@/lib/store';

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

/**
 * @file storeProvider.tsx
 * @description Next.js page for  route
 */

export default function StoreProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return <Provider store={store}>{children}</Provider>;
}
