/**
 * @file page.tsx
 * @description Admin Help System with documentation and guided tours
 */
'use client';

import { useState } from 'react';
import {
  RiQuestionLine,
  RiBookOpenLine,
  RiVideoLine,
  RiSearchLine,
  RiArrowRightLine,
  RiPlayLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';

interface IHelpArticle {
  id: string;
  title: string;
  category: string;
  content: string;
  tags: string[];
  lastUpdated: string;
}

interface ITutorial {
  id: string;
  title: string;
  description: string;
  duration: string;
  thumbnailUrl: string;
  videoUrl?: string;
  steps?: {
    title: string;
    description: string;
    target?: string;
  }[];
}

interface IFaq {
  question: string;
  answer: string;
  category: string;
}

function AdminHelpSystem() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('documentation');
  const [selectedArticle, setSelectedArticle] = useState<IHelpArticle | null>(
    null,
  );
  const [isTutorialDialogOpen, setIsTutorialDialogOpen] = useState(false);
  const [selectedTutorial, setSelectedTutorial] = useState<ITutorial | null>(
    null,
  );
  const [activeTutorialStep, setActiveTutorialStep] = useState(0);

  // Mock data for help articles
  const helpArticles: IHelpArticle[] = [
    {
      id: '1',
      title: 'Getting Started with the Admin Dashboard',
      category: 'Basics',
      content: `
# Getting Started with the Admin Dashboard

Welcome to the MrEngineer Admin Dashboard! This guide will help you understand the basics of navigating and using the dashboard effectively.

## Dashboard Overview

The admin dashboard is divided into several key sections:

- **Dashboard Home**: View key metrics and recent activity
- **User Management**: Manage user accounts, roles, and permissions
- **Content Management**: Create, edit, and organize content
- **Moderation Tools**: Review and moderate user-generated content
- **System Settings**: Configure system-wide settings
- **Analytics**: View detailed usage statistics

## Navigation

Use the sidebar menu to navigate between different sections of the dashboard. Each section contains related tools and features.

## Common Tasks

- **Creating a new user**: Navigate to User Management > Create User
- **Moderating comments**: Navigate to Moderation Tools > Comments
- **Changing site settings**: Navigate to System Settings

## Need More Help?

If you need additional assistance, check out the video tutorials or contact the support team.
      `,
      tags: ['getting started', 'basics', 'navigation'],
      lastUpdated: '2025-05-15',
    },
    {
      id: '2',
      title: 'User Management Guide',
      category: 'Users',
      content: `
# User Management Guide

This guide covers all aspects of managing users in the MrEngineer platform.

## User Roles

The platform has several user roles with different permissions:

- **Admin**: Full access to all features
- **Moderator**: Can moderate content but cannot change system settings
- **User**: Standard user with limited access

## Managing Users

### Creating Users

1. Navigate to User Management > Create User
2. Fill in the required information
3. Select the appropriate role
4. Click "Create User"

### Editing Users

1. Navigate to User Management
2. Find the user in the list
3. Click the edit button
4. Make the necessary changes
5. Click "Save Changes"

### Suspending Users

If a user violates the platform's terms of service, you can suspend their account:

1. Navigate to User Management
2. Find the user in the list
3. Click the "Actions" dropdown
4. Select "Suspend User"
5. Enter a reason for the suspension
6. Click "Confirm"

## Bulk User Operations

For managing multiple users at once, use the Bulk Operations feature:

1. Navigate to Bulk Operations > Users
2. Select the users you want to modify
3. Choose the action to perform
4. Confirm the action
      `,
      tags: ['users', 'management', 'roles', 'permissions'],
      lastUpdated: '2025-05-18',
    },
    {
      id: '3',
      title: 'Content Moderation Best Practices',
      category: 'Moderation',
      content: `
# Content Moderation Best Practices

This guide provides best practices for moderating content on the MrEngineer platform.

## Moderation Principles

- **Be consistent**: Apply the same standards to all content
- **Be objective**: Focus on the content, not the user
- **Be timely**: Address reported content promptly
- **Document decisions**: Always provide a reason for moderation actions

## Moderation Workflow

1. Review reported content in the Moderation Tools section
2. Assess the content against the community guidelines
3. Take appropriate action (approve, reject, flag)
4. Provide feedback to the user if necessary

## Handling Different Types of Content

### Comments

- Check for spam, offensive language, or harassment
- Consider the context of the comment
- Use the keyword flagging system for automatic detection

### User-Generated Content

- Verify that content meets quality standards
- Check for plagiarism or copyright issues
- Ensure content is appropriate for the target audience

## Escalation Process

If you're unsure about a moderation decision:

1. Flag the content for review by a senior moderator
2. Add notes explaining your concerns
3. Wait for feedback before taking final action
      `,
      tags: ['moderation', 'guidelines', 'best practices', 'content'],
      lastUpdated: '2025-05-20',
    },
    {
      id: '4',
      title: 'System Settings Configuration',
      category: 'Settings',
      content: `
# System Settings Configuration

This guide explains how to configure system settings in the MrEngineer platform.

## General Settings

- **Site Name**: The name displayed throughout the platform
- **Site Description**: A brief description of the platform
- **Contact Email**: The primary contact email for the platform
- **Maintenance Mode**: Enable this when performing updates

## User Settings

- **Allow Registration**: Enable or disable new user registrations
- **Require Email Verification**: Require users to verify their email
- **Default User Role**: The default role assigned to new users

## Email Settings

- **SMTP Configuration**: Set up email sending capabilities
- **Email Templates**: Customize notification emails
- **Email Frequency**: Control how often emails are sent

## Backup and Restore

- **Automated Backups**: Configure regular system backups
- **Backup Retention**: Set how long backups are kept
- **Manual Backup**: Create on-demand backups
- **Restore System**: Restore from a previous backup

## Saving Changes

Always remember to click the "Save Changes" button after modifying settings. Some changes may require a system restart to take effect.
      `,
      tags: ['settings', 'configuration', 'system', 'backup'],
      lastUpdated: '2025-05-22',
    },
    {
      id: '5',
      title: 'Analytics and Reporting Guide',
      category: 'Analytics',
      content: `
# Analytics and Reporting Guide

This guide explains how to use the analytics and reporting features in the MrEngineer platform.

## Dashboard Analytics

The main dashboard provides an overview of key metrics:

- **User Growth**: New user registrations over time
- **Content Creation**: New content created over time
- **Engagement**: User activity and interaction metrics
- **Popular Content**: Most viewed and engaged content

## Detailed Reports

### User Reports

- **Active Users**: Daily, weekly, and monthly active users
- **User Retention**: How many users return to the platform
- **User Acquisition**: Where users are coming from

### Content Reports

- **Content Performance**: Views, likes, and comments
- **Content Creation**: Trends in content creation
- **Content Categories**: Distribution across categories

## Exporting Data

You can export reports in various formats:

1. Navigate to the report you want to export
2. Click the "Export" button
3. Select the desired format (CSV, Excel, PDF)
4. Click "Download"

## Custom Reports

To create a custom report:

1. Navigate to Analytics > Custom Reports
2. Click "Create New Report"
3. Select the metrics and dimensions
4. Set the date range
5. Click "Generate Report"
      `,
      tags: ['analytics', 'reporting', 'metrics', 'data'],
      lastUpdated: '2025-05-19',
    },
  ];

  // Mock data for tutorials
  const tutorials: ITutorial[] = [
    {
      id: '1',
      title: 'Admin Dashboard Tour',
      description: 'A complete tour of the admin dashboard and its features',
      duration: '5 min',
      thumbnailUrl: '/placeholder-thumbnail.jpg',
      videoUrl: 'https://example.com/videos/admin-tour',
      steps: [
        {
          title: 'Dashboard Overview',
          description:
            'This is the main dashboard where you can see key metrics and recent activity.',
          target: '#dashboard',
        },
        {
          title: 'Navigation Menu',
          description:
            'Use this sidebar to navigate between different sections of the admin dashboard.',
          target: '#sidebar',
        },
        {
          title: 'User Management',
          description:
            'Here you can manage user accounts, roles, and permissions.',
          target: '#users',
        },
        {
          title: 'Content Management',
          description:
            'This section allows you to create, edit, and organize content.',
          target: '#content',
        },
        {
          title: 'System Settings',
          description: 'Configure system-wide settings and preferences here.',
          target: '#settings',
        },
      ],
    },
    {
      id: '2',
      title: 'User Management Basics',
      description: 'Learn how to create, edit, and manage user accounts',
      duration: '4 min',
      thumbnailUrl: '/placeholder-thumbnail.jpg',
      videoUrl: 'https://example.com/videos/user-management',
    },
    {
      id: '3',
      title: 'Content Moderation Workflow',
      description: 'A step-by-step guide to moderating user-generated content',
      duration: '6 min',
      thumbnailUrl: '/placeholder-thumbnail.jpg',
      videoUrl: 'https://example.com/videos/moderation-workflow',
    },
    {
      id: '4',
      title: 'System Configuration',
      description: 'How to configure system settings for optimal performance',
      duration: '7 min',
      thumbnailUrl: '/placeholder-thumbnail.jpg',
      videoUrl: 'https://example.com/videos/system-config',
    },
    {
      id: '5',
      title: 'Analytics and Reporting',
      description: 'Understanding and using the analytics dashboard',
      duration: '5 min',
      thumbnailUrl: '/placeholder-thumbnail.jpg',
      videoUrl: 'https://example.com/videos/analytics',
    },
    {
      id: '6',
      title: 'Bulk Operations Guide',
      description: 'How to perform actions on multiple items at once',
      duration: '3 min',
      thumbnailUrl: '/placeholder-thumbnail.jpg',
      videoUrl: 'https://example.com/videos/bulk-operations',
    },
  ];

  // Mock data for FAQs
  const faqs: IFaq[] = [
    {
      question: "How do I reset a user's password?",
      answer:
        'Navigate to User Management, find the user, click the "Actions" dropdown, and select "Reset Password". You can either set a temporary password or send a password reset email to the user.',
      category: 'Users',
    },
    {
      question: 'How do I enable maintenance mode?',
      answer:
        'Go to System Settings, scroll to the "General Settings" section, and toggle the "Maintenance Mode" switch. You can also set a custom maintenance message that users will see when they try to access the site.',
      category: 'Settings',
    },
    {
      question: 'How do I approve or reject content?',
      answer:
        'Navigate to Moderation Tools > Content, find the content item you want to moderate, and use the "Approve" or "Reject" buttons. If rejecting, you\'ll be prompted to provide a reason that will be sent to the content creator.',
      category: 'Moderation',
    },
    {
      question: 'How do I create a new admin user?',
      answer:
        'Go to User Management > Create User, fill in the user details, and select "Admin" from the role dropdown. Be careful when assigning admin privileges as they grant full access to the system.',
      category: 'Users',
    },
    {
      question: 'How do I export user data?',
      answer:
        'Navigate to Bulk Operations > Data, select "Users" as the data type, choose your preferred format (CSV, Excel, JSON), set any filters if needed, and click "Export".',
      category: 'Data',
    },
    {
      question: 'How do I customize email templates?',
      answer:
        'Go to System Settings > Email Templates, select the template you want to edit, make your changes in the editor, and click "Save Template". You can use variables like {{name}} that will be replaced with actual values when the email is sent.',
      category: 'Settings',
    },
    {
      question: 'How do I view system logs?',
      answer:
        'Navigate to Activity in the admin sidebar. Here you can see all actions performed by administrators, filter by action type or date range, and export logs if needed.',
      category: 'System',
    },
    {
      question: 'How do I create a backup of the system?',
      answer:
        'Go to System Settings > Backup, click "Create Manual Backup", select what you want to include in the backup, and click "Start Backup". Once completed, you can download the backup file for safekeeping.',
      category: 'System',
    },
  ];

  const filteredArticles = helpArticles.filter((article) => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      article.title.toLowerCase().includes(query) ||
      article.category.toLowerCase().includes(query) ||
      article.content.toLowerCase().includes(query) ||
      article.tags.some((tag) => tag.toLowerCase().includes(query))
    );
  });

  const filteredTutorials = tutorials.filter((tutorial) => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      tutorial.title.toLowerCase().includes(query) ||
      tutorial.description.toLowerCase().includes(query)
    );
  });

  const filteredFaqs = faqs.filter((faq) => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      faq.question.toLowerCase().includes(query) ||
      faq.answer.toLowerCase().includes(query) ||
      faq.category.toLowerCase().includes(query)
    );
  });

  const handleViewArticle = (article: IHelpArticle) => {
    setSelectedArticle(article);
  };

  const handleStartTutorial = (tutorial: ITutorial) => {
    setSelectedTutorial(tutorial);
    setActiveTutorialStep(0);
    setIsTutorialDialogOpen(true);
  };

  const handleNextTutorialStep = () => {
    if (
      selectedTutorial &&
      selectedTutorial.steps &&
      activeTutorialStep < selectedTutorial.steps.length - 1
    ) {
      setActiveTutorialStep(activeTutorialStep + 1);
    } else {
      setIsTutorialDialogOpen(false);
    }
  };

  const handlePrevTutorialStep = () => {
    if (activeTutorialStep > 0) {
      setActiveTutorialStep(activeTutorialStep - 1);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Admin Help Center</h1>
      </div>

      {/* Search */}
      <div className="relative">
        <Input
          placeholder="Search for help articles, tutorials, or FAQs..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pr-10"
        />
        <RiSearchLine className="absolute right-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
      </div>

      {/* Quick Links */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <Card
          className="cursor-pointer transition-colors hover:bg-accent"
          onClick={() => setActiveTab('documentation')}
        >
          <CardContent className="flex items-center gap-4 p-6">
            <div className="bg-primary/10 rounded-full p-3">
              <RiBookOpenLine className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-medium">Documentation</h3>
              <p className="text-sm text-muted-foreground">
                Comprehensive guides and articles
              </p>
            </div>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer transition-colors hover:bg-accent"
          onClick={() => setActiveTab('tutorials')}
        >
          <CardContent className="flex items-center gap-4 p-6">
            <div className="bg-primary/10 rounded-full p-3">
              <RiVideoLine className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-medium">Video Tutorials</h3>
              <p className="text-sm text-muted-foreground">
                Step-by-step guided tours
              </p>
            </div>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer transition-colors hover:bg-accent"
          onClick={() => setActiveTab('faq')}
        >
          <CardContent className="flex items-center gap-4 p-6">
            <div className="bg-primary/10 rounded-full p-3">
              <RiQuestionLine className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h3 className="font-medium">Frequently Asked Questions</h3>
              <p className="text-sm text-muted-foreground">
                Quick answers to common questions
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs
        defaultValue="documentation"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="documentation">Documentation</TabsTrigger>
          <TabsTrigger value="tutorials">Tutorials</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
        </TabsList>

        <TabsContent value="documentation" className="space-y-6">
          {selectedArticle ? (
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle>{selectedArticle.title}</CardTitle>
                    <CardDescription>
                      Category: {selectedArticle.category} | Last Updated:{' '}
                      {selectedArticle.lastUpdated}
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => setSelectedArticle(null)}
                  >
                    Back to Articles
                  </Button>
                </div>
                <div className="mt-2 flex flex-wrap gap-2">
                  {selectedArticle.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none dark:prose-invert">
                  {selectedArticle.content
                    .split('\n')
                    .map((paragraph, index) => {
                      if (paragraph.startsWith('# ')) {
                        return (
                          <h1
                            key={index}
                            className="mb-4 mt-6 text-2xl font-bold"
                          >
                            {paragraph.substring(2)}
                          </h1>
                        );
                      } else if (paragraph.startsWith('## ')) {
                        return (
                          <h2
                            key={index}
                            className="mb-3 mt-5 text-xl font-bold"
                          >
                            {paragraph.substring(3)}
                          </h2>
                        );
                      } else if (paragraph.startsWith('### ')) {
                        return (
                          <h3
                            key={index}
                            className="mb-2 mt-4 text-lg font-bold"
                          >
                            {paragraph.substring(4)}
                          </h3>
                        );
                      } else if (paragraph.startsWith('- ')) {
                        return (
                          <li key={index} className="ml-6">
                            {paragraph.substring(2)}
                          </li>
                        );
                      } else if (paragraph.startsWith('1. ')) {
                        return (
                          <div key={index} className="ml-6 flex gap-2">
                            <span>{paragraph.substring(0, 2)}</span>
                            <span>{paragraph.substring(3)}</span>
                          </div>
                        );
                      } else if (paragraph.trim() === '') {
                        return <div key={index} className="h-4"></div>;
                      } else {
                        return (
                          <p key={index} className="my-2">
                            {paragraph}
                          </p>
                        );
                      }
                    })}
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {filteredArticles.map((article) => (
                <Card
                  key={article.id}
                  className="cursor-pointer transition-colors hover:bg-accent"
                  onClick={() => handleViewArticle(article)}
                >
                  <CardHeader>
                    <CardTitle>{article.title}</CardTitle>
                    <CardDescription>
                      Category: {article.category}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="line-clamp-2 text-sm text-muted-foreground">
                      {article.content.substring(0, 150)}...
                    </p>
                    <div className="mt-4 flex flex-wrap gap-2">
                      {article.tags.map((tag) => (
                        <Badge key={tag} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <span className="text-xs text-muted-foreground">
                      Last updated: {article.lastUpdated}
                    </span>
                    <Button variant="ghost" size="sm">
                      Read More <RiArrowRightLine className="ml-2" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}

              {filteredArticles.length === 0 && (
                <div className="col-span-2 p-8 text-center">
                  <p className="text-muted-foreground">
                    No articles found matching your search criteria.
                  </p>
                </div>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="tutorials" className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {filteredTutorials.map((tutorial) => (
              <Card key={tutorial.id}>
                <div className="relative aspect-video bg-muted">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Button
                      variant="outline"
                      size="icon"
                      className="rounded-full bg-background/80 backdrop-blur-sm"
                      onClick={() => handleStartTutorial(tutorial)}
                    >
                      <RiPlayLine className="h-6 w-6" />
                    </Button>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle>{tutorial.title}</CardTitle>
                  <CardDescription>{tutorial.description}</CardDescription>
                </CardHeader>
                <CardFooter className="flex justify-between">
                  <Badge variant="outline">{tutorial.duration}</Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleStartTutorial(tutorial)}
                  >
                    Start Tutorial
                  </Button>
                </CardFooter>
              </Card>
            ))}

            {filteredTutorials.length === 0 && (
              <div className="col-span-3 p-8 text-center">
                <p className="text-muted-foreground">
                  No tutorials found matching your search criteria.
                </p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="faq" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>
                Quick answers to common questions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                {filteredFaqs.map((faq, index) => (
                  <AccordionItem key={index} value={`item-${index}`}>
                    <AccordionTrigger>
                      <div className="flex items-center gap-2 text-left">
                        <span>{faq.question}</span>
                        <Badge variant="outline">{faq.category}</Badge>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-muted-foreground">{faq.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))}

                {filteredFaqs.length === 0 && (
                  <div className="p-4 text-center">
                    <p className="text-muted-foreground">
                      No FAQs found matching your search criteria.
                    </p>
                  </div>
                )}
              </Accordion>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Tutorial Dialog */}
      <Dialog
        open={isTutorialDialogOpen}
        onOpenChange={setIsTutorialDialogOpen}
      >
        <DialogContent className="max-w-4xl">
          {selectedTutorial && (
            <>
              <DialogHeader>
                <DialogTitle>{selectedTutorial.title}</DialogTitle>
                <DialogDescription>
                  {selectedTutorial.description}
                </DialogDescription>
              </DialogHeader>

              {selectedTutorial.steps ? (
                <div className="py-4">
                  <div className="mb-4">
                    <div className="mb-2 flex items-center justify-between">
                      <h3 className="font-medium">
                        Step {activeTutorialStep + 1} of{' '}
                        {selectedTutorial.steps.length}:{' '}
                        {selectedTutorial.steps[activeTutorialStep].title}
                      </h3>
                      <Badge variant="outline">
                        {activeTutorialStep + 1}/{selectedTutorial.steps.length}
                      </Badge>
                    </div>
                    <Progress
                      value={
                        ((activeTutorialStep + 1) /
                          selectedTutorial.steps.length) *
                        100
                      }
                      className="h-2"
                    />
                  </div>

                  <div className="rounded-md border bg-accent p-6">
                    <p>
                      {selectedTutorial.steps[activeTutorialStep].description}
                    </p>
                  </div>

                  <div className="mt-6 flex justify-between">
                    <Button
                      variant="outline"
                      onClick={handlePrevTutorialStep}
                      disabled={activeTutorialStep === 0}
                    >
                      Previous
                    </Button>
                    <Button onClick={handleNextTutorialStep}>
                      {activeTutorialStep === selectedTutorial.steps.length - 1
                        ? 'Finish'
                        : 'Next'}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="py-4">
                  <div className="flex aspect-video items-center justify-center rounded-md bg-muted">
                    <p className="text-muted-foreground">
                      Video tutorial would play here
                    </p>
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsTutorialDialogOpen(false)}
                >
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default AdminHelpSystem;
