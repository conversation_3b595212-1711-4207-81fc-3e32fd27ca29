/**
 * @file page.tsx
 * @description Demo page for the DataTable component
 */
'use client';

import { useState } from 'react';
import { DataTable } from '@/components/ui/DataTable';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RiEyeLine, RiEditLine, RiDeleteBinLine } from 'react-icons/ri';
import { ColumnDef } from '@tanstack/react-table';

// Define user data interface
interface IUser {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  lastLogin: string;
}

// Define column structure for the table
const columns: ColumnDef<IUser, any>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) => (
      <span className="text-muted-foreground">
        {row.getValue('id') as string}
      </span>
    ),
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => (
      <span className="font-medium">{row.getValue('name') as string}</span>
    ),
  },
  {
    accessorKey: 'email',
    header: 'Email',
  },
  {
    accessorKey: 'role',
    header: 'Role',
    cell: ({ row }) => {
      const role = row.getValue('role') as string;
      return (
        <Badge
          className={`${
            role === 'Admin'
              ? 'bg-accent/20 text-accent'
              : role === 'Moderator'
                ? 'bg-primary/20 text-primary'
                : 'bg-muted text-muted-foreground'
          }`}
        >
          {role}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return (
        <Badge
          className={`${
            status === 'Active'
              ? 'bg-success/20 text-success'
              : 'bg-destructive/20 text-destructive'
          }`}
        >
          {status}
        </Badge>
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: () => {
      return (
        <div className="flex items-center justify-end space-x-2">
          <Button variant="ghost" size="icon" title="View">
            <RiEyeLine className="h-4 w-4 text-primary" />
          </Button>
          <Button variant="ghost" size="icon" title="Edit">
            <RiEditLine className="text-success h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" title="Delete">
            <RiDeleteBinLine className="h-4 w-4 text-destructive" />
          </Button>
        </div>
      );
    },
  },
];

// TODO: Need to make this dynamic in future - fetch users from API
const demoData: IUser[] = [
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
    lastLogin: '2 hours ago',
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'Moderator',
    status: 'Active',
    lastLogin: '1 day ago',
  },
  {
    id: '3',
    name: 'Mike Brown',
    email: '<EMAIL>',
    role: 'User',
    status: 'Active',
    lastLogin: '3 days ago',
  },
  {
    id: '4',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
    lastLogin: '5 hours ago',
  },
  {
    id: '5',
    name: 'David Wilson',
    email: '<EMAIL>',
    role: 'User',
    status: 'Inactive',
    lastLogin: '2 weeks ago',
  },
  {
    id: '6',
    name: 'Emily Davis',
    email: '<EMAIL>',
    role: 'Moderator',
    status: 'Active',
    lastLogin: '1 day ago',
  },
  {
    id: '7',
    name: 'Alex Turner',
    email: '<EMAIL>',
    role: 'User',
    status: 'Inactive',
    lastLogin: '1 month ago',
  },
  {
    id: '8',
    name: 'Lisa Chen',
    email: '<EMAIL>',
    role: 'User',
    status: 'Active',
    lastLogin: '4 days ago',
  },
  {
    id: '9',
    name: 'Robert Miller',
    email: '<EMAIL>',
    role: 'User',
    status: 'Active',
    lastLogin: '1 week ago',
  },
  {
    id: '10',
    name: 'Amanda White',
    email: '<EMAIL>',
    role: 'Moderator',
    status: 'Active',
    lastLogin: '2 days ago',
  },
  {
    id: '11',
    name: 'James Wilson',
    email: '<EMAIL>',
    role: 'User',
    status: 'Active',
    lastLogin: '6 hours ago',
  },
  {
    id: '12',
    name: 'Patricia Moore',
    email: '<EMAIL>',
    role: 'User',
    status: 'Inactive',
    lastLogin: '3 weeks ago',
  },
];

export default function TableDemoPage() {
  // Define proper type for selected rows
  const [selectedRows, setSelectedRows] = useState<IUser[]>([]);

  const handleRowSelectionChange = (rows: IUser[]) => {
    setSelectedRows(rows);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Advanced Table Demo</h1>
        <div className="flex items-center gap-2">
          {selectedRows.length > 0 && (
            <Button
              variant="outline"
              className="text-destructive hover:bg-destructive/10"
            >
              Delete Selected ({selectedRows.length})
            </Button>
          )}
          <Button className="flex items-center gap-2 rounded-md bg-accent px-4 py-2 text-accent-foreground transition-colors hover:bg-accent/90">
            Add New User
          </Button>
        </div>
      </div>

      <div className="rounded-md border bg-card p-6 shadow-sm">
        <h2 className="mb-4 text-lg font-semibold">User Management Table</h2>
        <p className="mb-6 text-muted-foreground">
          This table demonstrates all the features of our advanced DataTable
          component including pagination, row selection, column visibility
          control, sorting, filtering, and the ability to go to a specific page.
        </p>

        <DataTable
          columns={columns}
          data={demoData}
          searchKey="name"
          showRowSelection={true}
          onRowSelectionChange={handleRowSelectionChange}
        />
      </div>
    </div>
  );
}
