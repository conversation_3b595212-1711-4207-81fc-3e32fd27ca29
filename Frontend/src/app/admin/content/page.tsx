/**
 * @file page.tsx
 * @description Content management page for admin dashboard
 */
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiAddLine,
  RiSearchLine,
  RiFilterLine,
  RiFileTextLine,
  RiEyeLine,
  RiEditLine,
  RiDeleteBinLine,
  RiErrorWarningLine,
} from 'react-icons/ri';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { SelectDropdown } from '@/components/ui/SelectDropdown';
import { extractErrorMessage } from '@/utils/errorUtils';
import {
  useContentList,
  useDeleteContent,
  useUpdateContentStatus,
  IContent,
  ContentType,
  ContentStatus,
  IContentListParams,
} from '@/services/contentService';

export default function ContentPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [contentType, setContentType] = useState<string>('all');
  const [contentStatus, setContentStatus] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // Fixed: removed unused setter
  
  // Custom hooks from contentService
  const [fetchContentList, { data: contentData, isLoading }] = useContentList();
  const [deleteContentById, { isLoading: isDeleting }] = useDeleteContent();
  const [updateStatus, { isLoading: isStatusChanging }] = useUpdateContentStatus();
  
  // State for content data
  const [contentItems, setContentItems] = useState<IContent[]>([]);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  
  // Update state when API data changes
  useEffect(() => {
    if (contentData) {
      setContentItems(contentData.items || []);
      setTotalPages(contentData.meta?.totalPages || 1);
      setTotalItems(contentData.meta?.total || 0);
    }
  }, [contentData]);

  // Prepare API params based on current filters
  const getContentParams = useCallback(() => {
    const params: IContentListParams = {
      page: currentPage,
      limit: itemsPerPage,
    };

    // Add search query if provided
    if (searchQuery.trim()) {
      params.search = searchQuery.trim();
    }

    // Add content type filter if selected
    if (contentType !== 'all') {
      params.type = contentType as ContentType;
    }

    // Add content status filter if selected
    if (contentStatus !== 'all') {
      params.status = contentStatus as ContentStatus;
    }

    return params;
  }, [searchQuery, contentType, contentStatus, currentPage, itemsPerPage]);

  // Fetch content items from API
  useEffect(() => {
    const params = getContentParams();
    fetchContentList({ params });
  }, [fetchContentList, getContentParams]);

  // Handle API errors
  useEffect(() => {
    // Clear any previous errors when data loads successfully
    if (contentData && !isLoading) {
      setError(null);
    }
  }, [contentData, isLoading]);

  // Handle content deletion
  const handleDeleteContent = async (id: string) => {
    if (
      window.confirm(
        'Are you sure you want to delete this content? This action cannot be undone.',
      )
    ) {
      try {
        const response = await deleteContentById(undefined, { id });
        
        if (response?.success) {
          toast({
            title: 'Content Deleted',
            description: 'The content has been successfully deleted.',
          });
          
          // Refresh content list
          const params = getContentParams();
          fetchContentList({ params });
        } else {
          throw new Error(response?.message || 'Failed to delete content');
        }
      } catch (err) {
        console.error('Error deleting content:', err);
        toast({
          title: 'Error',
          description:
            extractErrorMessage(err) ||
            'Failed to delete content. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  // Handle content status change
  const handleStatusChange = async (id: string, status: ContentStatus) => {
    try {
      const response = await updateStatus(
        { status },
        undefined,
        { id }
      );
      
      if (response?.success) {
        toast({
          title: 'Status Updated',
          description: `Content status has been updated to "${status}".`,
        });
        
        // Refresh content list
        const params = getContentParams();
        fetchContentList({ params });
      } else {
        throw new Error(response?.message || 'Failed to update status');
      }
    } catch (err) {
      console.error('Error updating content status:', err);
      toast({
        title: 'Error',
        description:
          extractErrorMessage(err) ||
          'Failed to update content status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Status change dropdown options
  const statusOptions: ContentStatus[] = [
    'published',
    'draft',
    'review',
    'archived',
  ];

  // Handle status change from dropdown
  const handleStatusDropdownChange = (
    id: string,
    event: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    const newStatus = event.target.value as ContentStatus;
    handleStatusChange(id, newStatus);
  };

  // Use mock data for development until API is fully implemented
  useEffect(() => {
    if (contentItems.length === 0 && !isLoading && !error) {
      // Mock data for development
      const mockData: IContent[] = [
        {
          id: '1',
          title: 'Getting Started with JavaScript',
          description: 'Learn the basics of JavaScript programming',
          type: 'Course',
          status: 'published',
          body: 'JavaScript is a programming language...',
          author: {
            id: '101',
            name: 'John Doe',
          },
          tags: ['javascript', 'beginner'],
          createdAt: '2025-05-10T10:30:00',
          updatedAt: '2025-05-12T14:45:00',
          views: 1245,
          likes: 89,
          featured: true,
          thumbnail: '/images/javascript.jpg',
        },
        {
          id: '2',
          title: 'Advanced React Patterns',
          description: 'Master advanced React design patterns',
          type: 'Tutorial',
          status: 'published',
          body: 'In this tutorial, we will explore...',
          author: {
            id: '102',
            name: 'Sarah Johnson',
          },
          tags: ['react', 'advanced'],
          createdAt: '2025-05-08T09:15:00',
          updatedAt: '2025-05-08T09:15:00',
          views: 876,
          likes: 45,
          featured: false,
        },
        {
          id: '3',
          title: 'Introduction to TypeScript',
          description: 'Get started with TypeScript',
          type: 'Course',
          status: 'draft',
          body: 'TypeScript is a strongly typed...',
          author: {
            id: '103',
            name: 'Mike Brown',
          },
          tags: ['typescript', 'beginner'],
          createdAt: '2025-05-15T16:20:00',
          updatedAt: '2025-05-15T16:20:00',
          views: 0,
          likes: 0,
          featured: false,
        },
        {
          id: '4',
          title: 'Building RESTful APIs with Node.js',
          description: 'Learn to build robust APIs with Node.js',
          type: 'Tutorial',
          status: 'published',
          body: 'In this tutorial, we will build...',
          author: {
            id: '104',
            name: 'Jane Doe',
          },
          tags: ['nodejs', 'api', 'rest'],
          createdAt: '2025-05-05T11:10:00',
          updatedAt: '2025-05-14T13:25:00',
          views: 532,
          likes: 32,
          featured: true,
        },
        {
          id: '5',
          title: 'CSS Grid Mastery',
          description: 'Master CSS Grid layout techniques',
          type: 'Course',
          status: 'review',
          body: 'CSS Grid is a powerful layout...',
          author: {
            id: '105',
            name: 'David Wilson',
          },
          tags: ['css', 'grid', 'layout'],
          createdAt: '2025-05-18T08:45:00',
          updatedAt: '2025-05-18T08:45:00',
          views: 0,
          likes: 0,
          featured: false,
        },
      ];

      setContentItems(mockData);
      setTotalItems(mockData.length);
      setTotalPages(1);
    }
  }, [contentItems.length, isLoading, error]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge variant based on content status
  const getStatusBadgeVariant = (
    status: ContentStatus,
  ): 'default' | 'secondary' | 'destructive' | 'outline' | null | undefined => {
    switch (status) {
      case 'published':
        return 'default'; // Primary color with white text
      case 'draft':
        return 'secondary';
      case 'review':
        return 'outline';
      case 'archived':
        return 'destructive';
      default:
        return 'default';
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Navigate to create new content page
  const handleCreateNew = () => {
    router.push('/admin/content/new');
  };

  // Navigate to view content page
  const handleViewContent = (id: string) => {
    router.push(`/admin/content/${id}`);
  };

  // Navigate to edit content page
  const handleEditContent = (id: string) => {
    router.push(`/admin/content/${id}/edit`);
  };

  // Show loading state
  if (isLoading && contentItems.length === 0) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <h3 className="mt-2 text-lg font-medium">Loading Content</h3>
          <p className="text-sm text-muted-foreground">Please wait...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && contentItems.length === 0) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4 text-center">
          <RiErrorWarningLine className="h-16 w-16 text-destructive" />
          <h2 className="text-xl font-semibold">Failed to load content</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button
            variant="outline"
            onClick={() => {
              const params = getContentParams();
              fetchContentList({ params });
            }}
            className="mt-2"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Reset filters
  const handleResetFilters = () => {
    setSearchQuery('');
    setContentType('all');
    setContentStatus('all');
    setCurrentPage(1);
    
    // Fetch content with reset parameters
    fetchContentList({ 
      params: { 
        page: 1,
        limit: itemsPerPage 
      } 
    });
  };

  // Apply filters and search
  const handleApplyFilters = () => {
    // Reset to first page and refetch with current filters
    setCurrentPage(1);
    
    // Fetch content with current parameters
    const params = getContentParams();
    fetchContentList({ params });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Content Management</h1>
        <Button className="flex items-center gap-2" onClick={handleCreateNew}>
          <RiAddLine className="h-4 w-4" /> Create New Content
        </Button>
      </div>

      {/* Search and filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-grow">
          <Input
            type="text"
            placeholder="Search content..."
            className="w-full pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex items-center gap-1">
            <RiFilterLine className="h-4 w-4" /> Filter
          </Button>
          <SelectDropdown
            options={[
              { value: 'all', label: 'All Types' },
              { value: 'Course', label: 'Course' },
              { value: 'Tutorial', label: 'Tutorial' },
              { value: 'Article', label: 'Article' },
              { value: 'Guide', label: 'Guide' }
            ]}
            value={contentType}
            onChange={(value) => setContentType(value)}
            placeholder="Select content type"
          />
          <SelectDropdown
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'published', label: 'Published' },
              { value: 'draft', label: 'Draft' },
              { value: 'review', label: 'Review' },
              { value: 'archived', label: 'Archived' }
            ]}
            value={contentStatus}
            onChange={(value) => setContentStatus(value)}
            placeholder="Select content status"
          />
          <Button
            onClick={handleApplyFilters}
            className="ml-2"
            disabled={isLoading}
          >
            Apply
          </Button>
          <Button
            onClick={handleResetFilters}
            variant="outline"
            className="ml-2"
            disabled={isLoading}
          >
            Reset
          </Button>
        </div>
      </div>



      {/* Content table */}
      <div className="overflow-hidden rounded-md border">
        <table className="w-full">
          <thead className="bg-muted/50">
            <tr className="border-b">
              <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                Title
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                Type
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                Status
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                Author
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                Created
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                Views
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {contentItems.length === 0 ? (
              <tr>
                <td
                  colSpan={7}
                  className="px-4 py-8 text-center text-muted-foreground"
                >
                  No content found. Try adjusting your filters or create new
                  content.
                </td>
              </tr>
            ) : (
              contentItems.map((item) => (
                <tr
                  key={item.id}
                  className="border-b transition-colors hover:bg-muted/50"
                >
                  <td className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      <RiFileTextLine className="h-5 w-5 text-muted-foreground" />
                      <span className="font-medium">{item.title}</span>
                      {item.featured && (
                        <Badge variant="secondary" className="ml-2">
                          Featured
                        </Badge>
                      )}
                    </div>
                    <p className="mt-1 text-xs text-muted-foreground">
                      {item.description}
                    </p>
                  </td>
                  <td className="px-4 py-3 text-sm">{item.type}</td>
                  <td className="px-4 py-3 text-sm">
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={getStatusBadgeVariant(item.status)}
                        className={`capitalize ${item.status === 'published' ? 'text-white' : ''}`}
                      >
                        {item.status}
                      </Badge>
                      {isStatusChanging ? (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                      ) : (
                        <select
                          className="ml-2 h-7 rounded border border-input bg-transparent px-2 py-0 text-xs"
                          value={item.status}
                          onChange={(e) =>
                            handleStatusDropdownChange(item.id, e)
                          }
                          aria-label="Change status"
                        >
                          {statusOptions.map((status) => (
                            <option key={status} value={status}>
                              {status.charAt(0).toUpperCase() + status.slice(1)}
                            </option>
                          ))}
                        </select>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm">{item.author.name}</td>
                  <td className="px-4 py-3 text-sm">
                    {formatDate(item.createdAt)}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {item.views.toLocaleString()}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center justify-center gap-2 rounded-md border border-border bg-card p-1 shadow-sm">
                      <Button
                        isActionButton
                        actionType="primary"
                        size="sm"
                        onClick={() => handleViewContent(item.id)}
                        title="View Content"
                        className="h-8 w-8 p-0"
                      >
                        <RiEyeLine className="h-5 w-5" />
                      </Button>
                      <Button
                        isActionButton
                        actionType="success"
                        size="sm"
                        onClick={() => handleEditContent(item.id)}
                        title="Edit Content"
                        className="h-8 w-8 p-0"
                      >
                        <RiEditLine className="h-5 w-5" />
                      </Button>
                      <Button
                        isActionButton
                        actionType="destructive"
                        size="sm"
                        onClick={() => handleDeleteContent(item.id)}
                        title="Delete Content"
                        disabled={isDeleting}
                        className="h-8 w-8 p-0"
                      >
                        <RiDeleteBinLine className="h-5 w-5" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between border-t pt-4">
          <div className="text-sm text-muted-foreground">
            Showing {contentItems.length} of {totalItems} items
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1 || isLoading}
            >
              Previous
            </Button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePageChange(page)}
                disabled={isLoading}
                className={currentPage === page ? 'text-white hover:text-white dark:text-white dark:hover:text-white' : ''}
              >
                {page}
              </Button>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages || isLoading}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
