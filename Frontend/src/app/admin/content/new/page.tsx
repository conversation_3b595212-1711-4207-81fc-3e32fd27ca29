/**
 * @file page.tsx
 * @description Content creation page
 */
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { RiSaveLine, RiCloseLine, RiImageAddLine } from 'react-icons/ri';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { extractErrorMessage } from '@/utils/errorUtils';
import {
  createContent,
  ContentType,
  ContentStatus,
} from '@/services/contentService';

// Interface for form data
interface IContentFormData {
  title: string;
  description: string;
  type: ContentType;
  status: ContentStatus;
  body: string;
  tags: string[];
  featured: boolean;
  thumbnail?: string;
}

// Interface for form errors
interface IFormErrors {
  title?: string;
  description?: string;
  type?: string;
  status?: string;
  body?: string;
}

export default function CreateContentPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newTag, setNewTag] = useState('');

  // Initial form data
  const [formData, setFormData] = useState<IContentFormData>({
    title: '',
    description: '',
    type: 'Article',
    status: 'draft',
    body: '',
    tags: [],
    featured: false,
  });

  // Form validation errors
  const [errors, setErrors] = useState<IFormErrors>({});

  // Handle input change
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name as keyof IFormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name as keyof IFormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  // Handle switch change
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Add new tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  // Remove tag
  const handleRemoveTag = (tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((t) => t !== tag),
    }));
  };

  // Handle tag input keydown (add tag on Enter)
  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: IFormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.type) {
      newErrors.type = 'Content type is required';
    }

    if (!formData.status) {
      newErrors.status = 'Status is required';
    }

    if (!formData.body.trim()) {
      newErrors.body = 'Content body is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the form.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Prepare data for API
      const contentData = {
        ...formData,
        author: {
          id: '1', // TODO: Get from auth context
          name: 'Admin User', // TODO: Get from auth context
        },
      };

      // Create content via API
      const response = await createContent(contentData);

      toast({
        title: 'Content Created',
        description: 'The content has been successfully created.',
      });

      // Navigate back to content list
      router.push('/admin/content');
    } catch (err) {
      console.error('Error creating content:', err);
      toast({
        title: 'Error',
        description:
          extractErrorMessage(err) ||
          'Failed to create content. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (
      formData.title ||
      formData.description ||
      formData.body ||
      formData.tags.length > 0
    ) {
      if (
        window.confirm(
          'Are you sure you want to cancel? Any unsaved changes will be lost.',
        )
      ) {
        router.push('/admin/content');
      }
    } else {
      router.push('/admin/content');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Create New Content</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
            className="flex items-center gap-1"
          >
            <RiCloseLine className="h-4 w-4" /> Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="flex items-center gap-1"
          >
            {isSubmitting ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
            ) : (
              <RiSaveLine className="h-4 w-4" />
            )}
            Save Content
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-lg font-medium">Basic Information</h2>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter content title"
                className={errors.title ? 'border-destructive' : ''}
              />
              {errors.title && (
                <p className="text-sm text-destructive">{errors.title}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Content Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleSelectChange('type', value)}
              >
                <SelectTrigger
                  id="type"
                  className={errors.type ? 'border-destructive' : ''}
                >
                  <SelectValue placeholder="Select content type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Course">Course</SelectItem>
                  <SelectItem value="Tutorial">Tutorial</SelectItem>
                  <SelectItem value="Article">Article</SelectItem>
                  <SelectItem value="Guide">Guide</SelectItem>
                </SelectContent>
              </Select>
              {errors.type && (
                <p className="text-sm text-destructive">{errors.type}</p>
              )}
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter a brief description"
                rows={3}
                className={errors.description ? 'border-destructive' : ''}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description}</p>
              )}
            </div>
          </div>
        </div>

        {/* Content Body */}
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-lg font-medium">Content Body</h2>

          <div className="space-y-2">
            <Label htmlFor="body">Content</Label>
            <Textarea
              id="body"
              name="body"
              value={formData.body}
              onChange={handleInputChange}
              placeholder="Enter content body"
              rows={12}
              className={errors.body ? 'border-destructive' : ''}
            />
            {errors.body && (
              <p className="text-sm text-destructive">{errors.body}</p>
            )}
            <p className="text-xs text-muted-foreground">
              You can use Markdown formatting for rich text.
            </p>
          </div>
        </div>

        {/* Tags */}
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-lg font-medium">Tags</h2>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={handleTagKeyDown}
                placeholder="Add a tag"
                className="flex-grow"
              />
              <Button
                type="button"
                onClick={handleAddTag}
                variant="outline"
                className="shrink-0"
              >
                Add Tag
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {formData.tags.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No tags added yet.
                </p>
              ) : (
                formData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 rounded-full p-0.5 hover:bg-muted"
                    >
                      <RiCloseLine className="h-3 w-3" />
                    </button>
                  </Badge>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Publishing Options */}
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-lg font-medium">Publishing Options</h2>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger
                  id="status"
                  className={errors.status ? 'border-destructive' : ''}
                >
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="review">Review</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-destructive">{errors.status}</p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="featured"
                checked={formData.featured}
                onCheckedChange={(checked) =>
                  handleSwitchChange('featured', checked)
                }
              />
              <Label htmlFor="featured">Featured Content</Label>
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="thumbnail">Thumbnail Image</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="thumbnail"
                  name="thumbnail"
                  value={formData.thumbnail || ''}
                  onChange={handleInputChange}
                  placeholder="Enter image URL or upload"
                />
                <Button type="button" variant="outline" className="shrink-0">
                  <RiImageAddLine className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Recommended size: 1200x630 pixels
              </p>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
