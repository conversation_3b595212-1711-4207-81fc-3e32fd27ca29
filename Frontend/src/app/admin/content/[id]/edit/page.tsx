/**
 * @file page.tsx
 * @description Content edit page
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiSaveLine,
  RiCloseLine,
  RiImageAddLine,
  RiLoader4Line,
} from 'react-icons/ri';
import { toast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { extractErrorMessage } from '@/utils/errorUtils';
import {
  fetchContentById,
  updateContent,
  ContentType,
  ContentStatus,
  IContent,
} from '@/services/contentService';

// Interface for form data
interface IContentFormData {
  title: string;
  description: string;
  type: ContentType;
  status: ContentStatus;
  body: string;
  tags: string[];
  featured: boolean;
  thumbnail?: string;
}

// Interface for form errors
interface IFormErrors {
  title?: string;
  description?: string;
  type?: string;
  status?: string;
  body?: string;
}

// Props interface
interface ContentEditPageProps {
  params: {
    id: string;
  };
}

export default function ContentEditPage({ params }: ContentEditPageProps) {
  const router = useRouter();
  const { id } = params;

  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newTag, setNewTag] = useState('');
  const [originalContent, setOriginalContent] = useState<IContent | null>(null);

  // Initial form data
  const [formData, setFormData] = useState<IContentFormData>({
    title: '',
    description: '',
    type: 'Article',
    status: 'draft',
    body: '',
    tags: [],
    featured: false,
  });

  // Form validation errors
  const [errors, setErrors] = useState<IFormErrors>({});

  // Fetch content data
  useEffect(() => {
    const fetchContent = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetchContentById(id);
        const content = response.data;

        setOriginalContent(content);
        setFormData({
          title: content.title,
          description: content.description,
          type: content.type,
          status: content.status,
          body: content.body,
          tags: content.tags,
          featured: content.featured,
          thumbnail: content.thumbnail,
        });
      } catch (err) {
        console.error('Error fetching content:', err);
        setError(
          extractErrorMessage(err) ||
            'Failed to load content. Please try again.',
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, [id]);

  // Handle input change
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name as keyof IFormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name as keyof IFormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  // Handle switch change
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Add new tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  // Remove tag
  const handleRemoveTag = (tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((t) => t !== tag),
    }));
  };

  // Handle tag input keydown (add tag on Enter)
  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: IFormErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.type) {
      newErrors.type = 'Content type is required';
    }

    if (!formData.status) {
      newErrors.status = 'Status is required';
    }

    if (!formData.body.trim()) {
      newErrors.body = 'Content body is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors in the form.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Update content via API
      await updateContent(id, formData);

      toast({
        title: 'Content Updated',
        description: 'The content has been successfully updated.',
      });

      // Navigate back to content list
      router.push('/admin/content');
    } catch (err) {
      console.error('Error updating content:', err);
      toast({
        title: 'Error',
        description:
          extractErrorMessage(err) ||
          'Failed to update content. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (
      JSON.stringify(formData) !==
      JSON.stringify({
        title: originalContent?.title || '',
        description: originalContent?.description || '',
        type: originalContent?.type || 'Article',
        status: originalContent?.status || 'draft',
        body: originalContent?.body || '',
        tags: originalContent?.tags || [],
        featured: originalContent?.featured || false,
        thumbnail: originalContent?.thumbnail,
      })
    ) {
      if (
        window.confirm(
          'Are you sure you want to cancel? Any unsaved changes will be lost.',
        )
      ) {
        router.push('/admin/content');
      }
    } else {
      router.push('/admin/content');
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <h3 className="mt-2 text-lg font-medium">Loading Content</h3>
          <p className="text-sm text-muted-foreground">Please wait...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4 text-center">
          <div className="text-destructive">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-16 w-16"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold">Failed to load content</h2>
          <p className="text-muted-foreground">{error}</p>
          <div className="mt-4 flex gap-2">
            <Button
              variant="outline"
              onClick={() => router.push('/admin/content')}
            >
              Back to Content List
            </Button>
            <Button
              onClick={() => {
                setIsLoading(true);
                setError(null);
                window.location.reload();
              }}
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Edit Content</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isSubmitting}
            className="flex items-center gap-1"
          >
            <RiCloseLine className="h-4 w-4" /> Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="flex items-center gap-1"
          >
            {isSubmitting ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
            ) : (
              <RiSaveLine className="h-4 w-4" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-lg font-medium">Basic Information</h2>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter content title"
                className={errors.title ? 'border-destructive' : ''}
              />
              {errors.title && (
                <p className="text-sm text-destructive">{errors.title}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Content Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => handleSelectChange('type', value)}
              >
                <SelectTrigger
                  id="type"
                  className={errors.type ? 'border-destructive' : ''}
                >
                  <SelectValue placeholder="Select content type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Course">Course</SelectItem>
                  <SelectItem value="Tutorial">Tutorial</SelectItem>
                  <SelectItem value="Article">Article</SelectItem>
                  <SelectItem value="Guide">Guide</SelectItem>
                </SelectContent>
              </Select>
              {errors.type && (
                <p className="text-sm text-destructive">{errors.type}</p>
              )}
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter a brief description"
                rows={3}
                className={errors.description ? 'border-destructive' : ''}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description}</p>
              )}
            </div>
          </div>
        </div>

        {/* Content Body */}
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-lg font-medium">Content Body</h2>

          <div className="space-y-2">
            <Label htmlFor="body">Content</Label>
            <Textarea
              id="body"
              name="body"
              value={formData.body}
              onChange={handleInputChange}
              placeholder="Enter content body"
              rows={12}
              className={errors.body ? 'border-destructive' : ''}
            />
            {errors.body && (
              <p className="text-sm text-destructive">{errors.body}</p>
            )}
            <p className="text-xs text-muted-foreground">
              You can use Markdown formatting for rich text.
            </p>
          </div>
        </div>

        {/* Tags */}
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-lg font-medium">Tags</h2>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={handleTagKeyDown}
                placeholder="Add a tag"
                className="flex-grow"
              />
              <Button
                type="button"
                onClick={handleAddTag}
                variant="outline"
                className="shrink-0"
              >
                Add Tag
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {formData.tags.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No tags added yet.
                </p>
              ) : (
                formData.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 rounded-full p-0.5 hover:bg-muted"
                    >
                      <RiCloseLine className="h-3 w-3" />
                    </button>
                  </Badge>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Publishing Options */}
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-lg font-medium">Publishing Options</h2>

          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger
                  id="status"
                  className={errors.status ? 'border-destructive' : ''}
                >
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="review">Review</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-destructive">{errors.status}</p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="featured"
                checked={formData.featured}
                onCheckedChange={(checked) =>
                  handleSwitchChange('featured', checked)
                }
              />
              <Label htmlFor="featured">Featured Content</Label>
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="thumbnail">Thumbnail Image</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="thumbnail"
                  name="thumbnail"
                  value={formData.thumbnail || ''}
                  onChange={handleInputChange}
                  placeholder="Enter image URL or upload"
                />
                <Button type="button" variant="outline" className="shrink-0">
                  <RiImageAddLine className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Recommended size: 1200x630 pixels
              </p>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
