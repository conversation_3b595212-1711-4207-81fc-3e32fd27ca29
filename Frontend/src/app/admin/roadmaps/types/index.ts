export interface IDeleteRoadmapModalProps {
  isOpen: boolean;
  roadmapId: string | null;
  roadmapTitle: string;
  onClose: () => void;
  onConfirm: (id: string) => void;
  isDeleting: boolean;
}

export interface IPaginationParams {
  page: number;
  limit: number;
}

export interface IRoadmapListParams extends IPaginationParams {
  search?: string;
  status?: string;
  category?: string;
  difficulty?: string;
}

export interface IRoadmapUpdateParams {
  id: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  isFeatured?: boolean;
}

export interface IApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}
