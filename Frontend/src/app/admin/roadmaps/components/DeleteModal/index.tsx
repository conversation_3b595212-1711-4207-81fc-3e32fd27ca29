import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { IDeleteRoadmapModalProps } from '../../types';
import { useCallback } from 'react';
import { Button } from '@/components/ui/button';

// DeleteRoadmapModal component
export default function DeleteRoadmapModal({
  isOpen,
  roadmapId,
  roadmapTitle,
  onClose,
  onConfirm,
  isDeleting,
}: IDeleteRoadmapModalProps) {
  const handleConfirm = useCallback(() => {
    if (roadmapId) {
      onConfirm(roadmapId);
    }
  }, [roadmapId, onConfirm]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Roadmap</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete the roadmap{' '}
            <strong>&quot;{roadmapTitle}&quot;</strong>? This action cannot be
            undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
