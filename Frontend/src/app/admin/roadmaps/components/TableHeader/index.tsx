import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CardHeader, CardTitle } from '@/components/ui/card';
import { Dispatch, useCallback } from 'react';
import { debounce } from '@/lib/utils';
import { SetStateAction } from 'react';
import { IRoadmapListParams } from '../../types';

export default function TableHeader({
  params,
  setParams,
}: {
  params: IRoadmapListParams;
  setParams: Dispatch<SetStateAction<IRoadmapListParams>>;
}) {
  // Handlers
  const handleSearchChange = useCallback(
    debounce((value: string) => {
      setParams((prev) => ({ ...prev, search: value, page: 1 }));
    }, 500),
    [],
  );

  const handleStatusFilterChange = useCallback((value: string) => {
    setParams((prev) => ({
      ...prev,
      status: value === 'all' ? '' : value,
      page: 1,
    }));
  }, []);

  const handleCategoryFilterChange = useCallback((value: string) => {
    setParams((prev) => ({ ...prev, category: value, page: 1 }));
  }, []);

  const handleDifficultyFilterChange = useCallback((value: string) => {
    setParams((prev) => ({ ...prev, difficulty: value, page: 1 }));
  }, []);

  return (
    <CardHeader>
      <CardTitle>Manage Roadmaps</CardTitle>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex w-full max-w-sm items-center space-x-2">
          <Input
            placeholder="Search roadmaps..."
            onChange={(e) => handleSearchChange(e.target.value)}
            className="h-9"
            type="search"
          />
        </div>
        <div className="flex flex-wrap gap-2">
          <Select
            value={params.status || ''}
            onValueChange={handleStatusFilterChange}
          >
            <SelectTrigger className="h-9 w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="ACTIVE">Active</SelectItem>
              <SelectItem value="INACTIVE">Inactive</SelectItem>
              <SelectItem value="DRAFT">Draft</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={params.category || ''}
            onValueChange={handleCategoryFilterChange}
          >
            <SelectTrigger className="h-9 w-[120px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              <SelectItem value="frontend">Frontend</SelectItem>
              <SelectItem value="backend">Backend</SelectItem>
              <SelectItem value="fullstack">Full Stack</SelectItem>
              <SelectItem value="mobile">Mobile</SelectItem>
              <SelectItem value="devops">DevOps</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={params.difficulty || ''}
            onValueChange={handleDifficultyFilterChange}
          >
            <SelectTrigger className="h-9 w-[120px]">
              <SelectValue placeholder="Difficulty" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="beginner">Beginner</SelectItem>
              <SelectItem value="intermediate">Intermediate</SelectItem>
              <SelectItem value="advanced">Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </CardHeader>
  );
}
