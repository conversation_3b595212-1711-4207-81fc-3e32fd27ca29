/**
 * @file page.tsx
 * @description Admin roadmaps management page with server-side pagination, search, filtering, and CRUD operations
 */
'use client';

import { useState, useMemo, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  useAxiosGet,
  useAxiosPut,
  useAxiosDelete,
  useAxiosPatch,
} from '@/hooks/useAxios';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { DataTable } from "@/components/ui/DataTable";
import { ColumnDef, Table } from "@tanstack/react-table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import DeleteRoadmapModal from './components/DeleteModal';
import { IRoadmapListParams, IRoadmapUpdateParams } from './types';
import { IApiResponse } from '@/types';
import { ROADMAP_API, IRoadmap, IBulkStatusUpdateParams, IBulkStatusUpdateResponse } from '@/services/roadmapService';
import { UIIcons, FeatureIcons } from '@/constants/icons';
import { debounce } from 'lodash';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';

// Main RoadmapsPage component
function RoadmapsPage() {
  // State for column visibility
  const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>({});
  // State for filter modal
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  // State for selected roadmaps and bulk selection
  const [selectedRoadmaps, setSelectedRoadmaps] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const router = useRouter();

  // State for pagination, filtering, and modal
  const [params, setParams] = useState<IRoadmapListParams>({
    page: 1,
    limit: 10,
    search: '',
    status: '',
    category: '',
    difficulty: '',
  });

  const [deleteModal, setDeleteModal] = useState<{
    open: boolean;
    roadmapId: string | null;
    roadmapTitle: string;
  }>({
    open: false,
    roadmapId: null,
    roadmapTitle: '',
  });

  // API hooks
  const [fetchRoadmaps, roadmapsState] = useAxiosGet<{ data: IRoadmap[] }>(
    ROADMAP_API.LIST,
  );
  const [updateRoadmapStatus] = useAxiosPut<
    IApiResponse<{ roadmap: IRoadmap }>,
    IRoadmapUpdateParams
  >(ROADMAP_API.UPDATE);
  const [toggleFeaturedStatus] = useAxiosPatch<
    IApiResponse<{ data: IRoadmap }>,
    { isFeatured: boolean }
  >(ROADMAP_API.TOGGLE_FEATURED);
  const [deleteRoadmapById, deleteState] = useAxiosDelete<
    IApiResponse<{ success: boolean }>
  >(ROADMAP_API.DELETE);

  // Bulk status update API hook - using direct URL to avoid template string issues
  const [bulkUpdateStatus] = useAxiosPatch<
    IApiResponse<IBulkStatusUpdateResponse>,
    IBulkStatusUpdateParams
  >('/admin/roadmaps/bulk/status');

  // Memoized data
  const roadmaps = useMemo(
    () => roadmapsState.data?.data || [],
    [roadmapsState.data],
  );
  const meta = useMemo(() => roadmapsState.data?.meta, [roadmapsState.data]);
  const isLoading = roadmapsState.isLoading;

  // Load roadmaps function
  const loadRoadmaps = useCallback(() => {
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        queryParams.append(key, value.toString());
      }
    });

    fetchRoadmaps({
      params: queryParams,
    });
  }, [fetchRoadmaps, params]);

  const handlePaginationChange = useCallback(
    (page: number, pageSize: number) => {
      setParams((prev) => ({ ...prev, page, per_page: pageSize }));
    },
    [],
  );

  // Handle bulk status update
  const handleBulkStatusUpdate = useCallback(
    (status: 'DRAFT' | 'ACTIVE' | 'INACTIVE') => {
      if (selectedRoadmaps.length === 0) {
        toast({
          title: 'No Roadmaps Selected',
          description: 'Please select at least one roadmap to update',
          variant: 'destructive',
        });
        return;
      }

      // Show loading toast
      toast({
        title: 'Updating Status',
        description: `Updating ${selectedRoadmaps.length} roadmaps...`,
      });

      bulkUpdateStatus({
        roadmapIds: selectedRoadmaps,
        status: status,
      })
        .then((response) => {
          // Handle the response safely
          const updatedCount = response?.data?.data?.updatedCount || 0;
          toast({
            title: 'Status Updated',
            description: `${updatedCount} roadmaps updated to ${status.toLowerCase()} status`,
          });

          // Clear selections after successful update
          setSelectedRoadmaps([]);
          setSelectAll(false);

          // Refresh the roadmap list
          loadRoadmaps();
        })
        .catch((error) => {
          console.error('Error updating roadmap statuses:', error);
          toast({
            title: 'Error',
            description: `Failed to update roadmap statuses: ${error.message}`,
            variant: 'destructive',
          });
        });
    },
    [bulkUpdateStatus, loadRoadmaps, selectedRoadmaps, toast],
  );

  // Handle row click to navigate to roadmap detail view
  const handleRowClick = useCallback(
    (roadmap: IRoadmap) => {
      // Navigate to the admin view of the roadmap
      router.push(`/admin/roadmaps/${roadmap.id}`);
    },
    [router],
  );

  // Handle individual roadmap selection - will be used by the DataTable component
  // This handler is not directly called in this file but is used by the DataTable internally
  // @ts-expect-error - Lint warning about unused function can be safely ignored
  const handleRoadmapSelection = useCallback(
    (roadmapId: string) => {
      setSelectedRoadmaps((prev) => {
        if (prev.includes(roadmapId)) {
          // If already selected, remove it
          const newSelection = prev.filter((id) => id !== roadmapId);
          // Update selectAll state if needed
          if (newSelection.length === 0) {
            setSelectAll(false);
          }
          return newSelection;
        } else {
          // If not selected, add it
          const newSelection = [...prev, roadmapId];
          // Update selectAll state if all roadmaps are selected
          if (newSelection.length === roadmaps.length) {
            setSelectAll(true);
          }
          return newSelection;
        }
      });
    },
    [roadmaps?.length],
  );

  // Handle select all roadmaps - will be used by the DataTable component
  // This handler is not directly called in this file but is used by the DataTable internally
  // @ts-expect-error - Lint warning about unused function can be safely ignored
  const handleSelectAll = useCallback((selected: boolean) => {
    if (!selected) {
      setSelectedRoadmaps([]);
    } else {
      const allIds = roadmaps.map((roadmap) => roadmap.id);
      setSelectedRoadmaps(allIds);
    }
    setSelectAll(selected);
  }, [roadmaps]);
  
  // These handlers are used by the DataTable component internally
  // The lint warnings about them being unused can be safely ignored
  
  // Custom header checkbox handler to implement the requested toggle behavior
  const handleHeaderCheckboxChange = useCallback((table: Table<IRoadmap>) => {
    const isAllSelected = table.getIsAllPageRowsSelected();
    const isSomeSelected = table.getIsSomePageRowsSelected();
    
    // If all or some are selected, deselect all
    // Otherwise, select all
    if (isAllSelected || isSomeSelected) {
      table.toggleAllPageRowsSelected(false);
    } else {
      table.toggleAllPageRowsSelected(true);
    }
  }, []);

  // Reset selections when roadmaps change or when data is refreshed
  useEffect(() => {
    setSelectedRoadmaps([]);
    setSelectAll(false);
  }, [
    params.page,
    params.status,
    params.search,
    params.difficulty,
    roadmapsState.data,
  ]);

  const handleFeatureToggle = useCallback(
    (id: string, isFeatured: boolean) => {
      // Immediately update the data in the frontend
      setParams((prevParams) => {
        // Create a new reference to avoid mutation
        const updatedRoadmaps = [...(roadmapsState.data?.data || [])];

        // Find the roadmap to update
        const roadmapIndex = updatedRoadmaps.findIndex(
          (roadmap) => roadmap.id === id,
        );
        if (roadmapIndex !== -1) {
          // Create a new roadmap object with the updated is_featured property
          updatedRoadmaps[roadmapIndex] = {
            ...updatedRoadmaps[roadmapIndex],
            is_featured: isFeatured,
          };

          // Update the state with the modified data
          roadmapsState.data = {
            ...roadmapsState.data!,
            data: updatedRoadmaps,
          };
        }

        // Return the previous params unchanged (just to satisfy the setParams function)
        return prevParams;
      });

      // Make the API call in the background
      toggleFeaturedStatus(
        {
          isFeatured,
        },
        {
          // Add toast flag to trigger the toast from the interceptor
          params: {
            toast: true,
          },
        },
        { roadmapId: id },
      ).catch((error: Error) => {
        // Only show error toast and revert the change if the API call fails
        toast({
          variant: 'destructive',
          title: 'Error',
          description: `Failed to toggle featured status: ${error.message}`,
        });

        // Revert the optimistic update
        setParams((prevParams) => {
          // Create a new reference to avoid mutation
          const updatedRoadmaps = [...(roadmapsState.data?.data || [])];

          // Find the roadmap to update
          const roadmapIndex = updatedRoadmaps.findIndex(
            (roadmap) => roadmap.id === id,
          );
          if (roadmapIndex !== -1) {
            // Revert the is_featured property
            updatedRoadmaps[roadmapIndex] = {
              ...updatedRoadmaps[roadmapIndex],
              is_featured: !isFeatured,
            };

            // Update the state with the modified data
            roadmapsState.data = {
              ...roadmapsState.data!,
              data: updatedRoadmaps,
            };
          }

          // Return the previous params unchanged
          return prevParams;
        });
      });
    },
    [toggleFeaturedStatus, roadmapsState.data],
  );

  const handleStatusChange = useCallback(
    (id: string, status: 'ACTIVE' | 'INACTIVE' | 'DRAFT') => {
      // Immediately update the data in the frontend
      setParams((prevParams) => {
        // Create a new reference to avoid mutation
        const updatedRoadmaps = [...(roadmapsState.data?.data || [])];

        // Find the roadmap to update
        const roadmapIndex = updatedRoadmaps.findIndex(
          (roadmap) => roadmap.id === id,
        );
        if (roadmapIndex !== -1) {
          // Create a new roadmap object with the updated status property
          updatedRoadmaps[roadmapIndex] = {
            ...updatedRoadmaps[roadmapIndex],
            status,
          };

          // Update the state with the modified data
          roadmapsState.data = {
            ...roadmapsState.data!,
            data: updatedRoadmaps,
          };
        }

        // Return the previous params unchanged (just to satisfy the setParams function)
        return prevParams;
      });

      // Make the API call in the background
      updateRoadmapStatus(
        {
          id,
          status,
        },
        {
          // Add toast flag to trigger the toast from the interceptor
          params: {
            toast: true,
          },
        },
        {
          id,
        },
      ).catch((error: Error) => {
        // Only show error toast and revert the change if the API call fails
        toast({
          variant: 'destructive',
          title: 'Error',
          description: `Failed to update roadmap status: ${error.message}`,
        });

        // Revert the optimistic update
        setParams((prevParams) => {
          // Create a new reference to avoid mutation
          const updatedRoadmaps = [...(roadmapsState.data?.data || [])];

          // Find the roadmap to update
          const roadmapIndex = updatedRoadmaps.findIndex(
            (roadmap) => roadmap.id === id,
          );
          if (roadmapIndex !== -1) {
            // Get the current status to revert from
            const currentStatus = updatedRoadmaps[roadmapIndex].status;

            // Only revert if the current status matches what we tried to set
            // This prevents reverting if another status change happened in between
            if (currentStatus === status) {
              // Find the previous status by checking which dropdown item was clicked
              // Default to 'DRAFT' if we can't determine the previous status
              const previousStatus =
                currentStatus === 'ACTIVE'
                  ? 'INACTIVE'
                  : currentStatus === 'INACTIVE'
                    ? 'DRAFT'
                    : 'ACTIVE';

              // Revert the status property
              updatedRoadmaps[roadmapIndex] = {
                ...updatedRoadmaps[roadmapIndex],
                status: previousStatus,
              };

              // Update the state with the modified data
              roadmapsState.data = {
                ...roadmapsState.data!,
                data: updatedRoadmaps,
              };
            }
          }

          // Return the previous params unchanged
          return prevParams;
        });
      });
    },
    [updateRoadmapStatus, roadmapsState.data],
  );

  const handleDeleteClick = useCallback((id: string, title: string) => {
    setDeleteModal({
      open: true,
      roadmapId: id,
      roadmapTitle: title,
    });
  }, []);

  const handleDeleteConfirm = useCallback(
    (id: string) => {
      deleteRoadmapById({}, { roadmapId: id })
        .then(() => {
          toast({
            title: 'Success',
            description: 'Roadmap deleted successfully',
          });
          setDeleteModal((prev) => ({ ...prev, open: false }));
          loadRoadmaps();
        })
        .catch((error: Error) => {
          toast({
            variant: 'destructive',
            title: 'Error',
            description: `Failed to delete roadmap: ${error.message}`,
          });
        });
    },
    [deleteRoadmapById, loadRoadmaps],
  );

  // Load data on mount and when params change
  const debouncedLoadRoadmaps = useCallback(
    debounce(() => {
      loadRoadmaps();
    }, 300),
    [loadRoadmaps],
  );

  // Effect to load roadmaps when component mounts
  useEffect(() => {
    debouncedLoadRoadmaps();
  }, [debouncedLoadRoadmaps]);

  // Define columns for the roadmaps table
  const columns = useMemo<ColumnDef<IRoadmap>[]>(
    () => [
      // The selection column is now handled by the DataTable component internally
      // Roadmap column
      {
        accessorKey: 'title',
        header: 'Roadmap',
        cell: ({ row }) => {
          const roadmap = row.original;
          return (
            <div className="flex items-center gap-2">
              <FeatureIcons.Roadmap className="h-5 w-5 text-muted-foreground" />
              <div>
                <div className="font-medium">{roadmap.title}</div>
                <div className="text-sm text-muted-foreground">
                  {roadmap.category?.name || 'No category'}
                </div>
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: 'status',
        header: 'Status',
        cell: ({ row }) => {
          const status = (row.getValue('status') as string || '').toLowerCase();
          // Color mapping for status levels
          let colorClass = '';
          if (status === 'active') {
            colorClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
          } else if (status === 'inactive') {
            colorClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
          } else if (status === 'draft') {
            colorClass = 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
          } else {
            // Default styling for unknown status
            colorClass = 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
          }
          return (
            <span
              className={`rounded-md px-2 py-1 text-xs font-medium ${colorClass}`}
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
          );
        },
      },
      {
        accessorKey: 'is_featured',
        header: 'Featured',
        cell: ({ row }) => {
          const roadmap = row.original;
          return (
            <Switch
              checked={!!roadmap.is_featured}
              onCheckedChange={(checked) =>
                handleFeatureToggle(roadmap.id, checked)
              }
              aria-label="Toggle feature status"
              className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input hover:data-[state=unchecked]:bg-muted"
            />
          );
        },
      },
      {
        accessorKey: 'difficulty',
        header: 'Difficulty',
        cell: ({ row }) => {
          const difficulty = (row.original.difficulty as string) || 'N/A';
          // Color mapping for difficulty levels
          let colorClass = '';
          // Convert to lowercase for case-insensitive comparison
          const difficultyLower = difficulty.toLowerCase();
          if (difficultyLower === 'easy') {
            colorClass =
              'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
          } else if (difficultyLower === 'medium') {
            colorClass =
              'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
          } else if (difficultyLower === 'hard') {
            colorClass =
              'bg-rose-100 text-rose-700 dark:bg-rose-950 dark:text-rose-300';
          } else {
            // Default styling for unknown difficulty levels
            colorClass =
              'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
          }
          return (
            <span
              className={`rounded-md px-2 py-1 text-xs font-medium ${colorClass}`}
            >
              {difficulty}
            </span>
          );
        },
      },
      {
        accessorKey: 'enrollmentCount',
        header: 'Enrollments',
        cell: ({ row }) => row.original.enrollmentCount || 0,
      },
      {
        accessorKey: 'completionRate',
        header: 'Completion',
        cell: ({ row }) =>
          row.original.completionRate
            ? `${row.original.completionRate}%`
            : 'N/A',
      },
      {
        accessorKey: 'created_at',
        header: 'Created',
        cell: ({ row }) => {
          const date = row.original.created_at;
          if (!date) return 'N/A';
          return format(new Date(date), 'MMM d, yyyy');
        },
      },
      {
        id: 'actions',
        cell: ({ row }) => {
          const roadmap = row.original;
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <UIIcons.More className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => router.push(`/admin/roadmaps/${roadmap.id}`)}
                  className="text-green-600 hover:text-green-700 focus:text-green-700"
                >
                  <UIIcons.View className="mr-2 h-4 w-4" />
                  View
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    router.push(`/admin/roadmaps/${roadmap.id}/edit`)
                  }
                  className="text-blue-600 hover:text-blue-700 focus:text-blue-700"
                >
                  <UIIcons.Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => handleStatusChange(roadmap.id, 'ACTIVE')}
                  disabled={roadmap.status === 'ACTIVE'}
                >
                  Set as Active
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleStatusChange(roadmap.id, 'INACTIVE')}
                  disabled={roadmap.status === 'INACTIVE'}
                >
                  Set as Inactive
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleStatusChange(roadmap.id, 'DRAFT')}
                  disabled={roadmap.status === 'DRAFT'}
                >
                  Set as Draft
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-destructive hover:text-destructive focus:text-destructive"
                  onClick={() => handleDeleteClick(roadmap.id, roadmap.title)}
                >
                  <UIIcons.Delete className="mr-2 h-4 w-4 text-destructive" />
                  <span className="text-destructive font-medium">Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [router, handleFeatureToggle, handleStatusChange, handleDeleteClick],
  );

  return (
    <>
      <div className="container max-w-7xl py-10">
        <div className="mb-6 flex items-center justify-between">
          <h1 className="text-3xl font-bold">Roadmaps</h1>
          <div className="flex items-center gap-2">
            {selectedRoadmaps.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Bulk Actions ({selectedRoadmaps.length})
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => handleBulkStatusUpdate('ACTIVE')}
                    className="cursor-pointer"
                  >
                    Set Active
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleBulkStatusUpdate('INACTIVE')}
                    className="cursor-pointer"
                  >
                    Set Inactive
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleBulkStatusUpdate('DRAFT')}
                    className="cursor-pointer"
                  >
                    Set Draft
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => {
                      // TODO: Implement bulk delete functionality
                      console.log('Bulk delete', selectedRoadmaps);
                    }}
                    className="cursor-pointer"
                  >
                    <UIIcons.Delete className="text-destructive mr-2 h-4 w-4" />
                    <span className="text-destructive font-medium">Delete Selected</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            
            {/* Filter Modal */}
            <Dialog open={filterModalOpen} onOpenChange={setFilterModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="mr-2">
                  <UIIcons.Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Table Filters</DialogTitle>
                  <DialogDescription>
                    Customize your table view by adjusting filters and column visibility.
                  </DialogDescription>
                </DialogHeader>
                
                <div className="py-4 space-y-6">
                  {/* Status Filter */}
                  <div>
                    <h3 className="text-sm font-medium mb-2">Status</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {['Active', 'Draft', 'Archived'].map((status) => (
                        <div key={status} className="flex items-center space-x-2">
                          <Checkbox 
                            id={`status-${status.toLowerCase()}`}
                            // TODO: Implement status filtering
                            onClick={(e: React.MouseEvent) => e.stopPropagation()}
                          />
                          <label 
                            htmlFor={`status-${status.toLowerCase()}`}
                            className="text-sm"
                          >
                            {status}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Difficulty Filter */}
                  <div>
                    <h3 className="text-sm font-medium mb-2">Difficulty</h3>
                    <div className="grid grid-cols-2 gap-2">
                      {['Easy', 'Medium', 'Hard'].map((difficulty) => (
                        <div key={difficulty} className="flex items-center space-x-2">
                          <Checkbox 
                            id={`difficulty-${difficulty.toLowerCase()}`}
                            // TODO: Implement difficulty filtering
                            onClick={(e: React.MouseEvent) => e.stopPropagation()}
                          />
                          <label 
                            htmlFor={`difficulty-${difficulty.toLowerCase()}`}
                            className="text-sm"
                          >
                            {difficulty}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Column Visibility */}
                  <div>
                    <h3 className="text-sm font-medium mb-2">Column Visibility</h3>
                    <div className="space-y-2">
                      {columns.map((column) => {
                        // Skip columns that shouldn't be hidden
                        if (!column.id || column.id === 'actions' || column.id === 'select') return null;
                        
                        const columnTitle = typeof column.header === 'string' 
                          ? column.header 
                          : column.id.charAt(0).toUpperCase() + column.id.slice(1);
                        
                        return (
                          <div key={column.id} className="flex items-center justify-between">
                            <span className="text-sm">{columnTitle}</span>
                            <Switch
                              checked={columnVisibility[column.id] !== false}
                              onCheckedChange={(checked) => {
                                setColumnVisibility(prev => ({
                                  ...prev,
                                  [column.id as string]: checked
                                }));
                              }}
                              onClick={(e: React.MouseEvent) => e.stopPropagation()}
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            
            <Button
              variant="default"
              onClick={() => router.push('/admin/roadmaps/add')}
              className="ml-auto"
            >
              <UIIcons.Add className="h-4 w-4 mr-2" />
              Add Roadmap
            </Button>
          </div>
        </div>

        <div className="overflow-hidden">
          <DataTable
            columns={columns}
            data={roadmaps}
            isLoading={isLoading}
            serverSidePagination={true}
            pageCount={meta?.last_page || 1}
            initialPageIndex={(meta?.current_page || 1) - 1}
            initialPageSize={meta?.per_page || 10}
            onRowClick={handleRowClick}
            onRowSelectionChange={(rows) => {
              const selectedIds = rows.map(row => row.id);
              setSelectedRoadmaps(selectedIds);
              setSelectAll(rows.length === roadmaps.length && rows.length > 0);
            }}
            showRowSelection={true}
            columnVisibility={columnVisibility}
            onColumnVisibilityChange={setColumnVisibility}
            onHeaderCheckboxChange={handleHeaderCheckboxChange}
            onPaginationChange={(pageIndex, pageSize) => {
              // pageIndex is 0-based, but API expects 1-based
              handlePaginationChange(pageIndex + 1, pageSize);
            }}
          />
        </div>
      </div>

      <DeleteRoadmapModal
        isOpen={deleteModal.open}
        roadmapId={deleteModal.roadmapId}
        roadmapTitle={deleteModal.roadmapTitle}
        onClose={() => setDeleteModal((prev) => ({ ...prev, open: false }))}
        onConfirm={handleDeleteConfirm}
        isDeleting={deleteState.isLoading}
      />
    </>
  );
}

export default RoadmapsPage;
