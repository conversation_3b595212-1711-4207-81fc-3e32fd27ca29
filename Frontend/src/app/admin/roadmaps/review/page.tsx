/**
 * @file page.tsx
 * @description Roadmap approval workflow page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiCheckLine,
  RiCloseLine,
  RiRoadMapLine,
  RiUserLine,
  RiTimeLine,
  RiCalendarLine,
  RiStarLine,
  RiEyeLine,
  RiFilterLine,
  RiSearchLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';

// Define interfaces
interface ISubmittedRoadmap {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  submittedBy: {
    id: string;
    name: string;
  };
  submittedAt: string;
  status: 'Pending' | 'Approved' | 'Rejected';
  reviewedBy?: {
    id: string;
    name: string;
  };
  reviewedAt?: string;
  feedback?: string;
  sectionsCount: number;
  lessonsCount: number;
  estimatedHours: number;
}

function RoadmapReviewPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [roadmaps, setRoadmaps] = useState<ISubmittedRoadmap[]>([]);
  const [filteredRoadmaps, setFilteredRoadmaps] = useState<ISubmittedRoadmap[]>(
    [],
  );
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRoadmap, setSelectedRoadmap] =
    useState<ISubmittedRoadmap | null>(null);
  const [feedbackText, setFeedbackText] = useState('');
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);

  useEffect(() => {
    // TODO: Replace with actual API call
    const fetchSubmittedRoadmaps = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock submitted roadmaps data
        const mockRoadmaps: ISubmittedRoadmap[] = [
          {
            id: '101',
            title: 'Python for Data Science',
            description:
              'A comprehensive roadmap for learning Python specifically for data science applications.',
            category: 'Data Science',
            difficulty: 'Intermediate',
            submittedBy: {
              id: 'user1',
              name: 'John Smith',
            },
            submittedAt: '2023-06-10T14:30:00Z',
            status: 'Pending',
            sectionsCount: 5,
            lessonsCount: 18,
            estimatedHours: 35,
          },
          {
            id: '102',
            title: 'Blockchain Development Fundamentals',
            description:
              'Learn the basics of blockchain technology and how to develop decentralized applications.',
            category: 'Blockchain',
            difficulty: 'Advanced',
            submittedBy: {
              id: 'user2',
              name: 'Emily Johnson',
            },
            submittedAt: '2023-06-08T10:15:00Z',
            status: 'Pending',
            sectionsCount: 6,
            lessonsCount: 22,
            estimatedHours: 40,
          },
          {
            id: '103',
            title: 'UI/UX Design for Mobile Apps',
            description:
              'A practical guide to designing user interfaces and experiences for mobile applications.',
            category: 'Design',
            difficulty: 'Beginner',
            submittedBy: {
              id: 'user3',
              name: 'Michael Chen',
            },
            submittedAt: '2023-06-05T16:45:00Z',
            status: 'Approved',
            reviewedBy: {
              id: 'admin1',
              name: 'Admin User',
            },
            reviewedAt: '2023-06-06T09:30:00Z',
            feedback:
              'Great roadmap with clear progression. Approved for publication.',
            sectionsCount: 4,
            lessonsCount: 15,
            estimatedHours: 25,
          },
          {
            id: '104',
            title: 'Game Development with Unity',
            description:
              'Learn to create 2D and 3D games using the Unity game engine and C#.',
            category: 'Game Development',
            difficulty: 'Intermediate',
            submittedBy: {
              id: 'user4',
              name: 'Sarah Williams',
            },
            submittedAt: '2023-06-03T11:20:00Z',
            status: 'Rejected',
            reviewedBy: {
              id: 'admin1',
              name: 'Admin User',
            },
            reviewedAt: '2023-06-04T14:10:00Z',
            feedback:
              'The content needs more practical examples and projects. Please revise and resubmit with more hands-on lessons.',
            sectionsCount: 3,
            lessonsCount: 12,
            estimatedHours: 30,
          },
          {
            id: '105',
            title: 'Cloud Computing with AWS',
            description:
              'Master Amazon Web Services for cloud infrastructure and deployment.',
            category: 'Cloud Computing',
            difficulty: 'Advanced',
            submittedBy: {
              id: 'user5',
              name: 'David Rodriguez',
            },
            submittedAt: '2023-06-01T09:00:00Z',
            status: 'Approved',
            reviewedBy: {
              id: 'admin2',
              name: 'Admin Manager',
            },
            reviewedAt: '2023-06-02T13:45:00Z',
            feedback:
              'Excellent roadmap with comprehensive coverage of AWS services. Approved for publication.',
            sectionsCount: 7,
            lessonsCount: 25,
            estimatedHours: 50,
          },
        ];

        setRoadmaps(mockRoadmaps);
        setFilteredRoadmaps(mockRoadmaps);
        setLoading(false);
      }, 500);
    };

    fetchSubmittedRoadmaps();
  }, []);

  // Filter roadmaps based on status and search query
  useEffect(() => {
    let filtered = [...roadmaps];

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter((roadmap) => roadmap.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (roadmap) =>
          roadmap.title.toLowerCase().includes(query) ||
          roadmap.description.toLowerCase().includes(query) ||
          roadmap.submittedBy.name.toLowerCase().includes(query),
      );
    }

    setFilteredRoadmaps(filtered);
  }, [roadmaps, statusFilter, searchQuery]);

  // Handle roadmap approval
  const handleApproveRoadmap = () => {
    if (!selectedRoadmap) return;

    setIsApproving(true);

    // TODO: Implement actual API call to approve roadmap
    setTimeout(() => {
      // Update roadmap status
      const updatedRoadmaps = roadmaps.map((roadmap) =>
        roadmap.id === selectedRoadmap.id
          ? {
              ...roadmap,
              status: 'Approved' as const,
              reviewedBy: {
                id: 'admin1',
                name: 'Admin User',
              },
              reviewedAt: new Date().toISOString(),
              feedback: feedbackText,
            }
          : roadmap,
      );

      setRoadmaps(updatedRoadmaps);
      setFilteredRoadmaps(
        updatedRoadmaps.filter(
          (roadmap) =>
            (statusFilter === 'all' || roadmap.status === statusFilter) &&
            (!searchQuery ||
              roadmap.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              roadmap.description
                .toLowerCase()
                .includes(searchQuery.toLowerCase()) ||
              roadmap.submittedBy.name
                .toLowerCase()
                .includes(searchQuery.toLowerCase())),
        ),
      );

      setSelectedRoadmap(null);
      setFeedbackText('');
      setIsApproving(false);

      toast({
        title: 'Roadmap Approved',
        description: `"${selectedRoadmap.title}" has been approved and is now published.`,
      });
    }, 1000);
  };

  // Handle roadmap rejection
  const handleRejectRoadmap = () => {
    if (!selectedRoadmap) return;

    setIsRejecting(true);

    // TODO: Implement actual API call to reject roadmap
    setTimeout(() => {
      // Update roadmap status
      const updatedRoadmaps = roadmaps.map((roadmap) =>
        roadmap.id === selectedRoadmap.id
          ? {
              ...roadmap,
              status: 'Rejected' as const,
              reviewedBy: {
                id: 'admin1',
                name: 'Admin User',
              },
              reviewedAt: new Date().toISOString(),
              feedback: feedbackText,
            }
          : roadmap,
      );

      setRoadmaps(updatedRoadmaps);
      setFilteredRoadmaps(
        updatedRoadmaps.filter(
          (roadmap) =>
            (statusFilter === 'all' || roadmap.status === statusFilter) &&
            (!searchQuery ||
              roadmap.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              roadmap.description
                .toLowerCase()
                .includes(searchQuery.toLowerCase()) ||
              roadmap.submittedBy.name
                .toLowerCase()
                .includes(searchQuery.toLowerCase())),
        ),
      );

      setSelectedRoadmap(null);
      setFeedbackText('');
      setIsRejecting(false);

      toast({
        title: 'Roadmap Rejected',
        description: `"${selectedRoadmap.title}" has been rejected with feedback.`,
        variant: 'destructive',
      });
    }, 1000);
  };

  // Handle roadmap selection for review
  const handleReviewRoadmap = (roadmap: ISubmittedRoadmap) => {
    setSelectedRoadmap(roadmap);
    setFeedbackText(roadmap.feedback || '');
  };

  // Handle cancel review
  const handleCancelReview = () => {
    setSelectedRoadmap(null);
    setFeedbackText('');
  };

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-4 text-muted-foreground">
            Loading submitted roadmaps...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => router.push('/admin/roadmaps')}
          >
            <RiArrowLeftLine className="mr-1" /> Back to Roadmaps
          </Button>
          <h1 className="text-2xl font-bold">Roadmap Approval Workflow</h1>
          <p className="text-muted-foreground">
            Review and approve user-submitted roadmaps
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-grow">
          <Input
            placeholder="Search roadmaps..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
        </div>

        <div className="w-full md:w-48">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger>
              <div className="flex items-center">
                <RiFilterLine className="mr-2" />
                <SelectValue placeholder="Filter by status" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="Approved">Approved</SelectItem>
              <SelectItem value="Rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {/* Roadmaps List */}
        <div className="md:col-span-2">
          <h2 className="mb-4 text-lg font-semibold">Submitted Roadmaps</h2>

          {filteredRoadmaps.length > 0 ? (
            <div className="space-y-4">
              {filteredRoadmaps.map((roadmap) => (
                <Card
                  key={roadmap.id}
                  className={`cursor-pointer overflow-hidden transition-colors hover:border-primary ${selectedRoadmap?.id === roadmap.id ? 'border-primary' : ''}`}
                  onClick={() => handleReviewRoadmap(roadmap)}
                >
                  <div className="p-6">
                    <div className="flex items-start justify-between gap-4">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="text-lg font-semibold">
                            {roadmap.title}
                          </h3>
                          <Badge
                            className={`${
                              roadmap.status === 'Pending'
                                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                                : roadmap.status === 'Approved'
                                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                  : 'bg-red-100 text-red-800 hover:bg-red-200'
                            }`}
                          >
                            {roadmap.status}
                          </Badge>
                        </div>

                        <p className="mt-2 text-muted-foreground">
                          {roadmap.description}
                        </p>

                        <div className="mt-4 flex flex-wrap items-center gap-x-6 gap-y-2 text-sm">
                          <div className="flex items-center">
                            <RiRoadMapLine className="mr-1 text-muted-foreground" />
                            <span>{roadmap.category}</span>
                          </div>
                          <div className="flex items-center">
                            <RiUserLine className="mr-1 text-muted-foreground" />
                            <span>{roadmap.submittedBy.name}</span>
                          </div>
                          <div className="flex items-center">
                            <RiCalendarLine className="mr-1 text-muted-foreground" />
                            <span>
                              {new Date(
                                roadmap.submittedAt,
                              ).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 border-t border-border pt-4">
                      <div className="flex flex-wrap items-center justify-between gap-2">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center">
                            <span className="mr-1 text-sm font-medium">
                              {roadmap.sectionsCount}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              sections
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="mr-1 text-sm font-medium">
                              {roadmap.lessonsCount}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              lessons
                            </span>
                          </div>
                          <div className="flex items-center">
                            <RiTimeLine className="mr-1 text-muted-foreground" />
                            <span className="text-sm">
                              {roadmap.estimatedHours} hours
                            </span>
                          </div>
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/admin/roadmaps/${roadmap.id}`);
                          }}
                        >
                          <RiEyeLine /> View Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="rounded-md border py-12 text-center">
              <RiRoadMapLine className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">No roadmaps found</h3>
              <p className="mt-2 text-muted-foreground">
                {searchQuery || statusFilter !== 'all'
                  ? 'Try adjusting your filters to see more roadmaps.'
                  : 'There are no submitted roadmaps to review at this time.'}
              </p>
            </div>
          )}
        </div>

        {/* Review Panel */}
        <div>
          {selectedRoadmap ? (
            <Card>
              <CardHeader>
                <CardTitle>Review Roadmap</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold">
                    {selectedRoadmap.title}
                  </h3>
                  <Badge
                    className={`mt-1 ${
                      selectedRoadmap.status === 'Pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : selectedRoadmap.status === 'Approved'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {selectedRoadmap.status}
                  </Badge>
                </div>

                <div>
                  <p className="text-sm text-muted-foreground">
                    {selectedRoadmap.description}
                  </p>
                </div>

                <div className="border-t border-border pt-4">
                  <h4 className="mb-2 text-sm font-medium">
                    Submission Details
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Submitted By:
                      </span>
                      <span>{selectedRoadmap.submittedBy.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Submitted On:
                      </span>
                      <span>
                        {new Date(
                          selectedRoadmap.submittedAt,
                        ).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Category:</span>
                      <span>{selectedRoadmap.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Difficulty:</span>
                      <span>{selectedRoadmap.difficulty}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Estimated Hours:
                      </span>
                      <span>{selectedRoadmap.estimatedHours}</span>
                    </div>
                  </div>
                </div>

                {selectedRoadmap.status !== 'Pending' && (
                  <div className="border-t border-border pt-4">
                    <h4 className="mb-2 text-sm font-medium">Review Details</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Reviewed By:
                        </span>
                        <span>{selectedRoadmap.reviewedBy?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Reviewed On:
                        </span>
                        <span>
                          {selectedRoadmap.reviewedAt
                            ? new Date(
                                selectedRoadmap.reviewedAt,
                              ).toLocaleDateString()
                            : '-'}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                <div className="border-t border-border pt-4">
                  <label
                    className="mb-2 block text-sm font-medium"
                    htmlFor="feedback"
                  >
                    {selectedRoadmap.status === 'Pending'
                      ? 'Feedback'
                      : 'Review Feedback'}
                  </label>
                  <Textarea
                    id="feedback"
                    placeholder="Enter feedback for the roadmap creator..."
                    value={feedbackText}
                    onChange={(e) => setFeedbackText(e.target.value)}
                    rows={4}
                    disabled={selectedRoadmap.status !== 'Pending'}
                  />
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  {selectedRoadmap.status === 'Pending' ? (
                    <>
                      <Button variant="outline" onClick={handleCancelReview}>
                        Cancel
                      </Button>
                      <Button
                        variant="outline"
                        className="border-red-300 text-red-700 hover:bg-red-50"
                        onClick={handleRejectRoadmap}
                        disabled={isRejecting || isApproving}
                      >
                        <RiCloseLine className="mr-1" />
                        {isRejecting ? 'Rejecting...' : 'Reject'}
                      </Button>
                      <Button
                        className="border-green-300 bg-green-100 text-green-700 hover:bg-green-200"
                        onClick={handleApproveRoadmap}
                        disabled={isApproving || isRejecting}
                      >
                        <RiCheckLine className="mr-1" />
                        {isApproving ? 'Approving...' : 'Approve'}
                      </Button>
                    </>
                  ) : (
                    <Button variant="outline" onClick={handleCancelReview}>
                      Close
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-6">
                <div className="py-8 text-center">
                  <RiRoadMapLine className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">Select a Roadmap</h3>
                  <p className="mt-2 text-muted-foreground">
                    Click on a roadmap from the list to review and approve or
                    reject it.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <RiStarLine className="mr-2" /> Featured Roadmaps
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-sm text-muted-foreground">
                After approving a roadmap, you can feature it on the homepage to
                give it more visibility.
              </p>

              <div className="space-y-4">
                <div className="flex items-center justify-between rounded-md bg-muted p-3">
                  <div>
                    <div className="font-medium">Frontend Web Development</div>
                    <div className="text-xs text-muted-foreground">
                      1,245 enrollments
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="h-8">
                    <RiStarLine className="mr-1" /> Featured
                  </Button>
                </div>

                <div className="flex items-center justify-between rounded-md bg-muted p-3">
                  <div>
                    <div className="font-medium">
                      Machine Learning Fundamentals
                    </div>
                    <div className="text-xs text-muted-foreground">
                      723 enrollments
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="h-8">
                    <RiStarLine className="mr-1" /> Featured
                  </Button>
                </div>

                <div className="flex items-center justify-between rounded-md bg-muted p-3">
                  <div>
                    <div className="font-medium">Cloud Computing with AWS</div>
                    <div className="text-xs text-muted-foreground">
                      512 enrollments
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="h-8">
                    <RiStarLine className="mr-1" /> Featured
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default RoadmapReviewPage;
