/**
 * @file RoadmapFeedback.tsx
 * @description Component to display user feedback and comments for a roadmap
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiMessage2Line,
  RiStarLine,
  RiStarFill,
  RiUserLine,
  RiCalendarLine,
  RiThumbUpLine,
  RiThumbDownLine,
  RiFilterLine,
  RiMailLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';

// Define interfaces for feedback data
interface IReview {
  id: string;
  userId: string;
  userName: string;
  rating: number;
  comment: string;
  date: string;
  isHelpful: number;
  isNotHelpful: number;
  adminResponse?: string;
}

interface IRoadmapFeedbackProps {
  roadmapId: string;
}

function RoadmapFeedback({ roadmapId }: IRoadmapFeedbackProps) {
  const [loading, setLoading] = useState(true);
  const [reviews, setReviews] = useState<IReview[]>([]);
  const [filteredReviews, setFilteredReviews] = useState<IReview[]>([]);
  const [ratingFilter, setRatingFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedReview, setSelectedReview] = useState<IReview | null>(null);
  const [responseText, setResponseText] = useState('');

  useEffect(() => {
    // TODO: Replace with actual API call
    const fetchFeedback = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock reviews data
        const mockReviews: IReview[] = [
          {
            id: '1',
            userId: 'user1',
            userName: 'John Smith',
            rating: 5,
            comment:
              'This roadmap was incredibly helpful! The structure made it easy to follow along and I appreciated the practical challenges at the end of each section. Would highly recommend to anyone starting with frontend development.',
            date: '2023-06-15T14:30:00Z',
            isHelpful: 12,
            isNotHelpful: 1,
            adminResponse:
              "Thank you for your positive feedback, John! We're glad the roadmap helped you on your learning journey.",
          },
          {
            id: '2',
            userId: 'user2',
            userName: 'Emily Johnson',
            rating: 4,
            comment:
              'Great content and well-structured. I found the JavaScript section particularly useful. My only suggestion would be to add more resources for the React section.',
            date: '2023-06-12T09:45:00Z',
            isHelpful: 8,
            isNotHelpful: 0,
          },
          {
            id: '3',
            userId: 'user3',
            userName: 'Michael Chen',
            rating: 5,
            comment:
              'As someone who has been struggling to find a good learning path for frontend development, this roadmap was exactly what I needed. The progression from HTML to CSS to JavaScript to React made perfect sense.',
            date: '2023-06-10T16:20:00Z',
            isHelpful: 15,
            isNotHelpful: 2,
          },
          {
            id: '4',
            userId: 'user4',
            userName: 'Sarah Williams',
            rating: 3,
            comment:
              'The content is good but I found some of the lessons to be too basic. Would be nice to have more advanced material, especially in the CSS section.',
            date: '2023-06-08T11:15:00Z',
            isHelpful: 4,
            isNotHelpful: 1,
          },
          {
            id: '5',
            userId: 'user5',
            userName: 'David Rodriguez',
            rating: 2,
            comment:
              "I had higher expectations. Some of the content feels outdated, especially the React section which doesn't cover hooks in enough detail.",
            date: '2023-06-05T13:40:00Z',
            isHelpful: 3,
            isNotHelpful: 5,
            adminResponse:
              "Hi David, thank you for your feedback. We're actually in the process of updating our React content to include more material on hooks and modern practices. The updated content should be available within the next two weeks.",
          },
          {
            id: '6',
            userId: 'user6',
            userName: 'Jessica Lee',
            rating: 5,
            comment:
              'Excellent roadmap! The progression is logical and the challenges really helped reinforce the concepts. I particularly liked how the final project tied everything together.',
            date: '2023-06-03T10:10:00Z',
            isHelpful: 9,
            isNotHelpful: 0,
          },
          {
            id: '7',
            userId: 'user7',
            userName: 'Robert Taylor',
            rating: 4,
            comment:
              'Very good content overall. The videos were clear and concise. I would have given 5 stars if there were more real-world examples.',
            date: '2023-06-01T15:25:00Z',
            isHelpful: 6,
            isNotHelpful: 1,
          },
        ];

        setReviews(mockReviews);
        setFilteredReviews(mockReviews);
        setLoading(false);
      }, 500);
    };

    fetchFeedback();
  }, [roadmapId]);

  // Filter reviews based on rating and search query
  useEffect(() => {
    let filtered = [...reviews];

    // Filter by rating
    if (ratingFilter !== 'all') {
      const ratingValue = parseInt(ratingFilter, 10);
      filtered = filtered.filter((review) => review.rating === ratingValue);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (review) =>
          review.userName.toLowerCase().includes(query) ||
          review.comment.toLowerCase().includes(query),
      );
    }

    setFilteredReviews(filtered);
  }, [reviews, ratingFilter, searchQuery]);

  // Calculate average rating
  const averageRating =
    reviews.length > 0
      ? (
          reviews.reduce((sum, review) => sum + review.rating, 0) /
          reviews.length
        ).toFixed(1)
      : '0.0';

  // Count ratings by star
  const ratingCounts = [5, 4, 3, 2, 1].map((rating) => {
    return {
      rating,
      count: reviews.filter((review) => review.rating === rating).length,
      percentage:
        reviews.length > 0
          ? Math.round(
              (reviews.filter((review) => review.rating === rating).length /
                reviews.length) *
                100,
            )
          : 0,
    };
  });

  // Handle responding to a review
  const handleRespondToReview = (review: IReview) => {
    setSelectedReview(review);
    setResponseText(review.adminResponse || '');
  };

  const handleSaveResponse = () => {
    if (!selectedReview) return;

    // TODO: Implement API call to save response
    // For now, update the local state
    const updatedReviews = reviews.map((review) =>
      review.id === selectedReview.id
        ? { ...review, adminResponse: responseText }
        : review,
    );

    setReviews(updatedReviews);
    setFilteredReviews(
      updatedReviews.filter(
        (review) =>
          (ratingFilter === 'all' ||
            review.rating === parseInt(ratingFilter, 10)) &&
          (!searchQuery ||
            review.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            review.comment.toLowerCase().includes(searchQuery.toLowerCase())),
      ),
    );

    setSelectedReview(null);
    setResponseText('');
  };

  if (loading) {
    return (
      <div className="flex h-48 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-2 text-sm text-muted-foreground">
            Loading feedback data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold">User Feedback</h2>
        <p className="text-muted-foreground">
          Reviews and comments from users who have enrolled in this roadmap
        </p>
      </div>

      {/* Rating Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RiStarLine className="mr-2" /> Rating Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="flex flex-col items-center justify-center">
              <div className="text-5xl font-bold">{averageRating}</div>
              <div className="mt-2 flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <span key={star}>
                    {parseFloat(averageRating) >= star ? (
                      <RiStarFill className="h-5 w-5 text-amber-400" />
                    ) : (
                      <RiStarLine className="h-5 w-5 text-amber-400" />
                    )}
                  </span>
                ))}
              </div>
              <p className="mt-2 text-sm text-muted-foreground">
                {reviews.length} reviews
              </p>
            </div>

            <div className="md:col-span-2">
              <div className="space-y-2">
                {ratingCounts.map(({ rating, count, percentage }) => (
                  <div key={rating} className="flex items-center">
                    <div className="w-12 text-sm">{rating} stars</div>
                    <div className="mx-4 flex-1">
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div
                          className="h-2 rounded-full bg-amber-400"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="w-12 text-right text-sm">{count}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-grow">
          <Input
            placeholder="Search reviews..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          <RiMessage2Line className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
        </div>

        <div className="w-full md:w-48">
          <Select value={ratingFilter} onValueChange={setRatingFilter}>
            <SelectTrigger>
              <div className="flex items-center">
                <RiFilterLine className="mr-2" />
                <SelectValue placeholder="Filter by rating" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Ratings</SelectItem>
              <SelectItem value="5">5 Stars</SelectItem>
              <SelectItem value="4">4 Stars</SelectItem>
              <SelectItem value="3">3 Stars</SelectItem>
              <SelectItem value="2">2 Stars</SelectItem>
              <SelectItem value="1">1 Star</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.length > 0 ? (
          filteredReviews.map((review) => (
            <Card key={review.id} className="overflow-hidden">
              <div className="p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <span key={star}>
                            {review.rating >= star ? (
                              <RiStarFill className="h-5 w-5 text-amber-400" />
                            ) : (
                              <RiStarLine className="h-5 w-5 text-amber-400" />
                            )}
                          </span>
                        ))}
                      </div>
                      <Badge variant="outline">
                        {review.rating} Star{review.rating !== 1 ? 's' : ''}
                      </Badge>
                    </div>

                    <p className="mt-3">{review.comment}</p>

                    <div className="mt-4 flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <RiUserLine className="mr-1" />
                        {review.userName}
                      </div>
                      <div className="flex items-center">
                        <RiCalendarLine className="mr-1" />
                        {new Date(review.date).toLocaleDateString()}
                      </div>
                      <div className="flex items-center gap-3">
                        <span className="flex items-center">
                          <RiThumbUpLine className="mr-1" />
                          {review.isHelpful}
                        </span>
                        <span className="flex items-center">
                          <RiThumbDownLine className="mr-1" />
                          {review.isNotHelpful}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex-shrink-0">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1"
                      onClick={() => handleRespondToReview(review)}
                    >
                      <RiMailLine />{' '}
                      {review.adminResponse ? 'Edit Response' : 'Respond'}
                    </Button>
                  </div>
                </div>

                {/* Admin Response */}
                {review.adminResponse && (
                  <div className="mt-4 border-t border-border pt-4">
                    <div className="rounded-md bg-muted/50 p-4">
                      <div className="mb-2 flex items-center gap-2">
                        <Badge>Admin Response</Badge>
                      </div>
                      <p className="text-sm">{review.adminResponse}</p>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ))
        ) : (
          <div className="py-12 text-center">
            <RiMessage2Line className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">No reviews found</h3>
            <p className="mt-2 text-muted-foreground">
              {searchQuery || ratingFilter !== 'all'
                ? 'Try adjusting your filters to see more reviews.'
                : 'This roadmap has not received any reviews yet.'}
            </p>
          </div>
        )}
      </div>

      {/* Response Modal */}
      {selectedReview && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
          <div className="w-full max-w-2xl rounded-lg bg-card shadow-lg">
            <div className="p-6">
              <h3 className="mb-4 text-lg font-semibold">Respond to Review</h3>

              <div className="mb-4 rounded-md bg-muted/50 p-4">
                <div className="mb-2 flex items-center gap-2">
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <span key={star}>
                        {selectedReview.rating >= star ? (
                          <RiStarFill className="h-4 w-4 text-amber-400" />
                        ) : (
                          <RiStarLine className="h-4 w-4 text-amber-400" />
                        )}
                      </span>
                    ))}
                  </div>
                  <span className="text-sm">{selectedReview.userName}</span>
                </div>
                <p className="text-sm">{selectedReview.comment}</p>
              </div>

              <div className="mb-4">
                <label className="mb-1 block text-sm font-medium">
                  Your Response
                </label>
                <textarea
                  className="h-32 w-full rounded-md border p-3"
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                  placeholder="Type your response here..."
                ></textarea>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedReview(null);
                    setResponseText('');
                  }}
                >
                  Cancel
                </Button>
                <Button onClick={handleSaveResponse}>Save Response</Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RoadmapFeedback;
