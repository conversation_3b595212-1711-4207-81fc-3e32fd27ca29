/**
 * @file RoadmapStructure.tsx
 * @description Component to display the structure of a roadmap with sections and lessons
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiAddLine,
  RiFileListLine,
  RiArrowDownSLine,
  RiArrowRightSLine,
  RiDragMoveLine,
  RiLinkM,
  RiTimeLine,
  RiCheckboxCircleLine,
  RiFileTextLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Define interfaces for roadmap structure
interface ILesson {
  id: string;
  title: string;
  type: 'video' | 'article' | 'quiz' | 'challenge';
  duration: number; // in minutes
  completionCount: number;
  order: number;
  isRequired: boolean;
  resourceUrl?: string;
}

interface ISection {
  id: string;
  title: string;
  description: string;
  order: number;
  lessons: ILesson[];
}

interface IRoadmapStructureProps {
  roadmapId: string;
}

function RoadmapStructure({ roadmapId }: IRoadmapStructureProps) {
  const [loading, setLoading] = useState(true);
  const [sections, setSections] = useState<ISection[]>([]);
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});

  useEffect(() => {
    // TODO: Replace with actual API call
    const fetchRoadmapStructure = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock sections data
        const mockSections: ISection[] = [
          {
            id: '1',
            title: 'Getting Started with HTML',
            description:
              'Learn the basics of HTML, the backbone of web development',
            order: 1,
            lessons: [
              {
                id: '101',
                title: 'Introduction to HTML',
                type: 'video',
                duration: 15,
                completionCount: 1150,
                order: 1,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/intro-html',
              },
              {
                id: '102',
                title: 'HTML Document Structure',
                type: 'article',
                duration: 10,
                completionCount: 1050,
                order: 2,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/html-structure',
              },
              {
                id: '103',
                title: 'HTML Elements and Attributes',
                type: 'video',
                duration: 20,
                completionCount: 980,
                order: 3,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/html-elements',
              },
              {
                id: '104',
                title: 'HTML Quiz',
                type: 'quiz',
                duration: 15,
                completionCount: 920,
                order: 4,
                isRequired: true,
              },
            ],
          },
          {
            id: '2',
            title: 'CSS Fundamentals',
            description: 'Master the styling of web pages with CSS',
            order: 2,
            lessons: [
              {
                id: '201',
                title: 'Introduction to CSS',
                type: 'video',
                duration: 15,
                completionCount: 890,
                order: 1,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/intro-css',
              },
              {
                id: '202',
                title: 'CSS Selectors',
                type: 'article',
                duration: 12,
                completionCount: 850,
                order: 2,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/css-selectors',
              },
              {
                id: '203',
                title: 'CSS Box Model',
                type: 'video',
                duration: 18,
                completionCount: 820,
                order: 3,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/css-box-model',
              },
              {
                id: '204',
                title: 'CSS Layout Challenge',
                type: 'challenge',
                duration: 30,
                completionCount: 780,
                order: 4,
                isRequired: true,
              },
            ],
          },
          {
            id: '3',
            title: 'JavaScript Basics',
            description: 'Learn the fundamentals of JavaScript programming',
            order: 3,
            lessons: [
              {
                id: '301',
                title: 'Introduction to JavaScript',
                type: 'video',
                duration: 20,
                completionCount: 750,
                order: 1,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/intro-js',
              },
              {
                id: '302',
                title: 'Variables and Data Types',
                type: 'article',
                duration: 15,
                completionCount: 720,
                order: 2,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/js-variables',
              },
              {
                id: '303',
                title: 'Functions and Control Flow',
                type: 'video',
                duration: 25,
                completionCount: 690,
                order: 3,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/js-functions',
              },
              {
                id: '304',
                title: 'DOM Manipulation',
                type: 'video',
                duration: 22,
                completionCount: 670,
                order: 4,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/js-dom',
              },
              {
                id: '305',
                title: 'JavaScript Quiz',
                type: 'quiz',
                duration: 20,
                completionCount: 650,
                order: 5,
                isRequired: true,
              },
            ],
          },
          {
            id: '4',
            title: 'Introduction to React',
            description:
              'Get started with React, a popular JavaScript library for building user interfaces',
            order: 4,
            lessons: [
              {
                id: '401',
                title: 'React Fundamentals',
                type: 'video',
                duration: 25,
                completionCount: 620,
                order: 1,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/react-fundamentals',
              },
              {
                id: '402',
                title: 'Components and Props',
                type: 'article',
                duration: 18,
                completionCount: 590,
                order: 2,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/react-components',
              },
              {
                id: '403',
                title: 'State and Lifecycle',
                type: 'video',
                duration: 22,
                completionCount: 560,
                order: 3,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/react-state',
              },
              {
                id: '404',
                title: 'Building a React App',
                type: 'challenge',
                duration: 45,
                completionCount: 520,
                order: 4,
                isRequired: true,
              },
            ],
          },
        ];

        // Initialize expanded sections
        const expanded: Record<string, boolean> = {};
        mockSections.forEach((section) => {
          expanded[section.id] = true; // Default to expanded
        });

        setSections(mockSections);
        setExpandedSections(expanded);
        setLoading(false);
      }, 500);
    };

    fetchRoadmapStructure();
  }, [roadmapId]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };

  // Calculate total lessons and duration
  const totalLessons = sections.reduce(
    (total, section) => total + section.lessons.length,
    0,
  );
  const totalDuration = sections.reduce(
    (total, section) =>
      total + section.lessons.reduce((sum, lesson) => sum + lesson.duration, 0),
    0,
  );

  if (loading) {
    return (
      <div className="flex h-48 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-2 text-sm text-muted-foreground">
            Loading roadmap structure...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold">Roadmap Structure</h2>
          <p className="text-muted-foreground">
            Manage sections and lessons in this roadmap
          </p>
        </div>
        <Button className="flex items-center gap-1">
          <RiAddLine /> Add New Section
        </Button>
      </div>

      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RiFileListLine className="mr-2" /> Structure Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <h3 className="text-sm font-medium">Total Sections</h3>
              <p className="mt-1 text-2xl font-semibold">{sections.length}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Total Lessons</h3>
              <p className="mt-1 text-2xl font-semibold">{totalLessons}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Total Duration</h3>
              <p className="mt-1 text-2xl font-semibold">
                {Math.floor(totalDuration / 60)} hrs {totalDuration % 60} mins
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sections List */}
      <div className="space-y-4">
        {sections.map((section) => (
          <Card key={section.id} className="border-l-4 border-l-primary">
            <CardHeader
              className="cursor-pointer pb-2"
              onClick={() => toggleSection(section.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="bg-primary/10 flex h-6 w-6 items-center justify-center rounded-full text-primary">
                    {section.order}
                  </div>
                  <CardTitle className="flex items-center text-lg">
                    {section.title}
                    <Badge className="ml-2 text-xs" variant="outline">
                      {section.lessons.length} lessons
                    </Badge>
                  </CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <RiDragMoveLine className="h-4 w-4" />
                  </Button>
                  {expandedSections[section.id] ? (
                    <RiArrowDownSLine className="h-5 w-5" />
                  ) : (
                    <RiArrowRightSLine className="h-5 w-5" />
                  )}
                </div>
              </div>
              <p className="mt-1 text-sm text-muted-foreground">
                {section.description}
              </p>
            </CardHeader>

            {expandedSections[section.id] && (
              <CardContent>
                <div className="space-y-2">
                  {section.lessons.map((lesson) => (
                    <div
                      key={lesson.id}
                      className="flex flex-col justify-between rounded-md border bg-card p-3 hover:bg-accent/5 sm:flex-row sm:items-center"
                    >
                      <div className="flex items-center gap-2">
                        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-muted text-xs text-muted-foreground">
                          {lesson.order}
                        </div>
                        <div>
                          <div className="flex items-center gap-2 font-medium">
                            {lesson.title}
                            <Badge
                              variant="outline"
                              className={`text-xs ${
                                lesson.type === 'video'
                                  ? 'border-blue-200 bg-blue-50 text-blue-700'
                                  : lesson.type === 'article'
                                    ? 'border-green-200 bg-green-50 text-green-700'
                                    : lesson.type === 'quiz'
                                      ? 'border-purple-200 bg-purple-50 text-purple-700'
                                      : 'border-orange-200 bg-orange-50 text-orange-700'
                              }`}
                            >
                              {lesson.type}
                            </Badge>
                            {lesson.isRequired && (
                              <Badge
                                variant="outline"
                                className="bg-red-50 text-red-700 border-red-200 text-xs"
                              >
                                Required
                              </Badge>
                            )}
                          </div>
                          <div className="mt-1 flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center">
                              <RiTimeLine className="mr-1" />
                              {lesson.duration} mins
                            </div>
                            <div className="flex items-center">
                              <RiCheckboxCircleLine className="mr-1" />
                              {lesson.completionCount} completions
                            </div>
                            {lesson.resourceUrl && (
                              <div className="flex items-center">
                                <RiLinkM className="mr-1" />
                                <a
                                  href={lesson.resourceUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary hover:underline"
                                >
                                  Resource
                                </a>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="mt-2 flex items-center gap-2 sm:mt-0">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <RiDragMoveLine className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </div>
                    </div>
                  ))}

                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 flex w-full items-center justify-center gap-1 border-dashed"
                  >
                    <RiAddLine /> Add Lesson
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline">Preview Roadmap</Button>
        <Button>Save Changes</Button>
      </div>
    </div>
  );
}

export default RoadmapStructure;
