/**
 * @file RoadmapAnalytics.tsx
 * @description Component to display analytics for a roadmap
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiBarChartLine,
  RiLineChartLine,
  RiPieChartLine,
  RiUserLine,
  RiTimeLine,
  RiCalendarLine,
  RiMapPinLine,
  RiGlobalLine,
  RiCheckboxCircleLine,
  RiCloseCircleLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Define interfaces for analytics data
interface IEnrollmentData {
  date: string;
  count: number;
}

interface ICompletionData {
  date: string;
  count: number;
}

interface IDemographicData {
  region: string;
  count: number;
  percentage: number;
}

interface ILessonCompletionData {
  lessonId: string;
  lessonTitle: string;
  completionCount: number;
  dropoffCount: number;
  completionRate: number;
}

interface IRoadmapAnalyticsProps {
  roadmapId: string;
}

function RoadmapAnalytics({ roadmapId }: IRoadmapAnalyticsProps) {
  const [loading, setLoading] = useState(true);
  const [enrollmentData, setEnrollmentData] = useState<IEnrollmentData[]>([]);
  const [completionData, setCompletionData] = useState<ICompletionData[]>([]);
  const [demographicData, setDemographicData] = useState<IDemographicData[]>(
    [],
  );
  const [lessonCompletionData, setLessonCompletionData] = useState<
    ILessonCompletionData[]
  >([]);

  useEffect(() => {
    // TODO: Replace with actual API call
    const fetchAnalyticsData = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock enrollment data (last 7 days)
        const mockEnrollmentData: IEnrollmentData[] = [
          { date: '2023-06-10', count: 45 },
          { date: '2023-06-11', count: 52 },
          { date: '2023-06-12', count: 38 },
          { date: '2023-06-13', count: 65 },
          { date: '2023-06-14', count: 48 },
          { date: '2023-06-15', count: 57 },
          { date: '2023-06-16', count: 62 },
        ];

        // Mock completion data (last 7 days)
        const mockCompletionData: ICompletionData[] = [
          { date: '2023-06-10', count: 12 },
          { date: '2023-06-11', count: 18 },
          { date: '2023-06-12', count: 15 },
          { date: '2023-06-13', count: 22 },
          { date: '2023-06-14', count: 19 },
          { date: '2023-06-15', count: 24 },
          { date: '2023-06-16', count: 28 },
        ];

        // Mock demographic data
        const mockDemographicData: IDemographicData[] = [
          { region: 'North America', count: 520, percentage: 41.8 },
          { region: 'Europe', count: 310, percentage: 24.9 },
          { region: 'Asia', count: 240, percentage: 19.3 },
          { region: 'South America', count: 85, percentage: 6.8 },
          { region: 'Africa', count: 55, percentage: 4.4 },
          { region: 'Oceania', count: 35, percentage: 2.8 },
        ];

        // Mock lesson completion data
        const mockLessonCompletionData: ILessonCompletionData[] = [
          {
            lessonId: '101',
            lessonTitle: 'Introduction to HTML',
            completionCount: 1150,
            dropoffCount: 95,
            completionRate: 92.4,
          },
          {
            lessonId: '102',
            lessonTitle: 'HTML Document Structure',
            completionCount: 1050,
            dropoffCount: 100,
            completionRate: 91.3,
          },
          {
            lessonId: '103',
            lessonTitle: 'HTML Elements and Attributes',
            completionCount: 980,
            dropoffCount: 70,
            completionRate: 93.3,
          },
          {
            lessonId: '104',
            lessonTitle: 'HTML Quiz',
            completionCount: 920,
            dropoffCount: 60,
            completionRate: 93.9,
          },
          {
            lessonId: '201',
            lessonTitle: 'Introduction to CSS',
            completionCount: 890,
            dropoffCount: 30,
            completionRate: 96.7,
          },
          {
            lessonId: '202',
            lessonTitle: 'CSS Selectors',
            completionCount: 850,
            dropoffCount: 40,
            completionRate: 95.5,
          },
          {
            lessonId: '203',
            lessonTitle: 'CSS Box Model',
            completionCount: 820,
            dropoffCount: 30,
            completionRate: 96.5,
          },
          {
            lessonId: '204',
            lessonTitle: 'CSS Layout Challenge',
            completionCount: 780,
            dropoffCount: 40,
            completionRate: 95.1,
          },
          {
            lessonId: '301',
            lessonTitle: 'Introduction to JavaScript',
            completionCount: 750,
            dropoffCount: 30,
            completionRate: 96.2,
          },
          {
            lessonId: '302',
            lessonTitle: 'Variables and Data Types',
            completionCount: 720,
            dropoffCount: 30,
            completionRate: 96.0,
          },
        ];

        setEnrollmentData(mockEnrollmentData);
        setCompletionData(mockCompletionData);
        setDemographicData(mockDemographicData);
        setLessonCompletionData(mockLessonCompletionData);
        setLoading(false);
      }, 500);
    };

    fetchAnalyticsData();
  }, [roadmapId]);

  // Calculate total enrollments and completions
  const totalEnrollments = enrollmentData.reduce(
    (sum, item) => sum + item.count,
    0,
  );
  const totalCompletions = completionData.reduce(
    (sum, item) => sum + item.count,
    0,
  );

  if (loading) {
    return (
      <div className="flex h-48 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-2 text-sm text-muted-foreground">
            Loading analytics data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold">Roadmap Analytics</h2>
        <p className="text-muted-foreground">
          Performance metrics and user engagement data
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-sm font-medium">
              <RiUserLine className="mr-2 text-muted-foreground" /> Recent
              Enrollments
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEnrollments}</div>
            <p className="text-xs text-muted-foreground">
              New enrollments in the last 7 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-sm font-medium">
              <RiCheckboxCircleLine className="mr-2 text-muted-foreground" />{' '}
              Recent Completions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCompletions}</div>
            <p className="text-xs text-muted-foreground">
              Completions in the last 7 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-sm font-medium">
              <RiTimeLine className="mr-2 text-muted-foreground" /> Avg.
              Completion Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18.5 days</div>
            <p className="text-xs text-muted-foreground">
              Average time to complete the roadmap
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-sm font-medium">
              <RiCloseCircleLine className="mr-2 text-muted-foreground" />{' '}
              Dropout Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24.6%</div>
            <p className="text-xs text-muted-foreground">
              Users who enrolled but didn&apos;t complete
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="engagement" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="engagement" className="flex items-center gap-1">
            <RiLineChartLine className="h-4 w-4" /> Engagement
          </TabsTrigger>
          <TabsTrigger value="demographics" className="flex items-center gap-1">
            <RiGlobalLine className="h-4 w-4" /> Demographics
          </TabsTrigger>
          <TabsTrigger value="lessons" className="flex items-center gap-1">
            <RiBarChartLine className="h-4 w-4" /> Lesson Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="engagement" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <RiLineChartLine className="mr-2" /> Enrollment & Completion
                Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex h-80 w-full items-center justify-center rounded-md bg-muted/30">
                {/* TODO: Implement actual chart */}
                <div className="text-center">
                  <RiLineChartLine className="mx-auto h-12 w-12 text-muted-foreground" />
                  <p className="mt-2 text-muted-foreground">
                    Enrollment & Completion Chart
                  </p>
                  <p className="text-xs text-muted-foreground">
                    This would be an actual chart in production
                  </p>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <h3 className="mb-2 text-sm font-medium">
                    Daily Enrollments
                  </h3>
                  <div className="space-y-2">
                    {enrollmentData.map((item) => (
                      <div
                        key={item.date}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center">
                          <RiCalendarLine className="mr-2 text-muted-foreground" />
                          <span>
                            {new Date(item.date).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                            })}
                          </span>
                        </div>
                        <span className="font-medium">{item.count}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="mb-2 text-sm font-medium">
                    Daily Completions
                  </h3>
                  <div className="space-y-2">
                    {completionData.map((item) => (
                      <div
                        key={item.date}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center">
                          <RiCalendarLine className="mr-2 text-muted-foreground" />
                          <span>
                            {new Date(item.date).toLocaleDateString('en-US', {
                              month: 'short',
                              day: 'numeric',
                            })}
                          </span>
                        </div>
                        <span className="font-medium">{item.count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="demographics" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <RiPieChartLine className="mr-2" /> User Demographics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="flex h-64 w-full items-center justify-center rounded-md bg-muted/30">
                  {/* TODO: Implement actual chart */}
                  <div className="text-center">
                    <RiPieChartLine className="mx-auto h-12 w-12 text-muted-foreground" />
                    <p className="mt-2 text-muted-foreground">
                      Geographic Distribution Chart
                    </p>
                    <p className="text-xs text-muted-foreground">
                      This would be an actual chart in production
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="mb-2 text-sm font-medium">
                    Regional Distribution
                  </h3>
                  <div className="space-y-2">
                    {demographicData.map((item) => (
                      <div
                        key={item.region}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center">
                          <RiMapPinLine className="mr-2 text-muted-foreground" />
                          <span>{item.region}</span>
                        </div>
                        <div>
                          <span className="font-medium">{item.count}</span>
                          <span className="ml-2 text-muted-foreground">
                            ({item.percentage}%)
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="lessons" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <RiBarChartLine className="mr-2" /> Lesson Completion Rates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-3 text-left font-medium">
                        Lesson
                      </th>
                      <th className="px-4 py-3 text-center font-medium">
                        Completions
                      </th>
                      <th className="px-4 py-3 text-center font-medium">
                        Dropoffs
                      </th>
                      <th className="px-4 py-3 text-center font-medium">
                        Completion Rate
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {lessonCompletionData.map((lesson) => (
                      <tr
                        key={lesson.lessonId}
                        className="border-b hover:bg-muted/50"
                      >
                        <td className="px-4 py-3">{lesson.lessonTitle}</td>
                        <td className="px-4 py-3 text-center">
                          {lesson.completionCount}
                        </td>
                        <td className="px-4 py-3 text-center">
                          {lesson.dropoffCount}
                        </td>
                        <td className="px-4 py-3 text-center">
                          <div className="flex items-center justify-center">
                            <div className="mr-2 h-2 w-full max-w-[100px] rounded-full bg-muted">
                              <div
                                className="h-2 rounded-full bg-primary"
                                style={{ width: `${lesson.completionRate}%` }}
                              ></div>
                            </div>
                            <span>{lesson.completionRate}%</span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default RoadmapAnalytics;
