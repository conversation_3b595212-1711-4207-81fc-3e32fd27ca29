/**
 * @file RoadmapBasicInfo.tsx
 * @description Component for editing basic roadmap information
 */
'use client';

import { useState } from 'react';
import { RiInformationLine, RiPriceTag3Line } from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

// Define interfaces
interface IRoadmap {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  status: 'Published' | 'Draft' | 'Archived';
  isFeatured: boolean;
  estimatedHours: number;
  prerequisites: string[];
  relatedRoadmaps: string[];
  tags: string[];
  authorId: string;
  authorName: string;
}

interface IRoadmapBasicInfoProps {
  roadmap: IRoadmap;
  onUpdate: (data: Partial<IRoadmap>) => void;
}

function RoadmapBasicInfo({ roadmap, onUpdate }: IRoadmapBasicInfoProps) {
  const [title, setTitle] = useState(roadmap.title);
  const [description, setDescription] = useState(roadmap.description);
  const [category, setCategory] = useState(roadmap.category);
  const [difficulty, setDifficulty] = useState(roadmap.difficulty);
  const [status, setStatus] = useState(roadmap.status);
  const [estimatedHours, setEstimatedHours] = useState(
    roadmap.estimatedHours.toString(),
  );
  const [tagInput, setTagInput] = useState('');
  const [tags, setTags] = useState(roadmap.tags);

  // Available categories
  const categories = [
    'Web Development',
    'Mobile Development',
    'DevOps',
    'Data Science',
    'Machine Learning',
    'Design',
    'Game Development',
    'Blockchain',
    'Cloud Computing',
    'Cybersecurity',
  ];

  // Handle tag input
  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim().toLowerCase())) {
      const newTags = [...tags, tagInput.trim().toLowerCase()];
      setTags(newTags);
      onUpdate({ tags: newTags });
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(newTags);
    onUpdate({ tags: newTags });
  };

  // Handle input changes
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
    onUpdate({ title: e.target.value });
  };

  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    setDescription(e.target.value);
    onUpdate({ description: e.target.value });
  };

  const handleCategoryChange = (value: string) => {
    setCategory(value);
    onUpdate({ category: value });
  };

  const handleDifficultyChange = (
    value: 'Beginner' | 'Intermediate' | 'Advanced',
  ) => {
    setDifficulty(value);
    onUpdate({ difficulty: value });
  };

  const handleStatusChange = (value: 'Published' | 'Draft' | 'Archived') => {
    setStatus(value);
    onUpdate({ status: value });
  };

  const handleEstimatedHoursChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setEstimatedHours(e.target.value);
    const hours = parseInt(e.target.value, 10);
    if (!isNaN(hours) && hours > 0) {
      onUpdate({ estimatedHours: hours });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RiInformationLine className="mr-2" /> Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Roadmap Title</Label>
              <Input
                id="title"
                value={title}
                onChange={handleTitleChange}
                placeholder="Enter roadmap title"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={handleDescriptionChange}
                placeholder="Enter roadmap description"
                className="mt-1 min-h-[120px]"
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={category} onValueChange={handleCategoryChange}>
                  <SelectTrigger id="category" className="mt-1">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((cat) => (
                      <SelectItem key={cat} value={cat}>
                        {cat}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="difficulty">Difficulty Level</Label>
                <Select
                  value={difficulty}
                  onValueChange={handleDifficultyChange}
                >
                  <SelectTrigger id="difficulty" className="mt-1">
                    <SelectValue placeholder="Select difficulty" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Beginner">Beginner</SelectItem>
                    <SelectItem value="Intermediate">Intermediate</SelectItem>
                    <SelectItem value="Advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={status} onValueChange={handleStatusChange}>
                  <SelectTrigger id="status" className="mt-1">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Published">Published</SelectItem>
                    <SelectItem value="Draft">Draft</SelectItem>
                    <SelectItem value="Archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="estimatedHours">
                  Estimated Hours to Complete
                </Label>
                <Input
                  id="estimatedHours"
                  type="number"
                  value={estimatedHours}
                  onChange={handleEstimatedHoursChange}
                  min="1"
                  className="mt-1"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RiPriceTag3Line className="mr-2" /> Tags
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  {tag}
                  <button
                    type="button"
                    className="ml-1 flex h-4 w-4 items-center justify-center rounded-full hover:bg-muted"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    ×
                  </button>
                </Badge>
              ))}
              {tags.length === 0 && (
                <span className="text-sm text-muted-foreground">
                  No tags added yet
                </span>
              )}
            </div>

            <div className="flex gap-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                placeholder="Add a tag"
                onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
              />
              <button
                type="button"
                className="rounded-md bg-primary px-4 py-2 text-primary-foreground"
                onClick={handleAddTag}
              >
                Add
              </button>
            </div>

            <p className="text-xs text-muted-foreground">
              Tags help users find your roadmap. Add relevant keywords that
              describe your content.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default RoadmapBasicInfo;
