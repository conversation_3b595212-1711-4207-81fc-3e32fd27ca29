/**
 * @file RoadmapSettings.tsx
 * @description Component for editing advanced roadmap settings
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiSettings4Line,
  RiLinksLine,
  RiAddLine,
  RiCloseLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';

// Define interfaces
interface IRoadmap {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  status: 'Published' | 'Draft' | 'Archived';
  isFeatured: boolean;
  estimatedHours: number;
  prerequisites: string[];
  relatedRoadmaps: string[];
  tags: string[];
  authorId: string;
  authorName: string;
}

interface IRoadmapOption {
  id: string;
  title: string;
}

interface IRoadmapSettingsProps {
  roadmap: IRoadmap;
  onUpdate: (data: Partial<IRoadmap>) => void;
}

function RoadmapSettings({ roadmap, onUpdate }: IRoadmapSettingsProps) {
  const [isFeatured, setIsFeatured] = useState(roadmap.isFeatured);
  const [prerequisites, setPrerequisites] = useState(roadmap.prerequisites);
  const [relatedRoadmaps, setRelatedRoadmaps] = useState(
    roadmap.relatedRoadmaps,
  );
  const [selectedPrerequisite, setSelectedPrerequisite] = useState('');
  const [selectedRelatedRoadmap, setSelectedRelatedRoadmap] = useState('');
  const [availableRoadmaps, setAvailableRoadmaps] = useState<IRoadmapOption[]>(
    [],
  );

  useEffect(() => {
    // TODO: Replace with actual API call to fetch available roadmaps
    // Simulate API call
    setTimeout(() => {
      // Mock roadmap options
      const mockRoadmaps: IRoadmapOption[] = [
        { id: '2', title: 'Backend Development with Node.js' },
        { id: '3', title: 'DevOps Engineering' },
        { id: '4', title: 'Mobile App Development with React Native' },
        { id: '5', title: 'Machine Learning Fundamentals' },
        { id: '6', title: 'UI/UX Design Principles' },
      ];

      setAvailableRoadmaps(mockRoadmaps);
    }, 500);
  }, []);

  // Handle featured toggle
  const handleFeaturedToggle = (checked: boolean) => {
    setIsFeatured(checked);
    onUpdate({ isFeatured: checked });
  };

  // Handle prerequisites
  const handleAddPrerequisite = () => {
    if (selectedPrerequisite && !prerequisites.includes(selectedPrerequisite)) {
      const updatedPrerequisites = [...prerequisites, selectedPrerequisite];
      setPrerequisites(updatedPrerequisites);
      onUpdate({ prerequisites: updatedPrerequisites });
      setSelectedPrerequisite('');
    }
  };

  const handleRemovePrerequisite = (prerequisiteId: string) => {
    const updatedPrerequisites = prerequisites.filter(
      (id) => id !== prerequisiteId,
    );
    setPrerequisites(updatedPrerequisites);
    onUpdate({ prerequisites: updatedPrerequisites });
  };

  // Handle related roadmaps
  const handleAddRelatedRoadmap = () => {
    if (
      selectedRelatedRoadmap &&
      !relatedRoadmaps.includes(selectedRelatedRoadmap)
    ) {
      const updatedRelatedRoadmaps = [
        ...relatedRoadmaps,
        selectedRelatedRoadmap,
      ];
      setRelatedRoadmaps(updatedRelatedRoadmaps);
      onUpdate({ relatedRoadmaps: updatedRelatedRoadmaps });
      setSelectedRelatedRoadmap('');
    }
  };

  const handleRemoveRelatedRoadmap = (roadmapId: string) => {
    const updatedRelatedRoadmaps = relatedRoadmaps.filter(
      (id) => id !== roadmapId,
    );
    setRelatedRoadmaps(updatedRelatedRoadmaps);
    onUpdate({ relatedRoadmaps: updatedRelatedRoadmaps });
  };

  // Get roadmap title by ID
  const getRoadmapTitle = (roadmapId: string) => {
    const roadmap = availableRoadmaps.find((r) => r.id === roadmapId);
    return roadmap ? roadmap.title : 'Unknown Roadmap';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RiSettings4Line className="mr-2" /> Advanced Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={isFeatured}
              onCheckedChange={handleFeaturedToggle}
            />
            <Label htmlFor="featured">Feature this roadmap on homepage</Label>
          </div>

          <div className="border-t border-border pt-4">
            <h3 className="mb-2 text-base font-medium">Author Information</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="authorName">Author Name</Label>
                <Input
                  id="authorName"
                  value={roadmap.authorName}
                  disabled
                  className="mt-1 bg-muted"
                />
              </div>
              <div>
                <Label htmlFor="authorId">Author ID</Label>
                <Input
                  id="authorId"
                  value={roadmap.authorId}
                  disabled
                  className="mt-1 bg-muted"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RiLinksLine className="mr-2" /> Prerequisites & Related Roadmaps
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="mb-2 text-base font-medium">Prerequisites</h3>
            <p className="mb-4 text-sm text-muted-foreground">
              Select roadmaps that users should complete before starting this
              one.
            </p>

            <div className="mb-4 flex flex-wrap gap-2">
              {prerequisites.length > 0 ? (
                prerequisites.map((prerequisiteId) => (
                  <Badge
                    key={prerequisiteId}
                    variant="outline"
                    className="flex items-center gap-1"
                  >
                    {getRoadmapTitle(prerequisiteId)}
                    <button
                      type="button"
                      className="ml-1 flex h-4 w-4 items-center justify-center rounded-full hover:bg-muted"
                      onClick={() => handleRemovePrerequisite(prerequisiteId)}
                    >
                      <RiCloseLine className="h-3 w-3" />
                    </button>
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">
                  No prerequisites added
                </span>
              )}
            </div>

            <div className="flex gap-2">
              <Select
                value={selectedPrerequisite}
                onValueChange={setSelectedPrerequisite}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select a prerequisite roadmap" />
                </SelectTrigger>
                <SelectContent>
                  {availableRoadmaps
                    .filter(
                      (r) =>
                        r.id !== roadmap.id && !prerequisites.includes(r.id),
                    )
                    .map((r) => (
                      <SelectItem key={r.id} value={r.id}>
                        {r.title}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <Button
                onClick={handleAddPrerequisite}
                disabled={!selectedPrerequisite}
                className="flex items-center gap-1"
              >
                <RiAddLine /> Add
              </Button>
            </div>
          </div>

          <div className="border-t border-border pt-4">
            <h3 className="mb-2 text-base font-medium">Related Roadmaps</h3>
            <p className="mb-4 text-sm text-muted-foreground">
              Select roadmaps that are related to this one and might interest
              users.
            </p>

            <div className="mb-4 flex flex-wrap gap-2">
              {relatedRoadmaps.length > 0 ? (
                relatedRoadmaps.map((relatedId) => (
                  <Badge
                    key={relatedId}
                    variant="outline"
                    className="flex items-center gap-1"
                  >
                    {getRoadmapTitle(relatedId)}
                    <button
                      type="button"
                      className="ml-1 flex h-4 w-4 items-center justify-center rounded-full hover:bg-muted"
                      onClick={() => handleRemoveRelatedRoadmap(relatedId)}
                    >
                      <RiCloseLine className="h-3 w-3" />
                    </button>
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">
                  No related roadmaps added
                </span>
              )}
            </div>

            <div className="flex gap-2">
              <Select
                value={selectedRelatedRoadmap}
                onValueChange={setSelectedRelatedRoadmap}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select a related roadmap" />
                </SelectTrigger>
                <SelectContent>
                  {availableRoadmaps
                    .filter(
                      (r) =>
                        r.id !== roadmap.id && !relatedRoadmaps.includes(r.id),
                    )
                    .map((r) => (
                      <SelectItem key={r.id} value={r.id}>
                        {r.title}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <Button
                onClick={handleAddRelatedRoadmap}
                disabled={!selectedRelatedRoadmap}
                className="flex items-center gap-1"
              >
                <RiAddLine /> Add
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Visibility & Access Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch id="publicAccess" defaultChecked />
              <div>
                <Label htmlFor="publicAccess">Public Access</Label>
                <p className="text-sm text-muted-foreground">
                  Make this roadmap visible to all users
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="requireEnrollment" defaultChecked />
              <div>
                <Label htmlFor="requireEnrollment">Require Enrollment</Label>
                <p className="text-sm text-muted-foreground">
                  Users must enroll before accessing content
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="trackProgress" defaultChecked />
              <div>
                <Label htmlFor="trackProgress">Track Progress</Label>
                <p className="text-sm text-muted-foreground">
                  Track and display user progress through the roadmap
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default RoadmapSettings;
