/**
 * @file RoadmapStructureEditor.tsx
 * @description Component for editing roadmap structure (sections and lessons)
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiAddLine,
  RiDeleteBinLine,
  RiEditLine,
  RiDragMoveLine,
  RiArrowDownSLine,
  RiArrowRightSLine,
  RiSaveLine,
  RiCloseLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';

// Define interfaces for roadmap structure
interface ILesson {
  id: string;
  title: string;
  type: 'video' | 'article' | 'quiz' | 'challenge';
  duration: number; // in minutes
  order: number;
  isRequired: boolean;
  resourceUrl?: string;
  description?: string;
}

interface ISection {
  id: string;
  title: string;
  description: string;
  order: number;
  lessons: ILesson[];
}

interface IRoadmapStructureEditorProps {
  roadmapId: string;
}

function RoadmapStructureEditor({ roadmapId }: IRoadmapStructureEditorProps) {
  const [loading, setLoading] = useState(true);
  const [sections, setSections] = useState<ISection[]>([]);
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});

  // Edit states
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const [editingLessonId, setEditingLessonId] = useState<string | null>(null);
  const [sectionFormData, setSectionFormData] = useState<Partial<ISection>>({});
  const [lessonFormData, setLessonFormData] = useState<Partial<ILesson>>({});

  // Add new states
  const [isAddingSectionOpen, setIsAddingSectionOpen] = useState(false);
  const [isAddingLessonOpen, setIsAddingLessonOpen] = useState<string | null>(
    null,
  ); // sectionId or null

  useEffect(() => {
    // TODO: Replace with actual API call
    const fetchRoadmapStructure = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock sections data
        const mockSections: ISection[] = [
          {
            id: '1',
            title: 'Getting Started with HTML',
            description:
              'Learn the basics of HTML, the backbone of web development',
            order: 1,
            lessons: [
              {
                id: '101',
                title: 'Introduction to HTML',
                type: 'video',
                duration: 15,
                order: 1,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/intro-html',
                description:
                  'An introduction to HTML and its role in web development',
              },
              {
                id: '102',
                title: 'HTML Document Structure',
                type: 'article',
                duration: 10,
                order: 2,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/html-structure',
                description:
                  'Understanding the basic structure of HTML documents',
              },
              {
                id: '103',
                title: 'HTML Elements and Attributes',
                type: 'video',
                duration: 20,
                order: 3,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/html-elements',
                description:
                  'Learn about HTML elements and how to use attributes',
              },
              {
                id: '104',
                title: 'HTML Quiz',
                type: 'quiz',
                duration: 15,
                order: 4,
                isRequired: true,
                description: 'Test your knowledge of HTML basics',
              },
            ],
          },
          {
            id: '2',
            title: 'CSS Fundamentals',
            description: 'Master the styling of web pages with CSS',
            order: 2,
            lessons: [
              {
                id: '201',
                title: 'Introduction to CSS',
                type: 'video',
                duration: 15,
                order: 1,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/intro-css',
                description: 'An introduction to CSS and how it styles HTML',
              },
              {
                id: '202',
                title: 'CSS Selectors',
                type: 'article',
                duration: 12,
                order: 2,
                isRequired: true,
                resourceUrl: 'https://example.com/lessons/css-selectors',
                description: 'Learn about different types of CSS selectors',
              },
            ],
          },
        ];

        // Initialize expanded sections
        const expanded: Record<string, boolean> = {};
        mockSections.forEach((section) => {
          expanded[section.id] = true; // Default to expanded
        });

        setSections(mockSections);
        setExpandedSections(expanded);
        setLoading(false);
      }, 500);
    };

    fetchRoadmapStructure();
  }, [roadmapId]);

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };

  // Section CRUD operations
  const handleAddSection = () => {
    setSectionFormData({
      title: '',
      description: '',
      order: sections.length + 1,
    });
    setIsAddingSectionOpen(true);
  };

  const handleEditSection = (section: ISection) => {
    setSectionFormData({
      id: section.id,
      title: section.title,
      description: section.description,
      order: section.order,
    });
    setEditingSectionId(section.id);
  };

  const handleSaveSection = () => {
    if (!sectionFormData.title) return;

    if (editingSectionId) {
      // Update existing section
      setSections((prev) =>
        prev.map((section) =>
          section.id === editingSectionId
            ? { ...section, ...(sectionFormData as ISection) }
            : section,
        ),
      );
      setEditingSectionId(null);
    } else if (isAddingSectionOpen) {
      // Add new section
      const newSection: ISection = {
        id: `new-${Date.now()}`,
        title: sectionFormData.title || '',
        description: sectionFormData.description || '',
        order: sectionFormData.order || sections.length + 1,
        lessons: [],
      };

      setSections((prev) => [...prev, newSection]);
      setExpandedSections((prev) => ({ ...prev, [newSection.id]: true }));
      setIsAddingSectionOpen(false);
    }

    setSectionFormData({});
  };

  const handleDeleteSection = (sectionId: string) => {
    if (
      confirm(
        'Are you sure you want to delete this section? All lessons within it will also be deleted.',
      )
    ) {
      setSections((prev) => prev.filter((section) => section.id !== sectionId));
    }
  };

  // Lesson CRUD operations
  const handleAddLesson = (sectionId: string) => {
    setLessonFormData({
      title: '',
      type: 'video',
      duration: 15,
      order: sections.find((s) => s.id === sectionId)?.lessons.length || 0 + 1,
      isRequired: true,
      description: '',
    });
    setIsAddingLessonOpen(sectionId);
  };

  const handleEditLesson = (sectionId: string, lesson: ILesson) => {
    setLessonFormData({
      id: lesson.id,
      title: lesson.title,
      type: lesson.type,
      duration: lesson.duration,
      order: lesson.order,
      isRequired: lesson.isRequired,
      resourceUrl: lesson.resourceUrl,
      description: lesson.description,
    });
    setEditingLessonId(lesson.id);
    setIsAddingLessonOpen(sectionId);
  };

  const handleSaveLesson = () => {
    if (!lessonFormData.title || !isAddingLessonOpen) return;

    if (editingLessonId) {
      // Update existing lesson
      setSections((prev) =>
        prev.map((section) =>
          section.id === isAddingLessonOpen
            ? {
                ...section,
                lessons: section.lessons.map((lesson) =>
                  lesson.id === editingLessonId
                    ? { ...lesson, ...(lessonFormData as ILesson) }
                    : lesson,
                ),
              }
            : section,
        ),
      );
      setEditingLessonId(null);
    } else {
      // Add new lesson
      const newLesson: ILesson = {
        id: `new-${Date.now()}`,
        title: lessonFormData.title || '',
        type: lessonFormData.type as 'video' | 'article' | 'quiz' | 'challenge',
        duration: lessonFormData.duration || 15,
        order: lessonFormData.order || 1,
        isRequired:
          lessonFormData.isRequired !== undefined
            ? lessonFormData.isRequired
            : true,
        resourceUrl: lessonFormData.resourceUrl,
        description: lessonFormData.description,
      };

      setSections((prev) =>
        prev.map((section) =>
          section.id === isAddingLessonOpen
            ? { ...section, lessons: [...section.lessons, newLesson] }
            : section,
        ),
      );
    }

    setLessonFormData({});
    setIsAddingLessonOpen(null);
  };

  const handleDeleteLesson = (sectionId: string, lessonId: string) => {
    if (confirm('Are you sure you want to delete this lesson?')) {
      setSections((prev) =>
        prev.map((section) =>
          section.id === sectionId
            ? {
                ...section,
                lessons: section.lessons.filter(
                  (lesson) => lesson.id !== lessonId,
                ),
              }
            : section,
        ),
      );
    }
  };

  // Cancel editing/adding
  const handleCancelSectionEdit = () => {
    setEditingSectionId(null);
    setIsAddingSectionOpen(false);
    setSectionFormData({});
  };

  const handleCancelLessonEdit = () => {
    setEditingLessonId(null);
    setIsAddingLessonOpen(null);
    setLessonFormData({});
  };

  if (loading) {
    return (
      <div className="flex h-48 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-2 text-sm text-muted-foreground">
            Loading roadmap structure...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold">Roadmap Structure</h2>
          <p className="text-muted-foreground">
            Organize your roadmap into sections and lessons
          </p>
        </div>
        <Button
          className="flex items-center gap-1"
          onClick={handleAddSection}
          disabled={isAddingSectionOpen || editingSectionId !== null}
        >
          <RiAddLine /> Add New Section
        </Button>
      </div>

      {/* Section Form */}
      {(isAddingSectionOpen || editingSectionId) && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingSectionId ? 'Edit Section' : 'Add New Section'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="sectionTitle">Section Title</Label>
                <Input
                  id="sectionTitle"
                  value={sectionFormData.title || ''}
                  onChange={(e) =>
                    setSectionFormData({
                      ...sectionFormData,
                      title: e.target.value,
                    })
                  }
                  placeholder="Enter section title"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="sectionDescription">Description</Label>
                <Textarea
                  id="sectionDescription"
                  value={sectionFormData.description || ''}
                  onChange={(e) =>
                    setSectionFormData({
                      ...sectionFormData,
                      description: e.target.value,
                    })
                  }
                  placeholder="Enter section description"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="sectionOrder">Order</Label>
                <Input
                  id="sectionOrder"
                  type="number"
                  value={sectionFormData.order || ''}
                  onChange={(e) =>
                    setSectionFormData({
                      ...sectionFormData,
                      order: parseInt(e.target.value, 10),
                    })
                  }
                  min="1"
                  className="mt-1"
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={handleCancelSectionEdit}>
                  Cancel
                </Button>
                <Button onClick={handleSaveSection}>
                  {editingSectionId ? 'Update Section' : 'Add Section'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sections List */}
      <div className="space-y-4">
        {sections.length > 0 ? (
          sections.map((section) => (
            <Card key={section.id} className="border-l-4 border-l-primary">
              <CardHeader
                className="cursor-pointer pb-2"
                onClick={() => toggleSection(section.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="bg-primary/10 flex h-6 w-6 items-center justify-center rounded-full text-primary">
                      {section.order}
                    </div>
                    <CardTitle className="text-lg">{section.title}</CardTitle>
                    <Badge className="ml-2 text-xs" variant="outline">
                      {section.lessons.length} lessons
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditSection(section);
                      }}
                    >
                      <RiEditLine className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteSection(section.id);
                      }}
                    >
                      <RiDeleteBinLine className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <RiDragMoveLine className="h-4 w-4" />
                    </Button>
                    {expandedSections[section.id] ? (
                      <RiArrowDownSLine className="h-5 w-5" />
                    ) : (
                      <RiArrowRightSLine className="h-5 w-5" />
                    )}
                  </div>
                </div>
                <p className="mt-1 text-sm text-muted-foreground">
                  {section.description}
                </p>
              </CardHeader>

              {expandedSections[section.id] && (
                <CardContent>
                  {/* Lesson Form */}
                  {isAddingLessonOpen === section.id && (
                    <Card className="mb-4 border-dashed">
                      <CardHeader>
                        <CardTitle className="text-base">
                          {editingLessonId ? 'Edit Lesson' : 'Add New Lesson'}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="lessonTitle">Lesson Title</Label>
                            <Input
                              id="lessonTitle"
                              value={lessonFormData.title || ''}
                              onChange={(e) =>
                                setLessonFormData({
                                  ...lessonFormData,
                                  title: e.target.value,
                                })
                              }
                              placeholder="Enter lesson title"
                              className="mt-1"
                            />
                          </div>

                          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                              <Label htmlFor="lessonType">Lesson Type</Label>
                              <Select
                                value={lessonFormData.type}
                                onValueChange={(value) =>
                                  setLessonFormData({
                                    ...lessonFormData,
                                    type: value as any,
                                  })
                                }
                              >
                                <SelectTrigger id="lessonType" className="mt-1">
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="video">Video</SelectItem>
                                  <SelectItem value="article">
                                    Article
                                  </SelectItem>
                                  <SelectItem value="quiz">Quiz</SelectItem>
                                  <SelectItem value="challenge">
                                    Challenge
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div>
                              <Label htmlFor="lessonDuration">
                                Duration (minutes)
                              </Label>
                              <Input
                                id="lessonDuration"
                                type="number"
                                value={lessonFormData.duration || ''}
                                onChange={(e) =>
                                  setLessonFormData({
                                    ...lessonFormData,
                                    duration: parseInt(e.target.value, 10),
                                  })
                                }
                                min="1"
                                className="mt-1"
                              />
                            </div>

                            <div>
                              <Label htmlFor="lessonOrder">Order</Label>
                              <Input
                                id="lessonOrder"
                                type="number"
                                value={lessonFormData.order || ''}
                                onChange={(e) =>
                                  setLessonFormData({
                                    ...lessonFormData,
                                    order: parseInt(e.target.value, 10),
                                  })
                                }
                                min="1"
                                className="mt-1"
                              />
                            </div>

                            <div>
                              <Label htmlFor="lessonResourceUrl">
                                Resource URL (optional)
                              </Label>
                              <Input
                                id="lessonResourceUrl"
                                value={lessonFormData.resourceUrl || ''}
                                onChange={(e) =>
                                  setLessonFormData({
                                    ...lessonFormData,
                                    resourceUrl: e.target.value,
                                  })
                                }
                                placeholder="https://example.com/resource"
                                className="mt-1"
                              />
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="lessonDescription">
                              Description (optional)
                            </Label>
                            <Textarea
                              id="lessonDescription"
                              value={lessonFormData.description || ''}
                              onChange={(e) =>
                                setLessonFormData({
                                  ...lessonFormData,
                                  description: e.target.value,
                                })
                              }
                              placeholder="Enter lesson description"
                              className="mt-1"
                            />
                          </div>

                          <div className="flex items-center space-x-2">
                            <Switch
                              id="lessonRequired"
                              checked={lessonFormData.isRequired}
                              onCheckedChange={(checked) =>
                                setLessonFormData({
                                  ...lessonFormData,
                                  isRequired: checked,
                                })
                              }
                            />
                            <Label htmlFor="lessonRequired">
                              Required Lesson
                            </Label>
                          </div>

                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              onClick={handleCancelLessonEdit}
                            >
                              Cancel
                            </Button>
                            <Button onClick={handleSaveLesson}>
                              {editingLessonId ? 'Update Lesson' : 'Add Lesson'}
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <div className="space-y-2">
                    {section.lessons.map((lesson) => (
                      <div
                        key={lesson.id}
                        className="flex flex-col justify-between rounded-md border bg-card p-3 hover:bg-accent/5 sm:flex-row sm:items-center"
                      >
                        <div className="flex items-center gap-2">
                          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-muted text-xs text-muted-foreground">
                            {lesson.order}
                          </div>
                          <div>
                            <div className="flex items-center gap-2 font-medium">
                              {lesson.title}
                              <Badge
                                variant="outline"
                                className={`text-xs ${
                                  lesson.type === 'video'
                                    ? 'border-blue-200 bg-blue-50 text-blue-700'
                                    : lesson.type === 'article'
                                      ? 'border-green-200 bg-green-50 text-green-700'
                                      : lesson.type === 'quiz'
                                        ? 'border-purple-200 bg-purple-50 text-purple-700'
                                        : 'border-orange-200 bg-orange-50 text-orange-700'
                                }`}
                              >
                                {lesson.type}
                              </Badge>
                              {lesson.isRequired && (
                                <Badge
                                  variant="outline"
                                  className="bg-red-50 text-red-700 border-red-200 text-xs"
                                >
                                  Required
                                </Badge>
                              )}
                            </div>
                            {lesson.description && (
                              <p className="mt-1 text-xs text-muted-foreground">
                                {lesson.description}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="mt-2 flex items-center gap-2 sm:mt-0">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleEditLesson(section.id, lesson)}
                          >
                            <RiEditLine className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 h-8 w-8 p-0"
                            onClick={() =>
                              handleDeleteLesson(section.id, lesson.id)
                            }
                          >
                            <RiDeleteBinLine className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <RiDragMoveLine className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}

                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2 flex w-full items-center justify-center gap-1 border-dashed"
                      onClick={() => handleAddLesson(section.id)}
                      disabled={isAddingLessonOpen !== null}
                    >
                      <RiAddLine /> Add Lesson
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>
          ))
        ) : (
          <div className="rounded-md border py-12 text-center">
            <p className="text-muted-foreground">
              No sections added yet. Start by adding a section to your roadmap.
            </p>
            <Button className="mt-4" onClick={handleAddSection}>
              <RiAddLine className="mr-1" /> Add First Section
            </Button>
          </div>
        )}
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline">Preview Structure</Button>
        <Button className="flex items-center gap-1">
          <RiSaveLine /> Save Structure
        </Button>
      </div>
    </div>
  );
}

export default RoadmapStructureEditor;
