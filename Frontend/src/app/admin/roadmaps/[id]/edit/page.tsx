/**
 * @file page.tsx
 * @description Roadmap editor page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiSaveLine,
  RiFileListLine,
  RiRoadMapLine,
} from 'react-icons/ri';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';

// Import editor components
import RoadmapBasicInfo from './components/RoadmapBasicInfo';
import RoadmapStructureEditor from './components/RoadmapStructureEditor';
import RoadmapSettings from './components/RoadmapSettings';

// Define roadmap interface
interface IRoadmap {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  status: 'Published' | 'Draft' | 'Archived';
  isFeatured: boolean;
  estimatedHours: number;
  prerequisites: string[];
  relatedRoadmaps: string[];
  tags: string[];
  authorId: string;
  authorName: string;
}

function RoadmapEditorPage() {
  const router = useRouter();
  const params = useParams();
  const roadmapId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [roadmap, setRoadmap] = useState<IRoadmap | null>(null);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    // TODO: Replace with actual API call
    const fetchRoadmap = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock roadmap data
        const mockRoadmap: IRoadmap = {
          id: roadmapId,
          title: 'Frontend Web Development',
          description:
            'Learn modern frontend development with HTML, CSS, JavaScript, and React. This comprehensive roadmap covers everything from the basics to advanced concepts, helping you become a proficient frontend developer ready for the industry.',
          category: 'Web Development',
          difficulty: 'Beginner',
          status: 'Published',
          isFeatured: true,
          estimatedHours: 45,
          prerequisites: [],
          relatedRoadmaps: ['2', '3'],
          tags: ['html', 'css', 'javascript', 'react', 'frontend'],
          authorId: '101',
          authorName: 'Sarah Johnson',
        };

        setRoadmap(mockRoadmap);
        setLoading(false);
      }, 500);
    };

    fetchRoadmap();
  }, [roadmapId]);

  // Handle form submission
  const handleSave = () => {
    if (!roadmap) return;

    setSaving(true);

    // TODO: Implement actual API call to save roadmap
    setTimeout(() => {
      setSaving(false);

      toast({
        title: 'Roadmap Saved',
        description: 'The roadmap has been updated successfully.',
      });
    }, 1000);
  };

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-4 text-muted-foreground">Loading roadmap data...</p>
        </div>
      </div>
    );
  }

  if (!roadmap) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <RiRoadMapLine className="mx-auto h-12 w-12 text-muted-foreground" />
          <h2 className="mt-4 text-xl font-semibold">Roadmap Not Found</h2>
          <p className="mt-2 text-muted-foreground">
            The roadmap you&apos;re trying to edit doesn&apos;t exist or has
            been removed.
          </p>
          <Button
            className="mt-4"
            onClick={() => router.push('/admin/roadmaps')}
          >
            Back to Roadmaps
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => router.push(`/admin/roadmaps/${roadmap.id}`)}
          >
            <RiArrowLeftLine className="mr-1" /> Back to Roadmap
          </Button>
          <h1 className="text-2xl font-bold">Edit Roadmap</h1>
          <p className="text-muted-foreground">
            Update roadmap details, structure, and settings
          </p>
        </div>

        <Button
          className="flex items-center gap-1"
          onClick={handleSave}
          disabled={saving}
        >
          <RiSaveLine /> {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>

      {/* Tabs */}
      <Tabs
        defaultValue="basic"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic" className="flex items-center gap-1">
            <RiFileListLine className="h-4 w-4" /> Basic Information
          </TabsTrigger>
          <TabsTrigger value="structure" className="flex items-center gap-1">
            <RiRoadMapLine className="h-4 w-4" /> Roadmap Structure
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-1">
            <RiFileListLine className="h-4 w-4" /> Advanced Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="mt-6 space-y-6">
          <RoadmapBasicInfo
            roadmap={roadmap}
            onUpdate={(updatedData) =>
              setRoadmap({ ...roadmap, ...updatedData })
            }
          />
        </TabsContent>

        <TabsContent value="structure" className="mt-6">
          <RoadmapStructureEditor roadmapId={roadmap.id} />
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <RoadmapSettings
            roadmap={roadmap}
            onUpdate={(updatedData) =>
              setRoadmap({ ...roadmap, ...updatedData })
            }
          />
        </TabsContent>
      </Tabs>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          className="flex items-center gap-1"
          onClick={handleSave}
          disabled={saving}
        >
          <RiSaveLine /> {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
}

export default RoadmapEditorPage;
