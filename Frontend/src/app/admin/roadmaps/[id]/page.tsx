/**
 * @file page.tsx
 * @description Roadmap detail page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiEditLine,
  RiDeleteBinLine,
  RiStarLine,
  RiRoadMapLine,
  RiTimeLine,
  RiUserLine,
  RiCheckboxCircleLine,
  RiBarChartLine,
  RiFileListLine,
} from 'react-icons/ri';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';

// Import components
import RoadmapStructure from './components/RoadmapStructure';
import RoadmapAnalytics from './components/RoadmapAnalytics';
import RoadmapFeedback from './components/RoadmapFeedback';

// Define roadmap interface
interface IRoadmap {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  status: 'Published' | 'Draft' | 'Archived';
  isFeatured: boolean;
  enrollmentCount: number;
  completionRate: number;
  averageRating: number;
  createdAt: string;
  updatedAt: string;
  estimatedHours: number;
  authorId: string;
  authorName: string;
}

function RoadmapDetailPage() {
  const router = useRouter();
  const params = useParams();
  const roadmapId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [roadmap, setRoadmap] = useState<IRoadmap | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    // TODO: Replace with actual API call
    const fetchRoadmap = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock roadmap data
        const mockRoadmap: IRoadmap = {
          id: roadmapId,
          title: 'Frontend Web Development',
          description:
            'Learn modern frontend development with HTML, CSS, JavaScript, and React. This comprehensive roadmap covers everything from the basics to advanced concepts, helping you become a proficient frontend developer ready for the industry.',
          category: 'Web Development',
          difficulty: 'Beginner',
          status: 'Published',
          isFeatured: true,
          enrollmentCount: 1245,
          completionRate: 68,
          averageRating: 4.7,
          createdAt: '2023-01-15T10:30:00Z',
          updatedAt: '2023-05-20T14:15:00Z',
          estimatedHours: 45,
          authorId: '101',
          authorName: 'Sarah Johnson',
        };

        setRoadmap(mockRoadmap);
        setLoading(false);
      }, 500);
    };

    fetchRoadmap();
  }, [roadmapId]);

  // Handle roadmap actions
  const handleFeatureToggle = () => {
    if (!roadmap) return;

    // TODO: Implement API call to toggle featured status
    toast({
      title: roadmap.isFeatured ? 'Roadmap Unfeatured' : 'Roadmap Featured',
      description: `Roadmap has been ${roadmap.isFeatured ? 'removed from' : 'added to'} featured section.`,
    });
  };

  const handleDeleteRoadmap = () => {
    if (!roadmap) return;

    if (
      confirm(
        `Are you sure you want to delete "${roadmap.title}"? This action cannot be undone.`,
      )
    ) {
      // TODO: Implement API call to delete roadmap
      toast({
        title: 'Roadmap Deleted',
        description: `"${roadmap.title}" has been permanently deleted.`,
        variant: 'destructive',
      });

      router.push('/admin/roadmaps');
    }
  };

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-4 text-muted-foreground">
            Loading roadmap details...
          </p>
        </div>
      </div>
    );
  }

  if (!roadmap) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <RiRoadMapLine className="mx-auto h-12 w-12 text-muted-foreground" />
          <h2 className="mt-4 text-xl font-semibold">Roadmap Not Found</h2>
          <p className="mt-2 text-muted-foreground">
            The roadmap you&apos;re looking for doesn&apos;t exist or has been
            removed.
          </p>
          <Button
            className="mt-4"
            onClick={() => router.push('/admin/roadmaps')}
          >
            Back to Roadmaps
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div>
        <Button
          variant="ghost"
          className="-ml-4 mb-2 flex items-center text-muted-foreground"
          onClick={() => router.push('/admin/roadmaps')}
        >
          <RiArrowLeftLine className="mr-1" /> Back to Roadmaps
        </Button>
      </div>

      {/* Roadmap Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
        <div>
          <div className="flex flex-wrap items-center gap-2">
            <h1 className="text-2xl font-bold">{roadmap.title}</h1>
            {roadmap.isFeatured && (
              <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
                <RiStarLine className="mr-1" /> Featured
              </Badge>
            )}
            <Badge
              className={`${
                roadmap.status === 'Published'
                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                  : roadmap.status === 'Draft'
                    ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {roadmap.status}
            </Badge>
          </div>
          <p className="mt-2 text-muted-foreground">{roadmap.description}</p>

          <div className="mt-4 flex flex-wrap items-center gap-x-6 gap-y-2 text-sm">
            <div className="flex items-center">
              <RiRoadMapLine className="mr-1 text-muted-foreground" />
              <span>{roadmap.category}</span>
            </div>
            <div className="flex items-center">
              <RiUserLine className="mr-1 text-muted-foreground" />
              <span>
                {roadmap.enrollmentCount.toLocaleString()} enrollments
              </span>
            </div>
            <div className="flex items-center">
              <RiCheckboxCircleLine className="mr-1 text-muted-foreground" />
              <span>{roadmap.completionRate}% completion rate</span>
            </div>
            <div className="flex items-center">
              <RiStarLine className="mr-1 text-muted-foreground" />
              <span>{roadmap.averageRating.toFixed(1)} rating</span>
            </div>
            <div className="flex items-center">
              <RiTimeLine className="mr-1 text-muted-foreground" />
              <span>{roadmap.estimatedHours} hours</span>
            </div>
          </div>

          <div className="mt-2 text-xs text-muted-foreground">
            <span>
              Created by {roadmap.authorName} • Last updated{' '}
              {new Date(roadmap.updatedAt).toLocaleDateString()}
            </span>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => router.push(`/admin/roadmaps/${roadmap.id}/edit`)}
          >
            <RiEditLine /> Edit Roadmap
          </Button>

          <Button
            variant="outline"
            className={`flex items-center gap-1 ${roadmap.isFeatured ? 'border-amber-300 text-amber-700 hover:bg-amber-100' : ''}`}
            onClick={handleFeatureToggle}
          >
            <RiStarLine /> {roadmap.isFeatured ? 'Unfeature' : 'Feature'}
          </Button>

          <Button
            variant="outline"
            className="border-red-300 text-red-700 hover:bg-red-100 flex items-center gap-1"
            onClick={handleDeleteRoadmap}
          >
            <RiDeleteBinLine /> Delete
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs
        defaultValue="overview"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="structure">Structure</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="feedback">User Feedback</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6 space-y-6">
          {/* Overview Tab */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <RiUserLine className="mr-2 text-muted-foreground" />{' '}
                  Enrollments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {roadmap.enrollmentCount.toLocaleString()}
                </div>
                <p className="text-xs text-muted-foreground">
                  Total users enrolled
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <RiCheckboxCircleLine className="mr-2 text-muted-foreground" />{' '}
                  Completion Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {roadmap.completionRate}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Users who completed the roadmap
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <RiStarLine className="mr-2 text-muted-foreground" /> Average
                  Rating
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {roadmap.averageRating.toFixed(1)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Based on user reviews
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <RiFileListLine className="mr-2" /> Roadmap Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">Description</h3>
                <p className="mt-1 text-muted-foreground">
                  {roadmap.description}
                </p>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <h3 className="text-sm font-medium">Category</h3>
                  <p className="mt-1 text-muted-foreground">
                    {roadmap.category}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Difficulty</h3>
                  <p className="mt-1 text-muted-foreground">
                    {roadmap.difficulty}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Estimated Time</h3>
                  <p className="mt-1 text-muted-foreground">
                    {roadmap.estimatedHours} hours
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Status</h3>
                  <p className="mt-1 text-muted-foreground">{roadmap.status}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Created By</h3>
                  <p className="mt-1 text-muted-foreground">
                    {roadmap.authorName}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Last Updated</h3>
                  <p className="mt-1 text-muted-foreground">
                    {new Date(roadmap.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button
              onClick={() => router.push(`/admin/roadmaps/${roadmap.id}/edit`)}
              className="flex items-center gap-1"
            >
              <RiEditLine /> Edit Roadmap
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="structure" className="mt-6">
          <RoadmapStructure roadmapId={roadmap.id} />
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <RoadmapAnalytics roadmapId={roadmap.id} />
        </TabsContent>

        <TabsContent value="feedback" className="mt-6">
          <RoadmapFeedback roadmapId={roadmap.id} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default RoadmapDetailPage;
