// Define battle interface
export interface IBattle {
  id: string;
  title: string;
  description: string;
  type: 'Solo' | 'Team' | 'Tournament';
  difficulty: 'Easy' | 'Medium' | 'Hard' | 'Expert';
  status: 'Scheduled' | 'Active' | 'Completed' | 'Cancelled';
  isFeatured: boolean;
  startDate: string;
  endDate: string;
  participantCount: number;
  maxParticipants: number;
  completionRate: number;
  createdAt: string;
  updatedAt: string;
  challengeCount: number;
  prizePool?: string;
  tags: string[];

  rules?: string;
  eligibility?: string;
  registrationDeadline?: string;
  prizes?: Array<{
    rank: string;
    reward: string;
    description?: string;
  }>;
}
