/**
 * @file page.tsx
 * @description Battles management page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiAddLine,
  RiSearchLine,
  RiFilterLine,
  RiLoader4Line,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from '@/hooks/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { IBattle } from './types';
import BattleLists from './components/BattleLists';
import { useAxiosGet } from '@/hooks/useAxios';
import { BATTLE_API, IBattleListResponse } from '@/services/battleService';

function BattlesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  // State for battles data
  const [battles, setBattles] = useState<IBattle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // API hooks
  const [fetchBattles] = useAxiosGet<IBattle[]>(BATTLE_API.LIST);

  // Function to load battles data - not using useCallback to avoid dependency issues
  const loadBattles = async () => {
    setIsLoading(true);
    setError(null); // Reset error state

    try {
      const response = await fetchBattles();
      if (response.success && response.data) {
        setBattles(response.data);
        setLastUpdated(new Date());
      } else {
        const errorMessage = response.message || 'Failed to load battles';
        console.error('Error loading battles:', errorMessage);
        setError(errorMessage);
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to load battles';
      console.error('Error loading battles:', error);
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load battles on component mount - using empty dependency array to prevent infinite API calls
  useEffect(() => {
    let isMounted = true;

    const initialLoad = async () => {
      try {
        if (!isMounted) return;
        await loadBattles();
      } catch (error) {
        console.error('Failed to load battles:', error);
        if (isMounted) {
          setError('An unexpected error occurred while loading battles');
        }
      }
    };

    initialLoad();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array to prevent infinite API calls

  // Get unique types for filter - only if there are battles available
  const types =
    battles.length === 0
      ? ['all']
      : ['all', ...new Set(battles.map((battle) => battle.type))];

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold">Battle Zone Management</h1>
          <p className="text-muted-foreground">
            Manage coding battles and competitions
          </p>
        </div>
        <Button
          className="flex items-center gap-2"
          onClick={() => router.push('/admin/battles/create')}
        >
          <RiAddLine /> Create New Battle
        </Button>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <div className="relative">
          <Input
            placeholder="Search battles..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
        </div>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger>
            <div className="flex items-center">
              <RiFilterLine className="mr-2" />
              <SelectValue placeholder="Type" />
            </div>
          </SelectTrigger>
          <SelectContent>
            {types.map((type) => (
              <SelectItem key={type} value={type}>
                {type === 'all' ? 'All Types' : type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
          <SelectTrigger>
            <div className="flex items-center">
              <RiFilterLine className="mr-2" />
              <SelectValue placeholder="Difficulty" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Difficulties</SelectItem>
            <SelectItem value="Easy">Easy</SelectItem>
            <SelectItem value="Medium">Medium</SelectItem>
            <SelectItem value="Hard">Hard</SelectItem>
            <SelectItem value="Expert">Expert</SelectItem>
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger>
            <div className="flex items-center">
              <RiFilterLine className="mr-2" />
              <SelectValue placeholder="Status" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="Scheduled">Scheduled</SelectItem>
            <SelectItem value="Active">Active</SelectItem>
            <SelectItem value="Completed">Completed</SelectItem>
            <SelectItem value="Cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Battles List */}
      <div className="rounded-md border bg-card p-6 shadow-sm">
        {error && (
          <div className="mb-4 rounded-md border border-destructive bg-destructive/10 p-4 text-destructive">
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={loadBattles}
              disabled={isLoading}
            >
              <RiLoader4Line
                className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
              />
              Retry
            </Button>
          </div>
        )}

        {isLoading ? (
          <div className="flex h-64 w-full items-center justify-center">
            <RiLoader4Line className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading battles...</span>
          </div>
        ) : (
          <>
            <BattleLists
              battles={battles}
              searchQuery={searchQuery}
              typeFilter={typeFilter}
              difficultyFilter={difficultyFilter}
              statusFilter={statusFilter}
            />

            {/* Last updated timestamp */}
            <div className="mt-4 text-right text-xs text-muted-foreground">
              Last updated: {lastUpdated.toLocaleString()}
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default BattlesPage;
