/**
 * @file page.tsx
 * @description Create Battle Page - redirects to the battle editor with 'new' ID
 */
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

function CreateBattlePage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the battle editor with 'new' ID
    router.push('/admin/battles/new/edit');
  }, [router]);

  return (
    <div className="flex h-[50vh] w-full items-center justify-center">
      <div className="flex flex-col items-center gap-2">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-lg">Redirecting to battle editor...</p>
      </div>
    </div>
  );
}

export default CreateBattlePage;
