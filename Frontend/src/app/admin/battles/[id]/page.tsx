/**
 * @file page.tsx
 * @description Battle Detail Page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiEditLine,
  RiStarLine,
  RiStarFill,
  RiTrophyLine,
  RiDeleteBin2Line,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { useAxiosGet, useAxiosPut, useAxiosDelete } from '@/hooks/useAxios';
import {
  BATTLE_API,
  IBattleResponse,
  IBattleFeatureParams,
} from '@/services/battleService';
import TabSections from './components/TabSections';
import DeleteConfirmation from './components/DeleteConfirmation';
import { IBattle } from '../types';

function BattleDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('structure');
  const [battle, setBattle] = useState<IBattle | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [fetchBattle] = useAxiosGet<IBattleResponse>(
    `${BATTLE_API.DETAIL}/${params.id}`,
  );
  const [toggleFeature] = useAxiosPut<IBattleResponse, IBattleFeatureParams>(
    BATTLE_API.FEATURE,
  );
  const [deleteBattle] = useAxiosDelete<{ success: boolean; message: string }>(
    `${BATTLE_API.DELETE}/${params.id}`,
  );

  // Fetch battle data
  useEffect(() => {
    const getBattleData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetchBattle();

        if (response.success && response.data?.battle) {
          setBattle(response.data.battle);
        } else {
          setError(response.message || 'Failed to fetch battle details');
          toast({
            title: 'Error',
            description: response.message || 'Failed to fetch battle details',
            variant: 'destructive',
          });
        }
      } catch (err) {
        console.error('Error fetching battle:', err);
        setError('An error occurred while fetching battle details');
        toast({
          title: 'Error',
          description: 'An error occurred while fetching battle details',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    getBattleData();
  }, [fetchBattle, params.id]);

  // Handle feature toggle
  const handleFeatureToggle = async () => {
    if (!battle) return;

    try {
      const response = await toggleFeature({
        battleId: battle.id,
        isFeatured: !battle.isFeatured,
      });

      if (response.success && response.data?.battle) {
        setBattle(response.data.battle);
        toast({
          title: 'Success',
          description: `Battle ${response.data.battle.isFeatured ? 'featured' : 'unfeatured'} successfully`,
        });
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to update feature status',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error toggling feature status:', err);
      toast({
        title: 'Error',
        description: 'An error occurred while updating feature status',
        variant: 'destructive',
      });
    }
  };

  // Handle battle deletion
  const handleDeleteBattle = async () => {
    try {
      const response = await deleteBattle();

      if (response.success) {
        toast({
          title: 'Success',
          description: response.message || 'Battle deleted successfully',
        });
        router.push('/admin/battles');
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to delete battle',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error deleting battle:', err);
      toast({
        title: 'Error',
        description: 'An error occurred while deleting the battle',
        variant: 'destructive',
      });
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4 p-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  // Error state
  if (error || !battle) {
    return (
      <div className="flex flex-col items-center justify-center p-6">
        <div className="rounded-lg bg-destructive/10 p-4 text-destructive">
          {error || 'Battle not found'}
        </div>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push('/admin/battles')}
        >
          <RiArrowLeftLine className="mr-2 h-4 w-4" />
          Back to Battles
        </Button>
      </div>
    );
  }

  // Calculate battle progress percentage
  const calculateProgress = () => {
    const now = new Date().getTime();
    const start = new Date(battle.startDate).getTime();
    const end = new Date(battle.endDate).getTime();

    if (now < start) return 0;
    if (now > end) return 100;

    return Math.floor(((now - start) / (end - start)) * 100);
  };

  // This function is already implemented in the API integration section above
  // and is now using the actual API call to toggle the featured status

  return (
    <div className="space-y-6">
      {/* Header with back button and actions */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push('/admin/battles')}
            aria-label="Back to battles"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{battle.title}</h1>
            <p className="text-muted-foreground">Battle ID: {battle.id}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className={`flex items-center gap-1 ${battle.isFeatured ? 'border-amber-300 text-amber-700 hover:bg-amber-100' : ''}`}
            onClick={handleFeatureToggle}
          >
            {battle.isFeatured ? (
              <RiStarFill className="h-4 w-4" />
            ) : (
              <RiStarLine className="h-4 w-4" />
            )}
            {battle.isFeatured ? 'Featured' : 'Feature'}
          </Button>

          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => router.push(`/admin/battles/${battle.id}/edit`)}
          >
            <RiEditLine className="h-4 w-4" />
            Edit
          </Button>

          <Button
            variant="outline"
            className="border-red-300 text-red-700 hover:bg-red-100 flex items-center gap-1"
            onClick={() => setIsDeleteDialogOpen(true)}
          >
            <RiDeleteBin2Line className="h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Battle Overview Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-6 md:flex-row">
            <div className="flex-1">
              <div className="mb-4 flex flex-wrap gap-2">
                <Badge
                  className={`${
                    battle.status === 'Active'
                      ? 'bg-green-100 text-green-800 hover:bg-green-200'
                      : battle.status === 'Scheduled'
                        ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                        : battle.status === 'Completed'
                          ? 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                  }`}
                >
                  {battle.status}
                </Badge>
                <Badge
                  className={`${
                    battle.difficulty === 'Easy'
                      ? 'bg-green-100 text-green-800'
                      : battle.difficulty === 'Medium'
                        ? 'bg-blue-100 text-blue-800'
                        : battle.difficulty === 'Hard'
                          ? 'bg-orange-100 text-orange-800'
                          : 'bg-red-100 text-red-800'
                  }`}
                >
                  {battle.difficulty}
                </Badge>
                <Badge>{battle.type}</Badge>
                {battle.isFeatured && (
                  <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
                    Featured
                  </Badge>
                )}
              </div>

              <p className="mb-4 text-muted-foreground">{battle.description}</p>

              <div className="mb-4 flex flex-wrap gap-2">
                {battle.tags?.map((tag: string) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>

              <div className="mb-4 grid grid-cols-2 gap-4 sm:grid-cols-4">
                <div>
                  <div className="text-sm text-muted-foreground">
                    Start Date
                  </div>
                  <div className="font-medium">
                    {new Date(battle.startDate).toLocaleDateString()}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">End Date</div>
                  <div className="font-medium">
                    {new Date(battle.endDate).toLocaleDateString()}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">
                    Participants
                  </div>
                  <div className="font-medium">
                    {battle.participantCount}/{battle.maxParticipants}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">
                    Challenges
                  </div>
                  <div className="font-medium">{battle.challengeCount}</div>
                </div>
              </div>

              {/* Progress Bar for Active Battles */}
              {battle.status === 'Active' && (
                <div className="mb-4">
                  <div className="mb-2 flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      Battle Progress
                    </div>
                    <div className="text-sm font-medium">
                      {calculateProgress()}%
                    </div>
                  </div>
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div
                      className="h-2 rounded-full bg-primary"
                      style={{ width: `${calculateProgress()}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Rules and Eligibility */}
              <div className="space-y-4">
                {battle.rules && (
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Rules</h3>
                    <p className="text-sm text-muted-foreground">
                      {battle.rules}
                    </p>
                  </div>
                )}

                {battle.eligibility && (
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Eligibility</h3>
                    <p className="text-sm text-muted-foreground">
                      {battle.eligibility}
                    </p>
                  </div>
                )}

                {battle.registrationDeadline && (
                  <div>
                    <h3 className="mb-1 text-sm font-medium">
                      Registration Deadline
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {new Date(battle.registrationDeadline).toLocaleString()}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Prize Information */}
            <div className="md:w-1/3 lg:w-1/4">
              <Card className="bg-muted">
                <CardContent className="space-y-4 p-4">
                  <h3 className="flex items-center gap-1 font-semibold">
                    <RiTrophyLine /> Prize Pool: {battle.prizePool}
                  </h3>

                  {battle.prizes && battle.prizes.length > 0 && (
                    <div className="space-y-3">
                      {battle.prizes.map((prize, index) => (
                        <div
                          key={index}
                          className="rounded-md bg-background p-3"
                        >
                          <div className="font-medium">{prize.rank}</div>
                          <div className="font-bold text-primary">
                            {prize.reward}
                          </div>
                          {prize.description && (
                            <div className="mt-1 text-xs text-muted-foreground">
                              {prize.description}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different sections */}
      <TabSections
        battle={battle}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmation
        isDeleteDialogOpen={isDeleteDialogOpen}
        setIsDeleteDialogOpen={setIsDeleteDialogOpen}
        battle={battle}
        onDelete={handleDeleteBattle}
      />
    </div>
  );
}

export default BattleDetailPage;
