/**
 * @file BattleStructure.tsx
 * @description Component to display the structure and challenges of a battle
 */
'use client';

import { useState } from 'react';
import {
  RiSwordLine,
  RiAddLine,
  RiDeleteBinLine,
  RiDraggable,
  RiArrowUpDownLine,
  RiArrowDownSLine,
  RiArrowUpSLine,
  RiLinkM,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/components/ui/use-toast';

interface IChallenge {
  id: string;
  title: string;
  description: string;
  type: string;
  difficulty: string;
  points: number;
  timeLimit?: number; // in minutes
  order: number;
  isRequired: boolean;
}

interface IBattleStructureProps {
  battleId: string;
}

function BattleStructure({ battleId }: IBattleStructureProps) {
  const [challenges, setChallenges] = useState<IChallenge[]>([
    {
      id: '1',
      title: 'Responsive Navigation Bar',
      description:
        'Create a responsive navigation bar that works on mobile and desktop.',
      type: 'Coding',
      difficulty: 'Easy',
      points: 100,
      timeLimit: 60,
      order: 1,
      isRequired: true,
    },
    {
      id: '2',
      title: 'Interactive Form Validation',
      description:
        'Implement client-side form validation with helpful error messages.',
      type: 'Coding',
      difficulty: 'Medium',
      points: 150,
      timeLimit: 90,
      order: 2,
      isRequired: true,
    },
    {
      id: '3',
      title: 'CSS Grid Layout',
      description: 'Create a responsive grid layout using CSS Grid.',
      type: 'Coding',
      difficulty: 'Medium',
      points: 150,
      timeLimit: 75,
      order: 3,
      isRequired: true,
    },
    {
      id: '4',
      title: 'Accessibility Quiz',
      description:
        'Test your knowledge of web accessibility principles and practices.',
      type: 'Quiz',
      difficulty: 'Medium',
      points: 100,
      timeLimit: 30,
      order: 4,
      isRequired: true,
    },
    {
      id: '5',
      title: 'Bonus Challenge: Animation Effects',
      description:
        'Implement smooth animation effects using CSS and JavaScript.',
      type: 'Coding',
      difficulty: 'Hard',
      points: 200,
      timeLimit: 120,
      order: 5,
      isRequired: false,
    },
  ]);

  const [expandedChallenges, setExpandedChallenges] = useState<
    Record<string, boolean>
  >({});
  const [isAddChallengeDialogOpen, setIsAddChallengeDialogOpen] =
    useState(false);
  const [selectedChallenge, setSelectedChallenge] = useState<IChallenge | null>(
    null,
  );

  // Toggle challenge expansion
  const toggleChallenge = (challengeId: string) => {
    setExpandedChallenges((prev) => ({
      ...prev,
      [challengeId]: !prev[challengeId],
    }));
  };

  // Handle adding a challenge to the battle
  const handleAddChallenge = () => {
    // TODO: Implement add challenge functionality
    setIsAddChallengeDialogOpen(true);
  };

  // Handle removing a challenge from the battle
  const handleRemoveChallenge = (challengeId: string) => {
    // TODO: Implement remove challenge functionality
    if (
      confirm('Are you sure you want to remove this challenge from the battle?')
    ) {
      setChallenges(
        challenges.filter((challenge) => challenge.id !== challengeId),
      );
      toast({
        title: 'Challenge Removed',
        description: 'The challenge has been removed from this battle.',
      });
    }
  };

  // Handle reordering challenges
  const handleMoveChallenge = (
    challengeId: string,
    direction: 'up' | 'down',
  ) => {
    const index = challenges.findIndex(
      (challenge) => challenge.id === challengeId,
    );
    if (index === -1) return;

    if (direction === 'up' && index > 0) {
      const newChallenges = [...challenges];
      [newChallenges[index - 1], newChallenges[index]] = [
        newChallenges[index],
        newChallenges[index - 1],
      ];
      // Update order property
      newChallenges[index - 1].order = index;
      newChallenges[index].order = index + 1;
      setChallenges(newChallenges);
    } else if (direction === 'down' && index < challenges.length - 1) {
      const newChallenges = [...challenges];
      [newChallenges[index], newChallenges[index + 1]] = [
        newChallenges[index + 1],
        newChallenges[index],
      ];
      // Update order property
      newChallenges[index].order = index + 1;
      newChallenges[index + 1].order = index + 2;
      setChallenges(newChallenges);
    }
  };

  // Handle viewing challenge details
  const handleViewChallenge = (challenge: IChallenge) => {
    setSelectedChallenge(challenge);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Battle Challenges</h2>
        <Button
          variant="outline"
          className="flex items-center gap-1"
          onClick={handleAddChallenge}
        >
          <RiAddLine />
          Add Challenge
        </Button>
      </div>

      {challenges.length > 0 ? (
        <div className="space-y-4">
          {challenges.map((challenge, index) => (
            <Card
              key={challenge.id}
              className={`border ${challenge.isRequired ? '' : 'border-dashed'}`}
            >
              <CardHeader
                className="flex cursor-pointer flex-row items-center justify-between px-6 py-4"
                onClick={() => toggleChallenge(challenge.id)}
              >
                <div className="flex items-center gap-2">
                  <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full text-primary">
                    {challenge.order}
                  </div>
                  <CardTitle className="text-lg">{challenge.title}</CardTitle>
                  <div className="flex items-center gap-1">
                    <Badge variant="outline">{challenge.type}</Badge>
                    <Badge
                      className={`${
                        challenge.difficulty === 'Easy'
                          ? 'bg-green-100 text-green-800'
                          : challenge.difficulty === 'Medium'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-orange-100 text-orange-800'
                      }`}
                    >
                      {challenge.difficulty}
                    </Badge>
                    {!challenge.isRequired && (
                      <Badge variant="outline" className="border-dashed">
                        Optional
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewChallenge(challenge);
                    }}
                  >
                    <RiLinkM className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon">
                    {expandedChallenges[challenge.id] ? (
                      <RiArrowUpSLine />
                    ) : (
                      <RiArrowDownSLine />
                    )}
                  </Button>
                </div>
              </CardHeader>

              {expandedChallenges[challenge.id] && (
                <CardContent className="px-6 pb-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="mb-1 text-sm font-medium">Description</h3>
                      <p className="text-sm text-muted-foreground">
                        {challenge.description}
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
                      <div>
                        <h3 className="mb-1 text-sm font-medium">Points</h3>
                        <p className="text-sm">{challenge.points}</p>
                      </div>
                      {challenge.timeLimit && (
                        <div>
                          <h3 className="mb-1 text-sm font-medium">
                            Time Limit
                          </h3>
                          <p className="text-sm">
                            {challenge.timeLimit} minutes
                          </p>
                        </div>
                      )}
                      <div>
                        <h3 className="mb-1 text-sm font-medium">Required</h3>
                        <p className="text-sm">
                          {challenge.isRequired ? 'Yes' : 'No'}
                        </p>
                      </div>
                      <div>
                        <h3 className="mb-1 text-sm font-medium">Order</h3>
                        <p className="text-sm">{challenge.order}</p>
                      </div>
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={() => handleMoveChallenge(challenge.id, 'up')}
                        disabled={index === 0}
                      >
                        <RiArrowUpDownLine className="rotate-90" />
                        Move Up
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={() =>
                          handleMoveChallenge(challenge.id, 'down')
                        }
                        disabled={index === challenges.length - 1}
                      >
                        <RiArrowUpDownLine className="-rotate-90" />
                        Move Down
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-red-300 text-red-700 hover:bg-red-100 flex items-center gap-1"
                        onClick={() => handleRemoveChallenge(challenge.id)}
                      >
                        <RiDeleteBinLine />
                        Remove
                      </Button>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-6 text-center">
          <div className="py-8">
            <RiSwordLine className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">
              No challenges added yet
            </h3>
            <p className="mt-2 text-muted-foreground">
              Add challenges to this battle to get started.
            </p>
            <Button
              className="mx-auto mt-4 flex items-center gap-1"
              onClick={handleAddChallenge}
            >
              <RiAddLine />
              Add Challenge
            </Button>
          </div>
        </Card>
      )}

      {/* Add Challenge Dialog */}
      <Dialog
        open={isAddChallengeDialogOpen}
        onOpenChange={setIsAddChallengeDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Challenge to Battle</DialogTitle>
            <DialogDescription>
              Select challenges to add to this battle.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {/* TODO: Implement challenge search and selection UI */}
            <p className="text-center text-muted-foreground">
              Challenge selection interface would be implemented here.
            </p>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAddChallengeDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                // TODO: Implement adding selected challenges
                setIsAddChallengeDialogOpen(false);
                toast({
                  title: 'Challenge Added',
                  description:
                    'The selected challenge has been added to the battle.',
                });
              }}
            >
              Add Selected
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Challenge Details Dialog */}
      {selectedChallenge && (
        <Dialog
          open={!!selectedChallenge}
          onOpenChange={() => setSelectedChallenge(null)}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{selectedChallenge.title}</DialogTitle>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <div className="flex flex-wrap gap-2">
                <Badge variant="outline">{selectedChallenge.type}</Badge>
                <Badge
                  className={`${
                    selectedChallenge.difficulty === 'Easy'
                      ? 'bg-green-100 text-green-800'
                      : selectedChallenge.difficulty === 'Medium'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-orange-100 text-orange-800'
                  }`}
                >
                  {selectedChallenge.difficulty}
                </Badge>
                {!selectedChallenge.isRequired && (
                  <Badge variant="outline" className="border-dashed">
                    Optional
                  </Badge>
                )}
              </div>

              <div>
                <h3 className="mb-1 text-sm font-medium">Description</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedChallenge.description}
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4 sm:grid-cols-3">
                <div>
                  <h3 className="mb-1 text-sm font-medium">Points</h3>
                  <p className="text-sm">{selectedChallenge.points}</p>
                </div>
                {selectedChallenge.timeLimit && (
                  <div>
                    <h3 className="mb-1 text-sm font-medium">Time Limit</h3>
                    <p className="text-sm">
                      {selectedChallenge.timeLimit} minutes
                    </p>
                  </div>
                )}
                <div>
                  <h3 className="mb-1 text-sm font-medium">Required</h3>
                  <p className="text-sm">
                    {selectedChallenge.isRequired ? 'Yes' : 'No'}
                  </p>
                </div>
              </div>

              <div>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    // TODO: Navigate to challenge detail page
                    window.open(
                      `/admin/challenges/${selectedChallenge.id}`,
                      '_blank',
                    );
                  }}
                >
                  View Full Challenge Details
                </Button>
              </div>
            </div>

            <DialogFooter>
              <Button onClick={() => setSelectedChallenge(null)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

export default BattleStructure;
