/**
 * @file BattleAnalytics.tsx
 * @description Component to display analytics data for a battle
 */
'use client';

import { useState } from 'react';
import {
  RiBarChartLine,
  RiLineChartLine,
  RiUserLine,
  RiTimeLine,
  RiCheckboxCircleLine,
  RiCalendarLine,
  RiTrophyLine,
  RiMapPinLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface IBattleAnalyticsProps {
  battleId: string;
}

function BattleAnalytics({ battleId }: IBattleAnalyticsProps) {
  const [timeRange, setTimeRange] = useState('30days');

  // TODO: Replace with actual API call to fetch analytics data using battleId
  // Example: const { data } = useQuery(['battleAnalytics', battleId], () => fetchBattleAnalytics(battleId));
  const analyticsData = {
    totalParticipants: 156,
    activeParticipants: 132,
    completionRate: 45,
    averageScore: 320,
    averageTimeSpent: 120, // minutes
    participationOverTime: [
      { date: '2023-05-15', participants: 25 },
      { date: '2023-05-16', participants: 42 },
      { date: '2023-05-17', participants: 58 },
      { date: '2023-05-18', participants: 65 },
      { date: '2023-05-19', participants: 78 },
      { date: '2023-05-20', participants: 92 },
      { date: '2023-05-21', participants: 110 },
      { date: '2023-05-22', participants: 125 },
      { date: '2023-05-23', participants: 138 },
      { date: '2023-05-24', participants: 145 },
      { date: '2023-05-25', participants: 150 },
      { date: '2023-05-26', participants: 156 },
    ],
    challengeCompletion: [
      { challenge: 'Responsive Navigation Bar', completionRate: 85 },
      { challenge: 'Interactive Form Validation', completionRate: 72 },
      { challenge: 'CSS Grid Layout', completionRate: 68 },
      { challenge: 'Accessibility Quiz', completionRate: 90 },
      { challenge: 'Animation Effects', completionRate: 45 },
    ],
    scoreDistribution: {
      '0-100': 15,
      '101-200': 25,
      '201-300': 30,
      '301-400': 20,
      '401-500': 10,
    },
    userDemographics: {
      experienceLevel: {
        Beginner: 30,
        Intermediate: 45,
        Advanced: 25,
      },
      topCountries: [
        { country: 'United States', percentage: 25 },
        { country: 'India', percentage: 20 },
        { country: 'Germany', percentage: 12 },
        { country: 'United Kingdom', percentage: 10 },
        { country: 'Canada', percentage: 8 },
      ],
    },
    timeSpentDistribution: {
      under1h: 10,
      '1to2h': 25,
      '2to3h': 35,
      '3to4h': 20,
      over4h: 10,
    },
  };

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-end">
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <div className="flex items-center">
              <RiCalendarLine className="mr-2" />
              <SelectValue placeholder="Select time range" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="90days">Last 90 days</SelectItem>
            <SelectItem value="all">All time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Total Participants
                </p>
                <p className="text-3xl font-bold">
                  {analyticsData.totalParticipants}
                </p>
              </div>
              <div className="bg-primary/10 rounded-full p-2">
                <RiUserLine className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completion Rate</p>
                <p className="text-3xl font-bold">
                  {analyticsData.completionRate}%
                </p>
              </div>
              <div className="bg-primary/10 rounded-full p-2">
                <RiCheckboxCircleLine className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Average Score</p>
                <p className="text-3xl font-bold">
                  {analyticsData.averageScore}
                </p>
              </div>
              <div className="bg-primary/10 rounded-full p-2">
                <RiTrophyLine className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg. Time Spent</p>
                <p className="text-3xl font-bold">
                  {analyticsData.averageTimeSpent} min
                </p>
              </div>
              <div className="bg-primary/10 rounded-full p-2">
                <RiTimeLine className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="participation" className="w-full">
        <TabsList className="grid grid-cols-3 md:w-[400px]">
          <TabsTrigger
            value="participation"
            className="flex items-center gap-1"
          >
            <RiLineChartLine className="h-4 w-4" />
            Participation
          </TabsTrigger>
          <TabsTrigger value="challenges" className="flex items-center gap-1">
            <RiBarChartLine className="h-4 w-4" />
            Challenges
          </TabsTrigger>
          <TabsTrigger value="demographics" className="flex items-center gap-1">
            <RiMapPinLine className="h-4 w-4" />
            Demographics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="participation" className="mt-6 space-y-6">
          {/* Participation Over Time */}
          <Card>
            <CardHeader>
              <CardTitle>Participation Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] w-full">
                {/* TODO: Implement chart using a charting library */}
                <div className="flex h-full items-end gap-2">
                  {analyticsData.participationOverTime.map((data, index) => {
                    const height =
                      (data.participants /
                        Math.max(
                          ...analyticsData.participationOverTime.map(
                            (d) => d.participants,
                          ),
                        )) *
                      100;
                    return (
                      <div key={index} className="group relative flex-1">
                        <div
                          className="bg-primary/80 rounded-t-sm hover:bg-primary"
                          style={{ height: `${height}%` }}
                        ></div>
                        <div className="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-full transform whitespace-nowrap rounded bg-popover p-1 text-xs text-popover-foreground opacity-0 shadow transition-opacity group-hover:opacity-100">
                          {new Date(data.date).toLocaleDateString()}:{' '}
                          {data.participants} participants
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Score Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Score Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] w-full">
                {/* TODO: Implement chart using a charting library */}
                <div className="grid h-full grid-cols-5 gap-4">
                  {Object.entries(analyticsData.scoreDistribution).map(
                    ([key, value], index) => {
                      return (
                        <div key={index} className="flex flex-col items-center">
                          <div className="flex w-full flex-1 items-end">
                            <div
                              className="bg-primary/80 w-full rounded-t-sm hover:bg-primary"
                              style={{ height: `${value}%` }}
                            ></div>
                          </div>
                          <div className="mt-2 text-center text-xs text-muted-foreground">
                            {key} points
                          </div>
                          <div className="text-sm font-medium">{value}%</div>
                        </div>
                      );
                    },
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Time Spent Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Time Spent Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] w-full">
                {/* TODO: Implement chart using a charting library */}
                <div className="grid h-full grid-cols-5 gap-4">
                  {Object.entries(analyticsData.timeSpentDistribution).map(
                    ([key, value], index) => {
                      const labels = {
                        under1h: 'Under 1h',
                        '1to2h': '1-2h',
                        '2to3h': '2-3h',
                        '3to4h': '3-4h',
                        over4h: 'Over 4h',
                      };
                      const label = labels[key as keyof typeof labels];
                      return (
                        <div key={index} className="flex flex-col items-center">
                          <div className="flex w-full flex-1 items-end">
                            <div
                              className="bg-primary/80 w-full rounded-t-sm hover:bg-primary"
                              style={{ height: `${value}%` }}
                            ></div>
                          </div>
                          <div className="mt-2 text-center text-xs text-muted-foreground">
                            {label}
                          </div>
                          <div className="text-sm font-medium">{value}%</div>
                        </div>
                      );
                    },
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="challenges" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Challenge Completion Rates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.challengeCompletion.map((challenge, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span>{challenge.challenge}</span>
                      <span className="text-sm text-muted-foreground">
                        {challenge.completionRate}%
                      </span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div
                        className="h-2 rounded-full bg-primary"
                        style={{ width: `${challenge.completionRate}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="demographics" className="mt-6 space-y-6">
          {/* User Experience Level */}
          <Card>
            <CardHeader>
              <CardTitle>User Experience Level</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] w-full">
                {/* TODO: Implement chart using a charting library */}
                <div className="grid h-full grid-cols-3 gap-4">
                  {Object.entries(
                    analyticsData.userDemographics.experienceLevel,
                  ).map(([key, value], index) => {
                    return (
                      <div key={index} className="flex flex-col items-center">
                        <div className="flex w-full flex-1 items-end">
                          <div
                            className="bg-primary/80 w-full rounded-t-sm hover:bg-primary"
                            style={{ height: `${value}%` }}
                          ></div>
                        </div>
                        <div className="mt-2 text-sm font-medium">{key}</div>
                        <div className="text-sm text-muted-foreground">
                          {value}%
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Countries */}
          <Card>
            <CardHeader>
              <CardTitle>Top Countries</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.userDemographics.topCountries.map(
                  (country, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span>{country.country}</span>
                        <span className="text-sm text-muted-foreground">
                          {country.percentage}%
                        </span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div
                          className="h-2 rounded-full bg-primary"
                          style={{ width: `${country.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ),
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default BattleAnalytics;
