/**
 * @file EmailParticipantsDialog.tsx
 * @description Dialog component for sending emails to battle participants
 */
'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RiMailLine, RiSendPlaneLine } from 'react-icons/ri';

interface IParticipant {
  id: string;
  name: string;
  email: string;
  status: string;
}

interface IEmailParticipantsDialogProps {
  participants: IParticipant[];
  selectedParticipantIds?: string[];
  battleName: string;
  onSendEmails: (emailData: {
    subject: string;
    message: string;
    recipientIds: string[];
    template?: string;
  }) => Promise<void>;
  trigger?: React.ReactNode;
}

function EmailParticipantsDialog({
  participants,
  selectedParticipantIds = [],
  battleName,
  onSendEmails,
  trigger,
}: IEmailParticipantsDialogProps) {
  const [open, setOpen] = useState(false);
  const [subject, setSubject] = useState(`Updates about ${battleName}`);
  const [message, setMessage] = useState('');
  const [template, setTemplate] = useState('custom');
  const [recipientIds, setRecipientIds] = useState<string[]>(
    selectedParticipantIds,
  );
  const [isSending, setIsSending] = useState(false);
  const [sendToAll, setSendToAll] = useState(
    selectedParticipantIds.length === 0,
  );

  // Reset form when dialog opens
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      setSubject(`Updates about ${battleName}`);
      setMessage('');
      setTemplate('custom');
      setRecipientIds(selectedParticipantIds);
      setSendToAll(selectedParticipantIds.length === 0);
    }
  };

  // Handle template selection
  const handleTemplateChange = (value: string) => {
    setTemplate(value);

    // Set predefined message based on template
    switch (value) {
      case 'reminder':
        setSubject(`Reminder: ${battleName} is starting soon`);
        setMessage(
          `Hello,\n\nThis is a friendly reminder that the ${battleName} battle is starting soon. Please make sure you're prepared and ready to participate.\n\nGood luck!\n\nThe Mr. Engineer Team`,
        );
        break;
      case 'results':
        setSubject(`Results: ${battleName} battle`);
        setMessage(
          `Hello,\n\nThe ${battleName} battle has concluded. You can now view your results and standings on the platform.\n\nThank you for participating!\n\nThe Mr. Engineer Team`,
        );
        break;
      case 'announcement':
        setSubject(`Important Announcement: ${battleName}`);
        setMessage(
          `Hello,\n\nWe have an important announcement regarding the ${battleName} battle.\n\n[Insert announcement details here]\n\nThe Mr. Engineer Team`,
        );
        break;
      case 'custom':
      default:
        setSubject(`Updates about ${battleName}`);
        setMessage('');
        break;
    }
  };

  // Handle checkbox selection for recipients
  const handleRecipientToggle = (participantId: string) => {
    setRecipientIds((prev) => {
      if (prev.includes(participantId)) {
        return prev.filter((id) => id !== participantId);
      } else {
        return [...prev, participantId];
      }
    });
  };

  // Handle send to all toggle
  const handleSendToAllChange = (checked: boolean) => {
    setSendToAll(checked);
    if (checked) {
      setRecipientIds(participants.map((p) => p.id));
    } else {
      setRecipientIds([]);
    }
  };

  // Handle send email
  const handleSendEmail = async () => {
    if (recipientIds.length === 0) {
      // Show error or notification
      return;
    }

    setIsSending(true);
    try {
      await onSendEmails({
        subject,
        message,
        recipientIds,
        template: template !== 'custom' ? template : undefined,
      });
      setOpen(false);
    } catch (error) {
      console.error('Error sending emails:', error);
      // Show error notification
    } finally {
      setIsSending(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" className="flex items-center gap-1">
      <RiMailLine className="h-4 w-4" />
      Email Participants
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Email Battle Participants</DialogTitle>
          <DialogDescription>
            Send an email to selected participants of the battle.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Email Template */}
          <div className="space-y-2">
            <Label htmlFor="template">Email Template</Label>
            <Select value={template} onValueChange={handleTemplateChange}>
              <SelectTrigger id="template">
                <SelectValue placeholder="Select a template" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="custom">Custom Message</SelectItem>
                <SelectItem value="reminder">Battle Reminder</SelectItem>
                <SelectItem value="results">Battle Results</SelectItem>
                <SelectItem value="announcement">
                  Important Announcement
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Email Subject */}
          <div className="space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Email subject"
            />
          </div>

          {/* Email Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your message here"
              rows={6}
            />
          </div>

          {/* Recipients */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Recipients</Label>
              <div className="flex items-center gap-2">
                <Checkbox
                  id="send-to-all"
                  checked={sendToAll}
                  onCheckedChange={handleSendToAllChange}
                />
                <Label htmlFor="send-to-all" className="cursor-pointer text-sm">
                  Send to all participants
                </Label>
              </div>
            </div>

            {!sendToAll && (
              <div className="mt-2 max-h-[200px] divide-y overflow-y-auto rounded-md border">
                {participants.length > 0 ? (
                  participants.map((participant) => (
                    <div
                      key={participant.id}
                      className="flex cursor-pointer items-center p-3 hover:bg-muted"
                      onClick={() => handleRecipientToggle(participant.id)}
                    >
                      <Checkbox
                        checked={recipientIds.includes(participant.id)}
                        onCheckedChange={() =>
                          handleRecipientToggle(participant.id)
                        }
                        className="mr-3"
                      />
                      <div>
                        <p className="font-medium">{participant.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {participant.email}
                        </p>
                      </div>
                      <div className="ml-auto">
                        <span
                          className={`rounded-full px-2 py-0.5 text-xs ${participant.status === 'Active' ? 'bg-green-100 text-green-800' : participant.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}
                        >
                          {participant.status}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-muted-foreground">
                    No participants found for this battle.
                  </div>
                )}
              </div>
            )}

            <p className="mt-2 text-sm text-muted-foreground">
              {sendToAll
                ? `Email will be sent to all ${participants.length} participants.`
                : `Email will be sent to ${recipientIds.length} selected participant${recipientIds.length !== 1 ? 's' : ''}.`}
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSendEmail}
            disabled={
              isSending ||
              recipientIds.length === 0 ||
              !subject.trim() ||
              !message.trim()
            }
            className="flex items-center gap-1"
          >
            {isSending ? (
              'Sending...'
            ) : (
              <>
                <RiSendPlaneLine className="h-4 w-4" />
                Send Email
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default EmailParticipantsDialog;
