import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { IBattle } from '../../../types';
import { Button } from '@/components/ui/button';

interface IDeleteConfirmationProps {
  isDeleteDialogOpen: boolean;
  setIsDeleteDialogOpen: (open: boolean) => void;
  battle: IBattle;
  onDelete?: () => Promise<void>;
}

export default function DeleteConfirmation({
  isDeleteDialogOpen,
  setIsDeleteDialogOpen,
  battle,
  onDelete,
}: IDeleteConfirmationProps) {
  const router = useRouter();

  // Handle delete battle
  const handleDeleteBattle = async () => {
    if (onDelete) {
      // Use the parent component's delete function if provided
      await onDelete();
    } else {
      // Fallback for backward compatibility
      toast({
        title: 'Battle Deleted',
        description: `"${battle.title}" has been permanently deleted.`,
        variant: 'destructive',
      });
      setIsDeleteDialogOpen(false);
      router.push('/admin/battles');
    }
  };
  return (
    <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Battle</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete &quot;{battle.title}&quot;? This
            action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsDeleteDialogOpen(false)}
          >
            Cancel
          </Button>
          <Button variant="destructive" onClick={handleDeleteBattle}>
            Delete Battle
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
