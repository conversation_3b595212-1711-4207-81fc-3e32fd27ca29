'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { useAxiosGet } from '@/hooks/useAxios';
import { BATTLE_API } from '@/services/battleService';
import {
  RiAddLine,
  RiDeleteBinLine,
  RiArrowUpSLine,
  RiArrowDownSLine,
} from 'react-icons/ri';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

interface IBattleStructureProps {
  battleId: string;
}

interface IChallenge {
  id: string;
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  timeLimit: number; // in minutes
  type: 'coding' | 'quiz' | 'design';
  order: number;
}

interface IBattleStructureResponse {
  success: boolean;
  message?: string;
  data?: {
    challenges: IChallenge[];
  };
}

export default function BattleStructure({ battleId }: IBattleStructureProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [challenges, setChallenges] = useState<IChallenge[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearchDialogOpen, setIsSearchDialogOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<IChallenge[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // API hook
  const [fetchStructure] = useAxiosGet<IBattleStructureResponse>(
    `${BATTLE_API.DETAIL}/${battleId}/structure`,
  );

  // Fetch battle structure data
  useEffect(() => {
    const getStructureData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetchStructure();

        if (response.success && response.data) {
          // Type assertion to ensure correct typing
          setChallenges(
            (response.data as unknown as { challenges: IChallenge[] })
              .challenges || [],
          );
        } else {
          setError(response.message || 'Failed to fetch battle structure data');
          toast({
            title: 'Error',
            description:
              response.message || 'Failed to fetch battle structure data',
            variant: 'destructive',
          });
        }
      } catch (err) {
        console.error('Error fetching battle structure:', err);
        setError('An error occurred while fetching battle structure data');
        toast({
          title: 'Error',
          description: 'An error occurred while fetching battle structure data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    getStructureData();
  }, [fetchStructure, battleId, toast]);

  // Handle challenge search
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);

    // TODO: Implement actual API call to search challenges
    // Simulating API call with timeout
    setTimeout(() => {
      // Mock search results
      const results: IChallenge[] = [
        {
          id: 'ch1',
          title: 'Algorithm Challenge: Sorting',
          description: 'Implement a sorting algorithm',
          difficulty: 'medium' as const,
          points: 100,
          timeLimit: 30,
          type: 'coding' as const,
          order: 1,
        },
        {
          id: 'ch2',
          title: 'Data Structures Quiz',
          description: 'Test your knowledge of data structures',
          difficulty: 'easy' as const,
          points: 50,
          timeLimit: 15,
          type: 'quiz' as const,
          order: 2,
        },
        {
          id: 'ch3',
          title: 'UI Design Challenge',
          description: 'Design a responsive interface',
          difficulty: 'hard' as const,
          points: 150,
          timeLimit: 45,
          type: 'design' as const,
          order: 3,
        },
      ].filter(
        (ch) =>
          ch.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          ch.description.toLowerCase().includes(searchQuery.toLowerCase()),
      );

      setSearchResults(results);
      setIsSearching(false);
    }, 1000);
  };

  // Add challenge to battle
  const handleAddChallenge = (challenge: IChallenge) => {
    // TODO: Implement API call to add challenge to battle
    setChallenges((prev) => [
      ...prev,
      { ...challenge, order: prev.length + 1 },
    ]);
    toast({
      title: 'Challenge Added',
      description: `"${challenge.title}" has been added to the battle.`,
    });
    setIsSearchDialogOpen(false);
  };

  // Remove challenge from battle
  const handleRemoveChallenge = (challengeId: string) => {
    // TODO: Implement API call to remove challenge from battle
    setChallenges((prev) => prev.filter((ch) => ch.id !== challengeId));
    toast({
      title: 'Challenge Removed',
      description: 'Challenge has been removed from the battle.',
    });
  };

  // Move challenge up in order
  const handleMoveUp = (index: number) => {
    if (index === 0) return;

    const newChallenges = [...challenges];
    const temp = newChallenges[index];
    newChallenges[index] = newChallenges[index - 1];
    newChallenges[index - 1] = temp;

    // Update order property
    newChallenges.forEach((ch, i) => {
      ch.order = i + 1;
    });

    setChallenges(newChallenges);

    // TODO: Implement API call to update challenge order
  };

  // Move challenge down in order
  const handleMoveDown = (index: number) => {
    if (index === challenges.length - 1) return;

    const newChallenges = [...challenges];
    const temp = newChallenges[index];
    newChallenges[index] = newChallenges[index + 1];
    newChallenges[index + 1] = temp;

    // Update order property
    newChallenges.forEach((ch, i) => {
      ch.order = i + 1;
    });

    setChallenges(newChallenges);

    // TODO: Implement API call to update challenge order
  };

  // Get difficulty badge
  const getDifficultyBadge = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600">
            Easy
          </Badge>
        );
      case 'medium':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600">
            Medium
          </Badge>
        );
      case 'hard':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-600">
            Hard
          </Badge>
        );
      default:
        return <Badge variant="outline">{difficulty}</Badge>;
    }
  };

  // Get type badge
  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'coding':
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-600">
            Coding
          </Badge>
        );
      case 'quiz':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-600">
            Quiz
          </Badge>
        );
      case 'design':
        return (
          <Badge variant="outline" className="bg-indigo-50 text-indigo-600">
            Design
          </Badge>
        );
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Error state
  if (error && !challenges.length) {
    return (
      <div className="rounded-lg bg-destructive/10 p-4 text-destructive">
        {error || 'No battle structure data available'}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <h2 className="text-2xl font-bold">Battle Structure</h2>

        <Dialog open={isSearchDialogOpen} onOpenChange={setIsSearchDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-1">
              <RiAddLine className="h-4 w-4" />
              Add Challenge
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Search Challenges</DialogTitle>
            </DialogHeader>
            <div className="mt-4 space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Search challenges..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={handleSearch} disabled={isSearching}>
                  {isSearching ? 'Searching...' : 'Search'}
                </Button>
              </div>

              {isSearching ? (
                <div className="space-y-2">
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                </div>
              ) : (
                <div className="max-h-[400px] space-y-2 overflow-y-auto">
                  {searchResults.length > 0 ? (
                    searchResults.map((challenge) => (
                      <div
                        key={challenge.id}
                        className="flex items-center justify-between rounded-md border p-3"
                      >
                        <div>
                          <div className="font-medium">{challenge.title}</div>
                          <div className="text-sm text-muted-foreground">
                            {challenge.description}
                          </div>
                          <div className="mt-1 flex gap-2">
                            {getDifficultyBadge(challenge.difficulty)}
                            {getTypeBadge(challenge.type)}
                            <Badge variant="outline">
                              {challenge.points} pts
                            </Badge>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handleAddChallenge(challenge)}
                          disabled={challenges.some(
                            (ch) => ch.id === challenge.id,
                          )}
                        >
                          {challenges.some((ch) => ch.id === challenge.id)
                            ? 'Added'
                            : 'Add'}
                        </Button>
                      </div>
                    ))
                  ) : searchQuery ? (
                    <div className="py-4 text-center text-muted-foreground">
                      No challenges found matching &quot;{searchQuery}&quot;
                    </div>
                  ) : (
                    <div className="py-4 text-center text-muted-foreground">
                      Search for challenges to add to this battle
                    </div>
                  )}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Challenges ({challenges.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {challenges.length > 0 ? (
            <div className="space-y-4">
              {challenges.map((challenge, index) => (
                <div
                  key={challenge.id}
                  className="flex items-center justify-between rounded-md border p-4"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex flex-col items-center">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleMoveUp(index)}
                        disabled={index === 0}
                        className="h-8 w-8"
                      >
                        <RiArrowUpSLine className="h-5 w-5" />
                      </Button>
                      <span className="font-medium">{challenge.order}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleMoveDown(index)}
                        disabled={index === challenges.length - 1}
                        className="h-8 w-8"
                      >
                        <RiArrowDownSLine className="h-5 w-5" />
                      </Button>
                    </div>
                    <div>
                      <div className="font-medium">{challenge.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {challenge.description}
                      </div>
                      <div className="mt-1 flex flex-wrap gap-2">
                        {getDifficultyBadge(challenge.difficulty)}
                        {getTypeBadge(challenge.type)}
                        <Badge variant="outline">{challenge.points} pts</Badge>
                        <Badge variant="outline">
                          {challenge.timeLimit} min
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveChallenge(challenge.id)}
                      className="h-8 w-8 text-destructive"
                    >
                      <RiDeleteBinLine className="h-5 w-5" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-muted-foreground">
              No challenges added to this battle yet. Click &quot;Add
              Challenge&quot; to get started.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
