import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { RiListCheck2, Ri<PERSON>eam<PERSON>ine, RiBarChartLine } from 'react-icons/ri';
import BattleStructure from '../BattleStructure';
import BattleParticipants from '../BattleParticipants';
import BattleAnalytics from '../BattleAnalytics';
import { IBattle } from '../../../types';

interface ITabSectionsProps {
  battle: IBattle;
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export default function TabSections({
  battle,
  activeTab,
  setActiveTab,
}: ITabSectionsProps) {
  return (
    <Tabs
      defaultValue="structure"
      value={activeTab}
      onValueChange={setActiveTab}
      className="w-full"
    >
      <TabsList className="grid grid-cols-3 md:w-[400px]">
        <TabsTrigger value="structure" className="flex items-center gap-1">
          <RiListCheck2 className="h-4 w-4" />
          Structure
        </TabsTrigger>
        <TabsTrigger value="participants" className="flex items-center gap-1">
          <RiTeamLine className="h-4 w-4" />
          Participants
        </TabsTrigger>
        <TabsTrigger value="analytics" className="flex items-center gap-1">
          <RiBarChartLine className="h-4 w-4" />
          Analytics
        </TabsTrigger>
      </TabsList>

      <TabsContent value="structure" className="mt-6">
        <BattleStructure battleId={battle.id} />
      </TabsContent>

      <TabsContent value="participants" className="mt-6">
        <BattleParticipants battleId={battle.id} battleType={battle.type} />
      </TabsContent>

      <TabsContent value="analytics" className="mt-6">
        <BattleAnalytics battleId={battle.id} />
      </TabsContent>
    </Tabs>
  );
}
