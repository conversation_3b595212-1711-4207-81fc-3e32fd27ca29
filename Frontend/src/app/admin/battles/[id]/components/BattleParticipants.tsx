/**
 * @file BattleParticipants.tsx
 * @description Component to display and manage participants in a battle
 */
'use client';

import { useState } from 'react';
import {
  RiUserLine,
  RiTeamLine,
  RiSearchLine,
  RiFilterLine,
  RiMailLine,
  RiCloseLine,
  RiAddLine,
  RiTrophyLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/components/ui/use-toast';

interface IParticipant {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  status: 'Registered' | 'Active' | 'Completed' | 'Disqualified';
  progress: number; // percentage
  score?: number;
  rank?: number;
  teamId?: string;
  teamName?: string;
  registeredAt: string;
  lastActiveAt?: string;
}

interface ITeam {
  id: string;
  name: string;
  members: IParticipant[];
  status: 'Registered' | 'Active' | 'Completed' | 'Disqualified';
  progress: number; // percentage
  score?: number;
  rank?: number;
  registeredAt: string;
  lastActiveAt?: string;
}

interface IBattleParticipantsProps {
  battleId: string;
  battleType: string; // 'Solo', 'Team', or 'Tournament'
}

function BattleParticipants({
  battleId,
  battleType,
}: IBattleParticipantsProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isAddParticipantDialogOpen, setIsAddParticipantDialogOpen] =
    useState(false);
  const [isEmailParticipantsDialogOpen, setIsEmailParticipantsDialogOpen] =
    useState(false);
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>(
    [],
  );

  // Mock data for participants
  const participants: IParticipant[] = [
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=John',
      status: 'Active',
      progress: 60,
      score: 350,
      rank: 5,
      registeredAt: '2023-05-01T10:30:00Z',
      lastActiveAt: '2023-05-20T15:45:00Z',
    },
    {
      id: '2',
      name: 'Emily Johnson',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Emily',
      status: 'Completed',
      progress: 100,
      score: 480,
      rank: 2,
      registeredAt: '2023-04-28T09:15:00Z',
      lastActiveAt: '2023-05-18T14:20:00Z',
    },
    {
      id: '3',
      name: 'Michael Chen',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Michael',
      status: 'Active',
      progress: 75,
      score: 420,
      rank: 3,
      registeredAt: '2023-05-02T11:45:00Z',
      lastActiveAt: '2023-05-21T10:30:00Z',
    },
    {
      id: '4',
      name: 'Sarah Williams',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah',
      status: 'Registered',
      progress: 0,
      registeredAt: '2023-05-10T16:20:00Z',
    },
    {
      id: '5',
      name: 'David Rodriguez',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=David',
      status: 'Disqualified',
      progress: 30,
      score: 120,
      registeredAt: '2023-04-30T08:50:00Z',
      lastActiveAt: '2023-05-15T09:10:00Z',
    },
  ];

  // Mock data for teams
  const teams: ITeam[] = [
    {
      id: '1',
      name: 'Code Wizards',
      members: [
        {
          id: '1',
          name: 'John Smith',
          email: '<EMAIL>',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=John',
          status: 'Active',
          progress: 60,
          teamId: '1',
          teamName: 'Code Wizards',
          registeredAt: '2023-05-01T10:30:00Z',
          lastActiveAt: '2023-05-20T15:45:00Z',
        },
        {
          id: '2',
          name: 'Emily Johnson',
          email: '<EMAIL>',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Emily',
          status: 'Active',
          progress: 80,
          teamId: '1',
          teamName: 'Code Wizards',
          registeredAt: '2023-04-28T09:15:00Z',
          lastActiveAt: '2023-05-18T14:20:00Z',
        },
        {
          id: '3',
          name: 'Michael Chen',
          email: '<EMAIL>',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Michael',
          status: 'Active',
          progress: 75,
          teamId: '1',
          teamName: 'Code Wizards',
          registeredAt: '2023-05-02T11:45:00Z',
          lastActiveAt: '2023-05-21T10:30:00Z',
        },
      ],
      status: 'Active',
      progress: 72, // average of members
      score: 850,
      rank: 2,
      registeredAt: '2023-04-28T09:15:00Z',
      lastActiveAt: '2023-05-21T10:30:00Z',
    },
    {
      id: '2',
      name: 'Frontend Masters',
      members: [
        {
          id: '4',
          name: 'Sarah Williams',
          email: '<EMAIL>',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah',
          status: 'Active',
          progress: 90,
          teamId: '2',
          teamName: 'Frontend Masters',
          registeredAt: '2023-05-10T16:20:00Z',
          lastActiveAt: '2023-05-22T11:30:00Z',
        },
        {
          id: '5',
          name: 'David Rodriguez',
          email: '<EMAIL>',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=David',
          status: 'Active',
          progress: 85,
          teamId: '2',
          teamName: 'Frontend Masters',
          registeredAt: '2023-04-30T08:50:00Z',
          lastActiveAt: '2023-05-22T10:15:00Z',
        },
      ],
      status: 'Active',
      progress: 88, // average of members
      score: 920,
      rank: 1,
      registeredAt: '2023-04-30T08:50:00Z',
      lastActiveAt: '2023-05-22T11:30:00Z',
    },
  ];

  // Filter participants based on search query and status filter
  const filteredParticipants = participants.filter((participant) => {
    const matchesSearch =
      participant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      participant.email.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' || participant.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Filter teams based on search query and status filter
  const filteredTeams = teams.filter((team) => {
    const matchesSearch =
      team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      team.members.some(
        (member) =>
          member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          member.email.toLowerCase().includes(searchQuery.toLowerCase()),
      );

    const matchesStatus =
      statusFilter === 'all' || team.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handle adding a participant
  const handleAddParticipant = () => {
    setIsAddParticipantDialogOpen(true);
  };

  // Handle emailing participants
  const handleEmailParticipants = () => {
    setIsEmailParticipantsDialogOpen(true);
  };

  // Handle toggling participant selection
  const handleToggleParticipantSelection = (participantId: string) => {
    setSelectedParticipants((prev) =>
      prev.includes(participantId)
        ? prev.filter((id) => id !== participantId)
        : [...prev, participantId],
    );
  };

  // Handle changing participant status
  const handleChangeParticipantStatus = (
    participantId: string,
    newStatus: string,
  ) => {
    // TODO: Implement API call to change status
    toast({
      title: 'Status Updated',
      description: `Participant status has been changed to ${newStatus}.`,
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-xl font-bold">
          {battleType === 'Team' ? 'Teams' : 'Participants'}
        </h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={handleEmailParticipants}
            disabled={selectedParticipants.length === 0}
          >
            <RiMailLine />
            Email{' '}
            {selectedParticipants.length > 0
              ? `(${selectedParticipants.length})`
              : ''}
          </Button>
          <Button
            className="flex items-center gap-1"
            onClick={handleAddParticipant}
          >
            <RiAddLine />
            Add {battleType === 'Team' ? 'Team' : 'Participant'}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div className="relative">
          <Input
            placeholder={`Search ${battleType === 'Team' ? 'teams' : 'participants'}...`}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger>
            <div className="flex items-center">
              <RiFilterLine className="mr-2" />
              <SelectValue placeholder="Status" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="Registered">Registered</SelectItem>
            <SelectItem value="Active">Active</SelectItem>
            <SelectItem value="Completed">Completed</SelectItem>
            <SelectItem value="Disqualified">Disqualified</SelectItem>
          </SelectContent>
        </Select>

        <div className="flex items-center text-sm text-muted-foreground">
          {battleType === 'Team' ? (
            <span>{filteredTeams.length} teams found</span>
          ) : (
            <span>{filteredParticipants.length} participants found</span>
          )}
          {selectedParticipants.length > 0 && (
            <span className="ml-4">{selectedParticipants.length} selected</span>
          )}
        </div>
      </div>

      {/* Participants/Teams List */}
      {battleType === 'Team' ? (
        // Teams List
        <div className="space-y-4">
          {filteredTeams.length > 0 ? (
            filteredTeams.map((team) => (
              <Card key={team.id} className="overflow-hidden">
                <div className="p-6">
                  <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={selectedParticipants.includes(team.id)}
                          onChange={() =>
                            handleToggleParticipantSelection(team.id)
                          }
                        />
                        <h3 className="text-lg font-semibold">{team.name}</h3>
                        <Badge
                          className={`${
                            team.status === 'Active'
                              ? 'bg-green-100 text-green-800'
                              : team.status === 'Completed'
                                ? 'bg-blue-100 text-blue-800'
                                : team.status === 'Registered'
                                  ? 'bg-gray-100 text-gray-800'
                                  : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {team.status}
                        </Badge>
                        {team.rank && (
                          <Badge className="flex items-center gap-1 bg-amber-100 text-amber-800">
                            <RiTrophyLine /> Rank #{team.rank}
                          </Badge>
                        )}
                      </div>

                      <div className="mt-2 text-sm text-muted-foreground">
                        {team.members.length} members • Registered on{' '}
                        {new Date(team.registeredAt).toLocaleDateString()}
                        {team.lastActiveAt &&
                          ` • Last active ${new Date(team.lastActiveAt).toLocaleDateString()}`}
                      </div>

                      <div className="mt-4">
                        <div className="mb-1 flex items-center justify-between">
                          <div className="text-sm">Progress</div>
                          <div className="text-sm font-medium">
                            {team.progress}%
                          </div>
                        </div>
                        <div className="h-2 w-full rounded-full bg-muted">
                          <div
                            className="h-2 rounded-full bg-primary"
                            style={{ width: `${team.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      {team.score !== undefined && (
                        <div className="mt-2">
                          <div className="text-sm">
                            Score:{' '}
                            <span className="font-medium">{team.score}</span>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-wrap gap-2 md:min-w-[180px] md:flex-col md:items-end">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        View Details
                      </Button>

                      <Select
                        value={team.status}
                        onValueChange={(value) =>
                          handleChangeParticipantStatus(team.id, value)
                        }
                      >
                        <SelectTrigger className="h-9 w-full md:w-auto">
                          <SelectValue placeholder="Change Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Registered">Registered</SelectItem>
                          <SelectItem value="Active">Active</SelectItem>
                          <SelectItem value="Completed">Completed</SelectItem>
                          <SelectItem value="Disqualified">
                            Disqualified
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Team Members */}
                <div className="border-t bg-muted p-4">
                  <h4 className="mb-3 flex items-center gap-1 text-sm font-medium">
                    <RiTeamLine /> Team Members
                  </h4>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {team.members.map((member) => (
                      <div
                        key={member.id}
                        className="flex items-center gap-3 rounded-md bg-background p-2"
                      >
                        <div className="bg-primary/10 h-10 w-10 overflow-hidden rounded-full">
                          {member.avatar ? (
                            <img
                              src={member.avatar}
                              alt={member.name}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="flex h-full w-full items-center justify-center">
                              <RiUserLine className="h-5 w-5 text-primary" />
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {member.email}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <Card className="p-6 text-center">
              <div className="py-8">
                <RiTeamLine className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">No teams found</h3>
                <p className="mt-2 text-muted-foreground">
                  {searchQuery || statusFilter !== 'all'
                    ? 'Try adjusting your search filters.'
                    : 'Add teams to this battle to get started.'}
                </p>
                {!searchQuery && statusFilter === 'all' && (
                  <Button
                    className="mx-auto mt-4 flex items-center gap-1"
                    onClick={handleAddParticipant}
                  >
                    <RiAddLine />
                    Add Team
                  </Button>
                )}
              </div>
            </Card>
          )}
        </div>
      ) : (
        // Individual Participants List
        <div className="space-y-4">
          {filteredParticipants.length > 0 ? (
            filteredParticipants.map((participant) => (
              <Card key={participant.id} className="p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                  <div className="flex flex-1 gap-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-3 h-4 w-4 rounded border-gray-300"
                        checked={selectedParticipants.includes(participant.id)}
                        onChange={() =>
                          handleToggleParticipantSelection(participant.id)
                        }
                      />
                      <div className="bg-primary/10 h-12 w-12 overflow-hidden rounded-full">
                        {participant.avatar ? (
                          <img
                            src={participant.avatar}
                            alt={participant.name}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center">
                            <RiUserLine className="h-6 w-6 text-primary" />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold">
                          {participant.name}
                        </h3>
                        <Badge
                          className={`${
                            participant.status === 'Active'
                              ? 'bg-green-100 text-green-800'
                              : participant.status === 'Completed'
                                ? 'bg-blue-100 text-blue-800'
                                : participant.status === 'Registered'
                                  ? 'bg-gray-100 text-gray-800'
                                  : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {participant.status}
                        </Badge>
                        {participant.rank && (
                          <Badge className="flex items-center gap-1 bg-amber-100 text-amber-800">
                            <RiTrophyLine /> Rank #{participant.rank}
                          </Badge>
                        )}
                      </div>

                      <div className="text-sm text-muted-foreground">
                        {participant.email}
                      </div>

                      <div className="mt-2 text-xs text-muted-foreground">
                        Registered on{' '}
                        {new Date(
                          participant.registeredAt,
                        ).toLocaleDateString()}
                        {participant.lastActiveAt &&
                          ` • Last active ${new Date(participant.lastActiveAt).toLocaleDateString()}`}
                      </div>

                      <div className="mt-4">
                        <div className="mb-1 flex items-center justify-between">
                          <div className="text-sm">Progress</div>
                          <div className="text-sm font-medium">
                            {participant.progress}%
                          </div>
                        </div>
                        <div className="h-2 w-full rounded-full bg-muted">
                          <div
                            className="h-2 rounded-full bg-primary"
                            style={{ width: `${participant.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      {participant.score !== undefined && (
                        <div className="mt-2">
                          <div className="text-sm">
                            Score:{' '}
                            <span className="font-medium">
                              {participant.score}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 md:min-w-[180px] md:flex-col md:items-end">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1"
                    >
                      View Details
                    </Button>

                    <Select
                      value={participant.status}
                      onValueChange={(value) =>
                        handleChangeParticipantStatus(participant.id, value)
                      }
                    >
                      <SelectTrigger className="h-9 w-full md:w-auto">
                        <SelectValue placeholder="Change Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Registered">Registered</SelectItem>
                        <SelectItem value="Active">Active</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                        <SelectItem value="Disqualified">
                          Disqualified
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <Card className="p-6 text-center">
              <div className="py-8">
                <RiUserLine className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-medium">
                  No participants found
                </h3>
                <p className="mt-2 text-muted-foreground">
                  {searchQuery || statusFilter !== 'all'
                    ? 'Try adjusting your search filters.'
                    : 'Add participants to this battle to get started.'}
                </p>
                {!searchQuery && statusFilter === 'all' && (
                  <Button
                    className="mx-auto mt-4 flex items-center gap-1"
                    onClick={handleAddParticipant}
                  >
                    <RiAddLine />
                    Add Participant
                  </Button>
                )}
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Add Participant Dialog */}
      <Dialog
        open={isAddParticipantDialogOpen}
        onOpenChange={setIsAddParticipantDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Add {battleType === 'Team' ? 'Team' : 'Participant'}
            </DialogTitle>
            <DialogDescription>
              Add {battleType === 'Team' ? 'a team' : 'a participant'} to this
              battle.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {/* TODO: Implement participant/team selection UI */}
            <p className="text-center text-muted-foreground">
              {battleType === 'Team' ? 'Team' : 'Participant'} selection
              interface would be implemented here.
            </p>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAddParticipantDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                // TODO: Implement adding selected participant/team
                setIsAddParticipantDialogOpen(false);
                toast({
                  title: `${battleType === 'Team' ? 'Team' : 'Participant'} Added`,
                  description: `The selected ${battleType === 'Team' ? 'team has' : 'participant has'} been added to the battle.`,
                });
              }}
            >
              Add Selected
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Email Participants Dialog */}
      <Dialog
        open={isEmailParticipantsDialogOpen}
        onOpenChange={setIsEmailParticipantsDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Email Participants</DialogTitle>
            <DialogDescription>
              Send an email to {selectedParticipants.length} selected{' '}
              {selectedParticipants.length === 1
                ? 'participant'
                : 'participants'}
              .
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {/* TODO: Implement email composition UI */}
            <p className="text-center text-muted-foreground">
              Email composition interface would be implemented here.
            </p>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEmailParticipantsDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                // TODO: Implement sending email
                setIsEmailParticipantsDialogOpen(false);
                toast({
                  title: 'Email Sent',
                  description: `Email has been sent to ${selectedParticipants.length} ${selectedParticipants.length === 1 ? 'participant' : 'participants'}.`,
                });
              }}
            >
              Send Email
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default BattleParticipants;
