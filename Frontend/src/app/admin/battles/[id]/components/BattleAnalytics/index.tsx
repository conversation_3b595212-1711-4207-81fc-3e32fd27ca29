'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { useAxiosGet } from '@/hooks/useAxios';
import {
  BATTLE_API,
  IBattleAnalyticsResponse,
  IBattleAnalytics,
} from '@/services/battleService';

interface IBattleAnalyticsProps {
  battleId: string;
}

export default function BattleAnalytics({ battleId }: IBattleAnalyticsProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analyticsData, setAnalyticsData] = useState<IBattleAnalytics | null>(
    null,
  );
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'all'>('all');

  // API hook
  const [fetchAnalytics] = useAxiosGet<IBattleAnalyticsResponse>(
    `${BATTLE_API.ANALYTICS}/${battleId}`,
  );

  // Fetch analytics data
  useEffect(() => {
    const getAnalyticsData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetchAnalytics({ params: { timeRange } });

        if (response.success && response.data) {
          // Type assertion to ensure correct typing
          setAnalyticsData(response.data as unknown as IBattleAnalytics);
        } else {
          setError(response.message || 'Failed to fetch analytics data');
          toast({
            title: 'Error',
            description: response.message || 'Failed to fetch analytics data',
            variant: 'destructive',
          });
        }
      } catch (err) {
        console.error('Error fetching analytics:', err);
        setError('An error occurred while fetching analytics data');
        toast({
          title: 'Error',
          description: 'An error occurred while fetching analytics data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    getAnalyticsData();
  }, [fetchAnalytics, battleId, timeRange, toast]);

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Error state
  if (error || !analyticsData) {
    return (
      <div className="rounded-lg bg-destructive/10 p-4 text-destructive">
        {error || 'No analytics data available for this battle'}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <h2 className="text-2xl font-bold">Battle Analytics</h2>
        <Tabs
          value={timeRange}
          onValueChange={(value) =>
            setTimeRange(value as 'week' | 'month' | 'all')
          }
          className="w-[400px]"
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="week">Last Week</TabsTrigger>
            <TabsTrigger value="month">Last Month</TabsTrigger>
            <TabsTrigger value="all">All Time</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Participation Stats */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Participants
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData.participationStats.totalParticipants.toLocaleString()}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Active Participants
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData.participationStats.activeParticipants.toLocaleString()}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Completion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData.participationStats.completionRate.toFixed(1)}%
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Dropout Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData.participationStats.dropoutRate.toFixed(1)}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Average Score</p>
              <p className="text-xl font-bold">
                {analyticsData.performanceStats.averageScore.toFixed(1)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Highest Score</p>
              <p className="text-xl font-bold">
                {analyticsData.performanceStats.highestScore.toFixed(1)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Lowest Score</p>
              <p className="text-xl font-bold">
                {analyticsData.performanceStats.lowestScore.toFixed(1)}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                Avg. Completion Time
              </p>
              <p className="text-xl font-bold">
                {analyticsData.performanceStats.averageCompletionTime} min
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Engagement Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Engagement Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Avg. Time Spent</p>
              <p className="text-xl font-bold">
                {analyticsData.engagementStats.averageTimeSpent} min
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Avg. Attempts</p>
              <p className="text-xl font-bold">
                {analyticsData.engagementStats.averageAttemptsPerChallenge.toFixed(
                  1,
                )}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Social Shares</p>
              <p className="text-xl font-bold">
                {analyticsData.engagementStats.socialShares.toLocaleString()}
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Comments</p>
              <p className="text-xl font-bold">
                {analyticsData.engagementStats.commentsCount.toLocaleString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Time Series Data - In a real implementation, this would use a charting library */}
      <Card>
        <CardHeader>
          <CardTitle>Participation Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80 w-full">
            {/* TODO: Implement charts using a charting library like Chart.js or Recharts */}
            <div className="flex h-full items-center justify-center">
              <p className="text-muted-foreground">
                Chart visualization will be implemented with a charting library
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
