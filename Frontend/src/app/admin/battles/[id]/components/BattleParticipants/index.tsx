'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { useAxiosGet, useAxiosPut, useAxiosPost } from '@/hooks/useAxios';
import {
  BATTLE_API,
  IParticipantStatusUpdateParams,
} from '@/services/battleService';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import {
  RiMoreLine,
  RiMailSendLine,
  RiUserAddLine,
  RiUserRemoveLine,
  RiCloseLine,
} from 'react-icons/ri';

interface IBattleParticipantsProps {
  battleId: string;
  battleType?: string;
}

interface IParticipant {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  email: string;
  joinedAt: string;
  status: 'active' | 'completed' | 'dropped';
  progress: number;
  score: number;
  selected?: boolean;
  teamId?: string;
  teamName?: string;
}

interface IParticipantsResponse {
  participants: IParticipant[];
  total: number;
}

// Status dropdown component
function StatusDropdown({
  participant,
  onStatusChange,
  isUpdating,
}: {
  participant: IParticipant;
  onStatusChange: (
    id: string,
    status: 'active' | 'completed' | 'dropped',
  ) => void;
  isUpdating: boolean;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          disabled={isUpdating}
        >
          {isUpdating ? (
            <Skeleton className="h-4 w-4 rounded-full" />
          ) : (
            <RiMoreLine className="h-4 w-4" />
          )}
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => onStatusChange(participant.id, 'active')}
          disabled={participant.status === 'active' || isUpdating}
        >
          Set as Active
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onStatusChange(participant.id, 'completed')}
          disabled={participant.status === 'completed' || isUpdating}
        >
          Mark as Completed
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onStatusChange(participant.id, 'dropped')}
          disabled={participant.status === 'dropped' || isUpdating}
          className="text-destructive"
        >
          Mark as Dropped
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default function BattleParticipants({
  battleId,
  battleType,
}: IBattleParticipantsProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [participants, setParticipants] = useState<IParticipant[]>([]);
  const [total, setTotal] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [selectedParticipants, setSelectedParticipants] = useState<string[]>(
    [],
  );
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [emailSubject, setEmailSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [isSendingEmail, setIsSendingEmail] = useState(false);

  // API hooks
  const [fetchParticipants] = useAxiosGet<IParticipantsResponse>(
    `${BATTLE_API.PARTICIPANTS}/${battleId}`,
  );
  const [updateParticipantStatus] = useAxiosPut<{
    success: boolean;
    message?: string;
  }>(BATTLE_API.UPDATE_PARTICIPANT_STATUS);
  const [sendEmail] = useAxiosPost<{ success: boolean; message?: string }>(
    '/api/admin/email/send',
  );

  // Fetch participants data
  useEffect(() => {
    const getParticipantsData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetchParticipants({
          params: {
            page,
            limit,
            search: searchQuery || undefined,
            type: battleType, // Filter by battle type if provided
          },
        });

        if (response.success && response.data) {
          setParticipants(response.data.participants || []);
          setTotal(response.data.total || 0);
        } else {
          setError(response.message || 'Failed to fetch participants data');
          toast({
            title: 'Error',
            description:
              response.message || 'Failed to fetch participants data',
            variant: 'destructive',
          });
        }
      } catch (err) {
        console.error('Error fetching participants:', err);
        setError('An error occurred while fetching participants data');
        toast({
          title: 'Error',
          description: 'An error occurred while fetching participants data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    getParticipantsData();
  }, [fetchParticipants, battleId, page, limit, searchQuery, toast]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1); // Reset to first page on new search
  };

  // Handle status change
  const handleStatusChange = async (
    participantId: string,
    newStatus: 'active' | 'completed' | 'dropped',
  ) => {
    setUpdatingStatus(participantId);

    try {
      const response = await updateParticipantStatus({
        participantId,
        status: newStatus,
      } as IParticipantStatusUpdateParams);

      if (response.success) {
        // Update the participant status in the local state
        setParticipants((prevParticipants) =>
          prevParticipants.map((p) =>
            p.id === participantId ? { ...p, status: newStatus } : p,
          ),
        );

        toast({
          title: 'Status Updated',
          description: `Participant status has been updated to ${newStatus}.`,
        });
      } else {
        toast({
          title: 'Error',
          description:
            response.message || 'Failed to update participant status.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating participant status:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while updating participant status.',
        variant: 'destructive',
      });
    } finally {
      setUpdatingStatus(null);
    }
  };

  // Handle bulk status change
  const handleBulkStatusChange = async (
    newStatus: 'active' | 'completed' | 'dropped',
  ) => {
    if (selectedParticipants.length === 0) return;

    setIsLoading(true);

    try {
      // Create an array of promises for each status update
      const updatePromises = selectedParticipants.map((participantId) =>
        updateParticipantStatus({
          participantId,
          status: newStatus,
        } as IParticipantStatusUpdateParams),
      );

      // Wait for all updates to complete
      const results = await Promise.all(updatePromises);

      // Check if all updates were successful
      const allSuccessful = results.every((result) => result.success);

      if (allSuccessful) {
        // Update all selected participants' statuses in the local state
        setParticipants((prevParticipants) =>
          prevParticipants.map((p) =>
            selectedParticipants.includes(p.id)
              ? { ...p, status: newStatus }
              : p,
          ),
        );

        toast({
          title: 'Bulk Status Update',
          description: `${selectedParticipants.length} participants updated to ${newStatus}.`,
        });
      } else {
        toast({
          title: 'Error',
          description: 'Some participants could not be updated.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating participant statuses:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while updating participant statuses.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sending email to selected participants
  const handleSendEmail = async () => {
    if (selectedParticipants.length === 0 || !emailSubject || !emailContent)
      return;

    setIsSendingEmail(true);

    try {
      // Get email addresses of selected participants
      const recipients = participants
        .filter((p) => selectedParticipants.includes(p.id))
        .map((p) => p.email);

      const response = await sendEmail({
        recipients,
        subject: emailSubject,
        content: emailContent,
        battleId,
      });

      if (response.success) {
        toast({
          title: 'Email Sent',
          description: `Email has been sent to ${recipients.length} participants.`,
        });

        // Reset email form
        setEmailSubject('');
        setEmailContent('');
        setShowEmailDialog(false);
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to send email.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while sending email.',
        variant: 'destructive',
      });
    } finally {
      setIsSendingEmail(false);
    }
  };

  // Handle participant selection
  const handleSelectParticipant = (
    participantId: string,
    isSelected: boolean,
  ) => {
    setSelectedParticipants((prev) => {
      if (isSelected) {
        return [...prev, participantId];
      } else {
        return prev.filter((id) => id !== participantId);
      }
    });
  };

  // Handle select all participants
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedParticipants(participants.map((p) => p.id));
    } else {
      setSelectedParticipants([]);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600">
            Active
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600">
            Completed
          </Badge>
        );
      case 'dropped':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-600">
            Dropped
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Loading state
  if (isLoading && page === 1) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-64 w-full" />
        <div className="flex justify-between">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-48" />
        </div>
      </div>
    );
  }

  // Error state
  if (error && !participants.length) {
    return (
      <div className="rounded-lg bg-destructive/10 p-4 text-destructive">
        {error || 'No participants data available for this battle'}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Email Dialog */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Send Email to Participants</DialogTitle>
            <DialogDescription>
              Send an email to {selectedParticipants.length} selected
              participants.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email-subject" className="text-right">
                Subject
              </Label>
              <Input
                id="email-subject"
                placeholder="Email subject"
                className="col-span-3"
                value={emailSubject}
                onChange={(e) => setEmailSubject(e.target.value)}
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="email-content" className="text-right">
                Message
              </Label>
              <Textarea
                id="email-content"
                placeholder="Enter your message here..."
                className="col-span-3 min-h-[200px]"
                value={emailContent}
                onChange={(e) => setEmailContent(e.target.value)}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">Recipients</Label>
              <div className="col-span-3 max-h-[100px] overflow-y-auto rounded-md border border-border p-2">
                {selectedParticipants.map((id) => {
                  const participant = participants.find((p) => p.id === id);
                  return (
                    <div
                      key={id}
                      className="mb-1 flex items-center justify-between rounded-md bg-muted px-2 py-1"
                    >
                      <span className="text-sm">
                        {participant?.userName} ({participant?.email})
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => handleSelectParticipant(id, false)}
                      >
                        <RiCloseLine className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              onClick={handleSendEmail}
              disabled={!emailSubject || !emailContent || isSendingEmail}
            >
              {isSendingEmail ? 'Sending...' : 'Send Email'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <h2 className="text-2xl font-bold">Battle Participants</h2>

        {/* Search form */}
        <div className="flex items-center gap-2">
          <form
            onSubmit={handleSearch}
            className="flex w-full max-w-sm items-center space-x-2"
          >
            <Input
              type="search"
              placeholder="Search participants..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-xs"
            />
            <Button type="submit" variant="default">
              Search
            </Button>
          </form>

          {/* Bulk actions */}
          {selectedParticipants.length > 0 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowEmailDialog(true)}
                className="flex items-center gap-1"
              >
                <RiMailSendLine className="h-4 w-4" />
                Email Selected
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkStatusChange('active')}
                className="flex items-center gap-1"
              >
                <RiUserAddLine className="h-4 w-4" />
                Set Active
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkStatusChange('dropped')}
                className="flex items-center gap-1 text-destructive"
              >
                <RiUserRemoveLine className="h-4 w-4" />
                Mark Dropped
              </Button>
            </div>
          )}
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Participants ({total})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={
                      participants.length > 0 &&
                      selectedParticipants.length === participants.length
                    }
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all participants"
                  />
                </TableHead>
                <TableHead>User</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Score</TableHead>
                <TableHead>Joined</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {participants.length > 0 ? (
                participants.map((participant) => (
                  <TableRow
                    key={participant.id}
                    className={
                      selectedParticipants.includes(participant.id)
                        ? 'bg-muted/50'
                        : ''
                    }
                  >
                    <TableCell>
                      <Checkbox
                        checked={selectedParticipants.includes(participant.id)}
                        onCheckedChange={(checked) => {
                          handleSelectParticipant(participant.id, !!checked);
                        }}
                        aria-label={`Select ${participant.userName}`}
                      />
                    </TableCell>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src={participant.userAvatar}
                            alt={participant.userName}
                          />
                          <AvatarFallback>
                            {participant.userName.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div>{participant.userName}</div>
                          <div className="text-xs text-muted-foreground">
                            {participant.email}
                          </div>
                          {participant.teamName && (
                            <div className="text-xs text-muted-foreground">
                              Team: {participant.teamName}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(participant.status)}
                        <StatusDropdown
                          participant={participant}
                          onStatusChange={handleStatusChange}
                          isUpdating={updatingStatus === participant.id}
                        />
                      </div>
                    </TableCell>
                    <TableCell>{participant.progress}%</TableCell>
                    <TableCell>{participant.score}</TableCell>
                    <TableCell>
                      {new Date(participant.joinedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>{participant.progress}%</TableCell>
                    <TableCell>{participant.score}</TableCell>
                    <TableCell>
                      {new Date(participant.joinedAt).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    No participants found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          {/* Pagination */}
          {total > limit && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {(page - 1) * limit + 1}-{Math.min(page * limit, total)}{' '}
                of {total} participants
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage((p) => Math.max(1, p - 1))}
                  disabled={page === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage((p) => p + 1)}
                  disabled={page * limit >= total}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
