/**
 * @file BattleBasicInfo.tsx
 * @description Component for editing basic information of a battle
 */
'use client';

import { useState, useEffect } from 'react';
import { RiAddLine, RiCloseLine, RiCalendarLine } from 'react-icons/ri';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';

interface IBattleBasicInfoProps {
  battle: {
    title: string;
    description: string;
    type: string;
    difficulty: string;
    startDate: string;
    endDate: string;
    maxParticipants: number;
    prizePool?: string;
    tags: string[];
  };
  updateBattle: (data: {
    title?: string;
    description?: string;
    type?: string;
    difficulty?: string;
    startDate?: string;
    endDate?: string;
    maxParticipants?: number;
    prizePool?: string;
    tags?: string[];
  }) => void;
}

function BattleBasicInfo({ battle, updateBattle }: IBattleBasicInfoProps) {
  const [title, setTitle] = useState(battle.title);
  const [description, setDescription] = useState(battle.description);
  const [type, setType] = useState(battle.type);
  const [difficulty, setDifficulty] = useState(battle.difficulty);
  const [startDate, setStartDate] = useState<Date>(new Date(battle.startDate));
  const [endDate, setEndDate] = useState<Date>(new Date(battle.endDate));
  const [maxParticipants, setMaxParticipants] = useState(
    battle.maxParticipants,
  );
  const [prizePool, setPrizePool] = useState(battle.prizePool || '');
  const [tags, setTags] = useState<string[]>(battle.tags);
  const [newTag, setNewTag] = useState('');

  // Update parent component when form values change
  useEffect(() => {
    updateBattle({
      title,
      description,
      type,
      difficulty,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      maxParticipants,
      prizePool: prizePool || undefined,
      tags,
    });
  }, [
    title,
    description,
    type,
    difficulty,
    startDate,
    endDate,
    maxParticipants,
    prizePool,
    tags,
    updateBattle,
  ]);

  // Handle adding a new tag
  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim().toLowerCase())) {
      setTags([...tags, newTag.trim().toLowerCase()]);
      setNewTag('');
    }
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  // Handle key press in tag input
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <Card>
      <CardContent className="space-y-6 p-6">
        {/* Title */}
        <div className="space-y-2">
          <Label htmlFor="title">Battle Title</Label>
          <Input
            id="title"
            placeholder="Enter battle title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="Enter a brief description of the battle"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={4}
          />
        </div>

        {/* Type */}
        <div className="space-y-2">
          <Label htmlFor="type">Battle Type</Label>
          <Select value={type} onValueChange={setType}>
            <SelectTrigger id="type">
              <SelectValue placeholder="Select battle type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Solo">Solo</SelectItem>
              <SelectItem value="Team">Team</SelectItem>
              <SelectItem value="Tournament">Tournament</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Difficulty */}
        <div className="space-y-2">
          <Label htmlFor="difficulty">Difficulty</Label>
          <Select value={difficulty} onValueChange={setDifficulty}>
            <SelectTrigger id="difficulty">
              <SelectValue placeholder="Select difficulty" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Easy">Easy</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="Hard">Hard</SelectItem>
              <SelectItem value="Expert">Expert</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Date Range */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="start-date">Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="start-date"
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <RiCalendarLine className="mr-2 h-4 w-4" />
                  {format(startDate, 'PPP')}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={(date) => date && setStartDate(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label htmlFor="end-date">End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="end-date"
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <RiCalendarLine className="mr-2 h-4 w-4" />
                  {format(endDate, 'PPP')}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={(date) => date && setEndDate(date)}
                  initialFocus
                  disabled={(date) => date < startDate}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Max Participants */}
        <div className="space-y-2">
          <Label htmlFor="max-participants">Maximum Participants</Label>
          <Input
            id="max-participants"
            type="number"
            min="1"
            value={maxParticipants}
            onChange={(e) =>
              setMaxParticipants(parseInt(e.target.value) || 100)
            }
          />
        </div>

        {/* Prize Pool */}
        <div className="space-y-2">
          <Label htmlFor="prize-pool">Prize Pool (Optional)</Label>
          <Input
            id="prize-pool"
            placeholder="e.g. $500"
            value={prizePool}
            onChange={(e) => setPrizePool(e.target.value)}
          />
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <Label htmlFor="tags">Tags</Label>
          <div className="flex gap-2">
            <Input
              id="tags"
              placeholder="Add a tag"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={handleTagKeyPress}
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleAddTag}
              className="flex items-center gap-1"
            >
              <RiAddLine />
              Add
            </Button>
          </div>

          {tags.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-2">
              {tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="flex items-center gap-1 px-2 py-1"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <RiCloseLine className="h-4 w-4" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default BattleBasicInfo;
