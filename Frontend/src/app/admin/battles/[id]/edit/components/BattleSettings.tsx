/**
 * @file BattleSettings.tsx
 * @description Component for configuring additional settings for a battle
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiAddLine,
  RiCloseLine,
  RiCalendarLine,
  RiStarLine,
  RiStarFill,
  RiTrophyLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';

interface IPrize {
  rank: string;
  reward: string;
  description?: string;
}

interface IBattleSettingsProps {
  battle: {
    rules?: string;
    eligibility?: string;
    registrationDeadline?: string;
    prizes?: IPrize[];
    status: string;
    isFeatured: boolean;
  };
  updateBattle: (data: {
    rules?: string;
    eligibility?: string;
    registrationDeadline?: string;
    prizes?: IPrize[];
    status?: string;
    isFeatured?: boolean;
  }) => void;
}

function BattleSettings({ battle, updateBattle }: IBattleSettingsProps) {
  const [rules, setRules] = useState(battle.rules || '');
  const [eligibility, setEligibility] = useState(battle.eligibility || '');
  const [registrationDeadline, setRegistrationDeadline] = useState<
    Date | undefined
  >(
    battle.registrationDeadline
      ? new Date(battle.registrationDeadline)
      : undefined,
  );
  const [prizes, setPrizes] = useState<IPrize[]>(battle.prizes || []);
  const [status, setStatus] = useState(battle.status);
  const [isFeatured, setIsFeatured] = useState(battle.isFeatured);

  // For new prize form
  const [newPrizeRank, setNewPrizeRank] = useState('');
  const [newPrizeReward, setNewPrizeReward] = useState('');
  const [newPrizeDescription, setNewPrizeDescription] = useState('');

  // Update parent component when form values change
  useEffect(() => {
    updateBattle({
      rules: rules || undefined,
      eligibility: eligibility || undefined,
      registrationDeadline: registrationDeadline?.toISOString(),
      prizes: prizes.length > 0 ? prizes : undefined,
      status,
      isFeatured,
    });
  }, [
    rules,
    eligibility,
    registrationDeadline,
    prizes,
    status,
    isFeatured,
    updateBattle,
  ]);

  // Handle adding a new prize
  const handleAddPrize = () => {
    if (!newPrizeRank.trim() || !newPrizeReward.trim()) return;

    const newPrize: IPrize = {
      rank: newPrizeRank.trim(),
      reward: newPrizeReward.trim(),
      description: newPrizeDescription.trim() || undefined,
    };

    setPrizes([...prizes, newPrize]);
    setNewPrizeRank('');
    setNewPrizeReward('');
    setNewPrizeDescription('');
  };

  // Handle removing a prize
  const handleRemovePrize = (index: number) => {
    setPrizes(prizes.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      {/* Rules and Eligibility */}
      <Card>
        <CardHeader>
          <CardTitle>Rules and Eligibility</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Rules */}
          <div className="space-y-2">
            <Label htmlFor="rules">Rules</Label>
            <Textarea
              id="rules"
              placeholder="Enter the rules for this battle"
              value={rules}
              onChange={(e) => setRules(e.target.value)}
              rows={4}
            />
          </div>

          {/* Eligibility */}
          <div className="space-y-2">
            <Label htmlFor="eligibility">Eligibility</Label>
            <Textarea
              id="eligibility"
              placeholder="Enter eligibility requirements"
              value={eligibility}
              onChange={(e) => setEligibility(e.target.value)}
              rows={3}
            />
          </div>

          {/* Registration Deadline */}
          <div className="space-y-2">
            <Label htmlFor="registration-deadline">
              Registration Deadline (Optional)
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="registration-deadline"
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <RiCalendarLine className="mr-2 h-4 w-4" />
                  {registrationDeadline
                    ? format(registrationDeadline, 'PPP')
                    : 'Select a date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={registrationDeadline}
                  onSelect={setRegistrationDeadline}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </CardContent>
      </Card>

      {/* Prizes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-1">
            <RiTrophyLine className="text-amber-500" />
            Prizes
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Existing Prizes */}
          {prizes.length > 0 && (
            <div className="space-y-4">
              {prizes.map((prize, index) => (
                <div
                  key={index}
                  className="flex items-start gap-3 rounded-md border p-3"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{prize.rank}</h3>
                      <div className="font-bold text-primary">
                        {prize.reward}
                      </div>
                    </div>
                    {prize.description && (
                      <p className="mt-1 text-sm text-muted-foreground">
                        {prize.description}
                      </p>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-destructive hover:bg-destructive/10 hover:text-destructive"
                    onClick={() => handleRemovePrize(index)}
                  >
                    <RiCloseLine className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Add New Prize */}
          <div className="space-y-4 border-t pt-4">
            <h3 className="text-sm font-medium">Add New Prize</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="prize-rank">Rank/Position</Label>
                <Input
                  id="prize-rank"
                  placeholder="e.g. 1st Place, Runner-up"
                  value={newPrizeRank}
                  onChange={(e) => setNewPrizeRank(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="prize-reward">Reward</Label>
                <Input
                  id="prize-reward"
                  placeholder="e.g. $500, Gift Card"
                  value={newPrizeReward}
                  onChange={(e) => setNewPrizeReward(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="prize-description">Description (Optional)</Label>
              <Input
                id="prize-description"
                placeholder="Additional details about the prize"
                value={newPrizeDescription}
                onChange={(e) => setNewPrizeDescription(e.target.value)}
              />
            </div>

            <Button
              type="button"
              variant="outline"
              onClick={handleAddPrize}
              className="flex items-center gap-1"
              disabled={!newPrizeRank.trim() || !newPrizeReward.trim()}
            >
              <RiAddLine />
              Add Prize
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Status and Visibility */}
      <Card>
        <CardHeader>
          <CardTitle>Status and Visibility</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Battle Status</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Scheduled">Scheduled</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
                <SelectItem value="Cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Featured */}
          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={isFeatured}
              onCheckedChange={setIsFeatured}
            />
            <Label htmlFor="featured" className="flex items-center gap-1">
              {isFeatured ? (
                <RiStarFill className="text-amber-500" />
              ) : (
                <RiStarLine />
              )}
              Feature this battle
            </Label>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default BattleSettings;
