/**
 * @file BattleScheduling.tsx
 * @description Component for configuring battle scheduling settings
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiCalendarLine,
  RiTimeLine,
  RiUserLine,
  RiAlarmLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';

interface IBattleSchedulingProps {
  battle: {
    startDate?: string;
    endDate?: string;
    maxParticipants?: number;
    autoStart: boolean;
    reminderEnabled: boolean;
    reminderHours?: number;
  };
  updateBattle: (data: {
    startDate?: string;
    endDate?: string;
    maxParticipants?: number;
    autoStart?: boolean;
    reminderEnabled?: boolean;
    reminderHours?: number;
  }) => void;
}

function BattleScheduling({ battle, updateBattle }: IBattleSchedulingProps) {
  const [startDate, setStartDate] = useState<Date | undefined>(
    battle.startDate ? new Date(battle.startDate) : undefined,
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    battle.endDate ? new Date(battle.endDate) : undefined,
  );
  const [maxParticipants, setMaxParticipants] = useState<string>(
    battle.maxParticipants?.toString() || '',
  );
  const [autoStart, setAutoStart] = useState(battle.autoStart);
  const [reminderEnabled, setReminderEnabled] = useState(
    battle.reminderEnabled,
  );
  const [reminderHours, setReminderHours] = useState<string>(
    battle.reminderHours?.toString() || '24',
  );

  // Update parent component when form values change
  useEffect(() => {
    updateBattle({
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString(),
      maxParticipants: maxParticipants
        ? parseInt(maxParticipants, 10)
        : undefined,
      autoStart,
      reminderEnabled,
      reminderHours: reminderHours ? parseInt(reminderHours, 10) : undefined,
    });
  }, [
    startDate,
    endDate,
    maxParticipants,
    autoStart,
    reminderEnabled,
    reminderHours,
    updateBattle,
  ]);

  // Handle number input changes
  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    setter: React.Dispatch<React.SetStateAction<string>>,
  ) => {
    const value = e.target.value;
    if (value === '' || /^\d+$/.test(value)) {
      setter(value);
    }
  };

  return (
    <div className="space-y-6">
      {/* Battle Schedule */}
      <Card>
        <CardHeader>
          <CardTitle>Battle Schedule</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="start-date">Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="start-date"
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <RiCalendarLine className="mr-2 h-4 w-4" />
                  {startDate ? format(startDate, 'PPP') : 'Select a start date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={setStartDate}
                  initialFocus
                  disabled={(date) => {
                    // Disable dates in the past
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    return date < today;
                  }}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* End Date */}
          <div className="space-y-2">
            <Label htmlFor="end-date">End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="end-date"
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                  disabled={!startDate}
                >
                  <RiCalendarLine className="mr-2 h-4 w-4" />
                  {endDate ? format(endDate, 'PPP') : 'Select an end date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={setEndDate}
                  initialFocus
                  disabled={(date) => {
                    // Disable dates before the start date
                    if (!startDate) return true;
                    return date < startDate;
                  }}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Max Participants */}
          <div className="space-y-2">
            <Label htmlFor="max-participants">
              Maximum Participants (Optional)
            </Label>
            <div className="flex items-center">
              <RiUserLine className="mr-2 h-4 w-4 text-muted-foreground" />
              <Input
                id="max-participants"
                type="text"
                placeholder="Unlimited"
                value={maxParticipants}
                onChange={(e) => handleNumberChange(e, setMaxParticipants)}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Leave empty for unlimited participants
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Automation Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Automation Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Auto Start */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="auto-start">Auto Start Battle</Label>
              <p className="text-sm text-muted-foreground">
                Automatically start the battle at the scheduled start time
              </p>
            </div>
            <Switch
              id="auto-start"
              checked={autoStart}
              onCheckedChange={setAutoStart}
            />
          </div>

          {/* Reminder Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="reminder-enabled">Send Reminders</Label>
                <p className="text-sm text-muted-foreground">
                  Send reminder emails to participants before the battle starts
                </p>
              </div>
              <Switch
                id="reminder-enabled"
                checked={reminderEnabled}
                onCheckedChange={setReminderEnabled}
              />
            </div>

            {reminderEnabled && (
              <div className="border-l-2 border-border pl-6">
                <div className="space-y-2">
                  <Label htmlFor="reminder-hours">Hours Before Start</Label>
                  <div className="flex items-center">
                    <RiAlarmLine className="mr-2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="reminder-hours"
                      type="text"
                      placeholder="24"
                      value={reminderHours}
                      onChange={(e) => handleNumberChange(e, setReminderHours)}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Battle Duration */}
      <Card>
        <CardHeader>
          <CardTitle>Battle Duration</CardTitle>
        </CardHeader>
        <CardContent>
          {startDate && endDate ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <RiTimeLine className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Duration</p>
                  <p className="text-sm text-muted-foreground">
                    {Math.ceil(
                      (endDate.getTime() - startDate.getTime()) /
                        (1000 * 60 * 60 * 24),
                    )}{' '}
                    days
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between text-sm">
                <div>
                  <p className="font-medium">Start</p>
                  <p className="text-muted-foreground">
                    {format(startDate, 'PPP')}
                  </p>
                </div>
                <div>
                  <p className="text-right font-medium">End</p>
                  <p className="text-muted-foreground">
                    {format(endDate, 'PPP')}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <p className="py-2 text-center text-muted-foreground">
              Set start and end dates to see battle duration
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default BattleScheduling;
