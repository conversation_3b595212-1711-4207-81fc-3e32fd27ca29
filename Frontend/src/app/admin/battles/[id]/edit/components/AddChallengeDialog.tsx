/**
 * @file AddChallengeDialog.tsx
 * @description Dialog component for adding challenges to a battle
 */
'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RiAddLine, RiSearchLine } from 'react-icons/ri';

interface IChallenge {
  id: string;
  title: string;
  difficulty: string;
  type: string;
  points: number;
  timeLimit?: number;
  // Add other challenge properties as needed
}

interface IAddChallengeDialogProps {
  onAddChallenges: (challenges: IChallenge[]) => void;
  existingChallengeIds?: string[];
  trigger?: React.ReactNode;
}

function AddChallengeDialog({
  onAddChallenges,
  existingChallengeIds = [],
  trigger,
}: IAddChallengeDialogProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedChallenges, setSelectedChallenges] = useState<IChallenge[]>(
    [],
  );
  const [availableChallenges, setAvailableChallenges] = useState<IChallenge[]>(
    [],
  );
  const [filteredChallenges, setFilteredChallenges] = useState<IChallenge[]>(
    [],
  );
  const [isLoading, setIsLoading] = useState(false);

  // TODO: Replace with actual API call to fetch challenges
  useEffect(() => {
    const fetchChallenges = async () => {
      setIsLoading(true);
      try {
        // Mock data for now
        const mockChallenges: IChallenge[] = [
          {
            id: '1',
            title: 'Binary Search Implementation',
            difficulty: 'Medium',
            type: 'Algorithm',
            points: 50,
          },
          {
            id: '2',
            title: 'Linked List Reversal',
            difficulty: 'Medium',
            type: 'Data Structure',
            points: 50,
          },
          {
            id: '3',
            title: 'Dynamic Programming: Fibonacci',
            difficulty: 'Easy',
            type: 'Algorithm',
            points: 30,
          },
          {
            id: '4',
            title: 'Graph Traversal: BFS',
            difficulty: 'Hard',
            type: 'Algorithm',
            points: 80,
          },
          {
            id: '5',
            title: 'Hash Table Implementation',
            difficulty: 'Medium',
            type: 'Data Structure',
            points: 60,
          },
          {
            id: '6',
            title: 'Sorting Algorithm Comparison',
            difficulty: 'Hard',
            type: 'Algorithm',
            points: 70,
          },
          {
            id: '7',
            title: 'Tree Traversal Methods',
            difficulty: 'Medium',
            type: 'Data Structure',
            points: 50,
          },
          {
            id: '8',
            title: 'Recursion Problems',
            difficulty: 'Easy',
            type: 'Algorithm',
            points: 40,
          },
          {
            id: '9',
            title: 'Stack Implementation',
            difficulty: 'Easy',
            type: 'Data Structure',
            points: 30,
          },
          {
            id: '10',
            title: 'Queue Implementation',
            difficulty: 'Easy',
            type: 'Data Structure',
            points: 30,
          },
        ];

        // Filter out challenges that are already in the battle
        const filteredMockChallenges = mockChallenges.filter(
          (challenge) => !existingChallengeIds.includes(challenge.id),
        );

        setAvailableChallenges(filteredMockChallenges);
        setFilteredChallenges(filteredMockChallenges);
      } catch (error) {
        console.error('Error fetching challenges:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (open) {
      fetchChallenges();
    }
  }, [open, existingChallengeIds]);

  // Apply filters when search query, difficulty, or type changes
  useEffect(() => {
    let filtered = [...availableChallenges];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter((challenge) =>
        challenge.title.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    // Apply difficulty filter
    if (selectedDifficulty) {
      filtered = filtered.filter(
        (challenge) => challenge.difficulty === selectedDifficulty,
      );
    }

    // Apply type filter
    if (selectedType) {
      filtered = filtered.filter(
        (challenge) => challenge.type === selectedType,
      );
    }

    setFilteredChallenges(filtered);
  }, [searchQuery, selectedDifficulty, selectedType, availableChallenges]);

  const handleSelectChallenge = (challenge: IChallenge) => {
    const isSelected = selectedChallenges.some((c) => c.id === challenge.id);

    if (isSelected) {
      setSelectedChallenges(
        selectedChallenges.filter((c) => c.id !== challenge.id),
      );
    } else {
      setSelectedChallenges([...selectedChallenges, challenge]);
    }
  };

  const handleAddChallenges = () => {
    onAddChallenges(selectedChallenges);
    setSelectedChallenges([]);
    setOpen(false);
  };

  const resetFilters = () => {
    setSearchQuery('');
    setSelectedDifficulty('');
    setSelectedType('');
  };

  const defaultTrigger = (
    <Button variant="outline" className="flex w-full items-center gap-1">
      <RiAddLine className="h-4 w-4" />
      Add Challenges
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Challenges to Battle</DialogTitle>
          <DialogDescription>
            Select challenges to add to this battle. You can filter by
            difficulty, type, or search by title.
          </DialogDescription>
        </DialogHeader>

        {/* Filters */}
        <div className="grid gap-4 py-4">
          <div className="relative">
            <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search challenges..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="difficulty-filter">Difficulty</Label>
              <Select
                value={selectedDifficulty}
                onValueChange={setSelectedDifficulty}
              >
                <SelectTrigger id="difficulty-filter">
                  <SelectValue placeholder="All difficulties" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All difficulties</SelectItem>
                  <SelectItem value="Easy">Easy</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Hard">Hard</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type-filter">Type</Label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger id="type-filter">
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All types</SelectItem>
                  <SelectItem value="Algorithm">Algorithm</SelectItem>
                  <SelectItem value="Data Structure">Data Structure</SelectItem>
                  <SelectItem value="System Design">System Design</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Challenge List */}
        <div className="max-h-[300px] divide-y overflow-y-auto rounded-md border">
          {isLoading ? (
            <div className="p-4 text-center text-muted-foreground">
              Loading challenges...
            </div>
          ) : filteredChallenges.length > 0 ? (
            filteredChallenges.map((challenge) => {
              const isSelected = selectedChallenges.some(
                (c) => c.id === challenge.id,
              );

              return (
                <div
                  key={challenge.id}
                  className={`flex cursor-pointer items-center p-3 hover:bg-muted ${isSelected ? 'bg-muted/50' : ''}`}
                  onClick={() => handleSelectChallenge(challenge)}
                >
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={() => handleSelectChallenge(challenge)}
                    className="mr-3"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium">{challenge.title}</h4>
                    <div className="mt-1 flex items-center gap-2 text-sm text-muted-foreground">
                      <span
                        className={`rounded-full px-2 py-0.5 text-xs ${challenge.difficulty === 'Easy' ? 'bg-green-100 text-green-800' : challenge.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}
                      >
                        {challenge.difficulty}
                      </span>
                      <span>{challenge.type}</span>
                      <span>{challenge.points} points</span>
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="p-4 text-center text-muted-foreground">
              No challenges found. Try adjusting your filters.
            </div>
          )}
        </div>

        <div className="mt-2 flex items-center justify-between">
          <Button variant="ghost" size="sm" onClick={resetFilters}>
            Reset Filters
          </Button>
          <div className="text-sm text-muted-foreground">
            Selected: {selectedChallenges.length} challenge
            {selectedChallenges.length !== 1 ? 's' : ''}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleAddChallenges}
            disabled={selectedChallenges.length === 0}
          >
            Add Selected Challenges
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default AddChallengeDialog;
