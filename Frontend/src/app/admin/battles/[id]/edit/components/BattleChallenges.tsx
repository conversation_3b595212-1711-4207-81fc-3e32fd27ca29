/**
 * @file BattleChallenges.tsx
 * @description Component for managing the challenges in a battle
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiAddLine,
  RiDeleteBinLine,
  RiArrowUpDownLine,
  RiSearchLine,
  RiFilterLine,
  RiCheckboxCircleLine,
  RiCloseLine,
  RiLoader4Line,
  RiErrorWarningLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from '@/components/ui/use-toast';
import {
  fetchBattleChallenges,
  fetchAvailableChallenges,
  addChallengesToBattle,
  removeChallengeFromBattle,
  updateChallengeOrder,
  updateChallengeRequired,
  IChallenge,
} from '@/services/battleService';
import { extractErrorMessage, safelyExtractData } from '@/utils/apiUtils';

// Using IChallenge from battleService.ts

interface IBattleChallengesProps {
  battle: {
    id: string;
    challengeCount: number;
  };
  updateBattle: (data: { challengeCount: number }) => void;
}

function BattleChallenges({ battle, updateBattle }: IBattleChallengesProps) {
  // State for battle challenges
  const [challenges, setChallenges] = useState<IChallenge[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for challenge dialog
  const [isAddChallengeDialogOpen, setIsAddChallengeDialogOpen] =
    useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [selectedChallenges, setSelectedChallenges] = useState<string[]>([]);

  // State for available challenges
  const [availableChallenges, setAvailableChallenges] = useState<IChallenge[]>(
    [],
  );
  const [isLoadingAvailableChallenges, setIsLoadingAvailableChallenges] =
    useState(false);
  const [availableChallengesError, setAvailableChallengesError] = useState<
    string | null
  >(null);

  // State for API operations
  const [isAddingChallenges, setIsAddingChallenges] = useState(false);
  const [removingChallengeId, setRemovingChallengeId] = useState<string | null>(
    null,
  );
  const [movingChallengeId, setMovingChallengeId] = useState<string | null>(
    null,
  );
  const [togglingRequiredChallengeId, setTogglingRequiredChallengeId] =
    useState<string | null>(null);

  // Pagination for available challenges
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAvailableChallenges, setTotalAvailableChallenges] = useState(0);
  const limit = 10; // Number of challenges per page

  // Filter available challenges based on search query and filters
  const filteredAvailableChallenges = availableChallenges.filter(
    (challenge) => {
      const matchesSearch =
        challenge.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        challenge.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesType = typeFilter === 'all' || challenge.type === typeFilter;
      const matchesDifficulty =
        difficultyFilter === 'all' || challenge.difficulty === difficultyFilter;

      return matchesSearch && matchesType && matchesDifficulty;
    },
  );

  // Function to load battle challenges
  const loadBattleChallenges = async () => {
    if (!battle.id) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetchBattleChallenges(battle.id);
      const challengesData = safelyExtractData(response, 'data.challenges') as
        | IChallenge[]
        | null;

      if (challengesData) {
        setChallenges(challengesData);
      } else {
        setChallenges([]);
      }
    } catch (err) {
      console.error('Error fetching battle challenges:', err);
      setError(
        extractErrorMessage(
          err as Record<string, unknown>,
          'Failed to load battle challenges. Please try again.',
        ),
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch battle challenges when component mounts
  useEffect(() => {
    loadBattleChallenges();
  }, [battle.id]);

  // Function to load available challenges
  const loadAvailableChallenges = async () => {
    if (!battle.id) return;

    setIsLoadingAvailableChallenges(true);
    setAvailableChallengesError(null);

    try {
      const response = await fetchAvailableChallenges({
        search: searchQuery,
        type: typeFilter !== 'all' ? typeFilter : undefined,
        difficulty: difficultyFilter !== 'all' ? difficultyFilter : undefined,
        page,
        limit,
      });

      const challengesData = safelyExtractData(response, 'data.challenges') as
        | IChallenge[]
        | null;
      const totalCount = safelyExtractData(
        response,
        'data.totalCount',
      ) as number;

      if (challengesData) {
        setAvailableChallenges(challengesData);
        setTotalAvailableChallenges(totalCount || 0);
        setTotalPages(Math.ceil((totalCount || 0) / limit));
      } else {
        setAvailableChallenges([]);
        setTotalAvailableChallenges(0);
        setTotalPages(1);
      }
    } catch (err) {
      console.error('Error fetching available challenges:', err);
      setAvailableChallengesError(
        extractErrorMessage(
          err as Record<string, unknown>,
          'Failed to load available challenges. Please try again.',
        ),
      );
    } finally {
      setIsLoadingAvailableChallenges(false);
    }
  };

  // Fetch available challenges when dialog opens
  useEffect(() => {
    if (!isAddChallengeDialogOpen) return;
    loadAvailableChallenges();
  }, [
    isAddChallengeDialogOpen,
    searchQuery,
    typeFilter,
    difficultyFilter,
    page,
    limit,
  ]);

  // Update parent component when challenges change
  useEffect(() => {
    updateBattle({
      challengeCount: challenges.length,
    });
  }, [challenges, updateBattle]);

  // Handle adding challenges to the battle
  const handleAddChallenges = () => {
    setIsAddChallengeDialogOpen(true);
  };

  // Handle toggling challenge selection
  const handleToggleChallengeSelection = (challengeId: string) => {
    setSelectedChallenges((prev) =>
      prev.includes(challengeId)
        ? prev.filter((id) => id !== challengeId)
        : [...prev, challengeId],
    );
  };

  // Handle adding selected challenges to the battle
  const handleAddSelectedChallenges = async () => {
    if (selectedChallenges.length === 0) return;

    setIsAddingChallenges(true);
    try {
      const response = await addChallengesToBattle({
        battleId: battle.id,
        challengeIds: selectedChallenges,
      });

      if (response && response.success) {
        const challengesData = safelyExtractData(
          response,
          'data.challenges',
        ) as IChallenge[] | null;

        if (challengesData) {
          setChallenges(challengesData);
          updateBattle({ challengeCount: challengesData.length });
          toast({
            title: 'Challenges added',
            description: `Successfully added ${selectedChallenges.length} challenge(s) to the battle.`,
          });
        }

        setIsAddChallengeDialogOpen(false);
        setSelectedChallenges([]);
      }
    } catch (err) {
      console.error('Error adding challenges to battle:', err);
      toast({
        title: 'Error',
        description: extractErrorMessage(
          err as Record<string, unknown>,
          'Failed to add challenges. Please try again.',
        ),
        variant: 'destructive',
      });
    } finally {
      setIsAddingChallenges(false);
    }
  };

  // Handle removing a challenge from the battle
  const handleRemoveChallenge = async (challengeId: string) => {
    if (
      !confirm(
        'Are you sure you want to remove this challenge from the battle?',
      )
    ) {
      return;
    }

    setRemovingChallengeId(challengeId);

    try {
      const response = await removeChallengeFromBattle({
        battleId: battle.id,
        challengeId,
      });

      if (response && response.success) {
        const updatedChallenges = safelyExtractData(
          response,
          'data.challenges',
        ) as IChallenge[] | null;

        if (updatedChallenges) {
          setChallenges(updatedChallenges);
        } else {
          // Fallback to client-side removal if API doesn't return updated list
          setChallenges((prev) => {
            const filtered = prev.filter(
              (challenge) => challenge.id !== challengeId,
            );
            // Update order for remaining challenges
            return filtered.map((challenge, index) => ({
              ...challenge,
              order: index + 1,
            }));
          });
        }

        toast({
          title: 'Challenge Removed',
          description: 'The challenge has been removed from this battle.',
        });
      }
    } catch (err) {
      console.error('Error removing challenge:', err);
      toast({
        title: 'Error',
        description: extractErrorMessage(
          err as Record<string, unknown>,
          'Failed to remove challenge. Please try again.',
        ),
        variant: 'destructive',
      });
    } finally {
      setRemovingChallengeId(null);
    }
  };

  // Handle moving a challenge up or down in the order
  const handleMoveChallenge = async (
    challengeId: string,
    direction: 'up' | 'down',
  ) => {
    setMovingChallengeId(challengeId);

    const index = challenges.findIndex(
      (challenge) => challenge.id === challengeId,
    );
    if (index === -1) return;

    let newOrder;
    if (direction === 'up' && index > 0) {
      newOrder = challenges[index - 1].order;
    } else if (direction === 'down' && index < challenges.length - 1) {
      newOrder = challenges[index + 1].order;
    } else {
      return;
    }

    try {
      const response = await updateChallengeOrder({
        battleId: battle.id,
        challengeId,
        newOrder,
      });

      if (response && response.success) {
        const updatedChallenges = safelyExtractData(
          response,
          'data.challenges',
        ) as IChallenge[] | null;

        if (updatedChallenges) {
          setChallenges(updatedChallenges);
        } else {
          // Fallback to client-side reordering
          const newChallenges = [...challenges];
          if (direction === 'up' && index > 0) {
            [newChallenges[index - 1], newChallenges[index]] = [
              newChallenges[index],
              newChallenges[index - 1],
            ];
            // Update order property
            newChallenges[index - 1].order = index;
            newChallenges[index].order = index + 1;
          } else if (direction === 'down' && index < challenges.length - 1) {
            [newChallenges[index], newChallenges[index + 1]] = [
              newChallenges[index + 1],
              newChallenges[index],
            ];
            // Update order property
            newChallenges[index].order = index + 1;
            newChallenges[index + 1].order = index + 2;
          }
          setChallenges(newChallenges);
        }
      }
    } catch (err) {
      console.error('Error updating challenge order:', err);
      toast({
        title: 'Error',
        description: extractErrorMessage(
          err as Record<string, unknown>,
          'Failed to update challenge order. Please try again.',
        ),
        variant: 'destructive',
      });
    } finally {
      setMovingChallengeId(null);
    }
  };

  // Handle toggling whether a challenge is required
  const handleToggleRequired = async (
    challengeId: string,
    isRequired: boolean,
  ) => {
    setTogglingRequiredChallengeId(challengeId);

    try {
      const response = await updateChallengeRequired({
        battleId: battle.id,
        challengeId,
        isRequired,
      });

      if (response && response.success) {
        const updatedChallenges = safelyExtractData(
          response,
          'data.challenges',
        ) as IChallenge[] | null;

        if (updatedChallenges) {
          setChallenges(updatedChallenges);
        } else {
          // Fallback to client-side update
          setChallenges((prev) =>
            prev.map((c) => (c.id === challengeId ? { ...c, isRequired } : c)),
          );
        }
      }

      toast({
        title: 'Challenge Updated',
        description: `Challenge is now ${isRequired ? 'required' : 'optional'}.`,
      });
    } catch (err) {
      console.error('Error updating challenge required status:', err);
      toast({
        title: 'Error',
        description: extractErrorMessage(
          err as Record<string, unknown>,
          'Failed to update challenge required status. Please try again.',
        ),
        variant: 'destructive',
      });
    } finally {
      setTogglingRequiredChallengeId(null);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Battle Challenges</h2>
        <Button
          variant="outline"
          className="flex items-center gap-1"
          onClick={handleAddChallenges}
        >
          <RiAddLine />
          Add Challenges
        </Button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="flex flex-col items-center space-y-4">
            <RiLoader4Line className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Loading challenges...</p>
          </div>
        </div>
      ) : error ? (
        <Alert variant="destructive" className="my-4">
          <RiErrorWarningLine className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : challenges.length > 0 ? (
        <div className="space-y-4">
          {challenges.map((challenge, index) => (
            <Card
              key={challenge.id}
              className={`border ${challenge.isRequired ? '' : 'border-dashed'}`}
            >
              <CardContent className="p-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full text-primary">
                        {challenge.order}
                      </div>
                      <h3 className="text-lg font-semibold">
                        {challenge.title}
                      </h3>
                      <Badge variant="outline">{challenge.type}</Badge>
                      <Badge
                        className={`${
                          challenge.difficulty === 'Easy'
                            ? 'bg-success/20 text-success'
                            : challenge.difficulty === 'Medium'
                              ? 'bg-primary/20 text-primary'
                              : 'bg-warning/20 text-warning'
                        }`}
                      >
                        {challenge.difficulty}
                      </Badge>
                    </div>

                    <p className="mt-2 text-muted-foreground">
                      {challenge.description}
                    </p>

                    <div className="mt-4 grid grid-cols-2 gap-4 sm:grid-cols-4">
                      <div>
                        <div className="text-sm text-muted-foreground">
                          Points
                        </div>
                        <div className="font-medium">{challenge.points}</div>
                      </div>
                      {challenge.timeLimit && (
                        <div>
                          <div className="text-sm text-muted-foreground">
                            Time Limit
                          </div>
                          <div className="font-medium">
                            {challenge.timeLimit} minutes
                          </div>
                        </div>
                      )}
                      <div className="flex items-center space-x-2">
                        {togglingRequiredChallengeId === challenge.id ? (
                          <RiLoader4Line className="h-4 w-4 animate-spin text-primary" />
                        ) : (
                          <>
                            <Switch
                              id={`required-${challenge.id}`}
                              checked={challenge.isRequired}
                              onClick={() =>
                                handleToggleRequired(
                                  challenge.id,
                                  !challenge.isRequired,
                                )
                              }
                            />
                            <Label
                              htmlFor={`required-${challenge.id}`}
                              className="text-sm"
                            >
                              Required
                            </Label>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 md:min-w-[180px] md:flex-col md:items-end">
                    {movingChallengeId === challenge.id ? (
                      <Button
                        variant="outline"
                        size="sm"
                        disabled
                        className="w-full"
                      >
                        <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </Button>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-1"
                          onClick={() =>
                            handleMoveChallenge(challenge.id, 'up')
                          }
                          disabled={index === 0}
                        >
                          <RiArrowUpDownLine className="rotate-90" />
                          Move Up
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-1"
                          onClick={() =>
                            handleMoveChallenge(challenge.id, 'down')
                          }
                          disabled={index === challenges.length - 1}
                        >
                          <RiArrowUpDownLine className="-rotate-90" />
                          Move Down
                        </Button>
                      </>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1 border-destructive/30 text-destructive hover:bg-destructive/10"
                      onClick={() => handleRemoveChallenge(challenge.id)}
                      disabled={removingChallengeId === challenge.id}
                    >
                      {removingChallengeId === challenge.id ? (
                        <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <RiDeleteBinLine />
                      )}
                      {isRemovingChallenge === challenge.id
                        ? 'Removing...'
                        : 'Remove'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-6 text-center">
          <div className="py-8">
            <RiCheckboxCircleLine className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">
              No challenges added yet
            </h3>
            <p className="mt-2 text-muted-foreground">
              Add challenges to this battle to get started.
            </p>
            <Button
              className="mx-auto mt-4 flex items-center gap-1"
              onClick={handleAddChallenges}
            >
              <RiAddLine />
              Add Challenges
            </Button>
          </div>
        </Card>
      )}

      {/* Add Challenges Dialog */}
      <Dialog
        open={isAddChallengeDialogOpen}
        onOpenChange={setIsAddChallengeDialogOpen}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Add Challenges to Battle</DialogTitle>
            <DialogDescription>
              Select challenges to add to this battle.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Filters */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div className="relative">
                <Input
                  placeholder="Search challenges..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                  disabled={isLoadingAvailableChallenges}
                />
                <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
              </div>

              <Select
                value={typeFilter}
                onValueChange={setTypeFilter}
                disabled={isLoadingAvailableChallenges}
              >
                <SelectTrigger>
                  <div className="flex items-center">
                    <RiFilterLine className="mr-2" />
                    <SelectValue placeholder="Type" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Coding">Coding</SelectItem>
                  <SelectItem value="Quiz">Quiz</SelectItem>
                  <SelectItem value="Algorithm">Algorithm</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={difficultyFilter}
                onValueChange={setDifficultyFilter}
                disabled={isLoadingAvailableChallenges}
              >
                <SelectTrigger>
                  <div className="flex items-center">
                    <RiFilterLine className="mr-2" />
                    <SelectValue placeholder="Difficulty" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Difficulties</SelectItem>
                  <SelectItem value="Easy">Easy</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Hard">Hard</SelectItem>
                  <SelectItem value="Expert">Expert</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Challenge List */}
            <div className="max-h-[400px] overflow-y-auto rounded-md border">
              {isLoadingAvailableChallenges ? (
                <div className="flex items-center justify-center py-12">
                  <div className="flex flex-col items-center space-y-4">
                    <RiLoader4Line className="h-8 w-8 animate-spin text-primary" />
                    <p className="text-muted-foreground">
                      Loading challenges...
                    </p>
                  </div>
                </div>
              ) : availableChallengesError ? (
                <div className="p-6">
                  <Alert variant="destructive">
                    <RiErrorWarningLine className="h-4 w-4" />
                    <AlertDescription>
                      {availableChallengesError}
                    </AlertDescription>
                  </Alert>
                </div>
              ) : filteredAvailableChallenges.length > 0 ? (
                <div className="divide-y">
                  {filteredAvailableChallenges.map((challenge) => (
                    <div
                      key={challenge.id}
                      className={`flex cursor-pointer items-start gap-3 p-4 hover:bg-muted/50 ${selectedChallenges.includes(challenge.id) ? 'bg-muted' : ''}`}
                      onClick={() =>
                        handleToggleChallengeSelection(challenge.id)
                      }
                    >
                      <input
                        type="checkbox"
                        className="mt-1 h-5 w-5 rounded border-border"
                        checked={selectedChallenges.includes(challenge.id)}
                        onChange={() =>
                          handleToggleChallengeSelection(challenge.id)
                        }
                        onClick={(e) => e.stopPropagation()}
                      />
                      <div className="flex-1">
                        <div className="flex flex-wrap items-center gap-2">
                          <h3 className="font-medium">{challenge.title}</h3>
                          <Badge variant="outline">{challenge.type}</Badge>
                          <Badge
                            className={`${
                              challenge.difficulty === 'Easy'
                                ? 'bg-success/20 text-success'
                                : challenge.difficulty === 'Medium'
                                  ? 'bg-primary/20 text-primary'
                                  : 'bg-warning/20 text-warning'
                            }`}
                          >
                            {challenge.difficulty}
                          </Badge>
                        </div>
                        <p className="mt-1 text-sm text-muted-foreground">
                          {challenge.description}
                        </p>
                        <div className="mt-2 flex items-center gap-4 text-xs text-muted-foreground">
                          <span>{challenge.points} points</span>
                          {challenge.timeLimit && (
                            <span>{challenge.timeLimit} minutes</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-6 text-center">
                  <p className="text-muted-foreground">
                    No challenges found matching your filters.
                  </p>
                </div>
              )}
            </div>

            {/* Pagination controls */}
            {totalPages > 1 && !isLoadingAvailableChallenges && (
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing {(page - 1) * limit + 1} -{' '}
                  {Math.min(page * limit, totalAvailableChallenges)} of{' '}
                  {totalAvailableChallenges}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      setPage((prev) => Math.min(prev + 1, totalPages))
                    }
                    disabled={page === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}

            {selectedChallenges.length > 0 && (
              <div className="flex items-center justify-between rounded-md bg-muted p-2">
                <span className="text-sm">
                  {selectedChallenges.length}{' '}
                  {selectedChallenges.length === 1 ? 'challenge' : 'challenges'}{' '}
                  selected
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-sm"
                  onClick={() => setSelectedChallenges([])}
                  disabled={isAddingChallenges}
                >
                  <RiCloseLine className="mr-1" /> Clear selection
                </Button>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAddChallengeDialogOpen(false)}
              disabled={isAddingChallenges}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddSelectedChallenges}
              disabled={selectedChallenges.length === 0 || isAddingChallenges}
            >
              {isAddingChallenges ? (
                <>
                  <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  Add {selectedChallenges.length}{' '}
                  {selectedChallenges.length === 1 ? 'Challenge' : 'Challenges'}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default BattleChallenges;
