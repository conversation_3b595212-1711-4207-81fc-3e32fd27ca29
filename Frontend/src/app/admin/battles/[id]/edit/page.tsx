/**
 * @file page.tsx
 * @description Battle Editor Page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiSaveLine,
  RiInformationLine,
  RiSwordLine,
  RiSettings4Line,
  RiLoader4Line,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import BattleBasicInfo from './components/BattleBasicInfo';
import BattleChallenges from './components/BattleChallenges';
import BattleSettings from './components/BattleSettings';
import {
  fetchBattleById,
  createBattle,
  updateBattle,
} from '@/services/battleService';
import { extractErrorMessage, safelyExtractData } from '@/utils/apiUtils';

// Define battle interface
interface IBattle {
  id: string;
  title: string;
  description: string;
  type: 'Solo' | 'Team' | 'Tournament';
  difficulty: 'Easy' | 'Medium' | 'Hard' | 'Expert';
  status: 'Scheduled' | 'Active' | 'Completed' | 'Cancelled';
  isFeatured: boolean;
  startDate: string;
  endDate: string;
  maxParticipants: number;
  challengeCount: number;
  participantCount?: number;
  completionRate?: number;
  createdAt?: string;
  updatedAt?: string;
  prizePool?: string;
  tags: string[];
  rules?: string;
  eligibility?: string;
  registrationDeadline?: string;
  prizes?: Array<{
    rank: string;
    reward: string;
    description?: string;
  }>;
}

function BattleEditorPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const isNewBattle = params.id === 'new';
  const [activeTab, setActiveTab] = useState('basic-info');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(!isNewBattle);
  const [error, setError] = useState<string | null>(null);

  // Default empty battle for new battles
  const emptyBattle: IBattle = {
    id: '',
    title: '',
    description: '',
    type: 'Solo',
    difficulty: 'Medium',
    status: 'Scheduled',
    isFeatured: false,
    startDate: new Date().toISOString(),
    endDate: new Date(
      new Date().setDate(new Date().getDate() + 30),
    ).toISOString(),
    maxParticipants: 100,
    participantCount: 0,
    completionRate: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    challengeCount: 0,
    tags: [],
    prizes: [],
  };

  const [battle, setBattle] = useState<IBattle>(emptyBattle);

  // Fetch battle data if editing an existing battle
  useEffect(() => {
    if (!isNewBattle) {
      const loadBattleData = async () => {
        try {
          setIsLoading(true);
          setError(null);

          const response = await fetchBattleById(params.id);
          const battleData = safelyExtractData(
            response,
            'data.battle',
          ) as IBattle | null;

          if (battleData) {
            setBattle(battleData);
          } else {
            setError('Failed to load battle data. Please try again.');
          }
        } catch (error) {
          console.error('Error loading battle:', error);
          setError(
            extractErrorMessage(
              error as Record<string, unknown>,
              'Failed to load battle data. Please try again.',
            ),
          );
        } finally {
          setIsLoading(false);
        }
      };

      loadBattleData();
    }
  }, [isNewBattle, params.id]);

  // Update battle data from child components
  const updateBattleBasicInfo = (data: {
    title?: string;
    description?: string;
    type?: string;
    difficulty?: string;
    startDate?: string;
    endDate?: string;
    maxParticipants?: number;
    prizePool?: string;
    tags?: string[];
  }) => {
    // Convert string type to the enum type if needed
    const typedData: Partial<IBattle> = {
      ...data,
      type: data.type as 'Solo' | 'Team' | 'Tournament' | undefined,
      difficulty: data.difficulty as
        | 'Easy'
        | 'Medium'
        | 'Hard'
        | 'Expert'
        | undefined,
    };
    setBattle((prev) => ({ ...prev, ...typedData }));
  };

  const updateBattleChallenges = (challenges: { challengeCount: number }) => {
    setBattle((prev) => ({ ...prev, ...challenges }));
  };

  const updateBattleSettings = (settings: {
    rules?: string;
    eligibility?: string;
    registrationDeadline?: string;
    prizes?: Array<{
      rank: string;
      reward: string;
      description?: string;
    }>;
    status?: 'Scheduled' | 'Active' | 'Completed' | 'Cancelled';
    isFeatured?: boolean;
  }) => {
    setBattle((prev) => ({ ...prev, ...settings }));
  };

  // Handle save battle
  // Validate battle data before saving
  const validateBattle = (): boolean => {
    // Basic validation for required fields
    if (!battle.title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Battle title is required.',
        variant: 'destructive',
      });
      setActiveTab('basic-info');
      return false;
    }

    if (!battle.description.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Battle description is required.',
        variant: 'destructive',
      });
      setActiveTab('basic-info');
      return false;
    }

    // Validate dates
    const startDate = new Date(battle.startDate);
    const endDate = new Date(battle.endDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      toast({
        title: 'Validation Error',
        description: 'Invalid date format for start or end date.',
        variant: 'destructive',
      });
      setActiveTab('basic-info');
      return false;
    }

    if (startDate >= endDate) {
      toast({
        title: 'Validation Error',
        description: 'End date must be after start date.',
        variant: 'destructive',
      });
      setActiveTab('basic-info');
      return false;
    }

    return true;
  };

  // Handle save battle
  const handleSaveBattle = async () => {
    if (!validateBattle()) return;

    setIsSaving(true);

    try {
      let response;
      let newBattleId;

      if (isNewBattle) {
        // Create new battle
        const {
          id,
          createdAt,
          updatedAt,
          participantCount,
          completionRate,
          ...newBattleData
        } = battle;
        response = await createBattle(newBattleData);
        const createdBattle = safelyExtractData(
          response,
          'data.battle',
        ) as IBattle | null;
        newBattleId = createdBattle?.id;
      } else {
        // Update existing battle
        response = await updateBattle(battle.id, battle);
      }

      toast({
        title: isNewBattle ? 'Battle Created' : 'Battle Updated',
        description: isNewBattle
          ? 'New battle has been created successfully.'
          : 'Battle has been updated successfully.',
      });

      // Redirect to battle detail page if new battle
      if (isNewBattle && newBattleId) {
        router.push(`/admin/battles/${newBattleId}`);
      }
    } catch (error) {
      console.error('Error saving battle:', error);
      toast({
        title: 'Error',
        description: extractErrorMessage(
          error as Record<string, unknown>,
          'An error occurred while saving the battle. Please try again.',
        ),
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // If there's an error loading the battle data
  if (error && !isNewBattle) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.push('/admin/battles')}
            aria-label="Back"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Battle Editor</h1>
        </div>

        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>

        <Button onClick={() => window.location.reload()}>Try Again</Button>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-[50vh] flex-col items-center justify-center space-y-4">
        <RiLoader4Line className="h-12 w-12 animate-spin text-primary" />
        <p className="text-lg font-medium">Loading battle data...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with back button and save button */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() =>
              router.push(
                isNewBattle ? '/admin/battles' : `/admin/battles/${params.id}`,
              )
            }
            aria-label="Back"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {isNewBattle ? 'Create New Battle' : 'Edit Battle'}
            </h1>
            {!isNewBattle && (
              <p className="text-muted-foreground">Battle ID: {params.id}</p>
            )}
          </div>
        </div>

        <Button
          className="flex items-center gap-1"
          onClick={handleSaveBattle}
          disabled={isSaving || isLoading}
        >
          {isSaving ? (
            <>
              <RiLoader4Line className="h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <RiSaveLine className="h-4 w-4" />
              Save Battle
            </>
          )}
        </Button>
      </div>

      {/* Editor Tabs */}
      <Tabs
        defaultValue="basic-info"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid grid-cols-3 md:w-[400px]">
          <TabsTrigger value="basic-info" className="flex items-center gap-1">
            <RiInformationLine className="h-4 w-4" />
            Basic Info
          </TabsTrigger>
          <TabsTrigger value="challenges" className="flex items-center gap-1">
            <RiSwordLine className="h-4 w-4" />
            Challenges
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-1">
            <RiSettings4Line className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic-info" className="mt-6">
          <BattleBasicInfo
            battle={battle}
            updateBattle={updateBattleBasicInfo}
          />
        </TabsContent>

        <TabsContent value="challenges" className="mt-6">
          <BattleChallenges
            battle={battle}
            updateBattle={updateBattleChallenges}
          />
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <BattleSettings battle={battle} updateBattle={updateBattleSettings} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default BattleEditorPage;
