import { toast } from '@/components/ui/use-toast';
import {
  RiAddLine,
  RiSwordLine,
  Ri<PERSON>ser<PERSON>ine,
  RiTrophyLine,
  RiCalendarLine,
  RiBarChartLine,
  RiLoader4Line,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { IBattle } from '../../types';
import { useRouter } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useState } from 'react';
import { useAxiosPost, useAxiosDelete } from '@/hooks/useAxios';
import {
  BATTLE_API,
  IBattleFeatureParams,
  IBattleStatusParams,
} from '@/services/battleService';

interface IBattleListsProps {
  battles: IBattle[];
  searchQuery: string;
  typeFilter: string;
  difficultyFilter: string;
  statusFilter: string;
}

export default function BattleLists({
  battles,
  searchQuery,
  typeFilter,
  difficultyFilter,
  statusFilter,
}: IBattleListsProps) {
  const router = useRouter();

  // State for tracking loading states
  const [actionLoading, setActionLoading] = useState<Record<string, boolean>>(
    {},
  );

  // API hooks
  const [updateFeatureStatus] = useAxiosPost<
    { success: boolean },
    IBattleFeatureParams
  >(BATTLE_API.FEATURE);
  const [updateBattleStatus] = useAxiosPost<
    { success: boolean },
    IBattleStatusParams
  >(BATTLE_API.STATUS);
  const [deleteBattle] = useAxiosDelete<{ success: boolean }>(
    BATTLE_API.DELETE,
  );
  // Filter battles based on search query and filters
  const filteredBattles = battles.filter((battle) => {
    const matchesSearch =
      battle.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      battle.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      battle.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );

    const matchesType = typeFilter === 'all' || battle.type === typeFilter;
    const matchesDifficulty =
      difficultyFilter === 'all' || battle.difficulty === difficultyFilter;
    const matchesStatus =
      statusFilter === 'all' || battle.status === statusFilter;

    return matchesSearch && matchesType && matchesDifficulty && matchesStatus;
  });

  // Handle battle actions
  const handleFeatureToggle = async (
    battleId: string,
    currentlyFeatured: boolean,
  ) => {
    // Set loading state for this battle
    setActionLoading((prev) => ({ ...prev, [battleId]: true }));

    try {
      const response = await updateFeatureStatus({
        battleId,
        isFeatured: !currentlyFeatured,
      });

      if (response.success) {
        toast({
          title: currentlyFeatured ? 'Battle Unfeatured' : 'Battle Featured',
          description: `Battle has been ${currentlyFeatured ? 'removed from' : 'added to'} featured section.`,
        });
      } else {
        toast({
          title: 'Action Failed',
          description: response.message || 'Failed to update feature status',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating feature status:', error);
      toast({
        title: 'Action Failed',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setActionLoading((prev) => ({ ...prev, [battleId]: false }));
    }
  };

  const handleStatusChange = async (battleId: string, newStatus: string) => {
    // Set loading state for this battle
    setActionLoading((prev) => ({ ...prev, [battleId]: true }));

    try {
      const response = await updateBattleStatus({
        battleId,
        status: newStatus as 'Scheduled' | 'Active' | 'Completed' | 'Cancelled',
      });

      if (response.success) {
        toast({
          title: 'Status Updated',
          description: `Battle status has been changed to ${newStatus}.`,
        });
      } else {
        toast({
          title: 'Status Update Failed',
          description: response.message || 'Failed to update battle status',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating battle status:', error);
      toast({
        title: 'Status Update Failed',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setActionLoading((prev) => ({ ...prev, [battleId]: false }));
    }
  };

  const handleDeleteBattle = async (battleId: string, battleTitle: string) => {
    if (
      confirm(
        `Are you sure you want to delete "${battleTitle}"? This action cannot be undone.`,
      )
    ) {
      // Set loading state for this battle
      setActionLoading((prev) => ({ ...prev, [battleId]: true }));

      try {
        const response = await deleteBattle({
          url: `${BATTLE_API.DELETE}/${battleId}`,
        });

        if (response.success) {
          toast({
            title: 'Battle Deleted',
            description: `"${battleTitle}" has been permanently deleted.`,
            variant: 'destructive',
          });
        } else {
          toast({
            title: 'Deletion Failed',
            description: response.message || 'Failed to delete battle',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error deleting battle:', error);
        toast({
          title: 'Deletion Failed',
          description: 'An unexpected error occurred',
          variant: 'destructive',
        });
      } finally {
        setActionLoading((prev) => ({ ...prev, [battleId]: false }));
      }
    }
  };
  return (
    <div className="grid grid-cols-1 gap-6">
      {filteredBattles.length > 0 ? (
        filteredBattles.map((battle) => (
          <Card key={battle.id} className="p-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
              <div className="flex-1">
                <div className="flex flex-wrap items-center gap-2">
                  <h2 className="text-xl font-semibold">{battle.title}</h2>
                  {battle.isFeatured && (
                    <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
                      Featured
                    </Badge>
                  )}
                  <Badge
                    className={`${
                      battle.status === 'Active'
                        ? 'bg-green-100 text-green-800 hover:bg-green-200'
                        : battle.status === 'Scheduled'
                          ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                          : battle.status === 'Completed'
                            ? 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                            : 'bg-red-100 text-red-800 hover:bg-red-200'
                    }`}
                  >
                    {battle.status}
                  </Badge>
                  <Badge
                    className={`${
                      battle.difficulty === 'Easy'
                        ? 'bg-green-100 text-green-800'
                        : battle.difficulty === 'Medium'
                          ? 'bg-blue-100 text-blue-800'
                          : battle.difficulty === 'Hard'
                            ? 'bg-orange-100 text-orange-800'
                            : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {battle.difficulty}
                  </Badge>
                  <Badge variant="outline">{battle.type}</Badge>
                </div>

                <p className="mt-2 text-muted-foreground">
                  {battle.description}
                </p>

                <div className="mt-2 flex flex-wrap gap-2">
                  {battle?.tags?.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                <div className="mt-4 flex flex-wrap items-center gap-x-6 gap-y-2 text-sm">
                  <div className="flex items-center">
                    <RiCalendarLine className="mr-1 text-muted-foreground" />
                    <span>
                      {new Date(battle.startDate).toLocaleDateString()} -{' '}
                      {new Date(battle.endDate).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <RiSwordLine className="mr-1 text-muted-foreground" />
                    <span>
                      {battle.challengeCount}{' '}
                      {battle.challengeCount === 1 ? 'challenge' : 'challenges'}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <RiUserLine className="mr-1 text-muted-foreground" />
                    <span>
                      {battle.participantCount}/{battle.maxParticipants}{' '}
                      participants
                    </span>
                  </div>
                  {battle.status === 'Completed' && (
                    <div className="flex items-center">
                      <RiBarChartLine className="mr-1 text-muted-foreground" />
                      <span>{battle.completionRate}% completion rate</span>
                    </div>
                  )}
                  {battle.prizePool && (
                    <div className="flex items-center">
                      <RiTrophyLine className="mr-1 text-muted-foreground" />
                      <span>{battle.prizePool} prize pool</span>
                    </div>
                  )}
                </div>

                <div className="mt-2 text-xs text-muted-foreground">
                  <span>
                    Created {new Date(battle.createdAt).toLocaleDateString()} •
                    Last updated{' '}
                    {new Date(battle.updatedAt).toLocaleDateString()}
                  </span>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 md:min-w-[180px] md:flex-col md:items-end">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => router.push(`/admin/battles/${battle.id}`)}
                >
                  View Details
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() =>
                    router.push(`/admin/battles/${battle.id}/edit`)
                  }
                >
                  Edit
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className={`flex items-center gap-1 ${battle.isFeatured ? 'border-amber-300 text-amber-700 hover:bg-amber-100' : ''}`}
                  onClick={() =>
                    handleFeatureToggle(battle.id, battle.isFeatured)
                  }
                  disabled={actionLoading[battle.id]}
                >
                  {actionLoading[battle.id] ? (
                    <>
                      <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : battle.isFeatured ? (
                    'Unfeature'
                  ) : (
                    'Feature'
                  )}
                </Button>

                <Select
                  value={battle.status}
                  onValueChange={(value) =>
                    handleStatusChange(battle.id, value)
                  }
                  disabled={actionLoading[battle.id]}
                >
                  <SelectTrigger className="h-9 w-full md:w-auto">
                    {actionLoading[battle.id] ? (
                      <div className="flex items-center">
                        <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
                        <span>Loading...</span>
                      </div>
                    ) : (
                      <SelectValue placeholder="Change Status" />
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Scheduled">Scheduled</SelectItem>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                    <SelectItem value="Cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="sm"
                  className="border-red-300 text-red-700 hover:bg-red-100 flex items-center gap-1"
                  onClick={() => handleDeleteBattle(battle.id, battle.title)}
                  disabled={actionLoading[battle.id]}
                >
                  {actionLoading[battle.id] ? (
                    <>
                      <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    'Delete'
                  )}
                </Button>
              </div>
            </div>

            {/* Progress Bar for Active Battles */}
            {battle.status === 'Active' && (
              <div className="mt-6 border-t border-border pt-4">
                <div className="mb-2 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Battle Progress
                  </div>
                  <div className="text-sm font-medium">
                    {Math.floor(
                      ((new Date().getTime() -
                        new Date(battle.startDate).getTime()) /
                        (new Date(battle.endDate).getTime() -
                          new Date(battle.startDate).getTime())) *
                        100,
                    )}
                    %
                  </div>
                </div>
                <div className="h-2 w-full rounded-full bg-muted">
                  <div
                    className="h-2 rounded-full bg-primary"
                    style={{
                      width: `${Math.floor(
                        ((new Date().getTime() -
                          new Date(battle.startDate).getTime()) /
                          (new Date(battle.endDate).getTime() -
                            new Date(battle.startDate).getTime())) *
                          100,
                      )}%`,
                    }}
                  ></div>
                </div>
              </div>
            )}
          </Card>
        ))
      ) : (
        <div className="py-12 text-center">
          <RiSwordLine className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No battles found</h3>
          <p className="mt-2 text-muted-foreground">
            {searchQuery ||
            typeFilter !== 'all' ||
            difficultyFilter !== 'all' ||
            statusFilter !== 'all'
              ? "Try adjusting your search filters to find what you're looking for."
              : 'Get started by creating your first battle.'}
          </p>
          {!searchQuery &&
            typeFilter === 'all' &&
            difficultyFilter === 'all' &&
            statusFilter === 'all' && (
              <Button
                className="mt-4 flex items-center gap-2"
                onClick={() => router.push('/admin/battles/create')}
              >
                <RiAddLine /> Create New Battle
              </Button>
            )}
        </div>
      )}
    </div>
  );
}
