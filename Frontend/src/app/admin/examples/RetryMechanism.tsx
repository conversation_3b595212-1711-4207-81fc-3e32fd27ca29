/**
 * @file RetryMechanism.tsx
 * @description Example component showing how to use the retry mechanism for API calls
 */
'use client';

import { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { useAxiosGet } from '@/hooks/useAxios';
import { USER_API } from '@/services/userService';
import {
  retryWithBackoff,
  createRetryableApi,
  IRetryConfig,
} from '@/utils/retryUtils';
import { extractErrorMessage, isSuccessResponse } from '@/utils/apiUtils';
import {
  RiRefreshLine,
  RiLoader4Line,
  RiErrorWarningLine,
  RiCheckLine,
  RiTimeLine,
  RiSettings4Line,
} from 'react-icons/ri';
import { IApiResponse, IPaginationMeta } from '@/types';

/**
 * Example component demonstrating how to use the retry mechanism for API calls
 */
export function RetryMechanism() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [retryAttempts, setRetryAttempts] = useState<number[]>([]);
  const [retryDelays, setRetryDelays] = useState<number[]>([]);
  const [isSuccess, setIsSuccess] = useState(false);

  // Retry configuration
  const [retryConfig, setRetryConfig] = useState<IRetryConfig>({
    maxRetries: 3,
    initialDelay: 1000,
    backoffFactor: 2,
    maxDelay: 30000,
    shouldRetry: () => true, // Always retry in this example
    onRetry: (attempt, delay, error) => {
      setRetryAttempts((prev) => [...prev, attempt]);
      setRetryDelays((prev) => [...prev, delay]);
      console.warn(
        `Retry attempt ${attempt} after ${delay}ms due to error:`,
        error,
      );
    },
  });

  // API hooks
  const [fetchUsers] = useAxiosGet(USER_API.LIST);

  // Create a retryable version of the API call
  const fetchUsersWithRetry = useCallback(
    (simulateError: boolean = false) => {
      return retryWithBackoff(async () => {
        const response = await fetchUsers({
          params: {
            page: 1,
            limit: 10,
            // Simulate error if requested
            simulateError: simulateError ? 'true' : undefined,
          },
        });

        // Simulate error for demonstration purposes
        if (simulateError) {
          throw new Error('Simulated API error for retry demonstration');
        }

        return response;
      }, retryConfig);
    },
    [fetchUsers, retryConfig],
  );

  // Handle API call with retry
  const handleApiCall = async (simulateError: boolean = false) => {
    setIsLoading(true);
    setErrorMessage(null);
    setRetryAttempts([]);
    setRetryDelays([]);
    setIsSuccess(false);

    try {
      const response = await fetchUsersWithRetry(simulateError);

      // Cast response to unknown first, then to Record to avoid type errors
      const responseData = response as unknown as Record<string, unknown>;

      if (isSuccessResponse(responseData)) {
        setIsSuccess(true);
        toast({
          title: 'Success',
          description: 'API call completed successfully',
        });
      } else {
        const message = extractErrorMessage(
          responseData,
          'Failed to load data',
        );
        setErrorMessage(message);
        toast({
          title: 'Error',
          description: message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error with retry mechanism:', error);
      const message =
        error instanceof Error ? error.message : 'An unexpected error occurred';
      setErrorMessage(message);
      toast({
        title: 'Error After All Retries',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create a retryable version of the fetchUsers API function
  const retryableApi = useCallback(() => {
    return createRetryableApi(
      // Cast to the generic function type expected by createRetryableApi
      (async (params: Record<string, unknown>) => {
        return await fetchUsers({ params });
      }) as (...args: unknown[]) => Promise<unknown>,
      retryConfig,
    );
  }, [fetchUsers, retryConfig]);

  // Handle API call using the retryable API function
  const handleRetryableApiCall = async () => {
    setIsLoading(true);
    setErrorMessage(null);
    setRetryAttempts([]);
    setRetryDelays([]);
    setIsSuccess(false);

    try {
      // Create and immediately invoke the retryable API function
      const retryableFn = retryableApi();
      const response = await retryableFn({
        page: 1,
        limit: 10,
      });

      // Cast response to unknown first, then to Record to avoid type errors
      const responseData = response as unknown as Record<string, unknown>;

      if (isSuccessResponse(responseData)) {
        setIsSuccess(true);
        toast({
          title: 'Success',
          description: 'API call completed successfully using retryableApi',
        });
      } else {
        const message = extractErrorMessage(
          responseData,
          'Failed to load data',
        );
        setErrorMessage(message);
        toast({
          title: 'Error',
          description: message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error with retryable API:', error);
      const message =
        error instanceof Error ? error.message : 'An unexpected error occurred';
      setErrorMessage(message);
      toast({
        title: 'Error After All Retries',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">API Retry Mechanism Example</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RiSettings4Line className="h-5 w-5" />
              Retry Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="maxRetries">
                Maximum Retry Attempts: {retryConfig.maxRetries}
              </Label>
              <Slider
                id="maxRetries"
                min={1}
                max={10}
                step={1}
                value={[retryConfig.maxRetries]}
                onValueChange={(value) =>
                  setRetryConfig((prev) => ({ ...prev, maxRetries: value[0] }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="initialDelay">
                Initial Delay (ms): {retryConfig.initialDelay}
              </Label>
              <Slider
                id="initialDelay"
                min={100}
                max={5000}
                step={100}
                value={[retryConfig.initialDelay]}
                onValueChange={(value) =>
                  setRetryConfig((prev) => ({
                    ...prev,
                    initialDelay: value[0],
                  }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="backoffFactor">
                Backoff Factor: {retryConfig.backoffFactor}
              </Label>
              <Slider
                id="backoffFactor"
                min={1}
                max={5}
                step={0.5}
                value={[retryConfig.backoffFactor]}
                onValueChange={(value) =>
                  setRetryConfig((prev) => ({
                    ...prev,
                    backoffFactor: value[0],
                  }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxDelay">
                Maximum Delay (ms): {retryConfig.maxDelay}
              </Label>
              <Slider
                id="maxDelay"
                min={1000}
                max={60000}
                step={1000}
                value={[retryConfig.maxDelay]}
                onValueChange={(value) =>
                  setRetryConfig((prev) => ({ ...prev, maxDelay: value[0] }))
                }
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test API Calls with Retry</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col gap-4">
              <Button
                onClick={() => handleApiCall(false)}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <RiLoader4Line className="h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <RiRefreshLine className="h-4 w-4" />
                    Normal API Call
                  </>
                )}
              </Button>

              <Button
                onClick={() => handleApiCall(true)}
                disabled={isLoading}
                variant="outline"
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <RiLoader4Line className="h-4 w-4 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RiErrorWarningLine className="h-4 w-4" />
                    Simulate Error with Retry
                  </>
                )}
              </Button>

              <Button
                onClick={handleRetryableApiCall}
                disabled={isLoading}
                variant="secondary"
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <RiLoader4Line className="h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <RiRefreshLine className="h-4 w-4" />
                    Use createRetryableApi
                  </>
                )}
              </Button>
            </div>

            {retryAttempts.length > 0 && (
              <div className="mt-4 rounded-md border bg-muted p-4">
                <h3 className="mb-2 font-medium">Retry Attempts:</h3>
                <ul className="space-y-1">
                  {retryAttempts.map((attempt, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <RiTimeLine className="h-4 w-4 text-muted-foreground" />
                      Attempt {attempt} after {retryDelays[index]}ms delay
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {isSuccess && (
              <div className="mt-4 flex items-center gap-2 rounded-md border border-green-200 bg-green-50 p-4 text-green-600">
                <RiCheckLine className="h-5 w-5" />
                <span>
                  API call succeeded
                  {retryAttempts.length > 0
                    ? ` after ${retryAttempts.length} retries`
                    : ''}
                  !
                </span>
              </div>
            )}

            {errorMessage && (
              <div className="mt-4 flex items-center gap-2 rounded-md border border-destructive bg-destructive/10 p-4 text-destructive">
                <RiErrorWarningLine className="h-5 w-5" />
                <span>{errorMessage}</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Retry Mechanism Implementation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium">
              1. Basic Usage with retryWithBackoff
            </h3>
            <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
              {`import { retryWithBackoff } from '@/utils/retryUtils';

// Use the retry mechanism with an API call
const fetchDataWithRetry = async () => {
  try {
    const response = await retryWithBackoff(
      async () => {
        return await apiCall();
      },
      {
        maxRetries: 3,
        initialDelay: 1000,
        backoffFactor: 2,
        maxDelay: 30000,
      }
    );
    
    // Handle successful response
    return response;
  } catch (error) {
    // Handle error after all retries have failed
    console.error('Failed after multiple retries:', error);
    throw error;
  }
};`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium">
              2. Creating a Retryable API Function
            </h3>
            <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
              {`import { createRetryableApi } from '@/utils/retryUtils';
import { fetchUsers } from '@/services/userService';

// Create a retryable version of the API function
const fetchUsersWithRetry = createRetryableApi(fetchUsers, {
  maxRetries: 3,
  initialDelay: 1000,
  backoffFactor: 2,
});

// Use it like the original function
const response = await fetchUsersWithRetry({ params: { page: 1 } });`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium">3. Customizing Retry Behavior</h3>
            <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
              {`import { retryWithBackoff } from '@/utils/retryUtils';

const response = await retryWithBackoff(
  async () => {
    return await apiCall();
  },
  {
    maxRetries: 5,
    initialDelay: 500,
    backoffFactor: 1.5,
    maxDelay: 10000,
    // Only retry on network errors or 5xx server errors
    shouldRetry: (error) => {
      if (error.status >= 500) return true;
      if (error.message.includes('network')) return true;
      return false;
    },
    // Log each retry attempt
    onRetry: (attempt, delay, error) => {
      console.warn(\`Retry attempt \${attempt} after \${delay}ms\`);
      // Could also show a toast notification here
    },
  }
);`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default RetryMechanism;
