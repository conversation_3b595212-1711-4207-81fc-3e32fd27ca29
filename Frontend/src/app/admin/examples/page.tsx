/**
 * @file examples/page.tsx
 * @description Examples page showcasing the implementation of standardized hooks and utilities
 */
'use client';

import { useState } from 'react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { UserListWithPagination } from './UserListWithPagination';
import { FormWithValidation } from './FormWithValidation';
import { SafeApiHandling } from './SafeApiHandling';
import { RetryMechanism } from './RetryMechanism';

/**
 * Examples page component that showcases the implementation of standardized hooks and utilities
 */
export default function ExamplesPage() {
  const [activeTab, setActiveTab] = useState('pagination');

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Implementation Examples</h1>
        <p className="mt-2 text-muted-foreground">
          Explore examples of standardized hooks and utilities implementation
        </p>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pagination">Pagination & Filtering</TabsTrigger>
          <TabsTrigger value="form">Form Validation</TabsTrigger>
          <TabsTrigger value="api">API Handling</TabsTrigger>
          <TabsTrigger value="retry">Retry Mechanism</TabsTrigger>
        </TabsList>

        <Card>
          <CardHeader>
            <CardTitle>
              {activeTab === 'pagination' && 'Pagination & Filtering Example'}
              {activeTab === 'form' && 'Form Validation Example'}
              {activeTab === 'api' && 'Safe API Handling Example'}
              {activeTab === 'retry' && 'API Retry Mechanism Example'}
            </CardTitle>
            <CardDescription>
              {activeTab === 'pagination' &&
                'Demonstrates how to use usePagination and useFilter hooks for user management'}
              {activeTab === 'form' &&
                'Shows how to implement form validation using the useForm hook'}
              {activeTab === 'api' &&
                'Illustrates safe API response handling with proper null checks'}
              {activeTab === 'retry' &&
                'Shows how to implement retry mechanisms for failed API calls'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TabsContent value="pagination" className="mt-0">
              <UserListWithPagination />
            </TabsContent>
            <TabsContent value="form" className="mt-0">
              <FormWithValidation />
            </TabsContent>
            <TabsContent value="api" className="mt-0">
              <SafeApiHandling />
            </TabsContent>
            <TabsContent value="retry" className="mt-0">
              <RetryMechanism />
            </TabsContent>
          </CardContent>
        </Card>
      </Tabs>

      <div className="mt-8 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Implementation Notes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">usePagination Hook</h3>
              <p className="text-muted-foreground">
                Standardizes pagination state management across the application,
                handling page navigation, items per page, and pagination
                metadata.
              </p>
              <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
                {`// Basic usage
const pagination = usePagination({
  initialPage: 1,
  initialLimit: 10,
  onPageChange: (params) => {
    // Load data with new pagination parameters
    loadData(params);
  },
});

// Available methods
pagination.nextPage();
pagination.previousPage();
pagination.goToPage(3);
pagination.setItemsPerPage(20);`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium">useFilter Hook</h3>
              <p className="text-muted-foreground">
                Manages filtering state for tables and lists, supporting search
                queries and field-specific filtering.
              </p>
              <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
                {`// Basic usage
const filter = useFilter({
  onFilterChange: () => {
    // Handle filter changes
    loadData(filter.getQueryParams());
  },
});

// Adding and removing filters
filter.addFilter({ field: 'status', value: 'ACTIVE' });
filter.removeFilter('status');
filter.setSearchQuery('search term');
filter.reset();`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium">useForm Hook</h3>
              <p className="text-muted-foreground">
                Provides form state management with validation, error handling,
                and submission logic.
              </p>
              <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
                {`// Basic usage
const form = useForm({
  initialValues: { username: '', email: '' },
  validation: {
    username: [validationRules.required('Username is required')],
    email: [validationRules.email('Invalid email format')],
  },
  onSubmit: async (values, isValid) => {
    if (isValid) {
      // Submit form data
    }
  },
});

// Form methods
<input {...form.getFieldProps('username')} />
form.setValue('username', 'newValue');
form.handleSubmit();
form.resetForm();`}
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium">API Utilities</h3>
              <p className="text-muted-foreground">
                Ensures safe data extraction and proper null checks for API
                responses.
              </p>
              <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
                {`// Safe data extraction
const users = safelyExtractData(response, 'data.users', []);

// Error handling
const errorMessage = extractErrorMessage(response);

// Response validation
if (isSuccessResponse(response)) {
  // Handle success
}

// Check if data exists
if (hasData(response, 'data.users')) {
  // Process data
}`}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
