/**
 * @file SafeApiHandling.tsx
 * @description Example component showing how to use the API utilities for safe data extraction
 */
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAxiosGet } from '@/hooks/useAxios';
import { USER_API, IUser } from '@/services/userService';

// Extend IUser to ensure it can be used with safelyExtractData
interface IUserWithStringIndex extends IUser, Record<string, unknown> {}
import {
  safelyExtractData,
  extractErrorMessage,
  isSuccessResponse,
  extractPaginationMeta,
  hasData,
} from '@/utils/apiUtils';
import { RiLoader4Line, RiRefreshLine } from 'react-icons/ri';

/**
 * Example component demonstrating how to safely handle API responses
 * with proper null checks and error handling
 */
export function SafeApiHandling() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState<IUserWithStringIndex[]>([]);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [paginationInfo, setPaginationInfo] = useState<{
    total: number;
    currentPage: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  } | null>(null);

  // API hook
  const [fetchUsers] = useAxiosGet(USER_API.LIST);

  // Load users with safe data extraction
  const loadUsers = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      const response = await fetchUsers({
        params: {
          page: 1,
          limit: 10,
        },
      });

      // Check if the response is successful
      if (isSuccessResponse(response)) {
        // Safely extract user data with fallback to empty array
        const users = safelyExtractData<IUserWithStringIndex[]>(
          response,
          'data.users',
          [] as IUserWithStringIndex[],
        );

        // Check if we have user data
        if (hasData(response, 'data.users') && Array.isArray(users)) {
          setUserData(users);
          toast({
            title: 'Success',
            description: `Loaded ${users.length} users`,
          });
        } else {
          setUserData([]);
          toast({
            title: 'No Data',
            description: 'No users found',
          });
        }

        // Extract pagination metadata
        const pagination = extractPaginationMeta(response);
        setPaginationInfo({
          total: pagination.total || 0,
          currentPage: pagination.currentPage || 1,
          totalPages: pagination.totalPages || 1,
          hasNextPage: pagination.hasNextPage || false,
          hasPreviousPage: pagination.hasPreviousPage || false,
        });
      } else {
        // Extract error message with a default fallback
        const message = extractErrorMessage(response, 'Failed to load users');
        setErrorMessage(message);
        toast({
          title: 'Error',
          description: message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error loading users:', error);
      const message =
        error instanceof Error ? error.message : 'An unexpected error occurred';
      setErrorMessage(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load users on component mount
  useEffect(() => {
    loadUsers();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Safe API Handling Example</h1>
        <Button
          onClick={loadUsers}
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          {isLoading ? (
            <>
              <RiLoader4Line className="animate-spin" /> Loading...
            </>
          ) : (
            <>
              <RiRefreshLine /> Refresh
            </>
          )}
        </Button>
      </div>

      {errorMessage && (
        <Card className="border-destructive bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{errorMessage}</p>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>User Data</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex h-40 items-center justify-center">
              <RiLoader4Line className="mr-2 h-5 w-5 animate-spin" />
              <span>Loading user data...</span>
            </div>
          ) : userData && userData.length > 0 ? (
            <div className="space-y-4">
              <div className="rounded-md border">
                <table className="w-full">
                  <thead className="bg-muted/50">
                    <tr className="border-b">
                      <th className="p-2 text-left font-medium">Username</th>
                      <th className="p-2 text-left font-medium">Email</th>
                      <th className="p-2 text-left font-medium">Role</th>
                      <th className="p-2 text-left font-medium">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userData.map(
                      (user: IUserWithStringIndex, index: number) => (
                        <tr key={index} className="border-b">
                          <td className="p-2">
                            {/* Safe data extraction for nested properties */}
                            {safelyExtractData<string, string>(
                              user,
                              'username',
                              'N/A',
                            )}
                          </td>
                          <td className="p-2">
                            {safelyExtractData<string, string>(
                              user,
                              'email',
                              'N/A',
                            )}
                          </td>
                          <td className="p-2">
                            {safelyExtractData<string, string>(
                              user,
                              'role',
                              'N/A',
                            )}
                          </td>
                          <td className="p-2">
                            {safelyExtractData<string, string>(
                              user,
                              'status',
                              'N/A',
                            )}
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>

              {/* Display pagination information */}
              {paginationInfo && (
                <div className="text-sm text-muted-foreground">
                  <p>
                    Showing page {paginationInfo.currentPage} of{' '}
                    {paginationInfo.totalPages} (Total: {paginationInfo.total}{' '}
                    users)
                  </p>
                  <p>
                    Has next page: {paginationInfo.hasNextPage ? 'Yes' : 'No'} |
                    Has previous page:{' '}
                    {paginationInfo.hasPreviousPage ? 'Yes' : 'No'}
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="flex h-40 items-center justify-center">
              <p className="text-muted-foreground">No users found</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>API Response Handling Best Practices</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium">
              1. Always check for response success
            </h3>
            <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
              {`if (isSuccessResponse(response)) {
  // Handle success case
} else {
  // Handle error case
}`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium">
              2. Safely extract data with fallbacks
            </h3>
            <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
              {`const users = safelyExtractData<User[]>(
  response, 
  'data.users', 
  []  // Default value if data.users is null/undefined
);`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium">
              3. Extract error messages consistently
            </h3>
            <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
              {`const errorMessage = extractErrorMessage(
  response, 
  'Default error message'
);`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium">4. Handle pagination metadata</h3>
            <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
              {`const pagination = extractPaginationMeta(response);
// pagination contains: total, currentPage, totalPages, 
// hasNextPage, hasPreviousPage`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium">
              5. Check if data exists before using it
            </h3>
            <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
              {`if (hasData(response, 'data.users')) {
  // Process user data
} else {
  // Handle empty data case
}`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default SafeApiHandling;
