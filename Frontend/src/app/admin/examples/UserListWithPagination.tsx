/**
 * @file UserListWithPagination.tsx
 * @description Example component showing how to use the usePagination hook
 */
'use client';

import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  RiAddLine,
  RiUserLine,
  RiEyeLine,
  RiEditLine,
  RiDeleteBinLine,
  RiMoreLine,
  RiLoader4Line,
  RiArrowLeftLine,
  RiArrowRightLine,
} from 'react-icons/ri';
import { ColumnDef } from '@tanstack/react-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import {
  SuspendUserModal,
  DeleteUserModal,
} from '@/components/admin/UserActionModals';
import { useAxiosGet, useAxiosPost, useAxiosDelete } from '@/hooks/useAxios';
import {
  IUser,
  USER_API,
  IUserSuspendParams,
  IBulkActionParams,
  IUserActionResponse,
} from '@/services/userService';

// Define the user list response interface
interface IUserListResponse {
  users: IUser[];
}
import { DataTable } from '@/components/ui/DataTable';
import { usePagination } from '@/hooks/usePagination';
import { useFilter } from '@/hooks/useFilter';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

/**
 * Example component demonstrating how to use the usePagination and useFilter hooks
 * for a user management page with advanced filtering and pagination
 */
export function UserListWithPagination() {
  const router = useRouter();
  const { toast } = useToast();
  const [selectedRows, setSelectedRows] = useState<IUser[]>([]);
  const [users, setUsers] = useState<IUser[]>([]);
  const [isTableLoading, setIsTableLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Modal states
  const [suspendModalOpen, setSuspendModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<IUser | null>(null);
  const [bulkDeleteModalOpen, setBulkDeleteModalOpen] = useState(false);

  // Use our pagination hook
  const pagination = usePagination({
    initialPage: 1,
    initialLimit: 10,
    onPageChange: (params) => {
      // This will be called whenever the page or limit changes
      loadUsers(params);
    },
  });

  // Use our filter hook
  const filter = useFilter({
    onFilterChange: () => {
      // Reset to first page when filters change
      pagination.goToPage(1);
      loadUsers({ page: 1, limit: pagination.limit });
    },
  });

  // API hooks
  const [fetchUsers] = useAxiosGet<{ users: IUser[] }>(USER_API.LIST);
  const [suspendUser] = useAxiosPost<IUserActionResponse, IUserSuspendParams>(
    USER_API.SUSPEND,
  );
  const [deleteUser] = useAxiosDelete<IUserActionResponse>(USER_API.DELETE);
  const [bulkAction, { isLoading: isBulkActionLoading }] = useAxiosPost<
    IUserActionResponse,
    IBulkActionParams
  >(USER_API.BULK_ACTION);

  // Load users from API with pagination and filtering
  const loadUsers = useCallback(
    async (params = pagination.params) => {
      setIsTableLoading(true);
      setError(null);

      try {
        // Combine pagination params with filter params
        const queryParams = {
          ...params,
          ...filter.getQueryParams(),
        };

        // Convert to URL search params
        const urlParams = new URLSearchParams();
        Object.entries(queryParams).forEach(([key, value]) => {
          urlParams.append(key, String(value));
        });

        // Make the API request with params
        const response = await fetchUsers({
          params: queryParams,
        });

        if (response.success && response.data) {
          const users = response.data.users || [];
          setUsers(users);
          setLastUpdated(new Date());

          // Update pagination state from response metadata
          if (response.meta) {
            const metaTotal =
              typeof response.meta.total === 'number' ? response.meta.total : 0;
            const metaCurrentPage =
              typeof response.meta.currentPage === 'number'
                ? response.meta.currentPage
                : 1;
            const metaTotalPages =
              typeof response.meta.totalPages === 'number'
                ? response.meta.totalPages
                : 1;
            const metaLimit =
              typeof response.meta.limit === 'number'
                ? response.meta.limit
                : pagination.limit;

            pagination.updateFromMeta({
              total: metaTotal,
              currentPage: metaCurrentPage,
              totalPages: metaTotalPages,
              hasNextPage: metaCurrentPage < metaTotalPages,
              hasPreviousPage: metaCurrentPage > 1,
              limit: metaLimit,
            });
          }
        } else {
          const errorMessage = response.message || 'Failed to load users';
          setError(errorMessage);
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error loading users:', error);
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to load users';
        setError(errorMessage);
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      } finally {
        setIsTableLoading(false);
      }
    },
    [fetchUsers, pagination, filter, toast],
  );

  // Load users on component mount
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  // Handle user suspension
  const handleSuspendUser = (user: IUser) => {
    setSelectedUser(user);
    setSuspendModalOpen(true);
  };

  // Handle user deletion
  const handleDeleteUser = (user: IUser) => {
    setSelectedUser(user);
    setDeleteModalOpen(true);
  };

  // Handle bulk deletion
  const handleBulkDelete = () => {
    if (selectedRows.length > 0) {
      setBulkDeleteModalOpen(true);
    }
  };

  // Handle suspension confirmation
  const handleSuspendConfirm = async (suspendData: {
    duration: string;
    reason: string;
    notifyUser: boolean;
  }) => {
    if (!selectedUser) return;

    try {
      const response = await suspendUser({
        userId: selectedUser.id,
        duration: suspendData.duration,
        reason: suspendData.reason,
        notifyUser: suspendData.notifyUser,
      });

      if (response.success) {
        toast({
          title: 'User Suspended',
          description: `User ${selectedUser.username} has been suspended.`,
        });
        loadUsers();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to suspend user',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error suspending user:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setSuspendModalOpen(false);
      setSelectedUser(null);
    }
  };

  // Handle deletion confirmation
  const handleDeleteConfirm = async () => {
    if (!selectedUser) return;

    try {
      const response = await deleteUser({}, { userId: selectedUser.id });

      if (response.success) {
        toast({
          title: 'User Deleted',
          description: `User ${selectedUser.username} has been deleted.`,
        });
        loadUsers();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to delete user',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setDeleteModalOpen(false);
      setSelectedUser(null);
    }
  };

  // Handle bulk deletion confirmation
  const handleBulkDeleteConfirm = async () => {
    if (selectedRows.length === 0) return;

    try {
      const response = await bulkAction({
        userIds: selectedRows.map((user) => user.id),
        action: 'delete',
      });

      if (response.success) {
        toast({
          title: 'Users Deleted',
          description: `${selectedRows.length} users have been deleted.`,
        });
        setSelectedRows([]);
        loadUsers();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to delete users',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting users:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setBulkDeleteModalOpen(false);
    }
  };

  // Table columns definition
  const columns: ColumnDef<IUser>[] = [
    {
      accessorKey: 'username',
      header: 'Username',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full text-primary">
              <RiUserLine />
            </div>
            <div>
              <div className="font-medium">{user.username}</div>
              <div className="text-xs text-muted-foreground">{user.email}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => {
        const role = row.original.role;
        return (
          <Badge variant="outline" className="capitalize">
            {role.toLowerCase()}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <Badge
            className={
              status === 'ACTIVE'
                ? 'bg-green-50 text-green-600'
                : status === 'SUSPENDED'
                  ? 'bg-amber-50 text-amber-600'
                  : 'bg-red-50 text-red-600'
            }
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'lastLogin',
      header: 'Last Login',
      cell: ({ row }) => {
        return new Date(row.original.lastLogin).toLocaleDateString();
      },
    },
    {
      accessorKey: 'registrationDate',
      header: 'Registered',
      cell: ({ row }) => {
        return new Date(row.original.registrationDate).toLocaleDateString();
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <RiMoreLine className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => router.push(`/admin/users/${user.id}`)}
                className="flex items-center"
              >
                <RiEyeLine className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => router.push(`/admin/users/${user.id}/edit`)}
                className="flex items-center"
              >
                <RiEditLine className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleSuspendUser(user)}
                className="flex items-center text-amber-600"
              >
                <RiMoreLine className="mr-2 h-4 w-4" />
                Suspend
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleDeleteUser(user)}
                className="flex items-center text-destructive"
              >
                <RiDeleteBinLine className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Handle row selection change
  const handleRowSelectionChange = (rows: IUser[]) => {
    setSelectedRows(rows);
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">User Management</h1>
          <div className="flex items-center gap-2">
            {selectedRows.length > 0 && (
              <Button
                variant="outline"
                className="text-destructive hover:bg-destructive/10"
                onClick={handleBulkDelete}
                disabled={isBulkActionLoading}
              >
                {isBulkActionLoading ? (
                  <>
                    <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>Delete Selected ({selectedRows.length})</>
                )}
              </Button>
            )}
            <Button
              variant="outline"
              size="icon"
              onClick={() => loadUsers()}
              disabled={isTableLoading}
              className="mr-2"
              title="Refresh users"
            >
              <RiLoader4Line
                className={`h-4 w-4 ${isTableLoading ? 'animate-spin' : ''}`}
              />
            </Button>
            <Button
              className="flex items-center gap-2"
              onClick={() => router.push('/admin/users/create')}
            >
              <RiAddLine /> Add New User
            </Button>
          </div>
        </div>

        {/* Search and filter controls */}
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search users..."
              value={filter.searchQuery}
              onChange={(e) => filter.setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <div className="flex items-center gap-2">
            <Select
              value={filter.getFilterValue('status')?.toString() || ''}
              onValueChange={(value) =>
                value
                  ? filter.addFilter({ field: 'status', value })
                  : filter.removeFilter('status')
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="SUSPENDED">Suspended</SelectItem>
                <SelectItem value="INACTIVE">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filter.getFilterValue('role')?.toString() || ''}
              onValueChange={(value) =>
                value
                  ? filter.addFilter({ field: 'role', value })
                  : filter.removeFilter('role')
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Roles</SelectItem>
                <SelectItem value="ADMIN">Admin</SelectItem>
                <SelectItem value="MODERATOR">Moderator</SelectItem>
                <SelectItem value="USER">User</SelectItem>
              </SelectContent>
            </Select>
            {(filter.filters.length > 0 || filter.searchQuery) && (
              <Button variant="ghost" onClick={filter.reset} className="h-9">
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        <div className="rounded-md border bg-card p-6 shadow-sm">
          {error && (
            <div className="mb-4 rounded-md border border-destructive bg-destructive/10 p-4 text-destructive">
              {error}
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={() => loadUsers()}
                disabled={isTableLoading}
              >
                <RiLoader4Line
                  className={`mr-2 h-4 w-4 ${isTableLoading ? 'animate-spin' : ''}`}
                />
                Retry
              </Button>
            </div>
          )}

          {isTableLoading ? (
            <div className="flex h-64 w-full items-center justify-center">
              <RiLoader4Line className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading users...</span>
            </div>
          ) : (
            <>
              <DataTable
                columns={columns}
                data={users}
                searchKey="username"
                showRowSelection={true}
                onRowSelectionChange={handleRowSelectionChange}
              />

              {/* Pagination controls */}
              <div className="mt-4 flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing {users.length} of {pagination.total} users
                </div>

                <div className="flex items-center space-x-2">
                  <Select
                    value={pagination.limit.toString()}
                    onValueChange={(value) =>
                      pagination.setItemsPerPage(Number(value))
                    }
                  >
                    <SelectTrigger className="h-8 w-[70px]">
                      <SelectValue placeholder="10" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={pagination.previousPage}
                      disabled={!pagination.hasPreviousPage}
                      className="h-8 w-8"
                    >
                      <RiArrowLeftLine className="h-4 w-4" />
                    </Button>
                    <span className="text-sm">
                      Page {pagination.page} of {pagination.totalPages || 1}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={pagination.nextPage}
                      disabled={!pagination.hasNextPage}
                      className="h-8 w-8"
                    >
                      <RiArrowRightLine className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Last updated timestamp */}
              <div className="mt-2 text-right text-xs text-muted-foreground">
                Last updated: {lastUpdated.toLocaleString()}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      {selectedUser && (
        <>
          <SuspendUserModal
            isOpen={suspendModalOpen}
            onClose={() => setSuspendModalOpen(false)}
            userId={selectedUser.id}
            userName={selectedUser.username}
            onConfirm={handleSuspendConfirm}
          />

          <DeleteUserModal
            isOpen={deleteModalOpen}
            onClose={() => setDeleteModalOpen(false)}
            userId={selectedUser.id}
            userName={selectedUser.username}
            onConfirm={handleDeleteConfirm}
          />
        </>
      )}

      {/* Bulk Delete Modal - We reuse the DeleteUserModal for bulk deletion */}
      {selectedRows.length > 0 && (
        <DeleteUserModal
          isOpen={bulkDeleteModalOpen}
          onClose={() => setBulkDeleteModalOpen(false)}
          userId="multiple"
          userName={`${selectedRows.length} users`}
          onConfirm={handleBulkDeleteConfirm}
        />
      )}
    </>
  );
}

export default UserListWithPagination;
