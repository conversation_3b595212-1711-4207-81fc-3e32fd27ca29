/**
 * @file FormWithValidation.tsx
 * @description Example component showing how to use the useForm hook with validation
 */
'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { useForm, validationRules } from '@/hooks/useForm';
import { RiSaveLine, RiArrowLeftLine } from 'react-icons/ri';

// Define the form values interface
interface IUserFormValues extends Record<string, unknown> {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: string;
  bio: string;
  agreeToTerms: boolean;
}

/**
 * Example component demonstrating how to use the useForm hook
 * for a user creation form with validation
 */
export function FormWithValidation() {
  const router = useRouter();
  const { toast } = useToast();

  // Initialize the form with useForm hook
  const form = useForm<IUserFormValues>({
    initialValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: '',
      bio: '',
      agreeToTerms: false,
    },
    // Define validation rules for each field
    validation: {
      username: [
        validationRules.required('Username is required'),
        validationRules.minLength(3, 'Username must be at least 3 characters'),
        validationRules.maxLength(20, 'Username must be at most 20 characters'),
      ],
      email: [
        validationRules.required('Email is required'),
        validationRules.email('Please enter a valid email address'),
      ],
      password: [
        validationRules.required('Password is required'),
        validationRules.minLength(8, 'Password must be at least 8 characters'),
        validationRules.pattern(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
          'Password must include uppercase, lowercase, number and special character',
        ),
      ],
      confirmPassword: [
        validationRules.required('Please confirm your password'),
        validationRules.match('password', 'Passwords do not match'),
      ],
      role: [validationRules.required('Please select a role')],
      agreeToTerms: [
        {
          validate: (value) => value === true,
          message: 'You must agree to the terms and conditions',
        },
      ],
    },
    // Handle form submission
    onSubmit: async (values, isValid) => {
      if (!isValid) {
        toast({
          title: 'Validation Error',
          description: 'Please fix the errors in the form',
          variant: 'destructive',
        });
        return;
      }

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: 'User Created',
        description: `User ${values.username} has been created successfully`,
      });

      // Navigate back to users list
      router.push('/admin/users');
    },
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Create New User</h1>
        <Button
          variant="outline"
          className="flex items-center gap-1"
          onClick={() => router.push('/admin/users')}
        >
          <RiArrowLeftLine /> Back to Users
        </Button>
      </div>

      <Card>
        <form onSubmit={form.handleSubmit}>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Username field */}
            <div className="space-y-2">
              <Label htmlFor="username">
                Username <span className="text-destructive">*</span>
              </Label>
              <Input
                id="username"
                name="username"
                value={form.values.username as string}
                onChange={(e) => form.setValue('username', e.target.value)}
                onBlur={() => form.handleBlur('username')}
                className={
                  form.errors.username && form.touched.username
                    ? 'border-destructive'
                    : ''
                }
              />
              {form.errors.username && form.touched.username && (
                <p className="text-sm text-destructive">
                  {form.errors.username}
                </p>
              )}
            </div>

            {/* Email field */}
            <div className="space-y-2">
              <Label htmlFor="email">
                Email <span className="text-destructive">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                name="email"
                value={form.values.email as string}
                onChange={(e) => form.setValue('email', e.target.value)}
                onBlur={() => form.handleBlur('email')}
                className={
                  form.errors.email && form.touched.email
                    ? 'border-destructive'
                    : ''
                }
              />
              {form.errors.email && form.touched.email && (
                <p className="text-sm text-destructive">{form.errors.email}</p>
              )}
            </div>

            {/* Password fields */}
            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="password">
                  Password <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="password"
                  type="password"
                  name="password"
                  value={form.values.password as string}
                  onChange={(e) => form.setValue('password', e.target.value)}
                  onBlur={() => form.handleBlur('password')}
                  className={
                    form.errors.password && form.touched.password
                      ? 'border-destructive'
                      : ''
                  }
                />
                {form.errors.password && form.touched.password && (
                  <p className="text-sm text-destructive">
                    {form.errors.password}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">
                  Confirm Password <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  name="confirmPassword"
                  value={form.values.confirmPassword as string}
                  onChange={(e) =>
                    form.setValue('confirmPassword', e.target.value)
                  }
                  onBlur={() => form.handleBlur('confirmPassword')}
                  className={
                    form.errors.confirmPassword && form.touched.confirmPassword
                      ? 'border-destructive'
                      : ''
                  }
                />
                {form.errors.confirmPassword &&
                  form.touched.confirmPassword && (
                    <p className="text-sm text-destructive">
                      {form.errors.confirmPassword}
                    </p>
                  )}
              </div>
            </div>

            {/* Role selection */}
            <div className="space-y-2">
              <Label htmlFor="role">
                Role <span className="text-destructive">*</span>
              </Label>
              <Select
                value={form.values.role}
                onValueChange={(value) => form.setValue('role', value)}
              >
                <SelectTrigger
                  id="role"
                  className={
                    form.errors.role && form.touched.role
                      ? 'border-destructive'
                      : ''
                  }
                >
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="MODERATOR">Moderator</SelectItem>
                  <SelectItem value="USER">User</SelectItem>
                </SelectContent>
              </Select>
              {form.errors.role && form.touched.role && (
                <p className="text-sm text-destructive">{form.errors.role}</p>
              )}
            </div>

            {/* Bio field */}
            <div className="space-y-2">
              <Label htmlFor="bio">Bio</Label>
              <Textarea
                id="bio"
                name="bio"
                value={form.values.bio as string}
                onChange={(e) => form.setValue('bio', e.target.value)}
                onBlur={() => form.handleBlur('bio')}
                className="min-h-[100px]"
              />
            </div>

            {/* Terms and conditions */}
            <div className="flex items-start space-x-2">
              <Checkbox
                id="agreeToTerms"
                checked={form.values.agreeToTerms}
                onCheckedChange={(checked) =>
                  form.setValue('agreeToTerms', checked === true)
                }
              />
              <div className="grid gap-1.5 leading-none">
                <Label
                  htmlFor="agreeToTerms"
                  className={
                    form.errors.agreeToTerms && form.touched.agreeToTerms
                      ? 'text-destructive'
                      : ''
                  }
                >
                  I agree to the terms and conditions{' '}
                  <span className="text-destructive">*</span>
                </Label>
                {form.errors.agreeToTerms && form.touched.agreeToTerms && (
                  <p className="text-sm text-destructive">
                    {form.errors.agreeToTerms}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={form.resetForm}
              disabled={form.isSubmitting || !form.isDirty}
            >
              Reset
            </Button>
            <Button
              type="submit"
              disabled={form.isSubmitting}
              className="flex items-center gap-1"
            >
              {form.isSubmitting ? (
                'Creating...'
              ) : (
                <>
                  <RiSaveLine /> Create User
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

export default FormWithValidation;
