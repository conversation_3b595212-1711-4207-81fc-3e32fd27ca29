/**
 * @file page.tsx
 * @description Admin Global Search Functionality
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiSearchLine,
  RiFilter3Line,
  RiBookmarkLine,
  RiBookmarkFill,
  RiArrowRightLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';

interface ISearchResult {
  id: string;
  title: string;
  description: string;
  type: 'user' | 'content' | 'roadmap' | 'challenge' | 'comment' | 'setting';
  url: string;
  createdAt: string;
  metadata?: Record<string, any>;
  highlight?: string;
}

interface ISavedSearch {
  id: string;
  name: string;
  query: string;
  filters: ISearchFilters;
  createdAt: string;
}

interface ISearchFilters {
  types: string[];
  dateRange: {
    from: string | null;
    to: string | null;
  };
  status?: string;
  sortBy: 'relevance' | 'newest' | 'oldest';
}

function AdminSearch() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<ISearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [savedSearches, setSavedSearches] = useState<ISavedSearch[]>([
    {
      id: '1',
      name: 'Recent User Reports',
      query: 'report',
      filters: {
        types: ['user', 'comment'],
        dateRange: {
          from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split('T')[0],
          to: null,
        },
        status: 'pending',
        sortBy: 'newest',
      },
      createdAt: '2025-05-20',
    },
    {
      id: '2',
      name: 'Flagged Content',
      query: 'flagged',
      filters: {
        types: ['content', 'comment'],
        dateRange: {
          from: null,
          to: null,
        },
        status: 'flagged',
        sortBy: 'newest',
      },
      createdAt: '2025-05-18',
    },
  ]);

  const [filters, setFilters] = useState<ISearchFilters>({
    types: [],
    dateRange: {
      from: null,
      to: null,
    },
    sortBy: 'relevance',
  });

  const [isSaveSearchDialogOpen, setIsSaveSearchDialogOpen] = useState(false);
  const [newSearchName, setNewSearchName] = useState('');

  // Mock search results for demonstration
  // TODO: Replace with actual API call
  const mockSearchResults: ISearchResult[] = [
    {
      id: '1',
      title: 'John Doe',
      description: 'User account with 3 recent violations',
      type: 'user',
      url: '/admin/users/123',
      createdAt: '2025-05-22',
      metadata: {
        email: '<EMAIL>',
        status: 'active',
        violations: 3,
      },
      highlight: '...user has <mark>reported</mark> multiple content pieces...',
    },
    {
      id: '2',
      title: 'React Roadmap Feedback',
      description: 'Comment on React Roadmap: "This content is inappropriate"',
      type: 'comment',
      url: '/admin/moderation/comments?id=456',
      createdAt: '2025-05-23',
      metadata: {
        status: 'flagged',
        author: 'user123',
        contentId: 'roadmap-789',
      },
      highlight:
        '...This content is <mark>inappropriate</mark> and should be reviewed...',
    },
    {
      id: '3',
      title: 'Advanced JavaScript Challenge',
      description: 'User-submitted challenge with 5 reports',
      type: 'challenge',
      url: '/admin/content/challenges/789',
      createdAt: '2025-05-21',
      metadata: {
        author: 'expert_dev',
        status: 'under_review',
        reports: 5,
      },
      highlight:
        '...challenge has been <mark>reported</mark> by multiple users for...',
    },
    {
      id: '4',
      title: 'System Maintenance Setting',
      description: 'Maintenance mode configuration',
      type: 'setting',
      url: '/admin/settings',
      createdAt: '2025-05-20',
      metadata: {
        category: 'system',
        lastModified: '2025-05-20',
      },
    },
    {
      id: '5',
      title: 'Frontend Development Roadmap',
      description: 'Complete learning path for frontend developers',
      type: 'roadmap',
      url: '/admin/roadmaps/101',
      createdAt: '2025-05-19',
      metadata: {
        author: 'admin',
        status: 'published',
        popularity: 'high',
      },
    },
  ];

  const handleSearch = () => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      // Filter results based on search query and filters
      let results = [...mockSearchResults];

      // Filter by search query
      if (searchQuery) {
        results = results.filter(
          (result) =>
            result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            result.description
              .toLowerCase()
              .includes(searchQuery.toLowerCase()),
        );
      }

      // Filter by type
      if (filters.types.length > 0) {
        results = results.filter((result) =>
          filters.types.includes(result.type),
        );
      }

      // Filter by date range
      if (filters.dateRange.from) {
        results = results.filter(
          (result) =>
            new Date(result.createdAt) >=
            new Date(filters.dateRange.from as string),
        );
      }

      if (filters.dateRange.to) {
        results = results.filter(
          (result) =>
            new Date(result.createdAt) <=
            new Date(filters.dateRange.to as string),
        );
      }

      // Filter by status if specified
      if (filters.status) {
        results = results.filter(
          (result) => result.metadata?.status === filters.status,
        );
      }

      // Sort results
      switch (filters.sortBy) {
        case 'newest':
          results.sort(
            (a, b) =>
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
          );
          break;
        case 'oldest':
          results.sort(
            (a, b) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
          );
          break;
        case 'relevance':
        default:
          // Relevance is the default order in the mock data
          break;
      }

      setSearchResults(results);
      setIsLoading(false);
    }, 500);
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleTypeFilterChange = (type: string, checked: boolean) => {
    setFilters((prev) => {
      if (checked) {
        return {
          ...prev,
          types: [...prev.types, type],
        };
      } else {
        return {
          ...prev,
          types: prev.types.filter((t) => t !== type),
        };
      }
    });
  };

  const handleSaveSearch = () => {
    if (!newSearchName.trim() || !searchQuery.trim()) return;

    const newSavedSearch: ISavedSearch = {
      id: `search-${Date.now()}`,
      name: newSearchName,
      query: searchQuery,
      filters: { ...filters },
      createdAt: new Date().toISOString().split('T')[0],
    };

    setSavedSearches([...savedSearches, newSavedSearch]);
    setIsSaveSearchDialogOpen(false);
    setNewSearchName('');

    // TODO: Save to API
  };

  const handleLoadSavedSearch = (savedSearch: ISavedSearch) => {
    setSearchQuery(savedSearch.query);
    setFilters(savedSearch.filters);
    // Execute search with loaded parameters
    setTimeout(() => handleSearch(), 0);
  };

  const handleDeleteSavedSearch = (id: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent click handler
    setSavedSearches(savedSearches.filter((search) => search.id !== id));
    // TODO: Delete from API
  };

  const filterResultsByType = (type: string) => {
    if (type === 'all') return searchResults;
    return searchResults.filter((result) => result.type === type);
  };

  // Run search when query or filters change
  useEffect(() => {
    if (searchQuery.trim()) {
      handleSearch();
    }
  }, [filters]); // Intentionally not including searchQuery to avoid auto-search while typing

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Admin Search</h1>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="relative flex-1">
              <Input
                placeholder="Search across users, content, settings, and more..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <RiSearchLine
                className="absolute right-3 top-1/2 -translate-y-1/2 transform cursor-pointer text-muted-foreground"
                onClick={handleSearch}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="whitespace-nowrap"
              >
                <RiFilter3Line className="mr-2" />
                {showFilters ? 'Hide Filters' : 'Show Filters'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsSaveSearchDialogOpen(true)}
                disabled={!searchQuery.trim()}
                className="whitespace-nowrap"
              >
                <RiBookmarkLine className="mr-2" /> Save Search
              </Button>
            </div>
          </div>

          {showFilters && (
            <div className="mt-4 space-y-4 rounded-md border p-4">
              <h3 className="font-medium">Advanced Filters</h3>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Content Type</h4>
                  <div className="space-y-1">
                    {[
                      'user',
                      'content',
                      'roadmap',
                      'challenge',
                      'comment',
                      'setting',
                    ].map((type) => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={`type-${type}`}
                          checked={filters.types.includes(type)}
                          onCheckedChange={(checked) =>
                            handleTypeFilterChange(type, checked as boolean)
                          }
                        />
                        <label
                          htmlFor={`type-${type}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Date Range</h4>
                  <div className="space-y-2">
                    <div className="space-y-1">
                      <label className="text-sm">From</label>
                      <Input
                        type="date"
                        value={filters.dateRange.from || ''}
                        onChange={(e) =>
                          handleFilterChange('dateRange', {
                            ...filters.dateRange,
                            from: e.target.value || null,
                          })
                        }
                      />
                    </div>
                    <div className="space-y-1">
                      <label className="text-sm">To</label>
                      <Input
                        type="date"
                        value={filters.dateRange.to || ''}
                        onChange={(e) =>
                          handleFilterChange('dateRange', {
                            ...filters.dateRange,
                            to: e.target.value || null,
                          })
                        }
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Status</h4>
                  <Select
                    value={filters.status || ''}
                    onValueChange={(value) =>
                      handleFilterChange('status', value || undefined)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="flagged">Flagged</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="under_review">Under Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Sort By</h4>
                  <Select
                    value={filters.sortBy}
                    onValueChange={(value) =>
                      handleFilterChange('sortBy', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="relevance">Relevance</SelectItem>
                      <SelectItem value="newest">Newest First</SelectItem>
                      <SelectItem value="oldest">Oldest First</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() =>
                    setFilters({
                      types: [],
                      dateRange: {
                        from: null,
                        to: null,
                      },
                      sortBy: 'relevance',
                    })
                  }
                >
                  Reset Filters
                </Button>
                <Button onClick={handleSearch}>Apply Filters</Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Saved Searches */}
      {savedSearches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Saved Searches</CardTitle>
            <CardDescription>
              Quick access to your frequently used searches
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {savedSearches.map((savedSearch) => (
                <div
                  key={savedSearch.id}
                  className="cursor-pointer rounded-md border p-4 transition-colors hover:bg-accent"
                  onClick={() => handleLoadSavedSearch(savedSearch)}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-medium">{savedSearch.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        Query: "{savedSearch.query}"
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) =>
                        handleDeleteSavedSearch(savedSearch.id, e)
                      }
                    >
                      ×
                    </Button>
                  </div>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {savedSearch.filters.types.length > 0 &&
                      savedSearch.filters.types.map((type) => (
                        <Badge key={type} variant="outline" className="text-xs">
                          {type}
                        </Badge>
                      ))}
                    {savedSearch.filters.status && (
                      <Badge variant="outline" className="text-xs">
                        {savedSearch.filters.status}
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
            <CardDescription>
              Found {searchResults.length} results for "{searchQuery}"
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs
              defaultValue="all"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="mb-4">
                <TabsTrigger value="all">
                  All ({searchResults.length})
                </TabsTrigger>
                <TabsTrigger value="user">
                  Users ({filterResultsByType('user').length})
                </TabsTrigger>
                <TabsTrigger value="content">
                  Content ({filterResultsByType('content').length})
                </TabsTrigger>
                <TabsTrigger value="roadmap">
                  Roadmaps ({filterResultsByType('roadmap').length})
                </TabsTrigger>
                <TabsTrigger value="challenge">
                  Challenges ({filterResultsByType('challenge').length})
                </TabsTrigger>
                <TabsTrigger value="comment">
                  Comments ({filterResultsByType('comment').length})
                </TabsTrigger>
                <TabsTrigger value="setting">
                  Settings ({filterResultsByType('setting').length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="space-y-4">
                {searchResults.map((result) => (
                  <SearchResultItem key={result.id} result={result} />
                ))}
              </TabsContent>

              {[
                'user',
                'content',
                'roadmap',
                'challenge',
                'comment',
                'setting',
              ].map((type) => (
                <TabsContent key={type} value={type} className="space-y-4">
                  {filterResultsByType(type).map((result) => (
                    <SearchResultItem key={result.id} result={result} />
                  ))}
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>
      )}

      {searchQuery && searchResults.length === 0 && !isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <p className="text-muted-foreground">
                No results found for "{searchQuery}"
              </p>
              <p className="mt-1 text-sm text-muted-foreground">
                Try adjusting your search or filters
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <p className="text-muted-foreground">Searching...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Search Dialog */}
      <Dialog
        open={isSaveSearchDialogOpen}
        onOpenChange={setIsSaveSearchDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Search</DialogTitle>
            <DialogDescription>
              Save this search for quick access later
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search Name</label>
              <Input
                placeholder="Enter a name for this search"
                value={newSearchName}
                onChange={(e) => setNewSearchName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Search Query</label>
              <div className="rounded-md bg-accent p-2">
                <p className="text-sm">{searchQuery}</p>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Applied Filters</label>
              <div className="space-y-1 rounded-md bg-accent p-2">
                {filters.types.length > 0 && (
                  <p className="text-sm">Types: {filters.types.join(', ')}</p>
                )}
                {(filters.dateRange.from || filters.dateRange.to) && (
                  <p className="text-sm">
                    Date Range: {filters.dateRange.from || 'Any'} to{' '}
                    {filters.dateRange.to || 'Any'}
                  </p>
                )}
                {filters.status && (
                  <p className="text-sm">Status: {filters.status}</p>
                )}
                <p className="text-sm">Sort By: {filters.sortBy}</p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsSaveSearchDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleSaveSearch} disabled={!newSearchName.trim()}>
              Save Search
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

function SearchResultItem({ result }: { result: ISearchResult }) {
  const router = useRouter();

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'user':
        return '👤';
      case 'content':
        return '📄';
      case 'roadmap':
        return '🗺️';
      case 'challenge':
        return '🏆';
      case 'comment':
        return '💬';
      case 'setting':
        return '⚙️';
      default:
        return '📌';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'user':
        return 'bg-blue-100 text-blue-800';
      case 'content':
        return 'bg-green-100 text-green-800';
      case 'roadmap':
        return 'bg-purple-100 text-purple-800';
      case 'challenge':
        return 'bg-orange-100 text-orange-800';
      case 'comment':
        return 'bg-yellow-100 text-yellow-800';
      case 'setting':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div
      className="cursor-pointer rounded-md border p-4 transition-colors hover:bg-accent"
      onClick={() => router.push(result.url)}
    >
      <div className="flex items-start justify-between">
        <div>
          <div className="flex items-center gap-2">
            <span className="text-lg">{getTypeIcon(result.type)}</span>
            <h3 className="font-medium">{result.title}</h3>
            <Badge className={`${getTypeColor(result.type)}`}>
              {result.type.charAt(0).toUpperCase() + result.type.slice(1)}
            </Badge>
          </div>
          <p className="mt-1 text-sm text-muted-foreground">
            {result.description}
          </p>
          {result.highlight && (
            <div
              className="mt-2 rounded-md bg-accent p-2 text-sm"
              dangerouslySetInnerHTML={{ __html: result.highlight }}
            />
          )}
        </div>
        <Button variant="ghost" size="icon">
          <RiArrowRightLine />
        </Button>
      </div>
      <div className="mt-2 flex items-center justify-between">
        <div className="flex flex-wrap gap-2">
          {result.metadata &&
            Object.entries(result.metadata)
              .filter(([key]) => key !== 'status') // Status is handled separately
              .map(([key, value]) => (
                <div key={key} className="text-xs text-muted-foreground">
                  {key.charAt(0).toUpperCase() + key.slice(1)}:{' '}
                  {value.toString()}
                </div>
              ))}
        </div>
        <div className="text-xs text-muted-foreground">
          {new Date(result.createdAt).toLocaleDateString()}
        </div>
      </div>
    </div>
  );
}

export default AdminSearch;
