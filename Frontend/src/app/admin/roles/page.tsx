/**
 * @file page.tsx
 * @description Role management page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiAddLine,
  RiSearchLine,
  RiShieldUserLine,
  RiUserSettingsLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { useAxiosGet, useAxiosDelete } from '@/hooks/useAxios';
import { ROLE_API, IRole, IRoleListResponse } from '@/services/roleService';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';

export default function RolesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [roles, setRoles] = useState<IRole[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filteredRoles, setFilteredRoles] = useState<IRole[]>([]);

  // API hooks
  const [fetchRoles] = useAxiosGet<IRoleListResponse>(ROLE_API.LIST);
  const [deleteRole] = useAxiosDelete<{ success: boolean; message?: string }>(
    ROLE_API.DELETE,
  );

  // Fetch roles data
  useEffect(() => {
    const getRolesData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetchRoles();

        if (response.success && response.data?.roles) {
          setRoles(response.data.roles);
        } else {
          setError(response.message || 'Failed to fetch roles data');
          toast({
            title: 'Error',
            description: response.message || 'Failed to fetch roles data',
            variant: 'destructive',
          });
        }
      } catch (err) {
        console.error('Error fetching roles:', err);
        setError('An error occurred while fetching roles data');
        toast({
          title: 'Error',
          description: 'An error occurred while fetching roles data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    getRolesData();
  }, [fetchRoles]);

  // Filter roles based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredRoles(roles);
    } else {
      const filtered = roles.filter(
        (role) =>
          role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          role.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          role.type.toLowerCase().includes(searchQuery.toLowerCase()),
      );
      setFilteredRoles(filtered);
    }
  }, [roles, searchQuery]);

  // Handle role deletion
  const handleDeleteRole = async (role: IRole) => {
    if (
      window.confirm(`Are you sure you want to delete the ${role.name} role?`)
    ) {
      try {
        const response = await deleteRole({
          url: ROLE_API.DELETE.replace('{{roleId}}', role.id),
        });

        if (response.success) {
          toast({
            title: 'Role Deleted',
            description: `The ${role.name} role has been deleted.`,
          });

          // Update roles list
          setRoles((prev) => prev.filter((r) => r.id !== role.id));
        } else {
          toast({
            title: 'Error',
            description: response.message || 'Failed to delete role',
            variant: 'destructive',
          });
        }
      } catch (err) {
        console.error('Error deleting role:', err);
        toast({
          title: 'Error',
          description: 'An error occurred while deleting the role',
          variant: 'destructive',
        });
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Role Management</h1>
        <Button
          className="flex items-center gap-2 rounded-md bg-accent px-4 py-2 text-accent-foreground transition-colors hover:bg-accent/90"
          onClick={() => router.push('/admin/roles/create')}
        >
          <RiAddLine /> Add New Role
        </Button>
      </div>

      {/* Search */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-grow">
          <Input
            type="text"
            placeholder="Search roles..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className="overflow-hidden rounded-lg border bg-card shadow-sm"
            >
              <div className="space-y-4 p-6">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-8 w-32" />
                  <Skeleton className="h-6 w-20" />
                </div>
                <Skeleton className="h-4 w-full" />
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                </div>
                <div className="flex justify-end space-x-3">
                  <Skeleton className="h-9 w-20" />
                  <Skeleton className="h-9 w-24" />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error state */}
      {error && !isLoading && (
        <div className="rounded-lg bg-destructive/10 p-4 text-destructive">
          {error}
        </div>
      )}

      {/* Empty state */}
      {!isLoading && !error && filteredRoles.length === 0 && (
        <div className="rounded-lg border bg-card p-8 text-center">
          <h3 className="mb-2 text-lg font-medium">No roles found</h3>
          <p className="text-muted-foreground">
            {searchQuery
              ? 'No roles match your search criteria.'
              : 'There are no roles defined yet.'}
          </p>
          {searchQuery && (
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => setSearchQuery('')}
            >
              Clear Search
            </Button>
          )}
        </div>
      )}

      {/* Roles grid */}
      {!isLoading && !error && filteredRoles.length > 0 && (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredRoles.map((role) => (
            <div
              key={role.id}
              className="overflow-hidden rounded-lg border bg-card shadow-sm"
            >
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="mr-3 rounded-full bg-accent/10 p-2 text-accent">
                      <RiShieldUserLine className="text-xl" />
                    </div>
                    <h3 className="text-lg font-semibold">{role.name}</h3>
                  </div>
                  <span className="rounded-full bg-muted px-2 py-1 text-xs font-semibold text-muted-foreground">
                    {role.type}
                  </span>
                </div>

                <p className="mt-3 text-sm text-muted-foreground">
                  {role.description}
                </p>

                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div className="rounded-md bg-muted p-3">
                    <p className="text-xs text-muted-foreground">Users</p>
                    <p className="text-lg font-semibold text-foreground">
                      {role.userCount}
                    </p>
                  </div>
                  <div className="rounded-md bg-muted p-3">
                    <p className="text-xs text-muted-foreground">Permissions</p>
                    <p className="text-lg font-semibold text-foreground">
                      {role.permissions}
                    </p>
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-primary text-primary hover:bg-primary hover:text-white"
                    onClick={() => router.push(`/admin/roles/${role.id}/edit`)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white"
                    onClick={() =>
                      router.push(`/admin/roles/permissions?role=${role.id}`)
                    }
                  >
                    Permissions
                  </Button>
                  {role.type !== 'ADMIN' && role.type !== 'USER' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                      onClick={() => handleDeleteRole(role)}
                    >
                      Delete
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* User-Role Assignment */}
      <div className="mb-6 flex justify-end">
        <Button
          variant="outline"
          className="flex items-center gap-2"
          onClick={() => router.push('/admin/roles/assign')}
        >
          <RiUserSettingsLine /> Manage User Role Assignments
        </Button>
      </div>

      {/* Role information */}
      <div className="rounded-lg bg-card p-6 shadow-sm">
        <h2 className="mb-4 text-lg font-semibold">About Roles</h2>
        <p className="mb-4 text-gray-600">
          Roles define what actions users can perform in the system. Each role
          has a set of permissions that determine what features and actions are
          available to users with that role.
        </p>
        <div className="border-l-4 border-blue-500 bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-blue-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                The Administrator and User roles are system-defined and cannot
                be deleted. You can modify their permissions, but these roles
                must exist in the system.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
