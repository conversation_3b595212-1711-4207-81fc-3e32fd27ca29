/**
 * @file page.tsx
 * @description Role edit page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiSaveLine,
  RiShieldUserLine,
  RiCheckLine,
  RiCloseLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/use-toast';
import { useAxiosGet, useAxiosPut } from '@/hooks/useAxios';
import {
  ROLE_API,
  IRole,
  IRoleResponse,
  IPermission,
  IPermissionListResponse,
} from '@/services/roleService';

// Define permission interface with isGranted flag
interface IPermissionWithGrant extends IPermission {
  isGranted: boolean;
}

// Define permission group interface
interface IPermissionGroup {
  name: string;
  permissions: IPermissionWithGrant[];
}

function RoleEditPage() {
  const params = useParams();
  const router = useRouter();
  const roleId = params.id as string;

  const [role, setRole] = useState<IRole | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [permissionGroups, setPermissionGroups] = useState<IPermissionGroup[]>(
    [],
  );
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    description: '',
  });

  // TODO: Implement system role functionality when needed

  // API hooks
  const [fetchRole] = useAxiosGet<IRoleResponse>(
    ROLE_API.DETAIL.replace('{{roleId}}', roleId),
  );
  const [fetchPermissions] = useAxiosGet<IPermissionListResponse>(
    ROLE_API.PERMISSIONS,
  );
  const [updateRole] = useAxiosPut(
    ROLE_API.UPDATE.replace('{{roleId}}', roleId),
  );
  const [updatePermissions] = useAxiosPut(
    ROLE_API.ROLE_PERMISSIONS.replace('{{roleId}}', roleId),
  );

  // Fetch role and permissions data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch role details
        const roleResponse = await fetchRole();

        if (roleResponse.success && roleResponse.data?.role) {
          const roleData = roleResponse.data.role;
          setRole(roleData);
          setFormData({
            name: roleData.name,
            description: roleData.description || '',
            type: roleData.type,
          });
          // System role status is stored in roleData.isSystem
        } else {
          const errorMessage =
            roleResponse.message || 'Failed to fetch role data';
          setError(errorMessage);
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'destructive',
          });
          return;
        }

        // Fetch permissions data
        const permissionsResponse = await fetchPermissions();

        if (
          permissionsResponse.success &&
          permissionsResponse.data?.permissions
        ) {
          try {
            // Get the role's current permissions
            const rolePermissionsResponse = await fetch(
              `${ROLE_API.ROLE_PERMISSIONS.replace('{{roleId}}', roleId)}`,
            );

            if (!rolePermissionsResponse.ok) {
              throw new Error('Failed to fetch role permissions');
            }

            const rolePermissionsRawData = await rolePermissionsResponse.json();

            // Handle both direct response and wrapped response formats
            const rolePermissionsData =
              rolePermissionsRawData.data || rolePermissionsRawData;
            const grantedPermissionIds =
              rolePermissionsData.permissionIds || [];

            // Group permissions by category
            const permissions = permissionsResponse.data.permissions;
            const groupedPermissions: Record<string, IPermissionWithGrant[]> =
              {};

            permissions.forEach((permission: IPermission) => {
              const category = permission.category;
              if (!groupedPermissions[category]) {
                groupedPermissions[category] = [];
              }

              // Add isGranted flag based on the role's current permissions
              groupedPermissions[category].push({
                ...permission,
                isGranted: grantedPermissionIds.includes(permission.id),
              });
            });

            // Convert to IPermissionGroup array
            const groups: IPermissionGroup[] = Object.entries(
              groupedPermissions,
            ).map(([name, permissions]) => ({
              name,
              permissions,
            }));

            setPermissionGroups(groups);
          } catch (permissionsError) {
            console.error('Error fetching role permissions:', permissionsError);
            throw new Error('Failed to fetch role permissions');
          }
        } else {
          const errorMessage =
            permissionsResponse.message || 'Failed to fetch permissions';
          throw new Error(errorMessage);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(
          err instanceof Error
            ? err.message
            : 'An error occurred while fetching data',
        );
        toast({
          title: 'Error',
          description:
            err instanceof Error
              ? err.message
              : 'An error occurred while fetching data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [roleId, fetchRole, fetchPermissions, toast]);

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string, isGranted: boolean) => {
    setPermissionGroups((prevGroups) => {
      return prevGroups.map((group) => ({
        ...group,
        permissions: group.permissions.map((permission) =>
          permission.id === permissionId
            ? { ...permission, isGranted }
            : permission,
        ),
      }));
    });
  };

  // Toggle all permissions in a group
  const handleToggleGroup = (groupName: string, isGranted: boolean) => {
    setPermissionGroups((prevGroups) => {
      return prevGroups.map((group) =>
        group.name === groupName
          ? {
              ...group,
              permissions: group.permissions.map((permission) => ({
                ...permission,
                isGranted,
              })),
            }
          : group,
      );
    });
  };

  // Count granted permissions in a group
  const countGrantedPermissions = (group: IPermissionGroup) => {
    return group.permissions.filter((p) => p.isGranted).length;
  };

  // Check if all permissions in a group are granted
  const areAllPermissionsGranted = (group: IPermissionGroup) => {
    return group.permissions.every((p) => p.isGranted);
  };

  // Check if no permissions in a group are granted
  const areNoPermissionsGranted = (group: IPermissionGroup) => {
    return group.permissions.every((p) => !p.isGranted);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      // Update role details
      const roleUpdateResponse = await updateRole({
        name: formData.name,
        description: formData.description,
      });

      if (roleUpdateResponse.success) {
        // Get all granted permissions
        const grantedPermissions: string[] = [];

        permissionGroups.forEach((group) => {
          group.permissions.forEach((permission) => {
            if (permission.isGranted) {
              grantedPermissions.push(permission.id);
            }
          });
        });

        // Update role permissions
        const permissionsUpdateResponse = await updatePermissions({
          permissionIds: grantedPermissions,
        });

        if (permissionsUpdateResponse.success) {
          toast({
            title: 'Success',
            description: 'Role has been updated successfully',
          });

          // Redirect back to roles list
          router.push('/admin/roles');
        } else {
          throw new Error(
            permissionsUpdateResponse.message || 'Failed to update permissions',
          );
        }
      } else {
        throw new Error(roleUpdateResponse.message || 'Failed to update role');
      }
    } catch (error) {
      console.error('Error updating permissions:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle permissions update
  const handlePermissionsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setSaving(true);

    // Get all granted permission IDs
    const grantedPermissionIds = permissionGroups
      .flatMap((group) => group.permissions)
      .filter((permission) => permission.isGranted)
      .map((permission) => permission.id);

    try {
      // Call API to update permissions
      const response = await updatePermissions({
        permissionIds: grantedPermissionIds,
      });

      if (response.success) {
        toast({
          title: 'Permissions Updated',
          description: `Permissions for role "${role?.name}" have been successfully updated.`,
        });
      } else {
        toast({
          title: 'Error',
          description:
            response.message ||
            'Failed to update permissions. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating permissions:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="rounded-md border border-border p-8 text-center">
        <p className="text-muted-foreground">Loading role data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md bg-destructive/10 p-4 text-destructive">
        <p>{error}</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push('/admin/roles')}
        >
          Back to Roles
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => router.push('/admin/roles')}
          >
            <RiArrowLeftLine className="mr-1" /> Back to Roles
          </Button>
          <h1 className="text-2xl font-bold">Edit Role: {role?.name}</h1>
          <p className="text-muted-foreground">Role ID: {role?.id}</p>
        </div>
      </div>

      {/* Tabs for role details and permissions */}
      <Tabs defaultValue="details" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="details">Role Details</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
        </TabsList>

        {/* Details Tab */}
        <TabsContent value="details">
          <Card>
            <form onSubmit={handleSubmit}>
              <CardHeader>
                <CardTitle>Role Information</CardTitle>
                <CardDescription>
                  Edit the basic details for this role
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">
                      Role Name <span className="text-destructive">*</span>
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="e.g. Content Manager"
                      required
                      disabled={role?.type === 'ADMIN' || role?.type === 'USER'}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type">
                      Role Type <span className="text-destructive">*</span>
                    </Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) =>
                        handleSelectChange('type', value)
                      }
                      disabled={true} // Type cannot be changed after creation
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select role type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ADMIN">ADMIN</SelectItem>
                        <SelectItem value="MODERATOR">MODERATOR</SelectItem>
                        <SelectItem value="CONTRIBUTOR">CONTRIBUTOR</SelectItem>
                        <SelectItem value="USER">USER</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      Role type cannot be changed after creation.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Describe the purpose and responsibilities of this role"
                      rows={3}
                      disabled={role?.type === 'ADMIN' || role?.type === 'USER'}
                    />
                  </div>
                </div>

                {/* Role Information */}
                <div className="rounded-md bg-muted p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <RiShieldUserLine className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium">
                        About System Roles
                      </h3>
                      <div className="mt-2 text-sm text-muted-foreground">
                        <p>
                          The Administrator and User roles are system-defined
                          and have limited editing capabilities. You can modify
                          their permissions, but the name and core functionality
                          cannot be changed.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  type="submit"
                  disabled={
                    saving || role?.type === 'ADMIN' || role?.type === 'USER'
                  }
                  className="flex items-center gap-1"
                >
                  {saving ? (
                    'Saving...'
                  ) : (
                    <>
                      <RiSaveLine /> Save Changes
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="mt-4">
          <Card>
            <form onSubmit={handlePermissionsSubmit}>
              <CardHeader>
                <CardTitle>Role Permissions</CardTitle>
                <CardDescription>
                  Manage what actions users with this role can perform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {permissionGroups.map((group) => (
                    <div
                      key={group.name}
                      className="rounded-md border border-border"
                    >
                      <div className="flex items-center justify-between border-b border-border bg-muted/50 p-4">
                        <div>
                          <h3 className="font-medium">{group.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {countGrantedPermissions(group)} of{' '}
                            {group.permissions.length} permissions granted
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1"
                            onClick={() => handleToggleGroup(group.name, true)}
                            disabled={areAllPermissionsGranted(group)}
                          >
                            <RiCheckLine /> Grant All
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1"
                            onClick={() => handleToggleGroup(group.name, false)}
                            disabled={areNoPermissionsGranted(group)}
                          >
                            <RiCloseLine /> Revoke All
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-3 p-4">
                        {group.permissions.map((permission) => (
                          <div
                            key={permission.id}
                            className="flex items-center justify-between"
                          >
                            <div>
                              <p className="font-medium">{permission.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {permission.description}
                              </p>
                            </div>
                            <Switch
                              checked={permission.isGranted}
                              onCheckedChange={(checked) =>
                                handlePermissionToggle(permission.id, checked)
                              }
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  type="submit"
                  disabled={saving}
                  className="flex items-center gap-1"
                >
                  {saving ? (
                    'Saving...'
                  ) : (
                    <>
                      <RiSaveLine /> Save Permissions
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default RoleEditPage;
