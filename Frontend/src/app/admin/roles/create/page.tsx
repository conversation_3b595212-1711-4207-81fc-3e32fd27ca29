/**
 * @file page.tsx
 * @description Role creation page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { RiArrowLeftLine, RiShieldUserLine, RiAddLine } from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { useAxiosPost, useAxiosGet } from '@/hooks/useAxios';
import {
  ROLE_API,
  IRoleCreateParams,
  IPermission,
  IPermissionListResponse,
} from '@/services/roleService';

interface IFormData {
  name: string;
  type: string;
  description: string;
  permissionIds: string[];
}

function CreateRolePage() {
  const router = useRouter();
  const [creating, setCreating] = useState(false);

  // State for permissions
  const [permissions, setPermissions] = useState<IPermission[]>([]);
  const [isLoadingPermissions, setIsLoadingPermissions] = useState(true);
  const [permissionError, setPermissionError] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<IFormData>({
    name: '',
    type: 'USER',
    description: '',
    permissionIds: [],
  });

  // API hooks
  const [createRole] = useAxiosPost<{ success: boolean; message?: string }>(
    ROLE_API.CREATE,
  );
  const [fetchPermissions] = useAxiosGet<IPermissionListResponse>(
    ROLE_API.PERMISSIONS,
  );

  // Fetch permissions data
  useEffect(() => {
    const getPermissionsData = async () => {
      setIsLoadingPermissions(true);
      setPermissionError(null);

      try {
        const response = await fetchPermissions();

        if (response.success && response.data?.permissions) {
          setPermissions(response.data.permissions);
        } else {
          setPermissionError(
            response.message || 'Failed to fetch permissions data',
          );
          toast({
            title: 'Error',
            description: response.message || 'Failed to fetch permissions data',
            variant: 'destructive',
          });
        }
      } catch (err) {
        console.error('Error fetching permissions:', err);
        setPermissionError('An error occurred while fetching permissions data');
        toast({
          title: 'Error',
          description: 'An error occurred while fetching permissions data',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingPermissions(false);
      }
    };

    getPermissionsData();
  }, [fetchPermissions]);

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle permission selection
  const handlePermissionChange = (permissionId: string) => {
    setFormData((prev) => {
      const currentPermissions = [...prev.permissionIds];

      if (currentPermissions.includes(permissionId)) {
        // Remove permission if already selected
        return {
          ...prev,
          permissionIds: currentPermissions.filter((id) => id !== permissionId),
        };
      } else {
        // Add permission if not selected
        return {
          ...prev,
          permissionIds: [...currentPermissions, permissionId],
        };
      }
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.type) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    setCreating(true);

    try {
      // Prepare role data for API
      const roleData: IRoleCreateParams = {
        name: formData.name,
        type: formData.type,
        description: formData.description,
        permissionIds:
          formData.permissionIds.length > 0
            ? formData.permissionIds
            : undefined,
      };

      // Call API to create role
      const response = await createRole(roleData);

      if (response.success) {
        toast({
          title: 'Role Created',
          description: `Role "${formData.name}" has been successfully created.`,
        });

        // Redirect to roles list
        router.push('/admin/roles');
      } else {
        toast({
          title: 'Error',
          description:
            response.message || 'Failed to create role. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating role:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => router.push('/admin/roles')}
          >
            <RiArrowLeftLine className="mr-1" /> Back to Roles
          </Button>
          <h1 className="text-2xl font-bold">Create New Role</h1>
          <p className="text-muted-foreground">
            Define a new role with custom permissions
          </p>
        </div>
      </div>

      {/* Role creation form */}
      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>Role Information</CardTitle>
            <CardDescription>
              Enter the details for the new role
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  Role Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g. Content Manager"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">
                  Role Type <span className="text-destructive">*</span>
                </Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleSelectChange('type', value)}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select role type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ADMIN">ADMIN</SelectItem>
                    <SelectItem value="MODERATOR">MODERATOR</SelectItem>
                    <SelectItem value="CONTRIBUTOR">CONTRIBUTOR</SelectItem>
                    <SelectItem value="USER">USER</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  The role type determines the general level of access. You can
                  customize specific permissions after creating the role.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the purpose and responsibilities of this role"
                  rows={3}
                />
              </div>
            </div>

            {/* Role Information */}
            <div className="rounded-md bg-muted p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <RiShieldUserLine className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium">About Role Types</h3>
                  <div className="mt-2 space-y-1 text-sm text-muted-foreground">
                    <p>
                      <strong>ADMIN:</strong> Full access to all system features
                      and settings.
                    </p>
                    <p>
                      <strong>MODERATOR:</strong> Can moderate content and
                      manage users, but cannot change system settings.
                    </p>
                    <p>
                      <strong>CONTRIBUTOR:</strong> Can create and edit content,
                      but has limited administrative capabilities.
                    </p>
                    <p>
                      <strong>USER:</strong> Basic access with minimal
                      permissions, typically for regular users.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Permissions Section */}
            {!isLoadingPermissions && permissions.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Permissions</h3>
                <p className="text-sm text-muted-foreground">
                  Select the permissions for this role. You can modify these
                  later.
                </p>

                {/* Group permissions by category */}
                {Array.from(new Set(permissions.map((p) => p.category))).map(
                  (category) => (
                    <div key={category} className="space-y-2">
                      <h4 className="text-sm font-medium">{category}</h4>
                      <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
                        {permissions
                          .filter((p) => p.category === category)
                          .map((permission) => (
                            <div
                              key={permission.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`permission-${permission.id}`}
                                checked={formData.permissionIds.includes(
                                  permission.id,
                                )}
                                onCheckedChange={() =>
                                  handlePermissionChange(permission.id)
                                }
                              />
                              <label
                                htmlFor={`permission-${permission.id}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {permission.name}
                              </label>
                            </div>
                          ))}
                      </div>
                    </div>
                  ),
                )}
              </div>
            )}

            {/* Loading state for permissions */}
            {isLoadingPermissions && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Permissions</h3>
                <p className="text-sm text-muted-foreground">
                  Loading available permissions...
                </p>
              </div>
            )}

            {/* Error state for permissions */}
            {permissionError && !isLoadingPermissions && (
              <div className="rounded-md bg-destructive/10 p-4 text-destructive">
                <p>{permissionError}</p>
                <p className="mt-2 text-sm">
                  You can still create the role and assign permissions later.
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/roles')}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={creating}
              className="flex items-center gap-1"
            >
              {creating ? (
                'Creating...'
              ) : (
                <>
                  <RiAddLine /> Create Role
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>

      <div className="rounded border-l-4 border-blue-500 bg-blue-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-400"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">
              After creating a role, you&apos;ll be able to assign specific
              permissions and assign it to users. The Administrator and User
              roles are system-defined and cannot be deleted.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default CreateRolePage;
