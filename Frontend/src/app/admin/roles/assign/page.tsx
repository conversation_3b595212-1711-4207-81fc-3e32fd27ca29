/**
 * @file page.tsx
 * @description User-Role assignment interface for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiUserLine,
  RiShieldUserLine,
  RiSearchLine,
  RiFilterLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/ui/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { toast } from '@/components/ui/use-toast';
import { useAxiosGet, useAxiosPut } from '@/hooks/useAxios';
import { ROLE_API, IRole as IRoleType } from '@/services/roleService';
import { USER_API, IUser as IUserType } from '@/services/userService';

// Define user interface with selection state
interface IUser extends IUserType {
  currentRoles: string[];
  selected?: boolean;
  fullName?: string;
}

// Define role interface
interface IRole extends IRoleType {
  id: string;
  name: string;
  description: string;
  isSystem: boolean;
  userCount: number;
  createdAt: string;
  updatedAt: string;
}

function UserRoleAssignPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');

  // Data states
  const [users, setUsers] = useState<IUser[]>([]);
  const [roles, setRoles] = useState<IRole[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<IUser[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);

  // API hooks
  const [fetchUsers] = useAxiosGet<{ users: IUserType[] }>(USER_API.LIST);
  const [fetchRoles] = useAxiosGet<{ roles: IRoleType[] }>(ROLE_API.LIST);
  const [assignRole] = useAxiosPut<{ success: boolean; message?: string }>(
    USER_API.ASSIGN_ROLE,
  );

  // Fetch users and roles data - using empty dependency array to prevent infinite API calls
  useEffect(() => {
    // Using a mutable object to track component mount state
    const mountState = { current: true };
    
    const fetchData = async () => {
      if (!mountState.current) return;
      setLoading(true);

      try {
        // Fetch roles
        const rolesResponse = await fetchRoles();
        
        if (!mountState.current) return;

        if (rolesResponse.success && rolesResponse.data?.roles) {
          setRoles(rolesResponse.data.roles as IRole[]);
        } else {
          console.error('Failed to fetch roles:', rolesResponse.message);
          if (mountState.current) {
            toast({
              title: 'Error',
              description: 'Failed to fetch roles data',
              variant: 'destructive',
            });
          }
        }

        // Fetch users
        const usersResponse = await fetchUsers();
        
        if (!mountState.current) return;

        if (usersResponse.success && usersResponse.data?.users) {
          // For each user, fetch their current roles
          const usersWithRoles = await Promise.all(
            usersResponse.data.users.map(async (user: IUserType) => {
              // In a real implementation, you would fetch user roles from an API endpoint
              // For now, we'll simulate by assigning default roles
              const userRoles = ['User']; // Default role

              return {
                ...user,
                currentRoles: userRoles,
              } as IUser;
            }),
          );

          if (!mountState.current) return;
          setUsers(usersWithRoles);
        } else {
          console.error('Failed to fetch users:', usersResponse.message);
          if (mountState.current) {
            toast({
              title: 'Error',
              description: 'Failed to fetch users data',
              variant: 'destructive',
            });
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        if (mountState.current) {
          toast({
            title: 'Error',
            description: 'An error occurred while fetching data',
            variant: 'destructive',
          });
        }
      } finally {
        if (mountState.current) {
          setLoading(false);
        }
      }
    };

    fetchData();
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      mountState.current = false;
    };
  }, []);

  // Filter users based on search query and role filter
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      searchQuery === '' ||
      user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user?.email?.toLowerCase()?.includes(searchQuery?.toLowerCase()) ||
      user?.fullName?.toLowerCase()?.includes(searchQuery?.toLowerCase());

    const matchesRoleFilter =
      roleFilter === 'all' ||
      user.currentRoles.includes(
        roles.find((r) => r.id === roleFilter)?.name || '',
      );

    return matchesSearch && matchesRoleFilter;
  });

  // Define columns for the DataTable
  const columns: ColumnDef<IUser, unknown>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'fullName',
      header: 'User',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center">
            <div className="h-10 w-10 flex-shrink-0">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                <RiUserLine className="text-muted-foreground" size={20} />
              </div>
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-foreground">
                {user.fullName}
              </div>
              <div className="text-sm text-muted-foreground">{user.email}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'username',
      header: 'Username',
    },
    {
      accessorKey: 'currentRoles',
      header: 'Current Roles',
      cell: ({ row }) => {
        const roles = row.getValue('currentRoles') as string[];
        return (
          <div className="flex flex-wrap gap-1">
            {roles.map((role) => (
              <Badge
                key={role}
                className={`${
                  role === 'Administrator'
                    ? 'bg-accent/20 text-accent'
                    : role === 'Moderator'
                      ? 'bg-primary/20 text-primary'
                      : role === 'Contributor'
                        ? 'bg-success/20 text-success'
                        : 'bg-muted text-muted-foreground'
                }`}
              >
                {role}
              </Badge>
            ))}
          </div>
        );
      },
    },
  ];

  // Handle row selection change
  const handleRowSelectionChange = (selectedUsers: IUser[]) => {
    setSelectedUsers(selectedUsers);
  };

  // Handle role selection change
  const handleRoleSelectionChange = (roleId: string, checked: boolean) => {
    if (checked) {
      setSelectedRoles((prev) => [...prev, roleId]);
    } else {
      setSelectedRoles((prev) => prev.filter((id) => id !== roleId));
    }
  };

  // Handle assign roles
  const handleAssignRoles = async () => {
    if (selectedUsers.length === 0 || selectedRoles.length === 0) {
      toast({
        title: 'Selection Required',
        description: 'Please select at least one user and one role.',
        variant: 'destructive',
      });
      return;
    }

    setSaving(true);

    try {
      // Process each user and role combination
      const assignmentPromises = [];

      for (const user of selectedUsers) {
        for (const roleId of selectedRoles) {
          // Prepare the API call for each user-role assignment
          const roleAssignmentPromise = assignRole({
            url: USER_API.ASSIGN_ROLE.replace('{{userId}}', user.id),
            roleId,
          });

          assignmentPromises.push(roleAssignmentPromise);
        }
      }

      // Wait for all assignments to complete
      const results = await Promise.all(assignmentPromises);

      // Check if all assignments were successful
      const allSuccessful = results.every((result: { success: boolean }) => result.success);

      if (allSuccessful) {
        // Update users with new roles in the UI
        const updatedUsers = users.map((user) => {
          if (selectedUsers.some((selected) => selected.id === user.id)) {
            // Get role names from selected role IDs
            const roleNames = selectedRoles.map(
              (roleId) => roles.find((r) => r.id === roleId)?.name || '',
            );

            // Add new roles without duplicates
            const updatedRoles = [
              ...new Set([...user.currentRoles, ...roleNames]),
            ];

            return { ...user, currentRoles: updatedRoles };
          }
          return user;
        });

        setUsers(updatedUsers);
        setSelectedUsers([]);
        setSelectedRoles([]);

        toast({
          title: 'Roles Assigned',
          description: `Roles have been assigned to ${selectedUsers.length} user(s).`,
        });
      } else {
        toast({
          title: 'Error',
          description: 'Some role assignments failed. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error assigning roles:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while assigning roles',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle remove roles
  const handleRemoveRoles = async () => {
    if (selectedUsers.length === 0 || selectedRoles.length === 0) {
      toast({
        title: 'Selection Required',
        description: 'Please select at least one user and one role.',
        variant: 'destructive',
      });
      return;
    }

    setSaving(true);

    try {
      // Process each user and role combination for removal
      const removalPromises = [];

      for (const user of selectedUsers) {
        for (const roleId of selectedRoles) {
          // In a real implementation, you would call an API to remove the role
          // For now, we'll simulate by using the same assignRole endpoint with a different parameter
          const roleRemovalPromise = assignRole({
            url: USER_API.ASSIGN_ROLE.replace('{{userId}}', user.id),
            roleId,
            action: 'remove', // This parameter would need to be supported by your API
          });

          removalPromises.push(roleRemovalPromise);
        }
      }

      // Wait for all removals to complete
      const results = await Promise.all(removalPromises);

      // Check if all removals were successful
      const allSuccessful = results.every((result: { success: boolean }) => result.success);

      if (allSuccessful) {
        // Update users with roles removed in the UI
        const updatedUsers = users.map((user) => {
          if (selectedUsers.some((selected) => selected.id === user.id)) {
            // Get role names from selected role IDs
            const roleNamesToRemove = selectedRoles.map(
              (roleId) => roles.find((r) => r.id === roleId)?.name || '',
            );

            // Filter out roles to remove, but keep at least 'User' role
            let updatedRoles = user.currentRoles.filter(
              (role) => !roleNamesToRemove.includes(role),
            );

            // Ensure user has at least the basic User role
            if (updatedRoles.length === 0) {
              updatedRoles = ['User'];
            }

            return { ...user, currentRoles: updatedRoles };
          }
          return user;
        });

        setUsers(updatedUsers);
        setSelectedUsers([]);
        setSelectedRoles([]);

        toast({
          title: 'Roles Removed',
          description: `Roles have been removed from ${selectedUsers.length} user(s).`,
        });
      } else {
        toast({
          title: 'Error',
          description: 'Some role removals failed. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error removing roles:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while removing roles',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-4 text-muted-foreground">Loading data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => router.push('/admin/roles')}
          >
            <RiArrowLeftLine className="mr-1" /> Back to Roles
          </Button>
          <h1 className="text-2xl font-bold">Assign User Roles</h1>
          <p className="text-muted-foreground">
            Manage role assignments for users
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {/* User selection */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Select Users</CardTitle>
              <CardDescription>
                Choose users to assign or remove roles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Search and filter */}
                <div className="flex flex-col gap-4 sm:flex-row">
                  <div className="relative flex-grow">
                    <Input
                      placeholder="Search users..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                    <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
                  </div>
                  <div className="w-full flex-shrink-0 sm:w-48">
                    <Select value={roleFilter} onValueChange={setRoleFilter}>
                      <SelectTrigger>
                        <div className="flex items-center">
                          <RiFilterLine className="mr-2" />
                          <SelectValue placeholder="Filter by role" />
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Roles</SelectItem>
                        {roles.map((role) => (
                          <SelectItem key={role.id} value={role.id}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Users table */}
                <div className="rounded-md border">
                  <DataTable
                    columns={columns}
                    data={filteredUsers}
                    searchKey="username"
                    showRowSelection={true}
                    onRowSelectionChange={handleRowSelectionChange}
                  />
                </div>

                {/* Selected users count */}
                <div className="text-sm text-muted-foreground">
                  {selectedUsers.length > 0 ? (
                    <span>{selectedUsers.length} user(s) selected</span>
                  ) : (
                    <span>No users selected</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Role selection and actions */}
        <div>
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle>Select Roles</CardTitle>
              <CardDescription>
                Choose roles to assign or remove
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Role selection */}
                <div className="space-y-3">
                  {roles.map((role) => (
                    <div key={role.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`role-${role.id}`}
                        checked={selectedRoles.includes(role.id)}
                        onCheckedChange={(checked) =>
                          handleRoleSelectionChange(role.id, !!checked)
                        }
                      />
                      <div className="grid gap-1.5">
                        <Label
                          htmlFor={`role-${role.id}`}
                          className="font-medium"
                        >
                          {role.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {role.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Selected roles count */}
                <div className="text-sm text-muted-foreground">
                  {selectedRoles.length > 0 ? (
                    <span>{selectedRoles.length} role(s) selected</span>
                  ) : (
                    <span>No roles selected</span>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button
                className="flex w-full items-center justify-center gap-1"
                onClick={handleAssignRoles}
                disabled={
                  saving ||
                  selectedUsers.length === 0 ||
                  selectedRoles.length === 0
                }
              >
                <RiShieldUserLine /> Assign Roles
              </Button>
              <Button
                variant="outline"
                className="flex w-full items-center justify-center gap-1 border-destructive text-destructive hover:bg-destructive/10"
                onClick={handleRemoveRoles}
                disabled={
                  saving ||
                  selectedUsers.length === 0 ||
                  selectedRoles.length === 0
                }
              >
                <RiShieldUserLine /> Remove Roles
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Assign Roles</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-inside list-decimal space-y-2 text-muted-foreground">
            <li>Select one or more users from the table on the left.</li>
            <li>Choose one or more roles from the panel on the right.</li>
            <li>
              Click &quot;Assign Roles&quot; to add the selected roles to the
              users.
            </li>
            <li>
              Click &quot;Remove Roles&quot; to remove the selected roles from
              the users.
            </li>
          </ol>
          <div className="mt-4 rounded-md bg-muted p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <RiShieldUserLine className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium">Role Assignment Notes</h3>
                <div className="mt-2 space-y-1 text-sm text-muted-foreground">
                  <p>
                    - Users must have at least one role. The basic
                    &quot;User&quot; role cannot be removed if it&apos;s the
                    only role assigned.
                  </p>
                  <p>
                    - The &quot;Administrator&quot; role grants full access to
                    all system features. Assign it with caution.
                  </p>
                  <p>- Changes to role assignments take effect immediately.</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default UserRoleAssignPage;
