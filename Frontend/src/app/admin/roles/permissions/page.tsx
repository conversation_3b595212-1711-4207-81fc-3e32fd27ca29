/**
 * @file page.tsx
 * @description Permission management system for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiShieldUserLine,
  RiSaveLine,
  RiSearchLine,
  RiFilterLine,
  RiCheckLine,
  RiCloseLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';

// Define permission interface
interface IPermission {
  id: string;
  name: string;
  description: string;
  group: string;
  isGranted: boolean;
}

// Define permission group interface
interface IPermissionGroup {
  name: string;
  permissions: IPermission[];
}

// Define role interface
interface IRole {
  id: string;
  name: string;
  type: string;
  description: string;
  userCount: number;
  permissions: number;
  isSystemRole: boolean;
}

function PermissionManagementPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [groupFilter, setGroupFilter] = useState<string>('all');
  const [selectedRoleId, setSelectedRoleId] = useState<string>('1'); // Default to Administrator role

  // Data states
  const [roles, setRoles] = useState<IRole[]>([]);
  const [permissionGroups, setPermissionGroups] = useState<IPermissionGroup[]>(
    [],
  );

  useEffect(() => {
    // TODO: Replace with actual API call to fetch roles and permissions
    const fetchData = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock roles data
        const mockRoles: IRole[] = [
          {
            id: '1',
            name: 'Administrator',
            type: 'ADMIN',
            description: 'Full access to all system features',
            userCount: 2,
            permissions: 20,
            isSystemRole: true,
          },
          {
            id: '2',
            name: 'Moderator',
            type: 'MODERATOR',
            description: 'Can moderate content and view users',
            userCount: 5,
            permissions: 8,
            isSystemRole: false,
          },
          {
            id: '3',
            name: 'Contributor',
            type: 'CONTRIBUTOR',
            description: 'Can create and edit content',
            userCount: 12,
            permissions: 4,
            isSystemRole: false,
          },
          {
            id: '4',
            name: 'User',
            type: 'USER',
            description: 'Basic access to view content',
            userCount: 1231,
            permissions: 1,
            isSystemRole: true,
          },
        ];

        // Mock permission groups
        const mockPermissionGroups: IPermissionGroup[] = [
          {
            name: 'User Management',
            permissions: [
              {
                id: '1',
                name: 'user.view',
                description: 'View user profiles',
                group: 'User Management',
                isGranted: ['1', '2'].includes(selectedRoleId),
              },
              {
                id: '2',
                name: 'user.create',
                description: 'Create new users',
                group: 'User Management',
                isGranted: ['1'].includes(selectedRoleId),
              },
              {
                id: '3',
                name: 'user.edit',
                description: 'Edit user profiles',
                group: 'User Management',
                isGranted: ['1', '2'].includes(selectedRoleId),
              },
              {
                id: '4',
                name: 'user.delete',
                description: 'Delete users',
                group: 'User Management',
                isGranted: ['1'].includes(selectedRoleId),
              },
              {
                id: '5',
                name: 'user.suspend',
                description: 'Suspend users',
                group: 'User Management',
                isGranted: ['1', '2'].includes(selectedRoleId),
              },
            ],
          },
          {
            name: 'Content Management',
            permissions: [
              {
                id: '6',
                name: 'content.view',
                description: 'View content',
                group: 'Content Management',
                isGranted: ['1', '2', '3', '4'].includes(selectedRoleId),
              },
              {
                id: '7',
                name: 'content.create',
                description: 'Create content',
                group: 'Content Management',
                isGranted: ['1', '2', '3'].includes(selectedRoleId),
              },
              {
                id: '8',
                name: 'content.edit',
                description: 'Edit content',
                group: 'Content Management',
                isGranted: ['1', '2', '3'].includes(selectedRoleId),
              },
              {
                id: '9',
                name: 'content.delete',
                description: 'Delete content',
                group: 'Content Management',
                isGranted: ['1', '2'].includes(selectedRoleId),
              },
              {
                id: '10',
                name: 'content.approve',
                description: 'Approve content',
                group: 'Content Management',
                isGranted: ['1', '2'].includes(selectedRoleId),
              },
            ],
          },
          {
            name: 'Roadmap Management',
            permissions: [
              {
                id: '11',
                name: 'roadmap.view',
                description: 'View roadmaps',
                group: 'Roadmap Management',
                isGranted: ['1', '2', '3', '4'].includes(selectedRoleId),
              },
              {
                id: '12',
                name: 'roadmap.create',
                description: 'Create roadmaps',
                group: 'Roadmap Management',
                isGranted: ['1', '3'].includes(selectedRoleId),
              },
              {
                id: '13',
                name: 'roadmap.edit',
                description: 'Edit roadmaps',
                group: 'Roadmap Management',
                isGranted: ['1', '3'].includes(selectedRoleId),
              },
              {
                id: '14',
                name: 'roadmap.delete',
                description: 'Delete roadmaps',
                group: 'Roadmap Management',
                isGranted: ['1'].includes(selectedRoleId),
              },
              {
                id: '15',
                name: 'roadmap.feature',
                description: 'Feature roadmaps',
                group: 'Roadmap Management',
                isGranted: ['1'].includes(selectedRoleId),
              },
            ],
          },
          {
            name: 'System Settings',
            permissions: [
              {
                id: '16',
                name: 'settings.view',
                description: 'View system settings',
                group: 'System Settings',
                isGranted: ['1'].includes(selectedRoleId),
              },
              {
                id: '17',
                name: 'settings.edit',
                description: 'Edit system settings',
                group: 'System Settings',
                isGranted: ['1'].includes(selectedRoleId),
              },
              {
                id: '18',
                name: 'roles.manage',
                description: 'Manage roles and permissions',
                group: 'System Settings',
                isGranted: ['1'].includes(selectedRoleId),
              },
              {
                id: '19',
                name: 'analytics.view',
                description: 'View analytics',
                group: 'System Settings',
                isGranted: ['1', '2'].includes(selectedRoleId),
              },
              {
                id: '20',
                name: 'logs.view',
                description: 'View system logs',
                group: 'System Settings',
                isGranted: ['1'].includes(selectedRoleId),
              },
            ],
          },
        ];

        setRoles(mockRoles);
        setPermissionGroups(mockPermissionGroups);
        setLoading(false);
      }, 500); // Simulate loading delay
    };

    fetchData();
  }, [selectedRoleId]);

  // Filter permissions based on search query and group filter
  const filteredPermissionGroups = permissionGroups
    .filter((group) => groupFilter === 'all' || group.name === groupFilter)
    .map((group) => ({
      ...group,
      permissions: group.permissions.filter(
        (permission) =>
          searchQuery === '' ||
          permission.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          permission.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()),
      ),
    }))
    .filter((group) => group.permissions.length > 0);

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string, isGranted: boolean) => {
    setPermissionGroups((prevGroups) => {
      return prevGroups.map((group) => ({
        ...group,
        permissions: group.permissions.map((permission) =>
          permission.id === permissionId
            ? { ...permission, isGranted }
            : permission,
        ),
      }));
    });
  };

  // Toggle all permissions in a group
  const handleToggleGroup = (groupName: string, isGranted: boolean) => {
    setPermissionGroups((prevGroups) => {
      return prevGroups.map((group) =>
        group.name === groupName
          ? {
              ...group,
              permissions: group.permissions.map((permission) => ({
                ...permission,
                isGranted,
              })),
            }
          : group,
      );
    });
  };

  // Count granted permissions in a group
  const countGrantedPermissions = (group: IPermissionGroup) => {
    return group.permissions.filter((p) => p.isGranted).length;
  };

  // Check if all permissions in a group are granted
  const areAllPermissionsGranted = (group: IPermissionGroup) => {
    return group.permissions.every((p) => p.isGranted);
  };

  // Check if no permissions in a group are granted
  const areNoPermissionsGranted = (group: IPermissionGroup) => {
    return group.permissions.every((p) => !p.isGranted);
  };

  // Handle role change
  const handleRoleChange = (roleId: string) => {
    setSelectedRoleId(roleId);
  };

  // Handle save permissions
  const handleSavePermissions = () => {
    setSaving(true);

    // TODO: Replace with actual API call to save permissions
    setTimeout(() => {
      // Count total granted permissions
      const totalGrantedPermissions = permissionGroups.reduce(
        (total, group) =>
          total + group.permissions.filter((p) => p.isGranted).length,
        0,
      );

      // Update roles with new permissions count
      const updatedRoles = roles.map((role) =>
        role.id === selectedRoleId
          ? { ...role, permissions: totalGrantedPermissions }
          : role,
      );

      setRoles(updatedRoles);
      setSaving(false);

      toast({
        title: 'Permissions Saved',
        description: `Successfully updated permissions for ${roles.find((r) => r.id === selectedRoleId)?.name}.`,
      });
    }, 1000); // Simulate API delay
  };

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-4 text-muted-foreground">Loading permissions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => router.push('/admin/roles')}
          >
            <RiArrowLeftLine className="mr-1" /> Back to Roles
          </Button>
          <h1 className="text-2xl font-bold">Permission Management</h1>
          <p className="text-muted-foreground">
            Manage permissions for different roles
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        {/* Role selection */}
        <div>
          <Card className="sticky top-6">
            <CardHeader>
              <CardTitle>Select Role</CardTitle>
              <CardDescription>
                Choose a role to manage permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Select value={selectedRoleId} onValueChange={handleRoleChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.id}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Selected role info */}
                {roles.find((r) => r.id === selectedRoleId) && (
                  <div className="rounded-md bg-muted p-4">
                    <div className="flex items-center gap-2">
                      <RiShieldUserLine className="text-muted-foreground" />
                      <h3 className="font-medium">
                        {roles.find((r) => r.id === selectedRoleId)?.name}
                      </h3>
                    </div>
                    <p className="mt-1 text-sm text-muted-foreground">
                      {roles.find((r) => r.id === selectedRoleId)?.description}
                    </p>
                    <div className="mt-2 flex items-center gap-2">
                      <Badge>
                        {roles.find((r) => r.id === selectedRoleId)?.type}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {roles.find((r) => r.id === selectedRoleId)?.userCount}{' '}
                        users
                      </span>
                    </div>
                  </div>
                )}

                <Button
                  className="flex w-full items-center justify-center gap-1"
                  onClick={handleSavePermissions}
                  disabled={saving}
                >
                  {saving ? (
                    'Saving...'
                  ) : (
                    <>
                      <RiSaveLine /> Save Permissions
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Permissions */}
        <div className="md:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle>Permissions</CardTitle>
              <CardDescription>
                Configure what actions users with the{' '}
                {roles.find((r) => r.id === selectedRoleId)?.name} role can
                perform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Search and filter */}
                <div className="flex flex-col gap-4 sm:flex-row">
                  <div className="relative flex-grow">
                    <Input
                      placeholder="Search permissions..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                    <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
                  </div>
                  <div className="w-full flex-shrink-0 sm:w-48">
                    <Select value={groupFilter} onValueChange={setGroupFilter}>
                      <SelectTrigger>
                        <div className="flex items-center">
                          <RiFilterLine className="mr-2" />
                          <SelectValue placeholder="Filter by group" />
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Groups</SelectItem>
                        {permissionGroups.map((group) => (
                          <SelectItem key={group.name} value={group.name}>
                            {group.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Permission groups */}
                {filteredPermissionGroups.length > 0 ? (
                  filteredPermissionGroups.map((group) => (
                    <div
                      key={group.name}
                      className="rounded-md border border-border"
                    >
                      <div className="flex items-center justify-between border-b border-border bg-muted/50 p-4">
                        <div>
                          <h3 className="font-medium">{group.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {countGrantedPermissions(group)} of{' '}
                            {group.permissions.length} permissions granted
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1"
                            onClick={() => handleToggleGroup(group.name, true)}
                            disabled={areAllPermissionsGranted(group)}
                          >
                            <RiCheckLine /> Grant All
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1"
                            onClick={() => handleToggleGroup(group.name, false)}
                            disabled={areNoPermissionsGranted(group)}
                          >
                            <RiCloseLine /> Revoke All
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-3 p-4">
                        {group.permissions.map((permission) => (
                          <div
                            key={permission.id}
                            className="flex items-center justify-between"
                          >
                            <div>
                              <p className="font-medium">{permission.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {permission.description}
                              </p>
                            </div>
                            <Switch
                              checked={permission.isGranted}
                              onCheckedChange={(checked) =>
                                handlePermissionToggle(permission.id, checked)
                              }
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="py-8 text-center">
                    <p className="text-muted-foreground">
                      No permissions found matching your search criteria.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button
                onClick={handleSavePermissions}
                disabled={saving}
                className="flex items-center gap-1"
              >
                {saving ? (
                  'Saving...'
                ) : (
                  <>
                    <RiSaveLine /> Save Permissions
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>About Permissions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-muted-foreground">
              Permissions define what actions users with a specific role can
              perform in the system. Each permission grants access to a specific
              feature or functionality.
            </p>

            <div className="rounded-md bg-muted p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <RiShieldUserLine className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium">
                    Permission Management Tips
                  </h3>
                  <div className="mt-2 space-y-1 text-sm text-muted-foreground">
                    <p>
                      - Permissions are grouped by feature area for easier
                      management.
                    </p>
                    <p>
                      - The Administrator role typically has all permissions
                      granted.
                    </p>
                    <p>
                      - Be cautious when granting sensitive permissions like
                      user deletion or system settings.
                    </p>
                    <p>
                      - Changes to permissions take effect immediately after
                      saving.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PermissionManagementPage;
