/**
 * @file page.tsx
 * @description Email Template Manager for admin dashboard
 */
'use client';

import { useState } from 'react';
import {
  RiSaveLine,
  RiAddLine,
  RiDeleteBin6Line,
  RiMailSendLine,
} from 'react-icons/ri';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface IEmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  type:
    | 'welcome'
    | 'reset-password'
    | 'verification'
    | 'notification'
    | 'marketing'
    | 'custom';
  variables: string[];
}

function EmailTemplateManager() {
  // Mock data for email templates
  // TODO: Replace with actual API data
  const [templates, setTemplates] = useState<IEmailTemplate[]>([
    {
      id: '1',
      name: 'Welcome Email',
      subject: 'Welcome to MrEngineer!',
      body: "Hello {{name}},\n\nWelcome to MrEngineer! We're excited to have you join our community.\n\nGet started by exploring our roadmaps and challenges.\n\nBest regards,\nThe MrEngineer Team",
      type: 'welcome',
      variables: ['name', 'email'],
    },
    {
      id: '2',
      name: 'Password Reset',
      subject: 'Reset Your MrEngineer Password',
      body: 'Hello {{name}},\n\nYou recently requested to reset your password. Click the link below to set a new password:\n\n{{resetLink}}\n\nIf you did not request a password reset, please ignore this email.\n\nBest regards,\nThe MrEngineer Team',
      type: 'reset-password',
      variables: ['name', 'email', 'resetLink'],
    },
    {
      id: '3',
      name: 'Email Verification',
      subject: 'Verify Your MrEngineer Email',
      body: 'Hello {{name}},\n\nThank you for registering with MrEngineer. Please verify your email by clicking the link below:\n\n{{verificationLink}}\n\nBest regards,\nThe MrEngineer Team',
      type: 'verification',
      variables: ['name', 'email', 'verificationLink'],
    },
  ]);

  const [selectedTemplate, setSelectedTemplate] =
    useState<IEmailTemplate | null>(null);
  const [editedTemplate, setEditedTemplate] = useState<IEmailTemplate | null>(
    null,
  );
  const [testEmailAddress, setTestEmailAddress] = useState('');
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [isNewTemplateDialogOpen, setIsNewTemplateDialogOpen] = useState(false);
  const [newTemplate, setNewTemplate] = useState<Omit<IEmailTemplate, 'id'>>({
    name: '',
    subject: '',
    body: '',
    type: 'custom',
    variables: [],
  });
  const [newVariable, setNewVariable] = useState('');

  // Email sending configuration
  const [emailConfig, setEmailConfig] = useState({
    smtpServer: 'smtp.example.com',
    smtpPort: 587,
    smtpUsername: '<EMAIL>',
    smtpPassword: '',
    fromEmail: '<EMAIL>',
    fromName: 'MrEngineer',
    enableSsl: true,
  });

  const handleTemplateSelect = (template: IEmailTemplate) => {
    setSelectedTemplate(template);
    setEditedTemplate(template);
  };

  const handleEditChange = (field: keyof IEmailTemplate, value: string) => {
    if (editedTemplate) {
      setEditedTemplate({
        ...editedTemplate,
        [field]: value,
      });
    }
  };

  const handleSaveTemplate = () => {
    if (editedTemplate) {
      setTemplates(
        templates.map((t) => (t.id === editedTemplate.id ? editedTemplate : t)),
      );
      setSelectedTemplate(editedTemplate);
      // TODO: Save to API
      alert('Template saved successfully!');
    }
  };

  const handleDeleteTemplate = (id: string) => {
    if (confirm('Are you sure you want to delete this template?')) {
      setTemplates(templates.filter((t) => t.id !== id));
      setSelectedTemplate(null);
      setEditedTemplate(null);
      // TODO: Delete from API
      alert('Template deleted successfully!');
    }
  };

  const handleTestEmail = () => {
    // TODO: Implement actual email sending
    console.log(
      `Sending test email to ${testEmailAddress} using template:`,
      selectedTemplate,
    );
    alert(`Test email sent to ${testEmailAddress}`);
    setIsTestDialogOpen(false);
    setTestEmailAddress('');
  };

  const handleConfigChange = (
    field: string,
    value: string | number | boolean,
  ) => {
    setEmailConfig({
      ...emailConfig,
      [field]: value,
    });
  };

  const handleSaveConfig = () => {
    // TODO: Save email configuration to API
    console.log('Saving email configuration:', emailConfig);
    alert('Email configuration saved successfully!');
  };

  const handleAddVariable = () => {
    if (
      newVariable &&
      editedTemplate &&
      !editedTemplate.variables.includes(newVariable)
    ) {
      setEditedTemplate({
        ...editedTemplate,
        variables: [...editedTemplate.variables, newVariable],
      });
      setNewVariable('');
    }
  };

  const handleRemoveVariable = (variable: string) => {
    if (editedTemplate) {
      setEditedTemplate({
        ...editedTemplate,
        variables: editedTemplate.variables.filter((v) => v !== variable),
      });
    }
  };

  const handleCreateTemplate = () => {
    const newId = `template-${Date.now()}`;
    const createdTemplate = {
      ...newTemplate,
      id: newId,
    };

    setTemplates([...templates, createdTemplate]);
    setSelectedTemplate(createdTemplate);
    setEditedTemplate(createdTemplate);
    setIsNewTemplateDialogOpen(false);
    setNewTemplate({
      name: '',
      subject: '',
      body: '',
      type: 'custom',
      variables: [],
    });
    // TODO: Save to API
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Email Template Manager</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsNewTemplateDialogOpen(true)}
          >
            <RiAddLine className="mr-2" /> New Template
          </Button>
        </div>
      </div>

      <Tabs defaultValue="templates" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="templates">Email Templates</TabsTrigger>
          <TabsTrigger value="settings">Email Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
            {/* Template List */}
            <div className="space-y-4 md:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Templates</CardTitle>
                  <CardDescription>Select a template to edit</CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        className={`cursor-pointer p-4 hover:bg-accent ${selectedTemplate?.id === template.id ? 'bg-accent' : ''}`}
                        onClick={() => handleTemplateSelect(template)}
                      >
                        <div className="font-medium">{template.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {template.type}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Template Editor */}
            <div className="md:col-span-3">
              {selectedTemplate ? (
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div>
                      <CardTitle>{editedTemplate?.name}</CardTitle>
                      <CardDescription>Edit template content</CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsTestDialogOpen(true)}
                      >
                        <RiMailSendLine className="mr-2" /> Test
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() =>
                          handleDeleteTemplate(selectedTemplate.id)
                        }
                      >
                        <RiDeleteBin6Line className="mr-2" /> Delete
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Template Name
                      </label>
                      <Input
                        value={editedTemplate?.name || ''}
                        onChange={(e) =>
                          handleEditChange('name', e.target.value)
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Type</label>
                      <Select
                        value={editedTemplate?.type}
                        onValueChange={(value) =>
                          handleEditChange(
                            'type',
                            value as
                              | 'welcome'
                              | 'reset-password'
                              | 'verification'
                              | 'notification'
                              | 'marketing'
                              | 'custom',
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="welcome">Welcome</SelectItem>
                          <SelectItem value="reset-password">
                            Password Reset
                          </SelectItem>
                          <SelectItem value="verification">
                            Verification
                          </SelectItem>
                          <SelectItem value="notification">
                            Notification
                          </SelectItem>
                          <SelectItem value="marketing">Marketing</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Subject</label>
                      <Input
                        value={editedTemplate?.subject || ''}
                        onChange={(e) =>
                          handleEditChange('subject', e.target.value)
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Body</label>
                      <Textarea
                        value={editedTemplate?.body || ''}
                        onChange={(e) =>
                          handleEditChange('body', e.target.value)
                        }
                        rows={10}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Template Variables
                      </label>
                      <div className="mb-2 flex flex-wrap gap-2">
                        {editedTemplate?.variables.map((variable) => (
                          <div
                            key={variable}
                            className="flex items-center rounded-full bg-accent px-3 py-1"
                          >
                            <span className="text-sm">{`{{${variable}}}`}</span>
                            <button
                              className="ml-2 text-muted-foreground hover:text-destructive"
                              onClick={() => handleRemoveVariable(variable)}
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          value={newVariable}
                          onChange={(e) => setNewVariable(e.target.value)}
                          placeholder="New variable name"
                        />
                        <Button onClick={handleAddVariable} type="button">
                          Add
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button onClick={handleSaveTemplate}>
                      <RiSaveLine className="mr-2" /> Save Template
                    </Button>
                  </CardFooter>
                </Card>
              ) : (
                <Card>
                  <CardContent className="flex h-[400px] flex-col items-center justify-center p-6">
                    <p className="text-muted-foreground">
                      Select a template from the list or create a new one
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Email Sending Configuration</CardTitle>
              <CardDescription>
                Configure the SMTP settings for sending emails
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">SMTP Server</label>
                  <Input
                    value={emailConfig.smtpServer}
                    onChange={(e) =>
                      handleConfigChange('smtpServer', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">SMTP Port</label>
                  <Input
                    type="number"
                    value={emailConfig.smtpPort}
                    onChange={(e) =>
                      handleConfigChange('smtpPort', parseInt(e.target.value))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">SMTP Username</label>
                  <Input
                    value={emailConfig.smtpUsername}
                    onChange={(e) =>
                      handleConfigChange('smtpUsername', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">SMTP Password</label>
                  <Input
                    type="password"
                    value={emailConfig.smtpPassword}
                    onChange={(e) =>
                      handleConfigChange('smtpPassword', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">From Email</label>
                  <Input
                    value={emailConfig.fromEmail}
                    onChange={(e) =>
                      handleConfigChange('fromEmail', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">From Name</label>
                  <Input
                    value={emailConfig.fromName}
                    onChange={(e) =>
                      handleConfigChange('fromName', e.target.value)
                    }
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="enableSsl"
                    checked={emailConfig.enableSsl}
                    onChange={(e) =>
                      handleConfigChange('enableSsl', e.target.checked)
                    }
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="enableSsl" className="text-sm font-medium">
                    Enable SSL/TLS
                  </label>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig}>
                <RiSaveLine className="mr-2" /> Save Configuration
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Test Email Dialog */}
      <Dialog open={isTestDialogOpen} onOpenChange={setIsTestDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Send Test Email</DialogTitle>
            <DialogDescription>
              Send a test email using the current template
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Recipient Email</label>
              <Input
                value={testEmailAddress}
                onChange={(e) => setTestEmailAddress(e.target.value)}
                placeholder="Enter email address"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Template Preview</label>
              <div className="rounded-md bg-accent p-4">
                <div className="mb-2 font-medium">
                  Subject: {selectedTemplate?.subject}
                </div>
                <div className="whitespace-pre-line">
                  {selectedTemplate?.body}
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsTestDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleTestEmail} disabled={!testEmailAddress}>
              Send Test
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Template Dialog */}
      <Dialog
        open={isNewTemplateDialogOpen}
        onOpenChange={setIsNewTemplateDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Template</DialogTitle>
            <DialogDescription>Create a new email template</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Template Name</label>
              <Input
                value={newTemplate.name}
                onChange={(e) =>
                  setNewTemplate({ ...newTemplate, name: e.target.value })
                }
                placeholder="Enter template name"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Type</label>
              <Select
                value={newTemplate.type}
                onValueChange={(value) =>
                  setNewTemplate({
                    ...newTemplate,
                    type: value as
                      | 'welcome'
                      | 'reset-password'
                      | 'verification'
                      | 'notification'
                      | 'marketing'
                      | 'custom',
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="welcome">Welcome</SelectItem>
                  <SelectItem value="reset-password">Password Reset</SelectItem>
                  <SelectItem value="verification">Verification</SelectItem>
                  <SelectItem value="notification">Notification</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Subject</label>
              <Input
                value={newTemplate.subject}
                onChange={(e) =>
                  setNewTemplate({ ...newTemplate, subject: e.target.value })
                }
                placeholder="Enter email subject"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Body</label>
              <Textarea
                value={newTemplate.body}
                onChange={(e) =>
                  setNewTemplate({ ...newTemplate, body: e.target.value })
                }
                placeholder="Enter email body"
                rows={5}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsNewTemplateDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateTemplate}
              disabled={
                !newTemplate.name || !newTemplate.subject || !newTemplate.body
              }
            >
              Create Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default EmailTemplateManager;
