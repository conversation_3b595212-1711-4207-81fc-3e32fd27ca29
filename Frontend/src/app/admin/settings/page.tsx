/**
 * @file page.tsx
 * @description Settings page for admin dashboard
 */
'use client';

import { useState } from 'react';
import { RiSaveLine, RiRefreshLine } from 'react-icons/ri';

export default function SettingsPage() {
  // TODO: Need to make this dynamic in future - fetch settings from API
  const [generalSettings, setGeneralSettings] = useState({
    siteName: 'MrEngineer',
    siteDescription:
      'Learn programming and engineering skills with interactive roadmaps and challenges',
    contactEmail: '<EMAIL>',
    maxUploadSize: 10,
    maintenanceMode: false,
  });

  // TODO: Need to make this dynamic in future - fetch user settings from API
  const [userSettings, setUserSettings] = useState({
    allowRegistration: true,
    requireEmailVerification: true,
    defaultUserRole: 'USER',
    autoApproveUsers: false,
    maxLoginAttempts: 5,
  });

  // TODO: Need to make this dynamic in future - fetch content settings from API
  const [contentSettings, setContentSettings] = useState({
    moderationEnabled: true,
    allowComments: true,
    autoPublishContent: false,
    maxContentItems: 1000,
    contentReviewRequired: true,
  });

  const handleGeneralSettingsChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value, type } = e.target as HTMLInputElement;

    setGeneralSettings({
      ...generalSettings,
      [name]:
        type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    });
  };

  const handleUserSettingsChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value, type } = e.target as HTMLInputElement;

    setUserSettings({
      ...userSettings,
      [name]:
        type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    });
  };

  const handleContentSettingsChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value, type } = e.target as HTMLInputElement;

    setContentSettings({
      ...contentSettings,
      [name]:
        type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    });
  };

  const handleSaveSettings = () => {
    // TODO: Need to make this dynamic in future - implement API call to save settings
    console.log('Saving settings:', {
      generalSettings,
      userSettings,
      contentSettings,
    });
    // Show success message
    alert('Settings saved successfully!');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">System Settings</h1>
        <button
          onClick={handleSaveSettings}
          className="flex items-center gap-2 rounded-md bg-accent px-4 py-2 text-accent-foreground transition-colors hover:bg-accent/90"
        >
          <RiSaveLine /> Save All Settings
        </button>
      </div>

      {/* General Settings */}
      <div className="rounded-lg border bg-card p-6 shadow-sm">
        <h2 className="mb-4 text-lg font-semibold text-card-foreground">
          General Settings
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <label
              className="mb-1 block text-sm font-medium text-muted-foreground"
              htmlFor="siteName"
            >
              Site Name
            </label>
            <input
              type="text"
              id="siteName"
              name="siteName"
              value={generalSettings.siteName}
              onChange={handleGeneralSettingsChange}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            />
          </div>

          <div>
            <label
              className="mb-1 block text-sm font-medium text-gray-700"
              htmlFor="contactEmail"
            >
              Contact Email
            </label>
            <input
              type="email"
              id="contactEmail"
              name="contactEmail"
              value={generalSettings.contactEmail}
              onChange={handleGeneralSettingsChange}
              className="w-full rounded-md border px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div className="md:col-span-2">
            <label
              className="mb-1 block text-sm font-medium text-gray-700"
              htmlFor="siteDescription"
            >
              Site Description
            </label>
            <textarea
              id="siteDescription"
              name="siteDescription"
              value={generalSettings.siteDescription}
              onChange={handleGeneralSettingsChange}
              rows={3}
              className="w-full rounded-md border px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div>
            <label
              className="mb-1 block text-sm font-medium text-gray-700"
              htmlFor="maxUploadSize"
            >
              Max Upload Size (MB)
            </label>
            <input
              type="number"
              id="maxUploadSize"
              name="maxUploadSize"
              value={generalSettings.maxUploadSize}
              onChange={handleGeneralSettingsChange}
              className="w-full rounded-md border px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="maintenanceMode"
              name="maintenanceMode"
              checked={generalSettings.maintenanceMode}
              onChange={handleGeneralSettingsChange}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              className="ml-2 block text-sm text-gray-700"
              htmlFor="maintenanceMode"
            >
              Enable Maintenance Mode
            </label>
          </div>
        </div>
      </div>

      {/* User Settings */}
      <div className="rounded-lg border bg-card p-6 shadow-sm">
        <h2 className="mb-4 text-lg font-semibold text-card-foreground">
          User Settings
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="allowRegistration"
              name="allowRegistration"
              checked={userSettings.allowRegistration}
              onChange={handleUserSettingsChange}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              className="ml-2 block text-sm text-gray-700"
              htmlFor="allowRegistration"
            >
              Allow User Registration
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="requireEmailVerification"
              name="requireEmailVerification"
              checked={userSettings.requireEmailVerification}
              onChange={handleUserSettingsChange}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              className="ml-2 block text-sm text-gray-700"
              htmlFor="requireEmailVerification"
            >
              Require Email Verification
            </label>
          </div>

          <div>
            <label
              className="mb-1 block text-sm font-medium text-gray-700"
              htmlFor="defaultUserRole"
            >
              Default User Role
            </label>
            <select
              id="defaultUserRole"
              name="defaultUserRole"
              value={userSettings.defaultUserRole}
              onChange={handleUserSettingsChange}
              className="w-full rounded-md border bg-white px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="USER">User</option>
              <option value="CONTRIBUTOR">Contributor</option>
              <option value="MODERATOR">Moderator</option>
              <option value="ADMIN">Administrator</option>
            </select>
          </div>

          <div>
            <label
              className="mb-1 block text-sm font-medium text-gray-700"
              htmlFor="maxLoginAttempts"
            >
              Max Login Attempts
            </label>
            <input
              type="number"
              id="maxLoginAttempts"
              name="maxLoginAttempts"
              value={userSettings.maxLoginAttempts}
              onChange={handleUserSettingsChange}
              className="w-full rounded-md border px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="autoApproveUsers"
              name="autoApproveUsers"
              checked={userSettings.autoApproveUsers}
              onChange={handleUserSettingsChange}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              className="ml-2 block text-sm text-gray-700"
              htmlFor="autoApproveUsers"
            >
              Auto-approve New Users
            </label>
          </div>
        </div>
      </div>

      {/* Content Settings */}
      <div className="rounded-lg border bg-card p-6 shadow-sm">
        <h2 className="mb-4 text-lg font-semibold text-card-foreground">
          Content Settings
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="moderationEnabled"
              name="moderationEnabled"
              checked={contentSettings.moderationEnabled}
              onChange={handleContentSettingsChange}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              className="ml-2 block text-sm text-gray-700"
              htmlFor="moderationEnabled"
            >
              Enable Content Moderation
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="allowComments"
              name="allowComments"
              checked={contentSettings.allowComments}
              onChange={handleContentSettingsChange}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              className="ml-2 block text-sm text-gray-700"
              htmlFor="allowComments"
            >
              Allow Comments
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="autoPublishContent"
              name="autoPublishContent"
              checked={contentSettings.autoPublishContent}
              onChange={handleContentSettingsChange}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              className="ml-2 block text-sm text-gray-700"
              htmlFor="autoPublishContent"
            >
              Auto-publish Content
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="contentReviewRequired"
              name="contentReviewRequired"
              checked={contentSettings.contentReviewRequired}
              onChange={handleContentSettingsChange}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              className="ml-2 block text-sm text-gray-700"
              htmlFor="contentReviewRequired"
            >
              Require Content Review
            </label>
          </div>

          <div>
            <label
              className="mb-1 block text-sm font-medium text-gray-700"
              htmlFor="maxContentItems"
            >
              Max Content Items
            </label>
            <input
              type="number"
              id="maxContentItems"
              name="maxContentItems"
              value={contentSettings.maxContentItems}
              onChange={handleContentSettingsChange}
              className="w-full rounded-md border px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
        </div>
      </div>

      {/* System Actions */}
      <div className="rounded-lg border bg-card p-6 shadow-sm">
        <h2 className="mb-4 text-lg font-semibold text-card-foreground">
          System Actions
        </h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <button className="flex items-center justify-center gap-2 rounded-md border border-input px-4 py-3 text-sm text-muted-foreground transition-colors hover:bg-accent hover:text-accent-foreground">
            <RiRefreshLine /> Clear Cache
          </button>
          <button className="flex items-center justify-center gap-2 rounded-md border border-input px-4 py-3 text-sm text-muted-foreground transition-colors hover:bg-accent hover:text-accent-foreground">
            <RiRefreshLine /> Rebuild Search Index
          </button>
          <button className="flex items-center justify-center gap-2 rounded-md border border-input px-4 py-3 text-sm text-muted-foreground transition-colors hover:bg-accent hover:text-accent-foreground">
            <RiRefreshLine /> Run Database Maintenance
          </button>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSaveSettings}
          className="flex items-center gap-2 rounded-md bg-accent px-6 py-3 text-accent-foreground transition-colors hover:bg-accent/90"
        >
          <RiSaveLine /> Save All Settings
        </button>
      </div>
    </div>
  );
}
