/**
 * @file page.tsx
 * @description Notification Configuration for admin dashboard
 */
'use client';

import { useState } from 'react';
import {
  RiSaveLine,
  RiAddLine,
  RiDeleteBin6Line,
  RiNotification3Line,
} from 'react-icons/ri';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface INotificationType {
  id: string;
  name: string;
  description: string;
  category: 'system' | 'user' | 'content' | 'achievement' | 'marketing';
  channels: {
    email: boolean;
    inApp: boolean;
    push: boolean;
  };
  template: string;
  enabled: boolean;
}

function NotificationConfiguration() {
  // Mock data for notification types
  // TODO: Replace with actual API data
  const [notificationTypes, setNotificationTypes] = useState<
    INotificationType[]
  >([
    {
      id: '1',
      name: 'New Comment',
      description: 'Notify users when someone comments on their content',
      category: 'content',
      channels: {
        email: true,
        inApp: true,
        push: false,
      },
      template:
        'Someone commented on your {{contentType}}: "{{commentPreview}}"',
      enabled: true,
    },
    {
      id: '2',
      name: 'Achievement Unlocked',
      description: 'Notify users when they earn an achievement',
      category: 'achievement',
      channels: {
        email: true,
        inApp: true,
        push: true,
      },
      template:
        "Congratulations! You've earned the {{achievementName}} achievement!",
      enabled: true,
    },
    {
      id: '3',
      name: 'Roadmap Completed',
      description: 'Notify users when they complete a roadmap',
      category: 'achievement',
      channels: {
        email: true,
        inApp: true,
        push: true,
      },
      template: 'Congratulations on completing the {{roadmapName}} roadmap!',
      enabled: true,
    },
    {
      id: '4',
      name: 'Content Approved',
      description: 'Notify users when their submitted content is approved',
      category: 'content',
      channels: {
        email: true,
        inApp: true,
        push: false,
      },
      template: 'Your {{contentType}} "{{contentTitle}}" has been approved!',
      enabled: true,
    },
    {
      id: '5',
      name: 'Weekly Digest',
      description: 'Send users a weekly summary of new content and activities',
      category: 'marketing',
      channels: {
        email: true,
        inApp: false,
        push: false,
      },
      template: "Here's your weekly digest of what's new on MrEngineer",
      enabled: true,
    },
  ]);

  // Global notification settings
  const [globalSettings, setGlobalSettings] = useState({
    enableAllNotifications: true,
    defaultEmailFrequency: 'immediate',
    defaultPushEnabled: true,
    maxNotificationsPerDay: 10,
    digestDay: 'monday',
    digestTime: '09:00',
    batchNotifications: true,
    batchInterval: 15, // minutes
  });

  const [selectedType, setSelectedType] = useState<INotificationType | null>(
    null,
  );
  const [editedType, setEditedType] = useState<INotificationType | null>(null);
  const [isNewTypeDialogOpen, setIsNewTypeDialogOpen] = useState(false);
  const [newType, setNewType] = useState<Omit<INotificationType, 'id'>>({
    name: '',
    description: '',
    category: 'user',
    channels: { email: true, inApp: true, push: false },
    template: '',
    enabled: true,
  });

  const handleTypeSelect = (type: INotificationType) => {
    setSelectedType(type);
    setEditedType(type);
  };

  const handleEditChange = (field: keyof INotificationType, value: any) => {
    if (editedType) {
      setEditedType({
        ...editedType,
        [field]: value,
      });
    }
  };

  const handleChannelChange = (
    channel: keyof INotificationType['channels'],
    value: boolean,
  ) => {
    if (editedType) {
      setEditedType({
        ...editedType,
        channels: {
          ...editedType.channels,
          [channel]: value,
        },
      });
    }
  };

  const handleSaveType = () => {
    if (editedType) {
      setNotificationTypes((types) =>
        types.map((t) => (t.id === editedType.id ? editedType : t)),
      );
      setSelectedType(editedType);
      // TODO: Save to API
      alert('Notification type saved successfully!');
    }
  };

  const handleDeleteType = (id: string) => {
    if (confirm('Are you sure you want to delete this notification type?')) {
      setNotificationTypes((types) => types.filter((t) => t.id !== id));
      setSelectedType(null);
      setEditedType(null);
      // TODO: Delete from API
      alert('Notification type deleted successfully!');
    }
  };

  const handleCreateType = () => {
    const newId = `notification-${Date.now()}`;
    const createdType = {
      ...newType,
      id: newId,
    };

    setNotificationTypes([...notificationTypes, createdType]);
    setSelectedType(createdType);
    setEditedType(createdType);
    setIsNewTypeDialogOpen(false);
    setNewType({
      name: '',
      description: '',
      category: 'user',
      channels: { email: true, inApp: true, push: false },
      template: '',
      enabled: true,
    });
    // TODO: Save to API
  };

  const handleGlobalSettingChange = (field: string, value: any) => {
    setGlobalSettings({
      ...globalSettings,
      [field]: value,
    });
  };

  const handleSaveGlobalSettings = () => {
    // TODO: Save global settings to API
    console.log('Saving global notification settings:', globalSettings);
    alert('Global notification settings saved successfully!');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Notification Configuration</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsNewTypeDialogOpen(true)}
          >
            <RiAddLine className="mr-2" /> New Notification Type
          </Button>
        </div>
      </div>

      <Tabs defaultValue="types" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="types">Notification Types</TabsTrigger>
          <TabsTrigger value="settings">Global Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="types" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
            {/* Notification Types List */}
            <div className="space-y-4 md:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Notification Types</CardTitle>
                  <CardDescription>Select a type to edit</CardDescription>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {notificationTypes.map((type) => (
                      <div
                        key={type.id}
                        className={`cursor-pointer p-4 hover:bg-accent ${selectedType?.id === type.id ? 'bg-accent' : ''}`}
                        onClick={() => handleTypeSelect(type)}
                      >
                        <div className="font-medium">{type.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {type.category}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Notification Type Editor */}
            <div className="md:col-span-3">
              {selectedType ? (
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <div>
                      <CardTitle>{editedType?.name}</CardTitle>
                      <CardDescription>Edit notification type</CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteType(selectedType.id)}
                      >
                        <RiDeleteBin6Line className="mr-2" /> Delete
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Name</label>
                      <Input
                        value={editedType?.name || ''}
                        onChange={(e) =>
                          handleEditChange('name', e.target.value)
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Description</label>
                      <Textarea
                        value={editedType?.description || ''}
                        onChange={(e) =>
                          handleEditChange('description', e.target.value)
                        }
                        rows={2}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Category</label>
                      <Select
                        value={editedType?.category}
                        onValueChange={(value) =>
                          handleEditChange('category', value as any)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="system">System</SelectItem>
                          <SelectItem value="user">User</SelectItem>
                          <SelectItem value="content">Content</SelectItem>
                          <SelectItem value="achievement">
                            Achievement
                          </SelectItem>
                          <SelectItem value="marketing">Marketing</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Notification Template
                      </label>
                      <Textarea
                        value={editedType?.template || ''}
                        onChange={(e) =>
                          handleEditChange('template', e.target.value)
                        }
                        rows={3}
                        placeholder="Use {{variable}} for dynamic content"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Delivery Channels
                      </label>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <label className="text-sm">Email</label>
                          <Switch
                            checked={editedType?.channels.email || false}
                            onCheckedChange={(checked) =>
                              handleChannelChange('email', checked)
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <label className="text-sm">In-App</label>
                          <Switch
                            checked={editedType?.channels.inApp || false}
                            onCheckedChange={(checked) =>
                              handleChannelChange('inApp', checked)
                            }
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <label className="text-sm">Push Notification</label>
                          <Switch
                            checked={editedType?.channels.push || false}
                            onCheckedChange={(checked) =>
                              handleChannelChange('push', checked)
                            }
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <label className="text-sm font-medium">Enabled</label>
                      <Switch
                        checked={editedType?.enabled || false}
                        onCheckedChange={(checked) =>
                          handleEditChange('enabled', checked)
                        }
                      />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button onClick={handleSaveType}>
                      <RiSaveLine className="mr-2" /> Save Changes
                    </Button>
                  </CardFooter>
                </Card>
              ) : (
                <Card>
                  <CardContent className="flex h-[400px] flex-col items-center justify-center p-6">
                    <p className="text-muted-foreground">
                      Select a notification type from the list or create a new
                      one
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Global Notification Settings</CardTitle>
              <CardDescription>
                Configure system-wide notification behavior
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">
                    Enable All Notifications
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Master switch for all notification types
                  </p>
                </div>
                <Switch
                  checked={globalSettings.enableAllNotifications}
                  onCheckedChange={(checked) =>
                    handleGlobalSettingChange('enableAllNotifications', checked)
                  }
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">
                  Default Delivery Settings
                </h3>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm">Default Email Frequency</label>
                    <Select
                      value={globalSettings.defaultEmailFrequency}
                      onValueChange={(value) =>
                        handleGlobalSettingChange(
                          'defaultEmailFrequency',
                          value,
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="immediate">Immediate</SelectItem>
                        <SelectItem value="hourly">Hourly Digest</SelectItem>
                        <SelectItem value="daily">Daily Digest</SelectItem>
                        <SelectItem value="weekly">Weekly Digest</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm">
                      Default Push Notifications
                    </label>
                    <Switch
                      checked={globalSettings.defaultPushEnabled}
                      onCheckedChange={(checked) =>
                        handleGlobalSettingChange('defaultPushEnabled', checked)
                      }
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Rate Limiting</h3>
                <div className="space-y-2">
                  <label className="text-sm">
                    Maximum Notifications Per Day
                  </label>
                  <Input
                    type="number"
                    value={globalSettings.maxNotificationsPerDay}
                    onChange={(e) =>
                      handleGlobalSettingChange(
                        'maxNotificationsPerDay',
                        parseInt(e.target.value),
                      )
                    }
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Digest Settings</h3>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm">Weekly Digest Day</label>
                    <Select
                      value={globalSettings.digestDay}
                      onValueChange={(value) =>
                        handleGlobalSettingChange('digestDay', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monday">Monday</SelectItem>
                        <SelectItem value="tuesday">Tuesday</SelectItem>
                        <SelectItem value="wednesday">Wednesday</SelectItem>
                        <SelectItem value="thursday">Thursday</SelectItem>
                        <SelectItem value="friday">Friday</SelectItem>
                        <SelectItem value="saturday">Saturday</SelectItem>
                        <SelectItem value="sunday">Sunday</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm">Digest Time</label>
                    <Input
                      type="time"
                      value={globalSettings.digestTime}
                      onChange={(e) =>
                        handleGlobalSettingChange('digestTime', e.target.value)
                      }
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-sm font-medium">Batch Notifications</h3>
                    <p className="text-sm text-muted-foreground">
                      Combine multiple notifications into a single delivery
                    </p>
                  </div>
                  <Switch
                    checked={globalSettings.batchNotifications}
                    onCheckedChange={(checked) =>
                      handleGlobalSettingChange('batchNotifications', checked)
                    }
                  />
                </div>

                {globalSettings.batchNotifications && (
                  <div className="space-y-2">
                    <label className="text-sm">Batch Interval (minutes)</label>
                    <Select
                      value={globalSettings.batchInterval.toString()}
                      onValueChange={(value) =>
                        handleGlobalSettingChange(
                          'batchInterval',
                          parseInt(value),
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select interval" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 minutes</SelectItem>
                        <SelectItem value="15">15 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="60">1 hour</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveGlobalSettings}>
                <RiSaveLine className="mr-2" /> Save Global Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* New Notification Type Dialog */}
      <Dialog open={isNewTypeDialogOpen} onOpenChange={setIsNewTypeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Notification Type</DialogTitle>
            <DialogDescription>
              Create a new notification type for the system
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Name</label>
              <Input
                value={newType.name}
                onChange={(e) =>
                  setNewType({ ...newType, name: e.target.value })
                }
                placeholder="Enter notification type name"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea
                value={newType.description}
                onChange={(e) =>
                  setNewType({ ...newType, description: e.target.value })
                }
                placeholder="Enter description"
                rows={2}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Category</label>
              <Select
                value={newType.category}
                onValueChange={(value) =>
                  setNewType({ ...newType, category: value as any })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">System</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="content">Content</SelectItem>
                  <SelectItem value="achievement">Achievement</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Template</label>
              <Textarea
                value={newType.template}
                onChange={(e) =>
                  setNewType({ ...newType, template: e.target.value })
                }
                placeholder="Use {{variable}} for dynamic content"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Delivery Channels</label>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm">Email</label>
                  <Switch
                    checked={newType.channels.email}
                    onCheckedChange={(checked) =>
                      setNewType({
                        ...newType,
                        channels: { ...newType.channels, email: checked },
                      })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm">In-App</label>
                  <Switch
                    checked={newType.channels.inApp}
                    onCheckedChange={(checked) =>
                      setNewType({
                        ...newType,
                        channels: { ...newType.channels, inApp: checked },
                      })
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm">Push Notification</label>
                  <Switch
                    checked={newType.channels.push}
                    onCheckedChange={(checked) =>
                      setNewType({
                        ...newType,
                        channels: { ...newType.channels, push: checked },
                      })
                    }
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsNewTypeDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateType}
              disabled={!newType.name || !newType.template}
            >
              Create Notification Type
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default NotificationConfiguration;
