/**
 * @file page.tsx
 * @description Backup and Restore Tools for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiDownload2Line,
  RiUpload2Line,
  RiHistoryLine,
  RiRefreshLine,
  RiCalendarLine,
  RiTimeLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Tit<PERSON>,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';

interface IBackupHistory {
  id: string;
  filename: string;
  size: string;
  type: 'automatic' | 'manual';
  date: string;
  status: 'completed' | 'failed';
  downloadUrl?: string;
}

function BackupRestoreTools() {
  // Mock data for backup history
  // TODO: Replace with actual API data
  const [backupHistory, setBackupHistory] = useState<IBackupHistory[]>([
    {
      id: '1',
      filename: 'backup-20250523-120000.zip',
      size: '156.4 MB',
      type: 'automatic',
      date: '2025-05-23 12:00:00',
      status: 'completed',
      downloadUrl: '#',
    },
    {
      id: '2',
      filename: 'backup-20250522-120000.zip',
      size: '155.2 MB',
      type: 'automatic',
      date: '2025-05-22 12:00:00',
      status: 'completed',
      downloadUrl: '#',
    },
    {
      id: '3',
      filename: 'backup-20250521-120000.zip',
      size: '154.8 MB',
      type: 'automatic',
      date: '2025-05-21 12:00:00',
      status: 'completed',
      downloadUrl: '#',
    },
    {
      id: '4',
      filename: 'manual-backup-20250520-150000.zip',
      size: '154.5 MB',
      type: 'manual',
      date: '2025-05-20 15:00:00',
      status: 'completed',
      downloadUrl: '#',
    },
    {
      id: '5',
      filename: 'backup-20250520-120000.zip',
      size: '154.2 MB',
      type: 'automatic',
      date: '2025-05-20 12:00:00',
      status: 'failed',
    },
  ]);

  // Backup configuration
  const [backupConfig, setBackupConfig] = useState({
    automaticBackups: true,
    backupFrequency: 'daily',
    backupTime: '12:00',
    retentionPeriod: 30, // days
    includeUploads: true,
    includeUserData: true,
    includeSettings: true,
    compressionLevel: 'medium',
    encryptBackups: true,
    storageLocation: 'local', // local, s3, etc.
    notifyOnFailure: true,
    maxBackupSize: 500, // MB
  });

  // Restore settings
  const [selectedBackup, setSelectedBackup] = useState<string>('');
  const [restoreOptions, setRestoreOptions] = useState({
    includeUploads: true,
    includeUserData: true,
    includeSettings: true,
    overwriteExisting: false,
    preserveUsers: true,
  });

  // UI states
  const [isBackupInProgress, setIsBackupInProgress] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);
  const [isRestoreInProgress, setIsRestoreInProgress] = useState(false);
  const [restoreProgress, setRestoreProgress] = useState(0);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const handleConfigChange = (field: string, value: any) => {
    setBackupConfig({
      ...backupConfig,
      [field]: value,
    });
  };

  const handleRestoreOptionChange = (field: string, value: boolean) => {
    setRestoreOptions({
      ...restoreOptions,
      [field]: value,
    });
  };

  const handleSaveConfig = () => {
    // TODO: Save backup configuration to API
    console.log('Saving backup configuration:', backupConfig);
    alert('Backup configuration saved successfully!');
  };

  const handleManualBackup = () => {
    // Simulate backup process
    setIsBackupInProgress(true);
    setBackupProgress(0);

    const interval = setInterval(() => {
      setBackupProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsBackupInProgress(false);

          // Add new backup to history
          const now = new Date();
          const formattedDate = now
            .toISOString()
            .replace('T', ' ')
            .substring(0, 19);
          const filename = `manual-backup-${now
            .toISOString()
            .replace(/[\\:\\-\\.T]/g, '')
            .substring(0, 14)}.zip`;

          setBackupHistory((prev) => [
            {
              id: `backup-${Date.now()}`,
              filename,
              size: '156.7 MB',
              type: 'manual',
              date: formattedDate,
              status: 'completed',
              downloadUrl: '#',
            },
            ...prev,
          ]);

          alert('Manual backup completed successfully!');
          return 100;
        }
        return prev + 5;
      });
    }, 300);

    // TODO: Implement actual backup process with API
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setUploadedFile(e.target.files[0]);
    }
  };

  const handleRestore = () => {
    // Simulate restore process
    setIsRestoreInProgress(true);
    setRestoreProgress(0);
    setIsRestoreDialogOpen(false);

    const interval = setInterval(() => {
      setRestoreProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsRestoreInProgress(false);
          alert('Restore completed successfully! The system will now restart.');
          return 100;
        }
        return prev + 2;
      });
    }, 200);

    // TODO: Implement actual restore process with API
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Backup & Restore Tools</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleManualBackup}
            disabled={isBackupInProgress}
          >
            <RiDownload2Line className="mr-2" /> Create Manual Backup
          </Button>
        </div>
      </div>

      {(isBackupInProgress || isRestoreInProgress) && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            {isBackupInProgress && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Backup in progress...</h3>
                  <span className="text-sm">{backupProgress}%</span>
                </div>
                <Progress value={backupProgress} className="h-2" />
              </div>
            )}

            {isRestoreInProgress && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">
                    Restore in progress...
                  </h3>
                  <span className="text-sm">{restoreProgress}%</span>
                </div>
                <Progress value={restoreProgress} className="h-2" />
                <p className="text-sm text-muted-foreground">
                  Please do not close this window. The system will restart
                  automatically when the restore is complete.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="history" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="history">Backup History</TabsTrigger>
          <TabsTrigger value="config">Backup Configuration</TabsTrigger>
          <TabsTrigger value="restore">Restore System</TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Backup History</CardTitle>
              <CardDescription>
                View and download previous backups
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Filename</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {backupHistory.map((backup) => (
                    <TableRow key={backup.id}>
                      <TableCell>{backup.filename}</TableCell>
                      <TableCell>{backup.size}</TableCell>
                      <TableCell>
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${backup.type === 'automatic' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`}
                        >
                          {backup.type === 'automatic' ? 'Automatic' : 'Manual'}
                        </span>
                      </TableCell>
                      <TableCell>{backup.date}</TableCell>
                      <TableCell>
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${backup.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                        >
                          {backup.status === 'completed'
                            ? 'Completed'
                            : 'Failed'}
                        </span>
                      </TableCell>
                      <TableCell>
                        {backup.status === 'completed' && (
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline" asChild>
                              <a href={backup.downloadUrl} download>
                                <RiDownload2Line className="mr-1" /> Download
                              </a>
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedBackup(backup.id);
                                setIsRestoreDialogOpen(true);
                              }}
                            >
                              <RiHistoryLine className="mr-1" /> Restore
                            </Button>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Backup Configuration</CardTitle>
              <CardDescription>
                Configure automated backup settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Automatic Backups</h3>
                  <p className="text-sm text-muted-foreground">
                    Enable scheduled backups
                  </p>
                </div>
                <Switch
                  checked={backupConfig.automaticBackups}
                  onCheckedChange={(checked) =>
                    handleConfigChange('automaticBackups', checked)
                  }
                />
              </div>

              {backupConfig.automaticBackups && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Backup Frequency
                      </label>
                      <Select
                        value={backupConfig.backupFrequency}
                        onValueChange={(value) =>
                          handleConfigChange('backupFrequency', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hourly">Hourly</SelectItem>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Backup Time</label>
                      <Input
                        type="time"
                        value={backupConfig.backupTime}
                        onChange={(e) =>
                          handleConfigChange('backupTime', e.target.value)
                        }
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Retention Period (days)
                    </label>
                    <Input
                      type="number"
                      value={backupConfig.retentionPeriod}
                      onChange={(e) =>
                        handleConfigChange(
                          'retentionPeriod',
                          parseInt(e.target.value),
                        )
                      }
                    />
                    <p className="text-xs text-muted-foreground">
                      Backups older than this will be automatically deleted
                    </p>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Backup Content</h3>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Include User Uploads</label>
                    <Switch
                      checked={backupConfig.includeUploads}
                      onCheckedChange={(checked) =>
                        handleConfigChange('includeUploads', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Include User Data</label>
                    <Switch
                      checked={backupConfig.includeUserData}
                      onCheckedChange={(checked) =>
                        handleConfigChange('includeUserData', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Include System Settings</label>
                    <Switch
                      checked={backupConfig.includeSettings}
                      onCheckedChange={(checked) =>
                        handleConfigChange('includeSettings', checked)
                      }
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Advanced Settings</h3>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Compression Level
                    </label>
                    <Select
                      value={backupConfig.compressionLevel}
                      onValueChange={(value) =>
                        handleConfigChange('compressionLevel', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Storage Location
                    </label>
                    <Select
                      value={backupConfig.storageLocation}
                      onValueChange={(value) =>
                        handleConfigChange('storageLocation', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="local">Local Storage</SelectItem>
                        <SelectItem value="s3">Amazon S3</SelectItem>
                        <SelectItem value="gcs">
                          Google Cloud Storage
                        </SelectItem>
                        <SelectItem value="azure">
                          Azure Blob Storage
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <label className="text-sm">Encrypt Backups</label>
                  <Switch
                    checked={backupConfig.encryptBackups}
                    onCheckedChange={(checked) =>
                      handleConfigChange('encryptBackups', checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <label className="text-sm">Notify on Failure</label>
                  <Switch
                    checked={backupConfig.notifyOnFailure}
                    onCheckedChange={(checked) =>
                      handleConfigChange('notifyOnFailure', checked)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Maximum Backup Size (MB)
                  </label>
                  <Input
                    type="number"
                    value={backupConfig.maxBackupSize}
                    onChange={(e) =>
                      handleConfigChange(
                        'maxBackupSize',
                        parseInt(e.target.value),
                      )
                    }
                  />
                  <p className="text-xs text-muted-foreground">
                    0 for unlimited
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveConfig}>Save Configuration</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="restore" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Restore System</CardTitle>
              <CardDescription>
                Restore the system from a backup file
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Upload Backup File</h3>
                <div className="flex items-center space-x-2">
                  <Input
                    type="file"
                    accept=".zip,.sql,.gz,.tar,.bz2"
                    onChange={handleFileUpload}
                  />
                  <Button
                    variant="outline"
                    onClick={() => setIsRestoreDialogOpen(true)}
                    disabled={!uploadedFile}
                  >
                    <RiUpload2Line className="mr-2" /> Restore
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  Upload a backup file to restore the system
                </p>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Restore Options</h3>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Include User Uploads</label>
                    <Switch
                      checked={restoreOptions.includeUploads}
                      onCheckedChange={(checked) =>
                        handleRestoreOptionChange('includeUploads', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Include User Data</label>
                    <Switch
                      checked={restoreOptions.includeUserData}
                      onCheckedChange={(checked) =>
                        handleRestoreOptionChange('includeUserData', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm">Include System Settings</label>
                    <Switch
                      checked={restoreOptions.includeSettings}
                      onCheckedChange={(checked) =>
                        handleRestoreOptionChange('includeSettings', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm">Overwrite Existing Data</label>
                      <p className="text-xs text-muted-foreground">
                        If disabled, will only restore missing data
                      </p>
                    </div>
                    <Switch
                      checked={restoreOptions.overwriteExisting}
                      onCheckedChange={(checked) =>
                        handleRestoreOptionChange('overwriteExisting', checked)
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm">Preserve Current Users</label>
                      <p className="text-xs text-muted-foreground">
                        Keep existing user accounts
                      </p>
                    </div>
                    <Switch
                      checked={restoreOptions.preserveUsers}
                      onCheckedChange={(checked) =>
                        handleRestoreOptionChange('preserveUsers', checked)
                      }
                    />
                  </div>
                </div>
              </div>

              <div className="rounded-md border border-amber-200 bg-amber-50 p-4">
                <h3 className="text-sm font-medium text-amber-800">Warning</h3>
                <p className="mt-1 text-sm text-amber-700">
                  Restoring from a backup will replace your current data. This
                  process cannot be undone. Make sure to create a backup of your
                  current system before proceeding.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Restore Confirmation Dialog */}
      <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Restore</DialogTitle>
            <DialogDescription>
              Are you sure you want to restore the system? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="rounded-md border border-amber-200 bg-amber-50 p-4">
              <p className="text-sm text-amber-700">
                <strong>Warning:</strong> This will replace your current data
                according to the selected options. The system will be
                temporarily unavailable during the restore process.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Selected Options:</h3>
              <ul className="list-inside list-disc space-y-1 text-sm">
                <li>
                  Include User Uploads:{' '}
                  {restoreOptions.includeUploads ? 'Yes' : 'No'}
                </li>
                <li>
                  Include User Data:{' '}
                  {restoreOptions.includeUserData ? 'Yes' : 'No'}
                </li>
                <li>
                  Include System Settings:{' '}
                  {restoreOptions.includeSettings ? 'Yes' : 'No'}
                </li>
                <li>
                  Overwrite Existing Data:{' '}
                  {restoreOptions.overwriteExisting ? 'Yes' : 'No'}
                </li>
                <li>
                  Preserve Current Users:{' '}
                  {restoreOptions.preserveUsers ? 'Yes' : 'No'}
                </li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRestoreDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleRestore}>
              Confirm Restore
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default BackupRestoreTools;
