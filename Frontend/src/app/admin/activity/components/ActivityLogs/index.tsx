import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableHeader,
} from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { IActivityLog } from '../../types';
import { Button } from '@/components/ui/button';
import { RiArrowGoBackLine } from 'react-icons/ri';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

interface IProps {
  logs: IActivityLog[];
  activeTab: string;
  setActiveTab: (tab: string) => void;
  setSelectedLog: (log: IActivityLog | null) => void;
  setIsDetailDialogOpen: (open: boolean) => void;
  getStatusColor: (status: string) => string;
  handleUndoAction: (log: IActivityLog) => void;
  isLoading?: boolean;
}

export default function ActivityLogs({
  logs,
  activeTab,
  setActiveTab,
  setSelectedLog,
  setIsDetailDialogOpen,
  getStatusColor,
  handleUndoAction,
  isLoading = false,
}: IProps) {
  const filteredLogs = logs.filter((log) => log.targetType === activeTab);

  const handleViewDetails = (log: IActivityLog) => {
    setSelectedLog(log);
    setIsDetailDialogOpen(true);
  };

  const getActionTypeColor = (action: string) => {
    if (action.includes('delete')) return 'bg-red-100 text-red-800';
    if (action.includes('create') || action.includes('add'))
      return 'bg-green-100 text-green-800';
    if (action.includes('update') || action.includes('edit'))
      return 'bg-blue-100 text-blue-800';
    if (action.includes('approve')) return 'bg-green-100 text-green-800';
    if (action.includes('reject')) return 'bg-red-100 text-red-800';
    if (action.includes('suspend')) return 'bg-orange-100 text-orange-800';
    if (action.includes('undo')) return 'bg-purple-100 text-purple-800';
    return 'bg-gray-100 text-gray-800';
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Activity Logs</CardTitle>
        <CardDescription>
          Showing {filteredLogs.length} activity logs
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="user">Users</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="roadmap">Roadmaps</TabsTrigger>
            <TabsTrigger value="challenge">Challenges</TabsTrigger>
            <TabsTrigger value="comment">Comments</TabsTrigger>
            <TabsTrigger value="setting">Settings</TabsTrigger>
            <TabsTrigger value="system">System</TabsTrigger>
          </TabsList>

          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Admin</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Target</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell className="whitespace-nowrap">
                      {new Date(log.timestamp).toLocaleString()}
                    </TableCell>
                    <TableCell>{log.adminName}</TableCell>
                    <TableCell>
                      <Badge className={getActionTypeColor(log.action)}>
                        {log.action}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{log.targetName}</span>
                        <span className="text-xs text-muted-foreground">
                          {log.targetType}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(log.status)}>
                        {log.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetails(log)}
                        >
                          Details
                        </Button>
                        {log.reversible && log.status === 'success' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleUndoAction(log)}
                          >
                            <RiArrowGoBackLine className="mr-1" /> Undo
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredLogs.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="py-4 text-center">
                      No activity logs found matching the filters
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}
