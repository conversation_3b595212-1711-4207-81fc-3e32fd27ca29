'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  Di<PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { RiArrowGoBackLine } from 'react-icons/ri';
import { IActivityLog } from '../../types';

interface IUndoConfirmation {
  isUndoDialogOpen: boolean;
  setIsUndoDialogOpen: (open: boolean) => void;
  selectedLog: IActivityLog | null;
  activityLogs: IActivityLog[];
  setActivityLogs: (logs: IActivityLog[]) => void;
}

export default function UndoConfirmation({
  isUndoDialogOpen,
  setIsUndoDialogOpen,
  selectedLog,
  activityLogs,
  setActivityLogs,
}: IUndoConfirmation) {
  const confirmUndoAction = () => {
    // TODO: Implement actual undo functionality via API
    console.log(
      `Undoing action: ${selectedLog?.action} for ${selectedLog?.targetName}`,
    );

    // For demonstration, we'll just add a new log entry for the undo action
    if (selectedLog) {
      const undoLog: IActivityLog = {
        id: `undo-${Date.now()}`,
        action: `${selectedLog.action}.undo`,
        adminId: 'admin123', // Current admin
        adminName: 'Admin User', // Current admin name
        timestamp: new Date().toISOString(),
        ipAddress: '***********',
        targetType: selectedLog.targetType,
        targetId: selectedLog.targetId,
        targetName: selectedLog.targetName,
        details: `Undoing action: ${selectedLog.action} - ${selectedLog.details}`,
        reversible: false,
        status: 'success',
      };

      setActivityLogs([undoLog, ...activityLogs]);
    }

    setIsUndoDialogOpen(false);
  };
  return (
    <Dialog open={isUndoDialogOpen} onOpenChange={setIsUndoDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Confirm Undo Action</DialogTitle>
          <DialogDescription>
            Are you sure you want to undo this action? This will revert the
            changes made.
          </DialogDescription>
        </DialogHeader>
        {selectedLog && (
          <div className="py-4">
            <div className="rounded-md bg-accent p-4">
              <p className="text-sm font-medium">Action to undo:</p>
              <p className="mt-1 text-sm">
                {selectedLog.action} - {selectedLog.details}
              </p>
              <p className="mt-2 text-sm">
                <span className="font-medium">Target:</span>{' '}
                {selectedLog.targetName} ({selectedLog.targetType})
              </p>
              <p className="mt-1 text-sm">
                <span className="font-medium">Performed by:</span>{' '}
                {selectedLog.adminName} at{' '}
                {new Date(selectedLog.timestamp).toLocaleString()}
              </p>
            </div>
          </div>
        )}
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsUndoDialogOpen(false)}>
            Cancel
          </Button>
          <Button variant="default" onClick={confirmUndoAction}>
            <RiArrowGoBackLine className="mr-2" /> Confirm Undo
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
