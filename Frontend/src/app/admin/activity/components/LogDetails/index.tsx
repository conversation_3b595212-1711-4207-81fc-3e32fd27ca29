import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { IActivityLog } from '../../types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RiArrowGoBackLine } from 'react-icons/ri';

interface ILogDetails {
  selectedLog: IActivityLog | null;
  isDetailDialogOpen: boolean;
  setIsDetailDialogOpen: (open: boolean) => void;
  getStatusColor: (status: string) => string;
  handleUndoAction: (log: IActivityLog) => void;
}

export default function LogDetails({
  selectedLog,
  isDetailDialogOpen,
  setIsDetailDialogOpen,
  getStatusColor,
  handleUndoAction,
}: ILogDetails) {
  return (
    <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Activity Log Details</DialogTitle>
          <DialogDescription>
            Detailed information about this activity
          </DialogDescription>
        </DialogHeader>
        {selectedLog && (
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium">Action</h3>
                <p className="text-sm">{selectedLog.action}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Timestamp</h3>
                <p className="text-sm">
                  {new Date(selectedLog.timestamp).toLocaleString()}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Admin</h3>
                <p className="text-sm">{selectedLog.adminName}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">IP Address</h3>
                <p className="text-sm">{selectedLog.ipAddress}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Target Type</h3>
                <p className="text-sm">{selectedLog.targetType}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Target ID</h3>
                <p className="text-sm">{selectedLog.targetId}</p>
              </div>
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Target Name</h3>
                <p className="text-sm">{selectedLog.targetName}</p>
              </div>
              <div className="col-span-2">
                <h3 className="text-sm font-medium">Details</h3>
                <p className="text-sm">{selectedLog.details}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium">Status</h3>
                <Badge className={getStatusColor(selectedLog.status)}>
                  {selectedLog.status}
                </Badge>
              </div>
              <div>
                <h3 className="text-sm font-medium">Reversible</h3>
                <p className="text-sm">
                  {selectedLog.reversible ? 'Yes' : 'No'}
                </p>
              </div>
            </div>
          </div>
        )}
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsDetailDialogOpen(false)}
          >
            Close
          </Button>
          {selectedLog?.reversible && selectedLog?.status === 'success' && (
            <Button
              variant="default"
              onClick={() => {
                setIsDetailDialogOpen(false);
                handleUndoAction(selectedLog);
              }}
            >
              <RiArrowGoBackLine className="mr-2" /> Undo Action
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
