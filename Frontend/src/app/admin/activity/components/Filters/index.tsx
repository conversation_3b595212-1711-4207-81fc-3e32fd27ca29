import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RiFileDownloadLine, RiRefreshLine } from 'react-icons/ri';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface IFiltersProps {
  filters: {
    adminId: string;
    action: string;
    targetType: string;
    dateFrom: string;
    dateTo: string;
    status: string;
  };
  fetchActivityLogs: () => void;
  isLoading: boolean;
  exportLogs: () => void;
  setFilters: (filters: IFiltersProps['filters']) => void;
}

export default function Filters({
  filters,
  fetchActivityLogs,
  isLoading,
  exportLogs,
  setFilters,
}: IFiltersProps) {
  const handleFilterChange = (key: string, value: string) => {
    setFilters({
      ...filters,
      [key]: value,
    });
  };

  const resetFilters = () => {
    setFilters({
      adminId: '',
      action: '',
      targetType: '',
      dateFrom: '',
      dateTo: '',
      status: '',
    });
  };
  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Activity Filters</CardTitle>
            <CardDescription>
              Filter activity logs by various criteria
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={exportLogs}
            >
              <RiFileDownloadLine className="h-4 w-4" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={fetchActivityLogs}
              disabled={isLoading}
            >
              <RiRefreshLine
                className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <label className="text-sm font-medium">Admin</label>
            <Input
              placeholder="Search by admin ID or name"
              value={filters.adminId}
              onChange={(e) => handleFilterChange('adminId', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Action</label>
            <Select
              value={filters.action}
              onValueChange={(value) => handleFilterChange('action', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select action type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Actions</SelectItem>
                <SelectItem value="create">Create</SelectItem>
                <SelectItem value="update">Update</SelectItem>
                <SelectItem value="delete">Delete</SelectItem>
                <SelectItem value="approve">Approve</SelectItem>
                <SelectItem value="reject">Reject</SelectItem>
                <SelectItem value="suspend">Suspend</SelectItem>
                <SelectItem value="undo">Undo</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Target Type</label>
            <Select
              value={filters.targetType}
              onValueChange={(value) => handleFilterChange('targetType', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select target type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                <SelectItem value="user">User</SelectItem>
                <SelectItem value="content">Content</SelectItem>
                <SelectItem value="roadmap">Roadmap</SelectItem>
                <SelectItem value="challenge">Challenge</SelectItem>
                <SelectItem value="comment">Comment</SelectItem>
                <SelectItem value="setting">Setting</SelectItem>
                <SelectItem value="system">System</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Date From</label>
            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Date To</label>
            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Status</label>
            <Select
              value={filters.status}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Statuses</SelectItem>
                <SelectItem value="success">Success</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="mt-4 flex justify-end">
          <Button variant="outline" onClick={resetFilters} className="mr-2">
            Reset Filters
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
