/**
 * @file page.tsx
 * @description Admin Activity Logging and Audit Trail
 */
'use client';

import { useState, useEffect, useCallback } from 'react';
import { RiRefreshLine, RiFileDownloadLine } from 'react-icons/ri';
import { Button } from '@/components/ui/button';

import { useAxiosGet } from '@/hooks/useAxios';
import { IActivityLog } from './types';
import LogDetails from './components/LogDetails';
import UndoConfirmation from './components/UndoConfirmation';
import ActivityLogs from './components/ActivityLogs';
import Filters from './components/Filters';

function AdminActivityLogging() {
  // Fetch activity logs from API
  const [activityLogs, setActivityLogs] = useState<IActivityLog[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Use the existing useAxiosGet hook
  const [fetchLogs] = useAxiosGet<IActivityLog[]>('/admin/audit/logs');

  const [filters, setFilters] = useState({
    adminId: '',
    action: '',
    targetType: '',
    dateFrom: '',
    dateTo: '',
    status: '',
  });

  const [activeTab, setActiveTab] = useState('all');
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Fetch activity logs
  const fetchActivityLogs = useCallback(async () => {
    setError(null);
    setIsLoading(true);

    try {
      // Apply any filters as query parameters
      const params: Record<string, string> = {};

      if (filters.adminId) params.adminId = filters.adminId;
      if (filters.action) params.action = filters.action;
      if (filters.targetType) params.targetType = filters.targetType;
      if (filters.dateFrom) params.dateFrom = filters.dateFrom;
      if (filters.dateTo) params.dateTo = filters.dateTo;
      if (filters.status) params.status = filters.status;

      const response = await fetchLogs({ params });

      if (response.success && response.data) {
        setActivityLogs(response.data);
        setLastUpdated(new Date());
      } else {
        setError('Failed to fetch activity logs');
      }
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      setError('Failed to load activity logs. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [fetchLogs, filters]);

  // Fetch logs when component mounts or filters change
  useEffect(() => {
    fetchActivityLogs();
  }, [fetchActivityLogs]);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<IActivityLog | null>(null);
  const [isUndoDialogOpen, setIsUndoDialogOpen] = useState(false);

  const filterLogs = () => {
    let filtered = [...activityLogs];

    if (filters.adminId) {
      filtered = filtered.filter(
        (log) =>
          log.adminId === filters.adminId ||
          log.adminName.toLowerCase().includes(filters.adminId.toLowerCase()),
      );
    }

    if (filters.action) {
      filtered = filtered.filter((log) => log.action.includes(filters.action));
    }

    if (filters.targetType) {
      filtered = filtered.filter(
        (log) => log.targetType === filters.targetType,
      );
    }

    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter((log) => new Date(log.timestamp) >= fromDate);
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      toDate.setHours(23, 59, 59, 999); // End of the day
      filtered = filtered.filter((log) => new Date(log.timestamp) <= toDate);
    }

    if (filters.status) {
      filtered = filtered.filter((log) => log.status === filters.status);
    }

    if (activeTab !== 'all') {
      filtered = filtered.filter((log) => log.targetType === activeTab);
    }

    return filtered;
  };

  const filteredLogs = filterLogs();

  const handleUndoAction = (log: IActivityLog) => {
    setSelectedLog(log);
    setIsUndoDialogOpen(true);
  };

  const exportLogs = () => {
    // Export the filtered logs to a JSON file
    const exportData = filteredLogs.map((log) => ({
      ID: log.id,
      Action: log.action,
      Admin: log.adminName,
      Timestamp: log.timestamp,
      'IP Address': log.ipAddress,
      'Target Type': log.targetType,
      'Target Name': log.targetName,
      Details: log.details,
      Status: log.status,
    }));

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `activity-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Activity Logs</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportLogs}>
            <RiFileDownloadLine className="mr-2" /> Export Logs
          </Button>
          <Button
            variant="outline"
            onClick={() => setActivityLogs([...activityLogs])}
          >
            <RiRefreshLine className="mr-2" /> Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Filters
        filters={filters}
        fetchActivityLogs={fetchActivityLogs}
        isLoading={isLoading}
        exportLogs={exportLogs}
        setFilters={setFilters}
      />

      {/* Activity Logs */}
      {error && (
        <div className="mb-4 rounded-md border border-destructive bg-destructive/10 p-4 text-destructive">
          {error}
        </div>
      )}

      <ActivityLogs
        logs={filteredLogs}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        setSelectedLog={setSelectedLog}
        setIsDetailDialogOpen={setIsDetailDialogOpen}
        getStatusColor={getStatusColor}
        handleUndoAction={handleUndoAction}
        isLoading={isLoading}
      />

      {/* Last updated timestamp */}
      <div className="mt-6 text-right text-xs text-muted-foreground">
        Last updated: {lastUpdated.toLocaleString()}
      </div>

      {/* Log Details Dialog */}
      <LogDetails
        selectedLog={selectedLog}
        isDetailDialogOpen={isDetailDialogOpen}
        setIsDetailDialogOpen={setIsDetailDialogOpen}
        getStatusColor={getStatusColor}
        handleUndoAction={handleUndoAction}
      />

      {/* Undo Confirmation Dialog */}
      <UndoConfirmation
        isUndoDialogOpen={isUndoDialogOpen}
        setIsUndoDialogOpen={setIsUndoDialogOpen}
        selectedLog={selectedLog}
        activityLogs={activityLogs}
        setActivityLogs={setActivityLogs}
      />
    </div>
  );
}

export default AdminActivityLogging;
