import { Button } from '@/components/ui/button';
// No need to import types that aren't used
import { useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiCloseLine,
  RiSaveLine,
  RiRefreshLine,
} from 'react-icons/ri';

interface IHeaderProps {
  // We only need these props for the Header component
  saveReport: () => Promise<void>;
  isLoading: boolean;
}

export default function Header({ saveReport, isLoading }: IHeaderProps) {
  const router = useRouter();

  // The saveReport function is now passed from parent
  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push('/admin/analytics')}
          className="h-8 w-8"
        >
          <RiArrowLeftLine className="h-4 w-4" />
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">
          Custom Report Builder
        </h2>
      </div>
      <div className="flex flex-col gap-2 sm:flex-row">
        <Button
          variant="outline"
          onClick={() => router.push('/admin/analytics')}
          className="flex items-center gap-1"
        >
          <RiCloseLine className="h-4 w-4" />
          Cancel
        </Button>
        <Button
          onClick={saveReport}
          className="flex items-center gap-1"
          disabled={isLoading}
        >
          {isLoading ? (
            <RiRefreshLine className="h-4 w-4 animate-spin" />
          ) : (
            <RiSaveLine className="h-4 w-4" />
          )}
          Save Report
        </Button>
      </div>
    </div>
  );
}
