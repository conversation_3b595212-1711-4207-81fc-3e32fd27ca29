import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  RiMailLine,
  RiAddLine,
  RiDownloadLine,
  RiFileTextLine,
  RiFileExcel2Line,
  RiCloseLine,
} from 'react-icons/ri';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { TabsContent } from '@/components/ui/tabs';
import { useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ExportFormat,
  IReport,
  IReportSchedule,
  ScheduleFrequency,
} from '@/types/reports';

interface IScheduleProps {
  scheduleEnabled: boolean;
  setScheduleEnabled: (value: boolean) => void;
  scheduleFrequency: ScheduleFrequency;
  setScheduleFrequency: React.Dispatch<React.SetStateAction<ScheduleFrequency>>;
  scheduleExportFormat: ExportFormat;
  setScheduleExportFormat: React.Dispatch<React.SetStateAction<ExportFormat>>;
  scheduleRecipients: string[];
  setScheduleRecipients: React.Dispatch<React.SetStateAction<string[]>>;
  savedReports: IReport[];
  handleDeleteReport: (reportId: string) => Promise<void>;
  handleGenerateReport: (
    reportId: string,
    format: ExportFormat,
  ) => Promise<void>;
  toggleScheduleStatus: (reportId: string, enabled: boolean) => Promise<void>;
  updateScheduleSettings: (
    reportId: string,
    settings: Partial<IReportSchedule>,
  ) => Promise<void>;
  isLoading: boolean;
  dataRetentionPeriod?: string;
  onUpdateRetentionPeriod?: (period: string) => Promise<void>;
}

export default function Schedule({
  scheduleEnabled,
  setScheduleEnabled,
  scheduleFrequency,
  setScheduleFrequency,
  scheduleRecipients,
  setScheduleRecipients,
  scheduleExportFormat,
  setScheduleExportFormat,
  savedReports,
  handleDeleteReport,
  handleGenerateReport,
  toggleScheduleStatus,
  updateScheduleSettings,
  isLoading,
  dataRetentionPeriod = '90days',
  onUpdateRetentionPeriod,
}: IScheduleProps) {
  const [newRecipient, setNewRecipient] = useState<string>('');

  // Add a recipient email
  const addRecipient = () => {
    if (newRecipient && !scheduleRecipients.includes(newRecipient)) {
      setScheduleRecipients([...scheduleRecipients, newRecipient]);
      setNewRecipient('');
    }
  };

  const removeRecipient = (email: string) => {
    setScheduleRecipients(scheduleRecipients.filter((e) => e !== email));
  };
  return (
    <TabsContent value="schedule" className="mt-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Schedule Report</CardTitle>
          <CardDescription>Configure automated report delivery</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center space-x-2">
            <Switch
              id="schedule-enabled"
              checked={scheduleEnabled}
              onCheckedChange={setScheduleEnabled}
            />
            <Label htmlFor="schedule-enabled">Enable scheduled delivery</Label>
          </div>

          {scheduleEnabled && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="schedule-frequency">Frequency</Label>
                <Select
                  value={scheduleFrequency}
                  onValueChange={(value: string) =>
                    setScheduleFrequency(value as typeof scheduleFrequency)
                  }
                >
                  <SelectTrigger id="schedule-frequency">
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="schedule-export-format">Export Format</Label>
                <Select
                  value={scheduleExportFormat}
                  onValueChange={(value) =>
                    setScheduleExportFormat(
                      value as typeof scheduleExportFormat,
                    )
                  }
                >
                  <SelectTrigger id="schedule-export-format">
                    <SelectValue placeholder="Select export format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="excel">Excel</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="schedule-recipients">Recipients</Label>
                <div className="flex gap-2">
                  <Input
                    id="schedule-recipients"
                    placeholder="Enter email address"
                    value={newRecipient}
                    onChange={(e) => setNewRecipient(e.target.value)}
                  />
                  <Button
                    variant="outline"
                    onClick={addRecipient}
                    className="flex items-center gap-1"
                    disabled={!newRecipient}
                  >
                    <RiAddLine className="h-4 w-4" />
                    Add
                  </Button>
                </div>
              </div>

              {scheduleRecipients.length > 0 ? (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Recipient List</h4>
                  <div className="space-y-2">
                    {scheduleRecipients.map((email) => (
                      <div
                        key={email}
                        className="flex items-center justify-between rounded-md border p-2"
                      >
                        <div className="flex items-center gap-2">
                          <RiMailLine className="h-4 w-4 text-muted-foreground" />
                          <span>{email}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeRecipient(email)}
                          className="h-8 w-8 text-destructive hover:bg-destructive/10 hover:text-destructive"
                        >
                          <RiCloseLine className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="rounded-md border bg-muted/50 p-4 text-center text-muted-foreground">
                  No recipients added yet
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Export Options</CardTitle>
          <CardDescription>
            Configure data retention and export settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="data-retention">Data Retention Period</Label>
            <Select
              value={dataRetentionPeriod}
              onValueChange={(value) => {
                if (onUpdateRetentionPeriod) {
                  onUpdateRetentionPeriod(value);
                }
              }}
              disabled={isLoading || !onUpdateRetentionPeriod}
            >
              <SelectTrigger id="data-retention">
                <SelectValue placeholder="Select retention period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30days">30 days</SelectItem>
                <SelectItem value="90days">90 days</SelectItem>
                <SelectItem value="180days">180 days</SelectItem>
                <SelectItem value="1year">1 year</SelectItem>
                <SelectItem value="forever">Forever</SelectItem>
              </SelectContent>
            </Select>
            <p className="mt-1 text-xs text-muted-foreground">
              How long to keep report data before automatic deletion
            </p>
          </div>

          <div className="flex items-center justify-between rounded-md border p-4">
            <div className="flex items-center gap-2">
              <RiFileTextLine className="h-5 w-5 text-primary" />
              <div>
                <h4 className="font-medium">Export as PDF</h4>
                <p className="text-xs text-muted-foreground">
                  Download a PDF version of this report
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleGenerateReport('current', 'pdf')}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>Export Now</>
              )}
            </Button>
          </div>

          <div className="flex items-center justify-between rounded-md border p-4">
            <div className="flex items-center gap-2">
              <RiFileExcel2Line className="h-5 w-5 text-primary" />
              <div>
                <h4 className="font-medium">Export as CSV</h4>
                <p className="text-xs text-muted-foreground">
                  Download a CSV version of this report
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleGenerateReport('current', 'csv')}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>Export Now</>
              )}
            </Button>
          </div>

          <div className="flex items-center justify-between rounded-md border p-4">
            <div className="flex items-center gap-2">
              <RiDownloadLine className="h-5 w-5 text-muted-foreground" />
              <div>
                <h4 className="font-medium">One-time Export</h4>
                <p className="text-sm text-muted-foreground">
                  Export report data immediately
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="flex items-center gap-1"
                onClick={() => handleGenerateReport('current', 'pdf')}
                disabled={isLoading}
              >
                <RiDownloadLine className="h-4 w-4" />
                PDF
              </Button>
              <Button
                variant="outline"
                className="flex items-center gap-1"
                onClick={() => handleGenerateReport('current', 'csv')}
                disabled={isLoading}
              >
                <RiDownloadLine className="h-4 w-4" />
                CSV
              </Button>
            </div>
          </div>

          {/* Saved Reports */}
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-32 w-full" />
            </div>
          ) : savedReports.length > 0 ? (
            <div className="mt-6">
              <h3 className="mb-3 text-lg font-medium">Saved Reports</h3>
              <div className="space-y-3">
                {savedReports.map((report) => (
                  <div
                    key={report.id}
                    className="flex items-center justify-between rounded-md border p-3"
                  >
                    <div>
                      <h4 className="font-medium">{report.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {report.description || 'No description'}
                      </p>
                      <div className="mt-1 text-xs text-muted-foreground">
                        Created:{' '}
                        {new Date(report.createdAt).toLocaleDateString()}
                      </div>
                      <div className="mt-2 flex items-center gap-2">
                        <Switch
                          id={`schedule-enabled-${report.id}`}
                          checked={report.schedule.enabled}
                          onCheckedChange={(checked) =>
                            toggleScheduleStatus(report.id, checked)
                          }
                          disabled={isLoading}
                        />
                        <Label
                          htmlFor={`schedule-enabled-${report.id}`}
                          className="text-xs"
                        >
                          {report.schedule.enabled
                            ? 'Scheduled'
                            : 'Not scheduled'}
                        </Label>
                        {report.schedule.enabled && (
                          <span className="text-xs text-muted-foreground">
                            ({report.schedule.frequency},{' '}
                            {report.schedule.recipients.length} recipient(s))
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleGenerateReport(report.id, 'pdf')}
                        disabled={isLoading}
                        title="Download PDF"
                      >
                        <RiDownloadLine className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Open a dialog to edit schedule settings
                          updateScheduleSettings(report.id, {
                            frequency:
                              report.schedule.frequency === 'daily'
                                ? 'weekly'
                                : 'daily',
                          });
                        }}
                        disabled={isLoading}
                        title="Edit Schedule"
                      >
                        <RiMailLine className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteReport(report.id)}
                        disabled={isLoading}
                        title="Delete Report"
                      >
                        <RiCloseLine className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="mt-6 rounded-md border bg-muted/50 p-4 text-center text-muted-foreground">
              No saved reports yet. Create and save a report to see it here.
            </div>
          )}
        </CardContent>
      </Card>
    </TabsContent>
  );
}
