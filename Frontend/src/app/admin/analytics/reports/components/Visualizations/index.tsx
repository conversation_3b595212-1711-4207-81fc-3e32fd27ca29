import { TabsContent } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  RiCloseLine,
  RiBarChartGroupedLine,
  RiLineChartLine,
  RiPieChartLine,
  RiTableLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { IDimension, IMetric, IVisualization } from '@/types/reports';

interface IVisualizationsProps {
  availableMetrics: IMetric[];
  availableDimensions: IDimension[];
  selectedMetrics: string[];
  selectedDimensions: string[];
  setVisualizations: React.Dispatch<React.SetStateAction<IVisualization[]>>;
  visualizations: IVisualization[];
}

export default function Visualizations({
  availableMetrics,
  availableDimensions,
  selectedMetrics,
  selectedDimensions,
  setVisualizations,
  visualizations,
}: IVisualizationsProps) {
  // Add a new visualization
  const addVisualization = (type: 'bar' | 'line' | 'pie' | 'table') => {
    if (selectedMetrics.length === 0) {
      // Show error: No metrics selected
      return;
    }

    const newVisualization: IVisualization = {
      id: `viz_${Date.now()}`,
      type,
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} Chart`,
      metrics: [...selectedMetrics],
      dimensions: [...selectedDimensions],
    };

    setVisualizations((prev) => [...prev, newVisualization]);
  };

  // Remove a visualization
  const removeVisualization = (vizId: string) => {
    setVisualizations((prev) => prev.filter((viz) => viz.id !== vizId));
  };

  // Get metric name by ID
  const getMetricName = (metricId: string) => {
    const metric = availableMetrics.find((m) => m.id === metricId);
    return metric ? metric.name : metricId;
  };

  // Get dimension name by ID
  const getDimensionName = (dimensionId: string) => {
    const dimension = availableDimensions.find((d) => d.id === dimensionId);
    return dimension ? dimension.name : dimensionId;
  };

  return (
    <TabsContent value="visualizations" className="mt-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Add Visualizations</CardTitle>
          <CardDescription>
            Create charts and tables to visualize your data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              onClick={() => addVisualization('bar')}
              className="flex items-center gap-1"
              disabled={selectedMetrics.length === 0}
            >
              <RiBarChartGroupedLine className="h-4 w-4" />
              Bar Chart
            </Button>
            <Button
              variant="outline"
              onClick={() => addVisualization('line')}
              className="flex items-center gap-1"
              disabled={selectedMetrics.length === 0}
            >
              <RiLineChartLine className="h-4 w-4" />
              Line Chart
            </Button>
            <Button
              variant="outline"
              onClick={() => addVisualization('pie')}
              className="flex items-center gap-1"
              disabled={selectedMetrics.length === 0}
            >
              <RiPieChartLine className="h-4 w-4" />
              Pie Chart
            </Button>
            <Button
              variant="outline"
              onClick={() => addVisualization('table')}
              className="flex items-center gap-1"
              disabled={selectedMetrics.length === 0}
            >
              <RiTableLine className="h-4 w-4" />
              Data Table
            </Button>
          </div>

          {selectedMetrics.length === 0 && (
            <div className="mt-4 rounded-md border bg-muted/50 p-4 text-center text-muted-foreground">
              Select at least one metric in the Metrics tab to add
              visualizations
            </div>
          )}
        </CardContent>
      </Card>

      {/* Visualization Preview */}
      {visualizations.length > 0 ? (
        <div className="space-y-4">
          {visualizations.map((viz) => (
            <Card key={viz.id}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div>
                  <CardTitle>{viz.title}</CardTitle>
                  <CardDescription>
                    {viz.type.charAt(0).toUpperCase() + viz.type.slice(1)} chart
                  </CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeVisualization(viz.id)}
                  className="text-destructive hover:bg-destructive/10 hover:text-destructive"
                >
                  <RiCloseLine className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium">Metrics</h4>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {viz.metrics.map((metricId) => (
                        <div
                          key={metricId}
                          className="bg-primary/10 rounded-md px-2 py-1 text-xs text-primary"
                        >
                          {getMetricName(metricId)}
                        </div>
                      ))}
                    </div>
                  </div>

                  {viz.dimensions.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium">Dimensions</h4>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {viz.dimensions.map((dimensionId) => (
                          <div
                            key={dimensionId}
                            className="rounded-md bg-muted px-2 py-1 text-xs text-muted-foreground"
                          >
                            {getDimensionName(dimensionId)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex h-[200px] w-full items-center justify-center rounded-md border">
                    <div className="text-center text-muted-foreground">
                      {viz.type === 'bar' && (
                        <RiBarChartGroupedLine className="mx-auto h-12 w-12" />
                      )}
                      {viz.type === 'line' && (
                        <RiLineChartLine className="mx-auto h-12 w-12" />
                      )}
                      {viz.type === 'pie' && (
                        <RiPieChartLine className="mx-auto h-12 w-12" />
                      )}
                      {viz.type === 'table' && (
                        <RiTableLine className="mx-auto h-12 w-12" />
                      )}
                      <p className="mt-2">
                        Visualization preview will appear here
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            No visualizations added yet. Add visualizations using the buttons
            above.
          </CardContent>
        </Card>
      )}
    </TabsContent>
  );
}
