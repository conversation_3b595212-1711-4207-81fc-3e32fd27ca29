import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface IReportDetailsProps {
  readonly reportName: string;
  readonly setReportName: (reportNaem: string) => void;
  readonly reportDescription: string;
  readonly setReportDescription: (reportDescription: string) => void;
}

export default function ReportDetails({
  reportName,
  setReportName,
  reportDescription,
  setReportDescription,
}: IReportDetailsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Report Details</CardTitle>
        <CardDescription>
          Enter basic information about your report
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="report-name">Report Name</Label>
          <Input
            id="report-name"
            placeholder="Enter report name"
            value={reportName}
            onChange={(e) => setReportName(e.target.value)}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="report-description">Description (Optional)</Label>
          <Input
            id="report-description"
            placeholder="Enter report description"
            value={reportDescription}
            onChange={(e) => setReportDescription(e.target.value)}
          />
        </div>
      </CardContent>
    </Card>
  );
}
