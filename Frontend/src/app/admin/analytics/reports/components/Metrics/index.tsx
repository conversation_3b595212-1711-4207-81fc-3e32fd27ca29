import { TabsContent } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { IDimension, IMetric } from '@/types/reports';
import { Checkbox } from '@/components/ui/checkbox';
import { Dispatch, SetStateAction } from 'react';

interface IMetricsProps {
  availableMetrics: IMetric[];
  selectedMetrics: string[];
  availableDimensions: IDimension[];
  selectedDimensions: string[];
  setSelectedMetrics: Dispatch<SetStateAction<string[]>>;
  setSelectedDimensions: Dispatch<SetStateAction<string[]>>;
}

export default function Metrics({
  availableMetrics,
  selectedMetrics,
  availableDimensions,
  selectedDimensions,
  setSelectedMetrics,
  setSelectedDimensions,
}: IMetricsProps) {
  // Handle metric selection
  const toggleMetric = (metricId: string) => {
    setSelectedMetrics((prev) =>
      prev.includes(metricId)
        ? prev.filter((id) => id !== metricId)
        : [...prev, metricId],
    );
  };

  // Handle dimension selection
  const toggleDimension = (dimensionId: string) => {
    setSelectedDimensions((prev) =>
      prev.includes(dimensionId)
        ? prev.filter((id) => id !== dimensionId)
        : [...prev, dimensionId],
    );
  };
  return (
    <TabsContent value="metrics" className="mt-6 space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Metrics</CardTitle>
            <CardDescription>
              Select the metrics to include in your report
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(
                availableMetrics.reduce(
                  (acc, metric) => {
                    acc[metric.category] = acc[metric.category] || [];
                    acc[metric.category].push(metric);
                    return acc;
                  },
                  {} as Record<string, IMetric[]>,
                ),
              ).map(([category, metrics]) => (
                <div key={category} className="space-y-2">
                  <h3 className="text-sm font-medium">{category}</h3>
                  <div className="space-y-2">
                    {metrics.map((metric) => (
                      <div
                        key={metric.id}
                        className="flex items-start space-x-2"
                      >
                        <Checkbox
                          id={`metric-${metric.id}`}
                          checked={selectedMetrics.includes(metric.id)}
                          onCheckedChange={() => toggleMetric(metric.id)}
                        />
                        <div className="grid gap-1.5 leading-none">
                          <label
                            htmlFor={`metric-${metric.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {metric.name}
                          </label>
                          <p className="text-xs text-muted-foreground">
                            {metric.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Dimensions */}
        <Card>
          <CardHeader>
            <CardTitle>Dimensions</CardTitle>
            <CardDescription>
              Select dimensions to group your data by
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(
                availableDimensions.reduce(
                  (acc, dimension) => {
                    acc[dimension.category] = acc[dimension.category] || [];
                    acc[dimension.category].push(dimension);
                    return acc;
                  },
                  {} as Record<string, IDimension[]>,
                ),
              ).map(([category, dimensions]) => (
                <div key={category} className="space-y-2">
                  <h3 className="text-sm font-medium">{category}</h3>
                  <div className="space-y-2">
                    {dimensions.map((dimension) => (
                      <div
                        key={dimension.id}
                        className="flex items-start space-x-2"
                      >
                        <Checkbox
                          id={`dimension-${dimension.id}`}
                          checked={selectedDimensions.includes(dimension.id)}
                          onCheckedChange={() => toggleDimension(dimension.id)}
                        />
                        <div className="grid gap-1.5 leading-none">
                          <label
                            htmlFor={`dimension-${dimension.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {dimension.name}
                          </label>
                          <p className="text-xs text-muted-foreground">
                            {dimension.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </TabsContent>
  );
}
