/**
 * @file page.tsx
 * @description Custom Report Builder for admin dashboard
 */
'use client';

import { useState, useEffect, useCallback } from 'react';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  RiAlertLine,
  RiHistoryLine,
  RiRefreshLine,
  RiSaveLine,
} from 'react-icons/ri';
import {
  useAxiosGet,
  useAxiosPost,
  useAxiosPut,
  useAxiosDelete,
} from '@/hooks/useAxios';
import { IApiResponse } from '@/types';

import {
  IVisualization,
  IReport,
  IReportResponse,
  IReportCreateRequest,
  IReportUpdateRequest,
  IReportsListResponse,
  ExportFormat,
  IReportSchedule,
  transformReportResponse,
  generateVisualizationId,
  ScheduleFrequency,
} from '@/types/reports';
import {
  REPORT_API,
  IMetricsResponse,
  IDimensionsResponse,
  IScheduleUpdateParams,
  IReportGenerateResponse,
  IRetentionUpdateParams,
  IRetentionUpdateResponse,
} from '@/services/reportService';
import ReportDetails from './components/ReportDetails';
import Metrics from './components/Metrics';
import Visualizations from './components/Visualizations';
import Schedule from './components/Schedule';
import Header from './components/Header';
// We'll fetch metrics and dimensions from the API instead of using constants
import { IDimension, IMetric } from '@/types/reports';

function CustomReportBuilderPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('metrics');
  const [reportName, setReportName] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [selectedDimensions, setSelectedDimensions] = useState<string[]>([]);
  const [visualizations, setVisualizations] = useState<IVisualization[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSaveReportDialog, setShowSaveReportDialog] = useState(false);
  const [scheduleEnabled, setScheduleEnabled] = useState(false);
  const [scheduleFrequency, setScheduleFrequency] =
    useState<ScheduleFrequency>('weekly');
  const [scheduleRecipients, setScheduleRecipients] = useState<string[]>([]);
  const [scheduleExportFormat, setScheduleExportFormat] =
    useState<ExportFormat>('csv');
  const [savedReports, setSavedReports] = useState<IReport[]>([]);
  const [availableMetrics, setAvailableMetrics] = useState<IMetric[]>([]);
  const [availableDimensions, setAvailableDimensions] = useState<IDimension[]>(
    [],
  );
  const [dataRetentionPeriod, setDataRetentionPeriod] =
    useState<string>('90days');

  // State for export history tracking
  const [showExportHistory, setShowExportHistory] = useState(false);
  const [exportHistory, setExportHistory] = useState<
    Array<{
      reportId: string;
      reportName: string;
      format: ExportFormat;
      timestamp: string;
      status: 'success' | 'failed';
    }>
  >([]);

  // API hooks
  const [fetchReports] = useAxiosGet<IReportsListResponse>(REPORT_API.LIST);
  const [createReport] = useAxiosPost<IReportResponse, IReportCreateRequest>(
    REPORT_API.CREATE,
  );
  // For updating existing reports
  const [updateReportApi] = useAxiosPut<
    IReportResponse,
    Omit<IReportUpdateRequest, 'id'>
  >(REPORT_API.UPDATE);
  // For delete
  const [deleteReportApi] = useAxiosDelete<{
    success: boolean;
    message: string;
  }>(REPORT_API.DELETE);
  // Fetch metrics and dimensions
  const [fetchMetrics] = useAxiosGet<IMetricsResponse>(REPORT_API.METRICS);
  const [fetchDimensions] = useAxiosGet<IDimensionsResponse>(
    REPORT_API.DIMENSIONS,
  );
  // Update data retention period
  const [updateRetentionPeriod] = useAxiosPut<
    IRetentionUpdateResponse,
    IRetentionUpdateParams
  >(REPORT_API.RETENTION);
  // Generate report
  const [generateReportApi] = useAxiosPost<
    IReportGenerateResponse,
    { format: ExportFormat }
  >(REPORT_API.GENERATE);
  // Update report schedule
  const [updateScheduleApi] = useAxiosPut<
    { success: boolean; message: string },
    IScheduleUpdateParams
  >(REPORT_API.SCHEDULE);

  // Fallback data in case API fails
  const FALLBACK_METRICS: IMetric[] = [
    {
      id: 'users',
      name: 'Users',
      category: 'user',
      description: 'Total number of registered users',
    },
    {
      id: 'newUsers',
      name: 'New Users',
      category: 'user',
      description: 'Number of new user registrations',
    },
    {
      id: 'activeUsers',
      name: 'Active Users',
      category: 'user',
      description: 'Number of users who logged in',
    },
    {
      id: 'pageViews',
      name: 'Page Views',
      category: 'engagement',
      description: 'Total number of page views',
    },
    {
      id: 'sessions',
      name: 'Sessions',
      category: 'engagement',
      description: 'Total number of user sessions',
    },
    {
      id: 'avgSessionDuration',
      name: 'Avg. Session Duration',
      category: 'engagement',
      description: 'Average time users spend on the site',
    },
    {
      id: 'completionRate',
      name: 'Completion Rate',
      category: 'challenge',
      description: 'Percentage of started challenges that were completed',
    },
    {
      id: 'challengeAttempts',
      name: 'Challenge Attempts',
      category: 'challenge',
      description: 'Number of challenge attempts',
    },
    {
      id: 'challengeCompletions',
      name: 'Challenge Completions',
      category: 'challenge',
      description: 'Number of challenge completions',
    },
  ];

  const FALLBACK_DIMENSIONS: IDimension[] = [
    {
      id: 'date',
      name: 'Date',
      category: 'time',
      description: 'Group by date',
    },
    {
      id: 'week',
      name: 'Week',
      category: 'time',
      description: 'Group by week',
    },
    {
      id: 'month',
      name: 'Month',
      category: 'time',
      description: 'Group by month',
    },
    {
      id: 'quarter',
      name: 'Quarter',
      category: 'time',
      description: 'Group by quarter',
    },
    {
      id: 'year',
      name: 'Year',
      category: 'time',
      description: 'Group by year',
    },
    {
      id: 'userType',
      name: 'User Type',
      category: 'user',
      description: 'Group by user type (new vs returning)',
    },
    {
      id: 'userRole',
      name: 'User Role',
      category: 'user',
      description: 'Group by user role',
    },
    {
      id: 'device',
      name: 'Device',
      category: 'technical',
      description: 'Group by device type',
    },
    {
      id: 'browser',
      name: 'Browser',
      category: 'technical',
      description: 'Group by browser',
    },
    {
      id: 'country',
      name: 'Country',
      category: 'location',
      description: 'Group by country',
    },
    {
      id: 'region',
      name: 'Region',
      category: 'location',
      description: 'Group by region/state',
    },
    {
      id: 'challengeType',
      name: 'Challenge Type',
      category: 'challenge',
      description: 'Group by challenge type',
    },
    {
      id: 'challengeDifficulty',
      name: 'Challenge Difficulty',
      category: 'challenge',
      description: 'Group by challenge difficulty',
    },
  ];

  // Fetch metrics from API
  const loadMetricsAndDimensions = useCallback(async () => {
    // Prevent multiple simultaneous requests
    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    // Track if component is still mounted
    let isMounted = true;
    // Reference to cancel functions for API requests
    const cancelRequests: (() => void)[] = [];

    try {
      // Fetch metrics with timeout
      const metricsPromise = fetchMetrics() as Promise<
        IApiResponse<IMetricsResponse>
      > & { cancel?: () => void };

      // Add cancellation if available
      if (metricsPromise.cancel) {
        cancelRequests.push(metricsPromise.cancel);
      }

      // Create a timeout promise
      const metricsTimeout = new Promise<IApiResponse<IMetricsResponse>>(
        (_, reject) => {
          setTimeout(() => reject(new Error('Metrics request timeout')), 5000);
        },
      );

      // Race between the actual request and timeout
      const metricsResponse = await Promise.race([
        metricsPromise,
        metricsTimeout,
      ]);

      // Only update state if component is still mounted
      if (isMounted) {
        if (
          metricsResponse.success &&
          metricsResponse.data &&
          'metrics' in metricsResponse.data
        ) {
          setAvailableMetrics(metricsResponse.data.metrics as IMetric[]);
        } else {
          // If API fails, use fallback metrics from constants
          console.warn('Failed to load metrics from API, using fallbacks');
          setAvailableMetrics(FALLBACK_METRICS);
        }
      }

      // Fetch dimensions with timeout
      const dimensionsPromise = fetchDimensions() as Promise<
        IApiResponse<IDimensionsResponse>
      > & { cancel?: () => void };

      // Add cancellation if available
      if (dimensionsPromise.cancel) {
        cancelRequests.push(dimensionsPromise.cancel);
      }

      // Create a timeout promise
      const dimensionsTimeout = new Promise<IApiResponse<IDimensionsResponse>>(
        (_, reject) => {
          setTimeout(
            () => reject(new Error('Dimensions request timeout')),
            5000,
          );
        },
      );

      // Race between the actual request and timeout
      const dimensionsResponse = await Promise.race([
        dimensionsPromise,
        dimensionsTimeout,
      ]);

      // Only update state if component is still mounted
      if (isMounted) {
        if (
          dimensionsResponse.success &&
          dimensionsResponse.data &&
          'dimensions' in dimensionsResponse.data
        ) {
          setAvailableDimensions(
            dimensionsResponse.data.dimensions as IDimension[],
          );
        } else {
          // If API fails, use fallback dimensions from constants
          console.warn('Failed to load dimensions from API, using fallbacks');
          setAvailableDimensions(FALLBACK_DIMENSIONS);
        }
      }
    } catch (error) {
      console.error('Error loading metrics and dimensions:', error);
      // Only update state if component is still mounted
      if (isMounted) {
        // Use fallbacks from constants
        setAvailableMetrics(FALLBACK_METRICS);
        setAvailableDimensions(FALLBACK_DIMENSIONS);
      }
    } finally {
      // Only update loading state if component is still mounted
      if (isMounted) {
        setIsLoading(false);
      }
    }

    // Cleanup function to cancel requests and prevent state updates after unmount
    return () => {
      isMounted = false;
      cancelRequests.forEach((cancel) => cancel());
    };
  }, [fetchMetrics, fetchDimensions, isLoading]);

  // Load saved reports
  const loadSavedReports = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetchReports();
      if (response.success && response.data?.reports) {
        // Transform API response to frontend format
        const reports = response.data.reports.map(transformReportResponse);
        setSavedReports(reports);
      } else {
        setError('Failed to load saved reports');
        setSavedReports([]);
      }
    } catch (error) {
      console.error('Error loading reports:', error);
      setError('Failed to load saved reports. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [fetchReports]);

  // Load reports, metrics, and dimensions on component mount
  useEffect(() => {
    loadSavedReports();
    loadMetricsAndDimensions();
  }, [loadSavedReports, loadMetricsAndDimensions]);

  // Save report function
  const saveReport = async (reportId?: string) => {
    if (!reportName.trim()) {
      toast({
        title: 'Error',
        description: 'Report name is required',
        variant: 'destructive',
      });
      return;
    }

    if (selectedMetrics.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one metric',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Prepare visualizations by adding IDs if they don't have one
      const preparedVisualizations = visualizations.map((viz) => ({
        ...viz,
        id: viz.id || generateVisualizationId(),
      }));

      // Prepare schedule data
      const scheduleData: IReportSchedule = {
        enabled: scheduleEnabled,
        frequency: scheduleFrequency,
        recipients: scheduleRecipients,
        exportFormat: scheduleExportFormat,
      };

      let response;

      if (reportId) {
        // Update existing report
        const updateData: Omit<IReportUpdateRequest, 'id'> = {
          name: reportName,
          description: reportDescription,
          metrics: selectedMetrics,
          dimensions: selectedDimensions,
          visualizations: preparedVisualizations,
          schedule: scheduleData,
        };

        // Update existing report with ID in URL
        const updateUrl = `${REPORT_API.UPDATE}/${reportId}`;
        response = await updateReportApi(updateData, { url: updateUrl });
      } else {
        // Create new report
        const createData: IReportCreateRequest = {
          name: reportName,
          description: reportDescription,
          metrics: selectedMetrics,
          dimensions: selectedDimensions,
          visualizations: preparedVisualizations,
          schedule: scheduleData,
        };

        response = await createReport(createData);
      }

      if (response.success) {
        toast({
          title: reportId ? 'Report Updated' : 'Report Created',
          description: `Report "${reportName}" has been ${reportId ? 'updated' : 'created'} successfully.`,
        });

        // Refresh the list of saved reports
        loadSavedReports();

        // Reset form if creating a new report
        if (!reportId) {
          setReportName('');
          setReportDescription('');
          setSelectedMetrics([]);
          setSelectedDimensions([]);
          setVisualizations([]);
          setScheduleEnabled(false);
          setScheduleFrequency('weekly');
          setScheduleRecipients([]);
          setScheduleExportFormat('pdf');
        }
      } else {
        toast({
          title: 'Error',
          description:
            response.message ||
            `Failed to ${reportId ? 'update' : 'create'} report`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error(
        `Error ${reportId ? 'updating' : 'creating'} report:`,
        error,
      );
      toast({
        title: 'Error',
        description: `An unexpected error occurred while ${reportId ? 'updating' : 'creating'} the report.`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a report
  const handleDeleteReport = async (reportId: string) => {
    if (
      !confirm(
        'Are you sure you want to delete this report? This action cannot be undone.',
      )
    ) {
      return;
    }

    setIsLoading(true);

    try {
      // Delete report with ID in URL
      const deleteUrl = `${REPORT_API.DELETE}/${reportId}`;
      const response = await deleteReportApi(undefined, { url: deleteUrl });

      if (response.success) {
        toast({
          title: 'Report Deleted',
          description: 'The report has been deleted successfully.',
        });

        // Update the list of saved reports
        setSavedReports((prev) =>
          prev.filter((report) => report.id !== reportId),
        );
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to delete report',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error deleting report:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred while deleting the report.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update data retention period
  const handleUpdateRetentionPeriod = async (period: string) => {
    setIsLoading(true);

    try {
      const response = await updateRetentionPeriod({
        period,
      });

      if (response.success) {
        setDataRetentionPeriod(period);
        toast({
          title: 'Settings Updated',
          description: 'Data retention period has been updated.',
        });
      } else {
        toast({
          title: 'Error',
          description:
            response.message || 'Failed to update data retention period',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating data retention period:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred while updating settings.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle schedule status for a report
  const toggleScheduleStatus = async (reportId: string, enabled: boolean) => {
    setIsLoading(true);

    try {
      const response = await updateScheduleApi({
        reportId,
        enabled,
      });

      if (response.success) {
        toast({
          title: enabled ? 'Scheduling Enabled' : 'Scheduling Disabled',
          description: `Report scheduling has been ${enabled ? 'enabled' : 'disabled'}.`,
        });

        // Update the report in the local state
        setSavedReports((prev) =>
          prev.map((r) =>
            r.id === reportId
              ? { ...r, schedule: { ...r.schedule, enabled } }
              : r,
          ),
        );
      } else {
        toast({
          title: 'Error',
          description:
            response.message ||
            `Failed to ${enabled ? 'enable' : 'disable'} scheduling`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating schedule status:', error);
      toast({
        title: 'Error',
        description:
          'An unexpected error occurred while updating schedule status.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update schedule settings for a report
  const updateScheduleSettings = async (
    reportId: string,
    scheduleSettings: Partial<IReportSchedule>,
  ) => {
    setIsLoading(true);

    try {
      // Find the report to get its current schedule settings
      const report = savedReports.find((r) => r.id === reportId);
      if (!report) {
        throw new Error('Report not found');
      }

      // Prepare the schedule update parameters
      const updateParams: IScheduleUpdateParams = {
        reportId,
        ...scheduleSettings,
      };

      const response = await updateScheduleApi(updateParams);

      if (response.success) {
        toast({
          title: 'Schedule Updated',
          description: 'Report schedule settings have been updated.',
        });

        // Update the report in the local state
        setSavedReports((prev) =>
          prev.map((r) =>
            r.id === reportId
              ? {
                  ...r,
                  schedule: {
                    ...r.schedule,
                    ...scheduleSettings,
                  },
                }
              : r,
          ),
        );
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to update schedule settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating schedule settings:', error);
      toast({
        title: 'Error',
        description:
          'An unexpected error occurred while updating schedule settings.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Track report generation history
  const handleGenerateReport = async (
    reportId: string,
    format: ExportFormat,
  ) => {
    setIsLoading(true);

    // Find report name for history tracking
    const reportToExport = savedReports.find((r) => r.id === reportId);
    const reportName = reportToExport?.name || 'Unknown Report';
    const timestamp = new Date().toISOString();

    try {
      // Generate report with ID in URL
      const generateUrl = `${REPORT_API.GENERATE}/${reportId}`;
      const response = await generateReportApi(
        { format },
        { url: generateUrl },
      );

      // Handle the response properly
      if (response.success && response.data && 'url' in response.data) {
        // For file download, create a blob and download it
        const downloadResponse = await fetch(response.data.url as string);
        const blob = await downloadResponse.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `report-${reportId}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Add to export history
        setExportHistory((prev) => [
          {
            reportId,
            reportName,
            format,
            timestamp,
            status: 'success',
          },
          ...prev.slice(0, 9), // Keep only the last 10 entries
        ]);

        toast({
          title: 'Success',
          description: `Report generated and downloaded as ${format.toUpperCase()}`,
        });

        // Track this report generation in analytics
        // TODO: Implement analytics tracking for report generation
      } else {
        // Add failed attempt to history
        setExportHistory((prev) => [
          {
            reportId,
            reportName,
            format,
            timestamp,
            status: 'failed',
          },
          ...prev.slice(0, 9),
        ]);

        toast({
          title: 'Error',
          description:
            response.message || 'Failed to generate report. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error generating report:', error);

      // Add failed attempt to history
      setExportHistory((prev) => [
        {
          reportId,
          reportName,
          format,
          timestamp,
          status: 'failed',
        },
        ...prev.slice(0, 9),
      ]);

      toast({
        title: 'Error',
        description: 'Failed to generate report. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Error message if API fails */}
      {error && (
        <Alert className="mb-4 border-destructive">
          <RiAlertLine className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {/* Header with actions */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
        <Button onClick={() => setShowSaveReportDialog(true)}>
          <RiSaveLine className="mr-2 h-4 w-4" /> Save Custom Report
        </Button>
        {exportHistory.length > 0 && (
          <Button variant="outline" onClick={() => setShowExportHistory(true)}>
            <RiHistoryLine className="mr-2 h-4 w-4" /> Export History
          </Button>
        )}
        <Button variant="outline" onClick={loadSavedReports}>
          <RiRefreshLine className="mr-2 h-4 w-4" /> Refresh
        </Button>
      </div>
      <Header saveReport={saveReport} isLoading={isLoading} />

      {/* Export History Dialog */}
      {showExportHistory && (
        <Dialog open={showExportHistory} onOpenChange={setShowExportHistory}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Export History</DialogTitle>
              <DialogDescription>
                Recent report exports and their status.
              </DialogDescription>
            </DialogHeader>

            <div className="max-h-[400px] overflow-auto">
              <div className="space-y-4">
                {exportHistory.map((entry, index) => (
                  <div
                    key={index}
                    className="flex items-start justify-between rounded-lg border p-3"
                  >
                    <div className="space-y-1">
                      <p className="font-medium">{entry.reportName}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(entry.timestamp).toLocaleString()}
                      </p>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {entry.format.toUpperCase()}
                        </Badge>
                        <Badge
                          variant={
                            entry.status === 'success'
                              ? 'default'
                              : 'destructive'
                          }
                        >
                          {entry.status === 'success' ? 'Success' : 'Failed'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <DialogFooter className="sm:justify-start">
              <Button
                type="button"
                variant="secondary"
                onClick={() => setShowExportHistory(false)}
              >
                Close
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setExportHistory([])}
              >
                Clear History
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Saved Reports Dialog */}
      <Dialog
        open={showSaveReportDialog}
        onOpenChange={setShowSaveReportDialog}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Save Custom Report</DialogTitle>
            <DialogDescription>
              Save your custom report for later use.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex items-start justify-between rounded-lg border p-3">
              <div className="space-y-1">
                <p className="font-medium">Report Name</p>
                <input
                  type="text"
                  value={reportName}
                  onChange={(e) => setReportName(e.target.value)}
                  className="w-full rounded-lg border p-2"
                />
              </div>
            </div>
            <div className="flex items-start justify-between rounded-lg border p-3">
              <div className="space-y-1">
                <p className="font-medium">Report Description</p>
                <textarea
                  value={reportDescription}
                  onChange={(e) => setReportDescription(e.target.value)}
                  className="w-full rounded-lg border p-2"
                />
              </div>
            </div>
          </div>
          <DialogFooter className="sm:justify-start">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setShowSaveReportDialog(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="default"
              onClick={() => saveReport()}
            >
              Save Report
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Report Details */}
      <ReportDetails
        reportName={reportName}
        setReportName={setReportName}
        reportDescription={reportDescription}
        setReportDescription={setReportDescription}
      />

      {/* Tabs for report configuration */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="metrics">Metrics & Dimensions</TabsTrigger>
          <TabsTrigger value="visualizations">Visualizations</TabsTrigger>
          <TabsTrigger value="schedule">Schedule & Export</TabsTrigger>
        </TabsList>

        {/* Metrics & Dimensions Tab */}
        <Metrics
          availableMetrics={availableMetrics}
          selectedMetrics={selectedMetrics}
          availableDimensions={availableDimensions}
          selectedDimensions={selectedDimensions}
          setSelectedMetrics={setSelectedMetrics}
          setSelectedDimensions={setSelectedDimensions}
        />

        {/* Visualizations Tab */}
        <Visualizations
          availableMetrics={availableMetrics}
          availableDimensions={availableDimensions}
          selectedDimensions={selectedDimensions}
          selectedMetrics={selectedMetrics}
          setVisualizations={setVisualizations}
          visualizations={visualizations}
        />

        {/* Schedule & Export Tab */}
        <Schedule
          scheduleEnabled={scheduleEnabled}
          setScheduleEnabled={setScheduleEnabled}
          scheduleFrequency={scheduleFrequency}
          setScheduleFrequency={setScheduleFrequency}
          scheduleExportFormat={scheduleExportFormat}
          setScheduleExportFormat={setScheduleExportFormat}
          scheduleRecipients={scheduleRecipients}
          setScheduleRecipients={setScheduleRecipients}
          savedReports={savedReports}
          handleDeleteReport={handleDeleteReport}
          handleGenerateReport={handleGenerateReport}
          toggleScheduleStatus={toggleScheduleStatus}
          updateScheduleSettings={updateScheduleSettings}
          isLoading={isLoading}
          dataRetentionPeriod={dataRetentionPeriod}
          onUpdateRetentionPeriod={handleUpdateRetentionPeriod}
        />
      </Tabs>
    </div>
  );
}

export default CustomReportBuilderPage;
