/**
 * @file page.tsx
 * @description User Analytics page for admin dashboard
 */
'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  RiArrowLeftLine,
  RiCalendarLine,
  RiDownloadLine,
  RiUserLine,
  RiUserAddLine,
  RiLoginCircleLine,
  RiBarChartGroupedLine,
  RiPieChartLine,
  RiLineChartLine,
  RiRefreshLine,
  RiAlertLine,
} from 'react-icons/ri';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAxiosGet } from '@/hooks/useAxios';
import {
  IUserAnalytics,
  IUserAnalyticsResponse,
  transformUserAnalytics,
} from '@/types/userAnalytics';
import {
  TimeRangeOption,
  getDateRangeFromOption,
  formatDateForAPI,
} from '@/types/analytics';

function UserAnalyticsPage() {
  const router = useRouter();
  const [timeRange, setTimeRange] = useState<TimeRangeOption>('30days');
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState(getDateRangeFromOption(timeRange));
  const [analytics, setAnalytics] = useState<IUserAnalytics | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [error, setError] = useState<string | null>(null);

  // Use the existing useAxiosGet hooks
  const [fetchUserAnalytics, userAnalyticsState] =
    useAxiosGet<IUserAnalyticsResponse>('/analytics/user');

  // Determine loading state
  const isLoading = userAnalyticsState.isLoading;

  // Fetch user analytics data
  const fetchAnalyticsData = useCallback(async () => {
    setError(null);

    try {
      // Fetch user analytics with date range parameters
      const response = await fetchUserAnalytics({
        params: {
          startDate: formatDateForAPI(dateRange.startDate),
          endDate: formatDateForAPI(dateRange.endDate),
        },
      });

      if (response.success && response.data) {
        // Transform the API data to frontend format
        const transformedData = transformUserAnalytics(response.data);
        setAnalytics(transformedData);
        setLastUpdated(new Date());
      } else {
        setError(response.message || 'Failed to load user analytics data');
      }
    } catch (error) {
      console.error('Error fetching user analytics data:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to load user analytics data';
      setError(errorMessage);
    }
  }, [fetchUserAnalytics, dateRange]);

  // Update date range when time range changes
  useEffect(() => {
    setDateRange(getDateRangeFromOption(timeRange));
  }, [timeRange]);

  // Fetch data when date range changes
  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  // Create empty data structure for when API data is not yet loaded
  const emptyAnalyticsData: IUserAnalytics = {
    overview: {
      totalUsers: 0,
      activeUsers: 0,
      newUsers: 0,
      churnRate: 0,
      retentionRate: 0,
      averageSessionDuration: '0 minutes',
      averageSessionsPerUser: 0,
    },
    registration: {
      daily: [],
      sources: [],
      conversionRate: 0,
    },
    retention: {
      byWeek: [],
      byUserType: [],
    },
    engagement: {
      activeUsersByDay: [],
      activityDistribution: [],
      userSegments: [],
    },
    learning: {
      completionRates: [],
      averageProgress: 0,
      certificatesEarned: 0,
      skillDistribution: [],
    },
  };

  // Use API data or empty data structure when loading
  const analyticsData = analytics || emptyAnalyticsData;

  // Render bar chart (simplified for this example)
  const renderBarChart = (data: Array<{ date: string; count: number }>) => {
    const maxCount = Math.max(...data.map((item) => item.count));

    return (
      <div className="flex h-64 items-end justify-between">
        {data.map((item, index) => (
          <div key={index} className="flex flex-col items-center">
            <div
              className="w-12 rounded-t-md bg-primary"
              style={{ height: `${(item.count / maxCount) * 100}%` }}
            ></div>
            <div className="mt-2 text-sm text-muted-foreground">
              {new Date(item.date).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
              })}
            </div>
            <div className="text-xs text-muted-foreground/70">{item.count}</div>
          </div>
        ))}
      </div>
    );
  };

  // Render pie chart (simplified for this example)
  const renderPieChart = (
    data: Array<{
      source?: string;
      type?: string;
      activity?: string;
      segment?: string;
      skill?: string;
      percentage: number;
    }>,
  ) => {
    let currentAngle = 0;
    const colors = [
      'bg-primary',
      'bg-accent',
      'bg-success',
      'bg-warning',
      'bg-destructive',
    ];

    return (
      <div className="relative mx-auto h-[200px] w-[200px]">
        <svg viewBox="0 0 100 100" className="h-full w-full">
          {data.map((item, index) => {
            const startAngle = currentAngle;
            const angle = (item.percentage / 100) * 360;
            currentAngle += angle;
            const endAngle = currentAngle;

            const startRad = (startAngle - 90) * (Math.PI / 180);
            const endRad = (endAngle - 90) * (Math.PI / 180);

            const x1 = 50 + 40 * Math.cos(startRad);
            const y1 = 50 + 40 * Math.sin(startRad);
            const x2 = 50 + 40 * Math.cos(endRad);
            const y2 = 50 + 40 * Math.sin(endRad);

            const largeArcFlag = angle > 180 ? 1 : 0;

            const pathData = `M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;

            return (
              <path
                key={index}
                d={pathData}
                fill={`var(--${colors[index % colors.length].replace('bg-', '')})`}
                stroke="var(--background)"
                strokeWidth="1"
              />
            );
          })}
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-[100px] w-[100px] rounded-full bg-background"></div>
        </div>
      </div>
    );
  };

  // Render horizontal bar chart
  const renderHorizontalBarChart = (
    data: Array<{
      category?: string;
      week?: string;
      type?: string;
      rate: number;
    }>,
  ) => {
    return (
      <div className="space-y-4">
        {data.map((item, index) => (
          <div key={index} className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {item.category || item.week || item.type}
              </span>
              <span className="text-sm font-medium text-foreground">
                {item.rate}%
              </span>
            </div>
            <div className="h-2.5 w-full rounded-full bg-muted">
              <div
                className="h-2.5 rounded-full bg-primary"
                style={{ width: `${item.rate}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Last updated timestamp */}
      <div className="text-right text-xs text-muted-foreground">
        Last updated: {lastUpdated.toLocaleString()}
      </div>

      {/* Header with navigation and actions */}
      <div className="mb-6 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/admin/analytics')}
            className="flex items-center gap-1"
          >
            <RiArrowLeftLine className="h-4 w-4" />
            Back to Analytics
          </Button>
          <h1 className="text-2xl font-bold">User Analytics</h1>
        </div>
        <div className="flex flex-col items-start gap-3 sm:flex-row sm:items-center">
          <div className="flex items-center gap-2 rounded-md border p-2">
            <RiCalendarLine className="text-muted-foreground" />
            <select
              className="border-none bg-transparent text-sm focus:outline-none"
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as TimeRangeOption)}
            >
              <option value="7days">Last 7 days</option>
              <option value="30days">Last 30 days</option>
              <option value="90days">Last 90 days</option>
              <option value="year">Last year</option>
              <option value="custom">Custom range</option>
            </select>
          </div>

          {timeRange === 'custom' && (
            <div className="flex items-center gap-2 rounded-md border p-2">
              <input
                type="date"
                className="border-none bg-transparent text-sm focus:outline-none"
                value={formatDateForAPI(dateRange.startDate)}
                onChange={(e) => {
                  const newDate = new Date(e.target.value);
                  setDateRange((prev) => ({
                    ...prev,
                    startDate: newDate,
                  }));
                }}
              />
              <span className="text-muted-foreground">to</span>
              <input
                type="date"
                className="border-none bg-transparent text-sm focus:outline-none"
                value={formatDateForAPI(dateRange.endDate)}
                onChange={(e) => {
                  const newDate = new Date(e.target.value);
                  setDateRange((prev) => ({
                    ...prev,
                    endDate: newDate,
                  }));
                }}
              />
            </div>
          )}

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchAnalyticsData}
              disabled={isLoading}
              className="mr-2 flex items-center gap-1"
            >
              <RiRefreshLine
                className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              <RiDownloadLine className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Error message if API fails */}
      {error && (
        <Alert className="mb-4 border-destructive">
          <RiAlertLine className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="registration">Registration</TabsTrigger>
          <TabsTrigger value="retention">Retention</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <div className="flex items-center">
                    <RiUserLine className="mr-2 h-5 w-5 text-muted-foreground" />
                    <span className="text-2xl font-bold">
                      {analyticsData.overview.totalUsers.toLocaleString()}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Active Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <div className="flex items-center">
                    <RiUserLine className="mr-2 h-5 w-5 text-muted-foreground" />
                    <span className="text-2xl font-bold">
                      {analyticsData.overview.activeUsers.toLocaleString()}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  New Users (
                  {timeRange === 'year'
                    ? 'Year'
                    : `${timeRange.replace('days', ' days')}`}
                  )
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <div className="flex items-center">
                    <RiUserAddLine className="mr-2 h-5 w-5 text-muted-foreground" />
                    <span className="text-2xl font-bold">
                      {analyticsData.overview.newUsers.toLocaleString()}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Retention Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-8 w-24" />
                ) : (
                  <div className="flex items-center">
                    <RiLoginCircleLine className="mr-2 h-5 w-5 text-muted-foreground" />
                    <span className="text-2xl font-bold">
                      {analyticsData.overview.retentionRate}%
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>
                  New user registrations over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-64 w-full" />
                ) : (
                  renderBarChart(analyticsData.registration.daily)
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>User Retention</CardTitle>
                <CardDescription>Retention rate by week</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-64 w-full" />
                ) : (
                  renderHorizontalBarChart(analyticsData.retention.byWeek)
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="registration" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Registration Sources</CardTitle>
                <CardDescription>Where users are coming from</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                {isLoading ? (
                  <Skeleton className="h-[200px] w-[200px] rounded-full" />
                ) : (
                  <>
                    {renderPieChart(analyticsData.registration.sources)}
                    <div className="mt-6 grid grid-cols-2 gap-4">
                      {analyticsData.registration.sources.map(
                        (source, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-1 text-xs"
                          >
                            <div
                              className={`h-3 w-3 rounded-full bg-${
                                index === 0
                                  ? 'primary'
                                  : index === 1
                                    ? 'accent'
                                    : index === 2
                                      ? 'success'
                                      : index === 3
                                        ? 'warning'
                                        : 'destructive'
                              }`}
                            />
                            <span className="text-muted-foreground">
                              {source.source} ({source.percentage}%)
                            </span>
                          </div>
                        ),
                      )}
                    </div>
                  </>
                )}
              </CardContent>
              <CardFooter>
                <p className="text-xs text-muted-foreground">
                  Conversion rate: {analyticsData.registration.conversionRate}%
                </p>
              </CardFooter>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Daily Registrations</CardTitle>
                <CardDescription>New users per day</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-64 w-full" />
                ) : (
                  renderBarChart(analyticsData.registration.daily)
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="retention" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Retention by Week</CardTitle>
                <CardDescription>User retention over time</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-64 w-full" />
                ) : (
                  renderHorizontalBarChart(analyticsData.retention.byWeek)
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Retention by User Type</CardTitle>
                <CardDescription>Free vs. Premium users</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-64 w-full" />
                ) : (
                  renderHorizontalBarChart(analyticsData.retention.byUserType)
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Activity Distribution</CardTitle>
                <CardDescription>How users spend their time</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                {isLoading ? (
                  <Skeleton className="h-[200px] w-[200px] rounded-full" />
                ) : (
                  <>
                    {renderPieChart(
                      analyticsData.engagement.activityDistribution,
                    )}
                    <div className="mt-6 grid grid-cols-2 gap-4">
                      {analyticsData.engagement.activityDistribution.map(
                        (activity, index) => (
                          <div
                            key={index}
                            className="flex items-center gap-1 text-xs"
                          >
                            <div
                              className={`h-3 w-3 rounded-full bg-${
                                index === 0
                                  ? 'primary'
                                  : index === 1
                                    ? 'accent'
                                    : index === 2
                                      ? 'success'
                                      : 'destructive'
                              }`}
                            />
                            <span className="text-muted-foreground">
                              {activity.activity} ({activity.percentage}%)
                            </span>
                          </div>
                        ),
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>User Segmentation</CardTitle>
                <CardDescription>
                  User categories by engagement level
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-4">
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {analyticsData.engagement.userSegments.map(
                      (segment, index) => (
                        <div key={index} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-muted-foreground">
                              {segment.segment}
                            </span>
                            <span className="text-sm font-medium text-foreground">
                              {segment.percentage}%
                            </span>
                          </div>
                          <div className="h-2.5 w-full rounded-full bg-muted">
                            <div
                              className={`h-2.5 rounded-full ${index === 0 ? 'bg-success' : index === 1 ? 'bg-primary' : index === 2 ? 'bg-warning' : 'bg-destructive'}`}
                              style={{ width: `${segment.percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      ),
                    )}
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <p className="text-xs text-muted-foreground">
                  Highly Active: 5+ sessions per week
                  <br />
                  Regular: 2-4 sessions per week
                  <br />
                  Occasional: 1-4 sessions per month
                  <br />
                  Inactive: No activity in 30+ days
                </p>
              </CardFooter>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>User Engagement Timeline</CardTitle>
                  <CardDescription>Active users over time</CardDescription>
                </div>
                <div className="mt-2 flex items-center gap-2 sm:mt-0">
                  <Button variant="outline" size="sm" className="h-8">
                    <RiBarChartGroupedLine className="mr-1 h-4 w-4" />
                    Daily
                  </Button>
                  <Button variant="outline" size="sm" className="h-8">
                    <RiLineChartLine className="mr-1 h-4 w-4" />
                    Weekly
                  </Button>
                  <Button variant="outline" size="sm" className="h-8">
                    <RiPieChartLine className="mr-1 h-4 w-4" />
                    Monthly
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-64 w-full" />
              ) : (
                renderBarChart(analyticsData.engagement.activeUsersByDay)
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default UserAnalyticsPage;
