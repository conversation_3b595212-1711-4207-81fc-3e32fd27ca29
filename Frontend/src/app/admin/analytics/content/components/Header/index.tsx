import { Button } from '@/components/ui/button';
import {
  RiArrowLeftLine,
  RiCalendarLine,
  RiRefreshLine,
  RiDownloadLine,
} from 'react-icons/ri';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useRouter } from 'next/navigation';
import { TimeRangeOption } from '@/types/analytics';

interface IHeaderProps {
  timeRange: TimeRangeOption;
  setTimeRange: (value: TimeRangeOption) => void;
  isLoading: boolean;
  fetchAnalyticsData: () => void;
}

export default function Header({
  timeRange,
  setTimeRange,
  isLoading,
  fetchAnalyticsData,
}: IHeaderProps) {
  const router = useRouter();
  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="icon"
          onClick={() => router.push('/admin/analytics')}
        >
          <RiArrowLeftLine className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">Content Analytics</h1>
      </div>
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2 rounded-md border p-2">
          <RiCalendarLine className="text-muted-foreground" />
          <Select
            value={timeRange}
            onValueChange={(value) => setTimeRange(value as TimeRangeOption)}
          >
            <SelectTrigger className="border-0 bg-transparent p-0 shadow-none">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button
          variant="outline"
          onClick={fetchAnalyticsData}
          disabled={isLoading}
        >
          <RiRefreshLine
            className={`mr-1 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
          />
          Refresh
        </Button>
        <Button>
          <RiDownloadLine className="mr-1 h-4 w-4" /> Export Report
        </Button>
      </div>
    </div>
  );
}
