import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { IContentAnalytics } from '@/types/contentAnalytics';
import { JSX } from 'react';
import { Button } from '@/components/ui/button';
import {
  RiBarChartGroupedLine,
  RiLineChartLine,
  RiPieChartLine,
} from 'react-icons/ri';

interface IResourcesProps {
  isLoading: boolean;
  analyticsData: IContentAnalytics;
  renderBarChart: (data: Array<{ date: string; count: number }>) => JSX.Element;
}

export default function Roadmaps({
  isLoading,
  analyticsData,
  renderBarChart,
}: IResourcesProps) {
  return (
    <TabsContent value="roadmaps" className="mt-6 space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Weekly Roadmap Enrollments</CardTitle>
            <CardDescription>Number of enrollments over time</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              renderBarChart(analyticsData.roadmaps.weeklyEnrollments)
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Completion Rates</CardTitle>
            <CardDescription>
              Percentage of users who complete each roadmap
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <div className="h-64 space-y-4">
                {analyticsData.roadmaps.completionRates.map((item, index) => {
                  return (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span className="w-40 truncate font-medium">
                          {item.name}
                        </span>
                        <span className="text-muted-foreground">
                          {item.rate}%
                        </span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div
                          className="h-2 rounded-full bg-primary"
                          style={{ width: `${item.rate}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <CardTitle>Weekly Enrollments</CardTitle>
              <CardDescription>
                New roadmap enrollments over time
              </CardDescription>
            </div>
            <div className="mt-2 flex items-center gap-2 sm:mt-0">
              <Button variant="outline" size="sm" className="h-8">
                <RiBarChartGroupedLine className="mr-1 h-4 w-4" />
                Daily
              </Button>
              <Button variant="outline" size="sm" className="h-8">
                <RiLineChartLine className="mr-1 h-4 w-4" />
                Weekly
              </Button>
              <Button variant="outline" size="sm" className="h-8">
                <RiPieChartLine className="mr-1 h-4 w-4" />
                Monthly
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {renderBarChart(analyticsData.roadmaps.weeklyEnrollments)}
        </CardContent>
      </Card>
    </TabsContent>
  );
}
