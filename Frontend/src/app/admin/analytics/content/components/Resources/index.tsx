import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { TabsContent } from '@/components/ui/tabs';
import { IContentAnalytics } from '@/types/contentAnalytics';
import { JSX } from 'react';

interface IResourcesProps {
  isLoading: boolean;
  analyticsData: IContentAnalytics;
  renderBarChart: (data: Array<{ date: string; count: number }>) => JSX.Element;
}

export default function Resources({
  isLoading,
  analyticsData,
  renderBarChart,
}: IResourcesProps) {
  return (
    <TabsContent value="resources" className="mt-6 space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Content Performance Overview</CardTitle>
            <CardDescription>
              Completion rates and engagement metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-6 w-full" />
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <Skeleton className="h-24 w-full" />
                  <Skeleton className="h-24 w-full" />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <div className="mb-1 flex items-center justify-between">
                    <span className="text-sm font-medium">
                      Overall Completion Rate
                    </span>
                    <span className="text-sm font-medium">
                      {analyticsData.overview.completionRate}%
                    </span>
                  </div>
                  <div className="h-2.5 w-full rounded-full bg-muted">
                    <div
                      className="h-2.5 rounded-full bg-primary"
                      style={{
                        width: `${analyticsData.overview.completionRate}%`,
                      }}
                    ></div>
                  </div>
                </div>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="rounded-md border p-3">
                    <div className="text-sm font-medium">
                      Roadmap Enrollments
                    </div>
                    <div className="mt-1 text-2xl font-bold">
                      {analyticsData.overview.totalEnrollments.toLocaleString()}
                    </div>
                    <div className="mt-1 text-xs text-muted-foreground">
                      {analyticsData.overview.totalCompletions.toLocaleString()}{' '}
                      completions
                    </div>
                  </div>
                  <div className="rounded-md border p-3">
                    <div className="text-sm font-medium">
                      Challenge Attempts
                    </div>
                    <div className="mt-1 text-2xl font-bold">
                      {analyticsData.challenges.attempts
                        .reduce((sum, item) => sum + item.count, 0)
                        .toLocaleString()}
                    </div>
                    <div className="mt-1 text-xs text-muted-foreground">
                      {Math.round(
                        analyticsData.challenges.attempts.reduce(
                          (sum, item) => sum + item.successRate * item.count,
                          0,
                        ) /
                          analyticsData.challenges.attempts.reduce(
                            (sum, item) => sum + item.count,
                            0,
                          ),
                      )}
                      % success rate
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Top Resources</CardTitle>
            <CardDescription>Most viewed resources</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <div className="space-y-4">
                {analyticsData.resources.topResources.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-md border p-2"
                  >
                    <div className="flex-1">
                      <div className="truncate font-medium">{item.name}</div>
                      <div className="mt-1 flex items-center">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <svg
                            key={star}
                            className={`h-4 w-4 ${star <= Math.round(item.rating) ? 'fill-amber-500 text-amber-500' : 'text-muted-foreground'}`}
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                          </svg>
                        ))}
                        <span className="ml-1 text-xs text-muted-foreground">
                          {item.rating.toFixed(1)}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {item.views.toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">views</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Weekly Resource Views</CardTitle>
          <CardDescription>Number of resource views over time</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-64 w-full" />
          ) : (
            renderBarChart(analyticsData.resources.weeklyViews)
          )}
        </CardContent>
      </Card>
    </TabsContent>
  );
}
