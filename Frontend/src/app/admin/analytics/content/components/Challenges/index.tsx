import { TabsContent } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { IContentAnalytics } from '@/types/contentAnalytics';
import { JSX } from 'react';

interface IChallengesProps {
  isLoading: boolean;
  analyticsData: IContentAnalytics;
  renderBarChart: (data: Array<{ date: string; count: number }>) => JSX.Element;
}

export default function Challenges({
  isLoading,
  analyticsData,
  renderBarChart,
}: IChallengesProps) {
  return (
    <TabsContent value="challenges" className="mt-6 space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Challenge Attempts by Difficulty</CardTitle>
            <CardDescription>
              Number of attempts and success rates
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <div className="space-y-4">
                {analyticsData.challenges.attempts.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        {item.difficulty}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          {item.count.toLocaleString()} attempts
                        </span>
                        <span className="text-xs font-medium text-primary">
                          {item.successRate}% success
                        </span>
                      </div>
                    </div>
                    <div className="h-2.5 w-full overflow-hidden rounded-full bg-muted">
                      <div
                        className="h-2.5 rounded-full bg-primary"
                        style={{ width: `${item.successRate}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Top Challenges</CardTitle>
            <CardDescription>Most attempted challenges</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-64 w-full" />
            ) : (
              <div className="space-y-4">
                {analyticsData.challenges.topChallenges.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        {item.name}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          {item.attempts} attempts
                        </span>
                        <span className="text-xs font-medium text-primary">
                          {item.successRate}% success
                        </span>
                      </div>
                    </div>
                    <div className="h-2.5 w-full overflow-hidden rounded-full bg-muted">
                      <div
                        className="h-2.5 rounded-full bg-primary"
                        style={{ width: `${item.successRate}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>Weekly Challenge Attempts</CardTitle>
          <CardDescription>
            Number of challenge attempts over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderBarChart(analyticsData.challenges.weeklyAttempts)}
        </CardContent>
      </Card>
    </TabsContent>
  );
}
