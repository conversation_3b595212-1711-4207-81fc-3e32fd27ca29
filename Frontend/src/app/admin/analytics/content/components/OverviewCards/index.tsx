import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { RiCodeBoxLine, RiFileTextLine, RiRoadMapLine } from 'react-icons/ri';
import { IContentAnalytics } from '@/types/contentAnalytics';

interface IOverviewCardsProps {
  isLoading: boolean;
  analyticsData: IContentAnalytics;
}

export default function OverviewCards({
  isLoading,
  analyticsData,
}: IOverviewCardsProps) {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="space-y-1">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Roadmaps
            </CardTitle>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <CardDescription className="text-2xl font-bold">
                {analyticsData.overview.totalRoadmaps}
              </CardDescription>
            )}
          </div>
          <RiRoadMapLine className="h-6 w-6 text-primary" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-4 w-32" />
          ) : (
            <div className="text-xs text-muted-foreground">
              {analyticsData.overview.totalEnrollments.toLocaleString()} total
              enrollments
            </div>
          )}
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="space-y-1">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Challenges
            </CardTitle>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <CardDescription className="text-2xl font-bold">
                {analyticsData.overview.totalChallenges}
              </CardDescription>
            )}
          </div>
          <RiCodeBoxLine className="h-6 w-6 text-primary" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-4 w-32" />
          ) : (
            <div className="text-xs text-muted-foreground">
              {analyticsData.challenges.attempts
                .reduce((sum, item) => sum + item.count, 0)
                .toLocaleString()}{' '}
              total attempts
            </div>
          )}
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="space-y-1">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Resources
            </CardTitle>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <CardDescription className="text-2xl font-bold">
                {analyticsData.overview.totalResources}
              </CardDescription>
            )}
          </div>
          <RiFileTextLine className="h-6 w-6 text-primary" />
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <Skeleton className="h-4 w-32" />
          ) : (
            <div className="text-xs text-muted-foreground">
              {analyticsData.resources.usage
                .reduce((sum, item) => sum + item.views, 0)
                .toLocaleString()}{' '}
              total views
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
