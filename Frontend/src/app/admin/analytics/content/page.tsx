/**
 * @file page.tsx
 * @description Content Analytics page for admin dashboard
 */
'use client';

import { useState, useEffect, useCallback } from 'react';
import { RiAlertLine } from 'react-icons/ri';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAxiosGet } from '@/hooks/useAxios';
import {
  IContentAnalyticsResponse,
  IContentAnalytics,
  transformContentAnalytics,
} from '@/types/contentAnalytics';
import {
  TimeRangeOption,
  getDateRangeFromOption,
  formatDateForAPI,
} from '@/types/analytics';
import Resources from './components/Resources';
import Challenges from './components/Challenges';
import Roadmaps from './components/Roadmaps';
import OverviewCards from './components/OverviewCards';
import Header from './components/Header';

function ContentAnalyticsPage() {
  const [timeRange, setTimeRange] = useState<TimeRangeOption>('30days');
  const [activeTab, setActiveTab] = useState('overview');
  const [dateRange, setDateRange] = useState(getDateRangeFromOption(timeRange));
  const [analytics, setAnalytics] = useState<IContentAnalytics | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [error, setError] = useState<string | null>(null);

  // Use the existing useAxiosGet hook
  const [fetchContentAnalytics, contentAnalyticsState] =
    useAxiosGet<IContentAnalyticsResponse>('/analytics/content');

  // Determine loading state
  const isLoading = contentAnalyticsState.isLoading;

  // Fetch content analytics data
  const fetchAnalyticsData = useCallback(async () => {
    setError(null);

    try {
      // Fetch content analytics with date range parameters
      const response = await fetchContentAnalytics({
        params: {
          startDate: formatDateForAPI(dateRange.startDate),
          endDate: formatDateForAPI(dateRange.endDate),
        },
      });

      if (response.success && response.data) {
        // Transform the API data to frontend format
        const transformedData = transformContentAnalytics(response.data);
        setAnalytics(transformedData);
        setLastUpdated(new Date());
      } else {
        setError(response.message || 'Failed to load content analytics data');
      }
    } catch (error) {
      console.error('Error fetching content analytics data:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to load content analytics data';
      setError(errorMessage);
    }
  }, [fetchContentAnalytics, dateRange]);

  // Update date range when time range changes
  useEffect(() => {
    setDateRange(getDateRangeFromOption(timeRange));
  }, [timeRange]);

  // Fetch data when date range changes
  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  // Create empty data structure for when API data is not yet loaded
  const emptyAnalyticsData: IContentAnalytics = {
    overview: {
      totalRoadmaps: 0,
      totalChallenges: 0,
      totalResources: 0,
      totalEnrollments: 0,
      totalCompletions: 0,
      completionRate: 0,
    },
    roadmaps: {
      enrollments: [],
      completionRates: [],
      weeklyEnrollments: [],
    },
    challenges: {
      attempts: [],
      topChallenges: [],
      weeklyAttempts: [],
    },
    resources: {
      usage: [],
      topResources: [],
      weeklyViews: [],
    },
  };

  // Use API data or empty data structure when loading
  const analyticsData = analytics || emptyAnalyticsData;

  // Render bar chart for weekly data visualization
  const renderBarChart = (data: Array<{ date: string; count: number }>) => {
    if (!data || data.length === 0) return <div>No data available</div>;

    const maxCount = Math.max(...data.map((item) => item.count));
    return (
      <div className="flex h-64 items-end justify-between gap-1">
        {data.map((item, index) => (
          <div key={index} className="flex flex-col items-center">
            <div
              className="w-12 rounded-t-md bg-primary"
              style={{
                height: `${maxCount > 0 ? (item.count / maxCount) * 100 : 0}%`,
              }}
            ></div>
            <div className="mt-2 text-sm text-muted-foreground">
              {new Date(item.date).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
              })}
            </div>
            <div className="text-xs text-muted-foreground/70">{item.count}</div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Error message if API fails */}
      {error && (
        <Alert className="mb-4 border-destructive">
          <RiAlertLine className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Header with navigation and actions */}
      <Header
        timeRange={timeRange}
        setTimeRange={setTimeRange}
        isLoading={isLoading}
        fetchAnalyticsData={fetchAnalyticsData}
      />

      {/* Last updated timestamp */}
      <div className="text-right text-xs text-muted-foreground">
        Last updated: {lastUpdated.toLocaleString()}
      </div>

      {/* Overview Cards */}
      <OverviewCards isLoading={isLoading} analyticsData={analyticsData} />

      {/* Tabs for different analytics sections */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="roadmaps">Roadmaps</TabsTrigger>
          <TabsTrigger value="challenges">Challenges</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
        </TabsList>

        {/* Roadmaps Tab */}
        <Roadmaps
          isLoading={isLoading}
          analyticsData={analyticsData}
          renderBarChart={renderBarChart}
        />

        {/* Challenges Tab */}
        <Challenges
          isLoading={isLoading}
          analyticsData={analyticsData}
          renderBarChart={renderBarChart}
        />

        {/* Resources Tab */}
        <Resources
          isLoading={isLoading}
          analyticsData={analyticsData}
          renderBarChart={renderBarChart}
        />
      </Tabs>
    </div>
  );
}

export default ContentAnalyticsPage;
