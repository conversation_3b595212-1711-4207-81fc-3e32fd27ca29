/**
 * @file page.tsx
 * @description Analytics page for admin dashboard
 */
'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  RiDownloadLine,
  RiCalendarLine,
  RiRefreshLine,
  RiAlertLine,
} from 'react-icons/ri';
import { useAxiosGet } from '@/hooks/useAxios';
import { AxiosError } from 'axios';
import {
  IPlatformAnalytics,
  IGeneralAnalytics,
  TimeRangeOption,
  getDateRangeFromOption,
  formatDateForAPI,
  transformPlatformAnalytics,
} from '@/types/analytics';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState<TimeRangeOption>('30days');
  const [dateRange, setDateRange] = useState(getDateRangeFromOption(timeRange));
  const [analytics, setAnalytics] = useState<IGeneralAnalytics | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [error, setError] = useState<string | null>(null);

  // Use the existing useAxiosGet hook
  const [fetchAnalytics, analyticsState] = useAxiosGet<IPlatformAnalytics>(
    '/analytics/platform',
  );

  // Determine loading state
  const isLoading = analyticsState.isLoading;

  // Track retry attempts
  const [retryCount, setRetryCount] = useState(0);
  const MAX_RETRIES = 3;

  // Reference to the abort controller for cancelling requests
  const abortControllerRef = useRef<AbortController | null>(null);

  // Fetch analytics data
  const fetchAnalyticsData = useCallback(async () => {
    // Prevent fetching if we've exceeded retry limit
    if (retryCount >= MAX_RETRIES) {
      setError(
        `Failed to load analytics data after ${MAX_RETRIES} attempts. Please try refreshing the page.`,
      );
      return;
    }

    // Cancel any in-flight requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create a new abort controller
    abortControllerRef.current = new AbortController();

    // Clear error before attempting to fetch
    setError(null);

    try {
      // Fetch platform analytics with date range parameters and abort signal
      const response = await fetchAnalytics({
        params: {
          startDate: formatDateForAPI(dateRange.startDate),
          endDate: formatDateForAPI(dateRange.endDate),
        },
        signal: abortControllerRef.current.signal,
      });

      if (response.success && response.data) {
        // Reset retry count on success
        setRetryCount(0);
        // Transform the API data to frontend format
        const transformedData = transformPlatformAnalytics(response.data);
        setAnalytics(transformedData);
        setLastUpdated(new Date());
      } else if (!response.success) {
        // Backend indicated failure
        // The primary message from backend for failure is in response.message
        console.error(
          'API error in fetchAnalyticsData (response.success is false):',
          response.message,
        );
        setError(
          response.message ||
            'Failed to load analytics data. API Error from response.',
        );
        // Increment retry count
        setRetryCount((prev) => prev + 1);
      }
    } catch (error) {
      // Catch block for errors during the fetchAnalytics() call itself
      // Don't handle aborted requests as errors
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.log('Request was cancelled');
        return;
      }

      console.error('Error fetching analytics data (catch block):', error);
      let errorMessage =
        'Failed to load analytics data. Client-side error during fetch.';
      if (error instanceof AxiosError) {
        // Attempt to get a more specific message from AxiosError response
        errorMessage = error.response?.data?.message || error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      setError(errorMessage);
      // Increment retry count
      setRetryCount((prev) => prev + 1);
    }
  }, [fetchAnalytics, dateRange, retryCount]);

  // Update date range when time range changes
  useEffect(() => {
    setDateRange(getDateRangeFromOption(timeRange));
  }, [timeRange]);

  // Fetch data when date range changes
  useEffect(() => {
    // Only fetch if dateRange is valid
    if (dateRange.startDate && dateRange.endDate) {
      fetchAnalyticsData();
    }

    // Cleanup function to abort any pending requests when component unmounts or dependencies change
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, [fetchAnalyticsData, dateRange]);

  // Create empty placeholder data for when analytics is null
  const emptyData = {
    userMetrics: {
      totalUsers: 0,
      activeUsers: 0,
      newUsers: 0,
      userGrowthRate: 0,
    },
    contentMetrics: {
      totalContent: 0,
      newContent: 0,
      averageViews: 0,
      mostViewedCategory: '',
    },
    engagementMetrics: {
      averageSessionDuration: '0 minutes',
      completionRate: 0,
      bounceRate: 0,
      returnRate: 0,
    },
    monthlyActiveUsers: [],
    contentViews: [],
  };

  // Use the API data or fallback to empty data when null
  const analyticsData = analytics || emptyData;

  return (
    <div className="space-y-6">
      {/* Error message if API fails */}
      {error && (
        <Alert className="mb-4 border-destructive">
          <RiAlertLine className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="mb-6 flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
        <div className="flex flex-col items-start gap-3 sm:flex-row sm:items-center">
          <div className="flex items-center gap-2 rounded-md border p-2">
            <RiCalendarLine className="text-muted-foreground" />
            <select
              className="border-none bg-transparent text-sm focus:outline-none"
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as TimeRangeOption)}
            >
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="7days">Last 7 days</option>
              <option value="30days">Last 30 days</option>
              <option value="90days">Last 90 days</option>
              <option value="year">Last year</option>
              <option value="custom">Custom range</option>
            </select>
          </div>

          {timeRange === 'custom' && (
            <div className="flex items-center gap-2 rounded-md border p-2">
              <input
                type="date"
                className="border-none bg-transparent text-sm focus:outline-none"
                value={formatDateForAPI(dateRange.startDate)}
                onChange={(e) => {
                  const newDate = new Date(e.target.value);
                  setDateRange((prev) => ({
                    ...prev,
                    startDate: newDate,
                  }));
                }}
              />
              <span className="text-muted-foreground">to</span>
              <input
                type="date"
                className="border-none bg-transparent text-sm focus:outline-none"
                value={formatDateForAPI(dateRange.endDate)}
                onChange={(e) => {
                  const newDate = new Date(e.target.value);
                  setDateRange((prev) => ({
                    ...prev,
                    endDate: newDate,
                  }));
                }}
              />
            </div>
          )}

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Reset retry count when manually refreshing
                setRetryCount(0);
                fetchAnalyticsData();
              }}
              disabled={isLoading}
            >
              <RiRefreshLine
                className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
              />
              Refresh
            </Button>
            <Button variant="outline" size="sm">
              <RiDownloadLine className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h2 className="mb-4 text-lg font-semibold text-card-foreground">
            User Metrics
          </h2>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Total Users</span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.userMetrics.totalUsers.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Active Users</span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.userMetrics.activeUsers.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">
                  New Users (
                  {timeRange === 'year'
                    ? 'Year'
                    : `${timeRange.replace('days', ' days')}`}
                  )
                </span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.userMetrics.newUsers.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Growth Rate</span>
                <span className="text-success text-xl font-bold">
                  +{analyticsData.userMetrics.userGrowthRate}%
                </span>
              </div>
            </div>
          )}
        </div>

        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h2 className="mb-4 text-lg font-semibold text-card-foreground">
            Content Metrics
          </h2>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Total Content</span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.contentMetrics.totalContent.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">New Content</span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.contentMetrics.newContent.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Avg. Views</span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.contentMetrics.averageViews.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">
                  Most Viewed Category
                </span>
                <span className="text-xl font-bold text-accent">
                  {analyticsData.contentMetrics.mostViewedCategory}
                </span>
              </div>
            </div>
          )}
        </div>

        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h2 className="mb-4 text-lg font-semibold text-card-foreground">
            Engagement Metrics
          </h2>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">
                  Avg. Session Duration
                </span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.engagementMetrics.averageSessionDuration}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Completion Rate</span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.engagementMetrics.completionRate}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Bounce Rate</span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.engagementMetrics.bounceRate}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Return Rate</span>
                <span className="text-xl font-bold text-foreground">
                  {analyticsData.engagementMetrics.returnRate}%
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h2 className="mb-4 text-lg font-semibold text-card-foreground">
            Monthly Active Users
          </h2>
          {isLoading ? (
            <Skeleton className="h-64 w-full" />
          ) : (
            <div className="flex h-64 items-end justify-between">
              {analyticsData.monthlyActiveUsers.map((item) => (
                <div key={item.month} className="flex flex-col items-center">
                  <div
                    className="w-12 rounded-t-md bg-accent"
                    style={{
                      height: `${Math.min((item.users / Math.max(...analyticsData.monthlyActiveUsers.map((i) => i.users), 1)) * 100, 100)}px`,
                    }}
                  ></div>
                  <div className="mt-2 text-sm text-muted-foreground">
                    {item.month}
                  </div>
                  <div className="text-xs text-muted-foreground/70">
                    {item.users}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h2 className="mb-4 text-lg font-semibold text-card-foreground">
            Content Views by Category
          </h2>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
          ) : (
            <div className="space-y-4">
              {analyticsData.contentViews.map((item) => (
                <div key={item.category} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {item.category}
                    </span>
                    <span className="text-sm font-medium text-foreground">
                      {item.views.toLocaleString()}
                    </span>
                  </div>
                  <div className="h-2.5 w-full rounded-full bg-muted">
                    <div
                      className="h-2.5 rounded-full bg-accent"
                      style={{
                        width: `${Math.min((item.views / Math.max(...analyticsData.contentViews.map((i) => i.views), 1)) * 100, 100)}%`,
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Additional Insights */}
      <div className="rounded-lg border bg-card p-6 shadow-sm">
        <h2 className="mb-4 text-lg font-semibold text-card-foreground">
          Key Insights
        </h2>
        {isLoading ? (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="border-l-4 border-green-500 py-2 pl-4">
              <h3 className="font-medium text-green-700">User Growth</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                User growth is up{' '}
                {analyticsData.userMetrics.userGrowthRate.toFixed(1)}% compared
                to the previous period, with most new users coming from organic
                search.
              </p>
            </div>
            <div className="border-l-4 border-blue-500 py-2 pl-4">
              <h3 className="font-medium text-blue-700">Content Engagement</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {analyticsData.contentMetrics.mostViewedCategory} content
                continues to be the most popular, with tutorials showing the
                highest completion rates.
              </p>
            </div>
            <div className="border-l-4 border-yellow-500 py-2 pl-4">
              <h3 className="font-medium text-yellow-700">
                Retention Opportunity
              </h3>
              <p className="mt-1 text-sm text-muted-foreground">
                There&apos;s an opportunity to improve the{' '}
                {analyticsData.engagementMetrics.returnRate}% return rate by
                enhancing the onboarding experience and follow-up emails.
              </p>
            </div>
            <div className="border-l-4 border-purple-500 py-2 pl-4">
              <h3 className="font-medium text-purple-700">Content Gap</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {/* TODO: Replace with actual data from API when available */}
                User searches indicate high demand for additional{' '}
                {analyticsData.contentMetrics.mostViewedCategory} content, with
                current engagement metrics showing room for growth.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Last updated timestamp */}
      <div className="mt-6 text-right text-xs text-muted-foreground">
        Last updated: {lastUpdated.toLocaleString()}
      </div>
    </div>
  );
}
