/**
 * @file page.tsx
 * @description Export Functionality for admin dashboard
 */
'use client';

import { useState, useCallback, useEffect } from 'react';
import {
  RiArrowLeftLine,
  RiDownloadLine,
  RiFileExcel2Line,
  RiFilePdf2Line,
  RiFileZipLine,
  RiFileLine,
  RiCalendarLine,
  RiFilterLine,
  RiMailLine,
  RiHistoryLine,
} from 'react-icons/ri';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';

interface IExportHistory {
  id: string;
  name: string;
  type: string;
  format: string;
  dateCreated: string;
  size: string;
  status: 'completed' | 'processing' | 'failed';
}

function ExportFunctionalityPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('new-export');
  const [dataType, setDataType] = useState('users');
  const [exportFormat, setExportFormat] = useState('csv');
  const [dateRange, setDateRange] = useState('last30days');
  const [includeHeaders, setIncludeHeaders] = useState(true);
  const [compressionEnabled, setCompressionEnabled] = useState(false);
  const [sendEmail, setSendEmail] = useState(false);
  const [emailAddress, setEmailAddress] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [exportHistory, setExportHistory] = useState<IExportHistory[]>([]);

  // Selected fields for each data type
  const [selectedUserFields, setSelectedUserFields] = useState<string[]>([
    'id',
    'email',
    'name',
    'role',
    'createdAt',
    'lastLogin',
  ]);
  const [selectedRoadmapFields, setSelectedRoadmapFields] = useState<string[]>([
    'id',
    'title',
    'description',
    'enrollments',
    'completions',
  ]);
  const [selectedChallengeFields, setSelectedChallengeFields] = useState<
    string[]
  >(['id', 'title', 'difficulty', 'attempts', 'successRate']);
  const [selectedResourceFields, setSelectedResourceFields] = useState<
    string[]
  >(['id', 'title', 'type', 'views', 'completions', 'rating']);

  // Available fields for each data type
  const availableFields = {
    users: [
      { id: 'id', name: 'User ID' },
      { id: 'email', name: 'Email' },
      { id: 'name', name: 'Full Name' },
      { id: 'username', name: 'Username' },
      { id: 'role', name: 'Role' },
      { id: 'status', name: 'Status' },
      { id: 'createdAt', name: 'Registration Date' },
      { id: 'lastLogin', name: 'Last Login' },
      { id: 'country', name: 'Country' },
      { id: 'city', name: 'City' },
      { id: 'enrolledRoadmaps', name: 'Enrolled Roadmaps' },
      { id: 'completedChallenges', name: 'Completed Challenges' },
      { id: 'points', name: 'Points' },
      { id: 'badges', name: 'Badges' },
    ],
    roadmaps: [
      { id: 'id', name: 'Roadmap ID' },
      { id: 'title', name: 'Title' },
      { id: 'description', name: 'Description' },
      { id: 'category', name: 'Category' },
      { id: 'difficulty', name: 'Difficulty' },
      { id: 'createdAt', name: 'Creation Date' },
      { id: 'updatedAt', name: 'Last Updated' },
      { id: 'enrollments', name: 'Total Enrollments' },
      { id: 'completions', name: 'Total Completions' },
      { id: 'completionRate', name: 'Completion Rate' },
      { id: 'avgCompletionTime', name: 'Avg. Completion Time' },
      { id: 'rating', name: 'Rating' },
    ],
    challenges: [
      { id: 'id', name: 'Challenge ID' },
      { id: 'title', name: 'Title' },
      { id: 'description', name: 'Description' },
      { id: 'category', name: 'Category' },
      { id: 'difficulty', name: 'Difficulty' },
      { id: 'createdAt', name: 'Creation Date' },
      { id: 'updatedAt', name: 'Last Updated' },
      { id: 'attempts', name: 'Total Attempts' },
      { id: 'successRate', name: 'Success Rate' },
      { id: 'avgCompletionTime', name: 'Avg. Completion Time' },
      { id: 'tags', name: 'Tags' },
    ],
    resources: [
      { id: 'id', name: 'Resource ID' },
      { id: 'title', name: 'Title' },
      { id: 'description', name: 'Description' },
      { id: 'type', name: 'Resource Type' },
      { id: 'category', name: 'Category' },
      { id: 'difficulty', name: 'Difficulty' },
      { id: 'createdAt', name: 'Creation Date' },
      { id: 'updatedAt', name: 'Last Updated' },
      { id: 'views', name: 'Total Views' },
      { id: 'completions', name: 'Total Completions' },
      { id: 'completionRate', name: 'Completion Rate' },
      { id: 'rating', name: 'Rating' },
      { id: 'estimatedTime', name: 'Estimated Time' },
    ],
  };

  // Mock export history data for fallback
  const mockExportHistory: IExportHistory[] = [
    {
      id: 'export_001',
      name: 'User Data Export',
      type: 'users',
      format: 'csv',
      dateCreated: '2025-05-23T14:30:00Z',
      size: '2.4 MB',
      status: 'completed',
    },
    {
      id: 'export_002',
      name: 'Roadmap Analytics',
      type: 'roadmaps',
      format: 'excel',
      dateCreated: '2025-05-22T10:15:00Z',
      size: '1.8 MB',
      status: 'completed',
    },
    {
      id: 'export_003',
      name: 'Challenge Performance Data',
      type: 'challenges',
      format: 'json',
      dateCreated: '2025-05-21T16:45:00Z',
      size: '3.2 MB',
      status: 'completed',
    },
    {
      id: 'export_004',
      name: 'Resource Usage Analytics',
      type: 'resources',
      format: 'pdf',
      dateCreated: '2025-05-20T09:20:00Z',
      size: '5.1 MB',
      status: 'completed',
    },
    {
      id: 'export_005',
      name: 'Full Platform Backup',
      type: 'all',
      format: 'zip',
      dateCreated: '2025-05-19T12:00:00Z',
      size: '24.7 MB',
      status: 'completed',
    },
  ];

  // Toggle field selection
  const toggleField = (field: string) => {
    switch (dataType) {
      case 'users':
        setSelectedUserFields((prev) =>
          prev.includes(field)
            ? prev.filter((f) => f !== field)
            : [...prev, field],
        );
        break;
      case 'roadmaps':
        setSelectedRoadmapFields((prev) =>
          prev.includes(field)
            ? prev.filter((f) => f !== field)
            : [...prev, field],
        );
        break;
      case 'challenges':
        setSelectedChallengeFields((prev) =>
          prev.includes(field)
            ? prev.filter((f) => f !== field)
            : [...prev, field],
        );
        break;
      case 'resources':
        setSelectedResourceFields((prev) =>
          prev.includes(field)
            ? prev.filter((f) => f !== field)
            : [...prev, field],
        );
        break;
    }
  };

  // Get selected fields based on data type
  const getSelectedFields = () => {
    switch (dataType) {
      case 'users':
        return selectedUserFields;
      case 'roadmaps':
        return selectedRoadmapFields;
      case 'challenges':
        return selectedChallengeFields;
      case 'resources':
        return selectedResourceFields;
      default:
        return [];
    }
  };

  // API hooks
  const [createExport] = useAxiosPost<
    { id: string; downloadUrl?: string },
    {
      dataType: string;
      format: string;
      dateRange: string;
      fields: string[];
      options: {
        includeHeaders: boolean;
        compression: boolean;
        email: string | null;
      };
    }
  >('/analytics/exports');
  const [fetchExportHistory] = useAxiosGet<{ exports: IExportHistory[] }>(
    '/analytics/exports/history',
  );

  // Load export history
  const loadExportHistory = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetchExportHistory();
      if (response.success && response.data?.exports) {
        setExportHistory(response.data.exports);
      } else {
        // Use mock data as fallback if API fails
        setExportHistory(mockExportHistory);
        toast({
          title: 'Warning',
          description: 'Using cached export history data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error loading export history:', error);
      // Use mock data as fallback
      setExportHistory(mockExportHistory);
      toast({
        title: 'Error',
        description: 'Failed to load export history, using cached data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [fetchExportHistory, toast, mockExportHistory]);

  // Handle export action
  const handleExport = async () => {
    if (sendEmail && !emailAddress) {
      toast({
        title: 'Validation Error',
        description: 'Please enter an email address for email delivery',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      const exportData = {
        dataType,
        format: exportFormat,
        dateRange,
        fields: getSelectedFields(),
        options: {
          includeHeaders,
          compression: compressionEnabled,
          email: sendEmail ? emailAddress : null,
        },
      };

      const response = await createExport(exportData);

      if (response.success) {
        toast({
          title: 'Export Started',
          description:
            'Your export has been initiated and will be available shortly',
        });

        // If immediate download is available
        if (response.data?.downloadUrl) {
          // Create a temporary link and trigger download
          const link = document.createElement('a');
          link.href = response.data.downloadUrl;
          link.setAttribute(
            'download',
            `export-${dataType}-${new Date().toISOString()}.${exportFormat}`,
          );
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }

        // Refresh export history and show history tab
        await loadExportHistory();
        setActiveTab('export-history');
      } else {
        toast({
          title: 'Export Failed',
          description: response.message || 'Failed to start export process',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error starting export:', error);
      toast({
        title: 'Export Failed',
        description: 'An error occurred while starting the export',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Format icon based on file type
  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'csv':
        return <RiFileLine className="h-5 w-5" />;
      case 'excel':
        return <RiFileExcel2Line className="h-5 w-5" />;
      case 'pdf':
        return <RiFilePdf2Line className="h-5 w-5" />;
      case 'json':
        return <RiFileLine className="h-5 w-5" />;
      case 'zip':
        return <RiFileZipLine className="h-5 w-5" />;
      default:
        return <RiDownloadLine className="h-5 w-5" />;
    }
  };

  // Load export history when component mounts or tab changes to history
  useEffect(() => {
    if (activeTab === 'export-history') {
      loadExportHistory();
    }
  }, [activeTab, loadExportHistory]);

  return (
    <div className="space-y-6 p-6">
      {/* Header with navigation and actions */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/admin/analytics')}
            className="h-8 w-8"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">Export Data</h2>
        </div>
      </div>

      {/* Tabs for export options */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="new-export">New Export</TabsTrigger>
          <TabsTrigger value="export-history">Export History</TabsTrigger>
        </TabsList>

        {/* New Export Tab */}
        <TabsContent value="new-export" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Export Configuration</CardTitle>
              <CardDescription>
                Configure your data export settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Data Type Selection */}
              <div className="space-y-2">
                <Label htmlFor="data-type">Data Type</Label>
                <Select value={dataType} onValueChange={setDataType}>
                  <SelectTrigger id="data-type">
                    <SelectValue placeholder="Select data type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="users">Users</SelectItem>
                    <SelectItem value="roadmaps">Roadmaps</SelectItem>
                    <SelectItem value="challenges">Challenges</SelectItem>
                    <SelectItem value="resources">Resources</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Export Format */}
              <div className="space-y-2">
                <Label htmlFor="export-format">Export Format</Label>
                <Select value={exportFormat} onValueChange={setExportFormat}>
                  <SelectTrigger id="export-format">
                    <SelectValue placeholder="Select export format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="excel">Excel</SelectItem>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Date Range */}
              <div className="space-y-2">
                <Label htmlFor="date-range">Date Range</Label>
                <div className="flex items-center gap-2 rounded-md border p-2">
                  <RiCalendarLine className="text-muted-foreground" />
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="border-0 p-0 shadow-none focus:ring-0">
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="last7days">Last 7 days</SelectItem>
                      <SelectItem value="last30days">Last 30 days</SelectItem>
                      <SelectItem value="last90days">Last 90 days</SelectItem>
                      <SelectItem value="thisYear">This year</SelectItem>
                      <SelectItem value="allTime">All time</SelectItem>
                      <SelectItem value="custom">Custom range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Field Selection */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Fields to Include</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 text-xs"
                    onClick={() => {
                      const allFields = availableFields[
                        dataType as keyof typeof availableFields
                      ].map((f) => f.id);
                      switch (dataType) {
                        case 'users':
                          setSelectedUserFields(allFields);
                          break;
                        case 'roadmaps':
                          setSelectedRoadmapFields(allFields);
                          break;
                        case 'challenges':
                          setSelectedChallengeFields(allFields);
                          break;
                        case 'resources':
                          setSelectedResourceFields(allFields);
                          break;
                      }
                    }}
                  >
                    Select All
                  </Button>
                </div>
                <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
                  {availableFields[
                    dataType as keyof typeof availableFields
                  ].map((field) => (
                    <div key={field.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`field-${field.id}`}
                        checked={getSelectedFields().includes(field.id)}
                        onCheckedChange={() => toggleField(field.id)}
                      />
                      <label
                        htmlFor={`field-${field.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {field.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Additional Options */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Additional Options</h3>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-headers"
                    checked={includeHeaders}
                    onCheckedChange={(checked) => setIncludeHeaders(!!checked)}
                  />
                  <label
                    htmlFor="include-headers"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Include column headers
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="compression-enabled"
                    checked={compressionEnabled}
                    onCheckedChange={(checked) =>
                      setCompressionEnabled(!!checked)
                    }
                  />
                  <label
                    htmlFor="compression-enabled"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Compress export file (ZIP)
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="send-email"
                    checked={sendEmail}
                    onCheckedChange={(checked) => setSendEmail(!!checked)}
                  />
                  <label
                    htmlFor="send-email"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Send export via email
                  </label>
                </div>

                {sendEmail && (
                  <div className="ml-6 space-y-2">
                    <Label htmlFor="email-address">Email Address</Label>
                    <Input
                      id="email-address"
                      placeholder="Enter email address"
                      value={emailAddress}
                      onChange={(e) => setEmailAddress(e.target.value)}
                    />
                  </div>
                )}
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={handleExport}
                  className="flex items-center gap-1"
                  disabled={getSelectedFields().length === 0}
                >
                  <RiDownloadLine className="h-4 w-4" />
                  Export Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Export History Tab */}
        <TabsContent value="export-history" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>Export History</CardTitle>
                  <CardDescription>
                    View and download your previous exports
                  </CardDescription>
                </div>
                <div className="mt-2 sm:mt-0">
                  <Button variant="outline" className="flex items-center gap-1">
                    <RiFilterLine className="h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {exportHistory.map((export_) => (
                  <div
                    key={export_.id}
                    className="flex items-center justify-between rounded-md border p-4"
                  >
                    <div className="flex items-center gap-4">
                      <div
                        className={`rounded-md p-2 ${
                          export_.format === 'csv'
                            ? 'bg-green-100 text-green-600'
                            : export_.format === 'excel'
                              ? 'bg-blue-100 text-blue-600'
                              : export_.format === 'pdf'
                                ? 'bg-red-100 text-red-600'
                                : export_.format === 'json'
                                  ? 'bg-amber-100 text-amber-600'
                                  : export_.format === 'zip'
                                    ? 'bg-purple-100 text-purple-600'
                                    : 'bg-gray-100 text-gray-600'
                        }`}
                      >
                        {getFormatIcon(export_.format)}
                      </div>
                      <div>
                        <h4 className="font-medium">{export_.name}</h4>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <span className="capitalize">{export_.type}</span>
                          <span>•</span>
                          <span className="uppercase">{export_.format}</span>
                          <span>•</span>
                          <span>{export_.size}</span>
                          <span>•</span>
                          <span>
                            {new Date(export_.dateCreated).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (export_.status === 'completed') {
                            // Handle download
                            window.open(
                              `/api/analytics/exports/${export_.id}/download`,
                              '_blank',
                            );
                          } else {
                            toast({
                              title: 'Export Not Ready',
                              description:
                                'This export is still processing or has failed',
                              variant: 'destructive',
                            });
                          }
                        }}
                        disabled={export_.status !== 'completed'}
                      >
                        <RiDownloadLine className="h-4 w-4" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Scheduled Exports</CardTitle>
              <CardDescription>
                View and manage your scheduled exports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between rounded-md border p-4">
                  <div className="flex items-center gap-4">
                    <div className="rounded-md bg-blue-100 p-2 text-blue-600">
                      <RiHistoryLine className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">Weekly User Report</h4>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>Every Monday at 9:00 AM</span>
                        <span>•</span>
                        <span>CSV Format</span>
                        <span>•</span>
                        <span>2 recipients</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch defaultChecked={true} />
                  </div>
                </div>

                <div className="flex items-center justify-between rounded-md border p-4">
                  <div className="flex items-center gap-4">
                    <div className="rounded-md bg-blue-100 p-2 text-blue-600">
                      <RiHistoryLine className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">Monthly Analytics Report</h4>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>1st of every month at 8:00 AM</span>
                        <span>•</span>
                        <span>Excel Format</span>
                        <span>•</span>
                        <span>5 recipients</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch defaultChecked={true} />
                  </div>
                </div>

                <div className="flex items-center justify-between rounded-md border p-4">
                  <div className="flex items-center gap-4">
                    <div className="rounded-md bg-blue-100 p-2 text-blue-600">
                      <RiHistoryLine className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">
                        Quarterly Performance Report
                      </h4>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>Last day of quarter at 11:59 PM</span>
                        <span>•</span>
                        <span>PDF Format</span>
                        <span>•</span>
                        <span>3 recipients</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch defaultChecked={true} />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ExportFunctionalityPage;
