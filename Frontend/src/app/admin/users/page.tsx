/**
 * @file page.tsx
 * @description User management page for admin dashboard with advanced DataTable
 */
'use client';

import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { debounce } from 'lodash';
import {
  RiUserLine,
  RiEyeLine,
  RiEditLine,
  RiDeleteBinLine,
  RiMoreLine,
  RiBankLine,
  RiLoader4Line,
  RiSearchLine,
  RiAddLine,
  RiRefreshLine,
} from 'react-icons/ri';
import { ColumnDef } from '@tanstack/react-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from '@/hooks/use-toast';
import {
  SuspendUserModal,
  DeleteUserModal,
} from '@/components/admin/UserActionModals';
import { useAxiosGet, useAxiosPost, useAxiosDelete } from '@/hooks/useAxios';
import { IUser, USER_API } from '@/services/userService';
import { DataTable } from '@/components/ui/DataTable';

// Define user list params interface
interface IUserListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  role?: string;
}

interface IPagination {
  pageIndex: number;
  pageSize: number;
  pageCount?: number;
}

// Define bulk action modal props
interface IBulkActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
}

function BulkActionModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
}: IBulkActionModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-md rounded-lg bg-background p-6">
        <h2 className="mb-4 text-xl font-semibold">{title}</h2>
        <p className="mb-6 text-muted-foreground">{description}</p>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={onConfirm}>
            Confirm
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function UsersPage() {
  const router = useRouter();
  const [selectedRows, setSelectedRows] = useState<IUser[]>([]);
  const [users, setUsers] = useState<IUser[]>([]);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Pagination state
  const [pagination, setPagination] = useState<IPagination>({
    pageIndex: 0,
    pageSize: 10,
    pageCount: 1,
  });

  // Track if we're currently loading data
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);

  // Action modals state
  const [suspendUserModalOpen, setSuspendUserModalOpen] = useState(false);
  const [deleteUserModalOpen, setDeleteUserModalOpen] = useState(false);
  const [bulkDeleteModalOpen, setBulkDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<IUser | null>(null);

  // API hooks - properly type the response to include meta information
  const [fetchUsers] = useAxiosGet<{
    data: IUser[];
    meta: {
      total: number;
      currentPage: number;
      totalPages: number;
      limit: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  }>(USER_API.LIST);

  const [suspendUser] = useAxiosPost(USER_API.SUSPEND);

  const [deleteUser] = useAxiosDelete(USER_API.DELETE);

  const [bulkDeleteUsers, bulkDeleteUsersState] = useAxiosPost(
    USER_API.BULK_ACTION,
  );

  // Destructure loading state for bulk delete
  const isBulkDeleting = bulkDeleteUsersState.isLoading;

  // Load users function
  const loadUsers = useCallback(
    async (params?: IUserListParams) => {
      try {
        setIsTableLoading(true);
        setError('');

        // Clear existing users to prevent layout shifts
        if (params?.page !== undefined || params?.limit !== undefined) {
          setUsers([]);
        }

        // Prepare query parameters
        const queryParams: Record<string, number | string> = {
          page:
            params?.page !== undefined ? params.page : pagination.pageIndex + 1,
          limit:
            params?.limit !== undefined ? params.limit : pagination.pageSize,
        };

        // Add search query if provided
        if (params?.search || searchQuery) {
          queryParams.search = params?.search || searchQuery;
        }

        // Fetch users with query parameters
        const response = await fetchUsers({ params: queryParams });

        // Update users and pagination state if response is successful
        if (response?.data) {
          // Set users from response data
          if (Array.isArray(response.data.data)) {
            setUsers(response.data.data);
          } else {
            setUsers([]);
          }

          // Update pagination metadata directly from the API response
          if (response.data.meta) {
            const meta = response.data.meta;
            setPagination({
              pageIndex: meta.currentPage - 1, // Convert to 0-based for the table
              pageSize: meta.limit,
              pageCount: meta.totalPages,
            });
          }

          // Update last updated timestamp
          setLastUpdated(new Date());
        }
      } catch (err) {
        console.error('Error loading users:', err);
        setError('Failed to load users. Please try again.');
        setUsers([]);
      } finally {
        setIsTableLoading(false);
      }
    },
    [fetchUsers, pagination.pageIndex, pagination.pageSize, searchQuery],
  );

  // Handle pagination change
  const handlePaginationChange = useCallback(
    (pageIndex: number, pageSize: number) => {
      // Set loading state immediately to prevent layout shifts
      setIsTableLoading(true);

      // Clear current users data to prevent layout shifts
      setUsers([]);

      // Call API with updated pagination
      loadUsers({
        page: pageIndex + 1, // API uses 1-based indexing
        limit: pageSize,
      });
    },
    [loadUsers],
  );

  // Debounced search handler
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      // Set loading state immediately
      setIsTableLoading(true);

      // Clear current users data to prevent layout shifts
      setUsers([]);

      // Update search query
      setSearchQuery(value);

      // Reset pagination state to first page
      setPagination((prev: IPagination) => ({
        ...prev,
        pageIndex: 0,
      }));

      // Load users with search query
      loadUsers({
        search: value,
        page: 1, // Reset to first page on search
      });
    }, 500),
    [loadUsers],
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      debouncedSearch(e.target.value);
    },
    [debouncedSearch],
  );

  // We're not using row selection currently, but keeping this commented for future reference
  /*
  const handleRowSelectionChange = useCallback(
    (selectedRows: IUser[]) => {
      setSelectedRows(selectedRows);
    },
    []
  );
  */

  // If row selection is needed in the future, add these props to the DataTable:
  // showRowSelection={true}
  // onRowSelectionChange={handleRowSelectionChange}

  // User actions
  const handleViewUser = useCallback(
    (user: IUser) => {
      router.push(`/admin/users/${user.id}`);
    },
    [router],
  );

  const handleEditUser = useCallback(
    (user: IUser) => {
      router.push(`/admin/users/${user.id}/edit`);
    },
    [router],
  );

  const handleSuspendUser = useCallback(
    async (user: IUser) => {
      try {
        const response = await suspendUser({
          data: { userId: user.id, suspend: !user.is_suspended },
        });

        if (response?.success) {
          toast({
            title: 'Success',
            description: `User ${user.is_suspended ? 'activated' : 'suspended'} successfully`,
          });
          loadUsers();
        } else {
          toast({
            title: 'Error',
            description: `Failed to ${user.is_suspended ? 'activate' : 'suspend'} user`,
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error suspending user:', error);
        toast({
          title: 'Error',
          description: `Failed to ${user.is_suspended ? 'activate' : 'suspend'} user`,
          variant: 'destructive',
        });
      } finally {
        setSuspendUserModalOpen(false);
        setSelectedUser(null);
      }
    },
    [suspendUser, loadUsers],
  );

  const handleDeleteUser = useCallback(
    async (user: IUser) => {
      try {
        const response = await deleteUser({
          url: `${USER_API.DELETE}/${user.id}`,
        });

        if (response?.success) {
          toast({
            title: 'Success',
            description: 'User deleted successfully',
          });
          loadUsers();
        } else {
          toast({
            title: 'Error',
            description: 'Failed to delete user',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete user',
          variant: 'destructive',
        });
      } finally {
        setDeleteUserModalOpen(false);
        setSelectedUser(null);
      }
    },
    [deleteUser, loadUsers],
  );

  const handleBulkDelete = useCallback(async () => {
    if (selectedRows.length === 0) return;

    try {
      const userIds = selectedRows.map((user) => user.id);
      const response = await bulkDeleteUsers({
        data: { userIds },
      });

      if (response?.success) {
        toast({
          title: 'Success',
          description: `${selectedRows.length} users deleted successfully`,
        });
        setSelectedRows([]);
        loadUsers();
      } else {
        toast({
          title: 'Error',
          description: 'Failed to delete users',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error bulk deleting users:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete users',
        variant: 'destructive',
      });
    } finally {
      setBulkDeleteModalOpen(false);
    }
  }, [bulkDeleteUsers, loadUsers, selectedRows]);

  // Initial load and pagination change effect
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  // Define table columns
  const columns: ColumnDef<IUser>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
              <RiUserLine className="text-primary" />
            </div>
            <div>
              <div className="font-medium">
                {user.firstName && user.lastName
                  ? `${user.firstName} ${user.lastName}`
                  : user.username}
              </div>
              <div className="text-xs text-muted-foreground">{user.email}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'roles',
      header: 'Role',
      cell: ({ row }) => {
        const roles = row.original.roles || [];
        if (roles.length === 0)
          return <span className="text-muted-foreground">-</span>;

        const role = roles[0];
        const roleName = role.name;
        // We only need the name for display

        return (
          <div className="flex items-center gap-1">
            <RiBankLine className="text-muted-foreground" />
            <span>{roleName}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const user = row.original;
        const isActive = !user.is_suspended;
        // Using only valid badge variants: 'default', 'secondary', 'destructive', 'outline'
        const badgeVariant = isActive ? 'default' : 'destructive';
        const statusText = isActive ? 'Active' : 'Suspended';

        return <Badge variant={badgeVariant}>{statusText}</Badge>;
      },
    },
    {
      accessorKey: 'created_at',
      header: 'Created At',
      cell: ({ row }) => {
        if (!row.original.created_at) return <span>-</span>;
        const date = new Date(row.original.created_at);
        return <span>{date.toLocaleDateString()}</span>;
      },
    },
    {
      accessorKey: 'updated_at',
      header: 'Last Updated',
      cell: ({ row }) => {
        if (!row.original.updated_at) return <span>-</span>;
        const date = new Date(row.original.updated_at);
        return <span>{date.toLocaleDateString()}</span>;
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const user = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <RiMoreLine className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleViewUser(user)}>
                <RiEyeLine className="mr-2 h-4 w-4" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditUser(user)}>
                <RiEditLine className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setSelectedUser(user);
                  setSuspendUserModalOpen(true);
                }}
              >
                <RiUserLine className="mr-2 h-4 w-4" />
                {user.is_suspended ? 'Activate' : 'Suspend'}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setSelectedUser(user);
                  setDeleteUserModalOpen(true);
                }}
                className="text-destructive"
              >
                <RiDeleteBinLine className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4">
        <h1 className="text-2xl font-bold">User Management</h1>
        <p className="text-muted-foreground">
          Manage users, their roles, and permissions.
        </p>
      </div>

      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div className="w-full flex-1 sm:max-w-xs">
          <div className="relative">
            <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-10"
              onChange={handleSearchChange}
            />
          </div>
        </div>

        <div className="flex items-center gap-2 self-end sm:self-auto">
          {selectedRows.length > 0 && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setBulkDeleteModalOpen(true)}
              disabled={isBulkDeleting}
            >
              {isBulkDeleting ? (
                <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RiDeleteBinLine className="mr-2 h-4 w-4" />
              )}
              Delete Selected ({selectedRows.length})
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => loadUsers()}
            disabled={isTableLoading}
          >
            {isTableLoading ? (
              <RiLoader4Line className="h-4 w-4 animate-spin" />
            ) : (
              <RiRefreshLine className="h-4 w-4" />
            )}
          </Button>

          <Button size="sm" onClick={() => router.push('/admin/users/new')}>
            <RiAddLine className="mr-2 h-4 w-4" />
            Add New User
          </Button>
        </div>
      </div>

      {error && (
        <div className="flex items-center justify-between rounded-md bg-destructive/10 p-4">
          <p className="font-medium text-destructive">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadUsers()}
            disabled={isTableLoading}
          >
            {isTableLoading ? (
              <RiLoader4Line className="h-4 w-4 animate-spin" />
            ) : (
              <RiRefreshLine className="mr-2 h-4 w-4" />
            )}
            Retry
          </Button>
        </div>
      )}

      <DataTable
        columns={columns}
        data={users}
        onPaginationChange={handlePaginationChange}
        initialPageIndex={pagination.pageIndex}
        initialPageSize={pagination.pageSize}
        pageCount={pagination.pageCount}
        showRowSelection={false}
        isLoading={isTableLoading}
        serverSidePagination={true}
      />

      {lastUpdated && (
        <p className="text-xs text-muted-foreground">
          Last updated: {lastUpdated.toLocaleTimeString()}
        </p>
      )}

      {/* Modals */}
      {selectedUser && (
        <>
          <SuspendUserModal
            isOpen={suspendUserModalOpen}
            onClose={() => {
              setSuspendUserModalOpen(false);
              setSelectedUser(null);
            }}
            onConfirm={() => handleSuspendUser(selectedUser)}
            userId={selectedUser.id}
            userName={selectedUser.username || selectedUser.email}
          />

          <DeleteUserModal
            isOpen={deleteUserModalOpen}
            onClose={() => {
              setDeleteUserModalOpen(false);
              setSelectedUser(null);
            }}
            onConfirm={() => handleDeleteUser(selectedUser)}
            userId={selectedUser.id}
            userName={selectedUser.username || selectedUser.email}
          />
        </>
      )}

      <BulkActionModal
        isOpen={bulkDeleteModalOpen}
        onClose={() => setBulkDeleteModalOpen(false)}
        onConfirm={handleBulkDelete}
        title="Delete Selected Users"
        description={`Are you sure you want to delete ${selectedRows.length} selected users? This action cannot be undone.`}
      />
    </div>
  );
}
