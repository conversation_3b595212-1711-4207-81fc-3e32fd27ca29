/**
 * @file page.tsx
 * @description User edit page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiSaveLine,
  RiMailSendLine,
  RiLockPasswordLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { useAxiosGet, useAxiosPut, useAxiosPost } from '@/hooks/useAxios';
import {
  USER_API,
  IUser,
  IUserUpdateParams,
  IPasswordResetParams,
} from '@/services/userService';

// Extended user interface for the edit page
interface IUserExtended extends IUser {
  fullName: string;
  phone?: string;
  location?: string;
  emailVerified: boolean;
}

export default function UserEditPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;

  const [user, setUser] = useState<IUserExtended | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    fullName: '',
    role: '',
    status: '',
    phone: '',
    location: '',
    bio: '',
    emailVerified: false,
  });

  // Password reset state
  const [passwordReset, setPasswordReset] = useState({
    newPassword: '',
    confirmPassword: '',
  });

  // API hooks
  const [fetchUser] = useAxiosGet<{
    success: boolean;
    data?: IUserExtended;
    message?: string;
  }>(USER_API.DETAIL.replace('{{userId}}', userId));
  const [updateUser] = useAxiosPut<{ success: boolean; message?: string }>(
    USER_API.UPDATE.replace('{{userId}}', userId),
  );
  const [resetPassword] = useAxiosPost<{ success: boolean; message?: string }>(
    USER_API.RESET_PASSWORD.replace('{{userId}}', userId),
  );
  const [verifyEmail] = useAxiosPost<{ success: boolean; message?: string }>(
    USER_API.VERIFY_EMAIL.replace('{{userId}}', userId),
  );
  const [sendVerificationEmail] = useAxiosPost<{
    success: boolean;
    message?: string;
  }>(USER_API.SEND_VERIFICATION.replace('{{userId}}', userId));

  // Fetch user data
  useEffect(() => {
    const getUserData = async () => {
      setLoading(true);
      try {
        const response = await fetchUser();

        if (response.success && response.data) {
          // Convert response.data to IUserExtended type
          const userData = response.data as unknown as IUserExtended;
          setUser(userData);
          setFormData({
            username: userData.username,
            email: userData.email,
            fullName: userData.fullName || '',
            role: userData.role,
            status: userData.status,
            phone: userData.phone || '',
            location: userData.location || '',
            bio: userData.bio || '',
            emailVerified: userData.emailVerified,
          });
        } else {
          toast({
            title: 'Error',
            description: response.message || 'Failed to fetch user data',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast({
          title: 'Error',
          description: 'An error occurred while fetching user data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    getUserData();
  }, [fetchUser, userId, toast]);

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle password reset input changes
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordReset((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Prepare update data using IUserUpdateParams interface
      const updateData: IUserUpdateParams = {
        username: formData.username,
        email: formData.email,
        fullName: formData.fullName,
        role: formData.role,
        status: formData.status,
        phone: formData.phone,
        location: formData.location,
        bio: formData.bio,
        emailVerified: formData.emailVerified,
      };

      const response = await updateUser(updateData);

      if (response.success) {
        toast({
          title: 'Success',
          description: 'User information has been updated successfully.',
        });

        // Update user object with form data
        if (user) {
          setUser({
            ...user,
            ...formData,
          });
        }
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to update user information',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while updating user information',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle password reset
  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    // Validate passwords
    if (passwordReset.newPassword !== passwordReset.confirmPassword) {
      toast({
        title: 'Error',
        description: 'Passwords do not match.',
        variant: 'destructive',
      });
      setSaving(false);
      return;
    }

    if (passwordReset.newPassword.length < 8) {
      toast({
        title: 'Error',
        description: 'Password must be at least 8 characters long.',
        variant: 'destructive',
      });
      setSaving(false);
      return;
    }

    try {
      // Prepare password reset data
      const resetData: IPasswordResetParams = {
        userId: userId,
        newPassword: passwordReset.newPassword,
        sendNotification: true,
      };

      const response = await resetPassword(resetData);

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Password has been reset successfully.',
        });

        // Clear password fields
        setPasswordReset({
          newPassword: '',
          confirmPassword: '',
        });
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to reset password',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error resetting password:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while resetting the password',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle email verification
  const handleVerifyEmail = async () => {
    setSaving(true);

    try {
      const response = await verifyEmail({ userId });

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Email has been verified successfully.',
        });

        // Update email verified status
        setFormData((prev) => ({
          ...prev,
          emailVerified: true,
        }));
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to verify email',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error verifying email:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while verifying the email',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle sending verification email
  const handleSendVerificationEmail = async () => {
    setSaving(true);

    try {
      const response = await sendVerificationEmail({ userId });

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Verification email has been sent successfully.',
        });
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Failed to send verification email',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error sending verification email:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while sending the verification email',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-4 text-muted-foreground">Loading user data...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold">User Not Found</h2>
          <p className="mt-2 text-muted-foreground">
            The user you&apos;re looking for doesn&apos;t exist or has been
            deleted.
          </p>
          <Button className="mt-4" onClick={() => router.push('/admin/users')}>
            Go Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => router.back()}
          >
            <RiArrowLeftLine className="mr-1" /> Back
          </Button>
          <h1 className="text-2xl font-bold">Edit User: {user.fullName}</h1>
          <p className="text-muted-foreground">User ID: {user.id}</p>
        </div>
      </div>

      {/* Tabs for different sections */}
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="profile">Profile Information</TabsTrigger>
          <TabsTrigger value="account">Account Settings</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        {/* Profile Information Tab */}
        <TabsContent value="profile" className="mt-4">
          <Card>
            <form onSubmit={handleSubmit}>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Update the user&apos;s personal information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      placeholder="Full Name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      placeholder="Username"
                    />
                  </div>
                </div>

                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Email Address"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="Phone Number"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="City, Country"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio">Bio</Label>
                  <Textarea
                    id="bio"
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    placeholder="User bio"
                    rows={4}
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  type="submit"
                  disabled={saving}
                  className="flex items-center gap-1"
                >
                  {saving ? (
                    'Saving...'
                  ) : (
                    <>
                      <RiSaveLine /> Save Changes
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Account Settings Tab */}
        <TabsContent value="account" className="mt-4">
          <Card>
            <form onSubmit={handleSubmit}>
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
                <CardDescription>
                  Manage user role and account status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="role">User Role</Label>
                    <Select
                      value={formData.role}
                      onValueChange={(value) =>
                        handleSelectChange('role', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Administrator">
                          Administrator
                        </SelectItem>
                        <SelectItem value="Moderator">Moderator</SelectItem>
                        <SelectItem value="Contributor">Contributor</SelectItem>
                        <SelectItem value="User">User</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Account Status</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) =>
                        handleSelectChange('status', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Active">Active</SelectItem>
                        <SelectItem value="Inactive">Inactive</SelectItem>
                        <SelectItem value="Suspended">Suspended</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="emailVerified">Email Verified</Label>
                    <Switch
                      id="emailVerified"
                      checked={formData.emailVerified}
                      onCheckedChange={(checked) =>
                        handleSwitchChange('emailVerified', checked)
                      }
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {formData.emailVerified
                      ? 'User email is verified.'
                      : 'User email is not verified. You can manually verify it or send a verification email.'}
                  </p>

                  {!formData.emailVerified && (
                    <div className="mt-4 flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleVerifyEmail}
                        disabled={saving}
                      >
                        Manually Verify Email
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleSendVerificationEmail}
                        disabled={saving}
                        className="flex items-center gap-1"
                      >
                        <RiMailSendLine /> Send Verification Email
                      </Button>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Registration Date</Label>
                  <p className="text-muted-foreground">
                    {user.registrationDate}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Last Login</Label>
                  <p className="text-muted-foreground">{user.lastLogin}</p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  type="submit"
                  disabled={saving}
                  className="flex items-center gap-1"
                >
                  {saving ? (
                    'Saving...'
                  ) : (
                    <>
                      <RiSaveLine /> Save Changes
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="mt-4">
          <Card>
            <form onSubmit={handlePasswordReset}>
              <CardHeader>
                <CardTitle>Reset Password</CardTitle>
                <CardDescription>
                  Set a new password for the user
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    value={passwordReset.newPassword}
                    onChange={handlePasswordChange}
                    placeholder="New password"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={passwordReset.confirmPassword}
                    onChange={handlePasswordChange}
                    placeholder="Confirm new password"
                  />
                </div>

                <div className="rounded-md bg-muted p-4">
                  <div className="flex items-center gap-2">
                    <RiLockPasswordLine className="text-muted-foreground" />
                    <p className="text-sm font-medium">Password Requirements</p>
                  </div>
                  <ul className="mt-2 text-xs text-muted-foreground">
                    <li>Minimum 8 characters long</li>
                    <li>Must contain at least one uppercase letter</li>
                    <li>Must contain at least one number</li>
                    <li>Must contain at least one special character</li>
                  </ul>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  type="submit"
                  disabled={saving}
                  className="flex items-center gap-1"
                >
                  {saving ? (
                    'Resetting...'
                  ) : (
                    <>
                      <RiLockPasswordLine /> Reset Password
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
