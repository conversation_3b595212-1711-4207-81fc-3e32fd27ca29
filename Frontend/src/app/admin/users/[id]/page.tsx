/**
 * @file page.tsx
 * @description User detail page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiEditLine,
  RiDeleteBinLine,
  RiMailLine,
  RiPhoneLine,
  RiMapPinLine,
  RiCalendarLine,
  RiTimeLine,
} from 'react-icons/ri';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

// Define user interface
interface IUser {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: string;
  status: string;
  lastLogin: string;
  registrationDate: string;
  phone?: string;
  location?: string;
  bio?: string;
  profileImage?: string;
}

// Define activity interface
interface IActivity {
  id: string;
  action: string;
  timestamp: string;
  details: string;
}

// Define roadmap interface
interface IRoadmap {
  id: string;
  title: string;
  progress: number;
  enrollmentDate: string;
  lastActivity: string;
}

// Define achievement interface
interface IAchievement {
  id: string;
  title: string;
  description: string;
  earnedDate: string;
  icon: string;
}

// Define challenge interface
interface IChallenge {
  id: string;
  title: string;
  completionDate: string;
  difficulty: string;
  score: number;
}

export default function UserDetailPage() {
  const params = useParams();
  const userId = params.id as string;

  const [user, setUser] = useState<IUser | null>(null);
  const [activities, setActivities] = useState<IActivity[]>([]);
  const [roadmaps, setRoadmaps] = useState<IRoadmap[]>([]);
  const [achievements, setAchievements] = useState<IAchievement[]>([]);
  const [challenges, setChallenges] = useState<IChallenge[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // TODO: Replace with actual API call to fetch user data
    const fetchUserData = () => {
      setLoading(true);

      // Simulate API call
      setTimeout(() => {
        // Mock user data
        const mockUser: IUser = {
          id: userId,
          username: 'johndoe',
          email: '<EMAIL>',
          fullName: 'John Doe',
          role: 'User',
          status: 'Active',
          lastLogin: '2 hours ago',
          registrationDate: '2023-05-10',
          phone: '+****************',
          location: 'New York, USA',
          bio: 'Software engineer with 5 years of experience in web development.',
          profileImage: '/assets/avatars/avatar-1.png',
        };

        // Mock activity data
        const mockActivities: IActivity[] = [
          {
            id: '1',
            action: 'Completed Challenge',
            timestamp: '2 hours ago',
            details:
              'Completed the "Advanced JavaScript" challenge with a score of 95/100.',
          },
          {
            id: '2',
            action: 'Started Roadmap',
            timestamp: '1 day ago',
            details: 'Started the "Full Stack Development" roadmap.',
          },
          {
            id: '3',
            action: 'Earned Achievement',
            timestamp: '3 days ago',
            details: 'Earned the "JavaScript Master" achievement.',
          },
          {
            id: '4',
            action: 'Completed Lesson',
            timestamp: '1 week ago',
            details: 'Completed the "React Hooks" lesson.',
          },
          {
            id: '5',
            action: 'Account Login',
            timestamp: '1 week ago',
            details: 'Logged in from a new device (Chrome on Windows).',
          },
        ];

        // Mock roadmap data
        const mockRoadmaps: IRoadmap[] = [
          {
            id: '1',
            title: 'Full Stack Development',
            progress: 45,
            enrollmentDate: '2023-05-15',
            lastActivity: '1 day ago',
          },
          {
            id: '2',
            title: 'Machine Learning Basics',
            progress: 20,
            enrollmentDate: '2023-06-10',
            lastActivity: '1 week ago',
          },
          {
            id: '3',
            title: 'Cloud Computing',
            progress: 75,
            enrollmentDate: '2023-04-05',
            lastActivity: '3 days ago',
          },
        ];

        // Mock achievement data
        const mockAchievements: IAchievement[] = [
          {
            id: '1',
            title: 'JavaScript Master',
            description:
              'Completed all JavaScript challenges with a score of 90% or higher.',
            earnedDate: '3 days ago',
            icon: '🏆',
          },
          {
            id: '2',
            title: '7-Day Streak',
            description:
              'Logged in and completed at least one lesson for 7 consecutive days.',
            earnedDate: '1 week ago',
            icon: '🔥',
          },
          {
            id: '3',
            title: 'First Challenge',
            description: 'Completed your first coding challenge.',
            earnedDate: '2 months ago',
            icon: '🚀',
          },
          {
            id: '4',
            title: 'Community Helper',
            description: 'Answered 10 questions in the community forum.',
            earnedDate: '1 month ago',
            icon: '🤝',
          },
        ];

        // Mock challenge data
        const mockChallenges: IChallenge[] = [
          {
            id: '1',
            title: 'Advanced JavaScript',
            completionDate: '2 hours ago',
            difficulty: 'Hard',
            score: 95,
          },
          {
            id: '2',
            title: 'React Components',
            completionDate: '2 days ago',
            difficulty: 'Medium',
            score: 88,
          },
          {
            id: '3',
            title: 'CSS Flexbox',
            completionDate: '1 week ago',
            difficulty: 'Easy',
            score: 100,
          },
          {
            id: '4',
            title: 'Node.js Basics',
            completionDate: '2 weeks ago',
            difficulty: 'Medium',
            score: 92,
          },
          {
            id: '5',
            title: 'API Integration',
            completionDate: '1 month ago',
            difficulty: 'Hard',
            score: 85,
          },
        ];

        setUser(mockUser);
        setActivities(mockActivities);
        setRoadmaps(mockRoadmaps);
        setAchievements(mockAchievements);
        setChallenges(mockChallenges);
        setLoading(false);
      }, 500); // Simulate loading delay
    };

    fetchUserData();
  }, [userId]);

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="mt-4 text-muted-foreground">Loading user data...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold">User Not Found</h2>
          <p className="mt-2 text-muted-foreground">
            The user you&apos;re looking for doesn&apos;t exist or has been
            deleted.
          </p>
          <Button className="mt-4" onClick={() => window.history.back()}>
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with navigation and actions */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => window.history.back()}
          >
            <RiArrowLeftLine className="mr-1" /> Back to Users
          </Button>
          <h1 className="text-2xl font-bold">{user.fullName}</h1>
          <p className="text-muted-foreground">User ID: {user.id}</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() =>
              (window.location.href = `/admin/users/${user.id}/edit`)
            }
          >
            <RiEditLine /> Edit User
          </Button>
          <Button variant="destructive" className="flex items-center gap-1">
            <RiDeleteBinLine /> Delete User
          </Button>
        </div>
      </div>

      {/* User profile card and tabs */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* User profile card */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-center">
              <div className="flex h-32 w-32 items-center justify-center overflow-hidden rounded-full bg-muted">
                {user.profileImage ? (
                  <img
                    src={user.profileImage}
                    alt={user.username}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <span className="text-4xl">
                    {user.username.charAt(0).toUpperCase()}
                  </span>
                )}
              </div>
            </div>

            <div className="mt-4 text-center">
              <h3 className="text-lg font-semibold">{user.fullName}</h3>
              <p className="text-muted-foreground">@{user.username}</p>
              <div className="mt-2">
                <Badge
                  className={`${user.status === 'Active' ? 'bg-success/20 text-success' : 'bg-destructive/20 text-destructive'}`}
                >
                  {user.status}
                </Badge>
                <Badge
                  className={`ml-2 ${
                    user.role === 'Administrator'
                      ? 'bg-accent/20 text-accent'
                      : user.role === 'Moderator'
                        ? 'bg-primary/20 text-primary'
                        : user.role === 'Contributor'
                          ? 'bg-success/20 text-success'
                          : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {user.role}
                </Badge>
              </div>
            </div>

            <div className="mt-6 space-y-3">
              <div className="flex items-center">
                <RiMailLine className="mr-2 text-muted-foreground" />
                <span>{user.email}</span>
              </div>
              {user.phone && (
                <div className="flex items-center">
                  <RiPhoneLine className="mr-2 text-muted-foreground" />
                  <span>{user.phone}</span>
                </div>
              )}
              {user.location && (
                <div className="flex items-center">
                  <RiMapPinLine className="mr-2 text-muted-foreground" />
                  <span>{user.location}</span>
                </div>
              )}
              <div className="flex items-center">
                <RiCalendarLine className="mr-2 text-muted-foreground" />
                <span>Registered on {user.registrationDate}</span>
              </div>
              <div className="flex items-center">
                <RiTimeLine className="mr-2 text-muted-foreground" />
                <span>Last login {user.lastLogin}</span>
              </div>
            </div>

            {user.bio && (
              <div className="mt-4">
                <h4 className="mb-2 font-medium">Bio</h4>
                <p className="text-sm text-muted-foreground">{user.bio}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Tabs for activity, roadmaps, achievements, and challenges */}
        <div className="md:col-span-2">
          <Tabs defaultValue="activity" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="activity">Activity</TabsTrigger>
              <TabsTrigger value="roadmaps">Roadmaps</TabsTrigger>
              <TabsTrigger value="achievements">Achievements</TabsTrigger>
              <TabsTrigger value="challenges">Challenges</TabsTrigger>
            </TabsList>

            {/* Activity Tab */}
            <TabsContent value="activity" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>
                    User&apos;s recent actions and events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activities.length > 0 ? (
                      activities.map((activity) => (
                        <div
                          key={activity.id}
                          className="border-b border-border pb-4 last:border-0 last:pb-0"
                        >
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{activity.action}</h4>
                            <span className="text-sm text-muted-foreground">
                              {activity.timestamp}
                            </span>
                          </div>
                          <p className="mt-1 text-sm text-muted-foreground">
                            {activity.details}
                          </p>
                        </div>
                      ))
                    ) : (
                      <p className="text-center text-muted-foreground">
                        No recent activity found.
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Roadmaps Tab */}
            <TabsContent value="roadmaps" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Enrolled Roadmaps</CardTitle>
                  <CardDescription>
                    Learning paths the user is currently following
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {roadmaps.length > 0 ? (
                      roadmaps.map((roadmap) => (
                        <div
                          key={roadmap.id}
                          className="border-b border-border pb-4 last:border-0 last:pb-0"
                        >
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{roadmap.title}</h4>
                            <span className="text-sm text-muted-foreground">
                              Enrolled: {roadmap.enrollmentDate}
                            </span>
                          </div>
                          <div className="mt-2">
                            <div className="flex items-center justify-between text-sm">
                              <span>Progress: {roadmap.progress}%</span>
                              <span className="text-muted-foreground">
                                Last activity: {roadmap.lastActivity}
                              </span>
                            </div>
                            <div className="mt-1 h-2 w-full overflow-hidden rounded-full bg-muted">
                              <div
                                className="h-full bg-primary"
                                style={{ width: `${roadmap.progress}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-center text-muted-foreground">
                        No roadmaps enrolled.
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Achievements Tab */}
            <TabsContent value="achievements" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Achievements</CardTitle>
                  <CardDescription>
                    Badges and awards earned by the user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 sm:grid-cols-2">
                    {achievements.length > 0 ? (
                      achievements.map((achievement) => (
                        <div
                          key={achievement.id}
                          className="rounded-lg border border-border p-4"
                        >
                          <div className="flex items-center gap-3">
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted text-xl">
                              {achievement.icon}
                            </div>
                            <div>
                              <h4 className="font-medium">
                                {achievement.title}
                              </h4>
                              <p className="text-xs text-muted-foreground">
                                Earned {achievement.earnedDate}
                              </p>
                            </div>
                          </div>
                          <p className="mt-2 text-sm text-muted-foreground">
                            {achievement.description}
                          </p>
                        </div>
                      ))
                    ) : (
                      <p className="col-span-2 text-center text-muted-foreground">
                        No achievements earned yet.
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Challenges Tab */}
            <TabsContent value="challenges" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Completed Challenges</CardTitle>
                  <CardDescription>
                    Coding challenges the user has completed
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-border">
                          <th className="pb-2 text-left font-medium">
                            Challenge
                          </th>
                          <th className="pb-2 text-left font-medium">
                            Difficulty
                          </th>
                          <th className="pb-2 text-left font-medium">Score</th>
                          <th className="pb-2 text-left font-medium">
                            Completed
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {challenges.length > 0 ? (
                          challenges.map((challenge) => (
                            <tr
                              key={challenge.id}
                              className="border-b border-border last:border-0"
                            >
                              <td className="py-3">{challenge.title}</td>
                              <td className="py-3">
                                <Badge
                                  className={`${
                                    challenge.difficulty === 'Easy'
                                      ? 'bg-success/20 text-success'
                                      : challenge.difficulty === 'Medium'
                                        ? 'bg-warning/20 text-warning'
                                        : 'bg-destructive/20 text-destructive'
                                  }`}
                                >
                                  {challenge.difficulty}
                                </Badge>
                              </td>
                              <td className="py-3">{challenge.score}/100</td>
                              <td className="py-3 text-muted-foreground">
                                {challenge.completionDate}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td
                              colSpan={4}
                              className="py-4 text-center text-muted-foreground"
                            >
                              No challenges completed yet.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
