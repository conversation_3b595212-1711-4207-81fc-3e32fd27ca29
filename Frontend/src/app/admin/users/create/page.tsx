/**
 * @file page.tsx
 * @description User creation page for admin dashboard
 */
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { RiArrowLeftLine, RiUserAddLine, RiMailSendLine } from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/use-toast';
import { useAxiosPost } from '@/hooks/useAxios';
import { USER_API } from '@/services/userService';

interface IFormDataType {
  username: string;
  email: string;
  fullName: string;
  role: string;
  password: string;
  confirmPassword: string;
  phone: string;
  location: string;
  bio: string;
}

function CreateUserPage() {
  const router = useRouter();

  const [creating, setCreating] = useState(false);
  const [sendWelcomeEmail, setSendWelcomeEmail] = useState(true);

  // Form state
  const [formData, setFormData] = useState<IFormDataType>({
    username: '',
    email: '',
    fullName: '',
    role: 'User',
    password: '',
    confirmPassword: '',
    phone: '',
    location: '',
    bio: '',
  });

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // API hooks
  const [createUser] = useAxiosPost<{ success: boolean; message?: string }>(
    USER_API.CREATE,
  );

  // Validate password strength
  const validatePassword = (password: string): boolean => {
    // Password must be at least 8 characters long
    if (password.length < 8) return false;

    // Password must contain at least one uppercase letter
    if (!/[A-Z]/.test(password)) return false;

    // Password must contain at least one number
    if (!/[0-9]/.test(password)) return false;

    // Password must contain at least one special character
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) return false;

    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      toast({
        title: 'Password Error',
        description: 'Passwords do not match. Please try again.',
        variant: 'destructive',
      });
      return;
    }

    // Validate password strength
    if (!validatePassword(formData.password)) {
      toast({
        title: 'Password Error',
        description:
          'Password does not meet the requirements. Please check the password guidelines.',
        variant: 'destructive',
      });
      return;
    }

    // Validate required fields
    if (
      !formData.username ||
      !formData.email ||
      !formData.fullName ||
      !formData.password
    ) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast({
        title: 'Invalid Email',
        description: 'Please enter a valid email address.',
        variant: 'destructive',
      });
      return;
    }

    setCreating(true);

    try {
      // Prepare user data for API
      const userData = {
        username: formData.username,
        email: formData.email,
        fullName: formData.fullName,
        role: formData.role,
        password: formData.password,
        phone: formData.phone || undefined,
        location: formData.location || undefined,
        bio: formData.bio || undefined,
        sendWelcomeEmail,
      };

      // Call API to create user
      const response = await createUser(userData);

      if (response.success) {
        toast({
          title: 'User Created',
          description: `User ${formData.fullName} has been successfully created.${sendWelcomeEmail ? ' A welcome email has been sent.' : ''}`,
        });

        // Redirect to users list
        router.push('/admin/users');
      } else {
        toast({
          title: 'Error',
          description:
            response.message || 'Failed to create user. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating user:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with navigation */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <Button
            variant="ghost"
            className="-ml-4 mb-2 flex items-center text-muted-foreground"
            onClick={() => router.push('/admin/users')}
          >
            <RiArrowLeftLine className="mr-1" /> Back to Users
          </Button>
          <h1 className="text-2xl font-bold">Create New User</h1>
          <p className="text-muted-foreground">
            Add a new user to the platform
          </p>
        </div>
      </div>

      {/* User creation form */}
      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>
              Enter the details for the new user account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="mb-4 text-lg font-medium">Basic Information</h3>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="fullName">
                    Full Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    placeholder="Full Name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="username">
                    Username <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    placeholder="Username"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h3 className="mb-4 text-lg font-medium">Contact Information</h3>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="email">
                    Email Address <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Email Address"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Phone Number"
                  />
                </div>
              </div>

              <div className="mt-4 space-y-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="City, Country"
                />
              </div>
            </div>

            {/* Account Settings */}
            <div>
              <h3 className="mb-4 text-lg font-medium">Account Settings</h3>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="role">
                    User Role <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    value={formData.role}
                    onValueChange={(value) => handleSelectChange('role', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Administrator">
                        Administrator
                      </SelectItem>
                      <SelectItem value="Moderator">Moderator</SelectItem>
                      <SelectItem value="Contributor">Contributor</SelectItem>
                      <SelectItem value="User">User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="sendWelcomeEmail">Send Welcome Email</Label>
                    <Switch
                      id="sendWelcomeEmail"
                      checked={sendWelcomeEmail}
                      onCheckedChange={setSendWelcomeEmail}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Send an email to the user with their account details and
                    login instructions.
                  </p>
                </div>
              </div>
            </div>

            {/* Password */}
            <div>
              <h3 className="mb-4 text-lg font-medium">Set Password</h3>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="password">
                    Password <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="Password"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">
                    Confirm Password <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="Confirm Password"
                    required
                  />
                </div>
              </div>

              <div className="mt-4 rounded-md bg-muted p-4">
                <p className="text-sm font-medium">Password Requirements</p>
                <ul className="mt-2 text-xs text-muted-foreground">
                  <li>Minimum 8 characters long</li>
                  <li>Must contain at least one uppercase letter</li>
                  <li>Must contain at least one number</li>
                  <li>Must contain at least one special character</li>
                </ul>
              </div>
            </div>

            {/* Additional Information */}
            <div>
              <h3 className="mb-4 text-lg font-medium">
                Additional Information
              </h3>
              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  placeholder="User bio"
                  rows={4}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/users')}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={creating}
              className="flex items-center gap-1"
            >
              {creating ? (
                'Creating...'
              ) : (
                <>
                  <RiUserAddLine /> Create User{' '}
                  {sendWelcomeEmail && <RiMailSendLine className="ml-1" />}
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}

export default CreateUserPage;
