/**
 * @file BulkContentOperations.tsx
 * @description Component for performing bulk operations on content
 */
'use client';

import { useState } from 'react';
import {
  RiCheckLine,
  RiCloseLine,
  RiFlag2Line,
  RiDeleteBin6Line,
  RiEyeLine,
  RiPushpinLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface IContent {
  id: string;
  title: string;
  type: 'roadmap' | 'challenge' | 'comment' | 'article';
  author: string;
  status: 'published' | 'pending' | 'rejected' | 'flagged' | 'draft';
  createdAt: string;
  reports?: number;
  selected?: boolean;
}

function BulkContentOperations() {
  // Mock data for content
  // TODO: Replace with actual API data
  const [contentItems, setContentItems] = useState<IContent[]>([
    {
      id: 'content1',
      title: 'Frontend Development Roadmap',
      type: 'roadmap',
      author: 'John Doe',
      status: 'published',
      createdAt: '2025-05-20T14:30:00',
    },
    {
      id: 'content2',
      title: 'Advanced JavaScript Algorithms',
      type: 'challenge',
      author: 'Jane Smith',
      status: 'pending',
      createdAt: '2025-05-23T09:15:00',
    },
    {
      id: 'content3',
      title: 'This content is inappropriate',
      type: 'comment',
      author: 'Robert Johnson',
      status: 'flagged',
      createdAt: '2025-05-24T11:45:00',
      reports: 3,
    },
    {
      id: 'content4',
      title: 'How to Master React Hooks',
      type: 'article',
      author: 'Emily Davis',
      status: 'published',
      createdAt: '2025-05-22T16:20:00',
    },
    {
      id: 'content5',
      title: 'Python for Data Science',
      type: 'roadmap',
      author: 'Michael Wilson',
      status: 'draft',
      createdAt: '2025-05-21T08:30:00',
    },
    {
      id: 'content6',
      title: 'Web Security Fundamentals',
      type: 'challenge',
      author: 'Sarah Brown',
      status: 'pending',
      createdAt: '2025-05-24T10:00:00',
    },
    {
      id: 'content7',
      title: 'Great tutorial, thanks!',
      type: 'comment',
      author: 'David Miller',
      status: 'published',
      createdAt: '2025-05-23T13:45:00',
    },
    {
      id: 'content8',
      title: 'Offensive language in this post',
      type: 'comment',
      author: 'Lisa Taylor',
      status: 'flagged',
      createdAt: '2025-05-24T08:20:00',
      reports: 2,
    },
  ]);

  const [filters, setFilters] = useState({
    type: '',
    status: '',
    search: '',
  });

  const [selectedAction, setSelectedAction] = useState('');
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isFeedbackDialogOpen, setIsFeedbackDialogOpen] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [selectAll, setSelectAll] = useState(false);

  const handleFilterChange = (key: string, value: string) => {
    setFilters({
      ...filters,
      [key]: value,
    });
  };

  const filteredContent = contentItems.filter((item) => {
    let match = true;

    if (filters.type && item.type !== filters.type) {
      match = false;
    }

    if (filters.status && item.status !== filters.status) {
      match = false;
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const titleMatch = item.title.toLowerCase().includes(searchLower);
      const authorMatch = item.author.toLowerCase().includes(searchLower);

      if (!titleMatch && !authorMatch) {
        match = false;
      }
    }

    return match;
  });

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    setContentItems(
      contentItems.map((item) => ({
        ...item,
        selected: checked,
      })),
    );
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    setContentItems(
      contentItems.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            selected: checked,
          };
        }
        return item;
      }),
    );

    // Update selectAll state based on whether all visible items are selected
    const allSelected = contentItems.every((item) =>
      item.id === id ? checked : item.selected,
    );
    setSelectAll(allSelected);
  };

  const getSelectedItems = () => {
    return contentItems.filter((item) => item.selected);
  };

  const handleAction = (action: string) => {
    setSelectedAction(action);

    if (action === 'reject') {
      setIsFeedbackDialogOpen(true);
    } else {
      setIsConfirmDialogOpen(true);
    }
  };

  const executeAction = () => {
    // TODO: Replace with actual API calls
    switch (selectedAction) {
      case 'approve':
        setContentItems(
          contentItems.map((item) => {
            if (item.selected) {
              return {
                ...item,
                status: 'published',
                selected: false,
              };
            }
            return item;
          }),
        );
        break;
      case 'reject':
        // This is handled by the feedback dialog
        break;
      case 'flag':
        setContentItems(
          contentItems.map((item) => {
            if (item.selected) {
              return {
                ...item,
                status: 'flagged',
                selected: false,
              };
            }
            return item;
          }),
        );
        break;
      case 'delete':
        setContentItems(contentItems.filter((item) => !item.selected));
        break;
      case 'feature':
        // In a real application, we would set a featured flag
        setContentItems(
          contentItems.map((item) => {
            if (item.selected) {
              return {
                ...item,
                selected: false,
              };
            }
            return item;
          }),
        );
        alert(`${getSelectedItems().length} items have been featured`);
        break;
      default:
        break;
    }

    setSelectAll(false);
    setIsConfirmDialogOpen(false);
  };

  const handleRejectWithFeedback = () => {
    setContentItems(
      contentItems.map((item) => {
        if (item.selected) {
          return {
            ...item,
            status: 'rejected',
            selected: false,
          };
        }
        return item;
      }),
    );

    // TODO: Send feedback message to authors via API
    console.log(`Rejection feedback: ${feedbackMessage}`);

    setSelectAll(false);
    setFeedbackMessage('');
    setIsFeedbackDialogOpen(false);

    alert(
      `${getSelectedItems().length} items have been rejected with feedback`,
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'flagged':
        return 'bg-orange-100 text-orange-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'roadmap':
        return '🗺️';
      case 'challenge':
        return '🏆';
      case 'comment':
        return '💬';
      case 'article':
        return '📄';
      default:
        return '📄';
    }
  };

  return (
    <div className="space-y-6">
      {/* Filters and Actions */}
      <div className="flex flex-col justify-between gap-4 md:flex-row">
        <div className="flex flex-col gap-4 md:flex-row">
          <div className="w-full md:w-64">
            <Input
              placeholder="Search content..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>

          <Select
            value={filters.type}
            onValueChange={(value) => handleFilterChange('type', value)}
          >
            <SelectTrigger className="w-full md:w-40">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Types</SelectItem>
              <SelectItem value="roadmap">Roadmap</SelectItem>
              <SelectItem value="challenge">Challenge</SelectItem>
              <SelectItem value="comment">Comment</SelectItem>
              <SelectItem value="article">Article</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.status}
            onValueChange={(value) => handleFilterChange('status', value)}
          >
            <SelectTrigger className="w-full md:w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Statuses</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
              <SelectItem value="flagged">Flagged</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => handleAction('approve')}
            disabled={getSelectedItems().length === 0}
          >
            <RiCheckLine className="mr-2" /> Approve
          </Button>
          <Button
            variant="outline"
            onClick={() => handleAction('reject')}
            disabled={getSelectedItems().length === 0}
          >
            <RiCloseLine className="mr-2" /> Reject
          </Button>
          <Button
            variant="outline"
            onClick={() => handleAction('flag')}
            disabled={getSelectedItems().length === 0}
          >
            <RiFlag2Line className="mr-2" /> Flag
          </Button>
          <Button
            variant="outline"
            onClick={() => handleAction('delete')}
            disabled={getSelectedItems().length === 0}
          >
            <RiDeleteBin6Line className="mr-2" /> Delete
          </Button>
          <Button
            variant="outline"
            onClick={() => handleAction('feature')}
            disabled={getSelectedItems().length === 0}
          >
            <RiPushpinLine className="mr-2" /> Feature
          </Button>
        </div>
      </div>

      {/* Content Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectAll}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all content"
                />
              </TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Author</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredContent.length > 0 ? (
              filteredContent.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <Checkbox
                      checked={item.selected || false}
                      onCheckedChange={(checked) =>
                        handleSelectItem(item.id, checked as boolean)
                      }
                      aria-label={`Select ${item.title}`}
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <span>{getTypeIcon(item.type)}</span>
                      <span>{item.title}</span>
                      {item.reports && item.reports > 0 && (
                        <Badge variant="destructive">
                          {item.reports} reports
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{item.type}</TableCell>
                  <TableCell>{item.author}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(item.status)}>
                      {item.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(item.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <RiEyeLine /> View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="py-4 text-center">
                  No content found matching the filters
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Selected Items Summary */}
      <div className="rounded-md border bg-accent p-4">
        <p className="text-sm">{getSelectedItems().length} items selected</p>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Bulk Action</DialogTitle>
            <DialogDescription>
              Are you sure you want to perform this action on{' '}
              {getSelectedItems().length} content items?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="rounded-md border p-4">
              <h3 className="mb-2 text-sm font-medium">Selected Items:</h3>
              <ul className="max-h-40 space-y-1 overflow-y-auto text-sm">
                {getSelectedItems().map((item) => (
                  <li key={item.id}>
                    {item.title} ({item.type})
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={executeAction}>Confirm</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Feedback Dialog */}
      <Dialog
        open={isFeedbackDialogOpen}
        onOpenChange={setIsFeedbackDialogOpen}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Provide Rejection Feedback</DialogTitle>
            <DialogDescription>
              Add feedback for the authors of the {getSelectedItems().length}{' '}
              rejected items
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Feedback Message</label>
              <Textarea
                placeholder="Explain why this content is being rejected..."
                value={feedbackMessage}
                onChange={(e) => setFeedbackMessage(e.target.value)}
                rows={6}
              />
            </div>

            <div className="rounded-md border p-4">
              <h3 className="mb-2 text-sm font-medium">Items to Reject:</h3>
              <ul className="max-h-40 space-y-1 overflow-y-auto text-sm">
                {getSelectedItems().map((item) => (
                  <li key={item.id}>
                    {item.title} ({item.type}) by {item.author}
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsFeedbackDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleRejectWithFeedback}
              disabled={!feedbackMessage.trim()}
            >
              Reject with Feedback
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default BulkContentOperations;
