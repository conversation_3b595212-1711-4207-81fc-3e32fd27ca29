/**
 * @file BulkDataOperations.tsx
 * @description Component for performing bulk data operations
 */
'use client';

import { useState } from 'react';
import {
  RiUpload2Line,
  RiDownload2Line,
  RiRefreshLine,
  RiFileExcel2Line,
  RiFileZipLine,
  RiFileCodeLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';

interface IImportHistory {
  id: string;
  filename: string;
  type: string;
  records: number;
  status: 'completed' | 'failed' | 'processing';
  timestamp: string;
  errors?: number;
}

function BulkDataOperations() {
  const [activeTab, setActiveTab] = useState('import');
  const [importType, setImportType] = useState('users');
  const [exportType, setExportType] = useState('users');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [importOptions, setImportOptions] = useState({
    updateExisting: true,
    skipErrors: true,
    sendNotifications: false,
  });
  const [exportOptions, setExportOptions] = useState({
    format: 'csv',
    includeDeleted: false,
    dateRange: {
      from: '',
      to: '',
    },
  });

  // Mock data for import history
  const [importHistory, setImportHistory] = useState<IImportHistory[]>([
    {
      id: '1',
      filename: 'users_import_20250523.csv',
      type: 'users',
      records: 150,
      status: 'completed',
      timestamp: '2025-05-23T14:30:00',
    },
    {
      id: '2',
      filename: 'roadmaps_import_20250522.xlsx',
      type: 'roadmaps',
      records: 25,
      status: 'completed',
      timestamp: '2025-05-22T10:15:00',
    },
    {
      id: '3',
      filename: 'challenges_import_20250521.csv',
      type: 'challenges',
      records: 42,
      status: 'failed',
      timestamp: '2025-05-21T16:45:00',
      errors: 12,
    },
    {
      id: '4',
      filename: 'users_import_20250520.json',
      type: 'users',
      records: 75,
      status: 'completed',
      timestamp: '2025-05-20T09:30:00',
    },
  ]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleImportOptionChange = (key: string, value: boolean) => {
    setImportOptions({
      ...importOptions,
      [key]: value,
    });
  };

  const handleExportOptionChange = (key: string, value: any) => {
    setExportOptions({
      ...exportOptions,
      [key]: value,
    });
  };

  const handleDateRangeChange = (key: string, value: string) => {
    setExportOptions({
      ...exportOptions,
      dateRange: {
        ...exportOptions.dateRange,
        [key]: value,
      },
    });
  };

  const handleImport = () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsUploading(false);

          // Add to import history
          const newImport: IImportHistory = {
            id: `import-${Date.now()}`,
            filename: selectedFile.name,
            type: importType,
            records: Math.floor(Math.random() * 100) + 50, // Random number for demo
            status: 'completed',
            timestamp: new Date().toISOString(),
          };

          setImportHistory([newImport, ...importHistory]);
          setSelectedFile(null);

          // Reset file input
          const fileInput = document.getElementById(
            'file-upload',
          ) as HTMLInputElement;
          if (fileInput) fileInput.value = '';

          return 100;
        }
        return prev + 5;
      });
    }, 200);

    // TODO: Implement actual file upload and processing via API
  };

  const handleExport = () => {
    setIsExporting(true);
    setExportProgress(0);

    // Simulate export progress
    const interval = setInterval(() => {
      setExportProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsExporting(false);

          // Simulate file download
          const filename = `${exportType}_export_${new Date().toISOString().split('T')[0].replace(/-/g, '')}.${exportOptions.format}`;
          alert(`Export completed: ${filename}`);

          return 100;
        }
        return prev + 5;
      });
    }, 200);

    // TODO: Implement actual data export via API
  };

  const handlePreviewImport = () => {
    if (!selectedFile) return;

    // Simulate preview data
    // In a real application, you would parse the file and show actual data
    const mockPreviewData = [];

    if (importType === 'users') {
      mockPreviewData.push(
        {
          id: 'user1',
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'USER',
        },
        {
          id: 'user2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'MODERATOR',
        },
        {
          id: 'user3',
          name: 'Robert Johnson',
          email: '<EMAIL>',
          role: 'USER',
        },
      );
    } else if (importType === 'roadmaps') {
      mockPreviewData.push(
        {
          id: 'roadmap1',
          title: 'Frontend Development',
          author: 'John Doe',
          difficulty: 'Intermediate',
        },
        {
          id: 'roadmap2',
          title: 'Backend with Node.js',
          author: 'Jane Smith',
          difficulty: 'Advanced',
        },
      );
    } else if (importType === 'challenges') {
      mockPreviewData.push(
        {
          id: 'challenge1',
          title: 'JavaScript Algorithms',
          author: 'John Doe',
          difficulty: 'Hard',
        },
        {
          id: 'challenge2',
          title: 'CSS Layouts',
          author: 'Jane Smith',
          difficulty: 'Medium',
        },
      );
    }

    setPreviewData(mockPreviewData);
    setIsPreviewDialogOpen(true);

    // TODO: Implement actual file parsing and preview via API
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getFileIcon = (filename: string) => {
    if (filename.endsWith('.csv'))
      return <RiFileExcel2Line className="text-green-600" />;
    if (filename.endsWith('.xlsx') || filename.endsWith('.xls'))
      return <RiFileExcel2Line className="text-green-600" />;
    if (filename.endsWith('.json'))
      return <RiFileCodeLine className="text-blue-600" />;
    if (filename.endsWith('.zip'))
      return <RiFileZipLine className="text-orange-600" />;
    return <RiFileCodeLine className="text-gray-600" />;
  };

  return (
    <div className="space-y-6">
      <Tabs
        defaultValue="import"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="import">Import Data</TabsTrigger>
          <TabsTrigger value="export">Export Data</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-6">
          {/* Import Form */}
          <Card>
            <CardHeader>
              <CardTitle>Import Data</CardTitle>
              <CardDescription>
                Upload files to import data into the system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Data Type</label>
                  <Select value={importType} onValueChange={setImportType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select data type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="users">Users</SelectItem>
                      <SelectItem value="roadmaps">Roadmaps</SelectItem>
                      <SelectItem value="challenges">Challenges</SelectItem>
                      <SelectItem value="content">Content</SelectItem>
                      <SelectItem value="settings">Settings</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">File</label>
                  <Input
                    id="file-upload"
                    type="file"
                    onChange={handleFileChange}
                    accept=".csv,.xlsx,.xls,.json,.zip"
                  />
                  <p className="text-xs text-muted-foreground">
                    Accepted formats: CSV, Excel, JSON, ZIP
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Import Options</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="updateExisting"
                      checked={importOptions.updateExisting}
                      onCheckedChange={(checked) =>
                        handleImportOptionChange(
                          'updateExisting',
                          checked as boolean,
                        )
                      }
                    />
                    <label
                      htmlFor="updateExisting"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Update existing records
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="skipErrors"
                      checked={importOptions.skipErrors}
                      onCheckedChange={(checked) =>
                        handleImportOptionChange(
                          'skipErrors',
                          checked as boolean,
                        )
                      }
                    />
                    <label
                      htmlFor="skipErrors"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Skip records with errors
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="sendNotifications"
                      checked={importOptions.sendNotifications}
                      onCheckedChange={(checked) =>
                        handleImportOptionChange(
                          'sendNotifications',
                          checked as boolean,
                        )
                      }
                    />
                    <label
                      htmlFor="sendNotifications"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Send notifications to affected users
                    </label>
                  </div>
                </div>
              </div>

              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Uploading...</span>
                    <span className="text-sm">{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="h-2" />
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={handlePreviewImport}
                disabled={!selectedFile || isUploading}
              >
                Preview
              </Button>
              <Button
                onClick={handleImport}
                disabled={!selectedFile || isUploading}
              >
                <RiUpload2Line className="mr-2" /> Import
              </Button>
            </CardFooter>
          </Card>

          {/* Import History */}
          <Card>
            <CardHeader>
              <CardTitle>Import History</CardTitle>
              <CardDescription>Previous data imports</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {importHistory.map((item) => (
                  <div key={item.id} className="rounded-md border p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {getFileIcon(item.filename)}
                        <div>
                          <h3 className="font-medium">{item.filename}</h3>
                          <p className="text-sm text-muted-foreground">
                            {item.type} - {item.records} records
                          </p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(item.status)}>
                        {item.status}
                      </Badge>
                    </div>
                    <div className="mt-2 flex items-center justify-between text-sm">
                      <span>{new Date(item.timestamp).toLocaleString()}</span>
                      {item.errors && (
                        <span className="text-red-600">
                          {item.errors} errors
                        </span>
                      )}
                    </div>
                  </div>
                ))}

                {importHistory.length === 0 && (
                  <div className="py-4 text-center text-muted-foreground">
                    No import history found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="space-y-6">
          {/* Export Form */}
          <Card>
            <CardHeader>
              <CardTitle>Export Data</CardTitle>
              <CardDescription>Export data from the system</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Data Type</label>
                  <Select value={exportType} onValueChange={setExportType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select data type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="users">Users</SelectItem>
                      <SelectItem value="roadmaps">Roadmaps</SelectItem>
                      <SelectItem value="challenges">Challenges</SelectItem>
                      <SelectItem value="content">Content</SelectItem>
                      <SelectItem value="comments">Comments</SelectItem>
                      <SelectItem value="activity">Activity Logs</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Format</label>
                  <Select
                    value={exportOptions.format}
                    onValueChange={(value) =>
                      handleExportOptionChange('format', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                      <SelectItem value="json">JSON</SelectItem>
                      <SelectItem value="xml">XML</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Date Range</h3>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-1">
                    <label className="text-sm">From</label>
                    <Input
                      type="date"
                      value={exportOptions.dateRange.from}
                      onChange={(e) =>
                        handleDateRangeChange('from', e.target.value)
                      }
                    />
                  </div>
                  <div className="space-y-1">
                    <label className="text-sm">To</label>
                    <Input
                      type="date"
                      value={exportOptions.dateRange.to}
                      onChange={(e) =>
                        handleDateRangeChange('to', e.target.value)
                      }
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeDeleted"
                  checked={exportOptions.includeDeleted}
                  onCheckedChange={(checked) =>
                    handleExportOptionChange(
                      'includeDeleted',
                      checked as boolean,
                    )
                  }
                />
                <label
                  htmlFor="includeDeleted"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Include deleted records
                </label>
              </div>

              {isExporting && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Exporting...</span>
                    <span className="text-sm">{exportProgress}%</span>
                  </div>
                  <Progress value={exportProgress} className="h-2" />
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleExport}
                disabled={isExporting}
                className="ml-auto"
              >
                <RiDownload2Line className="mr-2" /> Export
              </Button>
            </CardFooter>
          </Card>

          {/* Export Templates */}
          <Card>
            <CardHeader>
              <CardTitle>Export Templates</CardTitle>
              <CardDescription>
                Download templates for data imports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-2">
                    <RiFileExcel2Line className="text-xl text-green-600" />
                    <div>
                      <h3 className="font-medium">Users Template</h3>
                      <p className="text-sm text-muted-foreground">
                        CSV format
                      </p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2 w-full">
                    <RiDownload2Line className="mr-2" /> Download
                  </Button>
                </div>

                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-2">
                    <RiFileExcel2Line className="text-xl text-green-600" />
                    <div>
                      <h3 className="font-medium">Roadmaps Template</h3>
                      <p className="text-sm text-muted-foreground">
                        Excel format
                      </p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2 w-full">
                    <RiDownload2Line className="mr-2" /> Download
                  </Button>
                </div>

                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-2">
                    <RiFileCodeLine className="text-xl text-blue-600" />
                    <div>
                      <h3 className="font-medium">Challenges Template</h3>
                      <p className="text-sm text-muted-foreground">
                        JSON format
                      </p>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2 w-full">
                    <RiDownload2Line className="mr-2" /> Download
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Preview Dialog */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Import Preview</DialogTitle>
            <DialogDescription>
              Preview of the first few records from {selectedFile?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {previewData.length > 0 ? (
              <div className="max-h-96 overflow-auto rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="bg-accent">
                      {Object.keys(previewData[0]).map((key) => (
                        <th
                          key={key}
                          className="px-4 py-2 text-left text-sm font-medium"
                        >
                          {key}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {previewData.map((row, index) => (
                      <tr key={index} className="border-t">
                        {Object.values(row).map((value, i) => (
                          <td key={i} className="px-4 py-2 text-sm">
                            {value as string}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="py-4 text-center text-muted-foreground">
                No preview data available
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsPreviewDialogOpen(false)}
            >
              Close
            </Button>
            <Button
              onClick={() => {
                setIsPreviewDialogOpen(false);
                handleImport();
              }}
              disabled={!selectedFile}
            >
              Proceed with Import
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default BulkDataOperations;
