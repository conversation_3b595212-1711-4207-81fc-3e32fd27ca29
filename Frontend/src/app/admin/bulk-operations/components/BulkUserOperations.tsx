/**
 * @file BulkUserOperations.tsx
 * @description Component for performing bulk operations on users
 */
'use client';

import { useState } from 'react';
import {
  RiUserLine,
  RiMailLine,
  RiUserSettingsLine,
  RiUserUnfollowLine,
  RiUserAddLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface IUser {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  lastActive: string;
  selected?: boolean;
}

function BulkUserOperations() {
  // Mock data for users
  // TODO: Replace with actual API data
  const [users, setUsers] = useState<IUser[]>([
    {
      id: 'user1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'USER',
      status: 'active',
      lastActive: '2025-05-23T14:30:00',
    },
    {
      id: 'user2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      role: 'MODERATOR',
      status: 'active',
      lastActive: '2025-05-24T09:15:00',
    },
    {
      id: 'user3',
      name: 'Robert Johnson',
      email: '<EMAIL>',
      role: 'USER',
      status: 'inactive',
      lastActive: '2025-05-10T11:45:00',
    },
    {
      id: 'user4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      role: 'USER',
      status: 'active',
      lastActive: '2025-05-22T16:20:00',
    },
    {
      id: 'user5',
      name: 'Michael Wilson',
      email: '<EMAIL>',
      role: 'USER',
      status: 'suspended',
      lastActive: '2025-05-15T08:30:00',
    },
    {
      id: 'user6',
      name: 'Sarah Brown',
      email: '<EMAIL>',
      role: 'USER',
      status: 'pending',
      lastActive: '2025-05-24T10:00:00',
    },
    {
      id: 'user7',
      name: 'David Miller',
      email: '<EMAIL>',
      role: 'USER',
      status: 'active',
      lastActive: '2025-05-23T13:45:00',
    },
    {
      id: 'user8',
      name: 'Lisa Taylor',
      email: '<EMAIL>',
      role: 'MODERATOR',
      status: 'active',
      lastActive: '2025-05-24T08:20:00',
    },
  ]);

  const [filters, setFilters] = useState({
    role: '',
    status: '',
    search: '',
  });

  const [selectedAction, setSelectedAction] = useState('');
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [emailSubject, setEmailSubject] = useState('');
  const [emailBody, setEmailBody] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [newRole, setNewRole] = useState('USER');

  const handleFilterChange = (key: string, value: string) => {
    setFilters({
      ...filters,
      [key]: value,
    });
  };

  const filteredUsers = users.filter((user) => {
    let match = true;

    if (filters.role && user.role !== filters.role) {
      match = false;
    }

    if (filters.status && user.status !== filters.status) {
      match = false;
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const nameMatch = user.name.toLowerCase().includes(searchLower);
      const emailMatch = user.email.toLowerCase().includes(searchLower);

      if (!nameMatch && !emailMatch) {
        match = false;
      }
    }

    return match;
  });

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    setUsers(
      users.map((user) => ({
        ...user,
        selected: checked,
      })),
    );
  };

  const handleSelectUser = (id: string, checked: boolean) => {
    setUsers(
      users.map((user) => {
        if (user.id === id) {
          return {
            ...user,
            selected: checked,
          };
        }
        return user;
      }),
    );

    // Update selectAll state based on whether all visible users are selected
    const allSelected = users.every((user) =>
      user.id === id ? checked : user.selected,
    );
    setSelectAll(allSelected);
  };

  const getSelectedUsers = () => {
    return users.filter((user) => user.selected);
  };

  const handleAction = (action: string) => {
    setSelectedAction(action);

    if (action === 'email') {
      setIsEmailDialogOpen(true);
    } else {
      setIsConfirmDialogOpen(true);
    }
  };

  const executeAction = () => {
    const selectedUsers = getSelectedUsers();

    // TODO: Replace with actual API calls
    switch (selectedAction) {
      case 'activate':
        setUsers(
          users.map((user) => {
            if (user.selected) {
              return {
                ...user,
                status: 'active',
                selected: false,
              };
            }
            return user;
          }),
        );
        break;
      case 'deactivate':
        setUsers(
          users.map((user) => {
            if (user.selected) {
              return {
                ...user,
                status: 'inactive',
                selected: false,
              };
            }
            return user;
          }),
        );
        break;
      case 'suspend':
        setUsers(
          users.map((user) => {
            if (user.selected) {
              return {
                ...user,
                status: 'suspended',
                selected: false,
              };
            }
            return user;
          }),
        );
        break;
      case 'changeRole':
        setUsers(
          users.map((user) => {
            if (user.selected) {
              return {
                ...user,
                role: newRole,
                selected: false,
              };
            }
            return user;
          }),
        );
        break;
      default:
        break;
    }

    setSelectAll(false);
    setIsConfirmDialogOpen(false);
  };

  const sendBulkEmail = () => {
    // TODO: Implement actual email sending via API
    console.log(`Sending email to ${getSelectedUsers().length} users:`);
    console.log(`Subject: ${emailSubject}`);
    console.log(`Body: ${emailBody}`);

    // Reset state
    setUsers(
      users.map((user) => ({
        ...user,
        selected: false,
      })),
    );
    setSelectAll(false);
    setEmailSubject('');
    setEmailBody('');
    setIsEmailDialogOpen(false);

    alert(`Email sent to ${getSelectedUsers().length} users`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Filters and Actions */}
      <div className="flex flex-col justify-between gap-4 md:flex-row">
        <div className="flex flex-col gap-4 md:flex-row">
          <div className="w-full md:w-64">
            <Input
              placeholder="Search users..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>

          <Select
            value={filters.role}
            onValueChange={(value) => handleFilterChange('role', value)}
          >
            <SelectTrigger className="w-full md:w-40">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Roles</SelectItem>
              <SelectItem value="USER">User</SelectItem>
              <SelectItem value="MODERATOR">Moderator</SelectItem>
              <SelectItem value="ADMIN">Admin</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.status}
            onValueChange={(value) => handleFilterChange('status', value)}
          >
            <SelectTrigger className="w-full md:w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Statuses</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="suspended">Suspended</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => handleAction('activate')}
            disabled={getSelectedUsers().length === 0}
          >
            <RiUserAddLine className="mr-2" /> Activate
          </Button>
          <Button
            variant="outline"
            onClick={() => handleAction('deactivate')}
            disabled={getSelectedUsers().length === 0}
          >
            <RiUserUnfollowLine className="mr-2" /> Deactivate
          </Button>
          <Button
            variant="outline"
            onClick={() => handleAction('suspend')}
            disabled={getSelectedUsers().length === 0}
          >
            <RiUserUnfollowLine className="mr-2" /> Suspend
          </Button>
          <Button
            variant="outline"
            onClick={() => handleAction('changeRole')}
            disabled={getSelectedUsers().length === 0}
          >
            <RiUserSettingsLine className="mr-2" /> Change Role
          </Button>
          <Button
            variant="outline"
            onClick={() => handleAction('email')}
            disabled={getSelectedUsers().length === 0}
          >
            <RiMailLine className="mr-2" /> Email
          </Button>
        </div>
      </div>

      {/* Users Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectAll}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all users"
                />
              </TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Last Active</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Checkbox
                      checked={user.selected || false}
                      onCheckedChange={(checked) =>
                        handleSelectUser(user.id, checked as boolean)
                      }
                      aria-label={`Select ${user.name}`}
                    />
                  </TableCell>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.role}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(user.status)}>
                      {user.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(user.lastActive).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="py-4 text-center">
                  No users found matching the filters
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Selected Users Summary */}
      <div className="rounded-md border bg-accent p-4">
        <p className="text-sm">{getSelectedUsers().length} users selected</p>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Bulk Action</DialogTitle>
            <DialogDescription>
              Are you sure you want to perform this action on{' '}
              {getSelectedUsers().length} users?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedAction === 'changeRole' && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Select New Role</label>
                <Select value={newRole} onValueChange={setNewRole}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USER">User</SelectItem>
                    <SelectItem value="MODERATOR">Moderator</SelectItem>
                    <SelectItem value="ADMIN">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="mt-4 rounded-md border p-4">
              <h3 className="mb-2 text-sm font-medium">Selected Users:</h3>
              <ul className="max-h-40 space-y-1 overflow-y-auto text-sm">
                {getSelectedUsers().map((user) => (
                  <li key={user.id}>
                    {user.name} ({user.email})
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={executeAction}>Confirm</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Email Dialog */}
      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Send Bulk Email</DialogTitle>
            <DialogDescription>
              Compose an email to send to {getSelectedUsers().length} users
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Subject</label>
              <Input
                placeholder="Email subject"
                value={emailSubject}
                onChange={(e) => setEmailSubject(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Message</label>
              <Textarea
                placeholder="Email body"
                value={emailBody}
                onChange={(e) => setEmailBody(e.target.value)}
                rows={8}
              />
            </div>

            <div className="rounded-md border p-4">
              <h3 className="mb-2 text-sm font-medium">Recipients:</h3>
              <ul className="max-h-40 space-y-1 overflow-y-auto text-sm">
                {getSelectedUsers().map((user) => (
                  <li key={user.id}>
                    {user.name} ({user.email})
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEmailDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={sendBulkEmail}
              disabled={!emailSubject.trim() || !emailBody.trim()}
            >
              Send Email
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default BulkUserOperations;
