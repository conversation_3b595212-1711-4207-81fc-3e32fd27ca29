/**
 * @file page.tsx
 * @description Bulk Operations for admin dashboard
 */
'use client';

import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import BulkUserOperations from './components/BulkUserOperations';
import BulkContentOperations from './components/BulkContentOperations';
import BulkDataOperations from './components/BulkDataOperations';

function BulkOperationsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Bulk Operations</h1>
      </div>

      <Tabs defaultValue="users" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Bulk User Operations</CardTitle>
              <CardDescription>
                Perform operations on multiple users at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkUserOperations />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Content Operations</CardTitle>
              <CardDescription>
                Moderate and manage multiple content items at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkContentOperations />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Data Operations</CardTitle>
              <CardDescription>
                Import, export, and update data in bulk
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkDataOperations />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default BulkOperationsPage;
