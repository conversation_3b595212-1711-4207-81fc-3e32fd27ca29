/**
 * @file page.tsx
 * @description Resources List Page for the admin dashboard
 */
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  RiAddLine,
  RiSearchLine,
  RiFilter3Line,
  RiMoreLine,
  RiDeleteBinLine,
  RiEditLine,
  RiEyeLine,
  RiStarLine,
  RiStarFill,
  RiFileTextLine,
  RiVideoLine,
  RiLinkM,
  RiBookOpenLine,
} from 'react-icons/ri';
import { DataTable } from '@/components/ui/DataTable';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface IResource {
  id: string;
  title: string;
  type: 'article' | 'video' | 'link' | 'document' | 'book';
  category: string;
  status: 'published' | 'draft' | 'archived';
  views: number;
  completions: number;
  rating: number;
  createdAt: string;
  updatedAt: string;
  isFeatured: boolean;
}

function ResourcesListPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');

  // TODO: Replace with actual API call to fetch resources
  const resources: IResource[] = [
    {
      id: '1',
      title: 'Introduction to Data Structures',
      type: 'article',
      category: 'Computer Science',
      status: 'published',
      views: 1250,
      completions: 875,
      rating: 4.7,
      createdAt: '2025-02-15T10:30:00Z',
      updatedAt: '2025-03-10T14:45:00Z',
      isFeatured: true,
    },
    {
      id: '2',
      title: 'Advanced JavaScript Patterns',
      type: 'video',
      category: 'Web Development',
      status: 'published',
      views: 980,
      completions: 620,
      rating: 4.5,
      createdAt: '2025-01-20T08:15:00Z',
      updatedAt: '2025-01-25T11:30:00Z',
      isFeatured: false,
    },
    {
      id: '3',
      title: 'System Design Interview Guide',
      type: 'document',
      category: 'Career',
      status: 'published',
      views: 3200,
      completions: 2100,
      rating: 4.9,
      createdAt: '2024-11-05T16:20:00Z',
      updatedAt: '2025-04-12T09:10:00Z',
      isFeatured: true,
    },
    {
      id: '4',
      title: 'Machine Learning Fundamentals',
      type: 'book',
      category: 'Artificial Intelligence',
      status: 'published',
      views: 1750,
      completions: 890,
      rating: 4.6,
      createdAt: '2025-03-01T13:45:00Z',
      updatedAt: '2025-03-15T10:20:00Z',
      isFeatured: false,
    },
    {
      id: '5',
      title: 'Blockchain Technology Overview',
      type: 'article',
      category: 'Blockchain',
      status: 'draft',
      views: 0,
      completions: 0,
      rating: 0,
      createdAt: '2025-05-10T09:30:00Z',
      updatedAt: '2025-05-10T09:30:00Z',
      isFeatured: false,
    },
    {
      id: '6',
      title: 'Cloud Computing Best Practices',
      type: 'link',
      category: 'Cloud Computing',
      status: 'published',
      views: 850,
      completions: 420,
      rating: 4.3,
      createdAt: '2025-02-28T11:15:00Z',
      updatedAt: '2025-03-05T16:40:00Z',
      isFeatured: false,
    },
    {
      id: '7',
      title: 'Mobile App Development with React Native',
      type: 'video',
      category: 'Mobile Development',
      status: 'published',
      views: 1650,
      completions: 980,
      rating: 4.8,
      createdAt: '2025-01-10T14:20:00Z',
      updatedAt: '2025-02-20T09:15:00Z',
      isFeatured: true,
    },
    {
      id: '8',
      title: 'DevOps Pipeline Automation',
      type: 'document',
      category: 'DevOps',
      status: 'archived',
      views: 750,
      completions: 320,
      rating: 4.1,
      createdAt: '2024-10-15T08:30:00Z',
      updatedAt: '2025-01-05T13:45:00Z',
      isFeatured: false,
    },
  ];

  // Filter resources based on search query and filters
  const filteredResources = resources.filter((resource) => {
    const matchesSearch = resource.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesType = selectedType ? resource.type === selectedType : true;
    const matchesCategory = selectedCategory
      ? resource.category === selectedCategory
      : true;
    const matchesStatus = selectedStatus
      ? resource.status === selectedStatus
      : true;

    return matchesSearch && matchesType && matchesCategory && matchesStatus;
  });

  // Get unique categories for filter
  const categories = [
    ...new Set(resources.map((resource) => resource.category)),
  ];

  // Handle featuring/unfeaturing a resource
  const handleToggleFeature = (resourceId: string) => {
    // TODO: Implement API call to toggle feature status
    console.log(`Toggle feature for resource ${resourceId}`);
  };

  // Handle deleting a resource
  const handleDelete = (resourceId: string) => {
    // TODO: Implement API call to delete resource
    console.log(`Delete resource ${resourceId}`);
  };

  // Resource type icon mapping
  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case 'article':
        return <RiFileTextLine className="h-4 w-4" />;
      case 'video':
        return <RiVideoLine className="h-4 w-4" />;
      case 'link':
        return <RiLinkM className="h-4 w-4" />;
      case 'document':
        return <RiFileTextLine className="h-4 w-4" />;
      case 'book':
        return <RiBookOpenLine className="h-4 w-4" />;
      default:
        return <RiFileTextLine className="h-4 w-4" />;
    }
  };

  // Resource status badge mapping
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="success">Published</Badge>;
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'archived':
        return <Badge variant="secondary">Archived</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // DataTable columns configuration
  const columns = [
    {
      accessorKey: 'title',
      header: 'Title',
      cell: ({ row }) => {
        const resource = row.original;
        return (
          <div className="flex items-start gap-2">
            <div className="mt-0.5 text-muted-foreground">
              {getResourceTypeIcon(resource.type)}
            </div>
            <div>
              <div className="font-medium">{resource.title}</div>
              <div className="text-xs text-muted-foreground">
                {resource.category}
              </div>
            </div>
            {resource.isFeatured && (
              <RiStarFill className="ml-1 h-4 w-4 text-amber-500" />
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'type',
      header: 'Type',
      cell: ({ row }) => {
        const type = row.getValue('type') as string;
        return <div className="capitalize">{type}</div>;
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return getStatusBadge(status);
      },
    },
    {
      accessorKey: 'views',
      header: 'Views',
      cell: ({ row }) => {
        const views = row.getValue('views') as number;
        return <div className="text-right">{views.toLocaleString()}</div>;
      },
    },
    {
      accessorKey: 'completions',
      header: 'Completions',
      cell: ({ row }) => {
        const completions = row.getValue('completions') as number;
        return <div className="text-right">{completions.toLocaleString()}</div>;
      },
    },
    {
      accessorKey: 'rating',
      header: 'Rating',
      cell: ({ row }) => {
        const rating = row.getValue('rating') as number;
        return (
          <div className="text-right">
            {rating > 0 ? (
              <span className="flex items-center justify-end gap-1">
                {rating.toFixed(1)}
                <RiStarFill className="h-3.5 w-3.5 text-amber-500" />
              </span>
            ) : (
              <span className="text-muted-foreground">-</span>
            )}
          </div>
        );
      },
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const resource = row.original;
        return (
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <RiMoreLine className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => router.push(`/admin/resources/${resource.id}`)}
                >
                  <RiEyeLine className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    router.push(`/admin/resources/${resource.id}/edit`)
                  }
                >
                  <RiEditLine className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleToggleFeature(resource.id)}
                >
                  {resource.isFeatured ? (
                    <>
                      <RiStarLine className="mr-2 h-4 w-4" />
                      Unfeature
                    </>
                  ) : (
                    <>
                      <RiStarFill className="mr-2 h-4 w-4" />
                      Feature
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleDelete(resource.id)}
                  className="text-destructive focus:text-destructive"
                >
                  <RiDeleteBinLine className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Resources</h2>
          <p className="text-muted-foreground">
            Manage learning resources for users.
          </p>
        </div>
        <Button
          onClick={() => router.push('/admin/resources/create')}
          className="w-full md:w-auto"
        >
          <RiAddLine className="mr-2 h-4 w-4" />
          Add Resource
        </Button>
      </div>

      {/* Resource Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Resources
            </CardTitle>
            <RiFileTextLine className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{resources.length}</div>
            <p className="text-xs text-muted-foreground">
              {resources.filter((r) => r.status === 'published').length}{' '}
              published
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <RiEyeLine className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resources
                .reduce((sum, resource) => sum + resource.views, 0)
                .toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all resources
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Completion Rate
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resources.reduce((sum, resource) => sum + resource.views, 0) > 0
                ? Math.round(
                    (resources.reduce(
                      (sum, resource) => sum + resource.completions,
                      0,
                    ) /
                      resources.reduce(
                        (sum, resource) => sum + resource.views,
                        0,
                      )) *
                      100,
                  )
                : 0}
              %
            </div>
            <p className="text-xs text-muted-foreground">
              Average completion percentage
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Average Rating
            </CardTitle>
            <RiStarFill className="h-4 w-4 text-amber-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resources.filter((r) => r.rating > 0).length > 0
                ? (
                    resources.reduce(
                      (sum, resource) => sum + resource.rating,
                      0,
                    ) / resources.filter((r) => r.rating > 0).length
                  ).toFixed(1)
                : '-'}
            </div>
            <p className="text-xs text-muted-foreground">
              Based on user feedback
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search resources..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <div className="flex flex-col gap-4 sm:flex-row">
          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger className="w-full sm:w-[150px]">
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All types</SelectItem>
              <SelectItem value="article">Article</SelectItem>
              <SelectItem value="video">Video</SelectItem>
              <SelectItem value="link">Link</SelectItem>
              <SelectItem value="document">Document</SelectItem>
              <SelectItem value="book">Book</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="All categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger className="w-full sm:w-[150px]">
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All statuses</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Resources Table */}
      <DataTable
        columns={columns}
        data={filteredResources}
        filterableColumns={[
          {
            id: 'status',
            title: 'Status',
            options: [
              { label: 'Published', value: 'published' },
              { label: 'Draft', value: 'draft' },
              { label: 'Archived', value: 'archived' },
            ],
          },
          {
            id: 'type',
            title: 'Type',
            options: [
              { label: 'Article', value: 'article' },
              { label: 'Video', value: 'video' },
              { label: 'Link', value: 'link' },
              { label: 'Document', value: 'document' },
              { label: 'Book', value: 'book' },
            ],
          },
        ]}
        searchableColumns={[
          {
            id: 'title',
            title: 'Title',
          },
          {
            id: 'category',
            title: 'Category',
          },
        ]}
      />
    </div>
  );
}

export default ResourcesListPage;
