/**
 * @file ResourceFeedback.tsx
 * @description Component to display user feedback for a resource
 */
'use client';

import { useState } from 'react';
import {
  RiStarFill,
  RiStarLine,
  RiThumbUpLine,
  RiThumbDownLine,
  RiChat1Line,
  RiUserLine,
  RiTimeLine,
  RiMailLine,
  RiArrowDownSLine,
  RiArrowUpSLine,
} from 'react-icons/ri';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';

interface IResourceFeedbackProps {
  resourceId: string;
}

function ResourceFeedback({ resourceId }: IResourceFeedbackProps) {
  const [sortBy, setSortBy] = useState('recent');
  const [filterRating, setFilterRating] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFeedbacks, setExpandedFeedbacks] = useState<string[]>([]);

  // TODO: Replace with actual API call to fetch feedback data
  const feedbackData = {
    summary: {
      averageRating: 4.7,
      totalReviews: 42,
      ratingDistribution: [
        { rating: 5, count: 30, percentage: 71 },
        { rating: 4, count: 8, percentage: 19 },
        { rating: 3, count: 2, percentage: 5 },
        { rating: 2, count: 1, percentage: 2.5 },
        { rating: 1, count: 1, percentage: 2.5 },
      ],
    },
    feedback: [
      {
        id: '1',
        userId: 'user1',
        userName: 'John Smith',
        userEmail: '<EMAIL>',
        userAvatar: '',
        rating: 5,
        comment:
          'This resource was incredibly helpful for understanding data structures. The explanations were clear and the examples were practical. I especially liked the section on trees and graphs which helped me visualize complex concepts.',
        helpful: 12,
        notHelpful: 1,
        createdAt: '2025-04-10T15:30:00Z',
      },
      {
        id: '2',
        userId: 'user2',
        userName: 'Emily Johnson',
        userEmail: '<EMAIL>',
        userAvatar: '',
        rating: 4,
        comment:
          'Great content overall. I would have liked more practice exercises, but the explanations were very clear. The cheatsheet attachment was a nice bonus that I&apos;ve been referring to frequently.',
        helpful: 8,
        notHelpful: 0,
        createdAt: '2025-04-05T09:15:00Z',
      },
      {
        id: '3',
        userId: 'user3',
        userName: 'Michael Chen',
        userEmail: '<EMAIL>',
        userAvatar: '',
        rating: 5,
        comment:
          'Excellent resource! The complexity analysis section was particularly helpful for my interview preparation. Would highly recommend to anyone studying computer science fundamentals.',
        helpful: 15,
        notHelpful: 2,
        createdAt: '2025-03-28T14:20:00Z',
      },
      {
        id: '4',
        userId: 'user4',
        userName: 'Sarah Williams',
        userEmail: '<EMAIL>',
        userAvatar: '',
        rating: 3,
        comment:
          'The content was good but I found some of the explanations a bit too theoretical. More code examples would have been helpful.',
        helpful: 5,
        notHelpful: 3,
        createdAt: '2025-03-20T11:45:00Z',
      },
      {
        id: '5',
        userId: 'user5',
        userName: 'David Rodriguez',
        userEmail: '<EMAIL>',
        userAvatar: '',
        rating: 5,
        comment:
          'This is exactly what I was looking for to brush up on my data structures knowledge. The content is well-organized and easy to follow.',
        helpful: 9,
        notHelpful: 0,
        createdAt: '2025-03-15T16:30:00Z',
      },
      {
        id: '6',
        userId: 'user6',
        userName: 'Lisa Thompson',
        userEmail: '<EMAIL>',
        userAvatar: '',
        rating: 2,
        comment:
          'I found this too advanced for beginners. The resource assumes prior knowledge that wasn&apos;t mentioned in the prerequisites.',
        helpful: 3,
        notHelpful: 7,
        createdAt: '2025-03-10T08:20:00Z',
      },
    ],
  };

  // Filter and sort feedback
  const filteredFeedback = feedbackData.feedback
    .filter((feedback) => {
      const matchesRating = filterRating
        ? feedback.rating === parseInt(filterRating)
        : true;
      const matchesSearch = searchQuery
        ? feedback.comment.toLowerCase().includes(searchQuery.toLowerCase()) ||
          feedback.userName.toLowerCase().includes(searchQuery.toLowerCase())
        : true;

      return matchesRating && matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case 'oldest':
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        case 'highest':
          return b.rating - a.rating;
        case 'lowest':
          return a.rating - b.rating;
        case 'helpful':
          return b.helpful - a.helpful;
        default:
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
      }
    });

  // Toggle feedback expansion
  const toggleFeedbackExpansion = (feedbackId: string) => {
    setExpandedFeedbacks((prev) =>
      prev.includes(feedbackId)
        ? prev.filter((id) => id !== feedbackId)
        : [...prev, feedbackId],
    );
  };

  // Render star rating
  const renderStarRating = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <span key={star}>
            {star <= rating ? (
              <RiStarFill className="h-4 w-4 text-amber-500" />
            ) : (
              <RiStarLine className="h-4 w-4 text-muted-foreground" />
            )}
          </span>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Feedback Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Feedback Summary</CardTitle>
          <CardDescription>
            Overview of user ratings and reviews
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-6 md:flex-row">
            {/* Average Rating */}
            <div className="flex flex-col items-center justify-center md:w-1/3">
              <div className="text-5xl font-bold">
                {feedbackData.summary.averageRating.toFixed(1)}
              </div>
              <div className="mt-2">
                {renderStarRating(feedbackData.summary.averageRating)}
              </div>
              <div className="mt-1 text-sm text-muted-foreground">
                Based on {feedbackData.summary.totalReviews} reviews
              </div>
            </div>

            {/* Rating Distribution */}
            <div className="flex-1">
              <h4 className="mb-2 text-sm font-medium">Rating Distribution</h4>
              <div className="space-y-2">
                {feedbackData.summary.ratingDistribution.map((item) => (
                  <div key={item.rating} className="flex items-center gap-2">
                    <div className="w-10 text-sm">{item.rating} stars</div>
                    <div className="flex-1">
                      <div className="h-2 w-full overflow-hidden rounded-full bg-muted">
                        <div
                          className="h-full rounded-full bg-amber-500"
                          style={{ width: `${item.percentage}%` }}
                        />
                      </div>
                    </div>
                    <div className="w-10 text-right text-sm">
                      {item.percentage}%
                    </div>
                    <div className="w-10 text-right text-sm text-muted-foreground">
                      {item.count}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Filters */}
      <div className="flex flex-col gap-4 sm:flex-row">
        <div className="relative flex-1">
          <RiChat1Line className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search in feedback..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Select value={filterRating} onValueChange={setFilterRating}>
          <SelectTrigger className="w-full sm:w-[150px]">
            <SelectValue placeholder="All ratings" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All ratings</SelectItem>
            <SelectItem value="5">5 stars</SelectItem>
            <SelectItem value="4">4 stars</SelectItem>
            <SelectItem value="3">3 stars</SelectItem>
            <SelectItem value="2">2 stars</SelectItem>
            <SelectItem value="1">1 star</SelectItem>
          </SelectContent>
        </Select>
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="recent">Most recent</SelectItem>
            <SelectItem value="oldest">Oldest first</SelectItem>
            <SelectItem value="highest">Highest rated</SelectItem>
            <SelectItem value="lowest">Lowest rated</SelectItem>
            <SelectItem value="helpful">Most helpful</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Feedback List */}
      <div className="space-y-4">
        {filteredFeedback.length > 0 ? (
          filteredFeedback.map((feedback) => {
            const isExpanded = expandedFeedbacks.includes(feedback.id);
            const shouldTruncate = feedback.comment.length > 200;
            const displayComment =
              shouldTruncate && !isExpanded
                ? `${feedback.comment.substring(0, 200)}...`
                : feedback.comment;

            return (
              <Card key={feedback.id}>
                <CardContent className="pt-6">
                  <div className="flex items-start gap-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage
                        src={feedback.userAvatar}
                        alt={feedback.userName}
                      />
                      <AvatarFallback>
                        {feedback.userName.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                        <div>
                          <div className="font-medium">{feedback.userName}</div>
                          <div className="mt-1 flex items-center gap-2">
                            {renderStarRating(feedback.rating)}
                            <span className="text-xs text-muted-foreground">
                              {new Date(
                                feedback.createdAt,
                              ).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="flex items-center gap-1"
                          >
                            <RiMailLine className="h-4 w-4" />
                            Contact
                          </Button>
                        </div>
                      </div>

                      <div className="mt-3">
                        <p className="text-sm">{displayComment}</p>
                        {shouldTruncate && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="mt-1 h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
                            onClick={() => toggleFeedbackExpansion(feedback.id)}
                          >
                            {isExpanded ? (
                              <span className="flex items-center gap-1">
                                Show less <RiArrowUpSLine className="h-3 w-3" />
                              </span>
                            ) : (
                              <span className="flex items-center gap-1">
                                Read more{' '}
                                <RiArrowDownSLine className="h-3 w-3" />
                              </span>
                            )}
                          </Button>
                        )}
                      </div>

                      <div className="mt-3 flex items-center gap-4">
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <RiThumbUpLine className="h-3.5 w-3.5" />
                          <span>{feedback.helpful} found helpful</span>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <RiThumbDownLine className="h-3.5 w-3.5" />
                          <span>{feedback.notHelpful} found not helpful</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        ) : (
          <Card>
            <CardContent className="py-6 text-center text-muted-foreground">
              No feedback found matching your filters.
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

export default ResourceFeedback;
