/**
 * @file ResourceAnalytics.tsx
 * @description Component to display analytics data for a resource
 */
'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface IResourceAnalyticsProps {
  resourceId: string;
}

function ResourceAnalytics({ resourceId }: IResourceAnalyticsProps) {
  const [timeRange, setTimeRange] = useState('30days');

  // TODO: Replace with actual API call to fetch analytics data
  const analyticsData = {
    views: {
      total: 1250,
      trend: '+12%',
      chartData: [
        { date: '2025-04-01', value: 32 },
        { date: '2025-04-02', value: 40 },
        { date: '2025-04-03', value: 45 },
        { date: '2025-04-04', value: 30 },
        { date: '2025-04-05', value: 25 },
        { date: '2025-04-06', value: 20 },
        { date: '2025-04-07', value: 35 },
        { date: '2025-04-08', value: 42 },
        { date: '2025-04-09', value: 48 },
        { date: '2025-04-10', value: 50 },
        { date: '2025-04-11', value: 55 },
        { date: '2025-04-12', value: 60 },
        { date: '2025-04-13', value: 52 },
        { date: '2025-04-14', value: 48 },
      ],
    },
    completions: {
      total: 875,
      trend: '+8%',
      chartData: [
        { date: '2025-04-01', value: 20 },
        { date: '2025-04-02', value: 25 },
        { date: '2025-04-03', value: 30 },
        { date: '2025-04-04', value: 22 },
        { date: '2025-04-05', value: 18 },
        { date: '2025-04-06', value: 15 },
        { date: '2025-04-07', value: 25 },
        { date: '2025-04-08', value: 30 },
        { date: '2025-04-09', value: 35 },
        { date: '2025-04-10', value: 38 },
        { date: '2025-04-11', value: 40 },
        { date: '2025-04-12', value: 45 },
        { date: '2025-04-13', value: 38 },
        { date: '2025-04-14', value: 35 },
      ],
    },
    avgTimeSpent: '12 minutes',
    completionRate: '70%',
    demographics: {
      byExperience: [
        { name: 'Beginner', value: 35 },
        { name: 'Intermediate', value: 45 },
        { name: 'Advanced', value: 20 },
      ],
      byRole: [
        { name: 'Student', value: 40 },
        { name: 'Developer', value: 35 },
        { name: 'Teacher', value: 15 },
        { name: 'Other', value: 10 },
      ],
    },
    referrers: [
      { source: 'Direct', count: 450, percentage: 36 },
      { source: 'Search', count: 325, percentage: 26 },
      { source: 'Roadmaps', count: 275, percentage: 22 },
      { source: 'Challenges', count: 125, percentage: 10 },
      { source: 'Other', count: 75, percentage: 6 },
    ],
  };

  // Render chart (simplified for this example)
  const renderChart = (
    data: Array<{ date: string; value: number }>,
    label: string,
  ) => {
    const maxValue = Math.max(...data.map((item) => item.value));

    return (
      <div className="h-[200px] w-full">
        <div className="flex h-full items-end gap-2">
          {data.map((item, index) => {
            const height = (item.value / maxValue) * 100;
            return (
              <div
                key={index}
                className="bg-primary/20 relative flex-1 rounded-t"
                style={{ height: `${height}%` }}
              >
                <div
                  className="absolute bottom-0 w-full rounded-t bg-primary transition-all duration-300"
                  style={{ height: `${height}%` }}
                  title={`${new Date(item.date).toLocaleDateString()}: ${item.value} ${label}`}
                />
              </div>
            );
          })}
        </div>
        <div className="mt-2 flex justify-between text-xs text-muted-foreground">
          <span>{new Date(data[0].date).toLocaleDateString()}</span>
          <span>
            {new Date(data[data.length - 1].date).toLocaleDateString()}
          </span>
        </div>
      </div>
    );
  };

  // Render pie chart (simplified for this example)
  const renderPieChart = (data: Array<{ name: string; value: number }>) => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;

    return (
      <div className="relative mx-auto h-[200px] w-[200px]">
        <svg viewBox="0 0 100 100" className="h-full w-full">
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const angle = (percentage / 100) * 360;
            const startAngle = currentAngle;
            const endAngle = currentAngle + angle;
            currentAngle = endAngle;

            const startRadians = (startAngle - 90) * (Math.PI / 180);
            const endRadians = (endAngle - 90) * (Math.PI / 180);

            const x1 = 50 + 50 * Math.cos(startRadians);
            const y1 = 50 + 50 * Math.sin(startRadians);
            const x2 = 50 + 50 * Math.cos(endRadians);
            const y2 = 50 + 50 * Math.sin(endRadians);

            const largeArcFlag = angle > 180 ? 1 : 0;

            const colors = [
              'bg-primary',
              'bg-blue-500',
              'bg-green-500',
              'bg-yellow-500',
              'bg-purple-500',
            ];
            const pathColors = [
              'var(--primary)',
              '#3b82f6',
              '#22c55e',
              '#eab308',
              '#a855f7',
            ];

            return (
              <path
                key={index}
                d={`M 50 50 L ${x1} ${y1} A 50 50 0 ${largeArcFlag} 1 ${x2} ${y2} Z`}
                fill={pathColors[index % pathColors.length]}
                stroke="white"
                strokeWidth="0.5"
              />
            );
          })}
        </svg>
        <div className="mt-4 grid grid-cols-2 gap-2">
          {data.map((item, index) => {
            const colors = [
              'bg-primary',
              'bg-blue-500',
              'bg-green-500',
              'bg-yellow-500',
              'bg-purple-500',
            ];
            return (
              <div key={index} className="flex items-center gap-1 text-xs">
                <div
                  className={`h-3 w-3 rounded-full ${colors[index % colors.length]}`}
                />
                <span>
                  {item.name} ({item.value}%)
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-end">
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="90days">Last 90 days</SelectItem>
            <SelectItem value="year">Last year</SelectItem>
            <SelectItem value="all">All time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Engagement Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData.views.total.toLocaleString()}
            </div>
            <p className="text-xs text-green-500">
              {analyticsData.views.trend} from previous period
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completions</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
              <path d="M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData.completions.total.toLocaleString()}
            </div>
            <p className="text-xs text-green-500">
              {analyticsData.completions.trend} from previous period
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Completion Rate
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData.completionRate}
            </div>
            <p className="text-xs text-muted-foreground">
              Of users who started the resource
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Time Spent
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <circle cx="12" cy="12" r="10" />
              <polyline points="12 6 12 12 16 14" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analyticsData.avgTimeSpent}
            </div>
            <p className="text-xs text-muted-foreground">Per user session</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Views Over Time</CardTitle>
            <CardDescription>
              Daily view count for the selected period
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderChart(analyticsData.views.chartData, 'views')}
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Completions Over Time</CardTitle>
            <CardDescription>
              Daily completion count for the selected period
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderChart(analyticsData.completions.chartData, 'completions')}
          </CardContent>
        </Card>
      </div>

      {/* Demographics */}
      <Card>
        <CardHeader>
          <CardTitle>User Demographics</CardTitle>
          <CardDescription>
            Breakdown of users who accessed this resource
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="experience" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="experience">By Experience Level</TabsTrigger>
              <TabsTrigger value="role">By Role</TabsTrigger>
            </TabsList>
            <TabsContent value="experience" className="mt-6">
              <div className="flex flex-col items-center justify-center gap-6 md:flex-row">
                {renderPieChart(analyticsData.demographics.byExperience)}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Key Insights</h4>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>
                      Intermediate users form the largest audience segment
                    </li>
                    <li>
                      Consider creating more advanced content to engage advanced
                      users
                    </li>
                    <li>Beginner-friendly resources have good engagement</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="role" className="mt-6">
              <div className="flex flex-col items-center justify-center gap-6 md:flex-row">
                {renderPieChart(analyticsData.demographics.byRole)}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Key Insights</h4>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>Students are the primary audience for this resource</li>
                    <li>Professional developers show significant interest</li>
                    <li>Consider creating specialized content for educators</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Referrers */}
      <Card>
        <CardHeader>
          <CardTitle>Traffic Sources</CardTitle>
          <CardDescription>
            Where users are finding this resource
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analyticsData.referrers.map((referrer, index) => (
              <div key={index} className="flex items-center">
                <div className="w-[100px] text-sm">{referrer.source}</div>
                <div className="flex-1">
                  <div className="h-2 w-full overflow-hidden rounded-full bg-muted">
                    <div
                      className="h-full rounded-full bg-primary"
                      style={{ width: `${referrer.percentage}%` }}
                    />
                  </div>
                </div>
                <div className="w-[60px] text-right text-sm">
                  {referrer.percentage}%
                </div>
                <div className="w-[80px] text-right text-sm text-muted-foreground">
                  {referrer.count.toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ResourceAnalytics;
