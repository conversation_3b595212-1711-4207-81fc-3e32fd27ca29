/**
 * @file ResourceContent.tsx
 * @description Component to display the content of a resource
 */
'use client';

import { useState } from 'react';
import {
  RiFileTextLine,
  RiVideoLine,
  RiLinkM,
  RiBookOpenLine,
  RiExternalLinkLine,
  RiDownloadLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface IResourceContentProps {
  resourceId: string;
  resourceType: string;
  resourceContent: string;
}

function ResourceContent({
  resourceId,
  resourceType,
  resourceContent,
}: IResourceContentProps) {
  // TODO: Replace with actual API call to fetch resource content
  const content = {
    title: 'Introduction to Data Structures',
    description:
      'A comprehensive guide to understanding data structures and their implementations.',
    sections: [
      {
        title: 'Arrays and Linked Lists',
        content:
          'Arrays are contiguous blocks of memory that store elements of the same type. They provide constant-time access to elements using indices but have fixed sizes in many programming languages. Linked lists, on the other hand, consist of nodes where each node contains data and a reference to the next node. They allow for dynamic size adjustment but require linear time for element access.',
      },
      {
        title: 'Stacks and Queues',
        content:
          'Stacks follow the Last-In-First-Out (LIFO) principle, where the last element added is the first one to be removed. Common operations include push (add) and pop (remove). Queues follow the First-In-First-Out (FIFO) principle, where the first element added is the first one to be removed. Common operations include enqueue (add) and dequeue (remove).',
      },
      {
        title: 'Trees and Graphs',
        content:
          'Trees are hierarchical data structures with a root node and child nodes. Binary trees have at most two children per node. Binary search trees maintain the property that the left subtree contains values less than the node, and the right subtree contains values greater than the node. Graphs consist of vertices (nodes) and edges (connections between nodes). They can be directed or undirected, weighted or unweighted.',
      },
    ],
    attachments: [
      {
        name: 'data_structures_cheatsheet.pdf',
        size: '2.4 MB',
        type: 'pdf',
        url: 'https://example.com/data_structures_cheatsheet.pdf',
      },
      {
        name: 'complexity_analysis.xlsx',
        size: '1.1 MB',
        type: 'xlsx',
        url: 'https://example.com/complexity_analysis.xlsx',
      },
    ],
  };

  // Get resource type icon
  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case 'article':
        return <RiFileTextLine className="h-5 w-5" />;
      case 'video':
        return <RiVideoLine className="h-5 w-5" />;
      case 'link':
        return <RiLinkM className="h-5 w-5" />;
      case 'document':
        return <RiFileTextLine className="h-5 w-5" />;
      case 'book':
        return <RiBookOpenLine className="h-5 w-5" />;
      default:
        return <RiFileTextLine className="h-5 w-5" />;
    }
  };

  // Render content based on resource type
  const renderContent = () => {
    switch (resourceType) {
      case 'video':
        return (
          <div className="flex aspect-video items-center justify-center rounded-md bg-muted">
            <div className="text-center">
              <RiVideoLine className="mx-auto h-12 w-12 text-muted-foreground" />
              <p className="mt-2 text-muted-foreground">
                Video preview not available in admin view
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mx-auto mt-4 flex items-center gap-1"
                asChild
              >
                <a
                  href={resourceContent}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <RiExternalLinkLine className="h-4 w-4" />
                  View Video
                </a>
              </Button>
            </div>
          </div>
        );
      case 'link':
        return (
          <div className="flex items-center justify-center rounded-md bg-muted p-6">
            <div className="text-center">
              <RiLinkM className="mx-auto h-12 w-12 text-muted-foreground" />
              <p className="mt-2 text-muted-foreground">
                External link resource
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mx-auto mt-4 flex items-center gap-1"
                asChild
              >
                <a
                  href={resourceContent}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <RiExternalLinkLine className="h-4 w-4" />
                  Visit Link
                </a>
              </Button>
            </div>
          </div>
        );
      case 'article':
      case 'document':
      case 'book':
      default:
        return (
          <div className="space-y-6">
            {content.sections.map((section, index) => (
              <div key={index} className="space-y-2">
                <h3 className="text-lg font-medium">{section.title}</h3>
                <p className="text-muted-foreground">{section.content}</p>
              </div>
            ))}
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            {getResourceTypeIcon(resourceType)}
            <CardTitle>Resource Content</CardTitle>
          </div>
          {resourceContent && (
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              asChild
            >
              <a
                href={resourceContent}
                target="_blank"
                rel="noopener noreferrer"
              >
                <RiExternalLinkLine className="h-4 w-4" />
                View Original
              </a>
            </Button>
          )}
        </CardHeader>
        <CardContent>{renderContent()}</CardContent>
      </Card>

      {/* Attachments */}
      {content.attachments && content.attachments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Attachments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {content.attachments.map((attachment, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-md border p-3 hover:bg-muted/50"
                >
                  <div className="flex items-center gap-3">
                    <RiFileTextLine className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{attachment.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {attachment.size}
                      </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="icon" asChild>
                    <a
                      href={attachment.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      download
                    >
                      <RiDownloadLine className="h-4 w-4" />
                      <span className="sr-only">Download</span>
                    </a>
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default ResourceContent;
