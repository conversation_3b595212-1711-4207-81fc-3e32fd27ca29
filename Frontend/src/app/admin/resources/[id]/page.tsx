/**
 * @file page.tsx
 * @description Resource Detail Page for the admin dashboard
 */
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiEditLine,
  RiDeleteBinLine,
  RiStarLine,
  RiStarFill,
  RiFileTextLine,
  RiVideoLine,
  RiLinkM,
  RiBookOpenLine,
  RiArrowLeftLine,
  RiExternalLinkLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

// Import components
import ResourceContent from './components/ResourceContent';
import ResourceAnalytics from './components/ResourceAnalytics';
import ResourceFeedback from './components/ResourceFeedback';

interface IResourceDetailPageProps {
  params: {
    id: string;
  };
}

function ResourceDetailPage({ params }: IResourceDetailPageProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('content');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // TODO: Replace with actual API call to fetch resource details
  const resource = {
    id: params.id,
    title: 'Introduction to Data Structures',
    type: 'article',
    category: 'Computer Science',
    status: 'published',
    description:
      'A comprehensive guide to understanding data structures and their implementations. This resource covers arrays, linked lists, stacks, queues, trees, and graphs.',
    content: 'https://example.com/data-structures',
    author: 'Jane Smith',
    difficulty: 'Intermediate',
    estimatedTime: '45 minutes',
    views: 1250,
    completions: 875,
    rating: 4.7,
    createdAt: '2025-02-15T10:30:00Z',
    updatedAt: '2025-03-10T14:45:00Z',
    isFeatured: true,
    topics: ['Arrays', 'Linked Lists', 'Trees', 'Graphs', 'Algorithms'],
    prerequisites: ['Basic Programming', 'JavaScript Fundamentals'],
  };

  // Handle featuring/unfeaturing a resource
  const handleToggleFeature = () => {
    // TODO: Implement API call to toggle feature status
    console.log(`Toggle feature for resource ${params.id}`);
  };

  // Handle deleting a resource
  const handleDelete = () => {
    // TODO: Implement API call to delete resource
    console.log(`Delete resource ${params.id}`);
    router.push('/admin/resources');
  };

  // Resource type icon mapping
  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case 'article':
        return <RiFileTextLine className="h-5 w-5" />;
      case 'video':
        return <RiVideoLine className="h-5 w-5" />;
      case 'link':
        return <RiLinkM className="h-5 w-5" />;
      case 'document':
        return <RiFileTextLine className="h-5 w-5" />;
      case 'book':
        return <RiBookOpenLine className="h-5 w-5" />;
      default:
        return <RiFileTextLine className="h-5 w-5" />;
    }
  };

  // Resource status badge mapping
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="success">Published</Badge>;
      case 'draft':
        return <Badge variant="outline">Draft</Badge>;
      case 'archived':
        return <Badge variant="secondary">Archived</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header with navigation and actions */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/admin/resources')}
            className="h-8 w-8"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">
            {resource.title}
          </h2>
          {resource.isFeatured && (
            <RiStarFill className="h-5 w-5 text-amber-500" />
          )}
        </div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Button
            variant="outline"
            onClick={handleToggleFeature}
            className="flex items-center gap-1"
          >
            {resource.isFeatured ? (
              <>
                <RiStarLine className="h-4 w-4" />
                Unfeature
              </>
            ) : (
              <>
                <RiStarFill className="h-4 w-4" />
                Feature
              </>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push(`/admin/resources/${params.id}/edit`)}
            className="flex items-center gap-1"
          >
            <RiEditLine className="h-4 w-4" />
            Edit
          </Button>
          <AlertDialog
            open={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
          >
            <AlertDialogTrigger asChild>
              <Button variant="destructive" className="flex items-center gap-1">
                <RiDeleteBinLine className="h-4 w-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This will permanently delete the resource &quot;
                  {resource.title}&quot; and remove it from all user libraries.
                  This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete}>
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Resource metadata */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Type</CardTitle>
            {getResourceTypeIcon(resource.type as string)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold capitalize">{resource.type}</div>
            <p className="text-xs text-muted-foreground">{resource.category}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {getStatusBadge(resource.status as string)}
            </div>
            <p className="text-xs text-muted-foreground">
              Last updated: {new Date(resource.updatedAt).toLocaleDateString()}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Engagement</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="h-4 w-4 text-muted-foreground"
            >
              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
            </svg>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resource.views.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {resource.completions.toLocaleString()} completions (
              {Math.round((resource.completions / resource.views) * 100)}%)
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rating</CardTitle>
            <RiStarFill className="h-4 w-4 text-amber-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {resource.rating.toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">
              Based on user feedback
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Resource description */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>About this resource</CardTitle>
              <CardDescription>Details and metadata</CardDescription>
            </div>
            {resource.content && (
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                asChild
              >
                <a
                  href={resource.content}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <RiExternalLinkLine className="h-4 w-4" />
                  View Source
                </a>
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium">Description</h4>
              <p className="mt-1 text-sm text-muted-foreground">
                {resource.description}
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <h4 className="text-sm font-medium">Author</h4>
                <p className="mt-1 text-sm text-muted-foreground">
                  {resource.author}
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Difficulty</h4>
                <p className="mt-1 text-sm text-muted-foreground">
                  {resource.difficulty}
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Estimated Time</h4>
                <p className="mt-1 text-sm text-muted-foreground">
                  {resource.estimatedTime}
                </p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Created</h4>
                <p className="mt-1 text-sm text-muted-foreground">
                  {new Date(resource.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium">Topics</h4>
              <div className="mt-1 flex flex-wrap gap-1">
                {resource.topics.map((topic) => (
                  <Badge key={topic} variant="secondary">
                    {topic}
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium">Prerequisites</h4>
              <div className="mt-1 flex flex-wrap gap-1">
                {resource.prerequisites.map((prereq) => (
                  <Badge key={prereq} variant="outline">
                    {prereq}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for different sections */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="feedback">User Feedback</TabsTrigger>
        </TabsList>
        <TabsContent value="content" className="mt-6">
          <ResourceContent
            resourceId={params.id}
            resourceType={resource.type as string}
            resourceContent={resource.content}
          />
        </TabsContent>
        <TabsContent value="analytics" className="mt-6">
          <ResourceAnalytics resourceId={params.id} />
        </TabsContent>
        <TabsContent value="feedback" className="mt-6">
          <ResourceFeedback resourceId={params.id} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ResourceDetailPage;
