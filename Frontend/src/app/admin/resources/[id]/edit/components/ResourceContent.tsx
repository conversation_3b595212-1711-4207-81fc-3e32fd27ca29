/**
 * @file ResourceContent.tsx
 * @description Component for editing the content of a resource
 */
'use client';

import { useState } from 'react';
import {
  RiFileTextLine,
  RiVideoLine,
  RiLinkM,
  RiBookOpenLine,
  RiAddLine,
  RiCloseLine,
  RiUpload2Line,
  RiExternalLinkLine,
  RiDeleteBinLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';

interface IAttachment {
  name: string;
  size: string;
  type: string;
  url: string;
}

interface IResourceContentProps {
  resourceData: {
    type: string;
    content: string;
    attachments: IAttachment[];
  };
  updateResourceData: (
    data: Partial<IResourceContentProps['resourceData']>,
  ) => void;
}

function ResourceContent({
  resourceData,
  updateResourceData,
}: IResourceContentProps) {
  const [newAttachmentName, setNewAttachmentName] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  // Handle content input based on resource type
  const handleContentInput = (value: string) => {
    updateResourceData({ content: value });
  };

  // Handle file upload
  const handleFileUpload = () => {
    // TODO: Implement actual file upload
    setIsUploading(true);

    // Simulate file upload
    setTimeout(() => {
      const newAttachment = {
        name: newAttachmentName || 'new_file.pdf',
        size: '1.2 MB',
        type: 'pdf',
        url: 'https://example.com/new_file.pdf',
      };

      updateResourceData({
        attachments: [...resourceData.attachments, newAttachment],
      });

      setNewAttachmentName('');
      setIsUploading(false);
    }, 1500);
  };

  // Handle attachment removal
  const handleRemoveAttachment = (index: number) => {
    const updatedAttachments = [...resourceData.attachments];
    updatedAttachments.splice(index, 1);
    updateResourceData({ attachments: updatedAttachments });
  };

  // Render content input based on resource type
  const renderContentInput = () => {
    switch (resourceData.type) {
      case 'video':
        return (
          <div className="space-y-2">
            <Label htmlFor="video-url">Video URL</Label>
            <div className="flex gap-2">
              <Input
                id="video-url"
                placeholder="Enter video URL (YouTube, Vimeo, etc.)"
                value={resourceData.content}
                onChange={(e) => handleContentInput(e.target.value)}
              />
              {resourceData.content && (
                <Button variant="outline" size="icon" asChild>
                  <a
                    href={resourceData.content}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <RiExternalLinkLine className="h-4 w-4" />
                  </a>
                </Button>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Paste the URL of the video you want to embed
            </p>
          </div>
        );
      case 'link':
        return (
          <div className="space-y-2">
            <Label htmlFor="external-link">External Link</Label>
            <div className="flex gap-2">
              <Input
                id="external-link"
                placeholder="Enter external resource URL"
                value={resourceData.content}
                onChange={(e) => handleContentInput(e.target.value)}
              />
              {resourceData.content && (
                <Button variant="outline" size="icon" asChild>
                  <a
                    href={resourceData.content}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <RiExternalLinkLine className="h-4 w-4" />
                  </a>
                </Button>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Link to an external resource like a blog post, documentation, or
              website
            </p>
          </div>
        );
      case 'article':
        return (
          <div className="space-y-2">
            <Label htmlFor="article-content">Article Content</Label>
            <Textarea
              id="article-content"
              placeholder="Write your article content here..."
              value={resourceData.content}
              onChange={(e) => handleContentInput(e.target.value)}
              rows={10}
            />
            <p className="text-xs text-muted-foreground">
              You can use Markdown formatting for rich text
            </p>
          </div>
        );
      case 'document':
      case 'book':
      default:
        return (
          <div className="space-y-2">
            <Label htmlFor="content-url">Content URL</Label>
            <div className="flex gap-2">
              <Input
                id="content-url"
                placeholder="Enter content URL"
                value={resourceData.content}
                onChange={(e) => handleContentInput(e.target.value)}
              />
              {resourceData.content && (
                <Button variant="outline" size="icon" asChild>
                  <a
                    href={resourceData.content}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <RiExternalLinkLine className="h-4 w-4" />
                  </a>
                </Button>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Link to the main content of this resource
            </p>
          </div>
        );
    }
  };

  // Get resource type icon
  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case 'article':
        return <RiFileTextLine className="h-5 w-5" />;
      case 'video':
        return <RiVideoLine className="h-5 w-5" />;
      case 'link':
        return <RiLinkM className="h-5 w-5" />;
      case 'document':
        return <RiFileTextLine className="h-5 w-5" />;
      case 'book':
        return <RiBookOpenLine className="h-5 w-5" />;
      default:
        return <RiFileTextLine className="h-5 w-5" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center gap-2">
          {getResourceTypeIcon(resourceData.type)}
          <CardTitle>Resource Content</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">{renderContentInput()}</CardContent>
      </Card>

      {/* Attachments */}
      <Card>
        <CardHeader>
          <CardTitle>Attachments</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Existing Attachments */}
          {resourceData.attachments.length > 0 && (
            <div className="space-y-2">
              {resourceData.attachments.map((attachment, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-md border p-3 hover:bg-muted/50"
                >
                  <div className="flex items-center gap-3">
                    <RiFileTextLine className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{attachment.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {attachment.size}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" asChild>
                      <a
                        href={attachment.url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <RiExternalLinkLine className="h-4 w-4" />
                        <span className="sr-only">View</span>
                      </a>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveAttachment(index)}
                      className="text-destructive hover:bg-destructive/10 hover:text-destructive"
                    >
                      <RiDeleteBinLine className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Upload New Attachment */}
          <div className="space-y-4">
            <Label htmlFor="attachment">Upload New Attachment</Label>
            <div className="flex gap-2">
              <Input
                id="attachment"
                placeholder="Attachment name (optional)"
                value={newAttachmentName}
                onChange={(e) => setNewAttachmentName(e.target.value)}
              />
              <Button
                variant="outline"
                onClick={handleFileUpload}
                disabled={isUploading}
                className="flex items-center gap-1 whitespace-nowrap"
              >
                <RiUpload2Line className="h-4 w-4" />
                {isUploading ? 'Uploading...' : 'Upload'}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Upload supplementary files like PDFs, spreadsheets, or code
              samples
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ResourceContent;
