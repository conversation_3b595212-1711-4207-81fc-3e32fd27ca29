/**
 * @file ResourceBasicInfo.tsx
 * @description Component for editing basic information of a resource
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiFileTextLine,
  RiVideoLine,
  RiLinkM,
  RiBookOpenLine,
  RiUser3Line,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface IResourceBasicInfoProps {
  resourceData: {
    title: string;
    type: string;
    category: string;
    description: string;
    author: string;
  };
  updateResourceData: (
    data: Partial<IResourceBasicInfoProps['resourceData']>,
  ) => void;
}

function ResourceBasicInfo({
  resourceData,
  updateResourceData,
}: IResourceBasicInfoProps) {
  // TODO: Replace with actual API call to fetch categories
  const categories = [
    'Computer Science',
    'Web Development',
    'Mobile Development',
    'Data Science',
    'Machine Learning',
    'DevOps',
    'Cloud Computing',
    'Blockchain',
    'Cybersecurity',
    'Artificial Intelligence',
    'Career',
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              placeholder="Enter resource title"
              value={resourceData.title}
              onChange={(e) => updateResourceData({ title: e.target.value })}
            />
          </div>

          {/* Type */}
          <div className="space-y-2">
            <Label htmlFor="type">Resource Type</Label>
            <Select
              value={resourceData.type}
              onValueChange={(value) => updateResourceData({ type: value })}
            >
              <SelectTrigger id="type">
                <SelectValue placeholder="Select resource type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="article">
                  <div className="flex items-center gap-2">
                    <RiFileTextLine className="h-4 w-4" />
                    <span>Article</span>
                  </div>
                </SelectItem>
                <SelectItem value="video">
                  <div className="flex items-center gap-2">
                    <RiVideoLine className="h-4 w-4" />
                    <span>Video</span>
                  </div>
                </SelectItem>
                <SelectItem value="link">
                  <div className="flex items-center gap-2">
                    <RiLinkM className="h-4 w-4" />
                    <span>Link</span>
                  </div>
                </SelectItem>
                <SelectItem value="document">
                  <div className="flex items-center gap-2">
                    <RiFileTextLine className="h-4 w-4" />
                    <span>Document</span>
                  </div>
                </SelectItem>
                <SelectItem value="book">
                  <div className="flex items-center gap-2">
                    <RiBookOpenLine className="h-4 w-4" />
                    <span>Book</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Category */}
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select
              value={resourceData.category}
              onValueChange={(value) => updateResourceData({ category: value })}
            >
              <SelectTrigger id="category">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter resource description"
              value={resourceData.description}
              onChange={(e) =>
                updateResourceData({ description: e.target.value })
              }
              rows={4}
            />
          </div>

          {/* Author */}
          <div className="space-y-2">
            <Label htmlFor="author">Author</Label>
            <div className="relative">
              <RiUser3Line className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="author"
                placeholder="Enter author name"
                value={resourceData.author}
                onChange={(e) => updateResourceData({ author: e.target.value })}
                className="pl-9"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ResourceBasicInfo;
