/**
 * @file ResourceSettings.tsx
 * @description Component for configuring additional settings for a resource
 */
'use client';

import { useState } from 'react';
import {
  RiAddLine,
  RiCloseLine,
  RiTimeLine,
  RiBarChartLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface IResourceSettingsProps {
  resourceData: {
    difficulty: string;
    estimatedTime: string;
    status: string;
    topics: string[];
    prerequisites: string[];
  };
  updateResourceData: (
    data: Partial<IResourceSettingsProps['resourceData']>,
  ) => void;
}

function ResourceSettings({
  resourceData,
  updateResourceData,
}: IResourceSettingsProps) {
  const [newTopic, setNewTopic] = useState('');
  const [newPrerequisite, setNewPrerequisite] = useState('');

  // Handle adding a new topic
  const handleAddTopic = () => {
    if (newTopic.trim() && !resourceData.topics.includes(newTopic.trim())) {
      updateResourceData({
        topics: [...resourceData.topics, newTopic.trim()],
      });
      setNewTopic('');
    }
  };

  // Handle removing a topic
  const handleRemoveTopic = (topic: string) => {
    updateResourceData({
      topics: resourceData.topics.filter((t) => t !== topic),
    });
  };

  // Handle adding a new prerequisite
  const handleAddPrerequisite = () => {
    if (
      newPrerequisite.trim() &&
      !resourceData.prerequisites.includes(newPrerequisite.trim())
    ) {
      updateResourceData({
        prerequisites: [...resourceData.prerequisites, newPrerequisite.trim()],
      });
      setNewPrerequisite('');
    }
  };

  // Handle removing a prerequisite
  const handleRemovePrerequisite = (prerequisite: string) => {
    updateResourceData({
      prerequisites: resourceData.prerequisites.filter(
        (p) => p !== prerequisite,
      ),
    });
  };

  return (
    <div className="space-y-6">
      {/* Difficulty and Time */}
      <Card>
        <CardHeader>
          <CardTitle>Resource Attributes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Difficulty */}
          <div className="space-y-2">
            <Label htmlFor="difficulty">Difficulty Level</Label>
            <Select
              value={resourceData.difficulty}
              onValueChange={(value) =>
                updateResourceData({ difficulty: value })
              }
            >
              <SelectTrigger id="difficulty">
                <SelectValue placeholder="Select difficulty level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Beginner">Beginner</SelectItem>
                <SelectItem value="Intermediate">Intermediate</SelectItem>
                <SelectItem value="Advanced">Advanced</SelectItem>
                <SelectItem value="Expert">Expert</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Estimated Time */}
          <div className="space-y-2">
            <Label htmlFor="estimated-time">Estimated Completion Time</Label>
            <div className="relative">
              <RiTimeLine className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="estimated-time"
                placeholder="e.g. 30 minutes, 2 hours"
                value={resourceData.estimatedTime}
                onChange={(e) =>
                  updateResourceData({ estimatedTime: e.target.value })
                }
                className="pl-9"
              />
            </div>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Publication Status</Label>
            <Select
              value={resourceData.status}
              onValueChange={(value) => updateResourceData({ status: value })}
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Topics */}
      <Card>
        <CardHeader>
          <CardTitle>Topics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Existing Topics */}
          <div className="flex flex-wrap gap-2">
            {resourceData.topics.length > 0 ? (
              resourceData.topics.map((topic, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {topic}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="ml-1 h-4 w-4 p-0 text-muted-foreground hover:text-foreground"
                    onClick={() => handleRemoveTopic(topic)}
                  >
                    <RiCloseLine className="h-3 w-3" />
                  </Button>
                </Badge>
              ))
            ) : (
              <div className="text-sm text-muted-foreground">
                No topics added yet
              </div>
            )}
          </div>

          {/* Add New Topic */}
          <div className="flex gap-2">
            <Input
              placeholder="Add a topic"
              value={newTopic}
              onChange={(e) => setNewTopic(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleAddTopic()}
            />
            <Button
              variant="outline"
              onClick={handleAddTopic}
              className="flex items-center gap-1 whitespace-nowrap"
              disabled={!newTopic.trim()}
            >
              <RiAddLine className="h-4 w-4" />
              Add
            </Button>
          </div>
          <p className="text-xs text-muted-foreground">
            Add relevant topics to help users find this resource
          </p>
        </CardContent>
      </Card>

      {/* Prerequisites */}
      <Card>
        <CardHeader>
          <CardTitle>Prerequisites</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Existing Prerequisites */}
          <div className="flex flex-wrap gap-2">
            {resourceData.prerequisites.length > 0 ? (
              resourceData.prerequisites.map((prerequisite, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  {prerequisite}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="ml-1 h-4 w-4 p-0 text-muted-foreground hover:text-foreground"
                    onClick={() => handleRemovePrerequisite(prerequisite)}
                  >
                    <RiCloseLine className="h-3 w-3" />
                  </Button>
                </Badge>
              ))
            ) : (
              <div className="text-sm text-muted-foreground">
                No prerequisites added yet
              </div>
            )}
          </div>

          {/* Add New Prerequisite */}
          <div className="flex gap-2">
            <Input
              placeholder="Add a prerequisite"
              value={newPrerequisite}
              onChange={(e) => setNewPrerequisite(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleAddPrerequisite()}
            />
            <Button
              variant="outline"
              onClick={handleAddPrerequisite}
              className="flex items-center gap-1 whitespace-nowrap"
              disabled={!newPrerequisite.trim()}
            >
              <RiAddLine className="h-4 w-4" />
              Add
            </Button>
          </div>
          <p className="text-xs text-muted-foreground">
            List any knowledge or skills users should have before accessing this
            resource
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

export default ResourceSettings;
