/**
 * @file page.tsx
 * @description Resource Editor Page for the admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { RiArrowLeftLine, RiSaveLine, RiCloseLine } from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { toast } from '@/components/ui/toast';

// Import components
import ResourceBasicInfo from './components/ResourceBasicInfo';
import ResourceContent from './components/ResourceContent';
import ResourceSettings from './components/ResourceSettings';

interface IResourceEditorPageProps {
  params: {
    id: string;
  };
}

function ResourceEditorPage({ params }: IResourceEditorPageProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('basic-info');
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [resourceData, setResourceData] = useState({
    title: '',
    type: '',
    category: '',
    description: '',
    content: '',
    author: '',
    difficulty: '',
    estimatedTime: '',
    status: 'draft',
    topics: [],
    prerequisites: [],
    attachments: [],
  });

  const isNewResource = params.id === 'new';

  // Load resource data if editing an existing resource
  useEffect(() => {
    if (!isNewResource) {
      // TODO: Replace with actual API call to fetch resource data
      const fetchResourceData = async () => {
        try {
          // Mock data for now
          const mockResource = {
            id: params.id,
            title: 'Introduction to Data Structures',
            type: 'article',
            category: 'Computer Science',
            description:
              'A comprehensive guide to understanding data structures and their implementations.',
            content: 'https://example.com/data-structures',
            author: 'Jane Smith',
            difficulty: 'Intermediate',
            estimatedTime: '45 minutes',
            status: 'published',
            topics: ['Arrays', 'Linked Lists', 'Trees', 'Graphs', 'Algorithms'],
            prerequisites: ['Basic Programming', 'JavaScript Fundamentals'],
            attachments: [
              {
                name: 'data_structures_cheatsheet.pdf',
                size: '2.4 MB',
                type: 'pdf',
                url: 'https://example.com/data_structures_cheatsheet.pdf',
              },
              {
                name: 'complexity_analysis.xlsx',
                size: '1.1 MB',
                type: 'xlsx',
                url: 'https://example.com/complexity_analysis.xlsx',
              },
            ],
          };

          setResourceData(mockResource);
        } catch (error) {
          console.error('Error fetching resource data:', error);
          // TODO: Show error toast
        }
      };

      fetchResourceData();
    }
  }, [params.id, isNewResource]);

  // Update resource data
  const updateResourceData = (data: Partial<typeof resourceData>) => {
    setResourceData((prev) => ({
      ...prev,
      ...data,
    }));
    setHasChanges(true);
  };

  // Handle save
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Replace with actual API call to save resource data
      console.log('Saving resource data:', resourceData);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setHasChanges(false);

      if (isNewResource) {
        // Redirect to the newly created resource
        router.push('/admin/resources/1'); // Replace with actual ID
      } else {
        // TODO: Show success toast
        console.log('Resource updated successfully');
      }
    } catch (error) {
      console.error('Error saving resource data:', error);
      // TODO: Show error toast
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (hasChanges) {
      // TODO: Show confirmation dialog
      if (
        confirm('You have unsaved changes. Are you sure you want to leave?')
      ) {
        router.push(
          isNewResource ? '/admin/resources' : `/admin/resources/${params.id}`,
        );
      }
    } else {
      router.push(
        isNewResource ? '/admin/resources' : `/admin/resources/${params.id}`,
      );
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header with navigation and actions */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/admin/resources')}
            className="h-8 w-8"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">
            {isNewResource ? 'Create New Resource' : 'Edit Resource'}
          </h2>
        </div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex items-center gap-1"
          >
            <RiCloseLine className="h-4 w-4" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            className="flex items-center gap-1"
            disabled={isSaving}
          >
            <RiSaveLine className="h-4 w-4" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>

      {/* Editor Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic-info">Basic Info</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        <TabsContent value="basic-info" className="mt-6">
          <ResourceBasicInfo
            resourceData={resourceData}
            updateResourceData={updateResourceData}
          />
        </TabsContent>
        <TabsContent value="content" className="mt-6">
          <ResourceContent
            resourceData={resourceData}
            updateResourceData={updateResourceData}
          />
        </TabsContent>
        <TabsContent value="settings" className="mt-6">
          <ResourceSettings
            resourceData={resourceData}
            updateResourceData={updateResourceData}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ResourceEditorPage;
