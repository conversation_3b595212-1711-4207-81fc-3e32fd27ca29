/**
 * @file page.tsx
 * @description Challenges management page for admin dashboard
 */
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiAddLine,
  RiSearchLine,
  RiFilterLine,
  RiTrophyLine,
  RiCodeLine,
  RiTimeLine,
  RiUserLine,
  RiCheckboxCircleLine,
  RiErrorWarningLine,
  RiBarChartLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';

// Define challenge interface
interface IChallenge {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'Easy' | 'Medium' | 'Hard' | 'Expert';
  type: 'Coding' | 'Quiz' | 'Project' | 'Algorithm';
  status: 'Active' | 'Draft' | 'Archived';
  isFeatured: boolean;
  completionRate: number;
  averageAttempts: number;
  successRate: number;
  totalAttempts: number;
  createdAt: string;
  updatedAt: string;
  estimatedMinutes: number;
  authorId: string;
  authorName: string;
  tags: string[];
}

function ChallengesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  // TODO: Replace with actual API call
  const challenges: IChallenge[] = [
    {
      id: '1',
      title: 'Build a Responsive Navigation Bar',
      description:
        'Create a responsive navigation bar using HTML, CSS, and JavaScript that collapses into a hamburger menu on mobile devices.',
      category: 'Frontend',
      difficulty: 'Easy',
      type: 'Coding',
      status: 'Active',
      isFeatured: true,
      completionRate: 78,
      averageAttempts: 2.3,
      successRate: 85,
      totalAttempts: 1245,
      createdAt: '2023-02-15T10:30:00Z',
      updatedAt: '2023-05-20T14:15:00Z',
      estimatedMinutes: 45,
      authorId: '101',
      authorName: 'Sarah Johnson',
      tags: ['html', 'css', 'javascript', 'responsive'],
    },
    {
      id: '2',
      title: 'Implement a Binary Search Tree',
      description:
        'Implement a binary search tree data structure with methods for insertion, deletion, and traversal.',
      category: 'Data Structures',
      difficulty: 'Medium',
      type: 'Algorithm',
      status: 'Active',
      isFeatured: false,
      completionRate: 62,
      averageAttempts: 3.7,
      successRate: 68,
      totalAttempts: 892,
      createdAt: '2023-03-10T09:45:00Z',
      updatedAt: '2023-06-05T11:20:00Z',
      estimatedMinutes: 60,
      authorId: '102',
      authorName: 'Michael Chen',
      tags: ['data structures', 'algorithms', 'binary tree', 'traversal'],
    },
    {
      id: '3',
      title: 'Create a RESTful API with Express',
      description:
        'Build a RESTful API using Express.js with endpoints for CRUD operations on a resource of your choice.',
      category: 'Backend',
      difficulty: 'Medium',
      type: 'Project',
      status: 'Active',
      isFeatured: true,
      completionRate: 54,
      averageAttempts: 2.9,
      successRate: 72,
      totalAttempts: 756,
      createdAt: '2023-04-05T13:15:00Z',
      updatedAt: '2023-06-12T16:40:00Z',
      estimatedMinutes: 90,
      authorId: '103',
      authorName: 'Alex Rodriguez',
      tags: ['nodejs', 'express', 'api', 'rest'],
    },
    {
      id: '4',
      title: 'JavaScript Fundamentals Quiz',
      description:
        'Test your knowledge of JavaScript fundamentals including variables, functions, objects, and arrays.',
      category: 'Frontend',
      difficulty: 'Easy',
      type: 'Quiz',
      status: 'Active',
      isFeatured: false,
      completionRate: 92,
      averageAttempts: 1.4,
      successRate: 88,
      totalAttempts: 1532,
      createdAt: '2023-01-20T10:00:00Z',
      updatedAt: '2023-05-15T09:30:00Z',
      estimatedMinutes: 20,
      authorId: '104',
      authorName: 'Jessica Lee',
      tags: ['javascript', 'fundamentals', 'quiz'],
    },
    {
      id: '5',
      title: 'Implement OAuth 2.0 Authentication',
      description:
        'Implement OAuth 2.0 authentication in a web application using a provider like Google or GitHub.',
      category: 'Security',
      difficulty: 'Hard',
      type: 'Project',
      status: 'Draft',
      isFeatured: false,
      completionRate: 0,
      averageAttempts: 0,
      successRate: 0,
      totalAttempts: 0,
      createdAt: '2023-06-01T11:20:00Z',
      updatedAt: '2023-06-01T11:20:00Z',
      estimatedMinutes: 120,
      authorId: '105',
      authorName: 'David Wilson',
      tags: ['oauth', 'authentication', 'security'],
    },
    {
      id: '6',
      title: 'Optimize Database Queries',
      description:
        'Analyze and optimize slow database queries to improve performance in a sample application.',
      category: 'Database',
      difficulty: 'Hard',
      type: 'Coding',
      status: 'Active',
      isFeatured: false,
      completionRate: 45,
      averageAttempts: 4.2,
      successRate: 52,
      totalAttempts: 423,
      createdAt: '2023-05-12T09:15:00Z',
      updatedAt: '2023-06-10T10:30:00Z',
      estimatedMinutes: 75,
      authorId: '106',
      authorName: 'Emily Parker',
      tags: ['database', 'optimization', 'sql', 'performance'],
    },
    {
      id: '7',
      title: 'Build a Machine Learning Model',
      description:
        'Create a simple machine learning model to classify images using a popular framework like TensorFlow or PyTorch.',
      category: 'Machine Learning',
      difficulty: 'Expert',
      type: 'Project',
      status: 'Active',
      isFeatured: true,
      completionRate: 38,
      averageAttempts: 5.1,
      successRate: 42,
      totalAttempts: 312,
      createdAt: '2023-04-20T14:30:00Z',
      updatedAt: '2023-06-08T15:45:00Z',
      estimatedMinutes: 150,
      authorId: '107',
      authorName: 'Robert Taylor',
      tags: ['machine learning', 'tensorflow', 'classification', 'ai'],
    },
  ];

  // Get unique categories for filter
  const categories = [
    'all',
    ...new Set(challenges.map((challenge) => challenge.category)),
  ];

  // Filter challenges based on search query and filters
  const filteredChallenges = challenges.filter((challenge) => {
    const matchesSearch =
      challenge.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      challenge.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      challenge.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );

    const matchesCategory =
      categoryFilter === 'all' || challenge.category === categoryFilter;
    const matchesDifficulty =
      difficultyFilter === 'all' || challenge.difficulty === difficultyFilter;
    const matchesType = typeFilter === 'all' || challenge.type === typeFilter;

    return matchesSearch && matchesCategory && matchesDifficulty && matchesType;
  });

  // Handle challenge actions
  const handleFeatureToggle = (
    challengeId: string,
    currentlyFeatured: boolean,
  ) => {
    // TODO: Implement API call to toggle featured status
    toast({
      title: currentlyFeatured ? 'Challenge Unfeatured' : 'Challenge Featured',
      description: `Challenge has been ${currentlyFeatured ? 'removed from' : 'added to'} featured section.`,
    });
  };

  const handleStatusChange = (challengeId: string, newStatus: string) => {
    // TODO: Implement API call to change status
    toast({
      title: 'Status Updated',
      description: `Challenge status has been changed to ${newStatus}.`,
    });
  };

  const handleDeleteChallenge = (
    challengeId: string,
    challengeTitle: string,
  ) => {
    if (
      confirm(
        `Are you sure you want to delete "${challengeTitle}"? This action cannot be undone.`,
      )
    ) {
      // TODO: Implement API call to delete challenge
      toast({
        title: 'Challenge Deleted',
        description: `"${challengeTitle}" has been permanently deleted.`,
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold">Challenge Management</h1>
          <p className="text-muted-foreground">
            Manage coding challenges and quizzes
          </p>
        </div>
        <Button
          className="flex items-center gap-2"
          onClick={() => router.push('/admin/challenges/create')}
        >
          <RiAddLine /> Create New Challenge
        </Button>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <div className="relative">
          <Input
            placeholder="Search challenges..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 transform text-muted-foreground" />
        </div>

        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger>
            <div className="flex items-center">
              <RiFilterLine className="mr-2" />
              <SelectValue placeholder="Category" />
            </div>
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={difficultyFilter} onValueChange={setDifficultyFilter}>
          <SelectTrigger>
            <div className="flex items-center">
              <RiFilterLine className="mr-2" />
              <SelectValue placeholder="Difficulty" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Difficulties</SelectItem>
            <SelectItem value="Easy">Easy</SelectItem>
            <SelectItem value="Medium">Medium</SelectItem>
            <SelectItem value="Hard">Hard</SelectItem>
            <SelectItem value="Expert">Expert</SelectItem>
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger>
            <div className="flex items-center">
              <RiFilterLine className="mr-2" />
              <SelectValue placeholder="Type" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="Coding">Coding</SelectItem>
            <SelectItem value="Quiz">Quiz</SelectItem>
            <SelectItem value="Project">Project</SelectItem>
            <SelectItem value="Algorithm">Algorithm</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Challenges List */}
      <div className="grid grid-cols-1 gap-6">
        {filteredChallenges.length > 0 ? (
          filteredChallenges.map((challenge) => (
            <Card key={challenge.id} className="p-6">
              <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h2 className="text-xl font-semibold">{challenge.title}</h2>
                    {challenge.isFeatured && (
                      <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
                        Featured
                      </Badge>
                    )}
                    <Badge
                      className={`${
                        challenge.status === 'Active'
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : challenge.status === 'Draft'
                            ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                      }`}
                    >
                      {challenge.status}
                    </Badge>
                    <Badge
                      className={`${
                        challenge.difficulty === 'Easy'
                          ? 'bg-green-100 text-green-800'
                          : challenge.difficulty === 'Medium'
                            ? 'bg-blue-100 text-blue-800'
                            : challenge.difficulty === 'Hard'
                              ? 'bg-orange-100 text-orange-800'
                              : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {challenge.difficulty}
                    </Badge>
                  </div>

                  <p className="mt-2 text-muted-foreground">
                    {challenge.description}
                  </p>

                  <div className="mt-2 flex flex-wrap gap-2">
                    {challenge.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="mt-4 flex flex-wrap items-center gap-x-6 gap-y-2 text-sm">
                    <div className="flex items-center">
                      <RiCodeLine className="mr-1 text-muted-foreground" />
                      <span>{challenge.type}</span>
                    </div>
                    <div className="flex items-center">
                      <RiUserLine className="mr-1 text-muted-foreground" />
                      <span>
                        {challenge.totalAttempts.toLocaleString()} attempts
                      </span>
                    </div>
                    <div className="flex items-center">
                      <RiCheckboxCircleLine className="mr-1 text-muted-foreground" />
                      <span>{challenge.completionRate}% completion rate</span>
                    </div>
                    <div className="flex items-center">
                      <RiErrorWarningLine className="mr-1 text-muted-foreground" />
                      <span>
                        {challenge.averageAttempts.toFixed(1)} avg attempts
                      </span>
                    </div>
                    <div className="flex items-center">
                      <RiTimeLine className="mr-1 text-muted-foreground" />
                      <span>{challenge.estimatedMinutes} minutes</span>
                    </div>
                  </div>

                  <div className="mt-2 text-xs text-muted-foreground">
                    <span>
                      Created by {challenge.authorName} u2022 Last updated{' '}
                      {new Date(challenge.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 md:min-w-[180px] md:flex-col md:items-end">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                    onClick={() =>
                      router.push(`/admin/challenges/${challenge.id}`)
                    }
                  >
                    View Details
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                    onClick={() =>
                      router.push(`/admin/challenges/${challenge.id}/edit`)
                    }
                  >
                    Edit
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    className={`flex items-center gap-1 ${challenge.isFeatured ? 'border-amber-300 text-amber-700 hover:bg-amber-100' : ''}`}
                    onClick={() =>
                      handleFeatureToggle(challenge.id, challenge.isFeatured)
                    }
                  >
                    {challenge.isFeatured ? 'Unfeature' : 'Feature'}
                  </Button>

                  <Select
                    value={challenge.status}
                    onValueChange={(value) =>
                      handleStatusChange(challenge.id, value)
                    }
                  >
                    <SelectTrigger className="h-9 w-full md:w-auto">
                      <SelectValue placeholder="Change Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Draft">Draft</SelectItem>
                      <SelectItem value="Archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-300 text-red-700 hover:bg-red-100 flex items-center gap-1"
                    onClick={() =>
                      handleDeleteChallenge(challenge.id, challenge.title)
                    }
                  >
                    Delete
                  </Button>
                </div>
              </div>

              {/* Analytics Preview */}
              <div className="mt-6 border-t border-border pt-4">
                <div className="mb-2 flex items-center gap-2">
                  <RiBarChartLine className="text-muted-foreground" />
                  <h3 className="font-medium">Performance Metrics</h3>
                </div>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div className="rounded-md bg-muted p-3">
                    <div className="text-sm text-muted-foreground">
                      Total Attempts
                    </div>
                    <div className="text-2xl font-semibold">
                      {challenge.totalAttempts.toLocaleString()}
                    </div>
                  </div>
                  <div className="rounded-md bg-muted p-3">
                    <div className="text-sm text-muted-foreground">
                      Success Rate
                    </div>
                    <div className="text-2xl font-semibold">
                      {challenge.successRate}%
                    </div>
                  </div>
                  <div className="rounded-md bg-muted p-3">
                    <div className="text-sm text-muted-foreground">
                      Avg. Attempts
                    </div>
                    <div className="text-2xl font-semibold">
                      {challenge.averageAttempts.toFixed(1)}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          <div className="py-12 text-center">
            <RiTrophyLine className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">No challenges found</h3>
            <p className="mt-2 text-muted-foreground">
              {searchQuery ||
              categoryFilter !== 'all' ||
              difficultyFilter !== 'all' ||
              typeFilter !== 'all'
                ? "Try adjusting your search filters to find what you're looking for."
                : 'Get started by creating your first challenge.'}
            </p>
            {!searchQuery &&
              categoryFilter === 'all' &&
              difficultyFilter === 'all' &&
              typeFilter === 'all' && (
                <Button
                  className="mt-4 flex items-center gap-2"
                  onClick={() => router.push('/admin/challenges/create')}
                >
                  <RiAddLine /> Create New Challenge
                </Button>
              )}
          </div>
        )}
      </div>
    </div>
  );
}

export default ChallengesPage;
