/**
 * @file ChallengeSidebar.tsx
 * @description Sidebar component for challenge management with quick actions and navigation
 */
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiEdit2Line,
  RiDeleteBin6Line,
  RiEyeLine,
  RiArrowLeftLine,
  RiStarLine,
  RiStarFill,
  RiHistoryLine,
  RiTimeLine,
  RiCheckboxCircleLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from '@/components/ui/use-toast';
import axios from 'axios';

interface IChallengeSidebarProps {
  challengeId: string;
  challengeName: string;
  isFeatured: boolean;
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  updatedAt: string;
  completionRate?: number;
  onFeatureToggle: () => void;
}

function ChallengeSidebar({
  challengeId,
  challengeName,
  isFeatured,
  status,
  createdAt,
  updatedAt,
  completionRate,
  onFeatureToggle,
}: IChallengeSidebarProps) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Handle delete challenge
  const handleDeleteChallenge = async () => {
    setIsDeleting(true);
    try {
      // TODO: Replace with actual API call when backend is ready
      await axios.delete(`/api/challenges/${challengeId}`);

      toast({
        title: 'Challenge deleted',
        description: 'The challenge has been deleted successfully.',
      });

      // Navigate back to challenges list
      router.push('/admin/challenges');
    } catch (err) {
      console.error('Error deleting challenge:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete challenge. Please try again.',
        variant: 'destructive',
      });
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // Get status badge color
  const getStatusColor = () => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-amber-100 text-amber-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          {/* Quick Actions */}
          <div className="space-y-1 p-4">
            <Button
              variant="outline"
              className="flex w-full items-center justify-start gap-2"
              onClick={() => router.push('/admin/challenges')}
            >
              <RiArrowLeftLine />
              Back to Challenges
            </Button>

            <Button
              variant="outline"
              className="flex w-full items-center justify-start gap-2"
              onClick={() => router.push(`/challenges/${challengeId}`)}
            >
              <RiEyeLine />
              View Challenge
            </Button>

            <Button
              variant="outline"
              className="flex w-full items-center justify-start gap-2"
              onClick={() =>
                router.push(`/admin/challenges/${challengeId}/edit`)
              }
            >
              <RiEdit2Line />
              Edit Challenge
            </Button>

            <Button
              variant={isFeatured ? 'default' : 'outline'}
              className={`flex w-full items-center justify-start gap-2 ${
                isFeatured ? 'bg-amber-600 hover:bg-amber-700' : ''
              }`}
              onClick={onFeatureToggle}
            >
              {isFeatured ? <RiStarFill /> : <RiStarLine />}
              {isFeatured ? 'Featured' : 'Feature Challenge'}
            </Button>

            <Button
              variant="destructive"
              className="flex w-full items-center justify-start gap-2"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              <RiDeleteBin6Line />
              Delete Challenge
            </Button>
          </div>

          {/* Challenge Info */}
          <div className="border-t p-4">
            <h3 className="mb-4 font-medium">Challenge Information</h3>

            <div className="space-y-3 text-sm">
              <div>
                <div className="text-muted-foreground">Status</div>
                <div
                  className={`mt-1 inline-block rounded-full px-2 py-0.5 text-xs font-medium ${getStatusColor()}`}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </div>
              </div>

              <div>
                <div className="text-muted-foreground">Created</div>
                <div className="flex items-center gap-1">
                  <RiHistoryLine className="h-4 w-4 text-muted-foreground" />
                  {formatDate(createdAt)}
                </div>
              </div>

              <div>
                <div className="text-muted-foreground">Last Updated</div>
                <div className="flex items-center gap-1">
                  <RiTimeLine className="h-4 w-4 text-muted-foreground" />
                  {formatDate(updatedAt)}
                </div>
              </div>

              {completionRate !== undefined && (
                <div>
                  <div className="text-muted-foreground">Completion Rate</div>
                  <div className="flex items-center gap-1">
                    <RiCheckboxCircleLine className="h-4 w-4 text-muted-foreground" />
                    {completionRate}%
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the challenge &quot;{challengeName}
              &quot; and all associated data. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteChallenge();
              }}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                  Deleting...
                </>
              ) : (
                'Delete Challenge'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export default ChallengeSidebar;
