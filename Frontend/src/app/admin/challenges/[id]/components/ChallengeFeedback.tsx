/**
 * @file ChallengeFeedback.tsx
 * @description Component to display user feedback for a challenge
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiMessage2Line,
  RiStarFill,
  RiStarLine,
  RiSearchLine,
  RiFilterLine,
  RiSendPlane2Line,
  RiUserLine,
  RiErrorWarningLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import {
  fetchChallengeFeedback,
  respondToFeedback,
} from '@/services/challengeService';
import { safelyExtractData } from '@/utils/apiUtils';
import { extractErrorMessage } from '@/utils/errorUtils';

// Interface for feedback items from the API
interface IFeedbackItem {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  comment: string;
  createdAt: string;
  adminResponse?: {
    response: string;
    respondedAt: string;
    respondedBy: string;
  };
}

interface IChallengeFeedbackProps {
  challengeId: string;
}

function ChallengeFeedback({ challengeId }: IChallengeFeedbackProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [ratingFilter, setRatingFilter] = useState('all');
  const [selectedFeedback, setSelectedFeedback] =
    useState<IFeedbackItem | null>(null);
  const [responseText, setResponseText] = useState('');
  const [feedbackData, setFeedbackData] = useState<IFeedbackItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch feedback data from API
  useEffect(() => {
    const fetchFeedbackData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Call the API to fetch challenge feedback
        const response = await fetchChallengeFeedback(challengeId);

        // Extract feedback data from the response
        const feedbackItems = safelyExtractData<IFeedbackItem[], []>(
          response as unknown as Record<string, unknown>,
          'data.feedback',
          [],
        );

        setFeedbackData(feedbackItems);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching challenge feedback:', err);
        setError(
          extractErrorMessage(err) ||
            'Failed to load feedback data. Please try again.',
        );
        setLoading(false);
      }
    };

    fetchFeedbackData();
  }, [challengeId]);

  // Filter feedback based on search query and rating filter
  const filteredFeedback = feedbackData.filter((feedback) => {
    const matchesSearch =
      feedback.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      feedback.comment.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesRating =
      ratingFilter === 'all' || feedback.rating === parseInt(ratingFilter);

    return matchesSearch && matchesRating;
  });

  // Handle responding to feedback
  const handleRespondToFeedback = (feedback: IFeedbackItem) => {
    setSelectedFeedback(feedback);
    setResponseText(feedback.adminResponse?.response || '');
  };

  // Handle submitting response
  const handleSubmitResponse = async () => {
    if (!selectedFeedback || !responseText.trim()) return;

    try {
      // Call the API to respond to feedback
      const response = await respondToFeedback(
        challengeId,
        selectedFeedback.id,
        responseText,
      );

      // Extract the updated feedback from the response
      const updatedFeedback = safelyExtractData<IFeedbackItem, null>(
        response as unknown as Record<string, unknown>,
        'data.feedback',
        null,
      );

      if (updatedFeedback) {
        // Update local state with the new feedback data
        const updatedFeedbackData = feedbackData.map((item) =>
          item.id === selectedFeedback.id ? updatedFeedback : item,
        );

        setFeedbackData(updatedFeedbackData);
        setSelectedFeedback(null);
        setResponseText('');

        toast({
          title: 'Response submitted',
          description: 'Your response has been submitted successfully.',
        });
      } else {
        // If we didn't get updated feedback back, still update the UI optimistically
        const updatedFeedbackData = feedbackData.map((item) =>
          item.id === selectedFeedback.id
            ? {
                ...item,
                adminResponse: {
                  response: responseText,
                  respondedAt: new Date().toISOString(),
                  respondedBy: 'Admin User', // Will be replaced by the server with actual admin name
                },
              }
            : item,
        );

        setFeedbackData(updatedFeedbackData);
        setSelectedFeedback(null);
        setResponseText('');

        toast({
          title: 'Response submitted',
          description: 'Your response has been submitted successfully.',
        });
      }
    } catch (err) {
      console.error('Error submitting response:', err);
      toast({
        title: 'Error',
        description:
          extractErrorMessage(err) ||
          'Failed to submit response. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Render star rating
  const renderStarRating = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <span key={star}>
            {star <= rating ? (
              <RiStarFill className="text-amber-500" />
            ) : (
              <RiStarLine className="text-amber-500" />
            )}
          </span>
        ))}
      </div>
    );
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex h-[300px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-sm text-muted-foreground">
            Loading feedback data...
          </p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex h-[300px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4 text-center">
          <RiErrorWarningLine className="h-12 w-12 text-destructive" />
          <h2 className="text-xl font-semibold">Failed to load feedback</h2>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative flex-1">
          <RiSearchLine className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search feedback..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <RiFilterLine className="h-4 w-4 text-muted-foreground" />
          <Select value={ratingFilter} onValueChange={setRatingFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Filter by rating" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Ratings</SelectItem>
              <SelectItem value="5">5 Stars</SelectItem>
              <SelectItem value="4">4 Stars</SelectItem>
              <SelectItem value="3">3 Stars</SelectItem>
              <SelectItem value="2">2 Stars</SelectItem>
              <SelectItem value="1">1 Star</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Feedback List */}
      <div className="space-y-4">
        {filteredFeedback.length > 0 ? (
          filteredFeedback.map((feedback) => (
            <Card key={feedback.id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex flex-col gap-4 md:flex-row">
                  <div className="flex-shrink-0 md:w-48">
                    <div className="mb-2 flex items-center gap-2">
                      {feedback.userAvatar ? (
                        <img
                          src={feedback.userAvatar}
                          alt={feedback.userName}
                          className="h-10 w-10 rounded-full"
                        />
                      ) : (
                        <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                          <RiUserLine className="h-5 w-5 text-primary" />
                        </div>
                      )}
                      <div>
                        <div className="font-medium">{feedback.userName}</div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(feedback.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    {renderStarRating(feedback.rating)}
                  </div>

                  <div className="flex-1">
                    <div className="mb-4">
                      <p className="whitespace-pre-wrap">{feedback.comment}</p>
                    </div>

                    {feedback.adminResponse && (
                      <div className="mb-4 rounded-md bg-muted p-4">
                        <div className="mb-2 flex items-start justify-between">
                          <div className="font-medium">Admin Response</div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(
                              feedback.adminResponse.respondedAt,
                            ).toLocaleDateString()}
                          </div>
                        </div>
                        <p>{feedback.adminResponse.response}</p>
                      </div>
                    )}

                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={() => handleRespondToFeedback(feedback)}
                      >
                        <RiMessage2Line />
                        {feedback.adminResponse ? 'Edit Response' : 'Respond'}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="py-12 text-center">
            <RiMessage2Line className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">No feedback found</h3>
            <p className="mt-2 text-muted-foreground">
              {searchQuery || ratingFilter !== 'all'
                ? 'Try adjusting your search filters to find feedback.'
                : 'There is no feedback for this challenge yet.'}
            </p>
          </div>
        )}
      </div>

      {/* Response Dialog */}
      {selectedFeedback && (
        <Card className="border-primary">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RiMessage2Line />
              Responding to {selectedFeedback.userName}'s Feedback
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="rounded-md bg-muted p-4">
              <div className="mb-2 flex items-center gap-2">
                <div className="font-medium">{selectedFeedback.userName}</div>
                {renderStarRating(selectedFeedback.rating)}
              </div>
              <p>{selectedFeedback.comment}</p>
            </div>

            <div>
              <label
                htmlFor="response"
                className="mb-2 block text-sm font-medium"
              >
                Your Response
              </label>
              <Textarea
                id="response"
                placeholder="Type your response here..."
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                rows={4}
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setSelectedFeedback(null)}
              >
                Cancel
              </Button>
              <Button
                className="flex items-center gap-1"
                onClick={handleSubmitResponse}
                disabled={!responseText.trim()}
              >
                <RiSendPlane2Line />
                Send Response
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default ChallengeFeedback;
