/**
 * @file ChallengeTags.tsx
 * @description Component to manage tags for a challenge
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiAddLine,
  RiCloseLine,
  RiErrorWarningLine,
  RiSaveLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { useAxiosGet } from '@/hooks/useAxios';
import axios from 'axios';

interface IChallengeTagsProps {
  challengeId: string;
  initialTags?: string[];
  onTagsUpdate?: (tags: string[]) => void;
}

function ChallengeTags({
  challengeId,
  initialTags = [],
  onTagsUpdate,
}: IChallengeTagsProps) {
  const [tags, setTags] = useState<string[]>(initialTags);
  const [newTag, setNewTag] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Load tags if not provided initially
  useEffect(() => {
    if (initialTags.length === 0) {
      fetchTags();
    }
  }, [challengeId, initialTags]);

  // Update local tags when initialTags change
  useEffect(() => {
    setTags(initialTags);
  }, [initialTags]);

  // Fetch tags from API
  const fetchTags = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call when backend is ready
      // const { data, error } = await useAxiosGet(`/api/challenges/${challengeId}/tags`);
      // if (data) {
      //   setTags(data.tags);
      // }
      // if (error) {
      //   setError(error);
      // }

      // Simulating API call for development
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Sample data for development
      const sampleTags = ['responsive', 'css', 'html', 'frontend', 'ui-design'];
      setTags(sampleTags);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching challenge tags:', err);
      setError('Failed to load tags. Please try again.');
      setLoading(false);
    }
  };

  // Add a new tag
  const handleAddTag = () => {
    if (!newTag.trim()) return;

    // Check if tag already exists
    if (tags.includes(newTag.trim().toLowerCase())) {
      toast({
        title: 'Tag already exists',
        description: 'This tag is already added to the challenge.',
        variant: 'destructive',
      });
      return;
    }

    // Add new tag
    const updatedTags = [...tags, newTag.trim().toLowerCase()];
    setTags(updatedTags);
    setNewTag('');

    // Notify parent component if callback provided
    if (onTagsUpdate) {
      onTagsUpdate(updatedTags);
    }
  };

  // Remove a tag
  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(updatedTags);

    // Notify parent component if callback provided
    if (onTagsUpdate) {
      onTagsUpdate(updatedTags);
    }
  };

  // Save tags to API
  const handleSaveTags = async () => {
    setIsSaving(true);
    try {
      // TODO: Replace with actual API call when backend is ready
      // await axios.put(`/api/challenges/${challengeId}/tags`, { tags });

      // Simulating API call for development
      await new Promise((resolve) => setTimeout(resolve, 500));

      toast({
        title: 'Tags saved',
        description: 'Challenge tags have been updated successfully.',
      });
      setIsSaving(false);
    } catch (err) {
      console.error('Error saving challenge tags:', err);
      toast({
        title: 'Error',
        description: 'Failed to save tags. Please try again.',
        variant: 'destructive',
      });
      setIsSaving(false);
    }
  };

  // Handle key press in input field
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Show loading state
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Tags</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[100px] w-full items-center justify-center">
            <div className="flex flex-col items-center gap-2">
              <div className="h-6 w-6 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              <p className="text-sm text-muted-foreground">Loading tags...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Tags</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[100px] w-full items-center justify-center">
            <div className="flex flex-col items-center gap-2 text-center">
              <RiErrorWarningLine className="h-8 w-8 text-destructive" />
              <p className="text-sm text-muted-foreground">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tags</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder="Add a tag..."
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            onKeyDown={handleKeyPress}
            className="flex-1"
          />
          <Button
            onClick={handleAddTag}
            disabled={!newTag.trim()}
            size="sm"
            className="flex items-center gap-1"
          >
            <RiAddLine />
            Add
          </Button>
        </div>

        <div className="flex flex-wrap gap-2">
          {tags.length > 0 ? (
            tags.map((tag) => (
              <Badge
                key={tag}
                variant="secondary"
                className="flex items-center gap-1"
              >
                {tag}
                <button
                  onClick={() => handleRemoveTag(tag)}
                  className="ml-1 rounded-full p-0.5 hover:bg-destructive/20"
                >
                  <RiCloseLine className="h-3.5 w-3.5" />
                  <span className="sr-only">Remove {tag}</span>
                </button>
              </Badge>
            ))
          ) : (
            <p className="text-sm text-muted-foreground">
              No tags added yet. Tags help users find this challenge more
              easily.
            </p>
          )}
        </div>

        {/* Only show save button if onTagsUpdate is not provided (standalone mode) */}
        {!onTagsUpdate && (
          <div className="flex justify-end">
            <Button
              onClick={handleSaveTags}
              disabled={isSaving}
              className="flex items-center gap-1"
            >
              {isSaving ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
              ) : (
                <RiSaveLine />
              )}
              {isSaving ? 'Saving...' : 'Save Tags'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default ChallengeTags;
