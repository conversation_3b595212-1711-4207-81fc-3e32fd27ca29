/**
 * @file ChallengeStructure.tsx
 * @description Component to display the structure and content of a challenge
 */
'use client';

import { useState } from 'react';
import {
  RiCodeLine,
  RiTestTubeLine,
  RiLightbulbLine,
  RiFileCopyLine,
  RiExternalLinkLine,
  RiArrowDownSLine,
  RiArrowUpSLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Define interfaces
interface ITestCase {
  input: string;
  expectedOutput: string;
  isHidden: boolean;
}

interface IChallenge {
  id: string;
  title: string;
  content?: string;
  testCases?: ITestCase[];
  hints?: string[];
  solutionUrl?: string;
  type: string;
  difficulty: string;
  estimatedMinutes: number;
}

interface IChallengeStructureProps {
  challengeId: string;
  challenge: IChallenge;
}

function ChallengeStructure({
  challengeId,
  challenge,
}: IChallengeStructureProps) {
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({
    content: true,
    testCases: false,
    hints: false,
    solution: false,
  });

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  return (
    <div className="space-y-6">
      {/* Challenge Content */}
      <Card>
        <CardHeader
          className="flex cursor-pointer flex-row items-center justify-between px-6 py-4"
          onClick={() => toggleSection('content')}
        >
          <div className="flex items-center gap-2">
            <RiCodeLine className="text-primary" />
            <CardTitle className="text-lg">Challenge Content</CardTitle>
          </div>
          <Button variant="ghost" size="icon">
            {expandedSections.content ? (
              <RiArrowUpSLine />
            ) : (
              <RiArrowDownSLine />
            )}
          </Button>
        </CardHeader>
        {expandedSections.content && (
          <CardContent className="px-6 pb-6">
            <div className="mb-4 flex items-center gap-2">
              <Badge>{challenge.type}</Badge>
              <Badge>{challenge.difficulty}</Badge>
              <Badge variant="outline">
                {challenge.estimatedMinutes} minutes
              </Badge>
            </div>
            <div className="whitespace-pre-wrap rounded-md bg-muted p-4">
              {challenge.content || 'No content available for this challenge.'}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Test Cases */}
      <Card>
        <CardHeader
          className="flex cursor-pointer flex-row items-center justify-between px-6 py-4"
          onClick={() => toggleSection('testCases')}
        >
          <div className="flex items-center gap-2">
            <RiTestTubeLine className="text-primary" />
            <CardTitle className="text-lg">Test Cases</CardTitle>
          </div>
          <Button variant="ghost" size="icon">
            {expandedSections.testCases ? (
              <RiArrowUpSLine />
            ) : (
              <RiArrowDownSLine />
            )}
          </Button>
        </CardHeader>
        {expandedSections.testCases && (
          <CardContent className="px-6 pb-6">
            {challenge.testCases && challenge.testCases.length > 0 ? (
              <div className="space-y-4">
                {challenge.testCases.map((testCase, index) => (
                  <div key={index} className="rounded-md border p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <h3 className="font-medium">Test Case #{index + 1}</h3>
                      {testCase.isHidden && (
                        <Badge variant="outline" className="text-xs">
                          Hidden from users
                        </Badge>
                      )}
                    </div>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <p className="mb-1 text-sm text-muted-foreground">
                          Input:
                        </p>
                        <div className="rounded-md bg-muted p-3 text-sm">
                          {testCase.input}
                        </div>
                      </div>
                      <div>
                        <p className="mb-1 text-sm text-muted-foreground">
                          Expected Output:
                        </p>
                        <div className="rounded-md bg-muted p-3 text-sm">
                          {testCase.expectedOutput}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">
                No test cases available for this challenge.
              </p>
            )}
          </CardContent>
        )}
      </Card>

      {/* Hints */}
      <Card>
        <CardHeader
          className="flex cursor-pointer flex-row items-center justify-between px-6 py-4"
          onClick={() => toggleSection('hints')}
        >
          <div className="flex items-center gap-2">
            <RiLightbulbLine className="text-primary" />
            <CardTitle className="text-lg">Hints</CardTitle>
          </div>
          <Button variant="ghost" size="icon">
            {expandedSections.hints ? <RiArrowUpSLine /> : <RiArrowDownSLine />}
          </Button>
        </CardHeader>
        {expandedSections.hints && (
          <CardContent className="px-6 pb-6">
            {challenge.hints && challenge.hints.length > 0 ? (
              <div className="space-y-3">
                {challenge.hints.map((hint, index) => (
                  <div key={index} className="rounded-md bg-muted p-4">
                    <div className="mb-1 flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        Hint {index + 1}
                      </Badge>
                    </div>
                    <p>{hint}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">
                No hints available for this challenge.
              </p>
            )}
          </CardContent>
        )}
      </Card>

      {/* Solution */}
      <Card>
        <CardHeader
          className="flex cursor-pointer flex-row items-center justify-between px-6 py-4"
          onClick={() => toggleSection('solution')}
        >
          <div className="flex items-center gap-2">
            <RiFileCopyLine className="text-primary" />
            <CardTitle className="text-lg">Solution</CardTitle>
          </div>
          <Button variant="ghost" size="icon">
            {expandedSections.solution ? (
              <RiArrowUpSLine />
            ) : (
              <RiArrowDownSLine />
            )}
          </Button>
        </CardHeader>
        {expandedSections.solution && (
          <CardContent className="px-6 pb-6">
            {challenge.solutionUrl ? (
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Reference solution is available at the following URL:
                </p>
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  asChild
                >
                  <a
                    href={challenge.solutionUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <RiExternalLinkLine /> View Solution
                  </a>
                </Button>
              </div>
            ) : (
              <p className="text-muted-foreground">
                No solution available for this challenge.
              </p>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  );
}

export default ChallengeStructure;
