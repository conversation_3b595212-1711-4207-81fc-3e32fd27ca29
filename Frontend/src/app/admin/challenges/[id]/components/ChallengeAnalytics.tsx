/**
 * @file ChallengeAnalytics.tsx
 * @description Component to display analytics data for a challenge
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiBarChartLine,
  RiUserLine,
  RiTimeLine,
  RiCheckboxCircleLine,
  RiErrorWarningLine,
  RiCalendarLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { fetchChallengeAnalytics } from '@/services/challengeService';
import { safelyExtractData } from '@/utils/apiUtils';
import { extractErrorMessage } from '@/utils/errorUtils';

interface IChallengeAnalyticsProps {
  challengeId: string;
}

// Analytics data structure matches the API response
interface IChallengeAnalytics {
  totalAttempts: number;
  completionRate: number;
  averageAttempts: number;
  successRate: number;
  averageTimeSpent: number; // minutes
  attemptsOverTime: Array<{
    date: string;
    attempts: number;
  }>;
  completionsByDifficulty: Record<string, number>;
  userDemographics: {
    experienceLevel: Record<string, number>;
    topCountries: Array<{
      country: string;
      percentage: number;
    }>;
  };
  commonErrors: Array<{
    error: string;
    count: number;
  }>;
  completionTime: Record<string, number>;
}

function ChallengeAnalytics({ challengeId }: IChallengeAnalyticsProps) {
  const [timeRange, setTimeRange] = useState('30days');
  const [analyticsData, setAnalyticsData] =
    useState<IChallengeAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch analytics data using API
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Call the API to fetch challenge analytics
        const response = await fetchChallengeAnalytics(challengeId, timeRange);

        // Extract analytics data from the response
        const analyticsData = safelyExtractData<IChallengeAnalytics, null>(
          response as unknown as Record<string, unknown>,
          'data.analytics',
          null,
        );

        if (analyticsData) {
          setAnalyticsData(analyticsData);
          setLoading(false);
        } else {
          setError('No analytics data available for this challenge');
          setLoading(false);
        }
      } catch (err) {
        console.error('Error fetching challenge analytics:', err);
        setError(
          extractErrorMessage(err) ||
            'Failed to load analytics data. Please try again.',
        );
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [challengeId, timeRange]);

  // Show loading state
  if (loading) {
    return (
      <div className="flex h-[300px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-sm text-muted-foreground">
            Loading analytics data...
          </p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !analyticsData) {
    return (
      <div className="flex h-[300px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4 text-center">
          <RiErrorWarningLine className="h-12 w-12 text-destructive" />
          <h2 className="text-xl font-semibold">Failed to load analytics</h2>
          <p className="text-muted-foreground">
            {error || 'Analytics data not available'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-end">
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <div className="flex items-center">
              <RiCalendarLine className="mr-2" />
              <SelectValue placeholder="Select time range" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="90days">Last 90 days</SelectItem>
            <SelectItem value="year">Last year</SelectItem>
            <SelectItem value="all">All time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Attempts
                </p>
                <h3 className="text-2xl font-bold">
                  {analyticsData.totalAttempts}
                </h3>
              </div>
              <RiUserLine className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Completion Rate
                </p>
                <h3 className="text-2xl font-bold">
                  {analyticsData.completionRate}%
                </h3>
              </div>
              <RiCheckboxCircleLine className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Avg. Attempts
                </p>
                <h3 className="text-2xl font-bold">
                  {analyticsData.averageAttempts.toFixed(1)}
                </h3>
              </div>
              <RiBarChartLine className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Avg. Time Spent
                </p>
                <h3 className="text-2xl font-bold">
                  {analyticsData.averageTimeSpent} min
                </h3>
              </div>
              <RiTimeLine className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 md:w-[400px]">
          <TabsTrigger value="overview" className="flex items-center gap-1">
            <RiBarChartLine className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="demographics" className="flex items-center gap-1">
            <RiUserLine className="h-4 w-4" />
            Demographics
          </TabsTrigger>
          <TabsTrigger value="errors" className="flex items-center gap-1">
            <RiErrorWarningLine className="h-4 w-4" />
            Common Errors
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6 space-y-6">
          {/* Attempts Over Time */}
          <Card>
            <CardHeader>
              <CardTitle>Attempts Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] w-full">
                {/* TODO: Implement chart using a charting library in the future */}
                <div className="flex h-[250px] w-full items-end justify-between gap-1">
                  {analyticsData.attemptsOverTime
                    .slice(-10)
                    .map((data, index) => (
                      <div
                        key={index}
                        className="group flex flex-1 flex-col items-center"
                      >
                        <div className="relative flex w-full flex-1 items-end">
                          <div
                            className="bg-primary/80 w-full rounded-t-sm transition-all hover:bg-primary"
                            style={{
                              height: `${(data.attempts / Math.max(...analyticsData.attemptsOverTime.map((d) => d.attempts))) * 100}%`,
                            }}
                          >
                            <div className="absolute bottom-full left-1/2 mb-1 hidden -translate-x-1/2 rounded bg-background p-1 text-xs shadow group-hover:block">
                              {data.attempts} attempts
                            </div>
                          </div>
                        </div>
                        <div className="mt-2 w-full truncate text-center text-xs text-muted-foreground">
                          {new Date(data.date).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                          })}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Completion by Difficulty */}
          <Card>
            <CardHeader>
              <CardTitle>Completion Rate by Difficulty</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(analyticsData.completionsByDifficulty).map(
                  ([difficulty, value], index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span>{difficulty}</span>
                        <span className="text-sm text-muted-foreground">
                          {value}%
                        </span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div
                          className="h-2 rounded-full bg-primary"
                          style={{ width: `${value}%` }}
                        ></div>
                      </div>
                    </div>
                  ),
                )}
              </div>
            </CardContent>
          </Card>

          {/* Completion Time Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Completion Time Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-5 gap-2">
                {Object.entries(analyticsData.completionTime).map(
                  ([timeRange, value], index) => {
                    const label = timeRange
                      .replace('under', '< ')
                      .replace('over', '> ')
                      .replace('to', '-')
                      .replace('min', ' min');

                    return (
                      <div key={index} className="flex flex-col items-center">
                        <div className="flex h-[100px] w-full flex-1 items-end">
                          <div
                            className="bg-primary/80 w-full rounded-t-sm transition-all hover:bg-primary"
                            style={{ height: `${value}%` }}
                          ></div>
                        </div>
                        <div className="mt-2 text-center text-xs font-medium">
                          {label}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {value}%
                        </div>
                      </div>
                    );
                  },
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="demographics" className="mt-6 space-y-6">
          {/* User Experience Level */}
          <Card>
            <CardHeader>
              <CardTitle>User Experience Level</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] w-full">
                {/* TODO: Implement chart using a charting library */}
                <div className="grid h-full grid-cols-3 gap-4">
                  {Object.entries(
                    analyticsData.userDemographics.experienceLevel,
                  ).map(([key, value], index) => {
                    return (
                      <div key={index} className="flex flex-col items-center">
                        <div className="flex w-full flex-1 items-end">
                          <div
                            className="bg-primary/80 w-full rounded-t-sm transition-all hover:bg-primary"
                            style={{ height: `${value}%` }}
                          ></div>
                        </div>
                        <div className="mt-2 text-sm font-medium">{key}</div>
                        <div className="text-sm text-muted-foreground">
                          {value}%
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Countries */}
          <Card>
            <CardHeader>
              <CardTitle>Top Countries</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.userDemographics.topCountries.map(
                  (country, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span>{country.country}</span>
                        <span className="text-sm text-muted-foreground">
                          {country.percentage}%
                        </span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div
                          className="h-2 rounded-full bg-primary"
                          style={{ width: `${country.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ),
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Common Errors</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.commonErrors.map((error, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span>{error.error}</span>
                      <span className="text-sm text-muted-foreground">
                        {error.count} occurrences
                      </span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div
                        className="h-2 rounded-full bg-primary"
                        style={{
                          width: `${(error.count / analyticsData.commonErrors[0].count) * 100}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ChallengeAnalytics;
