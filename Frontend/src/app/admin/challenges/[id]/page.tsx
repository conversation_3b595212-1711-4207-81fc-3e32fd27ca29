/**
 * @file page.tsx
 * @description Challenge detail page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiBarChartLine,
  RiMessage2Line,
  RiFileTextLine,
  RiErrorWarningLine,
  RiArrowLeftLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { extractErrorMessage, safelyExtractData } from '@/utils/apiUtils';
import {
  fetchChallengeById,
  updateChallengeFeatureStatus,
  IChallenge,
} from '@/services/challengeService';

// Components
import ChallengeStructure from './components/ChallengeStructure';
import ChallengeAnalytics from './components/ChallengeAnalytics';
import ChallengeFeedback from './components/ChallengeFeedback';
import ChallengeTags from './components/ChallengeTags';
import ChallengeSidebar from './components/ChallengeSidebar';

// Using IChallenge from challengeService.ts

function ChallengeDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('structure');
  const [challenge, setChallenge] = useState<IChallenge | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch challenge data using API
  useEffect(() => {
    const loadChallengeData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetchChallengeById(params.id);
        const challengeData = safelyExtractData<IChallenge, null>(
          response as unknown as Record<string, unknown>,
          'data.challenge',
          null,
        );

        if (challengeData) {
          setChallenge(challengeData);
        } else {
          setError('Failed to load challenge data. Please try again.');
        }
      } catch (err) {
        console.error('Error fetching challenge:', err);
        setError(
          extractErrorMessage(
            err as Record<string, unknown>,
            'Failed to load challenge data. Please try again.',
          ),
        );
      } finally {
        setLoading(false);
      }
    };

    loadChallengeData();
  }, [params.id]);

  // Handle feature toggle
  const handleFeatureToggle = async () => {
    if (!challenge) return;

    try {
      const newStatus = !challenge.isFeatured;
      const response = await updateChallengeFeatureStatus(
        challenge.id,
        newStatus,
      );
      const updatedChallenge = safelyExtractData<IChallenge, null>(
        response as unknown as Record<string, unknown>,
        'data.challenge',
        null,
      );

      if (updatedChallenge) {
        setChallenge(updatedChallenge);
      } else {
        setChallenge((prev) =>
          prev ? { ...prev, isFeatured: newStatus } : null,
        );
      }

      toast({
        title: newStatus ? 'Challenge Featured' : 'Challenge Unfeatured',
        description: `Challenge has been ${newStatus ? 'added to' : 'removed from'} featured section.`,
      });
    } catch (error) {
      console.error('Error toggling feature status:', error);
      toast({
        title: 'Action Failed',
        description: extractErrorMessage(
          error as Record<string, unknown>,
          'Failed to update feature status. Please try again.',
        ),
        variant: 'destructive',
      });
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <h3 className="mt-2 text-lg font-medium">Loading Challenge</h3>
          <p className="text-sm text-muted-foreground">Please wait...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !challenge) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4 text-center">
          <RiErrorWarningLine className="h-16 w-16 text-destructive" />
          <h2 className="text-xl font-semibold">Failed to load challenge</h2>
          <p className="text-muted-foreground">
            {error || 'Challenge not found'}
          </p>
          <Button
            variant="outline"
            onClick={() => router.push('/admin/challenges')}
            className="mt-2"
          >
            <RiArrowLeftLine className="mr-2" />
            Back to Challenges
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-6 lg:flex-row lg:space-x-6 lg:space-y-0">
        {/* Main Content */}
        <div className="flex-1 space-y-6">
          {/* Challenge Header */}
          <Card>
            <CardContent className="p-6">
              <div className="mb-4 flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
                <div>
                  <h1 className="text-2xl font-bold">{challenge.title}</h1>
                  <p className="mt-1 text-muted-foreground">
                    {challenge.description}
                  </p>
                </div>
              </div>

              {/* Tags */}
              <div className="mt-4">
                <ChallengeTags
                  challengeId={challenge.id}
                  initialTags={challenge.tags}
                  onTagsUpdate={(tags) =>
                    setChallenge((prev) => (prev ? { ...prev, tags } : null))
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Tabs for different sections */}
          <Tabs
            defaultValue="structure"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid grid-cols-3 md:w-[400px]">
              <TabsTrigger
                value="structure"
                className="flex items-center gap-1"
              >
                <RiFileTextLine className="h-4 w-4" />
                Structure
              </TabsTrigger>
              <TabsTrigger
                value="analytics"
                className="flex items-center gap-1"
              >
                <RiBarChartLine className="h-4 w-4" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="feedback" className="flex items-center gap-1">
                <RiMessage2Line className="h-4 w-4" />
                Feedback
              </TabsTrigger>
            </TabsList>

            <TabsContent value="structure" className="mt-6">
              <ChallengeStructure
                challengeId={challenge.id}
                challenge={challenge}
              />
            </TabsContent>

            <TabsContent value="analytics" className="mt-6">
              <ChallengeAnalytics challengeId={challenge.id} />
            </TabsContent>

            <TabsContent value="feedback" className="mt-6">
              <ChallengeFeedback challengeId={challenge.id} />
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="lg:w-80">
          <ChallengeSidebar
            challengeId={challenge.id}
            challengeName={challenge.title}
            isFeatured={challenge.isFeatured}
            status={challenge.status}
            createdAt={challenge.createdAt}
            updatedAt={challenge.updatedAt}
            completionRate={challenge.completionRate}
            onFeatureToggle={handleFeatureToggle}
          />
        </div>
      </div>
    </div>
  );
}

export default ChallengeDetailPage;
