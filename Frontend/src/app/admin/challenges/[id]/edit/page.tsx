/**
 * @file page.tsx
 * @description Challenge Editor Page for admin dashboard
 */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiArrowLeftLine,
  RiSaveLine,
  RiInformationLine,
  RiCodeLine,
  RiSettings4Line,
  RiErrorWarningLine,
  RiLoader4Line,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { safelyExtractData } from '@/utils/apiUtils';
import { extractErrorMessage } from '@/utils/errorUtils';
import {
  fetchChallengeById,
  createChallenge,
  updateChallenge,
} from '@/services/challengeService';
import ChallengeBasicInfo from './components/ChallengeBasicInfo';
import ChallengeContentEditor from './components/ChallengeContentEditor';
import ChallengeSettings from './components/ChallengeSettings';

// Use the IChallenge interface from the challenge service
import { IChallenge as IChallengeService } from '@/services/challengeService';

// Define challenge interface with local modifications for the editor
interface IChallenge extends IChallengeService {
  prerequisites?: string[];
  relatedChallenges?: string[];
}

function ChallengeEditorPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const isNewChallenge = params.id === 'new';
  const [activeTab, setActiveTab] = useState('basic-info');
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(!isNewChallenge);
  const [error, setError] = useState<string | null>(null);

  // Default empty challenge for new challenges
  const emptyChallenge: Omit<IChallenge, 'id'> = {
    title: '',
    description: '',
    category: '',
    difficulty: 'Medium',
    type: 'Coding',
    status: 'draft', // lowercase to match API definition
    isFeatured: false,
    estimatedMinutes: 30,
    tags: [],
    content: '',
    testCases: [],
    hints: [],
    prerequisites: [],
    relatedChallenges: [],
    createdAt: '',
    updatedAt: '',
    completionRate: 0,
    averageAttempts: 0,
    successRate: 0,
    totalAttempts: 0,
    authorId: '',
    authorName: '',
  };

  const [challenge, setChallenge] = useState<IChallenge>({
    ...emptyChallenge,
    id: isNewChallenge ? '' : params.id,
  });

  // Fetch challenge data if editing an existing challenge
  useEffect(() => {
    const loadChallengeData = async () => {
      if (isNewChallenge) return;

      try {
        setIsLoading(true);
        setError(null);

        const response = await fetchChallengeById(params.id);
        const challengeData = safelyExtractData<IChallenge, null>(
          response as unknown as Record<string, unknown>,
          'data.challenge',
          null,
        );

        if (challengeData) {
          setChallenge(challengeData);
        } else {
          setError('Failed to load challenge data. Please try again.');
        }
      } catch (err) {
        console.error('Error fetching challenge:', err);
        setError(
          extractErrorMessage(err) ||
            'Failed to load challenge data. Please try again.',
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadChallengeData();
  }, [isNewChallenge, params.id]);

  // Update challenge data from child components
  const updateChallengeBasicInfo = (basicInfo: Partial<IChallenge>) => {
    setChallenge((prev) => ({ ...prev, ...basicInfo }));
  };

  const updateChallengeContent = (content: {
    content?: string;
    testCases?: Array<{
      id: string;
      challenge_id: string;
      input: string;
      expected_output: string;
      is_hidden: boolean;
      name?: string;
    }>;
    hints?: string[];
    solutionUrl?: string;
  }) => {
    setChallenge((prev) => ({ ...prev, ...content }));
  };

  const updateChallengeSettings = (settings: {
    prerequisites?: string[];
    relatedChallenges?: string[];
    estimatedMinutes?: number;
    status?: 'draft' | 'published' | 'archived';
    isFeatured?: boolean;
  }) => {
    setChallenge((prev) => ({ ...prev, ...settings }));
  };

  // Handle save challenge
  const handleSaveChallenge = async () => {
    try {
      setIsSaving(true);

      // Validate required fields
      if (!challenge.title.trim()) {
        toast({
          title: 'Validation Error',
          description: 'Challenge title is required.',
          variant: 'destructive',
        });
        setActiveTab('basic-info');
        setIsSaving(false);
        return;
      }

      if (!challenge.description.trim()) {
        toast({
          title: 'Validation Error',
          description: 'Challenge description is required.',
          variant: 'destructive',
        });
        setActiveTab('basic-info');
        setIsSaving(false);
        return;
      }

      if (!challenge.category.trim()) {
        toast({
          title: 'Validation Error',
          description: 'Challenge category is required.',
          variant: 'destructive',
        });
        setActiveTab('basic-info');
        setIsSaving(false);
        return;
      }

      if (!challenge.content?.trim()) {
        toast({
          title: 'Validation Error',
          description: 'Challenge content is required.',
          variant: 'destructive',
        });
        setActiveTab('content');
        setIsSaving(false);
        return;
      }

      // Prepare challenge data for API
      const preparedChallenge = {
        ...challenge,
        // Format test cases to ensure they have all required fields
        testCases: challenge.testCases?.map((testCase) => {
          // If it's a new test case (has a temp id), remove the id so backend can generate one
          if (testCase.id && testCase.id.startsWith('temp-')) {
            const { id, ...rest } = testCase;
            return {
              ...rest,
              challenge_id: challenge.id, // This will be ignored for new challenges
            };
          }
          return testCase;
        }),
      };

      // Remove read-only fields before sending to API
      const {
        id: challengeId,
        createdAt,
        updatedAt,
        authorId,
        authorName,
        completionRate,
        averageAttempts,
        successRate,
        totalAttempts,
        ...apiChallenge
      } = preparedChallenge;

      let response;
      if (isNewChallenge) {
        // Create new challenge
        response = await createChallenge(apiChallenge);
      } else {
        // Update existing challenge with the ID
        response = await updateChallenge(challenge.id, apiChallenge);
      }

      // Extract challenge data from response
      const savedChallenge = safelyExtractData<IChallenge, null>(
        response as unknown as Record<string, unknown>,
        'data.challenge',
        null,
      );

      if (savedChallenge) {
        toast({
          title: isNewChallenge ? 'Challenge Created' : 'Challenge Updated',
          description: isNewChallenge
            ? 'New challenge has been created successfully.'
            : 'Challenge has been updated successfully.',
        });

        // Redirect to challenge detail page
        router.push(`/admin/challenges/${savedChallenge.id}`);
      } else {
        throw new Error('Failed to save challenge data');
      }
    } catch (err) {
      console.error('Error saving challenge:', err);
      toast({
        title: 'Error',
        description:
          extractErrorMessage(err) ||
          'An error occurred while saving the challenge. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <h3 className="mt-2 text-lg font-medium">Loading Challenge</h3>
          <p className="text-sm text-muted-foreground">Please wait...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex h-[400px] w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4 text-center">
          <RiErrorWarningLine className="h-16 w-16 text-destructive" />
          <h2 className="text-xl font-semibold">Failed to load challenge</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button
            variant="outline"
            onClick={() => router.push('/admin/challenges')}
            className="mt-2"
          >
            <RiArrowLeftLine className="mr-2" />
            Back to Challenges
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with back button and save button */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() =>
              router.push(
                isNewChallenge
                  ? '/admin/challenges'
                  : `/admin/challenges/${params.id}`,
              )
            }
            aria-label="Back"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {isNewChallenge ? 'Create New Challenge' : 'Edit Challenge'}
            </h1>
            {!isNewChallenge && (
              <p className="text-muted-foreground">Challenge ID: {params.id}</p>
            )}
          </div>
        </div>

        <Button
          className="flex items-center gap-1"
          onClick={handleSaveChallenge}
          disabled={isSaving}
        >
          {isSaving ? (
            <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <RiSaveLine className="mr-2 h-4 w-4" />
          )}
          {isSaving ? 'Saving...' : 'Save Challenge'}
        </Button>
      </div>

      {/* Editor Tabs */}
      <Tabs
        defaultValue="basic-info"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid grid-cols-3 md:w-[400px]">
          <TabsTrigger value="basic-info" className="flex items-center gap-1">
            <RiInformationLine className="h-4 w-4" />
            Basic Info
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center gap-1">
            <RiCodeLine className="h-4 w-4" />
            Content
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-1">
            <RiSettings4Line className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic-info" className="mt-6">
          <ChallengeBasicInfo
            challenge={challenge}
            updateChallenge={updateChallengeBasicInfo}
          />
        </TabsContent>

        <TabsContent value="content" className="mt-6">
          <ChallengeContentEditor
            challenge={challenge}
            updateChallenge={updateChallengeContent}
          />
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <ChallengeSettings
            challenge={challenge}
            updateChallenge={updateChallengeSettings}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ChallengeEditorPage;
