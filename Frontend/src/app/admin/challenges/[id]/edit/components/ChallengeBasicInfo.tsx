/**
 * @file ChallengeBasicInfo.tsx
 * @description Component for editing basic information of a challenge
 */
'use client';

import { useState, useEffect } from 'react';
import { RiAddLine, RiCloseLine } from 'react-icons/ri';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { IChallenge } from '@/services/challengeService';

interface IChallengeBasicInfoProps {
  challenge: Pick<
    IChallenge,
    'title' | 'description' | 'category' | 'difficulty' | 'type' | 'tags'
  >;
  updateChallenge: (
    data: Partial<
      Pick<
        IChallenge,
        'title' | 'description' | 'category' | 'difficulty' | 'type' | 'tags'
      >
    >,
  ) => void;
}

function ChallengeBasicInfo({
  challenge,
  updateChallenge,
}: IChallengeBasicInfoProps) {
  const [title, setTitle] = useState(challenge.title);
  const [description, setDescription] = useState(challenge.description);
  const [category, setCategory] = useState(challenge.category);
  const [difficulty, setDifficulty] = useState(challenge.difficulty);
  const [type, setType] = useState(challenge.type);
  const [tags, setTags] = useState<string[]>(challenge.tags);
  const [newTag, setNewTag] = useState('');

  // Update parent component when form values change
  useEffect(() => {
    updateChallenge({
      title,
      description,
      category,
      difficulty,
      type,
      tags,
    });
  }, [title, description, category, difficulty, type, tags, updateChallenge]);

  // Handle adding a new tag
  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim().toLowerCase())) {
      setTags([...tags, newTag.trim().toLowerCase()]);
      setNewTag('');
    }
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  // Handle key press in tag input
  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // TODO: Replace with actual API call to fetch categories
  const categories = [
    'Frontend',
    'Backend',
    'Data Structures',
    'Algorithms',
    'Database',
    'DevOps',
    'Mobile',
    'Machine Learning',
    'Security',
    'Cloud',
    'Networking',
    'Testing',
  ];

  return (
    <Card>
      <CardContent className="space-y-6 p-6">
        {/* Title */}
        <div className="space-y-2">
          <Label htmlFor="title">Challenge Title</Label>
          <Input
            id="title"
            placeholder="Enter challenge title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="Enter a brief description of the challenge"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={4}
          />
        </div>

        {/* Category */}
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger id="category">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((cat) => (
                <SelectItem key={cat} value={cat}>
                  {cat}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Difficulty */}
        <div className="space-y-2">
          <Label htmlFor="difficulty">Difficulty</Label>
          <Select
            value={difficulty}
            onValueChange={(value: 'Easy' | 'Medium' | 'Hard' | 'Expert') =>
              setDifficulty(value)
            }
          >
            <SelectTrigger id="difficulty">
              <SelectValue placeholder="Select difficulty" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Easy">Easy</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="Hard">Hard</SelectItem>
              <SelectItem value="Expert">Expert</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Type */}
        <div className="space-y-2">
          <Label htmlFor="type">Challenge Type</Label>
          <Select
            value={type}
            onValueChange={(
              value: 'Coding' | 'Quiz' | 'Project' | 'Algorithm',
            ) => setType(value)}
          >
            <SelectTrigger id="type">
              <SelectValue placeholder="Select challenge type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Coding">Coding</SelectItem>
              <SelectItem value="Quiz">Quiz</SelectItem>
              <SelectItem value="Project">Project</SelectItem>
              <SelectItem value="Algorithm">Algorithm</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <Label htmlFor="tags">Tags</Label>
          <div className="flex gap-2">
            <Input
              id="tags"
              placeholder="Add a tag"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={handleTagKeyPress}
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleAddTag}
              className="flex items-center gap-1"
            >
              <RiAddLine />
              Add
            </Button>
          </div>

          {tags.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-2">
              {tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="flex items-center gap-1 px-2 py-1"
                >
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <RiCloseLine className="h-4 w-4" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default ChallengeBasicInfo;
