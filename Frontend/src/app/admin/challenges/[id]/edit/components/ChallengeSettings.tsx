/**
 * @file ChallengeSettings.tsx
 * @description Component for configuring additional settings for a challenge
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiAddLine,
  RiCloseLine,
  RiTimeLine,
  RiStarLine,
  RiStarFill,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { IChallenge } from '@/services/challengeService';

interface IChallengeSettingsProps {
  challenge: Pick<IChallenge, 'estimatedMinutes' | 'status' | 'isFeatured'> & {
    prerequisites?: string[];
    relatedChallenges?: string[];
  };
  updateChallenge: (
    data: Partial<
      Pick<IChallenge, 'estimatedMinutes' | 'status' | 'isFeatured'>
    > & {
      prerequisites?: string[];
      relatedChallenges?: string[];
    },
  ) => void;
}

function ChallengeSettings({
  challenge,
  updateChallenge,
}: IChallengeSettingsProps) {
  const [prerequisites, setPrerequisites] = useState<string[]>(
    challenge.prerequisites || [],
  );
  const [newPrerequisite, setNewPrerequisite] = useState('');

  const [relatedChallenges, setRelatedChallenges] = useState<string[]>(
    challenge.relatedChallenges || [],
  );
  const [newRelatedChallenge, setNewRelatedChallenge] = useState('');

  const [estimatedMinutes, setEstimatedMinutes] = useState(
    challenge.estimatedMinutes,
  );
  const [status, setStatus] = useState(challenge.status);
  const [isFeatured, setIsFeatured] = useState(challenge.isFeatured);

  // Update parent component when form values change
  useEffect(() => {
    updateChallenge({
      prerequisites,
      relatedChallenges,
      estimatedMinutes,
      status,
      isFeatured,
    });
  }, [
    prerequisites,
    relatedChallenges,
    estimatedMinutes,
    status,
    isFeatured,
    updateChallenge,
  ]);

  // Handle adding a new prerequisite
  const handleAddPrerequisite = () => {
    if (
      newPrerequisite.trim() &&
      !prerequisites.includes(newPrerequisite.trim())
    ) {
      setPrerequisites([...prerequisites, newPrerequisite.trim()]);
      setNewPrerequisite('');
    }
  };

  // Handle removing a prerequisite
  const handleRemovePrerequisite = (prerequisiteToRemove: string) => {
    setPrerequisites(
      prerequisites.filter(
        (prerequisite) => prerequisite !== prerequisiteToRemove,
      ),
    );
  };

  // Handle key press in prerequisite input
  const handlePrerequisiteKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddPrerequisite();
    }
  };

  // Handle adding a new related challenge
  const handleAddRelatedChallenge = () => {
    if (
      newRelatedChallenge.trim() &&
      !relatedChallenges.includes(newRelatedChallenge.trim())
    ) {
      setRelatedChallenges([...relatedChallenges, newRelatedChallenge.trim()]);
      setNewRelatedChallenge('');
    }
  };

  // Handle removing a related challenge
  const handleRemoveRelatedChallenge = (challengeToRemove: string) => {
    setRelatedChallenges(
      relatedChallenges.filter((challenge) => challenge !== challengeToRemove),
    );
  };

  // Handle key press in related challenge input
  const handleRelatedChallengeKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddRelatedChallenge();
    }
  };

  return (
    <div className="space-y-6">
      {/* Prerequisites */}
      <Card>
        <CardHeader>
          <CardTitle>Prerequisites</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="prerequisites">Add Prerequisites</Label>
            <div className="flex gap-2">
              <Input
                id="prerequisites"
                placeholder="Enter a prerequisite"
                value={newPrerequisite}
                onChange={(e) => setNewPrerequisite(e.target.value)}
                onKeyPress={handlePrerequisiteKeyPress}
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleAddPrerequisite}
                className="flex items-center gap-1"
              >
                <RiAddLine />
                Add
              </Button>
            </div>

            {prerequisites.length > 0 && (
              <div className="mt-3 flex flex-wrap gap-2">
                {prerequisites.map((prerequisite) => (
                  <Badge
                    key={prerequisite}
                    variant="secondary"
                    className="flex items-center gap-1 px-2 py-1"
                  >
                    {prerequisite}
                    <button
                      type="button"
                      onClick={() => handleRemovePrerequisite(prerequisite)}
                      className="ml-1 text-muted-foreground hover:text-foreground"
                    >
                      <RiCloseLine className="h-4 w-4" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Related Challenges */}
      <Card>
        <CardHeader>
          <CardTitle>Related Challenges</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="related-challenges">Add Related Challenges</Label>
            <div className="flex gap-2">
              <Input
                id="related-challenges"
                placeholder="Enter a related challenge"
                value={newRelatedChallenge}
                onChange={(e) => setNewRelatedChallenge(e.target.value)}
                onKeyPress={handleRelatedChallengeKeyPress}
              />
              <Button
                type="button"
                variant="outline"
                onClick={handleAddRelatedChallenge}
                className="flex items-center gap-1"
              >
                <RiAddLine />
                Add
              </Button>
            </div>

            {relatedChallenges.length > 0 && (
              <div className="mt-3 flex flex-wrap gap-2">
                {relatedChallenges.map((relatedChallenge) => (
                  <Badge
                    key={relatedChallenge}
                    variant="secondary"
                    className="flex items-center gap-1 px-2 py-1"
                  >
                    {relatedChallenge}
                    <button
                      type="button"
                      onClick={() =>
                        handleRemoveRelatedChallenge(relatedChallenge)
                      }
                      className="ml-1 text-muted-foreground hover:text-foreground"
                    >
                      <RiCloseLine className="h-4 w-4" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Additional Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Estimated Time */}
          <div className="space-y-2">
            <Label htmlFor="estimated-time" className="flex items-center gap-1">
              <RiTimeLine />
              Estimated Completion Time (minutes)
            </Label>
            <Input
              id="estimated-time"
              type="number"
              min="1"
              value={estimatedMinutes}
              onChange={(e) =>
                setEstimatedMinutes(parseInt(e.target.value) || 30)
              }
              className="max-w-[200px]"
            />
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={status}
              onValueChange={(value: 'draft' | 'published' | 'archived') =>
                setStatus(value)
              }
            >
              <SelectTrigger id="status" className="max-w-[200px]">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Featured */}
          <div className="flex items-center space-x-2">
            <Switch
              id="featured"
              checked={isFeatured}
              onCheckedChange={setIsFeatured}
            />
            <Label htmlFor="featured" className="flex items-center gap-1">
              {isFeatured ? (
                <RiStarFill className="text-amber-500" />
              ) : (
                <RiStarLine />
              )}
              Feature this challenge
            </Label>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ChallengeSettings;
