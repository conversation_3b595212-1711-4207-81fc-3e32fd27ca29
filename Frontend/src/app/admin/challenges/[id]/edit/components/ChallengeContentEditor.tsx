/**
 * @file ChallengeContentEditor.tsx
 * @description Component for editing the content, test cases, and hints of a challenge
 */
'use client';

import { useState, useEffect } from 'react';
import {
  RiAddLine,
  RiDeleteBinLine,
  RiEyeLine,
  RiEyeOffLine,
} from 'react-icons/ri';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { IChallenge, ITestCase } from '@/services/challengeService';

// ITestCase is now imported from challengeService

interface IChallengeContentEditorProps {
  challenge: Pick<
    IChallenge,
    'content' | 'testCases' | 'hints' | 'solutionUrl'
  >;
  updateChallenge: (
    data: Partial<
      Pick<IChallenge, 'content' | 'testCases' | 'hints' | 'solutionUrl'>
    >,
  ) => void;
}

function ChallengeContentEditor({
  challenge,
  updateChallenge,
}: IChallengeContentEditorProps) {
  const [content, setContent] = useState(challenge.content || '');
  const [testCases, setTestCases] = useState<ITestCase[]>(
    challenge.testCases || [],
  );
  const [hints, setHints] = useState<string[]>(challenge.hints || []);
  const [solutionUrl, setSolutionUrl] = useState(challenge.solutionUrl || '');

  // Update parent component when form values change
  useEffect(() => {
    updateChallenge({
      content,
      testCases,
      hints,
      solutionUrl,
    });
  }, [content, testCases, hints, solutionUrl, updateChallenge]);

  // Handle adding a new test case
  const handleAddTestCase = () => {
    setTestCases([
      ...testCases,
      {
        id: `temp-${Date.now()}`, // Temporary ID until saved to backend
        challenge_id: '', // Will be set by backend
        input: '',
        expected_output: '',
        is_hidden: false,
        name: `Test Case ${testCases.length + 1}`,
      },
    ]);
  };

  // Handle updating a test case
  const handleUpdateTestCase = (
    index: number,
    field: keyof ITestCase,
    value: string | boolean,
  ) => {
    const updatedTestCases = [...testCases];
    updatedTestCases[index] = { ...updatedTestCases[index], [field]: value };
    setTestCases(updatedTestCases);
  };

  // Handle removing a test case
  const handleRemoveTestCase = (index: number) => {
    setTestCases(testCases.filter((_, i) => i !== index));
  };

  // Handle adding a new hint
  const handleAddHint = () => {
    setHints([...hints, '']);
  };

  // Handle updating a hint
  const handleUpdateHint = (index: number, value: string) => {
    const updatedHints = [...hints];
    updatedHints[index] = value;
    setHints(updatedHints);
  };

  // Handle removing a hint
  const handleRemoveHint = (index: number) => {
    setHints(hints.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-6">
      {/* Challenge Content */}
      <Card>
        <CardHeader>
          <CardTitle>Challenge Content</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              placeholder="Enter the detailed challenge content and instructions"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={8}
            />
          </div>
        </CardContent>
      </Card>

      {/* Test Cases */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Test Cases</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={handleAddTestCase}
          >
            <RiAddLine />
            Add Test Case
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {testCases.length > 0 ? (
            testCases.map((testCase, index) => (
              <div key={index} className="space-y-4 rounded-md border p-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Test Case #{index + 1}</h3>
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Switch
                        id={`hidden-${index}`}
                        checked={testCase.is_hidden}
                        onCheckedChange={(checked) =>
                          handleUpdateTestCase(index, 'is_hidden', checked)
                        }
                      />
                      <Label
                        htmlFor={`hidden-${index}`}
                        className="cursor-pointer"
                      >
                        {testCase.is_hidden ? (
                          <span className="flex items-center gap-1">
                            <RiEyeOffLine className="h-4 w-4" />
                            Hidden
                          </span>
                        ) : (
                          <span className="flex items-center gap-1">
                            <RiEyeLine className="h-4 w-4" />
                            Visible
                          </span>
                        )}
                      </Label>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive hover:bg-destructive/10 hover:text-destructive"
                      onClick={() => handleRemoveTestCase(index)}
                    >
                      <RiDeleteBinLine className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`input-${index}`}>Input</Label>
                    <Textarea
                      id={`input-${index}`}
                      placeholder="Enter test case input"
                      value={testCase.input}
                      onChange={(e) =>
                        handleUpdateTestCase(index, 'input', e.target.value)
                      }
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`output-${index}`}>Expected Output</Label>
                    <Textarea
                      id={`output-${index}`}
                      placeholder="Enter expected output"
                      value={testCase.expected_output}
                      onChange={(e) =>
                        handleUpdateTestCase(
                          index,
                          'expected_output',
                          e.target.value,
                        )
                      }
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="py-4 text-center text-muted-foreground">
              No test cases added yet. Click &quot;Add Test Case&quot; to create
              one.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Hints */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Hints</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={handleAddHint}
          >
            <RiAddLine />
            Add Hint
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {hints.length > 0 ? (
            hints.map((hint, index) => (
              <div key={index} className="flex items-start gap-2">
                <div className="flex-1">
                  <Label htmlFor={`hint-${index}`} className="sr-only">
                    Hint {index + 1}
                  </Label>
                  <Textarea
                    id={`hint-${index}`}
                    placeholder={`Enter hint #${index + 1}`}
                    value={hint}
                    onChange={(e) => handleUpdateHint(index, e.target.value)}
                    rows={2}
                  />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="mt-1 text-destructive hover:bg-destructive/10 hover:text-destructive"
                  onClick={() => handleRemoveHint(index)}
                >
                  <RiDeleteBinLine className="h-4 w-4" />
                </Button>
              </div>
            ))
          ) : (
            <div className="py-4 text-center text-muted-foreground">
              No hints added yet. Click &quot;Add Hint&quot; to create one.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Solution URL */}
      <Card>
        <CardHeader>
          <CardTitle>Solution Reference</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="solution-url">Solution URL (Optional)</Label>
            <Input
              id="solution-url"
              placeholder="Enter URL to reference solution"
              value={solutionUrl}
              onChange={(e) => setSolutionUrl(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ChallengeContentEditor;
