/**
 * @file page.tsx
 * @description User-Generated Content Review for admin dashboard
 */

'use client';

import { useState, useEffect } from 'react';
import {
  RiArrowLeftLine,
  RiRefreshLine,
  RiSettings3Line,
  RiCheckLine,
  RiCloseLine,
  RiFilterLine,
  RiRoadMapLine,
  RiCodeBoxLine,
  RiEyeLine,
  RiSendPlaneLine,
  RiSearchLine,
  RiTimeLine,
  RiUserLine,
} from 'react-icons/ri';
import { useRouter } from 'next/navigation';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface IContentSubmission {
  id: string;
  title: string;
  description: string;
  type: 'roadmap' | 'challenge';
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  submittedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  rejectionReason?: string;
  reviewNotes?: string;
}

interface IContentFilters {
  type: string;
  status: string;
  category: string;
  difficulty: string;
  searchTerm: string;
}

function UserGeneratedContentReviewPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('pending');
  const [submissions, setSubmissions] = useState<IContentSubmission[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<
    IContentSubmission[]
  >([]);
  const [filters, setFilters] = useState<IContentFilters>({
    type: 'all',
    status: 'pending',
    category: 'all',
    difficulty: 'all',
    searchTerm: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSubmission, setSelectedSubmission] =
    useState<IContentSubmission | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [reviewNotes, setReviewNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [isRejectionDialogOpen, setIsRejectionDialogOpen] = useState(false);

  // Mock data for content submissions
  const mockSubmissions: IContentSubmission[] = [
    {
      id: 'submission_1',
      title: 'Full Stack Development with MERN',
      description:
        'A comprehensive roadmap for becoming a MERN stack developer, covering MongoDB, Express, React, and Node.js.',
      type: 'roadmap',
      category: 'web development',
      difficulty: 'intermediate',
      author: {
        id: 'user_1',
        name: 'John Doe',
        avatar: 'https://ui-avatars.com/api/?name=John+Doe',
      },
      submittedAt: '2025-05-23T14:30:00Z',
      status: 'pending',
    },
    {
      id: 'submission_2',
      title: 'Binary Search Tree Implementation',
      description:
        'A challenge to implement a binary search tree with insertion, deletion, and traversal operations.',
      type: 'challenge',
      category: 'data structures',
      difficulty: 'intermediate',
      author: {
        id: 'user_2',
        name: 'Jane Smith',
        avatar: 'https://ui-avatars.com/api/?name=Jane+Smith',
      },
      submittedAt: '2025-05-22T10:15:00Z',
      status: 'pending',
    },
    {
      id: 'submission_3',
      title: 'Machine Learning Engineer Path',
      description:
        'A roadmap covering the essential skills, tools, and concepts needed to become a machine learning engineer.',
      type: 'roadmap',
      category: 'machine learning',
      difficulty: 'advanced',
      author: {
        id: 'user_3',
        name: 'Alex Johnson',
      },
      submittedAt: '2025-05-21T16:45:00Z',
      status: 'approved',
      reviewNotes:
        'Well-structured roadmap with clear progression. Approved with minor edits to the ML Ops section.',
    },
    {
      id: 'submission_4',
      title: 'Parallel Processing Simulation',
      description:
        'A challenge to implement a parallel processing simulator that handles task scheduling and resource allocation.',
      type: 'challenge',
      category: 'system design',
      difficulty: 'expert',
      author: {
        id: 'user_4',
        name: 'Michael Brown',
        avatar: 'https://ui-avatars.com/api/?name=Michael+Brown',
      },
      submittedAt: '2025-05-20T09:20:00Z',
      status: 'rejected',
      rejectionReason:
        'The challenge is too complex for our platform. Please break it down into smaller, more focused challenges.',
    },
    {
      id: 'submission_5',
      title: 'iOS Development with Swift',
      description:
        'A roadmap for becoming an iOS developer using Swift, covering UI design, networking, and app deployment.',
      type: 'roadmap',
      category: 'mobile development',
      difficulty: 'intermediate',
      author: {
        id: 'user_5',
        name: 'Sarah Wilson',
      },
      submittedAt: '2025-05-19T12:00:00Z',
      status: 'approved',
      reviewNotes:
        'Excellent roadmap with practical projects at each stage. Approved without changes.',
    },
  ];

  // Simulate fetching submissions from API
  useEffect(() => {
    const fetchSubmissions = async () => {
      setIsLoading(true);
      // In a real app, this would be an API call
      setTimeout(() => {
        setSubmissions(mockSubmissions);
        setIsLoading(false);
      }, 1000);
    };

    fetchSubmissions();
  }, []);

  // Filter submissions based on current filters and active tab
  useEffect(() => {
    let filtered = [...submissions];

    // Filter by tab (status)
    if (activeTab !== 'all') {
      filtered = filtered.filter(
        (submission) => submission.status === activeTab,
      );
    }

    // Apply additional filters
    if (filters.type !== 'all') {
      filtered = filtered.filter(
        (submission) => submission.type === filters.type,
      );
    }

    if (filters.category !== 'all') {
      filtered = filtered.filter(
        (submission) => submission.category === filters.category,
      );
    }

    if (filters.difficulty !== 'all') {
      filtered = filtered.filter(
        (submission) => submission.difficulty === filters.difficulty,
      );
    }

    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (submission) =>
          submission.title.toLowerCase().includes(searchLower) ||
          submission.description.toLowerCase().includes(searchLower) ||
          submission.author.name.toLowerCase().includes(searchLower),
      );
    }

    // Sort by date (newest first)
    filtered.sort(
      (a, b) =>
        new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime(),
    );

    setFilteredSubmissions(filtered);
  }, [submissions, filters, activeTab]);

  // Handle filter changes
  const handleFilterChange = (key: keyof IContentFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  // Open review dialog
  const handleOpenReview = (submission: IContentSubmission) => {
    setSelectedSubmission(submission);
    setReviewNotes(submission.reviewNotes || '');
    setIsReviewDialogOpen(true);
  };

  // Open rejection dialog
  const handleOpenRejection = (submission: IContentSubmission) => {
    setSelectedSubmission(submission);
    setRejectionReason(submission.rejectionReason || '');
    setIsRejectionDialogOpen(true);
  };

  // Handle approving a submission
  const handleApproveSubmission = () => {
    if (!selectedSubmission) return;

    setSubmissions((prev) =>
      prev.map((submission) =>
        submission.id === selectedSubmission.id
          ? { ...submission, status: 'approved', reviewNotes }
          : submission,
      ),
    );

    setIsReviewDialogOpen(false);
    setSelectedSubmission(null);
    setReviewNotes('');
  };

  // Handle rejecting a submission
  const handleRejectSubmission = () => {
    if (!selectedSubmission) return;

    setSubmissions((prev) =>
      prev.map((submission) =>
        submission.id === selectedSubmission.id
          ? { ...submission, status: 'rejected', rejectionReason }
          : submission,
      ),
    );

    setIsRejectionDialogOpen(false);
    setSelectedSubmission(null);
    setRejectionReason('');
  };

  // Get counts for each tab
  const getCounts = () => {
    return {
      all: submissions.length,
      pending: submissions.filter(
        (submission) => submission.status === 'pending',
      ).length,
      approved: submissions.filter(
        (submission) => submission.status === 'approved',
      ).length,
      rejected: submissions.filter(
        (submission) => submission.status === 'rejected',
      ).length,
    };
  };

  const counts = getCounts();

  // Get difficulty badge color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-blue-100 text-blue-800';
      case 'advanced':
        return 'bg-amber-100 text-amber-800';
      case 'expert':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/admin/moderation')}
            className="h-8 w-8"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">Content Review</h2>
        </div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => setActiveTab('pending')}
          >
            <RiRefreshLine className="h-4 w-4" />
            Refresh
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => router.push('/admin/moderation/content/guidelines')}
          >
            <RiSettings3Line className="h-4 w-4" />
            Guidelines
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pending" className="flex items-center gap-1">
            Pending
            <Badge variant="secondary" className="ml-1">
              {counts.pending}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="approved" className="flex items-center gap-1">
            Approved
            <Badge variant="secondary" className="ml-1">
              {counts.approved}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="rejected" className="flex items-center gap-1">
            Rejected
            <Badge variant="secondary" className="ml-1">
              {counts.rejected}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="all" className="flex items-center gap-1">
            All
            <Badge variant="secondary" className="ml-1">
              {counts.all}
            </Badge>
          </TabsTrigger>
        </TabsList>

        {/* Tab Content */}
        {['pending', 'approved', 'rejected', 'all'].map((tab) => (
          <TabsContent key={tab} value={tab} className="mt-6 space-y-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                  <div>
                    <CardTitle className="capitalize">
                      {tab} Submissions
                    </CardTitle>
                    <CardDescription>
                      {tab === 'pending' &&
                        'User-generated content awaiting review'}
                      {tab === 'approved' && 'Content that has been approved'}
                      {tab === 'rejected' && 'Content that has been rejected'}
                      {tab === 'all' &&
                        'All user-generated content submissions'}
                    </CardDescription>
                  </div>

                  {/* Filters */}
                  <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                    <div className="relative flex-1">
                      <RiSearchLine className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search submissions..."
                        className="pl-8"
                        value={filters.searchTerm}
                        onChange={(e) =>
                          handleFilterChange('searchTerm', e.target.value)
                        }
                      />
                    </div>
                    <Select
                      value={filters.type}
                      onValueChange={(value) =>
                        handleFilterChange('type', value)
                      }
                    >
                      <SelectTrigger className="w-[130px]">
                        <SelectValue placeholder="Content Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="roadmap">Roadmaps</SelectItem>
                        <SelectItem value="challenge">Challenges</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select
                      value={filters.difficulty}
                      onValueChange={(value) =>
                        handleFilterChange('difficulty', value)
                      }
                    >
                      <SelectTrigger className="w-[130px]">
                        <SelectValue placeholder="Difficulty" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Levels</SelectItem>
                        <SelectItem value="beginner">Beginner</SelectItem>
                        <SelectItem value="intermediate">
                          Intermediate
                        </SelectItem>
                        <SelectItem value="advanced">Advanced</SelectItem>
                        <SelectItem value="expert">Expert</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  // Loading state
                  <div className="flex h-40 items-center justify-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  </div>
                ) : filteredSubmissions.length > 0 ? (
                  // Submissions list
                  <div className="space-y-4">
                    {filteredSubmissions.map((submission) => (
                      <div
                        key={submission.id}
                        className="rounded-lg border p-4"
                      >
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
                          <div className="flex-1 space-y-2">
                            {/* Title and badges */}
                            <div className="flex flex-wrap items-center gap-2">
                              <h3 className="text-lg font-medium">
                                {submission.title}
                              </h3>
                              <Badge
                                variant={
                                  submission.type === 'roadmap'
                                    ? 'default'
                                    : 'secondary'
                                }
                                className="capitalize"
                              >
                                {submission.type}
                              </Badge>
                              <div
                                className={`rounded-full px-2 py-0.5 text-xs capitalize ${getDifficultyColor(submission.difficulty)}`}
                              >
                                {submission.difficulty}
                              </div>
                            </div>

                            {/* Description */}
                            <p className="text-sm text-muted-foreground">
                              {submission.description}
                            </p>

                            {/* Metadata */}
                            <div className="flex flex-wrap items-center gap-3 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <RiUserLine className="h-3 w-3" />
                                {submission.author.name}
                              </div>
                              <div className="flex items-center gap-1">
                                <RiTimeLine className="h-3 w-3" />
                                {formatDate(submission.submittedAt)}
                              </div>
                              <div className="flex items-center gap-1 capitalize">
                                <RiFilterLine className="h-3 w-3" />
                                {submission.category}
                              </div>
                            </div>

                            {/* Review notes or rejection reason */}
                            {submission.status === 'approved' &&
                              submission.reviewNotes && (
                                <div className="rounded-md bg-green-50 p-2 text-xs text-green-800">
                                  <strong>Review notes:</strong>{' '}
                                  {submission.reviewNotes}
                                </div>
                              )}

                            {submission.status === 'rejected' &&
                              submission.rejectionReason && (
                                <div className="bg-red-50 text-red-800 rounded-md p-2 text-xs">
                                  <strong>Rejection reason:</strong>{' '}
                                  {submission.rejectionReason}
                                </div>
                              )}
                          </div>

                          {/* Action buttons */}
                          <div className="flex flex-row gap-2 sm:flex-col">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-1"
                              onClick={() =>
                                router.push(
                                  `/admin/moderation/content/${submission.id}`,
                                )
                              }
                            >
                              <RiEyeLine className="h-4 w-4" />
                              View
                            </Button>

                            {submission.status === 'pending' && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-1 text-destructive hover:bg-destructive/10 hover:text-destructive"
                                  onClick={() =>
                                    handleOpenRejection(submission)
                                  }
                                >
                                  <RiCloseLine className="h-4 w-4" />
                                  Reject
                                </Button>
                                <Button
                                  size="sm"
                                  className="flex items-center gap-1"
                                  onClick={() => handleOpenReview(submission)}
                                >
                                  <RiCheckLine className="h-4 w-4" />
                                  Approve
                                </Button>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  // Empty state
                  <div className="flex h-40 flex-col items-center justify-center rounded-md border border-dashed">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                      {tab === 'pending' && (
                        <RiFilterLine className="h-5 w-5 text-muted-foreground" />
                      )}
                      {tab === 'approved' && (
                        <RiCheckLine className="h-5 w-5 text-muted-foreground" />
                      )}
                      {tab === 'rejected' && (
                        <RiCloseLine className="h-5 w-5 text-muted-foreground" />
                      )}
                      {tab === 'all' && (
                        <RiFilterLine className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>
                    <h3 className="mt-2 text-lg font-medium">
                      No submissions found
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {tab === 'pending' &&
                        'There are no pending submissions to review'}
                      {tab === 'approved' &&
                        'There are no approved submissions'}
                      {tab === 'rejected' &&
                        'There are no rejected submissions'}
                      {tab === 'all' &&
                        'There are no submissions matching your filters'}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Approval Dialog */}
      <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Approve Submission</DialogTitle>
            <DialogDescription>
              {selectedSubmission?.type === 'roadmap'
                ? 'Review and approve this roadmap for publication.'
                : 'Review and approve this challenge for publication.'}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="mb-4">
              <h3 className="text-sm font-medium">
                {selectedSubmission?.title}
              </h3>
              <p className="text-xs text-muted-foreground">
                Submitted by {selectedSubmission?.author.name}
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="review-notes">Review Notes (Optional)</Label>
              <Textarea
                id="review-notes"
                placeholder="Add any notes about your review..."
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground">
                These notes will be visible to administrators but not to the
                content creator.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsReviewDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleApproveSubmission}>
              Approve Submission
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog
        open={isRejectionDialogOpen}
        onOpenChange={setIsRejectionDialogOpen}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Reject Submission</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this submission. This will
              be sent to the creator.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="mb-4">
              <h3 className="text-sm font-medium">
                {selectedSubmission?.title}
              </h3>
              <p className="text-xs text-muted-foreground">
                Submitted by {selectedSubmission?.author.name}
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="rejection-reason">Rejection Reason</Label>
              <Textarea
                id="rejection-reason"
                placeholder="Explain why this submission is being rejected..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground">
                Be specific and constructive to help the creator improve their
                submission.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRejectionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejectSubmission}
              disabled={!rejectionReason.trim()}
            >
              Reject Submission
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default UserGeneratedContentReviewPage;
