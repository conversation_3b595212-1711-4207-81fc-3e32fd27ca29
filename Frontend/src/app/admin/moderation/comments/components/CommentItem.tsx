/**
 * @file CommentItem.tsx
 * @description Individual comment item with moderation actions
 */

'use client';

import { useState } from 'react';
import {
  RiCheckLine,
  RiCloseLine,
  RiFlagLine,
  RiUserLine,
  RiTimeLine,
  RiAlertLine,
  RiEyeLine,
  RiMoreLine,
} from 'react-icons/ri';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ICommentItemProps {
  comment: IComment;
  onApprove: (id: string) => void;
  onReject: (id: string, reason?: string) => void;
  onFlag: (id: string, flagged: boolean) => void;
  onView: (id: string) => void;
}

interface IComment {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  contentType: string;
  contentId: string;
  contentTitle: string;
  createdAt: string;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  flagged: boolean;
  flagReason?: string;
  rejectionReason?: string;
  flaggedKeywords?: string[];
}

function CommentItem({
  comment,
  onApprove,
  onReject,
  onFlag,
  onView,
}: ICommentItemProps) {
  const [isRejectionDialogOpen, setIsRejectionDialogOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');

  // Handle approve action
  const handleApprove = () => {
    onApprove(comment.id);
  };

  // Open rejection dialog
  const openRejectionDialog = () => {
    setIsRejectionDialogOpen(true);
  };

  // Handle reject action
  const handleReject = () => {
    onReject(comment.id, rejectionReason);
    setIsRejectionDialogOpen(false);
    setRejectionReason('');
  };

  // Handle flag action
  const handleFlag = () => {
    onFlag(comment.id, !comment.flagged);
  };

  // Handle view content action
  const handleViewContent = () => {
    onView(comment.contentId);
  };

  // Get status badge variant based on comment status
  const getStatusBadgeVariant = (
    status: string,
  ): 'default' | 'destructive' | 'outline' | 'secondary' => {
    switch (status) {
      case 'approved':
        return 'outline'; // Using outline instead of success which isn't a valid variant
      case 'rejected':
        return 'destructive';
      case 'flagged':
        return 'secondary'; // Using secondary instead of warning which isn't a valid variant
      default:
        return 'secondary';
    }
  };

  // Format content type for display
  const formatContentType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return (
    <div className="space-y-2 rounded-lg border p-4">
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3">
          {/* User avatar */}
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
            {comment.author.avatar ? (
              <img
                src={comment.author.avatar}
                alt={comment.author.name}
                className="h-10 w-10 rounded-full object-cover"
              />
            ) : (
              <RiUserLine className="h-5 w-5 text-muted-foreground" />
            )}
          </div>

          {/* Comment metadata */}
          <div>
            <div className="flex flex-wrap items-center gap-2">
              <span className="font-medium">{comment.author.name}</span>
              <Badge
                variant={getStatusBadgeVariant(comment.status)}
                className="capitalize"
              >
                {comment.status}
              </Badge>
              {comment.flagged && (
                <Badge
                  variant="secondary"
                  className="flex items-center gap-1 bg-muted text-muted-foreground"
                >
                  <RiFlagLine className="h-3 w-3" />
                  Flagged
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <RiTimeLine className="h-3 w-3" />
                {formatDistanceToNow(new Date(comment.createdAt), {
                  addSuffix: true,
                })}
              </span>
              <span>•</span>
              <span className="flex items-center gap-1">
                {formatContentType(comment.contentType)}: {comment.contentTitle}
              </span>
            </div>
          </div>
        </div>

        {/* Action dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <RiMoreLine className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleViewContent}>
              <RiEyeLine className="mr-2 h-4 w-4" />
              View Content
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleFlag}>
              <RiFlagLine className="mr-2 h-4 w-4" />
              {comment.flagged ? 'Remove Flag' : 'Flag Comment'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Comment content */}
      <div className="rounded-md bg-muted/50 p-3">
        <p className="text-sm">{comment.content}</p>

        {/* Flagged keywords */}
        {comment.flaggedKeywords && comment.flaggedKeywords.length > 0 && (
          <div className="mt-2 flex flex-wrap items-center gap-1 border-t border-dashed border-muted-foreground/20 pt-2">
            <span className="text-warning-foreground flex items-center gap-1 text-xs">
              <RiAlertLine className="h-3 w-3" />
              Flagged keywords:
            </span>
            {comment.flaggedKeywords.map((keyword, index) => (
              <Badge
                key={index}
                variant="outline"
                className="text-warning-foreground text-xs"
              >
                {keyword}
              </Badge>
            ))}
          </div>
        )}
      </div>

      {/* Rejection reason (if rejected) */}
      {comment.status === 'rejected' && comment.rejectionReason && (
        <div className="rounded-md bg-destructive/10 p-2 text-xs text-destructive-foreground">
          <strong>Rejection reason:</strong> {comment.rejectionReason}
        </div>
      )}

      {/* Action buttons */}
      {comment.status === 'pending' && (
        <div className="flex items-center justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={openRejectionDialog}
            className="flex items-center gap-1"
          >
            <RiCloseLine className="h-4 w-4" />
            Reject
          </Button>
          <Button
            size="sm"
            onClick={handleApprove}
            className="flex items-center gap-1"
          >
            <RiCheckLine className="h-4 w-4" />
            Approve
          </Button>
        </div>
      )}

      {/* Rejection Dialog */}
      <Dialog
        open={isRejectionDialogOpen}
        onOpenChange={setIsRejectionDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Comment</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this comment. This will be
              visible to the user.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Enter rejection reason..."
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRejectionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleReject}>
              Reject Comment
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default CommentItem;
