/**
 * @file KeywordFlaggingSystem.tsx
 * @description Component for managing keyword flagging system
 */

'use client';

import { useState, useEffect } from 'react';
import {
  RiAddLine,
  RiCloseLine,
  RiAlertLine,
  RiSaveLine,
  RiDeleteBinLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface IKeywordCategory {
  id: string;
  name: string;
  severity: 'low' | 'medium' | 'high';
  autoReject: boolean;
  keywords: string[];
}

interface IKeywordFlaggingSystemProps {
  onSave: (categories: IKeywordCategory[]) => void;
}

function KeywordFlaggingSystem({ onSave }: IKeywordFlaggingSystemProps) {
  // Mock initial categories - in a real app, these would come from an API
  const initialCategories: IKeywordCategory[] = [
    {
      id: 'profanity',
      name: 'Profanity',
      severity: 'medium',
      autoReject: true,
      keywords: ['example_word_1', 'example_word_2', 'example_word_3'],
    },
    {
      id: 'harassment',
      name: 'Harassment',
      severity: 'high',
      autoReject: true,
      keywords: ['example_word_4', 'example_word_5'],
    },
    {
      id: 'spam',
      name: 'Spam',
      severity: 'low',
      autoReject: false,
      keywords: ['example_word_6', 'example_word_7', 'example_word_8'],
    },
  ];

  const [categories, setCategories] =
    useState<IKeywordCategory[]>(initialCategories);
  const [newCategory, setNewCategory] = useState<IKeywordCategory>({
    id: '',
    name: '',
    severity: 'medium',
    autoReject: false,
    keywords: [],
  });
  const [newKeyword, setNewKeyword] = useState<string>('');
  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(
    null,
  );
  const [isAddingCategory, setIsAddingCategory] = useState<boolean>(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);

  // Reset the form when switching between add and edit modes
  useEffect(() => {
    if (!isAddingCategory && !editingCategoryId) {
      setNewCategory({
        id: '',
        name: '',
        severity: 'medium',
        autoReject: false,
        keywords: [],
      });
      setNewKeyword('');
    }
  }, [isAddingCategory, editingCategoryId]);

  // Load category data for editing
  useEffect(() => {
    if (editingCategoryId) {
      const categoryToEdit = categories.find(
        (cat) => cat.id === editingCategoryId,
      );
      if (categoryToEdit) {
        setNewCategory({ ...categoryToEdit });
      }
    }
  }, [editingCategoryId, categories]);

  // Handle adding a new keyword to the current category
  const handleAddKeyword = () => {
    if (!newKeyword.trim()) return;

    // Check if keyword already exists
    if (newCategory.keywords.includes(newKeyword.trim().toLowerCase())) {
      // Show error: Keyword already exists
      return;
    }

    setNewCategory((prev) => ({
      ...prev,
      keywords: [...prev.keywords, newKeyword.trim().toLowerCase()],
    }));
    setNewKeyword('');
    setHasUnsavedChanges(true);
  };

  // Handle removing a keyword from the current category
  const handleRemoveKeyword = (keyword: string) => {
    setNewCategory((prev) => ({
      ...prev,
      keywords: prev.keywords.filter((k) => k !== keyword),
    }));
    setHasUnsavedChanges(true);
  };

  // Handle saving a category (new or edited)
  const handleSaveCategory = () => {
    if (!newCategory.name.trim()) {
      // Show error: Category name is required
      return;
    }

    if (newCategory.keywords.length === 0) {
      // Show error: At least one keyword is required
      return;
    }

    if (isAddingCategory) {
      // Generate a unique ID for the new category
      const newId = `category_${Date.now()}`;
      const categoryToAdd = { ...newCategory, id: newId };
      setCategories((prev) => [...prev, categoryToAdd]);
    } else if (editingCategoryId) {
      // Update existing category
      setCategories((prev) =>
        prev.map((cat) => (cat.id === editingCategoryId ? newCategory : cat)),
      );
    }

    // Reset form and state
    setNewCategory({
      id: '',
      name: '',
      severity: 'medium',
      autoReject: false,
      keywords: [],
    });
    setNewKeyword('');
    setEditingCategoryId(null);
    setIsAddingCategory(false);
    setHasUnsavedChanges(true);
  };

  // Handle deleting a category
  const handleDeleteCategory = (categoryId: string) => {
    setCategories((prev) => prev.filter((cat) => cat.id !== categoryId));

    if (editingCategoryId === categoryId) {
      setEditingCategoryId(null);
      setNewCategory({
        id: '',
        name: '',
        severity: 'medium',
        autoReject: false,
        keywords: [],
      });
    }

    setHasUnsavedChanges(true);
  };

  // Handle canceling the current operation
  const handleCancel = () => {
    setNewCategory({
      id: '',
      name: '',
      severity: 'medium',
      autoReject: false,
      keywords: [],
    });
    setNewKeyword('');
    setEditingCategoryId(null);
    setIsAddingCategory(false);
  };

  // Handle saving all changes
  const handleSaveAll = () => {
    onSave(categories);
    setHasUnsavedChanges(false);
  };

  // Get severity badge color
  // Helper to get the appropriate badge variant based on severity
  const getSeverityColor = (
    severity: string,
  ): 'default' | 'destructive' | 'outline' | 'secondary' => {
    switch (severity) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Keyword Flagging System</h2>
        <Button
          onClick={handleSaveAll}
          disabled={!hasUnsavedChanges}
          className="flex items-center gap-1"
        >
          <RiSaveLine className="h-4 w-4" />
          Save Changes
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Category List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Categories</CardTitle>
              <CardDescription>
                Manage keyword categories for flagging
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {categories.map((category) => (
                <div
                  key={category.id}
                  className={`flex items-center justify-between rounded-md border p-3 ${editingCategoryId === category.id ? 'bg-primary/5 border-primary' : ''}`}
                >
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{category.name}</span>
                      <Badge variant={getSeverityColor(category.severity)}>
                        {category.severity}
                      </Badge>
                    </div>
                    <div className="mt-1 text-xs text-muted-foreground">
                      {category.keywords.length} keywords
                      {category.autoReject && (
                        <span className="ml-2 text-destructive">
                          u2022 Auto-reject
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => {
                        setEditingCategoryId(category.id);
                        setIsAddingCategory(false);
                      }}
                    >
                      <span className="sr-only">Edit</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-4 w-4"
                      >
                        <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                        <path d="m15 5 4 4" />
                      </svg>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-destructive hover:bg-destructive/10 hover:text-destructive"
                      onClick={() => handleDeleteCategory(category.id)}
                    >
                      <span className="sr-only">Delete</span>
                      <RiDeleteBinLine className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}

              {categories.length === 0 && (
                <div className="flex h-20 items-center justify-center rounded-md border border-dashed">
                  <p className="text-sm text-muted-foreground">
                    No categories yet
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => {
                  setIsAddingCategory(true);
                  setEditingCategoryId(null);
                }}
              >
                <RiAddLine className="mr-1 h-4 w-4" />
                Add Category
              </Button>
            </CardFooter>
          </Card>
        </div>

        {/* Category Editor */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>
                {isAddingCategory
                  ? 'Add New Category'
                  : editingCategoryId
                    ? 'Edit Category'
                    : 'Category Details'}
              </CardTitle>
              <CardDescription>
                {isAddingCategory || editingCategoryId
                  ? 'Configure category settings and keywords'
                  : 'Select a category to edit or add a new one'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isAddingCategory || editingCategoryId ? (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="category-name">Category Name</Label>
                    <Input
                      id="category-name"
                      placeholder="Enter category name"
                      value={newCategory.name}
                      onChange={(e) =>
                        setNewCategory({ ...newCategory, name: e.target.value })
                      }
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="severity">Severity Level</Label>
                      <Select
                        value={newCategory.severity}
                        onValueChange={(value: 'low' | 'medium' | 'high') =>
                          setNewCategory({ ...newCategory, severity: value })
                        }
                      >
                        <SelectTrigger id="severity">
                          <SelectValue placeholder="Select severity" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center space-x-2 pt-8">
                      <Switch
                        id="auto-reject"
                        checked={newCategory.autoReject}
                        onCheckedChange={(checked) =>
                          setNewCategory({
                            ...newCategory,
                            autoReject: checked,
                          })
                        }
                      />
                      <Label
                        htmlFor="auto-reject"
                        className="flex items-center gap-1"
                      >
                        <RiAlertLine className="h-4 w-4 text-destructive" />
                        Auto-reject comments
                      </Label>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="keywords">Keywords</Label>
                    <div className="flex gap-2">
                      <Input
                        id="keywords"
                        placeholder="Enter keyword"
                        value={newKeyword}
                        onChange={(e) => setNewKeyword(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddKeyword();
                          }
                        }}
                      />
                      <Button
                        variant="outline"
                        onClick={handleAddKeyword}
                        disabled={!newKeyword.trim()}
                      >
                        <RiAddLine className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label className="mb-2 block">Keyword List</Label>
                    <div className="min-h-[100px] rounded-md border p-3">
                      <div className="flex flex-wrap gap-2">
                        {newCategory.keywords.map((keyword, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="flex items-center gap-1"
                          >
                            {keyword}
                            <button
                              onClick={() => handleRemoveKeyword(keyword)}
                              className="ml-1 rounded-full p-0.5 hover:bg-muted-foreground/20"
                            >
                              <RiCloseLine className="h-3 w-3" />
                              <span className="sr-only">Remove</span>
                            </button>
                          </Badge>
                        ))}

                        {newCategory.keywords.length === 0 && (
                          <p className="text-sm text-muted-foreground">
                            No keywords added yet
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex h-[300px] items-center justify-center rounded-md border border-dashed">
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground">
                      Select a category to edit or add a new one
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
            {(isAddingCategory || editingCategoryId) && (
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button onClick={handleSaveCategory}>
                  {isAddingCategory ? 'Add Category' : 'Save Changes'}
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}

export default KeywordFlaggingSystem;
