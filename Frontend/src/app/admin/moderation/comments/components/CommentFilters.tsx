/**
 * @file CommentFilters.tsx
 * @description Filtering component for comment moderation interface
 */

'use client';

import { useState } from 'react';
import { RiFilterLine, RiCloseLine, RiSearchLine } from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';

interface ICommentFiltersProps {
  onFilterChange: (filters: ICommentFilters) => void;
}

interface ICommentFilters {
  contentType: string;
  status: string;
  dateRange: string;
  searchTerm: string;
  flagged: boolean;
}

function CommentFilters({ onFilterChange }: ICommentFiltersProps) {
  const [filters, setFilters] = useState<ICommentFilters>({
    contentType: 'all',
    status: 'pending',
    dateRange: 'last7days',
    searchTerm: '',
    flagged: false,
  });

  const [activeFiltersCount, setActiveFiltersCount] = useState<number>(2); // Default filters: status and dateRange
  const [isOpen, setIsOpen] = useState<boolean>(false);

  // Handle filter changes
  const handleFilterChange = (
    key: keyof ICommentFilters,
    value: string | boolean,
  ) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // Count active filters (excluding defaults)
    let count = 0;
    if (newFilters.contentType !== 'all') count++;
    if (newFilters.status !== 'all') count++;
    if (newFilters.dateRange !== 'all') count++;
    if (newFilters.searchTerm) count++;
    if (newFilters.flagged) count++;

    setActiveFiltersCount(count);
    onFilterChange(newFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    const defaultFilters = {
      contentType: 'all',
      status: 'all',
      dateRange: 'all',
      searchTerm: '',
      flagged: false,
    };
    setFilters(defaultFilters);
    setActiveFiltersCount(0);
    onFilterChange(defaultFilters);
    setIsOpen(false);
  };

  // Apply filters
  const applyFilters = () => {
    onFilterChange(filters);
    setIsOpen(false);
  };

  return (
    <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
      {/* Search input */}
      <div className="relative flex-1">
        <RiSearchLine className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search comments..."
          className="pl-8"
          value={filters.searchTerm}
          onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
        />
      </div>

      {/* Filter popover */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="flex items-center gap-1">
            <RiFilterLine className="h-4 w-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge
                variant="secondary"
                className="ml-1 h-5 w-5 rounded-full p-0 text-xs"
              >
                {activeFiltersCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="end">
          <div className="space-y-4">
            <h4 className="font-medium">Filter Comments</h4>

            {/* Content Type Filter */}
            <div className="space-y-2">
              <Label htmlFor="content-type">Content Type</Label>
              <Select
                value={filters.contentType}
                onValueChange={(value) =>
                  handleFilterChange('contentType', value)
                }
              >
                <SelectTrigger id="content-type">
                  <SelectValue placeholder="Select content type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Content</SelectItem>
                  <SelectItem value="roadmap">Roadmaps</SelectItem>
                  <SelectItem value="challenge">Challenges</SelectItem>
                  <SelectItem value="resource">Resources</SelectItem>
                  <SelectItem value="battle">Battles</SelectItem>
                  <SelectItem value="forum">Forum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange('status', value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="flagged">Flagged</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Range Filter */}
            <div className="space-y-2">
              <Label htmlFor="date-range">Date Range</Label>
              <Select
                value={filters.dateRange}
                onValueChange={(value) =>
                  handleFilterChange('dateRange', value)
                }
              >
                <SelectTrigger id="date-range">
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="last7days">Last 7 Days</SelectItem>
                  <SelectItem value="last30days">Last 30 Days</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Flagged Filter */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="flagged"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                checked={filters.flagged}
                onChange={(e) =>
                  handleFilterChange('flagged', e.target.checked)
                }
              />
              <Label htmlFor="flagged" className="text-sm font-medium">
                Show only flagged comments
              </Label>
            </div>

            {/* Filter Actions */}
            <div className="flex items-center justify-between pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="h-8 px-2 text-xs"
              >
                <RiCloseLine className="mr-1 h-3 w-3" />
                Clear Filters
              </Button>
              <Button
                size="sm"
                onClick={applyFilters}
                className="h-8 px-3 text-xs"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

export default CommentFilters;
