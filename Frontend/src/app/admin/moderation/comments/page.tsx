/**
 * @file page.tsx
 * @description Comment moderation interface for admin dashboard
 */

'use client';

import { useState, useEffect } from 'react';
import {
  RiArrowLeftLine,
  RiRefreshLine,
  RiSettings3Line,
  RiCheckLine,
  RiCloseLine,
  RiFilterLine,
  RiFlagLine,
} from 'react-icons/ri';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

import CommentFilters from './components/CommentFilters';
import CommentItem from './components/CommentItem';
import KeywordFlaggingSystem from './components/KeywordFlaggingSystem';

interface IComment {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  contentType: string;
  contentId: string;
  contentTitle: string;
  createdAt: string;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  flagged: boolean;
  flagReason?: string;
  rejectionReason?: string;
  flaggedKeywords?: string[];
}

interface ICommentFilters {
  contentType: string;
  status: string;
  dateRange: string;
  searchTerm: string;
  flagged: boolean;
}

interface IKeywordCategory {
  id: string;
  name: string;
  severity: 'low' | 'medium' | 'high';
  autoReject: boolean;
  keywords: string[];
}

function CommentModerationPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('pending');
  const [comments, setComments] = useState<IComment[]>([]);
  const [filteredComments, setFilteredComments] = useState<IComment[]>([]);
  const [filters, setFilters] = useState<ICommentFilters>({
    contentType: 'all',
    status: 'pending',
    dateRange: 'last7days',
    searchTerm: '',
    flagged: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [autoModerationEnabled, setAutoModerationEnabled] = useState(true);

  // Mock data for comments
  const mockComments: IComment[] = [
    {
      id: 'comment_1',
      content:
        'This roadmap was really helpful for learning React. I especially liked the section on hooks and state management.',
      author: {
        id: 'user_1',
        name: 'John Doe',
        avatar: 'https://ui-avatars.com/api/?name=John+Doe',
      },
      contentType: 'roadmap',
      contentId: 'roadmap_1',
      contentTitle: 'React Developer Roadmap',
      createdAt: '2025-05-24T08:30:00Z',
      status: 'pending',
      flagged: false,
    },
    {
      id: 'comment_2',
      content:
        'This challenge was too difficult and the instructions were unclear. I wasted a lot of time trying to understand what was expected.',
      author: {
        id: 'user_2',
        name: 'Jane Smith',
        avatar: 'https://ui-avatars.com/api/?name=Jane+Smith',
      },
      contentType: 'challenge',
      contentId: 'challenge_1',
      contentTitle: 'Advanced Algorithms Challenge',
      createdAt: '2025-05-23T14:15:00Z',
      status: 'pending',
      flagged: true,
      flaggedKeywords: ['difficult', 'unclear', 'wasted'],
    },
    {
      id: 'comment_3',
      content:
        'Great resource! The explanations were clear and concise. I would recommend this to anyone starting with data structures.',
      author: {
        id: 'user_3',
        name: 'Alex Johnson',
      },
      contentType: 'resource',
      contentId: 'resource_1',
      contentTitle: 'Data Structures Fundamentals',
      createdAt: '2025-05-22T11:45:00Z',
      status: 'approved',
      flagged: false,
    },
    {
      id: 'comment_4',
      content:
        'This battle was unfair. The time limit was too short and the problems were much harder than advertised.',
      author: {
        id: 'user_4',
        name: 'Michael Brown',
        avatar: 'https://ui-avatars.com/api/?name=Michael+Brown',
      },
      contentType: 'battle',
      contentId: 'battle_1',
      contentTitle: 'Weekend Code Battle',
      createdAt: '2025-05-21T16:20:00Z',
      status: 'rejected',
      flagged: true,
      flaggedKeywords: ['unfair'],
      rejectionReason:
        'Comment contains negative feedback without constructive suggestions.',
    },
    {
      id: 'comment_5',
      content:
        'The content in this resource is outdated. Many of the techniques shown are no longer best practices in 2025.',
      author: {
        id: 'user_5',
        name: 'Sarah Wilson',
      },
      contentType: 'resource',
      contentId: 'resource_2',
      contentTitle: 'Web Development Guide',
      createdAt: '2025-05-20T09:10:00Z',
      status: 'flagged',
      flagged: true,
      flaggedKeywords: ['outdated'],
    },
    {
      id: 'comment_6',
      content:
        'I found a bug in this challenge. When submitting with certain edge cases, the tests fail even though the solution is correct.',
      author: {
        id: 'user_6',
        name: 'David Lee',
        avatar: 'https://ui-avatars.com/api/?name=David+Lee',
      },
      contentType: 'challenge',
      contentId: 'challenge_2',
      contentTitle: 'String Manipulation Challenge',
      createdAt: '2025-05-19T13:25:00Z',
      status: 'pending',
      flagged: false,
    },
  ];

  // Simulate fetching comments from API
  useEffect(() => {
    const fetchComments = async () => {
      setIsLoading(true);
      // In a real app, this would be an API call
      setTimeout(() => {
        setComments(mockComments);
        setIsLoading(false);
      }, 1000);
    };

    fetchComments();
  }, []);

  // Filter comments based on current filters and active tab
  useEffect(() => {
    let filtered = [...comments];

    // Filter by tab (status)
    if (activeTab !== 'all') {
      filtered = filtered.filter((comment) => comment.status === activeTab);
    }

    // Apply additional filters
    if (filters.contentType !== 'all') {
      filtered = filtered.filter(
        (comment) => comment.contentType === filters.contentType,
      );
    }

    if (filters.status !== 'all') {
      filtered = filtered.filter(
        (comment) => comment.status === filters.status,
      );
    }

    if (filters.flagged) {
      filtered = filtered.filter((comment) => comment.flagged);
    }

    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (comment) =>
          comment.content.toLowerCase().includes(searchLower) ||
          comment.author.name.toLowerCase().includes(searchLower) ||
          comment.contentTitle.toLowerCase().includes(searchLower),
      );
    }

    // Sort by date (newest first)
    filtered.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );

    setFilteredComments(filtered);
  }, [comments, filters, activeTab]);

  // Handle approving a comment
  const handleApproveComment = (commentId: string) => {
    setComments((prev) =>
      prev.map((comment) =>
        comment.id === commentId
          ? { ...comment, status: 'approved', flagged: false }
          : comment,
      ),
    );
  };

  // Handle rejecting a comment
  const handleRejectComment = (commentId: string, reason?: string) => {
    setComments((prev) =>
      prev.map((comment) =>
        comment.id === commentId
          ? { ...comment, status: 'rejected', rejectionReason: reason }
          : comment,
      ),
    );
  };

  // Handle flagging/unflagging a comment
  const handleFlagComment = (commentId: string, flagged: boolean) => {
    setComments((prev) =>
      prev.map((comment) =>
        comment.id === commentId
          ? {
              ...comment,
              flagged,
              status: flagged ? 'flagged' : comment.status,
            }
          : comment,
      ),
    );
  };

  // Handle viewing the content associated with a comment
  const handleViewContent = (contentId: string) => {
    // In a real app, this would navigate to the content page
    console.log('Viewing content:', contentId);
  };

  // Handle saving keyword categories
  const handleSaveKeywordCategories = (categories: IKeywordCategory[]) => {
    // In a real app, this would save to an API
    console.log('Saving keyword categories:', categories);
    // Show success message
  };

  // Get counts for each tab
  const getCounts = () => {
    return {
      all: comments.length,
      pending: comments.filter((comment) => comment.status === 'pending')
        .length,
      approved: comments.filter((comment) => comment.status === 'approved')
        .length,
      rejected: comments.filter((comment) => comment.status === 'rejected')
        .length,
      flagged: comments.filter((comment) => comment.status === 'flagged')
        .length,
    };
  };

  const counts = getCounts();

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/admin/moderation')}
            className="h-8 w-8"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">
            Comment Moderation
          </h2>
        </div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => setActiveTab('pending')}
          >
            <RiRefreshLine className="h-4 w-4" />
            Refresh
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => setActiveTab('settings')}
          >
            <RiSettings3Line className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="pending" className="flex items-center gap-1">
            Pending
            <Badge variant="secondary" className="ml-1">
              {counts.pending}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="approved" className="flex items-center gap-1">
            Approved
            <Badge variant="secondary" className="ml-1">
              {counts.approved}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="rejected" className="flex items-center gap-1">
            Rejected
            <Badge variant="secondary" className="ml-1">
              {counts.rejected}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="flagged" className="flex items-center gap-1">
            Flagged
            <Badge variant="secondary" className="ml-1">
              {counts.flagged}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Comments Tabs Content */}
        {['pending', 'approved', 'rejected', 'flagged', 'all'].map((tab) => (
          <TabsContent key={tab} value={tab} className="mt-6 space-y-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                  <div>
                    <CardTitle className="capitalize">{tab} Comments</CardTitle>
                    <CardDescription>
                      {tab === 'pending' && 'Comments awaiting moderation'}
                      {tab === 'approved' && 'Comments that have been approved'}
                      {tab === 'rejected' && 'Comments that have been rejected'}
                      {tab === 'flagged' &&
                        'Comments that have been flagged for review'}
                      {tab === 'all' && 'All comments across the platform'}
                    </CardDescription>
                  </div>
                  <CommentFilters onFilterChange={setFilters} />
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {isLoading ? (
                  // Loading state
                  <div className="flex h-40 items-center justify-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  </div>
                ) : filteredComments.length > 0 ? (
                  // Comments list
                  <div className="space-y-4">
                    {filteredComments.map((comment) => (
                      <CommentItem
                        key={comment.id}
                        comment={comment}
                        onApprove={handleApproveComment}
                        onReject={handleRejectComment}
                        onFlag={handleFlagComment}
                        onView={handleViewContent}
                      />
                    ))}
                  </div>
                ) : (
                  // Empty state
                  <div className="flex h-40 flex-col items-center justify-center rounded-md border border-dashed">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                      {tab === 'pending' && (
                        <RiFilterLine className="h-5 w-5 text-muted-foreground" />
                      )}
                      {tab === 'approved' && (
                        <RiCheckLine className="h-5 w-5 text-muted-foreground" />
                      )}
                      {tab === 'rejected' && (
                        <RiCloseLine className="h-5 w-5 text-muted-foreground" />
                      )}
                      {tab === 'flagged' && (
                        <RiFlagLine className="h-5 w-5 text-muted-foreground" />
                      )}
                      {tab === 'all' && (
                        <RiFilterLine className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>
                    <h3 className="mt-2 text-lg font-medium">
                      No comments found
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {tab === 'pending' &&
                        'There are no pending comments to moderate'}
                      {tab === 'approved' && 'There are no approved comments'}
                      {tab === 'rejected' && 'There are no rejected comments'}
                      {tab === 'flagged' && 'There are no flagged comments'}
                      {tab === 'all' &&
                        'There are no comments matching your filters'}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}

        {/* Settings Tab Content */}
        <TabsContent value="settings" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Moderation Settings</CardTitle>
              <CardDescription>
                Configure how comments are moderated across the platform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Automatic Moderation</h3>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="auto-moderation"
                    checked={autoModerationEnabled}
                    onCheckedChange={setAutoModerationEnabled}
                  />
                  <Label htmlFor="auto-moderation">
                    Enable automatic moderation
                  </Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  When enabled, comments containing flagged keywords will be
                  automatically flagged or rejected based on severity.
                </p>
              </div>

              <Separator />

              <KeywordFlaggingSystem onSave={handleSaveKeywordCategories} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default CommentModerationPage;
