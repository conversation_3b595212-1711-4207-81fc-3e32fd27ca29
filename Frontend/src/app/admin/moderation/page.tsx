/**
 * @file page.tsx
 * @description Main Moderation Dashboard for admin
 */

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  RiChatCheckLine,
  RiFileCheckLine,
  RiAlertLine,
  RiShieldCheckLine,
  RiTimeLine,
  RiUserLine,
  RiArrowRightLine,
  RiBarChartLine,
} from 'react-icons/ri';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

function ModerationDashboardPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for moderation stats
  const moderationStats = {
    pendingComments: 12,
    pendingContent: 5,
    pendingReports: 8,
    recentViolations: 3,
    totalModeratedToday: 47,
    totalModeratedWeek: 215,
    flaggedKeywords: [
      { keyword: 'example_word_1', count: 15 },
      { keyword: 'example_word_2', count: 8 },
      { keyword: 'example_word_3', count: 6 },
    ],
    recentActions: [
      {
        type: 'comment',
        action: 'approved',
        time: '15 minutes ago',
        user: 'John Doe',
      },
      {
        type: 'report',
        action: 'resolved',
        time: '32 minutes ago',
        user: 'Jane Smith',
      },
      {
        type: 'content',
        action: 'rejected',
        time: '1 hour ago',
        user: 'Alex Johnson',
      },
      {
        type: 'user',
        action: 'warned',
        time: '3 hours ago',
        user: 'Michael Brown',
      },
    ],
  };

  // Navigation cards for moderation sections
  const moderationCards = [
    {
      title: 'Comment Moderation',
      description: 'Review and moderate user comments across the platform',
      icon: <RiChatCheckLine className="h-8 w-8 text-primary" />,
      path: '/admin/moderation/comments',
      count: moderationStats.pendingComments,
      color: 'bg-blue-50 text-blue-700',
    },
    {
      title: 'Content Review',
      description: 'Review user-submitted roadmaps and challenges',
      icon: <RiFileCheckLine className="h-8 w-8 text-primary" />,
      path: '/admin/moderation/content',
      count: moderationStats.pendingContent,
      color: 'bg-green-50 text-green-700',
    },
    {
      title: 'Reported Content',
      description: 'Handle user reports and content violations',
      icon: <RiAlertLine className="h-8 w-8 text-primary" />,
      path: '/admin/moderation/reports',
      count: moderationStats.pendingReports,
      color: 'bg-amber-50 text-amber-700',
    },
    {
      title: 'Guidelines Enforcement',
      description: 'Monitor policy violations and issue warnings',
      icon: <RiShieldCheckLine className="h-8 w-8 text-primary" />,
      path: '/admin/moderation/enforcement',
      count: moderationStats.recentViolations,
      color: 'bg-red-50 text-red-700',
    },
  ];

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h2 className="text-2xl font-bold tracking-tight">
          Moderation Dashboard
        </h2>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="mt-6 space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Pending Comments
                </CardTitle>
                <RiChatCheckLine className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {moderationStats.pendingComments}
                </div>
                <p className="text-xs text-muted-foreground">
                  +{Math.floor(Math.random() * 10)}% from yesterday
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Content Submissions
                </CardTitle>
                <RiFileCheckLine className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {moderationStats.pendingContent}
                </div>
                <p className="text-xs text-muted-foreground">
                  -{Math.floor(Math.random() * 10)}% from yesterday
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  User Reports
                </CardTitle>
                <RiAlertLine className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {moderationStats.pendingReports}
                </div>
                <p className="text-xs text-muted-foreground">
                  +{Math.floor(Math.random() * 15)}% from yesterday
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Moderated Today
                </CardTitle>
                <RiBarChartLine className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {moderationStats.totalModeratedToday}
                </div>
                <p className="text-xs text-muted-foreground">
                  {moderationStats.totalModeratedWeek} this week
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Moderation Sections */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {moderationCards.map((card, index) => (
              <Card key={index} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    {card.icon}
                    {card.count > 0 && (
                      <Badge className={card.color}>{card.count} pending</Badge>
                    )}
                  </div>
                  <CardTitle className="mt-2">{card.title}</CardTitle>
                  <CardDescription>{card.description}</CardDescription>
                </CardHeader>
                <CardFooter className="pt-2">
                  <Button
                    variant="outline"
                    className="w-full justify-between"
                    onClick={() => router.push(card.path)}
                  >
                    <span>View {card.title}</span>
                    <RiArrowRightLine className="h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* Flagged Keywords */}
          <Card>
            <CardHeader>
              <CardTitle>Top Flagged Keywords</CardTitle>
              <CardDescription>
                Most frequently flagged keywords in the last 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {moderationStats.flaggedKeywords.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-sm">{item.keyword}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-24 overflow-hidden rounded-full bg-muted">
                        <div
                          className="h-full bg-primary"
                          style={{
                            width: `${(item.count / moderationStats.flaggedKeywords[0].count) * 100}%`,
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium">{item.count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Moderation Activity</CardTitle>
              <CardDescription>
                Latest actions taken by moderators
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {moderationStats.recentActions.map((action, index) => (
                  <div key={index} className="flex items-start">
                    <div className="flex h-9 w-9 items-center justify-center rounded-full bg-muted">
                      {action.type === 'comment' && (
                        <RiChatCheckLine className="h-4 w-4" />
                      )}
                      {action.type === 'report' && (
                        <RiAlertLine className="h-4 w-4" />
                      )}
                      {action.type === 'content' && (
                        <RiFileCheckLine className="h-4 w-4" />
                      )}
                      {action.type === 'user' && (
                        <RiUserLine className="h-4 w-4" />
                      )}
                    </div>
                    <div className="ml-4 space-y-1">
                      <p className="text-sm font-medium">
                        <span className="capitalize">{action.type}</span>{' '}
                        {action.action}
                      </p>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <RiTimeLine className="mr-1 h-3 w-3" />
                        <span>{action.time}</span>
                        <span className="mx-1">•</span>
                        <span>{action.user}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">
                View All Activity
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Moderation Stats</CardTitle>
              <CardDescription>
                Performance metrics for the moderation team
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <p className="text-sm font-medium">Average Response Time</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-3xl font-bold">27</span>
                    <span className="text-sm text-muted-foreground">
                      minutes
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Resolution Rate</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-3xl font-bold">94%</span>
                    <span className="text-sm text-muted-foreground">
                      same-day
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">Flagged Content</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-3xl font-bold">156</span>
                    <span className="text-sm text-muted-foreground">
                      this month
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">User Warnings</p>
                  <div className="flex items-baseline gap-2">
                    <span className="text-3xl font-bold">32</span>
                    <span className="text-sm text-muted-foreground">
                      this month
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default ModerationDashboardPage;
