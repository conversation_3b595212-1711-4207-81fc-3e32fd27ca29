/**
 * @file EnforcementActions.tsx
 * @description Component for taking enforcement actions against users
 */

'use client';

import { useState } from 'react';
import {
  RiErrorWarningLine,
  RiShieldLine,
  RiCloseLine,
  RiMailLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface IEnforcementActionsProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  userName: string;
  violationId?: string;
  onActionTaken: (action: IEnforcementAction) => void;
}

interface IEnforcementAction {
  userId: string;
  type: 'warning' | 'restriction' | 'suspension' | 'ban';
  reason: string;
  duration?: string;
  sendEmail: boolean;
  emailMessage?: string;
  violationId?: string;
}

function EnforcementActions({
  isOpen,
  onClose,
  userId,
  userName,
  violationId,
  onActionTaken,
}: IEnforcementActionsProps) {
  const [actionType, setActionType] = useState<
    'warning' | 'restriction' | 'suspension' | 'ban'
  >('warning');
  const [reason, setReason] = useState('');
  const [duration, setDuration] = useState('1_day');
  const [restrictionType, setRestrictionType] = useState('commenting');
  const [sendEmail, setSendEmail] = useState(true);
  const [emailMessage, setEmailMessage] = useState('');

  // Reset form when dialog opens
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  // Handle taking action
  const handleTakeAction = () => {
    if (!reason) return;

    const action: IEnforcementAction = {
      userId,
      type: actionType,
      reason,
      duration:
        actionType !== 'warning' && actionType !== 'ban' ? duration : undefined,
      sendEmail,
      emailMessage: sendEmail && emailMessage ? emailMessage : undefined,
      violationId,
    };

    onActionTaken(action);
    onClose();
  };

  // Get default email message based on action type
  const getDefaultEmailMessage = () => {
    switch (actionType) {
      case 'warning':
        return `Dear ${userName},\n\nWe're reaching out regarding content that violates our community guidelines. This is a formal warning. Please review our guidelines to ensure your future contributions align with our community standards.\n\nReason: ${reason}\n\nThank you for your understanding.`;
      case 'restriction':
        return `Dear ${userName},\n\nWe're reaching out regarding content that violates our community guidelines. As a result, we've temporarily restricted your ${restrictionType} privileges for ${formatDuration(duration)}.\n\nReason: ${reason}\n\nThank you for your understanding.`;
      case 'suspension':
        return `Dear ${userName},\n\nWe're reaching out regarding content that violates our community guidelines. As a result, your account has been temporarily suspended for ${formatDuration(duration)}.\n\nReason: ${reason}\n\nThank you for your understanding.`;
      case 'ban':
        return `Dear ${userName},\n\nWe're reaching out regarding serious or repeated violations of our community guidelines. After careful review, we've made the decision to permanently ban your account.\n\nReason: ${reason}\n\nThank you for your understanding.`;
      default:
        return '';
    }
  };

  // Format duration for display
  const formatDuration = (durationCode: string) => {
    switch (durationCode) {
      case '1_day':
        return '1 day';
      case '3_days':
        return '3 days';
      case '7_days':
        return '7 days';
      case '14_days':
        return '14 days';
      case '30_days':
        return '30 days';
      case 'permanent':
        return 'permanently';
      default:
        return durationCode;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Take Enforcement Action</DialogTitle>
          <DialogDescription>
            Apply enforcement actions to user {userName} for guideline
            violations.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          {/* Action Type */}
          <div className="space-y-2">
            <Label>Action Type</Label>
            <RadioGroup
              value={actionType}
              onValueChange={(
                value: 'warning' | 'restriction' | 'suspension' | 'ban',
              ) => {
                setActionType(value);
                // Reset email message when action type changes
                if (sendEmail) {
                  setEmailMessage('');
                }
              }}
              className="grid grid-cols-2 gap-4"
            >
              <div className="flex items-center space-x-2 rounded-md border p-3">
                <RadioGroupItem value="warning" id="warning" />
                <Label htmlFor="warning" className="flex items-center gap-1">
                  <RiErrorWarningLine className="h-4 w-4 text-blue-600" />
                  Warning
                </Label>
              </div>
              <div className="flex items-center space-x-2 rounded-md border p-3">
                <RadioGroupItem value="restriction" id="restriction" />
                <Label
                  htmlFor="restriction"
                  className="flex items-center gap-1"
                >
                  <RiShieldLine className="h-4 w-4 text-amber-600" />
                  Restriction
                </Label>
              </div>
              <div className="flex items-center space-x-2 rounded-md border p-3">
                <RadioGroupItem value="suspension" id="suspension" />
                <Label htmlFor="suspension" className="flex items-center gap-1">
                  <RiCloseLine className="text-red-600 h-4 w-4" />
                  Suspension
                </Label>
              </div>
              <div className="flex items-center space-x-2 rounded-md border p-3">
                <RadioGroupItem value="ban" id="ban" />
                <Label htmlFor="ban" className="flex items-center gap-1">
                  <RiCloseLine className="h-4 w-4 text-purple-600" />
                  Ban
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Restriction Type (only for restriction) */}
          {actionType === 'restriction' && (
            <div className="space-y-2">
              <Label htmlFor="restriction-type">Restriction Type</Label>
              <Select
                value={restrictionType}
                onValueChange={setRestrictionType}
              >
                <SelectTrigger id="restriction-type">
                  <SelectValue placeholder="Select restriction type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="commenting">Commenting</SelectItem>
                  <SelectItem value="posting">Content Creation</SelectItem>
                  <SelectItem value="reporting">Reporting</SelectItem>
                  <SelectItem value="messaging">Messaging</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Duration (for restriction and suspension) */}
          {(actionType === 'restriction' || actionType === 'suspension') && (
            <div className="space-y-2">
              <Label htmlFor="duration">Duration</Label>
              <Select value={duration} onValueChange={setDuration}>
                <SelectTrigger id="duration">
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1_day">1 Day</SelectItem>
                  <SelectItem value="3_days">3 Days</SelectItem>
                  <SelectItem value="7_days">7 Days</SelectItem>
                  <SelectItem value="14_days">14 Days</SelectItem>
                  <SelectItem value="30_days">30 Days</SelectItem>
                  <SelectItem value="permanent">Permanent</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {actionType === 'restriction'
                  ? `The user will be unable to ${restrictionType} for the selected duration.`
                  : 'The user will be unable to access the platform for the selected duration.'}
              </p>
            </div>
          )}

          {/* Reason */}
          <div className="space-y-2">
            <Label htmlFor="reason">Reason</Label>
            <Textarea
              id="reason"
              placeholder="Explain why this action is being taken..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="min-h-[80px]"
            />
            <p className="text-xs text-muted-foreground">
              This will be visible to the user and in the moderation logs.
            </p>
          </div>

          {/* Email Notification */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="send-email" className="flex items-center gap-1">
                <RiMailLine className="h-4 w-4" />
                Send email notification
              </Label>
              <Switch
                id="send-email"
                checked={sendEmail}
                onCheckedChange={setSendEmail}
              />
            </div>
            {sendEmail && (
              <div className="space-y-2">
                <Textarea
                  placeholder="Enter email message..."
                  value={emailMessage || getDefaultEmailMessage()}
                  onChange={(e) => setEmailMessage(e.target.value)}
                  className="min-h-[150px]"
                />
                <div className="flex items-center justify-between">
                  <p className="text-xs text-muted-foreground">
                    This email will be sent to the user.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 text-xs"
                    onClick={() => setEmailMessage(getDefaultEmailMessage())}
                  >
                    Reset to Default
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleTakeAction}
            disabled={!reason.trim()}
            className="flex items-center gap-1"
          >
            <RiShieldLine className="h-4 w-4" />
            Apply {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default EnforcementActions;
