/**
 * @file UserViolations.tsx
 * @description Component for tracking user violations and enforcement actions
 */

'use client';

import { useState } from 'react';
import {
  RiErrorWarningLine,
  RiTimeLine,
  RiAlertLine,
  RiEyeLine,
  RiUserLine,
  RiMailLine,
  RiShieldLine,
  RiHistoryLine,
} from 'react-icons/ri';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Image from 'next/image';

interface IUserViolation {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  violationType:
    | 'spam'
    | 'harassment'
    | 'inappropriate'
    | 'plagiarism'
    | 'other';
  description: string;
  contentType:
    | 'comment'
    | 'roadmap'
    | 'challenge'
    | 'resource'
    | 'forum'
    | 'profile';
  contentId: string;
  contentTitle?: string;
  createdAt: string;
  status: 'active' | 'resolved' | 'dismissed';
  enforcementActions: IEnforcementAction[];
  violationCount: number;
  riskLevel: 'low' | 'medium' | 'high';
}

interface IEnforcementAction {
  id: string;
  type: 'warning' | 'restriction' | 'suspension' | 'ban';
  reason: string;
  appliedAt: string;
  appliedBy: string;
  duration?: string;
  expiresAt?: string;
  status: 'active' | 'expired' | 'removed';
}

interface IUserViolationsProps {
  violations: IUserViolation[];
  onViewViolation: (violationId: string) => void;
  onViewUser: (userId: string) => void;
  onContactUser: (userId: string, userName: string) => void;
  onTakeAction: (userId: string, userName: string, violationId: string) => void;
  onViewHistory: (userId: string, userName: string) => void;
}

function UserViolations({
  violations,
  onViewViolation,
  onViewUser,
  onContactUser,
  onTakeAction,
  onViewHistory,
}: IUserViolationsProps) {
  const [activeTab, setActiveTab] = useState('all');

  // Filter violations based on active tab
  const getFilteredViolations = () => {
    if (activeTab === 'all') return violations;
    if (activeTab === 'high')
      return violations.filter((v) => v.riskLevel === 'high');
    if (activeTab === 'medium')
      return violations.filter((v) => v.riskLevel === 'medium');
    if (activeTab === 'low')
      return violations.filter((v) => v.riskLevel === 'low');
    return violations;
  };

  const filteredViolations = getFilteredViolations();

  // Get violation type badge color
  const getViolationTypeColor = (type: string) => {
    switch (type) {
      case 'spam':
        return 'bg-accent text-accent-foreground';
      case 'harassment':
        return 'bg-destructive/20 text-destructive';
      case 'inappropriate':
        return 'bg-muted text-muted-foreground';
      case 'plagiarism':
        return 'bg-secondary text-secondary-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  // Get risk level badge color
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'bg-destructive/20 text-destructive';
      case 'medium':
        return 'bg-muted text-muted-foreground';
      case 'low':
        return 'bg-primary/20 text-primary2';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  // Get enforcement action badge color
  const getActionColor = (type: string) => {
    switch (type) {
      case 'warning':
        return 'bg-accent text-accent-foreground';
      case 'restriction':
        return 'bg-muted text-muted-foreground';
      case 'suspension':
        return 'bg-destructive/20 text-destructive';
      case 'ban':
        return 'bg-secondary text-secondary-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Users</TabsTrigger>
          <TabsTrigger value="high">High Risk</TabsTrigger>
          <TabsTrigger value="medium">Medium Risk</TabsTrigger>
          <TabsTrigger value="low">Low Risk</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-4">
          <div className="space-y-4">
            {filteredViolations.length > 0 ? (
              filteredViolations.map((violation) => (
                <Card key={violation.id}>
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        {/* User avatar */}
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                          {violation.userAvatar ? (
                            <Image
                              src={violation.userAvatar}
                              alt={violation.userName}
                              width={40}
                              height={40}
                              className="h-10 w-10 rounded-full object-cover"
                            />
                          ) : (
                            <RiUserLine className="h-5 w-5 text-muted-foreground" />
                          )}
                        </div>

                        {/* User info and violation type */}
                        <div>
                          <div className="flex flex-wrap items-center gap-2">
                            <span className="font-medium">
                              {violation.userName}
                            </span>
                            <div
                              className={`rounded-full px-2 py-0.5 text-xs capitalize ${getViolationTypeColor(violation.violationType)}`}
                            >
                              {violation.violationType}
                            </div>
                            <div
                              className={`rounded-full px-2 py-0.5 text-xs capitalize ${getRiskLevelColor(violation.riskLevel)}`}
                            >
                              {violation.riskLevel} risk
                            </div>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <RiHistoryLine className="h-3 w-3" />
                              {violation.violationCount}{' '}
                              {violation.violationCount === 1
                                ? 'violation'
                                : 'violations'}
                            </span>
                            <span>u2022</span>
                            <span className="flex items-center gap-1">
                              <RiTimeLine className="h-3 w-3" />
                              {formatDistanceToNow(
                                new Date(violation.createdAt),
                                { addSuffix: true },
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {/* Violation description */}
                      <div className="rounded-md bg-muted/50 p-3">
                        <p className="text-sm">{violation.description}</p>
                        <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                          <span className="capitalize">
                            {violation.contentType}:
                          </span>
                          <span>
                            {violation.contentTitle ||
                              `ID: ${violation.contentId}`}
                          </span>
                        </div>
                      </div>

                      {/* Enforcement actions */}
                      {violation.enforcementActions.length > 0 && (
                        <div className="space-y-2">
                          <h4 className="text-xs font-medium text-muted-foreground">
                            Current Enforcement:
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {violation.enforcementActions.map((action) => (
                              <div
                                key={action.id}
                                className={`flex items-center gap-1 rounded-full px-2 py-1 text-xs ${getActionColor(action.type)}`}
                              >
                                <RiShieldLine className="h-3 w-3" />
                                <span className="capitalize">
                                  {action.type}
                                </span>
                                {action.expiresAt && (
                                  <span>
                                    u2022 Expires{' '}
                                    {formatDistanceToNow(
                                      new Date(action.expiresAt),
                                      { addSuffix: true },
                                    )}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Action buttons */}
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs"
                          onClick={() => onViewViolation(violation.id)}
                        >
                          <RiEyeLine className="mr-1 h-3 w-3" />
                          View Details
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs"
                          onClick={() => onViewUser(violation.userId)}
                        >
                          <RiUserLine className="mr-1 h-3 w-3" />
                          View User
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs"
                          onClick={() =>
                            onContactUser(violation.userId, violation.userName)
                          }
                        >
                          <RiMailLine className="mr-1 h-3 w-3" />
                          Contact User
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs"
                          onClick={() =>
                            onTakeAction(
                              violation.userId,
                              violation.userName,
                              violation.id,
                            )
                          }
                        >
                          <RiErrorWarningLine className="mr-1 h-3 w-3" />
                          Take Action
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs"
                          onClick={() =>
                            onViewHistory(violation.userId, violation.userName)
                          }
                        >
                          <RiHistoryLine className="mr-1 h-3 w-3" />
                          View History
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="flex h-40 flex-col items-center justify-center p-6">
                  <RiAlertLine className="h-8 w-8 text-muted-foreground" />
                  <h3 className="mt-2 text-lg font-medium">
                    No violations found
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {activeTab === 'all'
                      ? 'There are no user violations to display'
                      : `There are no ${activeTab} risk violations to display`}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default UserViolations;
