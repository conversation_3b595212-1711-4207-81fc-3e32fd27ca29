/**
 * @file ViolationHistory.tsx
 * @description Component for viewing user violation history and patterns
 */

'use client';

import { RiTimeLine, RiErrorWarningLine, RiShieldLine } from 'react-icons/ri';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface IViolationHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  userName: string;
  violationHistory: IViolationHistoryItem[];
  enforcementHistory: IEnforcementHistoryItem[];
}

interface IViolationHistoryItem {
  id: string;
  date: string;
  violationType: string;
  contentType: string;
  contentTitle: string;
  status: 'active' | 'resolved' | 'dismissed';
}

interface IEnforcementHistoryItem {
  id: string;
  date: string;
  actionType: 'warning' | 'restriction' | 'suspension' | 'ban';
  reason: string;
  appliedBy: string;
  duration?: string;
  status: 'active' | 'expired' | 'removed';
  expiresAt?: string;
}

function ViolationHistory({
  isOpen,
  onClose,
  userId,
  userName,
  violationHistory,
  enforcementHistory,
}: IViolationHistoryProps) {
  // Get violation type badge color
  const getViolationTypeColor = (type: string) => {
    switch (type) {
      case 'spam':
        return 'bg-accent text-accent-foreground';
      case 'harassment':
        return 'bg-destructive/20 text-destructive';
      case 'inappropriate':
        return 'bg-muted text-muted-foreground';
      case 'plagiarism':
        return 'bg-secondary text-secondary-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  // Get enforcement action badge color
  const getActionColor = (type: string) => {
    switch (type) {
      case 'warning':
        return 'bg-accent text-accent-foreground';
      case 'restriction':
        return 'bg-muted text-muted-foreground';
      case 'suspension':
        return 'bg-destructive/20 text-destructive';
      case 'ban':
        return 'bg-secondary text-secondary-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-destructive/20 text-destructive';
      case 'expired':
        return 'bg-muted text-muted-foreground';
      case 'removed':
        return 'bg-accent text-accent-foreground';
      case 'resolved':
        return 'bg-primary/20 text-primary2';
      case 'dismissed':
        return 'bg-muted text-muted-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy h:mm a');
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>User Violation History</DialogTitle>
          <DialogDescription>
            Viewing violation and enforcement history for {userName}
          </DialogDescription>
        </DialogHeader>
        <div className="max-h-[60vh] space-y-6 overflow-y-auto py-4">
          {/* Violation History */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Violation History</h3>
            {violationHistory.length > 0 ? (
              <div className="space-y-3">
                {violationHistory.map((violation) => (
                  <div key={violation.id} className="rounded-md border p-3">
                    <div className="flex flex-wrap items-center justify-between gap-2">
                      <div className="flex items-center gap-2">
                        <RiErrorWarningLine className="h-4 w-4 text-muted-foreground" />
                        <div
                          className={`rounded-full px-2 py-0.5 text-xs capitalize ${getViolationTypeColor(violation.violationType)}`}
                        >
                          {violation.violationType}
                        </div>
                        <Badge variant="outline" className="capitalize">
                          {violation.contentType}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <div
                          className={`rounded-full px-2 py-0.5 text-xs capitalize ${getStatusColor(violation.status)}`}
                        >
                          {violation.status}
                        </div>
                        <span className="flex items-center gap-1 text-xs text-muted-foreground">
                          <RiTimeLine className="h-3 w-3" />
                          {formatDate(violation.date)}
                        </span>
                      </div>
                    </div>
                    <p className="mt-2 text-sm">{violation.contentTitle}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex h-20 items-center justify-center rounded-md border border-dashed">
                <p className="text-sm text-muted-foreground">
                  No violation history found
                </p>
              </div>
            )}
          </div>

          {/* Enforcement History */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Enforcement History</h3>
            {enforcementHistory.length > 0 ? (
              <div className="space-y-3">
                {enforcementHistory.map((action) => (
                  <div key={action.id} className="rounded-md border p-3">
                    <div className="flex flex-wrap items-center justify-between gap-2">
                      <div className="flex items-center gap-2">
                        <RiShieldLine className="h-4 w-4 text-muted-foreground" />
                        <div
                          className={`rounded-full px-2 py-0.5 text-xs capitalize ${getActionColor(action.actionType)}`}
                        >
                          {action.actionType}
                        </div>
                        {action.duration && (
                          <span className="text-xs text-muted-foreground">
                            Duration: {action.duration}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <div
                          className={`rounded-full px-2 py-0.5 text-xs capitalize ${getStatusColor(action.status)}`}
                        >
                          {action.status}
                        </div>
                        <span className="flex items-center gap-1 text-xs text-muted-foreground">
                          <RiTimeLine className="h-3 w-3" />
                          {formatDate(action.date)}
                        </span>
                      </div>
                    </div>
                    <p className="mt-2 text-sm">{action.reason}</p>
                    <div className="mt-1 flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">
                        Applied by: {action.appliedBy}
                      </span>
                      {action.expiresAt && (
                        <span className="text-xs text-muted-foreground">
                          Expires: {formatDate(action.expiresAt)}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex h-20 items-center justify-center rounded-md border border-dashed">
                <p className="text-sm text-muted-foreground">
                  No enforcement history found
                </p>
              </div>
            )}
          </div>

          {/* Pattern Analysis */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium">Behavior Pattern Analysis</h3>
            <div className="rounded-md border p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Total Violations</span>
                  <span className="text-sm font-bold">
                    {violationHistory.length}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    Active Enforcement Actions
                  </span>
                  <span className="text-sm font-bold">
                    {
                      enforcementHistory.filter((a) => a.status === 'active')
                        .length
                    }
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">First Violation</span>
                  <span className="text-sm">
                    {violationHistory.length > 0
                      ? formatDate(
                          violationHistory[violationHistory.length - 1].date,
                        )
                      : 'N/A'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    Most Recent Violation
                  </span>
                  <span className="text-sm">
                    {violationHistory.length > 0
                      ? formatDate(violationHistory[0].date)
                      : 'N/A'}
                  </span>
                </div>

                {/* Violation Types Distribution */}
                {violationHistory.length > 0 && (
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Violation Types</span>
                    <div className="space-y-2">
                      {Object.entries(
                        violationHistory.reduce(
                          (acc, violation) => {
                            acc[violation.violationType] =
                              (acc[violation.violationType] || 0) + 1;
                            return acc;
                          },
                          {} as Record<string, number>,
                        ),
                      ).map(([type, count]) => (
                        <div
                          key={type}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center gap-2">
                            <div
                              className={`h-2 w-2 rounded-full ${getViolationTypeColor(type)}`}
                            />
                            <span className="text-xs capitalize">{type}</span>
                          </div>
                          <span className="text-xs font-medium">{count}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default ViolationHistory;
