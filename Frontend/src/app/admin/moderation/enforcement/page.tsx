/**
 * @file page.tsx
 * @description Community Guidelines Enforcement for admin dashboard
 */

'use client';

import { useState, useEffect } from 'react';
import {
  RiArrowLeftLine,
  RiRefreshLine,
  RiSearchLine,
  RiErrorWarningLine,
  RiShieldLine,
  RiSettings3Line,
} from 'react-icons/ri';
import { useRouter } from 'next/navigation';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import UserViolations from './components/UserViolations';
import EnforcementActions from './components/EnforcementActions';
import ViolationHistory from './components/ViolationHistory';

interface IUserViolation {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  violationType:
    | 'spam'
    | 'harassment'
    | 'inappropriate'
    | 'plagiarism'
    | 'other';
  description: string;
  contentType:
    | 'comment'
    | 'roadmap'
    | 'challenge'
    | 'resource'
    | 'forum'
    | 'profile';
  contentId: string;
  contentTitle?: string;
  createdAt: string;
  status: 'active' | 'resolved' | 'dismissed';
  enforcementActions: IEnforcementAction[];
  violationCount: number;
  riskLevel: 'low' | 'medium' | 'high';
}

interface IEnforcementAction {
  id: string;
  type: 'warning' | 'restriction' | 'suspension' | 'ban';
  reason: string;
  appliedAt: string;
  appliedBy: string;
  duration?: string;
  expiresAt?: string;
  status: 'active' | 'expired' | 'removed';
}

interface IViolationHistoryItem {
  id: string;
  date: string;
  violationType: string;
  contentType: string;
  contentTitle: string;
  status: 'active' | 'resolved' | 'dismissed';
}

interface IEnforcementHistoryItem {
  id: string;
  date: string;
  actionType: 'warning' | 'restriction' | 'suspension' | 'ban';
  reason: string;
  appliedBy: string;
  duration?: string;
  status: 'active' | 'expired' | 'removed';
  expiresAt?: string;
}

interface IViolationFilters {
  violationType: string;
  contentType: string;
  riskLevel: string;
  searchTerm: string;
}

function CommunityGuidelinesEnforcementPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('violations');
  const [violations, setViolations] = useState<IUserViolation[]>([]);
  const [filteredViolations, setFilteredViolations] = useState<
    IUserViolation[]
  >([]);
  const [filters, setFilters] = useState<IViolationFilters>({
    violationType: 'all',
    contentType: 'all',
    riskLevel: 'all',
    searchTerm: '',
  });
  const [isLoading, setIsLoading] = useState(true);

  // Dialogs state
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false);
  const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<{
    id: string;
    name: string;
  }>({ id: '', name: '' });
  const [selectedViolationId, setSelectedViolationId] = useState<string>('');

  // Mock data for violations
  const mockViolations: IUserViolation[] = [
    {
      id: 'violation_1',
      userId: 'user_1',
      userName: 'John Doe',
      userAvatar: 'https://ui-avatars.com/api/?name=John+Doe',
      violationType: 'spam',
      description:
        'User has been posting promotional content across multiple forums.',
      contentType: 'forum',
      contentId: 'forum_1',
      contentTitle: 'Discussion: Best Practices for React',
      createdAt: '2025-05-24T08:30:00Z',
      status: 'active',
      enforcementActions: [
        {
          id: 'action_1',
          type: 'warning',
          reason: 'Posting promotional content is against our guidelines.',
          appliedAt: '2025-05-24T09:15:00Z',
          appliedBy: 'Admin User',
          status: 'active',
        },
      ],
      violationCount: 2,
      riskLevel: 'low',
    },
    {
      id: 'violation_2',
      userId: 'user_2',
      userName: 'Jane Smith',
      userAvatar: 'https://ui-avatars.com/api/?name=Jane+Smith',
      violationType: 'harassment',
      description:
        'User has been posting offensive comments targeting specific community members.',
      contentType: 'comment',
      contentId: 'comment_1',
      contentTitle: 'Comment on Advanced Algorithms Challenge',
      createdAt: '2025-05-23T14:15:00Z',
      status: 'active',
      enforcementActions: [
        {
          id: 'action_2',
          type: 'restriction',
          reason: 'Harassment of other users.',
          appliedAt: '2025-05-23T15:30:00Z',
          appliedBy: 'Admin User',
          duration: '7 days',
          expiresAt: '2025-05-30T15:30:00Z',
          status: 'active',
        },
      ],
      violationCount: 3,
      riskLevel: 'high',
    },
    {
      id: 'violation_3',
      userId: 'user_3',
      userName: 'Alex Johnson',
      violationType: 'plagiarism',
      description:
        'User has submitted a roadmap that appears to be copied from another website without attribution.',
      contentType: 'roadmap',
      contentId: 'roadmap_1',
      contentTitle: 'Machine Learning Engineer Path',
      createdAt: '2025-05-22T11:45:00Z',
      status: 'active',
      enforcementActions: [],
      violationCount: 1,
      riskLevel: 'medium',
    },
    {
      id: 'violation_4',
      userId: 'user_4',
      userName: 'Michael Brown',
      userAvatar: 'https://ui-avatars.com/api/?name=Michael+Brown',
      violationType: 'inappropriate',
      description:
        'User profile contains inappropriate content that violates community guidelines.',
      contentType: 'profile',
      contentId: 'user_4',
      contentTitle: 'User Profile: Michael Brown',
      createdAt: '2025-05-21T16:20:00Z',
      status: 'resolved',
      enforcementActions: [
        {
          id: 'action_3',
          type: 'warning',
          reason: 'Inappropriate profile content.',
          appliedAt: '2025-05-21T17:10:00Z',
          appliedBy: 'Admin User',
          status: 'expired',
        },
      ],
      violationCount: 1,
      riskLevel: 'low',
    },
    {
      id: 'violation_5',
      userId: 'user_5',
      userName: 'Sarah Wilson',
      violationType: 'other',
      description:
        'User is repeatedly submitting invalid reports against other users.',
      contentType: 'forum',
      contentId: 'forum_2',
      contentTitle: 'Discussion: JavaScript Performance Tips',
      createdAt: '2025-05-20T09:10:00Z',
      status: 'active',
      enforcementActions: [],
      violationCount: 4,
      riskLevel: 'medium',
    },
  ];

  // Mock data for user violation history
  const mockViolationHistory: Record<string, IViolationHistoryItem[]> = {
    user_1: [
      {
        id: 'vh_1',
        date: '2025-05-24T08:30:00Z',
        violationType: 'spam',
        contentType: 'forum',
        contentTitle: 'Discussion: Best Practices for React',
        status: 'active',
      },
      {
        id: 'vh_2',
        date: '2025-05-20T10:15:00Z',
        violationType: 'spam',
        contentType: 'comment',
        contentTitle: 'Comment on Web Development Guide',
        status: 'resolved',
      },
    ],
    user_2: [
      {
        id: 'vh_3',
        date: '2025-05-23T14:15:00Z',
        violationType: 'harassment',
        contentType: 'comment',
        contentTitle: 'Comment on Advanced Algorithms Challenge',
        status: 'active',
      },
      {
        id: 'vh_4',
        date: '2025-05-18T09:30:00Z',
        violationType: 'inappropriate',
        contentType: 'comment',
        contentTitle: 'Comment on Weekend Code Battle',
        status: 'resolved',
      },
      {
        id: 'vh_5',
        date: '2025-05-10T16:45:00Z',
        violationType: 'harassment',
        contentType: 'forum',
        contentTitle: 'Discussion: Career Advice for Developers',
        status: 'resolved',
      },
    ],
  };

  // Mock data for user enforcement history
  const mockEnforcementHistory: Record<string, IEnforcementHistoryItem[]> = {
    user_1: [
      {
        id: 'eh_1',
        date: '2025-05-24T09:15:00Z',
        actionType: 'warning',
        reason: 'Posting promotional content is against our guidelines.',
        appliedBy: 'Admin User',
        status: 'active',
      },
    ],
    user_2: [
      {
        id: 'eh_2',
        date: '2025-05-23T15:30:00Z',
        actionType: 'restriction',
        reason: 'Harassment of other users.',
        appliedBy: 'Admin User',
        duration: '7 days',
        expiresAt: '2025-05-30T15:30:00Z',
        status: 'active',
      },
      {
        id: 'eh_3',
        date: '2025-05-18T10:00:00Z',
        actionType: 'warning',
        reason: 'Inappropriate comments.',
        appliedBy: 'Moderator User',
        status: 'expired',
      },
    ],
  };

  // Simulate fetching violations from API
  useEffect(() => {
    const fetchViolations = async () => {
      setIsLoading(true);
      // In a real app, this would be an API call
      setTimeout(() => {
        setViolations(mockViolations);
        setIsLoading(false);
      }, 1000);
    };

    fetchViolations();
  }, []);

  // Filter violations based on current filters
  useEffect(() => {
    let filtered = [...violations];

    if (filters.violationType !== 'all') {
      filtered = filtered.filter(
        (violation) => violation.violationType === filters.violationType,
      );
    }

    if (filters.contentType !== 'all') {
      filtered = filtered.filter(
        (violation) => violation.contentType === filters.contentType,
      );
    }

    if (filters.riskLevel !== 'all') {
      filtered = filtered.filter(
        (violation) => violation.riskLevel === filters.riskLevel,
      );
    }

    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (violation) =>
          violation.userName.toLowerCase().includes(searchLower) ||
          violation.description.toLowerCase().includes(searchLower) ||
          (violation.contentTitle &&
            violation.contentTitle.toLowerCase().includes(searchLower)),
      );
    }

    // Sort by risk level (high to low) and then by date (newest first)
    filtered.sort((a, b) => {
      const riskOrder = { high: 0, medium: 1, low: 2 };
      const riskDiff =
        riskOrder[a.riskLevel as keyof typeof riskOrder] -
        riskOrder[b.riskLevel as keyof typeof riskOrder];

      if (riskDiff !== 0) {
        return riskDiff;
      }

      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    setFilteredViolations(filtered);
  }, [violations, filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof IViolationFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  // Handle viewing violation details
  const handleViewViolation = (violationId: string) => {
    // In a real app, this would navigate to the violation details page
    console.log('Viewing violation:', violationId);
  };

  // Handle viewing user profile
  const handleViewUser = (userId: string) => {
    // In a real app, this would navigate to the user profile page
    router.push(`/admin/users/${userId}`);
  };

  // Handle contacting a user
  const handleContactUser = (userId: string, userName: string) => {
    setSelectedUser({ id: userId, name: userName });
    // In a real app, this would open a contact dialog
    console.log('Contacting user:', userId, userName);
  };

  // Handle taking enforcement action
  const handleTakeAction = (
    userId: string,
    userName: string,
    violationId: string,
  ) => {
    setSelectedUser({ id: userId, name: userName });
    setSelectedViolationId(violationId);
    setIsActionDialogOpen(true);
  };

  // Handle viewing user history
  const handleViewHistory = (userId: string, userName: string) => {
    setSelectedUser({ id: userId, name: userName });
    setIsHistoryDialogOpen(true);
  };

  // Handle enforcement action taken
  const handleActionTaken = (action: any) => {
    // In a real app, this would send the action to an API
    console.log('Action taken:', action);

    // For demo purposes, update the local state
    const newAction: IEnforcementAction = {
      id: `action_${Date.now()}`,
      type: action.type as 'warning' | 'restriction' | 'suspension' | 'ban',
      reason: action.reason,
      appliedAt: new Date().toISOString(),
      appliedBy: 'Admin User',
      duration: action.duration,
      expiresAt: action.duration
        ? new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        : undefined,
      status: 'active' as 'active' | 'expired' | 'removed',
    };

    setViolations((prev) =>
      prev.map((violation) =>
        violation.id === selectedViolationId
          ? {
              ...violation,
              enforcementActions: [...violation.enforcementActions, newAction],
            }
          : violation,
      ),
    );
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/admin/moderation')}
            className="h-8 w-8"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">
            Guidelines Enforcement
          </h2>
        </div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => window.location.reload()}
          >
            <RiRefreshLine className="h-4 w-4" />
            Refresh
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() =>
              router.push('/admin/moderation/enforcement/settings')
            }
          >
            <RiSettings3Line className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="violations" className="flex items-center gap-1">
            <RiErrorWarningLine className="h-4 w-4" />
            User Violations
          </TabsTrigger>
          <TabsTrigger value="patterns" className="flex items-center gap-1">
            <RiShieldLine className="h-4 w-4" />
            Behavior Patterns
          </TabsTrigger>
        </TabsList>

        {/* Violations Tab */}
        <TabsContent value="violations" className="mt-6 space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <CardTitle>User Violations</CardTitle>
                  <CardDescription>
                    Monitor and manage user violations of community guidelines
                  </CardDescription>
                </div>

                {/* Filters */}
                <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                  <div className="relative flex-1">
                    <RiSearchLine className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search violations..."
                      className="pl-8"
                      value={filters.searchTerm}
                      onChange={(e) =>
                        handleFilterChange('searchTerm', e.target.value)
                      }
                    />
                  </div>
                  <Select
                    value={filters.violationType}
                    onValueChange={(value) =>
                      handleFilterChange('violationType', value)
                    }
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Violation Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="spam">Spam</SelectItem>
                      <SelectItem value="harassment">Harassment</SelectItem>
                      <SelectItem value="inappropriate">
                        Inappropriate
                      </SelectItem>
                      <SelectItem value="plagiarism">Plagiarism</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select
                    value={filters.riskLevel}
                    onValueChange={(value) =>
                      handleFilterChange('riskLevel', value)
                    }
                  >
                    <SelectTrigger className="w-[130px]">
                      <SelectValue placeholder="Risk Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Levels</SelectItem>
                      <SelectItem value="high">High Risk</SelectItem>
                      <SelectItem value="medium">Medium Risk</SelectItem>
                      <SelectItem value="low">Low Risk</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                // Loading state
                <div className="flex h-40 items-center justify-center">
                  <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                </div>
              ) : (
                <UserViolations
                  violations={filteredViolations}
                  onViewViolation={handleViewViolation}
                  onViewUser={handleViewUser}
                  onContactUser={handleContactUser}
                  onTakeAction={handleTakeAction}
                  onViewHistory={handleViewHistory}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Patterns Tab */}
        <TabsContent value="patterns" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Behavior Patterns</CardTitle>
              <CardDescription>
                Analyze user behavior patterns and identify potential issues
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {/* Violation Types */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Violation Types</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { type: 'harassment', count: 12, percentage: 35 },
                        { type: 'spam', count: 8, percentage: 23 },
                        { type: 'inappropriate', count: 7, percentage: 20 },
                        { type: 'plagiarism', count: 5, percentage: 15 },
                        { type: 'other', count: 2, percentage: 7 },
                      ].map((item) => (
                        <div key={item.type} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm capitalize">
                              {item.type}
                            </span>
                            <span className="text-sm font-medium">
                              {item.count} ({item.percentage}%)
                            </span>
                          </div>
                          <div className="h-2 w-full rounded-full bg-muted">
                            <div
                              className="h-2 rounded-full bg-primary"
                              style={{ width: `${item.percentage}%` }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Content Types */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Content Types</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { type: 'comment', count: 18, percentage: 53 },
                        { type: 'forum', count: 8, percentage: 23 },
                        { type: 'profile', count: 4, percentage: 12 },
                        { type: 'roadmap', count: 3, percentage: 9 },
                        { type: 'challenge', count: 1, percentage: 3 },
                      ].map((item) => (
                        <div key={item.type} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm capitalize">
                              {item.type}
                            </span>
                            <span className="text-sm font-medium">
                              {item.count} ({item.percentage}%)
                            </span>
                          </div>
                          <div className="h-2 w-full rounded-full bg-muted">
                            <div
                              className="h-2 rounded-full bg-primary"
                              style={{ width: `${item.percentage}%` }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Repeat Offenders */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Repeat Offenders</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[
                        { name: 'Jane Smith', violations: 3, risk: 'high' },
                        { name: 'Sarah Wilson', violations: 4, risk: 'medium' },
                        { name: 'John Doe', violations: 2, risk: 'low' },
                        { name: 'Robert Taylor', violations: 2, risk: 'low' },
                        { name: 'Emily Chen', violations: 2, risk: 'low' },
                      ].map((user, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between rounded-md border p-3"
                        >
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{user.name}</span>
                            <Badge
                              className={`${user.risk === 'high' ? 'bg-destructive/20 text-destructive' : user.risk === 'medium' ? 'bg-muted text-muted-foreground' : 'bg-primary/20 text-primary2'}`}
                            >
                              {user.risk}
                            </Badge>
                          </div>
                          <span className="text-sm">
                            {user.violations} violations
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Enforcement Actions */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">
                      Enforcement Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { type: 'warning', count: 24, percentage: 60 },
                        { type: 'restriction', count: 10, percentage: 25 },
                        { type: 'suspension', count: 5, percentage: 12 },
                        { type: 'ban', count: 1, percentage: 3 },
                      ].map((item) => (
                        <div key={item.type} className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm capitalize">
                              {item.type}
                            </span>
                            <span className="text-sm font-medium">
                              {item.count} ({item.percentage}%)
                            </span>
                          </div>
                          <div className="h-2 w-full rounded-full bg-muted">
                            <div
                              className="h-2 rounded-full bg-primary"
                              style={{ width: `${item.percentage}%` }}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Enforcement Action Dialog */}
      <EnforcementActions
        isOpen={isActionDialogOpen}
        onClose={() => setIsActionDialogOpen(false)}
        userId={selectedUser.id}
        userName={selectedUser.name}
        violationId={selectedViolationId}
        onActionTaken={handleActionTaken}
      />

      {/* Violation History Dialog */}
      <ViolationHistory
        isOpen={isHistoryDialogOpen}
        onClose={() => setIsHistoryDialogOpen(false)}
        userId={selectedUser.id}
        userName={selectedUser.name}
        violationHistory={mockViolationHistory[selectedUser.id] || []}
        enforcementHistory={mockEnforcementHistory[selectedUser.id] || []}
      />
    </div>
  );
}

export default CommunityGuidelinesEnforcementPage;
