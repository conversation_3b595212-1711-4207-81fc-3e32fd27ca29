/**
 * @file page.tsx
 * @description Reported Content Management for admin dashboard
 */

'use client';

import { useState, useEffect } from 'react';
import {
  RiArrowLeftLine,
  RiRefreshLine,
  RiFilterLine,
  RiEyeLine,
  RiCheckLine,
  RiCloseLine,
  RiAlertLine,
  RiUserLine,
  RiTimeLine,
  RiDeleteBinLine,
  RiSearchLine,
  RiMailLine,
} from 'react-icons/ri';
import { useRouter } from 'next/navigation';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface IReport {
  id: string;
  contentType:
    | 'comment'
    | 'roadmap'
    | 'challenge'
    | 'resource'
    | 'user'
    | 'forum';
  contentId: string;
  contentTitle: string;
  reason: string;
  details?: string;
  reporter: {
    id: string;
    name: string;
    avatar?: string;
  };
  reportedUser: {
    id: string;
    name: string;
    avatar?: string;
  };
  createdAt: string;
  status: 'pending' | 'resolved' | 'dismissed';
  resolution?: string;
  priority: 'low' | 'medium' | 'high';
}

interface IReportFilters {
  contentType: string;
  status: string;
  priority: string;
  searchTerm: string;
}

function ReportedContentManagementPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('pending');
  const [reports, setReports] = useState<IReport[]>([]);
  const [filteredReports, setFilteredReports] = useState<IReport[]>([]);
  const [filters, setFilters] = useState<IReportFilters>({
    contentType: 'all',
    status: 'pending',
    priority: 'all',
    searchTerm: '',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState<IReport | null>(null);
  const [isResolveDialogOpen, setIsResolveDialogOpen] = useState(false);
  const [resolution, setResolution] = useState('');
  const [isContactDialogOpen, setIsContactDialogOpen] = useState(false);
  const [contactMessage, setContactMessage] = useState('');

  // Mock data for reports
  const mockReports: IReport[] = [
    {
      id: 'report_1',
      contentType: 'comment',
      contentId: 'comment_1',
      contentTitle: 'Comment on React Developer Roadmap',
      reason: 'Inappropriate content',
      details: 'This comment contains offensive language and personal attacks.',
      reporter: {
        id: 'user_1',
        name: 'John Doe',
        avatar: 'https://ui-avatars.com/api/?name=John+Doe',
      },
      reportedUser: {
        id: 'user_2',
        name: 'Jane Smith',
        avatar: 'https://ui-avatars.com/api/?name=Jane+Smith',
      },
      createdAt: '2025-05-24T08:30:00Z',
      status: 'pending',
      priority: 'high',
    },
    {
      id: 'report_2',
      contentType: 'roadmap',
      contentId: 'roadmap_1',
      contentTitle: 'Machine Learning Engineer Path',
      reason: 'Plagiarism',
      details:
        'This roadmap appears to be copied from another website without attribution.',
      reporter: {
        id: 'user_3',
        name: 'Alex Johnson',
      },
      reportedUser: {
        id: 'user_4',
        name: 'Michael Brown',
        avatar: 'https://ui-avatars.com/api/?name=Michael+Brown',
      },
      createdAt: '2025-05-23T14:15:00Z',
      status: 'pending',
      priority: 'medium',
    },
    {
      id: 'report_3',
      contentType: 'user',
      contentId: 'user_5',
      contentTitle: 'User Profile: Sarah Wilson',
      reason: 'Spam',
      details:
        'This user is spamming promotional content across multiple forums and comments.',
      reporter: {
        id: 'user_6',
        name: 'David Lee',
        avatar: 'https://ui-avatars.com/api/?name=David+Lee',
      },
      reportedUser: {
        id: 'user_5',
        name: 'Sarah Wilson',
      },
      createdAt: '2025-05-22T11:45:00Z',
      status: 'resolved',
      resolution:
        'User has been warned and promotional content has been removed.',
      priority: 'medium',
    },
    {
      id: 'report_4',
      contentType: 'challenge',
      contentId: 'challenge_1',
      contentTitle: 'Advanced Algorithms Challenge',
      reason: 'Incorrect solution',
      details:
        'The provided solution for this challenge is incorrect and misleads learners.',
      reporter: {
        id: 'user_7',
        name: 'Emily Chen',
        avatar: 'https://ui-avatars.com/api/?name=Emily+Chen',
      },
      reportedUser: {
        id: 'user_8',
        name: 'Robert Taylor',
      },
      createdAt: '2025-05-21T16:20:00Z',
      status: 'dismissed',
      resolution: 'The solution was verified and found to be correct.',
      priority: 'low',
    },
    {
      id: 'report_5',
      contentType: 'forum',
      contentId: 'forum_post_1',
      contentTitle: 'Discussion: Best Practices for React',
      reason: 'Off-topic',
      details:
        'This discussion has gone completely off-topic and is no longer relevant to React.',
      reporter: {
        id: 'user_9',
        name: 'Thomas Wilson',
      },
      reportedUser: {
        id: 'user_10',
        name: 'Amanda Garcia',
        avatar: 'https://ui-avatars.com/api/?name=Amanda+Garcia',
      },
      createdAt: '2025-05-20T09:10:00Z',
      status: 'pending',
      priority: 'low',
    },
  ];

  // Simulate fetching reports from API
  useEffect(() => {
    const fetchReports = async () => {
      setIsLoading(true);
      // In a real app, this would be an API call
      setTimeout(() => {
        setReports(mockReports);
        setIsLoading(false);
      }, 1000);
    };

    fetchReports();
  }, []);

  // Filter reports based on current filters and active tab
  useEffect(() => {
    let filtered = [...reports];

    // Filter by tab (status)
    if (activeTab !== 'all') {
      filtered = filtered.filter((report) => report.status === activeTab);
    }

    // Apply additional filters
    if (filters.contentType !== 'all') {
      filtered = filtered.filter(
        (report) => report.contentType === filters.contentType,
      );
    }

    if (filters.priority !== 'all') {
      filtered = filtered.filter(
        (report) => report.priority === filters.priority,
      );
    }

    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (report) =>
          report.contentTitle.toLowerCase().includes(searchLower) ||
          report.reason.toLowerCase().includes(searchLower) ||
          (report.details &&
            report.details.toLowerCase().includes(searchLower)) ||
          report.reporter.name.toLowerCase().includes(searchLower) ||
          report.reportedUser.name.toLowerCase().includes(searchLower),
      );
    }

    // Sort by priority (high to low) and then by date (newest first)
    filtered.sort((a, b) => {
      const priorityOrder = { high: 0, medium: 1, low: 2 };
      const priorityDiff =
        priorityOrder[a.priority as keyof typeof priorityOrder] -
        priorityOrder[b.priority as keyof typeof priorityOrder];

      if (priorityDiff !== 0) {
        return priorityDiff;
      }

      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    setFilteredReports(filtered);
  }, [reports, filters, activeTab]);

  // Handle filter changes
  const handleFilterChange = (key: keyof IReportFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  // Open resolve dialog
  const handleOpenResolve = (report: IReport) => {
    setSelectedReport(report);
    setResolution(report.resolution || '');
    setIsResolveDialogOpen(true);
  };

  // Open contact dialog
  const handleOpenContact = (report: IReport) => {
    setSelectedReport(report);
    setContactMessage('');
    setIsContactDialogOpen(true);
  };

  // Handle resolving a report
  const handleResolveReport = () => {
    if (!selectedReport) return;

    setReports((prev) =>
      prev.map((report) =>
        report.id === selectedReport.id
          ? { ...report, status: 'resolved', resolution }
          : report,
      ),
    );

    setIsResolveDialogOpen(false);
    setSelectedReport(null);
    setResolution('');
  };

  // Handle dismissing a report
  const handleDismissReport = () => {
    if (!selectedReport) return;

    setReports((prev) =>
      prev.map((report) =>
        report.id === selectedReport.id
          ? { ...report, status: 'dismissed', resolution }
          : report,
      ),
    );

    setIsResolveDialogOpen(false);
    setSelectedReport(null);
    setResolution('');
  };

  // Handle contacting a user
  const handleContactUser = () => {
    if (!selectedReport || !contactMessage) return;

    // In a real app, this would send a message to the user
    console.log(
      'Contacting user:',
      selectedReport.reportedUser.id,
      'Message:',
      contactMessage,
    );

    setIsContactDialogOpen(false);
    setSelectedReport(null);
    setContactMessage('');
  };

  // Handle viewing the reported content
  const handleViewContent = (report: IReport) => {
    // In a real app, this would navigate to the content
    console.log(
      'Viewing content:',
      report.contentId,
      'Type:',
      report.contentType,
    );
  };

  // Get counts for each tab
  const getCounts = () => {
    return {
      all: reports.length,
      pending: reports.filter((report) => report.status === 'pending').length,
      resolved: reports.filter((report) => report.status === 'resolved').length,
      dismissed: reports.filter((report) => report.status === 'dismissed')
        .length,
    };
  };

  const counts = getCounts();

  // Get priority badge color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-amber-100 text-amber-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get content type icon
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'comment':
        return <span className="i-ri-chat-1-line h-4 w-4" />;
      case 'roadmap':
        return <span className="i-ri-road-map-line h-4 w-4" />;
      case 'challenge':
        return <span className="i-ri-code-box-line h-4 w-4" />;
      case 'resource':
        return <span className="i-ri-file-text-line h-4 w-4" />;
      case 'user':
        return <span className="i-ri-user-line h-4 w-4" />;
      case 'forum':
        return <span className="i-ri-discuss-line h-4 w-4" />;
      default:
        return <span className="i-ri-file-line h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/admin/moderation')}
            className="h-8 w-8"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">
            Reported Content
          </h2>
        </div>
        <div className="flex flex-col gap-2 sm:flex-row">
          <Button
            variant="outline"
            className="flex items-center gap-1"
            onClick={() => setActiveTab('pending')}
          >
            <RiRefreshLine className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pending" className="flex items-center gap-1">
            Pending
            <Badge variant="secondary" className="ml-1">
              {counts.pending}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="resolved" className="flex items-center gap-1">
            Resolved
            <Badge variant="secondary" className="ml-1">
              {counts.resolved}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="dismissed" className="flex items-center gap-1">
            Dismissed
            <Badge variant="secondary" className="ml-1">
              {counts.dismissed}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="all" className="flex items-center gap-1">
            All
            <Badge variant="secondary" className="ml-1">
              {counts.all}
            </Badge>
          </TabsTrigger>
        </TabsList>

        {/* Tab Content */}
        {['pending', 'resolved', 'dismissed', 'all'].map((tab) => (
          <TabsContent key={tab} value={tab} className="mt-6 space-y-6">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                  <div>
                    <CardTitle className="capitalize">{tab} Reports</CardTitle>
                    <CardDescription>
                      {tab === 'pending' &&
                        'Reports awaiting moderation action'}
                      {tab === 'resolved' && 'Reports that have been resolved'}
                      {tab === 'dismissed' &&
                        'Reports that have been dismissed'}
                      {tab === 'all' && 'All reports across the platform'}
                    </CardDescription>
                  </div>

                  {/* Filters */}
                  <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                    <div className="relative flex-1">
                      <RiSearchLine className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search reports..."
                        className="pl-8"
                        value={filters.searchTerm}
                        onChange={(e) =>
                          handleFilterChange('searchTerm', e.target.value)
                        }
                      />
                    </div>
                    <Select
                      value={filters.contentType}
                      onValueChange={(value) =>
                        handleFilterChange('contentType', value)
                      }
                    >
                      <SelectTrigger className="w-[140px]">
                        <SelectValue placeholder="Content Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="comment">Comments</SelectItem>
                        <SelectItem value="roadmap">Roadmaps</SelectItem>
                        <SelectItem value="challenge">Challenges</SelectItem>
                        <SelectItem value="resource">Resources</SelectItem>
                        <SelectItem value="user">Users</SelectItem>
                        <SelectItem value="forum">Forum Posts</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select
                      value={filters.priority}
                      onValueChange={(value) =>
                        handleFilterChange('priority', value)
                      }
                    >
                      <SelectTrigger className="w-[130px]">
                        <SelectValue placeholder="Priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Priorities</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  // Loading state
                  <div className="flex h-40 items-center justify-center">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                  </div>
                ) : filteredReports.length > 0 ? (
                  // Reports list
                  <div className="space-y-4">
                    {filteredReports.map((report) => (
                      <div key={report.id} className="rounded-lg border p-4">
                        <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
                          <div className="flex-1 space-y-2">
                            {/* Title and badges */}
                            <div className="flex flex-wrap items-center gap-2">
                              <h3 className="text-lg font-medium">
                                {report.contentTitle}
                              </h3>
                              <Badge variant="outline" className="capitalize">
                                {report.contentType}
                              </Badge>
                              <div
                                className={`rounded-full px-2 py-0.5 text-xs capitalize ${getPriorityColor(report.priority)}`}
                              >
                                {report.priority} priority
                              </div>
                            </div>

                            {/* Reason and details */}
                            <div>
                              <div className="text-sm font-medium">
                                Reason: {report.reason}
                              </div>
                              {report.details && (
                                <p className="mt-1 text-sm text-muted-foreground">
                                  {report.details}
                                </p>
                              )}
                            </div>

                            {/* Metadata */}
                            <div className="flex flex-wrap items-center gap-3 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <RiUserLine className="h-3 w-3" />
                                Reported by: {report.reporter.name}
                              </div>
                              <div className="flex items-center gap-1">
                                <RiUserLine className="h-3 w-3" />
                                Reported user: {report.reportedUser.name}
                              </div>
                              <div className="flex items-center gap-1">
                                <RiTimeLine className="h-3 w-3" />
                                {formatDate(report.createdAt)}
                              </div>
                            </div>

                            {/* Resolution */}
                            {(report.status === 'resolved' ||
                              report.status === 'dismissed') &&
                              report.resolution && (
                                <div
                                  className={`rounded-md p-2 text-xs ${report.status === 'resolved' ? 'bg-green-50 text-green-800' : 'bg-gray-50 text-gray-800'}`}
                                >
                                  <strong>
                                    {report.status === 'resolved'
                                      ? 'Resolution'
                                      : 'Dismissal reason'}
                                    :
                                  </strong>{' '}
                                  {report.resolution}
                                </div>
                              )}
                          </div>

                          {/* Action buttons */}
                          <div className="flex flex-row gap-2 sm:flex-col">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-1"
                              onClick={() => handleViewContent(report)}
                            >
                              <RiEyeLine className="h-4 w-4" />
                              View Content
                            </Button>

                            {report.status === 'pending' && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-1"
                                  onClick={() => handleOpenContact(report)}
                                >
                                  <RiMailLine className="h-4 w-4" />
                                  Contact User
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-1 text-destructive hover:bg-destructive/10 hover:text-destructive"
                                  onClick={() => handleOpenResolve(report)}
                                >
                                  <RiCloseLine className="h-4 w-4" />
                                  Resolve
                                </Button>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  // Empty state
                  <div className="flex h-40 flex-col items-center justify-center rounded-md border border-dashed">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                      <RiAlertLine className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <h3 className="mt-2 text-lg font-medium">
                      No reports found
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {tab === 'pending' &&
                        'There are no pending reports to review'}
                      {tab === 'resolved' && 'There are no resolved reports'}
                      {tab === 'dismissed' && 'There are no dismissed reports'}
                      {tab === 'all' &&
                        'There are no reports matching your filters'}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Resolve Dialog */}
      <Dialog open={isResolveDialogOpen} onOpenChange={setIsResolveDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Resolve Report</DialogTitle>
            <DialogDescription>
              Take action on this report and document the resolution.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="mb-4">
              <h3 className="text-sm font-medium">
                {selectedReport?.contentTitle}
              </h3>
              <p className="text-xs text-muted-foreground">
                Reported for: {selectedReport?.reason}
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="resolution">Resolution Notes</Label>
              <Textarea
                id="resolution"
                placeholder="Describe what action was taken..."
                value={resolution}
                onChange={(e) => setResolution(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground">
                Document the actions taken to address this report.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => handleDismissReport()}
              className="flex items-center gap-1"
            >
              Dismiss Report
            </Button>
            <Button
              onClick={handleResolveReport}
              disabled={!resolution.trim()}
              className="flex items-center gap-1"
            >
              <RiCheckLine className="h-4 w-4" />
              Resolve Report
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Contact User Dialog */}
      <Dialog open={isContactDialogOpen} onOpenChange={setIsContactDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Contact User</DialogTitle>
            <DialogDescription>
              Send a message to the reported user about this content.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="mb-4">
              <h3 className="text-sm font-medium">
                To: {selectedReport?.reportedUser.name}
              </h3>
              <p className="text-xs text-muted-foreground">
                Regarding: {selectedReport?.contentTitle}
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                placeholder="Enter your message to the user..."
                value={contactMessage}
                onChange={(e) => setContactMessage(e.target.value)}
                className="min-h-[150px]"
              />
              <p className="text-xs text-muted-foreground">
                Be professional and clear about any policy violations or
                requested changes.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsContactDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleContactUser}
              disabled={!contactMessage.trim()}
              className="flex items-center gap-1"
            >
              <RiMailLine className="h-4 w-4" />
              Send Message
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default ReportedContentManagementPage;
