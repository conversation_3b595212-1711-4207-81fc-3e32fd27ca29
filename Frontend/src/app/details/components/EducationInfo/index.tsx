/**
 * @file index.tsx
 * @description React component for EducationInfo
 */
import { useFormContext } from 'react-hook-form';

import { HelpCircle } from 'lucide-react';

import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { FormControl } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import {
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export function EducationInfo({
  collegesData,
}: {
  collegesData: { id: string; name: string }[];
}) {
  const {
    register,
    formState: { errors },
    control,
  } = useFormContext();

  return (
    <div className="space-y-4">
      <div>
        <FormField
          control={control}
          name="subjectId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1">
                College{' '}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Select College </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </FormLabel>
              <Select
                value={field.value}
                onValueChange={(value) => {
                  field.onChange(value);
                }}
              >
                <FormControl>
                  <SelectTrigger className="focus:ring-primary/20 transition-all duration-200 focus:ring-2">
                    <SelectValue placeholder="Select a college" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {collegesData.map((college) => (
                    <SelectItem key={college.id} value={college.id}>
                      {college.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div>
        <Label htmlFor="graduationYear">Graduation Year</Label>
        <Input
          id="graduationYear"
          type="number"
          {...register('graduationYear', {
            setValueAs: (v) => parseInt(v, 10) || new Date().getFullYear(),
          })}
        />
        {errors.graduationYear && (
          <p className="mt-1 text-sm text-destructive">
            {errors.graduationYear.message as string}
          </p>
        )}
      </div>
    </div>
  );
}
