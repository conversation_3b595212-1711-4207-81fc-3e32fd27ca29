/**
 * @file page.tsx
 * @description Next.js page for details route
 */
'use client';

import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

import { useAxiosPut } from '@/hooks/useAxios';

import { useRouter } from 'next/navigation';

import { yupResolver } from '@hookform/resolvers/yup';
import { User } from '@supabase/supabase-js';

import { Button } from '@/components/ui/button';
import Loader from '@/components/Loader';
import { useAxiosGet } from '@/hooks/useAxios';
import { setUser } from '@/lib/features/user/userSlice';
import { detailsSchema } from '@/lib/validations';
import { IUser } from '@/types';
import { createClient } from '@/utils/supabase/client';

import { ContactInfo } from './components/ContactInfo';
import { EducationInfo } from './components/EducationInfo';
import { PersonalInfo } from './components/PersonalInfo';
import { ProfessionalInfo } from './components/ProfessionalInfo';
import { Stepper } from './components/Stepper';

/**
 * @file page.tsx
 * @description Next.js page for details route
 */

const steps = [
  'Personal Info',
  'Contact Info',
  'Professional Info',
  'Education Info',
];

// Add explicit type for form values
interface IFormValues {
  full_name: string;
  username: string;
  bio?: string;
  avatarUrl?: string;
  address?: string;
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
  websiteUrl?: string;
  specialization?: string;
  skills?: string[];
  experienceLevel?: 'beginner' | 'intermediate' | 'advanced';
  college?: string;
  graduationYear?: number;
}

// Update stepFields definition
const stepFields: (keyof IFormValues)[][] = [
  ['full_name', 'username', 'bio', 'avatarUrl'],
  ['address', 'githubUrl', 'linkedinUrl', 'twitterUrl', 'websiteUrl'],
  ['specialization', 'skills', 'experienceLevel'],
  ['college', 'graduationYear'],
];

// Add isLastStep helper
const isLastStep = (step: number) => step === steps.length - 1;

export default function ProfilePage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const supabase = createClient();

  const [currentStep, setCurrentStep] = useState(0);
  const [supabaseUser, setSupabaseUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // API hooks
  const [postUserDetails] = useAxiosPut<{ user: IUser }>('/users/me');
  const [getUser, { data: userData, isLoading: isGetUserLoading }] =
    useAxiosGet<{ user: IUser }>('/users/me');
  const [getColleges, { data: collegesData }] = useAxiosGet<{
    colleges: Array<{ id: string; name: string }>;
  }>('/colleges');

  const methods = useForm<IFormValues>({
    resolver: yupResolver(detailsSchema),
    defaultValues: {
      full_name: '',
      username: '',
      bio: '',
      avatarUrl: '',
      address: '',
      githubUrl: '',
      linkedinUrl: '',
      twitterUrl: '',
      websiteUrl: '',
      specialization: '',
      college: '',
      graduationYear: new Date().getFullYear(),
      skills: [] as string[],
      experienceLevel: undefined,
    },
  });

  const onSubmit = async (data: IFormValues) => {
    try {
      setIsLoading(true);
      const response = await postUserDetails(data);
      if (response?.data?.user) {
        dispatch(setUser({ user: response?.data?.user }));
        toast.success('Profile updated successfully');
        router.push('/dashboard');
      } else {
        console.error('Error updating user details');
        toast.error('Error updating user details');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Error updating profile');
    } finally {
      setIsLoading(false);
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const handleStepClick = async (stepIndex: number) => {
    if (stepIndex === currentStep) return;

    if (stepIndex < currentStep) {
      setCurrentStep(stepIndex);
    } else {
      let allValid = true;
      for (let i = currentStep; i < stepIndex; i++) {
        const valid = await methods.trigger(stepFields[i]);
        if (!valid) {
          allValid = false;
          break;
        }
      }
      if (allValid) setCurrentStep(stepIndex);
    }
  };

  const getCurrentUser = async () => {
    try {
      const response = await getUser();
      if (response?.data?.user) {
        dispatch(setUser({ user: response.data.user }));
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      toast.error('Error fetching user details');
    }
  };

  const getSupabaseUser = async () => {
    try {
      const { data, error } = await supabase.auth.getUser();
      if (data) {
        setSupabaseUser(data.user);
      }
      if (error) {
        console.error(error);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const fetchColleges = async () => {
    try {
      const response = await getColleges();
      if (!response?.data?.colleges) {
        console.error('No colleges data received');
      }
    } catch (error) {
      console.error('Error fetching colleges:', error);
      toast.error('Error loading colleges');
    }
  };

  useEffect(() => {
    getCurrentUser();
    getSupabaseUser();
    fetchColleges();
  }, []);

  useEffect(() => {
    if (userData?.user) {
      // Check if user has already completed their profile
      const hasCompletedProfile = Boolean(
        userData.user.fullName &&
          userData.user.username &&
          userData.user.specialization,
      );

      dispatch(
        setUser({
          detailsComplete: hasCompletedProfile,
          user: userData.user,
        }),
      );

      // Only redirect to dashboard if user has completed their profile
      if (hasCompletedProfile) {
        router.push('/dashboard');
      }
    }
  }, [userData, dispatch, router]);

  useEffect(() => {
    if (supabaseUser) {
      methods.reset({
        full_name: supabaseUser.user_metadata?.full_name || '',
        username: supabaseUser.user_metadata?.preferred_username || '',
        avatarUrl: supabaseUser.user_metadata?.avatar_url || '',
      });
    }
  }, [supabaseUser, methods]);

  const handleNext = async (data: IFormValues) => {
    if (isLastStep(currentStep)) {
      await onSubmit(data);
    } else {
      setCurrentStep((prev) => prev + 1);
    }
  };

  if (isGetUserLoading && !userData?.user) {
    return <Loader type="SiteLoader" />;
  }

  return (
    <div className="container mx-auto my-auto w-full max-w-4xl rounded-lg bg-lightSecondary py-8 shadow-lg">
      <div className="flex justify-center pb-5">
        <span className="rounded-lg bg-lightSecondary px-2 py-1">
          {supabaseUser?.email}
        </span>
      </div>
      <Stepper
        steps={steps}
        currentStep={currentStep}
        onStepClick={handleStepClick}
      />
      <FormProvider {...methods}>
        <form
          onSubmit={methods.handleSubmit(handleNext)}
          className="mt-8 space-y-8"
        >
          {currentStep === 0 && <PersonalInfo />}
          {currentStep === 1 && <ContactInfo />}
          {currentStep === 2 && <ProfessionalInfo />}
          {currentStep === 3 && (
            <EducationInfo collegesData={collegesData?.colleges || []} />
          )}
          <div className="flex justify-between">
            <Button
              type="button"
              onClick={prevStep}
              disabled={currentStep === 0 || isLoading}
              className="text-white hover:bg-primary2"
            >
              Previous
            </Button>
            <Button
              type="submit"
              className="text-white hover:bg-primary2"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader type="Spin" className="mr-2 h-4 w-4" />
                  {isLastStep(currentStep) ? 'Submitting...' : 'Loading...'}
                </>
              ) : isLastStep(currentStep) ? (
                'Submit'
              ) : (
                'Next'
              )}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}
