@tailwind base;
@tailwind components;
@tailwind utilities;

/* Date Picker Custom Styling */
.calendar-themed,
.react-datepicker {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
  border-color: var(--primary) !important;
  border-width: 1px;
  font-family: inherit;
  border-radius: var(--radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.calendar-wrapper-themed {
  background-color: transparent !important;
}

.react-datepicker__triangle {
  display: none;
}

.react-datepicker__header {
  background-color: rgba(131, 0, 184, 0.1) !important; /* primary/10 */
  color: hsl(var(--card-foreground)) !important;
  border-bottom-color: var(--primary) !important;
  border-bottom-width: 1px;
  padding-top: 0.75rem;
}

.react-datepicker__current-month,
.react-datepicker__day-name {
  color: hsl(var(--card-foreground)) !important;
  font-weight: 500;
}

.react-datepicker__day {
  color: hsl(var(--card-foreground)) !important;
  border-radius: var(--radius);
  margin: 0.2rem;
}

.react-datepicker__day:hover {
  background-color: rgba(131, 0, 184, 0.15) !important; /* primary/15 */
  color: hsl(var(--card-foreground)) !important;
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background-color: var(--primary) !important;
  color: white !important;
  font-weight: 600;
}

.react-datepicker__day--disabled {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.5;
}

.react-datepicker__month {
  margin: 0.4rem;
}

.react-datepicker__day--outside-month {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.6;
}

/* Today's date highlight */
.react-datepicker__day--today {
  border: 1px solid var(--primary);
  background-color: transparent;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  :root {
    --bg-color: rgb(244 242 238);

    --background: 0 0% 100%;

    --foreground: 224 71.4% 4.1%;

    --card: 0 0% 100%;

    --card-foreground: 224 71.4% 4.1%;

    --popover: 0 0% 100%;

    --popover-foreground: 224 71.4% 4.1%;

    /* --primary: 220.9 39.3% 11%; */

    --primary: #8300b8;

    --primary2: #690091;

    --primary-light: #8300b826;

    --primary-foreground: 210 20% 98%;

    --secondary: 220 14.3% 95.9%;

    --secondary-foreground: 220.9 39.3% 11%;

    --muted: 220 14.3% 95.9%;

    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;

    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;

    --input: 220 13% 91%;

    --ring: 224 71.4% 4.1%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem;

    --red: 357 54% 43%;

    --dark: rgb(29, 29, 31);

    --dark-secondary: black;

    --light: rgb(245, 245, 247);

    --light-secondary: white;

    --gray-text: #6b7280;
  }
  .dark {
    --bg-color: rgb(0, 0, 0);

    --background: 224 71.4% 4.1%;

    --foreground: 210 20% 98%;

    --card: 224 71.4% 4.1%;

    --card-foreground: 210 20% 98%;

    --popover: 224 71.4% 4.1%;

    --popover-foreground: 210 20% 98%;

    /* --primary: 210 20% 98%; */

    --primary-foreground: 220.9 39.3% 11%;

    --secondary: 215 27.9% 16.9%;

    --secondary-foreground: 210 20% 98%;

    --muted: 215 27.9% 16.9%;

    --muted-foreground: 217.9 10.6% 64.9%;

    --accent: 215 27.9% 16.9%;

    --accent-foreground: 210 20% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 210 20% 98%;

    --border: 215 27.9% 16.9%;

    --input: 215 27.9% 16.9%;

    --ring: 216 12.2% 83.9%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%;

    --dark: rgb(245, 245, 247);

    /* --light: #1c1c1c; */

    --light: #1e293b;

    --light-secondary: rgb(27 31 35);

    /* --light: black; */

    /* --light: #0f0f0f; */

    /* --light: #282828; */

    /* --light: rgb(29, 29, 31); */

    --dark-secondary: white;

    /* --light-secondary: #141619; */
    /* --light-secondary: #2c2e3a; */
    /* --light-secondary: #111827; -- last used */

    /* --gray-text: #6b7280; */
  }
}
