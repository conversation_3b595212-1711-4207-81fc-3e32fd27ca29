.container {
  max-width: 800px;
  margin: 0 auto;
}

.bg-light {
  background-color: #ffffff;
}

.dark\:bg-gray-800 {
  background-color: #2d3748;
}

.text-gray-900 {
  color: #1a202c;
}

.dark\:text-gray-100 {
  color: #f7fafc;
}

.text-gray-700 {
  color: #4a5568;
}

.dark\:text-gray-300 {
  color: #e2e8f0;
}

.border-gray-300 {
  border-color: #d2d6dc;
}

.dark\:border-gray-600 {
  border-color: #4a5568;
}

.rounded-md {
  border-radius: 0.375rem;
}

.shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-blue-500 {
  color: #4299e1;
}

.hover\:underline:hover {
  text-decoration: underline;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.block {
  display: block;
}

.w-full {
  width: 100%;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
