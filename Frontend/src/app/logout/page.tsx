/**
 * @file page.tsx
 * @description Next.js page for logout route
 */
'use client';

import { useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';

import { useRouter } from 'next/navigation';

import { clearUser } from '@/lib/features/user/userSlice';
import { createClient } from '@/utils/supabase/client';

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

/**
 * @file page.tsx
 * @description Next.js page for logout route
 */

export default function Logout() {
  const router = useRouter();
  const dispatch = useDispatch();
  const supabase = createClient();
  const logoutExecuted = useRef(false);

  const logout = async () => {
    if (logoutExecuted.current) return;
    logoutExecuted.current = true;

    try {
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // Clear Redux state
      dispatch(clearUser());

      // Clear any cookies
      document.cookie = 'token=; Max-Age=0; path=/;';

      // Redirect to login page
      router.push('/auth/login');
      // Delay the toast to ensure it happens after navigation
      setTimeout(() => {
        toast.success('Logged out successfully');
      }, 100);
    } catch (error) {
      console.error('Error during logout:', error);
      toast.error('Error during logout. Please try again.');
    }
  };

  useEffect(() => {
    logout();

    // Cleanup function
    return () => {
      logoutExecuted.current = true;
    };
  }, []);

  return null;
}
