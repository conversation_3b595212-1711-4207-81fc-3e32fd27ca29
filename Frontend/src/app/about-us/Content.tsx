'use client';

import Image from 'next/image';

import { motion } from 'framer-motion';

import { companyInfo } from '@/constants';

export default function AboutContent() {
  return (
    <main className="bg-background py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <motion.div
          className="mb-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="mb-4 text-4xl font-bold text-foreground md:text-5xl lg:text-6xl">
            About <span className="text-primary">{companyInfo.name}</span>
          </h1>
          <p className="mx-auto max-w-3xl text-lg text-muted-foreground">
            We&apos;re on a mission to transform engineering education and help
            students build successful careers through practical learning and
            community support.
          </p>
        </motion.div>

        {/* Our Mission Section */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="rounded-xl border border-border bg-card p-8 shadow-sm">
            <h2 className="mb-6 text-3xl font-bold text-foreground">
              Our Mission
            </h2>
            <p className="text-lg text-muted-foreground">
              Our mission is to empower individuals by providing them with the
              tools and resources they need to excel in their engineering
              careers. We strive to create a supportive and collaborative
              environment where learners can grow and achieve their goals.
            </p>
          </div>
        </motion.section>

        {/* Our History Section */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="rounded-xl border border-border bg-card p-8 shadow-sm">
            <h2 className="mb-6 text-3xl font-bold text-foreground">
              Our History
            </h2>
            <p className="text-lg text-muted-foreground">
              Founded in 2024, our platform has grown to become a trusted
              resource for engineering professionals and students. Over the
              years, we have helped thousands of individuals enhance their
              skills and advance their careers through our comprehensive
              resources and community support.
            </p>
          </div>
        </motion.section>

        {/* Team Section */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <h2 className="mb-10 text-center text-3xl font-bold text-foreground">
            Our Team
          </h2>
          <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {[
              {
                name: 'Shailesh Chaudhari',
                role: 'Founder & CEO',
                image: 'https://shaileshchaudhari.vercel.app/Images/home.webp',
              },
            ].map((member, index) => (
              <motion.div
                key={index}
                className="flex flex-col items-center rounded-xl border border-border bg-card p-6 text-center shadow-sm transition-all hover:shadow-md"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
              >
                <div className="mb-4 h-24 w-24 overflow-hidden rounded-full">
                  <Image
                    src={member.image}
                    alt={member.name}
                    width={96}
                    height={96}
                    className="h-full w-full object-cover"
                    unoptimized
                  />
                </div>
                <h3 className="mb-1 text-lg font-bold text-foreground">
                  {member.name}
                </h3>
                <p className="text-sm text-muted-foreground">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Call to Action */}
        <motion.section
          className="rounded-xl border border-border bg-card p-10 text-center shadow-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <h2 className="mb-4 text-3xl font-bold text-foreground">
            Join Our Community
          </h2>
          <p className="mx-auto mb-6 max-w-2xl text-muted-foreground">
            Be part of a growing community of engineering students and
            professionals. Start your journey with {companyInfo.name} today.
          </p>
          <a
            href="/auth/register"
            className="inline-flex items-center rounded-full bg-primary px-6 py-3 text-white transition-colors hover:bg-primary2"
          >
            Get Started Free
          </a>
        </motion.section>
      </div>
    </main>
  );
}
