/**
 * @file utils.ts
 * @description
 */
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };

    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

export function calculateProgressPercentage(
  completed: number,
  total: number,
): number {
  if (total === 0) return 0;
  const percentage = Math.round((completed / total) * 100);
  return Math.min(100, Math.max(0, percentage));
}

/**
 * Formats a date into a human-readable string
 *
 * This utility converts a Date object or date string into a formatted date string
 * using the browser's toLocaleDateString method with US English locale.
 *
 * @example
 * // Returns "Jan 1, 2023"
 * formatDate("2023-01-01T00:00:00Z")
 *
 * @example
 * // Returns "Jan 1, 2023" (using Date object)
 * formatDate(new Date(2023, 0, 1))
 *
 * @param date - The date to format (string or Date object)
 * @returns A formatted date string in the format "MMM D, YYYY"
 */
export function formatDate(date: string | Date): string {
  // Convert string dates to Date objects
  const d = typeof date === 'string' ? new Date(date) : date;

  // Format the date using US locale and specified options
  return d.toLocaleDateString('en-US', {
    year: 'numeric', // 2023
    month: 'short', // Jan
    day: 'numeric', // 1
  });
}

/**
 * Formats a number with K/M suffix for thousands/millions
 *
 * This utility converts large numbers into a more readable format by adding
 * K for thousands and M for millions, with one decimal place of precision.
 *
 * @example
 * // Returns "1.5K"
 * formatNumber(1500)
 *
 * @example
 * // Returns "2.3M"
 * formatNumber(2300000)
 *
 * @example
 * // Returns "42" (numbers less than 1000 are returned as is)
 * formatNumber(42)
 *
 * @param num - The number to format
 * @returns A formatted string with K/M suffix for large numbers
 */
export function formatNumber(num: number): string {
  // Handle millions (1,000,000+)
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }

  // Handle thousands (1,000 - 999,999)
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }

  // Handle small numbers and edge cases
  return num?.toString() || '';
}

/**
 * Truncates text to a specified maximum length and adds ellipsis
 *
 * This utility shortens long text to the specified maximum length and
 * adds an ellipsis (...) at the end to indicate truncation.
 *
 * @example
 * // Returns "Hello..."
 * truncateText("Hello world", 5)
 *
 * @example
 * // Returns "Hello world" (no truncation needed)
 * truncateText("Hello world", 20)
 *
 * @example
 * // Returns "" (empty string for null/undefined input)
 * truncateText(null, 10)
 *
 * @param text - The text to truncate
 * @param maxLength - The maximum length to allow before truncating
 * @returns The truncated text with ellipsis or the original text if short enough
 */
export function truncateText(text: string, maxLength: number): string {
  // Handle empty or null/undefined text
  if (!text) return '';

  // Return the original text if it's already short enough
  if (text.length <= maxLength) return text;

  // Truncate the text and add ellipsis
  return text.slice(0, maxLength) + '...';
}

/**
 * Extracts initials from a name (up to 2 characters)
 *
 * This utility extracts the first letter of each word in a name and
 * combines up to the first two letters to create initials.
 *
 * @example
 * // Returns "JD"
 * getInitials("John Doe")
 *
 * @example
 * // Returns "JS" (only first two words are used)
 * getInitials("John Smith Johnson")
 *
 * @example
 * // Returns "J"
 * getInitials("John")
 *
 * @example
 * // Returns "" (empty string for null/undefined input)
 * getInitials("")
 *
 * @param name - The name to extract initials from
 * @returns The uppercase initials (up to 2 characters)
 */
export function getInitials(name: string): string {
  // Handle empty or null/undefined name
  if (!name) return '';

  // Split the name by spaces, extract first letter of each part,
  // take up to 2 parts, join them, and convert to uppercase
  return name
    .split(' ') // Split into words
    .map((part) => part.charAt(0)) // Get first letter of each word
    .slice(0, 2) // Take up to 2 initials
    .join('') // Join them together
    .toUpperCase(); // Convert to uppercase
}
