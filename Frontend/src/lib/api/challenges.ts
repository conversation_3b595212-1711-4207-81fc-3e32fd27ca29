/**
 * @file challenges.ts
 * @description
 */
import axios from 'axios';

// Define the challenge interface
interface IChallenge {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  category: string;
  points: number;
  tags?: string[];
  created_at?: string;
  updated_at?: string;
}

// Get a challenge by ID
export async function getChallengeById(id: string): Promise<IChallenge | null> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    const response = await axios.get(`${apiUrl}/challenges/${id}`);

    if (response.data && response.data.challenge) {
      return response.data.challenge;
    }

    return null;
  } catch (error) {
    console.error('Error fetching challenge:', error);
    return null;
  }
}

// Get all challenges with optional filters
export async function getChallenges(
  params?: Record<string, any>,
): Promise<IChallenge[]> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    const response = await axios.get(`${apiUrl}/challenges`, { params });

    if (response.data && response.data.challenges) {
      return response.data.challenges;
    }

    return [];
  } catch (error) {
    console.error('Error fetching challenges:', error);
    return [];
  }
}
