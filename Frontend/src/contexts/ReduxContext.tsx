/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */
'use client';

import { Provider } from 'react-redux';

import { store } from '@/lib/store';

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

/**
 * @file ReduxContext.tsx
 * @description React context for reduxcontext state management
 */

export const ReduxProvider = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>{children}</Provider>
);
