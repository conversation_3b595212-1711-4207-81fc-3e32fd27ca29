/**
 * @file useChallengeService.ts
 * @description Custom React hook for challengeservice functionality
 */
import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';
import { getDefaultCode } from '@/utils/codeTemplates';

// Types
export interface ChallengeBoilerplate {
  language: string;
  code: string;
  challenge_id: string;
}

export interface TestCase {
  id: string;
  challenge_id: string;
  input: string;
  expected_output: string;
  is_hidden: boolean;
  name?: string;
}

export interface ChallengeSubmission {
  id: string;
  user_id: string;
  challenge_id: string;
  code: string;
  language: string;
  status: string;
  runtime_ms: number;
  memory_used_kb: number;
  feedback: string;
  score: number;
  created_at: string;
  test_results?: {
    passed: boolean;
    name?: string;
    input?: string;
    expected_output?: string;
    actual_output?: string;
    error?: string;
    is_hidden?: boolean;
  }[];
}

// Custom hooks for challenge operations

// Hook to fetch boilerplate code for a specific challenge and language
export const useBoilerplate = (challengeId: string, language: string) => {
  const [getBoilerplate] = useAxiosGet<{ boilerplate: ChallengeBoilerplate }>(
    `/challenges/${challengeId}/boilerplates/${language}`,
  );

  const fetchBoilerplate = async (): Promise<string> => {
    try {
      const response = await getBoilerplate();
      if (response.data && response.data.boilerplate) {
        return response.data.boilerplate.code;
      }
      // If no boilerplate found, return default code
      return getDefaultCode(language);
    } catch (error) {
      console.error('Error fetching boilerplate:', error);
      // Fallback to default code template
      return getDefaultCode(language);
    }
  };

  return { fetchBoilerplate };
};

// Hook to fetch all available boilerplates for a challenge
export const useAllBoilerplates = (challengeId: string) => {
  const [getAllBoilerplates] = useAxiosGet<{
    boilerplates: ChallengeBoilerplate[];
  }>(`/challenges/${challengeId}/boilerplates`);

  const fetchAllBoilerplates = async (): Promise<Record<string, string>> => {
    try {
      const response = await getAllBoilerplates();
      if (response.data && response.data.boilerplates) {
        // Convert array to object with language as key
        return response.data.boilerplates.reduce(
          (acc, boilerplate) => {
            acc[boilerplate.language] = boilerplate.code;
            return acc;
          },
          {} as Record<string, string>,
        );
      }
      return {};
    } catch (error) {
      console.error('Error fetching all boilerplates:', error);
      return {};
    }
  };

  return { fetchAllBoilerplates };
};

// Hook to fetch test cases for a challenge
export const useTestCases = (challengeId: string) => {
  const [getTestCases] = useAxiosGet<{ test_cases: TestCase[] }>(
    `/challenges/${challengeId}/test-cases`,
  );

  const fetchTestCases = async (): Promise<TestCase[]> => {
    try {
      const response = await getTestCases();
      if (response.data && response.data.test_cases) {
        return response.data.test_cases;
      }
      return [];
    } catch (error) {
      console.error('Error fetching test cases:', error);
      return [];
    }
  };

  return { fetchTestCases };
};

// Hook to fetch submission history for a challenge
export const useSubmissionHistory = (challengeId: string) => {
  const [getSubmissions] = useAxiosGet<{ submissions: ChallengeSubmission[] }>(
    `/challenges/${challengeId}/submissions`,
  );

  const fetchSubmissionHistory = async (): Promise<ChallengeSubmission[]> => {
    try {
      const response = await getSubmissions();
      if (response.data && response.data.submissions) {
        return response.data.submissions;
      }
      return [];
    } catch (error) {
      console.error('Error fetching submission history:', error);
      return [];
    }
  };

  return { fetchSubmissionHistory };
};

// Hook to submit a solution
export const useSubmitSolution = (challengeId: string) => {
  const [submitSolution] = useAxiosPost<{ submission: ChallengeSubmission }>(
    `/challenges/${challengeId}/submit`,
  );

  const submitChallengeSolution = async (
    code: string,
    language: string,
  ): Promise<ChallengeSubmission> => {
    try {
      const response = await submitSolution({ code, language });
      if (response.data && response.data.submission) {
        return response.data.submission;
      }
      throw new Error('No submission data returned');
    } catch (error) {
      console.error('Error submitting solution:', error);
      throw error;
    }
  };

  return { submitChallengeSolution };
};
