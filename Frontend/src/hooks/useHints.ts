/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */
'use client';

import { useCallback, useEffect, useState } from 'react';

import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

/**
 * @file useHints.ts
 * @description Custom React hook for hints functionality
 */

export interface Hint {
  id: string;
  challenge_id: string;
  content: string;
  level: number;
  points_penalty: number;
  is_unlocked: boolean;
}

export interface HintUsage {
  challengeId: string;
  hintsUnlocked: string[];
  totalPenalty: number;
}

/**
 * Hook to manage hint usage for challenges
 */
export function useHints(challengeId: string) {
  const [hints, setHints] = useState<Hint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getHints] = useAxiosGet<{ hints: Hint[] }>(
    `/challenges/${challengeId}/hints`,
  );
  const [unlockHint] = useAxiosPost<{ hint: Hint }>(
    `/challenges/${challengeId}/hints/:id/unlock`,
  );

  // Fetch hints
  const fetchHints = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getHints();
      if (response.data && response.data.hints) {
        // Sort hints by level
        const sortedHints = [...response.data.hints].sort(
          (a, b) => a.level - b.level,
        );
        setHints(sortedHints);
      }
    } catch (err) {
      console.error('Error fetching hints:', err);
      setError('Failed to load hints');

      // Try to load from localStorage as fallback
      loadFromLocalStorage();
    } finally {
      setIsLoading(false);
    }
  }, [challengeId, getHints]);

  // Unlock a hint
  const unlockHintById = useCallback(
    async (hintId: string) => {
      try {
        const hint = hints.find((h) => h.id === hintId);
        if (!hint) throw new Error('Hint not found');

        // Optimistic update
        setHints((prev) =>
          prev.map((h) => (h.id === hintId ? { ...h, is_unlocked: true } : h)),
        );

        // API call
        const response = await unlockHint(
          {},
          {
            url: `/challenges/${challengeId}/hints/${hintId}/unlock`,
          },
        );

        if (response.data && response.data.hint) {
          // Update localStorage
          saveToLocalStorage(
            hints.map((h) =>
              h.id === hintId ? { ...h, is_unlocked: true } : h,
            ),
          );

          return true;
        }

        return false;
      } catch (err) {
        console.error('Error unlocking hint:', err);

        // Revert optimistic update
        setHints((prev) =>
          prev.map((h) => (h.id === hintId ? { ...h, is_unlocked: false } : h)),
        );

        throw err;
      }
    },
    [challengeId, hints, unlockHint],
  );

  // Calculate total penalty
  const calculateTotalPenalty = useCallback(() => {
    return hints
      .filter((hint) => hint.is_unlocked)
      .reduce((total, hint) => total + hint.points_penalty, 0);
  }, [hints]);

  // Get unlocked hints
  const getUnlockedHints = useCallback(() => {
    return hints.filter((hint) => hint.is_unlocked);
  }, [hints]);

  // Load hints from localStorage (fallback)
  const loadFromLocalStorage = useCallback(() => {
    try {
      const key = `challenge-${challengeId}-hints`;
      const storedHints = localStorage.getItem(key);
      if (storedHints) {
        setHints(JSON.parse(storedHints));
      }
    } catch (err) {
      console.error('Error loading hints from localStorage:', err);
    }
  }, [challengeId]);

  // Save hints to localStorage (backup)
  const saveToLocalStorage = useCallback(
    (hintsData: Hint[]) => {
      try {
        const key = `challenge-${challengeId}-hints`;
        localStorage.setItem(key, JSON.stringify(hintsData));

        // Also update hint usage tracking
        updateHintUsageTracking(hintsData);
      } catch (err) {
        console.error('Error saving hints to localStorage:', err);
      }
    },
    [challengeId],
  );

  // Update hint usage tracking
  const updateHintUsageTracking = useCallback(
    (hintsData: Hint[]) => {
      try {
        // Get current hint usage
        const hintUsageStr = localStorage.getItem('hint-usage');
        const hintUsage: HintUsage[] = hintUsageStr
          ? JSON.parse(hintUsageStr)
          : [];

        // Calculate unlocked hints and total penalty
        const unlockedHintIds = hintsData
          .filter((hint) => hint.is_unlocked)
          .map((hint) => hint.id);

        const totalPenalty = hintsData
          .filter((hint) => hint.is_unlocked)
          .reduce((total, hint) => total + hint.points_penalty, 0);

        // Update or add challenge hint usage
        const existingIndex = hintUsage.findIndex(
          (h) => h.challengeId === challengeId,
        );
        if (existingIndex >= 0) {
          hintUsage[existingIndex] = {
            challengeId,
            hintsUnlocked: unlockedHintIds,
            totalPenalty,
          };
        } else {
          hintUsage.push({
            challengeId,
            hintsUnlocked: unlockedHintIds,
            totalPenalty,
          });
        }

        // Save updated hint usage
        localStorage.setItem('hint-usage', JSON.stringify(hintUsage));
      } catch (err) {
        console.error('Error updating hint usage tracking:', err);
      }
    },
    [challengeId],
  );

  // Load hints on mount
  useEffect(() => {
    fetchHints();
  }, [fetchHints]);

  return {
    hints,
    isLoading,
    error,
    fetchHints,
    unlockHint: unlockHintById,
    calculateTotalPenalty,
    getUnlockedHints,
  };
}

/**
 * Hook to get hint usage statistics
 */
export function useHintStats() {
  const [stats, setStats] = useState<{
    totalHintsUsed: number;
    totalPointsLost: number;
    challengesWithHints: number;
  }>({
    totalHintsUsed: 0,
    totalPointsLost: 0,
    challengesWithHints: 0,
  });

  useEffect(() => {
    try {
      const hintUsageStr = localStorage.getItem('hint-usage');
      if (hintUsageStr) {
        const hintUsage: HintUsage[] = JSON.parse(hintUsageStr);

        const totalHintsUsed = hintUsage.reduce(
          (total, usage) => total + usage.hintsUnlocked.length,
          0,
        );

        const totalPointsLost = hintUsage.reduce(
          (total, usage) => total + usage.totalPenalty,
          0,
        );

        const challengesWithHints = hintUsage.length;

        setStats({
          totalHintsUsed,
          totalPointsLost,
          challengesWithHints,
        });
      }
    } catch (err) {
      console.error('Error calculating hint stats:', err);
    }
  }, []);

  return stats;
}
