/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */
'use client';

import { useEffect, useState } from 'react';

import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

/**
 * @file useChallengeProgress.ts
 * @description Custom React hook for challengeprogress functionality
 */

export type ChallengeStatus = 'not_started' | 'in_progress' | 'completed';

export interface ChallengeProgress {
  challenge_id: string;
  status: ChallengeStatus;
  last_attempted_at?: string;
  completed_at?: string;
  attempts_count: number;
  best_score?: number;
  best_language?: string;
  best_submission_id?: string;
}

export interface UserChallengeStats {
  total_completed: number;
  total_in_progress: number;
  total_challenges: number;
  completion_percentage: number;
  average_score: number;
  favorite_language?: string;
  streak_days: number;
}

/**
 * Hook to manage user's challenge progress
 */
export const useChallengeProgress = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userProgress, setUserProgress] = useState<ChallengeProgress[]>([]);
  const [userStats, setUserStats] = useState<UserChallengeStats | null>(null);

  // API hooks
  const [getUserProgress] = useAxiosGet<{ progress: ChallengeProgress[] }>(
    '/user/challenges',
  );
  const [getUserStats] = useAxiosGet<{ stats: UserChallengeStats }>(
    '/user/challenges/stats',
  );
  const [updateProgress] = useAxiosPost<{ progress: ChallengeProgress }>(
    '/user/challenges/progress',
  );

  // Fetch user's challenge progress
  const fetchUserProgress = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getUserProgress();
      if (response.data && response.data.progress) {
        setUserProgress(response.data.progress);
      }
    } catch (err) {
      console.error('Error fetching user progress:', err);
      setError('Failed to load your challenge progress');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user's challenge statistics
  const fetchUserStats = async () => {
    try {
      const response = await getUserStats();
      if (response.data && response.data.stats) {
        setUserStats(response.data.stats);
      }
    } catch (err) {
      console.error('Error fetching user stats:', err);
      // Don't set error here to avoid blocking the UI if only stats fail
    }
  };

  // Update a challenge's progress
  const updateChallengeProgress = async (
    challengeId: string,
    status: ChallengeStatus,
    submissionId?: string,
  ) => {
    try {
      const response = await updateProgress({
        challenge_id: challengeId,
        status,
        submission_id: submissionId,
      });

      if (response.data && response.data.progress) {
        // Update local state
        setUserProgress((prev) => {
          const existingIndex = prev.findIndex(
            (p) => p.challenge_id === challengeId,
          );
          if (existingIndex >= 0) {
            // Update existing progress
            const updated = [...prev];
            updated[existingIndex] = response.data.progress;
            return updated;
          } else {
            // Add new progress
            return [...prev, response.data.progress];
          }
        });
        return response.data.progress;
      }
      return null;
    } catch (err) {
      console.error('Error updating challenge progress:', err);
      throw err;
    }
  };

  // Get progress for a specific challenge
  const getChallengeProgress = (
    challengeId: string,
  ): ChallengeProgress | null => {
    return userProgress.find((p) => p.challenge_id === challengeId) || null;
  };

  // Get challenges by status
  const getChallengesByStatus = (status: ChallengeStatus): string[] => {
    return userProgress
      .filter((p) => p.status === status)
      .map((p) => p.challenge_id);
  };

  // Load progress on mount
  useEffect(() => {
    fetchUserProgress();
    fetchUserStats();
  }, []);

  return {
    isLoading,
    error,
    userProgress,
    userStats,
    fetchUserProgress,
    fetchUserStats,
    updateChallengeProgress,
    getChallengeProgress,
    getChallengesByStatus,
  };
};

/**
 * Hook to manage a single challenge's progress
 */
export const useSingleChallengeProgress = (challengeId: string) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<ChallengeProgress | null>(null);

  // API hooks
  const [getChallengeProgress] = useAxiosGet<{ progress: ChallengeProgress }>(
    `/user/challenges/${challengeId}/progress`,
  );
  const [updateProgress] = useAxiosPost<{ progress: ChallengeProgress }>(
    `/user/challenges/${challengeId}/progress`,
  );

  // Fetch challenge progress
  const fetchProgress = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getChallengeProgress();
      if (response.data && response.data.progress) {
        setProgress(response.data.progress);
      }
    } catch (err) {
      console.error(
        `Error fetching progress for challenge ${challengeId}:`,
        err,
      );
      setError('Failed to load challenge progress');
    } finally {
      setIsLoading(false);
    }
  };

  // Update challenge progress
  const updateChallengeProgress = async (
    status: ChallengeStatus,
    submissionId?: string,
  ) => {
    try {
      const response = await updateProgress({
        status,
        submission_id: submissionId,
      });

      if (response.data && response.data.progress) {
        setProgress(response.data.progress);
        return response.data.progress;
      }
      return null;
    } catch (err) {
      console.error(
        `Error updating progress for challenge ${challengeId}:`,
        err,
      );
      throw err;
    }
  };

  // Load progress on mount
  useEffect(() => {
    fetchProgress();
  }, [challengeId]);

  return {
    isLoading,
    error,
    progress,
    fetchProgress,
    updateChallengeProgress,
  };
};

/**
 * Fallback implementation using localStorage when backend is not available
 */
export const useLocalChallengeProgress = () => {
  const [userProgress, setUserProgress] = useState<ChallengeProgress[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load progress from localStorage
  const loadProgressFromStorage = () => {
    if (typeof window === 'undefined') return;

    try {
      const storedProgress = localStorage.getItem('challengeProgress');
      if (storedProgress) {
        setUserProgress(JSON.parse(storedProgress));
      }
    } catch (err) {
      console.error('Error loading progress from localStorage:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Save progress to localStorage
  const saveProgressToStorage = (progress: ChallengeProgress[]) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem('challengeProgress', JSON.stringify(progress));
    } catch (err) {
      console.error('Error saving progress to localStorage:', err);
    }
  };

  // Update a challenge's progress
  const updateChallengeProgress = (
    challengeId: string,
    status: ChallengeStatus,
  ) => {
    const now = new Date().toISOString();

    setUserProgress((prev) => {
      const existingIndex = prev.findIndex(
        (p) => p.challenge_id === challengeId,
      );
      let updatedProgress: ChallengeProgress[];

      if (existingIndex >= 0) {
        // Update existing progress
        updatedProgress = [...prev];
        const existing = updatedProgress[existingIndex];

        updatedProgress[existingIndex] = {
          ...existing,
          status,
          attempts_count: existing.attempts_count + 1,
          last_attempted_at: now,
          ...(status === 'completed' &&
            !existing.completed_at && { completed_at: now }),
        };
      } else {
        // Add new progress
        const newProgress: ChallengeProgress = {
          challenge_id: challengeId,
          status,
          attempts_count: 1,
          last_attempted_at: now,
          ...(status === 'completed' && { completed_at: now }),
        };
        updatedProgress = [...prev, newProgress];
      }

      // Save to localStorage
      saveProgressToStorage(updatedProgress);
      return updatedProgress;
    });
  };

  // Get progress for a specific challenge
  const getChallengeProgress = (
    challengeId: string,
  ): ChallengeProgress | null => {
    return userProgress.find((p) => p.challenge_id === challengeId) || null;
  };

  // Get challenges by status
  const getChallengesByStatus = (status: ChallengeStatus): string[] => {
    return userProgress
      .filter((p) => p.status === status)
      .map((p) => p.challenge_id);
  };

  // Calculate user stats
  const getUserStats = (): UserChallengeStats => {
    const completed = userProgress.filter(
      (p) => p.status === 'completed',
    ).length;
    const inProgress = userProgress.filter(
      (p) => p.status === 'in_progress',
    ).length;
    const total = userProgress.length;

    return {
      total_completed: completed,
      total_in_progress: inProgress,
      total_challenges: total,
      completion_percentage: total > 0 ? (completed / total) * 100 : 0,
      average_score: 0, // Not tracked in localStorage
      streak_days: 0, // Not tracked in localStorage
    };
  };

  // Load progress on mount
  useEffect(() => {
    loadProgressFromStorage();
  }, []);

  return {
    isLoading,
    error: null,
    userProgress,
    userStats: getUserStats(),
    fetchUserProgress: loadProgressFromStorage,
    fetchUserStats: () => {}, // No-op for localStorage
    updateChallengeProgress,
    getChallengeProgress,
    getChallengesByStatus,
  };
};

/**
 * Combined hook that uses backend API if available, falls back to localStorage
 */
export const useCombinedChallengeProgress = () => {
  const apiProgress = useChallengeProgress();
  const localProgress = useLocalChallengeProgress();
  const [useLocalStorage, setUseLocalStorage] = useState(false);

  // Determine whether to use localStorage based on API errors
  useEffect(() => {
    if (apiProgress.error) {
      setUseLocalStorage(true);
    }
  }, [apiProgress.error]);

  return useLocalStorage ? localProgress : apiProgress;
};
