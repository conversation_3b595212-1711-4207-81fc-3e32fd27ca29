/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */
'use client';

import { useCallback, useEffect, useState } from 'react';

import { toast } from 'sonner';

import { useAxiosDelete, useAxiosGet, useAxiosPost } from '@/hooks/useAxios';

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

/**
 * @file useBookmarks.ts
 * @description Custom React hook for bookmarks functionality
 */

export interface Bookmark {
  id: string;
  user_id: string;
  challenge_id: string;
  created_at: string;
}

/**
 * Hook to manage challenge bookmarks
 */
export function useBookmarks() {
  const [bookmarks, setBookmarks] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getBookmarks] = useAxiosGet<{ bookmarks: Bookmark[] }>(
    '/user/bookmarks/challenges',
  );
  const [addBookmark] = useAxiosPost<{ bookmark: Bookmark }>(
    '/user/bookmarks/challenges',
  );
  const [removeBookmark] = useAxiosDelete<{ success: boolean }>(
    '/user/bookmarks/challenges',
  );

  // Fetch bookmarks
  const fetchBookmarks = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getBookmarks();
      if (response.data && response.data.bookmarks) {
        setBookmarks(
          response.data.bookmarks.map((bookmark) => bookmark.challenge_id),
        );
      }
    } catch (err) {
      console.error('Error fetching bookmarks:', err);
      setError('Failed to load your bookmarks');
      // Try to load from localStorage as fallback
      loadFromLocalStorage();
    } finally {
      setIsLoading(false);
    }
  }, [getBookmarks]);

  // Toggle bookmark
  const toggleBookmark = useCallback(
    async (challengeId: string) => {
      const isBookmarked = bookmarks.includes(challengeId);

      // Optimistic update
      setBookmarks((prev) =>
        isBookmarked
          ? prev.filter((id) => id !== challengeId)
          : [...prev, challengeId],
      );

      try {
        if (isBookmarked) {
          await removeBookmark({ challenge_id: challengeId });
          toast.success('Bookmark removed');
        } else {
          await addBookmark({ challenge_id: challengeId });
          toast.success('Challenge bookmarked');
        }
        // Save to localStorage as backup
        saveToLocalStorage(
          isBookmarked
            ? bookmarks.filter((id) => id !== challengeId)
            : [...bookmarks, challengeId],
        );
      } catch (err) {
        console.error('Error toggling bookmark:', err);
        // Revert optimistic update
        setBookmarks((prev) =>
          isBookmarked
            ? [...prev, challengeId]
            : prev.filter((id) => id !== challengeId),
        );
        toast.error('Failed to update bookmark');
      }
    },
    [bookmarks, addBookmark, removeBookmark],
  );

  // Check if a challenge is bookmarked
  const isBookmarked = useCallback(
    (challengeId: string) => {
      return bookmarks.includes(challengeId);
    },
    [bookmarks],
  );

  // Load bookmarks from localStorage (fallback)
  const loadFromLocalStorage = useCallback(() => {
    try {
      const storedBookmarks = localStorage.getItem('challengeBookmarks');
      if (storedBookmarks) {
        setBookmarks(JSON.parse(storedBookmarks));
      }
    } catch (err) {
      console.error('Error loading bookmarks from localStorage:', err);
    }
  }, []);

  // Save bookmarks to localStorage (backup)
  const saveToLocalStorage = useCallback((bookmarkIds: string[]) => {
    try {
      localStorage.setItem('challengeBookmarks', JSON.stringify(bookmarkIds));
    } catch (err) {
      console.error('Error saving bookmarks to localStorage:', err);
    }
  }, []);

  // Load bookmarks on mount
  useEffect(() => {
    fetchBookmarks();
  }, [fetchBookmarks]);

  return {
    bookmarks,
    isLoading,
    error,
    fetchBookmarks,
    toggleBookmark,
    isBookmarked,
  };
}

/**
 * Hook to manage a single challenge bookmark
 */
export function useChallengeBookmark(challengeId: string) {
  const { isBookmarked, toggleBookmark, isLoading } = useBookmarks();

  const toggleThisBookmark = useCallback(() => {
    toggleBookmark(challengeId);
  }, [challengeId, toggleBookmark]);

  return {
    isBookmarked: isBookmarked(challengeId),
    toggleBookmark: toggleThisBookmark,
    isLoading,
  };
}
