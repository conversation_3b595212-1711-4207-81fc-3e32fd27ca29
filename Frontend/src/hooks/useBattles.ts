/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */
'use client';

import { use<PERSON>allback, useEffect, useState } from 'react';

import { toast } from 'sonner';

import {
  IBattle,
  IBattleChallenge,
  IBattleLeaderboardEntry,
  IBattleProgress,
} from '@/app/coding-challenges/types/battle';
import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * @file useBattles.ts
 * @description Custom React hook for battles functionality
 */

/**
 * Hook to manage battle zone functionality
 */
export function useBattles() {
  const [battles, setBattles] = useState<IBattle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getBattles] = useAxiosGet<{ battles: IBattle[] }>('/battles');
  const [enrollInBattle] = useAxiosPost<
    { battle_id: string },
    { success: boolean }
  >('/battles/enroll');

  // Fetch all battles
  const fetchBattles = useCallback(
    async (params?: Record<string, any>) => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await getBattles({ params });
        if (response.data && response.data.battles) {
          setBattles(response.data.battles);
        }
      } catch (err) {
        console.error('Error fetching battles:', err);
        setError('Failed to load battles');

        // Use mock data for development
        if (process.env.NODE_ENV === 'development') {
          setBattles(getMockBattles());
        }
      } finally {
        setIsLoading(false);
      }
    },
    [getBattles],
  );

  // Enroll in a battle
  const enroll = useCallback(
    async (battleId: string) => {
      try {
        const response = await enrollInBattle({ battle_id: battleId });

        if (response.data && response.data.success) {
          // Update local state
          setBattles((prevBattles) =>
            prevBattles.map((battle) =>
              battle.id === battleId
                ? {
                    ...battle,
                    is_enrolled: true,
                    participants_count: battle.participants_count + 1,
                  }
                : battle,
            ),
          );

          toast.success('Successfully enrolled in battle!');
          return true;
        }

        return false;
      } catch (err) {
        console.error('Error enrolling in battle:', err);
        toast.error('Failed to enroll in battle');
        return false;
      }
    },
    [enrollInBattle],
  );

  // Get active battles
  const getActiveBattles = useCallback(() => {
    return battles.filter((battle) => battle.status === 'active');
  }, [battles]);

  // Get upcoming battles
  const getUpcomingBattles = useCallback(() => {
    return battles.filter((battle) => battle.status === 'pending');
  }, [battles]);

  // Get completed battles
  const getCompletedBattles = useCallback(() => {
    return battles.filter((battle) => battle.status === 'completed');
  }, [battles]);

  // Get enrolled battles
  const getEnrolledBattles = useCallback(() => {
    return battles.filter((battle) => battle.is_enrolled);
  }, [battles]);

  // Load battles on mount
  useEffect(() => {
    fetchBattles();
  }, [fetchBattles]);

  return {
    battles,
    isLoading,
    error,
    fetchBattles,
    enroll,
    getActiveBattles,
    getUpcomingBattles,
    getCompletedBattles,
    getEnrolledBattles,
  };
}

/**
 * Hook to manage a specific battle
 */
export function useBattle(battleId: string) {
  const [battle, setBattle] = useState<IBattle | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getBattle] = useAxiosGet<{ battle: IBattle }>(`/battles/${battleId}`);
  const [getLeaderboard] = useAxiosGet<{
    leaderboard: IBattleLeaderboardEntry[];
  }>(`/battles/${battleId}/leaderboard`);
  const [updateProgress] = useAxiosPost<
    { challenge_id: string },
    { success: boolean; progress: IBattleProgress }
  >(`/battles/${battleId}/progress`);

  // Fetch battle data
  const fetchBattle = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch battle details
      const battleResponse = await getBattle();
      if (battleResponse.data && battleResponse.data.battle) {
        setBattle(battleResponse.data.battle);
      }

      // Fetch leaderboard if battle is active or completed
      if (battleResponse.data?.battle?.status !== 'pending') {
        const leaderboardResponse = await getLeaderboard();
        if (leaderboardResponse.data && leaderboardResponse.data.leaderboard) {
          setBattle((prevBattle) =>
            prevBattle
              ? {
                  ...prevBattle,
                  leaderboard: leaderboardResponse.data.leaderboard,
                }
              : null,
          );
        }
      }
    } catch (err) {
      console.error('Error fetching battle data:', err);
      setError('Failed to load battle data');

      // Use mock data for development
      if (process.env.NODE_ENV === 'development') {
        const mockBattle = getMockBattles().find((b) => b.id === battleId);
        if (mockBattle) {
          setBattle(mockBattle);
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [battleId, getBattle, getLeaderboard]);

  // Update challenge progress
  const completeChallenge = useCallback(
    async (challengeId: string) => {
      if (!battle) return false;

      try {
        const response = await updateProgress({ challenge_id: challengeId });

        if (response.data && response.data.success) {
          // Update local state
          setBattle((prevBattle) => {
            if (!prevBattle) return null;

            // Update challenge status
            const updatedChallenges = prevBattle.challenges.map((challenge) =>
              challenge.id === challengeId
                ? { ...challenge, is_completed: true }
                : challenge,
            );

            // Unlock next challenge if available
            const completedIndex = updatedChallenges.findIndex(
              (c) => c.id === challengeId,
            );
            if (
              completedIndex >= 0 &&
              completedIndex < updatedChallenges.length - 1
            ) {
              updatedChallenges[completedIndex + 1].is_locked = false;
            }

            return {
              ...prevBattle,
              challenges: updatedChallenges,
            };
          });

          return true;
        }

        return false;
      } catch (err) {
        console.error('Error updating challenge progress:', err);
        toast.error('Failed to update progress');
        return false;
      }
    },
    [battle, updateProgress],
  );

  // Get next challenge
  const getNextChallenge = useCallback(() => {
    if (!battle) return null;

    // Find the first incomplete and unlocked challenge
    return (
      battle.challenges.find(
        (challenge) => !challenge.is_completed && !challenge.is_locked,
      ) || null
    );
  }, [battle]);

  // Load battle data on mount
  useEffect(() => {
    fetchBattle();
  }, [fetchBattle]);

  return {
    battle,
    isLoading,
    error,
    fetchBattle,
    completeChallenge,
    getNextChallenge,
  };
}

// Mock data for development
function getMockBattles(): IBattle[] {
  const now = new Date();
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);

  const lastWeek = new Date(now);
  lastWeek.setDate(lastWeek.getDate() - 7);

  return [
    {
      id: 'battle1',
      title: 'Weekly Algorithm Challenge',
      description:
        'Compete with others to solve algorithm challenges in this weekly battle.',
      status: 'active',
      start_time: yesterday.toISOString(),
      end_time: tomorrow.toISOString(),
      participants_count: 128,
      max_participants: 200,
      challenges: [
        {
          id: 'challenge1',
          title: 'Two Sum',
          description: 'Find two numbers that add up to a target.',
          difficulty: 'EASY',
          points: 100,
          order: 1,
          is_completed: true,
          is_locked: false,
        },
        {
          id: 'challenge2',
          title: 'Valid Parentheses',
          description: 'Determine if the input string has valid parentheses.',
          difficulty: 'EASY',
          points: 100,
          order: 2,
          is_completed: false,
          is_locked: false,
        },
        {
          id: 'challenge3',
          title: 'Merge Intervals',
          description: 'Merge overlapping intervals.',
          difficulty: 'MEDIUM',
          points: 200,
          order: 3,
          is_completed: false,
          is_locked: true,
        },
      ],
      leaderboard: [
        {
          user_id: 'user1',
          username: 'codemaster',
          display_name: 'Code Master',
          avatar_url: 'https://example.com/avatar1.jpg',
          points: 300,
          challenges_completed: 3,
          rank: 1,
        },
        {
          user_id: 'user2',
          username: 'algoguru',
          display_name: 'Algo Guru',
          avatar_url: 'https://example.com/avatar2.jpg',
          points: 250,
          challenges_completed: 2,
          rank: 2,
        },
        {
          user_id: 'current_user',
          username: 'you',
          display_name: 'You',
          avatar_url: 'https://example.com/avatar3.jpg',
          points: 100,
          challenges_completed: 1,
          rank: 3,
          is_current_user: true,
        },
      ],
      created_at: lastWeek.toISOString(),
      updated_at: yesterday.toISOString(),
      is_enrolled: true,
    },
    {
      id: 'battle2',
      title: 'Data Structures Showdown',
      description:
        'Test your knowledge of data structures in this competitive battle.',
      status: 'pending',
      start_time: tomorrow.toISOString(),
      end_time: new Date(tomorrow.getTime() + 86400000 * 2).toISOString(),
      participants_count: 64,
      max_participants: 150,
      challenges: [
        {
          id: 'challenge4',
          title: 'Implement Queue using Stacks',
          description: 'Implement a queue using only stacks.',
          difficulty: 'MEDIUM',
          points: 200,
          order: 1,
          is_locked: false,
        },
        {
          id: 'challenge5',
          title: 'LRU Cache',
          description:
            'Design and implement a data structure for Least Recently Used (LRU) cache.',
          difficulty: 'MEDIUM',
          points: 200,
          order: 2,
          is_locked: true,
        },
        {
          id: 'challenge6',
          title: 'Serialize and Deserialize Binary Tree',
          description:
            'Design an algorithm to serialize and deserialize a binary tree.',
          difficulty: 'HARD',
          points: 300,
          order: 3,
          is_locked: true,
        },
      ],
      created_at: yesterday.toISOString(),
      updated_at: yesterday.toISOString(),
      is_enrolled: false,
    },
    {
      id: 'battle3',
      title: 'Dynamic Programming Marathon',
      description: 'Challenge yourself with dynamic programming problems.',
      status: 'completed',
      start_time: new Date(lastWeek.getTime() - 86400000 * 7).toISOString(),
      end_time: lastWeek.toISOString(),
      participants_count: 96,
      challenges: [
        {
          id: 'challenge7',
          title: 'Climbing Stairs',
          description: 'Count the number of ways to climb n stairs.',
          difficulty: 'EASY',
          points: 100,
          order: 1,
          is_completed: true,
          is_locked: false,
        },
        {
          id: 'challenge8',
          title: 'Coin Change',
          description:
            'Find the fewest number of coins that you need to make up a given amount.',
          difficulty: 'MEDIUM',
          points: 200,
          order: 2,
          is_completed: true,
          is_locked: false,
        },
        {
          id: 'challenge9',
          title: 'Longest Increasing Subsequence',
          description:
            'Find the length of the longest subsequence of a given sequence.',
          difficulty: 'MEDIUM',
          points: 200,
          order: 3,
          is_completed: false,
          is_locked: false,
        },
      ],
      leaderboard: [
        {
          user_id: 'user3',
          username: 'dpmaster',
          display_name: 'DP Master',
          avatar_url: 'https://example.com/avatar4.jpg',
          points: 500,
          challenges_completed: 3,
          rank: 1,
        },
        {
          user_id: 'current_user',
          username: 'you',
          display_name: 'You',
          avatar_url: 'https://example.com/avatar3.jpg',
          points: 300,
          challenges_completed: 2,
          rank: 2,
          is_current_user: true,
        },
        {
          user_id: 'user4',
          username: 'coder123',
          display_name: 'Coder 123',
          avatar_url: 'https://example.com/avatar5.jpg',
          points: 100,
          challenges_completed: 1,
          rank: 3,
        },
      ],
      created_at: new Date(lastWeek.getTime() - 86400000 * 14).toISOString(),
      updated_at: lastWeek.toISOString(),
      is_enrolled: true,
    },
  ];
}
