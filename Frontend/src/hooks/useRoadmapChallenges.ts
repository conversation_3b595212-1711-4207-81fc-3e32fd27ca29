/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */
'use client';

import { useCallback, useEffect, useState } from 'react';

import { toast } from 'sonner';

import { IChallenge } from '@/app/coding-challenges/types';
import {
  IRoadmap,
  IRoadmapProgress,
} from '@/app/coding-challenges/types/roadmap';
import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 */

/**
 * @file useRoadmapChallenges.ts
 * @description Custom React hook for roadmapchallenges functionality
 *
 * Hook to manage roadmap challenges integration
 */
export function useRoadmapChallenges(roadmapId?: string) {
  const [roadmap, setRoadmap] = useState<IRoadmap | null>(null);
  const [relatedChallenges, setRelatedChallenges] = useState<IChallenge[]>([]);
  const [suggestedChallenges, setSuggestedChallenges] = useState<IChallenge[]>(
    [],
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getRoadmap] = useAxiosGet<{ roadmap: IRoadmap }>(
    roadmapId ? `/roadmaps/${roadmapId}` : '',
  );
  const [getRoadmapChallenges] = useAxiosGet<{ challenges: IChallenge[] }>(
    roadmapId ? `/roadmaps/${roadmapId}/challenges` : '',
  );
  const [getSuggestedChallenges] = useAxiosGet<{ challenges: IChallenge[] }>(
    roadmapId ? `/roadmaps/${roadmapId}/suggested-challenges` : '',
  );
  const [updateChallengeProgress] = useAxiosPost<
    { success: boolean },
    IRoadmapProgress
  >('/roadmaps/progress');

  // Fetch roadmap data
  const fetchRoadmapData = useCallback(async () => {
    if (!roadmapId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Fetch roadmap details
      const roadmapResponse = await getRoadmap();
      if (roadmapResponse.data && roadmapResponse.data.roadmap) {
        setRoadmap(roadmapResponse.data.roadmap);
      }

      // Fetch related challenges
      const challengesResponse = await getRoadmapChallenges();
      if (challengesResponse.data && challengesResponse.data.challenges) {
        setRelatedChallenges(challengesResponse.data.challenges);
      }

      // Fetch suggested challenges
      const suggestedResponse = await getSuggestedChallenges();
      if (suggestedResponse.data && suggestedResponse.data.challenges) {
        setSuggestedChallenges(suggestedResponse.data.challenges);
      }
    } catch (err) {
      console.error('Error fetching roadmap data:', err);
      setError('Failed to load roadmap data');

      // Use mock data for development
      if (process.env.NODE_ENV === 'development') {
        const mockData = getMockRoadmapData(roadmapId);
        setRoadmap(mockData.roadmap);
        setRelatedChallenges(mockData.challenges);
        setSuggestedChallenges(mockData.suggestedChallenges);
      }
    } finally {
      setIsLoading(false);
    }
  }, [roadmapId, getRoadmap, getRoadmapChallenges, getSuggestedChallenges]);

  // Update challenge progress in roadmap
  const updateProgress = useCallback(
    async (
      topicId: string,
      challengeId: string,
      status: 'not_started' | 'in_progress' | 'completed',
      progress?: number,
    ) => {
      if (!roadmapId) return false;

      try {
        const progressData: IRoadmapProgress = {
          roadmap_id: roadmapId,
          topic_id: topicId,
          challenge_id: challengeId,
          status,
          progress,
          last_activity_date: new Date().toISOString(),
        };

        const { data } = await updateChallengeProgress(progressData);

        if (data?.success) {
          // Update local state
          setRoadmap((prevRoadmap) => {
            if (!prevRoadmap) return null;

            // Create a deep copy of the roadmap
            const updatedRoadmap = { ...prevRoadmap };

            // Update the specific topic and challenge
            updatedRoadmap.topics = prevRoadmap.topics.map((topic) => {
              if (topic.id === topicId) {
                return {
                  ...topic,
                  challenges: topic.challenges?.map((challenge) =>
                    challenge.id === challengeId
                      ? { ...challenge, status, progress }
                      : challenge,
                  ),
                };
              }
              return topic;
            });

            // Recalculate overall progress
            const totalChallenges = updatedRoadmap.topics.reduce(
              (sum, topic) => sum + (topic.challenges?.length || 0),
              0,
            );

            const completedChallenges = updatedRoadmap.topics.reduce(
              (sum, topic) =>
                sum +
                (topic.challenges?.filter((c) => c.status === 'completed')
                  .length || 0),
              0,
            );

            updatedRoadmap.progress = {
              completed: completedChallenges,
              total: totalChallenges,
              percentage:
                totalChallenges > 0
                  ? (completedChallenges / totalChallenges) * 100
                  : 0,
            };

            return updatedRoadmap;
          });

          return true;
        }

        return false;
      } catch (err) {
        console.error('Error updating challenge progress:', err);
        toast.error('Failed to update progress');
        return false;
      }
    },
    [roadmapId, updateChallengeProgress],
  );

  // Get challenges for a specific topic
  const getChallengesForTopic = useCallback(
    (topicId: string) => {
      if (!roadmap) return [];

      const topic = roadmap.topics.find((t) => t.id === topicId);
      return topic?.challenges || [];
    },
    [roadmap],
  );

  // Get next recommended challenge
  const getNextRecommendedChallenge = useCallback(() => {
    if (!roadmap) return null;

    // Find the first topic with incomplete challenges
    for (const topic of roadmap.topics) {
      if (!topic.challenges) continue;

      // Find the first required challenge that's not completed
      const nextChallenge = topic.challenges.find(
        (challenge) =>
          challenge.is_required && challenge.status !== 'completed',
      );

      if (nextChallenge) {
        return {
          topic,
          challenge: nextChallenge,
        };
      }
    }

    return null;
  }, [roadmap]);

  // Load roadmap data on mount or when roadmapId changes
  useEffect(() => {
    if (roadmapId) {
      fetchRoadmapData();
    }
  }, [roadmapId, fetchRoadmapData]);

  return {
    roadmap,
    relatedChallenges,
    suggestedChallenges,
    isLoading,
    error,
    fetchRoadmapData,
    updateProgress,
    getChallengesForTopic,
    getNextRecommendedChallenge,
  };
}

// Mock data for development
function getMockRoadmapData(roadmapId: string): {
  roadmap: IRoadmap;
  challenges: IChallenge[];
  suggestedChallenges: IChallenge[];
} {
  // Create mock roadmap
  const roadmap: IRoadmap = {
    id: roadmapId,
    title: 'Data Structures and Algorithms',
    description: 'Master the fundamentals of data structures and algorithms',
    image_url: 'https://example.com/roadmap.jpg',
    topics: [
      {
        id: 'topic1',
        title: 'Arrays and Strings',
        description: 'Fundamental operations on arrays and strings',
        level: 1,
        order: 1,
        status: 'in_progress',
        progress: 50,
        challenges: [
          {
            id: 'challenge1',
            title: 'Two Sum',
            difficulty: 'EASY',
            points: 10,
            status: 'completed',
            is_required: true,
            order: 1,
          },
          {
            id: 'challenge2',
            title: 'Valid Anagram',
            difficulty: 'EASY',
            points: 10,
            status: 'in_progress',
            is_required: true,
            order: 2,
          },
          {
            id: 'challenge3',
            title: 'Group Anagrams',
            difficulty: 'MEDIUM',
            points: 20,
            status: 'not_started',
            is_required: false,
            order: 3,
          },
        ],
      },
      {
        id: 'topic2',
        title: 'Linked Lists',
        description: 'Operations on singly and doubly linked lists',
        level: 1,
        order: 2,
        status: 'not_started',
        progress: 0,
        challenges: [
          {
            id: 'challenge4',
            title: 'Reverse Linked List',
            difficulty: 'EASY',
            points: 10,
            status: 'not_started',
            is_required: true,
            order: 1,
          },
          {
            id: 'challenge5',
            title: 'Merge Two Sorted Lists',
            difficulty: 'EASY',
            points: 10,
            status: 'not_started',
            is_required: true,
            order: 2,
          },
        ],
      },
      {
        id: 'topic3',
        title: 'Trees and Graphs',
        description: 'Tree traversals and graph algorithms',
        level: 2,
        order: 3,
        status: 'not_started',
        progress: 0,
        challenges: [
          {
            id: 'challenge6',
            title: 'Maximum Depth of Binary Tree',
            difficulty: 'EASY',
            points: 10,
            status: 'not_started',
            is_required: true,
            order: 1,
          },
          {
            id: 'challenge7',
            title: 'Binary Tree Level Order Traversal',
            difficulty: 'MEDIUM',
            points: 20,
            status: 'not_started',
            is_required: true,
            order: 2,
          },
        ],
      },
    ],
    progress: {
      completed: 1,
      total: 7,
      percentage: 14.29,
    },
    challenges_count: 7,
    enrolled_count: 1250,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  // Create mock challenges
  const challenges: IChallenge[] = roadmap.topics.flatMap((topic) =>
    (topic.challenges || []).map((challenge) => ({
      id: challenge.id,
      title: challenge.title,
      description: `This is a ${challenge.difficulty.toLowerCase()} level challenge about ${topic.title.toLowerCase()}.`,
      difficulty:
        challenge.difficulty === 'EASY'
          ? 'Easy'
          : challenge.difficulty === 'MEDIUM'
            ? 'Medium'
            : 'Hard',
      points: challenge.points,
      category: topic.title,
      tags: [topic.title.split(' ')[0].toLowerCase()],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })),
  );

  // Create mock suggested challenges
  const suggestedChallenges: IChallenge[] = [
    {
      id: 'suggested1',
      title: 'Valid Parentheses',
      description: 'Determine if the input string has valid parentheses.',
      difficulty: 'Easy',
      points: 10,
      category: 'Stacks',
      tags: ['stack', 'string'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 'suggested2',
      title: 'Implement Queue using Stacks',
      description: 'Implement a queue using only stacks.',
      difficulty: 'Medium',
      points: 20,
      category: 'Stacks',
      tags: ['stack', 'queue', 'design'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 'suggested3',
      title: 'Longest Substring Without Repeating Characters',
      description:
        'Find the length of the longest substring without repeating characters.',
      difficulty: 'Medium',
      points: 20,
      category: 'Strings',
      tags: ['string', 'sliding-window', 'hash-table'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ];

  return { roadmap, challenges, suggestedChallenges };
}
