/**
 * @file useBattleZoneStats.ts
 * @description Custom hook for fetching battle zone statistics
 */
import { useEffect, useState, useCallback } from 'react';

import { useAxiosGet } from './useAxios';

// Define a common error interface
interface IErrorWithMessage {
  message?: string;
}

interface IBattleZoneStats {
  activeBattles: number;
  upcomingBattles: number;
  totalParticipants: number;
  winRate: number;
}

/**
 * Custom hook for fetching battle zone statistics
 * @param userId Optional user ID to get user-specific statistics
 * @returns Battle zone statistics and loading/error states
 */
export const useBattleZoneStats = (userId?: string) => {
  // Default stats state
  const [stats, setStats] = useState<IBattleZoneStats>({
    activeBattles: 0,
    upcomingBattles: 0,
    totalParticipants: 0,
    winRate: 0,
  });

  // Use the useAxiosGet hook to fetch battle zone statistics
  const [fetchStats, { data, isLoading, isError, error }] =
    useAxiosGet<IBattleZoneStats>('/battles/statistics');

  // Update stats when data changes
  useEffect(() => {
    if (data) {
      setStats({
        activeBattles: data.activeBattles ?? 0,
        upcomingBattles: data.upcomingBattles ?? 0,
        totalParticipants: data.totalParticipants ?? 0,
        winRate: data.winRate ?? 0,
      });
    }
  }, [data]);

  // Fetch stats on mount and when userId changes
  useEffect(() => {
    const config = userId ? { params: { user_id: userId } } : undefined;
    fetchStats(config);

    // Set up a refresh interval (every 5 minutes)
    const intervalId = setInterval(() => fetchStats(config), 5 * 60 * 1000);

    // Clean up on component unmount
    return () => clearInterval(intervalId);
  }, [userId, fetchStats]);

  // Function to manually refresh stats with cache clearing
  const refreshStats = useCallback(() => {
    const config = userId
      ? { params: { user_id: userId, refresh: 'true' } }
      : { params: { refresh: 'true' } };
    return fetchStats(config);
  }, [userId, fetchStats]);

  // Provide more detailed error information
  const errorMessage = isError
    ? error instanceof Error
      ? error.message
      : typeof error === 'string'
        ? error
        : error
          ? (error as IErrorWithMessage).message ||
            'Failed to load battle statistics'
          : 'Failed to load battle statistics'
    : null;

  return {
    stats,
    isLoading,
    error: errorMessage,
    refreshStats,
  };
};

export default useBattleZoneStats;
