/**
 * @file useRoles.ts
 * @description Custom hook for role-based access control
 */
import { useSelector } from 'react-redux';

import { RootState } from '@/lib/store';
import { IRole } from '@/types';

// RBAC bypass flag
// TODO: Remove this bypass when RBAC implementation is ready for production
const BYPASS_RBAC = true;

type RoleType = 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'USER';

export const useRoles = () => {
  const user = useSelector((state: RootState) => state.user.user);
  const userRoles = user?.roles || [];

  /**
   * Check if the current user has a specific role
   * @param roleType The role type to check for
   * @returns Boolean indicating if the user has the specified role
   */
  const hasRole = (roleType: RoleType): boolean => {
    // TODO: Remove this bypass when RBAC implementation is ready for production
    if (BYPASS_RBAC) {
      return true;
    }
    return userRoles.some((role) => role.type === roleType);
  };

  /**
   * Check if the current user has any of the specified roles
   * @param roleTypes Array of role types to check for
   * @returns Boolean indicating if the user has any of the specified roles
   */
  const hasAnyRole = (roleTypes: RoleType[]): boolean => {
    // TODO: Remove this bypass when RBAC implementation is ready for production
    if (BYPASS_RBAC) {
      return true;
    }
    return userRoles.some((role) => roleTypes.includes(role.type as RoleType));
  };

  /**
   * Check if the current user has all of the specified roles
   * @param roleTypes Array of role types to check for
   * @returns Boolean indicating if the user has all of the specified roles
   */
  const hasAllRoles = (roleTypes: RoleType[]): boolean => {
    // TODO: Remove this bypass when RBAC implementation is ready for production
    if (BYPASS_RBAC) {
      return true;
    }
    return roleTypes.every((roleType) =>
      userRoles.some((role) => role.type === roleType),
    );
  };

  /**
   * Get all roles of the current user
   * @returns Array of user roles
   */
  const getUserRoles = (): IRole[] => {
    return userRoles;
  };

  /**
   * Check if the user is an admin
   * @returns Boolean indicating if the user is an admin
   */
  const isAdmin = (): boolean => {
    // TODO: Remove this bypass when RBAC implementation is ready for production
    if (BYPASS_RBAC) {
      return true;
    }
    return hasRole('ADMIN');
  };

  /**
   * Check if the user is a moderator
   * @returns Boolean indicating if the user is a moderator
   */
  const isModerator = (): boolean => {
    // TODO: Remove this bypass when RBAC implementation is ready for production
    if (BYPASS_RBAC) {
      return true;
    }
    return hasRole('MODERATOR');
  };

  /**
   * Check if the user is a contributor
   * @returns Boolean indicating if the user is a contributor
   */
  const isContributor = (): boolean => {
    // TODO: Remove this bypass when RBAC implementation is ready for production
    if (BYPASS_RBAC) {
      return true;
    }
    return hasRole('CONTRIBUTOR');
  };

  /**
   * Check if the user is a regular user
   * @returns Boolean indicating if the user is a regular user
   */
  const isUser = (): boolean => {
    // TODO: Remove this bypass when RBAC implementation is ready for production
    if (BYPASS_RBAC) {
      return true;
    }
    return hasRole('USER');
  };

  return {
    hasRole,
    hasAnyRole,
    hasAllRoles,
    getUserRoles,
    isAdmin,
    isModerator,
    isContributor,
    isUser,
  };
};
