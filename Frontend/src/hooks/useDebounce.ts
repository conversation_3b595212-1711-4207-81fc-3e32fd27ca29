/**
 * @file useDebounce.ts
 * @description Custom React hook for debounce functionality
 */
import { useEffect, useState } from 'react';

/**
 * useDebounce - Debounces a value by delaying updates
 *
 * This hook is useful for delaying the processing of a rapidly changing value,
 * such as search input, to avoid excessive operations like API calls.
 *
 * @example
 * // Basic usage with a search input
 * const [searchTerm, setSearchTerm] = useState('');
 * const debouncedSearchTerm = useDebounce(searchTerm, 500);
 *
 * // The API call will only be made after the user stops typing for 500ms
 * useEffect(() => {
 *   if (debouncedSearchTerm) {
 *     searchApi(debouncedSearchTerm);
 *   }
 * }, [debouncedSearchTerm]);
 *
 * @template T - The type of the value being debounced
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds
 * @returns The debounced value
 */
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set a timeout to update the debounced value after the specified delay
    const timer = setTimeout(() => setDebouncedValue(value), delay);

    // Clean up the timeout if the value or delay changes before the timeout completes
    return () => clearTimeout(timer);
  }, [value, delay]);

  return debouncedValue;
}

export default useDebounce;
