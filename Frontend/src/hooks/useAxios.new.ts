/**
 * @file useAxios.ts
 * @description Custom React hooks for making API requests with axios
 *
 * This file provides a set of hooks for making HTTP requests (GET, POST, PUT, PATCH, DELETE)
 * with automatic authentication via Supabase, error handling, and loading state management.
 * All hooks include protection against infinite API calls and duplicate requests.
 *
 * The hooks follow a consistent pattern:
 * - They return a tuple with an execute function and a state object
 * - The execute function makes the API request and returns the response
 * - The state object contains loading, success, error states and the response data
 *
 * @example
 * // Using the GET hook
 * const [fetchUsers, { data, isLoading, isError }] = useAxiosGet<User[]>('/users');
 *
 * // Fetch data when component mounts - will never cause infinite calls
 * useEffect(() => {
 *   fetchUsers();
 * }, []); // Empty dependency array
 *
 * @example
 * // Using the POST hook
 * const [createUser, { isLoading, isSuccess }] = useAxiosPost<User, UserInput>('/users');
 *
 * // Submit form data
 * const handleSubmit = async (userData) => {
 *   const response = await createUser(userData);
 *   if (response.success) {
 *     // Handle success
 *   }
 * };
 */
import { useCallback, useState, useRef, useEffect } from 'react';
import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { createClient } from '@/utils/supabase/client';
import { IPaginationMeta } from '@/types';

/**
 * Determine the API base URL with fallback options
 * This helps prevent connection errors if the environment variable is missing
 */
const getApiBaseUrl = () => {
  // First try the environment variable
  const envUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
  if (envUrl) return envUrl;
  
  // Fallback to localhost with standard port if in development
  if (process.env.NODE_ENV === 'development') {
    console.warn('NEXT_PUBLIC_API_BASE_URL not found, using fallback: http://localhost:5000');
    return 'http://localhost:5000';
  }
  
  // In production, log an error but still provide a fallback
  console.error('NEXT_PUBLIC_API_BASE_URL not configured! API calls will likely fail.');
  return '/api'; // Relative path as last resort
};

/**
 * Axios HTTP client instance with default configuration
 *
 * This client is used by all the hooks in this file to make API requests.
 * It includes default headers, timeout, and base URL configuration.
 */
const httpClient = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 15000, // Increased timeout for slower connections
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Add response interceptor for better error logging
httpClient.interceptors.response.use(
  response => response,
  error => {
    // Log connection errors clearly
    if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
      console.error(`Network error connecting to API at ${error.config?.url}. Check if the API server is running.`);
      console.error('API base URL:', httpClient.defaults.baseURL);
    }
    return Promise.reject(error);
  }
);

/**
 * Token cache to avoid repeated Supabase calls
 * This helps prevent excessive auth calls that could trigger rate limits
 */
let cachedToken: string | null = null;
let tokenExpiryTime: number | null = null;
let tokenRefreshPromise: Promise<string | null> | null = null;

/**
 * Get auth token with caching to reduce Supabase API calls
 * @returns Promise resolving to the access token or null
 */
const getAuthToken = async (): Promise<string | null> => {
  // If we have a valid cached token that's not expired, use it
  const now = Date.now();
  if (cachedToken && tokenExpiryTime && now < tokenExpiryTime) {
    return cachedToken;
  }

  // If there's already a refresh in progress, wait for it
  if (tokenRefreshPromise) {
    return tokenRefreshPromise;
  }

  // Otherwise, fetch a new token
  tokenRefreshPromise = (async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.warn('Supabase session error:', error.message);
        return null;
      }

      if (!data.session) {
        return null;
      }

      // Cache the token with a 5-minute expiry (or use the actual expiry if available)
      cachedToken = data.session.access_token;
      tokenExpiryTime = now + 5 * 60 * 1000; // 5 minutes
      
      return cachedToken;
    } catch (err) {
      console.error('Error getting Supabase token:', err);
      return null;
    } finally {
      // Clear the promise so future calls can create a new one
      tokenRefreshPromise = null;
    }
  })();

  return tokenRefreshPromise;
};

/**
 * Supabase authentication interceptor
 *
 * This interceptor automatically adds the Supabase access token to all requests
 * if the user is authenticated, enabling authenticated API requests.
 * It uses token caching to reduce Supabase API calls.
 */
httpClient.interceptors.request.use(async (config) => {
  const token = await getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

/**
 * Base API response interface
 *
 * This interface defines the standard structure of API responses from the backend.
 * All API responses should follow this structure for consistency.
 *
 * @template T - The type of data contained in the response
 */
export interface IBaseApiResponse<T> {
  success: boolean;
  message: string;
  error: boolean;
  data: T;
  meta?: {
    /**
     * Pagination information if the response is paginated
     */
    pagination?: {
      /**
       * Total number of items available
       */
      total: number;
      
      /**
       * Number of items per page
       */
      perPage: number;
      
      /**
       * Current page number
       */
      currentPage: number;
      
      /**
       * Total number of pages
       */
      lastPage: number;
    };
    
    /**
     * Any additional metadata returned by the API
     */
    [key: string]: unknown;
  };
  
  // Simplified pagination fields for backward compatibility
  total?: number;
  perPage?: number;
  currentPage?: number;
  lastPage?: number;
}

/**
 * State object for API hooks
 *
 * This type represents the current state of an API request, including
 * loading state, success/error flags, and the response data.
 *
 * @template T - The type of data expected in the response
 */
export interface IApiState<T> {
  isLoading: boolean;
  isSuccess: boolean;
  isError: boolean;
  error: AxiosError | null;
  data: T | null;
  message: string;
  success: boolean;
  meta?: IBaseApiResponse<T>['meta'];
}

/**
 * Return type for GET and DELETE hooks
 *
 * @template T - The type of data expected in the response
 */
export type ApiHookReturn<T> = [
  (
    config?: AxiosRequestConfig,
    replacements?: { [key: string]: string },
  ) => Promise<IBaseApiResponse<T>>,
  IApiState<T>,
];

/**
 * Return type for POST, PUT, and PATCH hooks
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 */
export type ApiPostHookReturn<T, D = unknown> = [
  (
    data: D,
    config?: AxiosRequestConfig,
    replacements?: { [key: string]: string },
  ) => Promise<IBaseApiResponse<T>>,
  IApiState<T>,
];

/**
 * Interface for validation error objects
 */
export interface IValidationError {
  field: string;
  message: string;
  type?: string;
  context?: Record<string, unknown>;
}

/**
 * Handles API request errors and formats them into a consistent response
 *
 * @template T - The type of data expected in the response
 * @param error - The error object from the failed request
 * @returns A formatted error response
 */
const handleError = <T>(
  error: unknown,
): IBaseApiResponse<T> & { validationErrors?: IValidationError[] } => {
  // Default error response
  const errorResponse: IBaseApiResponse<T> & { validationErrors?: IValidationError[] } = {
    success: false,
    error: true,
    message: 'An unexpected error occurred',
    data: null as unknown as T,
  };

  // Handle Axios errors
  if (axios.isAxiosError(error)) {
    // Get the error message from the response if available
    const responseData = error.response?.data;
    
    if (responseData) {
      errorResponse.message = responseData.message || 'API request failed';
      
      // Handle validation errors
      if (responseData.errors && Array.isArray(responseData.errors)) {
        errorResponse.validationErrors = responseData.errors;
      }
    } else {
      // Network or request errors
      errorResponse.message = error.message || 'Network error';
    }
  } else if (error instanceof Error) {
    // Handle generic JavaScript errors
    errorResponse.message = error.message;
  }

  return errorResponse;
};

/**
 * Custom hook for making GET requests with guaranteed protection against infinite API calls
 *
 * This hook provides a function to make GET requests to the specified URL
 * and maintains the state of the request (loading, success, error, data).
 * It uses refs to ensure stability and prevent re-renders from triggering additional API calls.
 *
 * @example
 * const [fetchUsers, { data, isLoading, isError }] = useAxiosGet<User[]>('/users');
 *
 * // In your component:
 * useEffect(() => {
 *   // This will never cause infinite calls, even without dependencies
 *   fetchUsers();
 * }, []); 
 *
 * @template T - The type of data expected in the response
 * @param url - The URL to make the GET request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosGet = <T>(
  url: string,
): [
  (
    config?: AxiosRequestConfig,
    replacements?: { [key: string]: string },
  ) => Promise<IBaseApiResponse<T & { meta?: IPaginationMeta }>>,
  IApiState<T & { meta?: IPaginationMeta }>,
] => {
  // Store the URL in a ref to prevent re-renders when it changes
  const urlRef = useRef(url);
  
  // Update the ref if the URL changes
  useEffect(() => {
    urlRef.current = url;
  }, [url]);
  
  // Create a ref to track if a request is in progress to prevent duplicate calls
  const isRequestInProgressRef = useRef(false);
  
  // Create a ref to track the last request parameters to prevent duplicate calls with the same parameters
  const lastRequestParamsRef = useRef<{
    url: string;
    configString: string;
    timestamp: number;
  } | null>(null);
  
  // Create a state object for the component to render with
  const [state, setState] = useState<IApiState<T & { meta?: IPaginationMeta }>>(
    {
      data: null,
      message: '',
      success: false,
      isLoading: false,
      isSuccess: false,
      isError: false,
      error: null,
      meta: undefined,
    },
  );

  /**
   * Execute function for making the GET request
   * This function is completely stable and won't cause infinite loops
   */
  const execute = useCallback(
    async (
      config?: AxiosRequestConfig,
      replacements: { [key: string]: string } = {},
    ) => {
      // Get the current URL from the ref
      const currentUrl = urlRef.current;
      
      // Process URL template replacements
      let modifiedUrl = currentUrl;
      for (const key in replacements) {
        modifiedUrl = modifiedUrl.replace(
          `:${key}`,
          encodeURIComponent(replacements[key]),
        );
      }
      
      // Create a string representation of the config for comparison
      const configString = JSON.stringify(config || {});
      
      // Check if this is a duplicate request (same URL, same config, within 500ms)
      const now = Date.now();
      const lastRequest = lastRequestParamsRef.current;
      if (
        lastRequest && 
        lastRequest.url === modifiedUrl && 
        lastRequest.configString === configString &&
        now - lastRequest.timestamp < 500 // 500ms debounce
      ) {
        console.log('Preventing duplicate API call to:', modifiedUrl);
        // Return the last state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Update the last request params
      lastRequestParamsRef.current = {
        url: modifiedUrl,
        configString,
        timestamp: now,
      };
      
      // If a request is already in progress, don't start another one
      if (isRequestInProgressRef.current) {
        console.log('Request already in progress for:', modifiedUrl);
        // Return the current state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Mark that a request is in progress
      isRequestInProgressRef.current = true;
      
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true, isError: false }));

      try {
        // Make the GET request with error handling for network issues
        const response = await httpClient.get(modifiedUrl, config)
          .catch((error) => {
            // Log network errors clearly
            if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
              console.error(`Network error connecting to API at ${modifiedUrl}. Check if the API server is running.`);
              console.error('Full error:', error);
            }
            throw error;
          });

        // Update state with response data
        setState({
          data: response.data.data,
          message: response.data.message,
          success: response.data.success,
          meta: response.data.meta,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;

        return response.data;
      } catch (error) {
        // Check if it's an Axios error with a response
        if (axios.isAxiosError(error)) {
          if (error.response) {
            console.error('API error status:', error.response.status);
            if (error.response.data) {
              console.error(
                'API error message:',
                typeof error.response.data === 'object' &&
                  error.response.data.message
                  ? error.response.data.message
                  : 'No error message available',
              );
            }
          } else if (error.request) {
            // Request was made but no response received
            console.error('No response received from API. Check if the API server is running.');
          }
        }

        // Handle error and update state
        const errorResponse = handleError<T & { meta?: IPaginationMeta }>(
          error,
        );
        setState({
          data: null,
          message: errorResponse.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: errorResponse.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;
        
        return errorResponse;
      }
    },
    [], // No dependencies - this function is completely stable
  );

  return [execute, state];
};

/**
 * Custom hook for making POST requests with guaranteed protection against infinite API calls
 *
 * This hook provides a function to make POST requests to the specified URL
 * with a request body and maintains the state of the request.
 * It uses refs to ensure stability and prevent re-renders from triggering additional API calls.
 *
 * @example
 * const [createUser, { isLoading, isSuccess }] = useAxiosPost<User, UserInput>('/users');
 *
 * const handleSubmit = async (userData) => {
 *   const response = await createUser(userData);
 *   if (response.success) {
 *     // Handle success
 *   }
 * };
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 * @param url - The URL to make the POST request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosPost = <T, D = unknown>(
  url: string,
): ApiPostHookReturn<T, D> => {
  // Store the URL in a ref to prevent re-renders when it changes
  const urlRef = useRef(url);
  
  // Update the ref if the URL changes
  useEffect(() => {
    urlRef.current = url;
  }, [url]);
  
  // Create a ref to track if a request is in progress to prevent duplicate calls
  const isRequestInProgressRef = useRef(false);
  
  // Create a ref to track the last request parameters to prevent duplicate calls with the same parameters
  const lastRequestParamsRef = useRef<{
    dataString: string;
    configString: string;
    timestamp: number;
  } | null>(null);
  
  // Create a state object for the component to render with
  const [state, setState] = useState<IApiState<T>>({
    data: null,
    message: '',
    success: false,
    isLoading: false,
    isSuccess: false,
    isError: false,
    error: null,
    meta: undefined,
  });

  /**
   * Execute function for making the POST request
   * This function is completely stable and won't cause infinite loops
   */
  const execute = useCallback(
    async (data: D, config?: AxiosRequestConfig, replacements: { [key: string]: string } = {}) => {
      // Get the current URL from the ref
      const currentUrl = urlRef.current;
      
      // Process URL template replacements
      let modifiedUrl = currentUrl;
      for (const key in replacements) {
        modifiedUrl = modifiedUrl.replace(
          `:${key}`,
          encodeURIComponent(replacements[key]),
        );
      }
      
      // Create string representations for comparison
      const dataString = JSON.stringify(data || {});
      const configString = JSON.stringify(config || {});
      
      // Check if this is a duplicate request (same data, same config, within 500ms)
      const now = Date.now();
      const lastRequest = lastRequestParamsRef.current;
      if (
        lastRequest && 
        lastRequest.dataString === dataString && 
        lastRequest.configString === configString &&
        now - lastRequest.timestamp < 500 // 500ms debounce
      ) {
        console.log('Preventing duplicate POST API call to:', modifiedUrl);
        // Return the last state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Update the last request params
      lastRequestParamsRef.current = {
        dataString,
        configString,
        timestamp: now,
      };
      
      // If a request is already in progress, don't start another one
      if (isRequestInProgressRef.current) {
        console.log('POST request already in progress for:', modifiedUrl);
        // Return the current state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Mark that a request is in progress
      isRequestInProgressRef.current = true;
      
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true, isError: false }));

      try {
        // Make the POST request with error handling for network issues
        const response = await httpClient.post<IBaseApiResponse<T>>(
          modifiedUrl,
          data,
          config,
        ).catch((error) => {
          // Log network errors clearly
          if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
            console.error(`Network error connecting to API at ${modifiedUrl}. Check if the API server is running.`);
            console.error('Full error:', error);
          }
          throw error;
        });

        // Update state with response data
        setState({
          data: response.data.data,
          message: response.data.message,
          success: response.data.success,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
          meta: response.data.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;

        return response.data;
      } catch (error) {
        // Handle error and update state
        const errorResponse = handleError<T>(error);
        setState({
          data: null,
          message: errorResponse.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: errorResponse.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;
        
        return errorResponse;
      }
    },
    [], // No dependencies - this function is completely stable
  );

  return [execute, state];
};

/**
 * Custom hook for making DELETE requests with guaranteed protection against infinite API calls
 *
 * This hook provides a function to make DELETE requests to the specified URL
 * and maintains the state of the request. DELETE is used to remove resources.
 * It uses refs to ensure stability and prevent re-renders from triggering additional API calls.
 *
 * @example
 * const [deleteUser, { isLoading, isSuccess }] = useAxiosDelete<void>('/users/123');
 *
 * const handleDelete = async () => {
 *   const response = await deleteUser();
 *   if (response.success) {
 *     // Handle successful deletion
 *   }
 * };
 *
 * @template T - The type of data expected in the response (often void for DELETE)
 * @param url - The URL to make the DELETE request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosDelete = <T = void>(url: string): ApiHookReturn<T> => {
  // Store the URL in a ref to prevent re-renders when it changes
  const urlRef = useRef(url);
  
  // Update the ref if the URL changes
  useEffect(() => {
    urlRef.current = url;
  }, [url]);
  
  // Create a ref to track if a request is in progress to prevent duplicate calls
  const isRequestInProgressRef = useRef(false);
  
  // Create a ref to track the last request parameters to prevent duplicate calls with the same parameters
  const lastRequestParamsRef = useRef<{
    url: string;
    configString: string;
    timestamp: number;
  } | null>(null);
  
  // Create a state object for the component to render with
  const [state, setState] = useState<IApiState<T>>({
    data: null,
    message: '',
    success: false,
    isLoading: false,
    isSuccess: false,
    isError: false,
    error: null,
    meta: undefined,
  });

  /**
   * Execute function for making the DELETE request
   * This function is completely stable and won't cause infinite loops
   */
  const execute = useCallback(
    async (config?: AxiosRequestConfig, replacements: { [key: string]: string } = {}) => {
      // Get the current URL from the ref
      const currentUrl = urlRef.current;
      
      // Process URL template replacements
      let modifiedUrl = currentUrl;
      for (const key in replacements) {
        modifiedUrl = modifiedUrl.replace(
          `:${key}`,
          encodeURIComponent(replacements[key]),
        );
      }
      
      // Create a string representation of the config for comparison
      const configString = JSON.stringify(config || {});
      
      // Check if this is a duplicate request (same URL, same config, within 500ms)
      const now = Date.now();
      const lastRequest = lastRequestParamsRef.current;
      if (
        lastRequest && 
        lastRequest.url === modifiedUrl && 
        lastRequest.configString === configString &&
        now - lastRequest.timestamp < 500 // 500ms debounce
      ) {
        console.log('Preventing duplicate DELETE API call to:', modifiedUrl);
        // Return the last state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Update the last request params
      lastRequestParamsRef.current = {
        url: modifiedUrl,
        configString,
        timestamp: now,
      };
      
      // If a request is already in progress, don't start another one
      if (isRequestInProgressRef.current) {
        console.log('DELETE request already in progress for:', modifiedUrl);
        // Return the current state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Mark that a request is in progress
      isRequestInProgressRef.current = true;
      
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true, isError: false }));

      try {
        // Make the DELETE request with error handling for network issues
        const response = await httpClient.delete<IBaseApiResponse<T>>(
          modifiedUrl,
          config,
        ).catch((error) => {
          // Log network errors clearly
          if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
            console.error(`Network error connecting to API at ${modifiedUrl}. Check if the API server is running.`);
            console.error('Full error:', error);
          }
          throw error;
        });

        // Update state with response data
        setState({
          data: response.data.data,
          message: response.data.message,
          success: response.data.success,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
          meta: response.data.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;

        return response.data;
      } catch (error) {
        // Handle error and update state
        const errorResponse = handleError<T>(error);
        setState({
          data: null,
          message: errorResponse.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: errorResponse.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;
        
        return errorResponse;
      }
    },
    [], // No dependencies - this function is completely stable
  );

  return [execute, state];
};

/**
 * Custom hook for making PUT requests with guaranteed protection against infinite API calls
 *
 * This hook provides a function to make PUT requests to the specified URL
 * with a request body and maintains the state of the request.
 * PUT is typically used for complete resource updates/replacements.
 * It uses refs to ensure stability and prevent re-renders from triggering additional API calls.
 *
 * @example
 * const [updateUser, { isLoading, isSuccess }] = useAxiosPut<User, UserInput>('/users/123');
 *
 * const handleUpdate = async (userData) => {
 *   const response = await updateUser(userData);
 *   if (response.success) {
 *     // Handle successful update
 *   }
 * };
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 * @param url - The URL to make the PUT request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosPut = <T, D = unknown>(
  url: string,
): ApiPostHookReturn<T, D> => {
  // Store the URL in a ref to prevent re-renders when it changes
  const urlRef = useRef(url);
  
  // Update the ref if the URL changes
  useEffect(() => {
    urlRef.current = url;
  }, [url]);
  
  // Create a ref to track if a request is in progress to prevent duplicate calls
  const isRequestInProgressRef = useRef(false);
  
  // Create a ref to track the last request parameters to prevent duplicate calls with the same parameters
  const lastRequestParamsRef = useRef<{
    dataString: string;
    configString: string;
    timestamp: number;
  } | null>(null);
  
  // Create a state object for the component to render with
  const [state, setState] = useState<IApiState<T>>({
    data: null,
    message: '',
    success: false,
    isLoading: false,
    isSuccess: false,
    isError: false,
    error: null,
    meta: undefined,
  });

  /**
   * Execute function for making the PUT request
   * This function is completely stable and won't cause infinite loops
   */
  const execute = useCallback(
    async (data: D, config?: AxiosRequestConfig, replacements: { [key: string]: string } = {}) => {
      // Get the current URL from the ref
      const currentUrl = urlRef.current;
      
      // Process URL template replacements
      let modifiedUrl = currentUrl;
      for (const key in replacements) {
        modifiedUrl = modifiedUrl.replace(
          `:${key}`,
          encodeURIComponent(replacements[key]),
        );
      }
      
      // Create string representations for comparison
      const dataString = JSON.stringify(data || {});
      const configString = JSON.stringify(config || {});
      
      // Check if this is a duplicate request (same data, same config, within 500ms)
      const now = Date.now();
      const lastRequest = lastRequestParamsRef.current;
      if (
        lastRequest && 
        lastRequest.dataString === dataString && 
        lastRequest.configString === configString &&
        now - lastRequest.timestamp < 500 // 500ms debounce
      ) {
        console.log('Preventing duplicate PUT API call to:', modifiedUrl);
        // Return the last state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Update the last request params
      lastRequestParamsRef.current = {
        dataString,
        configString,
        timestamp: now,
      };
      
      // If a request is already in progress, don't start another one
      if (isRequestInProgressRef.current) {
        console.log('PUT request already in progress for:', modifiedUrl);
        // Return the current state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Mark that a request is in progress
      isRequestInProgressRef.current = true;
      
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true, isError: false }));

      try {
        // Make the PUT request with error handling for network issues
        const response = await httpClient.put<IBaseApiResponse<T>>(
          modifiedUrl,
          data,
          config,
        ).catch((error) => {
          // Log network errors clearly
          if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
            console.error(`Network error connecting to API at ${modifiedUrl}. Check if the API server is running.`);
            console.error('Full error:', error);
          }
          throw error;
        });

        // Update state with response data
        setState({
          data: response.data.data,
          message: response.data.message,
          success: response.data.success,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
          meta: response.data.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;

        return response.data;
      } catch (error) {
        // Handle error and update state
        const errorResponse = handleError<T>(error);
        setState({
          data: null,
          message: errorResponse.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: errorResponse.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;
        
        return errorResponse;
      }
    },
    [], // No dependencies - this function is completely stable
  );

  return [execute, state];
};

/**
 * Custom hook for making PATCH requests with guaranteed protection against infinite API calls
 *
 * This hook provides a function to make PATCH requests to the specified URL
 * with a request body and maintains the state of the request.
 * PATCH is typically used for partial resource updates.
 * It uses refs to ensure stability and prevent re-renders from triggering additional API calls.
 *
 * @example
 * const [patchUser, { isLoading, isSuccess }] = useAxiosPatch<User, Partial<UserInput>>('/users/123');
 *
 * const handlePartialUpdate = async (userData) => {
 *   const response = await patchUser(userData);
 *   if (response.success) {
 *     // Handle successful update
 *   }
 * };
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 * @param url - The URL to make the PATCH request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosPatch = <T, D = unknown>(
  url: string,
): ApiPostHookReturn<T, D> => {
  // Store the URL in a ref to prevent re-renders when it changes
  const urlRef = useRef(url);
  
  // Update the ref if the URL changes
  useEffect(() => {
    urlRef.current = url;
  }, [url]);
  
  // Create a ref to track if a request is in progress to prevent duplicate calls
  const isRequestInProgressRef = useRef(false);
  
  // Create a ref to track the last request parameters to prevent duplicate calls with the same parameters
  const lastRequestParamsRef = useRef<{
    dataString: string;
    configString: string;
    timestamp: number;
  } | null>(null);
  
  // Create a state object for the component to render with
  const [state, setState] = useState<IApiState<T>>({
    data: null,
    message: '',
    success: false,
    isLoading: false,
    isSuccess: false,
    isError: false,
    error: null,
    meta: undefined,
  });

  /**
   * Execute function for making the PATCH request
   * This function is completely stable and won't cause infinite loops
   */
  const execute = useCallback(
    async (data: D, config?: AxiosRequestConfig, replacements: { [key: string]: string } = {}) => {
      // Get the current URL from the ref
      const currentUrl = urlRef.current;
      
      // Process URL template replacements
      let modifiedUrl = currentUrl;
      for (const key in replacements) {
        modifiedUrl = modifiedUrl.replace(
          `:${key}`,
          encodeURIComponent(replacements[key]),
        );
      }
      
      // Create string representations for comparison
      const dataString = JSON.stringify(data || {});
      const configString = JSON.stringify(config || {});
      
      // Check if this is a duplicate request (same data, same config, within 500ms)
      const now = Date.now();
      const lastRequest = lastRequestParamsRef.current;
      if (
        lastRequest && 
        lastRequest.dataString === dataString && 
        lastRequest.configString === configString &&
        now - lastRequest.timestamp < 500 // 500ms debounce
      ) {
        console.log('Preventing duplicate PATCH API call to:', modifiedUrl);
        // Return the last state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Update the last request params
      lastRequestParamsRef.current = {
        dataString,
        configString,
        timestamp: now,
      };
      
      // If a request is already in progress, don't start another one
      if (isRequestInProgressRef.current) {
        console.log('PATCH request already in progress for:', modifiedUrl);
        // Return the current state as a promise to maintain the same interface
        return Promise.resolve({
          success: state.success,
          error: state.isError,
          message: state.message,
          data: state.data as T,
          meta: state.meta,
        });
      }
      
      // Mark that a request is in progress
      isRequestInProgressRef.current = true;
      
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true, isError: false }));

      try {
        // Make the PATCH request with error handling for network issues
        const response = await httpClient.patch<IBaseApiResponse<T>>(
          modifiedUrl,
          data,
          config,
        ).catch((error) => {
          // Log network errors clearly
          if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
            console.error(`Network error connecting to API at ${modifiedUrl}. Check if the API server is running.`);
            console.error('Full error:', error);
          }
          throw error;
        });

        // Update state with response data
        setState({
          data: response.data.data,
          message: response.data.message,
          success: response.data.success,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
          meta: response.data.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;

        return response.data;
      } catch (error) {
        // Handle error and update state
        const errorResponse = handleError<T>(error);
        setState({
          data: null,
          message: errorResponse.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: errorResponse.meta,
        });
        
        // Mark that the request is complete
        isRequestInProgressRef.current = false;
        
        return errorResponse;
      }
    },
    [], // No dependencies - this function is completely stable
  );

  return [execute, state];
};
