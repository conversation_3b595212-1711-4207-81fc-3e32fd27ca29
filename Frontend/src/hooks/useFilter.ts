/**
 * @file useFilter.ts
 * @description Custom React hook for standardized filtering across tables and lists
 *
 * This hook provides a consistent way to handle filtering state and logic,
 * including search queries, field filtering, and filter combinations.
 */

import { useState, useCallback, useEffect } from 'react';

export interface IFilterField {
  field: string;
  value: string | number | boolean | null;
  operator?:
    | 'eq'
    | 'neq'
    | 'gt'
    | 'gte'
    | 'lt'
    | 'lte'
    | 'contains'
    | 'startsWith'
    | 'endsWith';
}

export interface IFilterOptions {
  initialSearchQuery?: string;
  initialFilters?: IFilterField[];
  debounceMs?: number;
  onFilterChange?: (filters: IFilterState) => void;
}

export interface IFilterState {
  searchQuery: string;
  filters: IFilterField[];
}

/**
 * Hook for managing filtering state and operations
 *
 * @param options - Filtering configuration options
 * @returns Filtering state and control functions
 */
export function useFilter(options: IFilterOptions = {}) {
  const {
    initialSearchQuery = '',
    initialFilters = [],
    debounceMs = 300,
    onFilterChange,
  } = options;

  // Filter state
  const [state, setState] = useState<IFilterState>({
    searchQuery: initialSearchQuery,
    filters: initialFilters,
  });

  // Debounced search query for performance
  const [debouncedSearchQuery, setDebouncedSearchQuery] =
    useState(initialSearchQuery);

  // Set up debounce for search query
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchQuery(state.searchQuery);
    }, debounceMs);

    return () => {
      clearTimeout(handler);
    };
  }, [state.searchQuery, debounceMs]);

  // Call onFilterChange callback when filters change
  useEffect(() => {
    if (onFilterChange) {
      onFilterChange({
        ...state,
        searchQuery: debouncedSearchQuery,
      });
    }
  }, [debouncedSearchQuery, state.filters, onFilterChange]);

  // Set search query
  const setSearchQuery = useCallback((query: string) => {
    setState((prev) => ({
      ...prev,
      searchQuery: query,
    }));
  }, []);

  // Add a filter
  const addFilter = useCallback((filter: IFilterField) => {
    setState((prev) => ({
      ...prev,
      filters: [...prev.filters, filter],
    }));
  }, []);

  // Update a filter by field
  const updateFilter = useCallback(
    (
      field: string,
      value: string | number | boolean | null,
      operator?: IFilterField['operator'],
    ) => {
      setState((prev) => ({
        ...prev,
        filters: prev.filters.map((f) =>
          f.field === field
            ? { ...f, value, ...(operator && { operator }) }
            : f,
        ),
      }));
    },
    [],
  );

  // Remove a filter by field
  const removeFilter = useCallback((field: string) => {
    setState((prev) => ({
      ...prev,
      filters: prev.filters.filter((f) => f.field !== field),
    }));
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setState((prev) => ({
      ...prev,
      filters: [],
    }));
  }, []);

  // Reset to initial state
  const reset = useCallback(() => {
    setState({
      searchQuery: initialSearchQuery,
      filters: initialFilters,
    });
    setDebouncedSearchQuery(initialSearchQuery);
  }, [initialSearchQuery, initialFilters]);

  // Get filter value by field
  const getFilterValue = useCallback(
    (field: string) => {
      const filter = state.filters.find((f) => f.field === field);
      return filter ? filter.value : null;
    },
    [state.filters],
  );

  // Check if a field is filtered
  const hasFilter = useCallback(
    (field: string) => {
      return state.filters.some((f) => f.field === field);
    },
    [state.filters],
  );

  // Convert filters to query params for API requests
  const getQueryParams = useCallback(() => {
    const params: Record<string, string> = {};

    // Add search query if present
    if (debouncedSearchQuery) {
      params.search = debouncedSearchQuery;
    }

    // Add filters
    state.filters.forEach((filter) => {
      const { field, value, operator = 'eq' } = filter;
      if (value !== null && value !== undefined && value !== '') {
        params[`filter[${field}][${operator}]`] = String(value);
      }
    });

    return params;
  }, [debouncedSearchQuery, state.filters]);

  return {
    // Filter state
    searchQuery: state.searchQuery,
    debouncedSearchQuery,
    filters: state.filters,

    // Filter actions
    setSearchQuery,
    addFilter,
    updateFilter,
    removeFilter,
    clearFilters,
    reset,

    // Helper methods
    getFilterValue,
    hasFilter,
    getQueryParams,
  };
}

export default useFilter;
