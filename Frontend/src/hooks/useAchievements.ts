/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */
'use client';

import { useCallback, useEffect, useState } from 'react';

import { toast } from 'sonner';

import {
  IAchievement,
  IAchievementNotification,
} from '@/app/coding-challenges/types/achievements';
import { useAxiosGet } from '@/hooks/useAxios';

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * @file useAchievements.ts
 * @description Custom React hook for achievements functionality
 */

/**
 * Hook to fetch and manage user achievements
 */
export function useAchievements() {
  const [achievements, setAchievements] = useState<IAchievement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getAchievements] = useAxiosGet<{ achievements: IAchievement[] }>(
    '/user/achievements',
  );

  // Fetch all achievements
  const fetchAchievements = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getAchievements();
      if (response.data && response.data.achievements) {
        setAchievements(response.data.achievements);
      }
    } catch (err) {
      setError('Failed to load achievements');

      // Use mock data for development
      if (process.env.NODE_ENV === 'development') {
        setAchievements(getMockAchievements());
      }
    } finally {
      setIsLoading(false);
    }
  }, [getAchievements]);

  // Get unlocked achievements
  const getUnlockedAchievements = useCallback(() => {
    return achievements.filter((achievement) => achievement.unlocked);
  }, [achievements]);

  // Get locked achievements
  const getLockedAchievements = useCallback(() => {
    return achievements.filter(
      (achievement) => !achievement.unlocked && !achievement.hidden,
    );
  }, [achievements]);

  // Get achievements by category
  const getAchievementsByCategory = useCallback(
    (category: string) => {
      return achievements.filter(
        (achievement) => achievement.category === category,
      );
    },
    [achievements],
  );

  // Get achievements by tier
  const getAchievementsByTier = useCallback(
    (tier: string) => {
      return achievements.filter((achievement) => achievement.tier === tier);
    },
    [achievements],
  );

  // Load achievements on mount
  useEffect(() => {
    fetchAchievements();
  }, [fetchAchievements]);

  return {
    achievements,
    isLoading,
    error,
    fetchAchievements,
    getUnlockedAchievements,
    getLockedAchievements,
    getAchievementsByCategory,
    getAchievementsByTier,
  };
}

/**
 * Hook to fetch and manage achievement notifications
 */
export function useAchievementNotifications() {
  const [notifications, setNotifications] = useState<
    IAchievementNotification[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasUnread, setHasUnread] = useState(false);

  // API hooks
  const [getNotifications] = useAxiosGet<{
    notifications: IAchievementNotification[];
  }>('/user/achievements/notifications');

  // Fetch all notifications
  const fetchNotifications = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getNotifications();
      if (response.data && response.data.notifications) {
        setNotifications(response.data.notifications);
        setHasUnread(
          response.data.notifications.some(
            (n: IAchievementNotification) => !n.read,
          ),
        );
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to load notifications';
      setError(errorMessage);

      // Use mock data for development
      if (process.env.NODE_ENV === 'development') {
        const mockNotifications = getMockNotifications();
        setNotifications(mockNotifications);
        setHasUnread(mockNotifications.some((n) => !n.read));
      }
    } finally {
      setIsLoading(false);
    }
  }, [getNotifications]);

  // Mark notification as read
  const markAsRead = useCallback(
    async (notificationId: string) => {
      try {
        // In a real implementation, this would make an API call
        // For now, we'll just update the local state
        setNotifications((prev) =>
          prev.map((notification) =>
            notification.id === notificationId
              ? { ...notification, read: true }
              : notification,
          ),
        );

        // Update hasUnread state
        // Check if there are still any unread notifications
        const stillHasUnread = notifications.some(
          (n) => n.id !== notificationId && !n.read,
        );
        setHasUnread(stillHasUnread);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error';
        toast.error(errorMessage);
      }
    },
    [notifications],
  );

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      // In a real implementation, this would make an API call
      // For now, we'll just update the local state
      setNotifications((prev) =>
        prev.map((notification) => ({ ...notification, read: true })),
      );
      setHasUnread(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      toast.error(errorMessage);
    }
  }, []);

  // Load notifications on mount
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  return {
    notifications,
    isLoading,
    error,
    hasUnread,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
  };
}

// Mock data for development
function getMockAchievements(): IAchievement[] {
  return [
    {
      id: '1',
      title: 'First Steps',
      description: 'Complete your first coding challenge',
      icon: 'rocket',
      category: 'challenges',
      tier: 'bronze',
      points: 10,
      unlocked: true,
      unlocked_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: '2',
      title: 'Problem Solver',
      description: 'Complete 10 coding challenges',
      icon: 'brain',
      category: 'challenges',
      tier: 'silver',
      points: 25,
      progress: {
        current: 7,
        target: 10,
        percentage: 70,
      },
      unlocked: false,
    },
    {
      id: '3',
      title: 'Challenge Master',
      description: 'Complete 50 coding challenges',
      icon: 'trophy',
      category: 'challenges',
      tier: 'gold',
      points: 100,
      progress: {
        current: 7,
        target: 50,
        percentage: 14,
      },
      unlocked: false,
    },
    {
      id: '4',
      title: 'Algorithm Apprentice',
      description: 'Complete 5 algorithm challenges',
      icon: 'code',
      category: 'challenges',
      tier: 'bronze',
      points: 15,
      progress: {
        current: 3,
        target: 5,
        percentage: 60,
      },
      unlocked: false,
    },
    {
      id: '5',
      title: 'Data Structure Guru',
      description: 'Complete 5 data structure challenges',
      icon: 'database',
      category: 'challenges',
      tier: 'bronze',
      points: 15,
      unlocked: true,
      unlocked_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: '6',
      title: 'Efficiency Expert',
      description: 'Submit a solution that runs in O(n) time',
      icon: 'zap',
      category: 'solutions',
      tier: 'silver',
      points: 30,
      unlocked: true,
      unlocked_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: '7',
      title: 'Memory Miser',
      description: 'Submit a solution that uses less than 1MB of memory',
      icon: 'cpu',
      category: 'solutions',
      tier: 'silver',
      points: 30,
      unlocked: false,
    },
    {
      id: '8',
      title: 'Streak Starter',
      description: 'Solve challenges for 3 consecutive days',
      icon: 'calendar',
      category: 'streaks',
      tier: 'bronze',
      points: 20,
      progress: {
        current: 2,
        target: 3,
        percentage: 66,
      },
      unlocked: false,
    },
    {
      id: '9',
      title: 'Consistent Coder',
      description: 'Solve challenges for 7 consecutive days',
      icon: 'calendar',
      category: 'streaks',
      tier: 'silver',
      points: 50,
      progress: {
        current: 2,
        target: 7,
        percentage: 28,
      },
      unlocked: false,
    },
    {
      id: '10',
      title: 'Helpful Commenter',
      description: 'Write 5 helpful comments on other solutions',
      icon: 'message-circle',
      category: 'community',
      tier: 'bronze',
      points: 15,
      progress: {
        current: 2,
        target: 5,
        percentage: 40,
      },
      unlocked: false,
    },
  ];
}

function getMockNotifications(): IAchievementNotification[] {
  const mockAchievements = getMockAchievements();

  return [
    {
      id: '1',
      achievement_id: '1',
      achievement: mockAchievements[0],
      user_id: 'user1',
      read: true,
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: '2',
      achievement_id: '5',
      achievement: mockAchievements[4],
      user_id: 'user1',
      read: false,
      created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: '3',
      achievement_id: '6',
      achievement: mockAchievements[5],
      user_id: 'user1',
      read: false,
      created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];
}
