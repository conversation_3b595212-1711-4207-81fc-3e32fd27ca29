/**
 * @file useBattleApi.ts
 * @description Custom React hook for battleapi functionality
 */
import { useCallback, useState } from 'react';

import {
  useAxiosDelete,
  useAxiosGet,
  useAxiosPost,
  useAxiosPut,
} from '@/hooks/useAxios';
import {
  IBattle,
  IBattleFilters,
  IBattleQuestion,
  IBattleResponse,
  IBattlesResponse,
  IPaginatedResponse,
} from '@/types/battle';

// Define the leaderboard entry interface with proper naming convention
export interface IBattleLeaderboardEntry {
  user_id: string;
  username: string;
  score: number;
  rank: number;
  correct_answers: number;
  total_answers: number;
  time_taken: number;
}

export const useBattleApi = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [getBattles] = useAxiosGet<IBattlesResponse>('/battles');
  // Update the endpoint to match the backend route structure
  const [getBattle] = useAxiosGet<IBattleResponse>('/battles/{{id}}');
  const [getBattleQuestions] = useAxiosGet<IPaginatedResponse<IBattleQuestion>>(
    '/battles/{{id}}/questions',
  );
  const [getBattleLeaderboard] = useAxiosGet<
    IPaginatedResponse<IBattleLeaderboardEntry>
  >('/battles/{{id}}/leaderboard');
  const [createBattle] = useAxiosPost<IBattleResponse>('/battles/create');
  const [updateBattle] = useAxiosPut<IBattleResponse>('/battles/{{id}}');
  const [deleteBattle] = useAxiosDelete<{ success: boolean }>(
    '/battles/{{id}}',
  );
  const [joinBattle] = useAxiosPost<IBattleResponse>('/battles/{{id}}/join');
  const [leaveBattle] = useAxiosPost<IBattleResponse>('/battles/{{id}}/leave');
  const [submitAnswer] = useAxiosPost<{ success: boolean; score: number }>(
    '/battles/submit',
  );

  const fetchBattles = useCallback(
    async (filters?: IBattleFilters) => {
      setIsLoading(true);
      setError(null);

      try {
        // Convert filters to query params
        const queryParams = new URLSearchParams();
        if (filters) {
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
              queryParams.append(key, String(value));
            }
          });
        }

        const response = await getBattles({ params: queryParams.toString() });
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [getBattles],
  );

  const fetchBattle = useCallback(
    async (id: string) => {
      setIsLoading(true);
      setError(null);

      try {
        // Ensure the ID is valid
        if (!id || typeof id !== 'string' || id.trim() === '') {
          setError('Invalid battle ID');
          setIsLoading(false);
          return null;
        }
        // Make the API request with the ID as a replacement parameter
        const response = await getBattle({}, { id });

        // Ensure we're setting isLoading to false
        setIsLoading(false);

        // Check if we have a valid response
        if (!response) {
          setError('No response received from server');
          return null;
        }

        // Check if we have data in the response
        if (!response.data) {
          setError('Invalid response format: missing data');
          return null;
        }

        // Check if data is an array or a single object
        let battleData;
        if (Array.isArray(response.data.data)) {
          battleData =
            response.data.data.length > 0 ? response.data.data[0] : null;
          if (!battleData) {
            setError('Battle not found');
            return null;
          }
        } else {
          battleData = response.data.data;
        }

        return response.data;
      } catch (err) {
        setIsLoading(false);
        const errorMessage =
          err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        return null;
      }
    },
    [getBattle],
  );

  const fetchBattleQuestions = useCallback(
    async (id: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await getBattleQuestions({}, { id });
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [getBattleQuestions],
  );

  const fetchBattleLeaderboard = useCallback(
    async (id: string, page = 1, limit = 10) => {
      setIsLoading(true);
      setError(null);

      try {
        const queryParams = new URLSearchParams();
        queryParams.append('page', String(page));
        queryParams.append('limit', String(limit));

        const response = await getBattleLeaderboard(
          { params: queryParams.toString() },
          { id },
        );
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [getBattleLeaderboard],
  );

  const createNewBattle = useCallback(
    async (battleData: Partial<IBattle>) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await createBattle(battleData);
        if (!response?.data) {
          throw new Error('Failed to create battle: No response data');
        }
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to create battle. Please try again.';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    },
    [createBattle],
  );

  const updateExistingBattle = useCallback(
    async (id: string, battleData: Partial<IBattle>) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await updateBattle(battleData, { params: { id } });
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [updateBattle],
  );

  const removeExistingBattle = useCallback(
    async (id: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await deleteBattle({}, { id });
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [deleteBattle],
  );

  const joinExistingBattle = useCallback(
    async (id: string) => {
      setIsLoading(true);
      setError(null);

      try {
        // Use the replacements parameter to correctly replace {{id}} in the URL template
        const response = await joinBattle({}, {}, { id });
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [joinBattle],
  );

  const leaveExistingBattle = useCallback(
    async (id: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await leaveBattle({}, { params: { id } });
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [leaveBattle],
  );

  const submitBattleAnswer = useCallback(
    async (data: {
      battle_id: string;
      question_id: string;
      answer: string;
      time_taken: number;
    }) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await submitAnswer(data);
        setIsLoading(false);
        return response.data;
      } catch (err) {
        setIsLoading(false);
        setError(err instanceof Error ? err.message : 'An error occurred');
        return null;
      }
    },
    [submitAnswer],
  );

  return {
    isLoading,
    error,
    fetchBattles,
    fetchBattle,
    fetchBattleQuestions,
    fetchBattleLeaderboard,
    createNewBattle,
    updateExistingBattle,
    removeExistingBattle,
    joinExistingBattle,
    leaveExistingBattle,
    submitBattleAnswer,
  };
};

export default useBattleApi;
