/**
 * @file usePublicData.ts
 * @description Custom React hook for publicdata functionality
 */
import { useCallback, useEffect, useState } from 'react';

import {
  College,
  LeaderboardEntry,
  PlatformStats,
  publicDataFallbacks,
} from '@/services/publicDataService';

import { useAxiosGet } from './useAxios';

/**
 * Hook for fetching public leaderboard data
 * @param limit Number of entries to fetch (default: 10)
 * @returns Hook state and execute function
 */
export const usePublicLeaderboard = (limit: number = 10) => {
  const [execute] = useAxiosGet<LeaderboardEntry[]>('/public/leaderboard');
  const [data, setData] = useState<LeaderboardEntry[]>(
    publicDataFallbacks.leaderboardData,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<Error | string | null>(null);

  const fetchLeaderboard = useCallback(async () => {
    try {
      setIsLoading(true);
      setIsError(false);
      const response = await execute({ params: { limit } });

      // Check if response is an error object
      if (response.error) {
        console.error('Error in leaderboard response:', response.message);
        setIsError(true);
        setError(response.message);
        return publicDataFallbacks.leaderboardData;
      }

      // Check if response has valid data
      if (response.data && Array.isArray(response.data)) {
        setData(response.data);
        return response.data;
      } else {
        console.warn(
          'Leaderboard response is not an array, using fallback data',
        );
        return publicDataFallbacks.leaderboardData;
      }
    } catch (err) {
      console.error('Error fetching leaderboard:', err);
      setIsError(true);
      setError(err instanceof Error ? err : String(err));
      return publicDataFallbacks.leaderboardData;
    } finally {
      setIsLoading(false);
    }
  }, [execute, limit]);

  // Fetch data on mount
  useEffect(() => {
    fetchLeaderboard();
  }, [fetchLeaderboard]);

  return {
    fetchLeaderboard,
    data,
    isLoading,
    isError,
    error,
  };
};

/**
 * Hook for fetching public platform statistics
 * @returns Hook state and execute function
 */
export const usePublicStats = () => {
  const [execute] = useAxiosGet<PlatformStats>('/public/stats');
  const [data, setData] = useState<PlatformStats>(
    publicDataFallbacks.statsData,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<Error | string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setIsLoading(true);
      setIsError(false);
      const response = await execute();

      // Check if response is an error object
      if (response.error) {
        console.error('Error in stats response:', response.message);
        setIsError(true);
        setError(response.message);
        return publicDataFallbacks.statsData;
      }

      // Check if response has valid data
      if (response.data) {
        setData(response.data);
        return response.data;
      } else {
        console.warn('Stats response is invalid, using fallback data');
        return publicDataFallbacks.statsData;
      }
    } catch (err) {
      console.error('Error fetching platform stats:', err);
      setIsError(true);
      setError(err instanceof Error ? err : String(err));
      return publicDataFallbacks.statsData;
    } finally {
      setIsLoading(false);
    }
  }, [execute]);

  // Fetch data on mount
  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    fetchStats,
    data,
    isLoading,
    isError,
    error,
  };
};

/**
 * Hook for fetching top colleges
 * @param limit Number of colleges to fetch (default: 10)
 * @returns Hook state and execute function
 */
export const useTopColleges = (limit: number = 10) => {
  const [execute] = useAxiosGet<College[]>('/public/colleges');
  const [data, setData] = useState<College[]>(publicDataFallbacks.collegeData);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<Error | string | null>(null);

  const fetchColleges = useCallback(async () => {
    try {
      setIsLoading(true);
      setIsError(false);
      const response = await execute({ params: { limit } });

      // Check if response is an error object
      if (response.error) {
        console.error('Error in colleges response:', response.message);
        setIsError(true);
        setError(response.message);
        return publicDataFallbacks.collegeData;
      }

      // Check if response has valid data
      if (response.data && Array.isArray(response.data)) {
        setData(response.data);
        return response.data;
      } else {
        console.warn('Colleges response is not an array, using fallback data');
        return publicDataFallbacks.collegeData;
      }
    } catch (err) {
      console.error('Error fetching colleges:', err);
      setIsError(true);
      setError(err instanceof Error ? err : String(err));
      return publicDataFallbacks.collegeData;
    } finally {
      setIsLoading(false);
    }
  }, [execute, limit]);

  // Fetch data on mount
  useEffect(() => {
    fetchColleges();
  }, [fetchColleges]);

  return {
    fetchColleges,
    data,
    isLoading,
    isError,
    error,
  };
};

/**
 * Hook for searching colleges
 * @param query Search query
 * @param limit Number of results to return (default: 10)
 * @returns Hook state and execute function
 */
export const useSearchColleges = () => {
  const [execute] = useAxiosGet<College[]>('/public/colleges/search');
  const [data, setData] = useState<College[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState<Error | string | null>(null);

  const searchColleges = useCallback(
    async (query: string, limit: number = 10) => {
      if (!query || query.length < 2) return [];

      try {
        setIsLoading(true);
        setIsError(false);
        const response = await execute({ params: { query, limit } });

        // Check if response is an error object
        if (response.error) {
          console.error('Error in college search response:', response.message);
          setIsError(true);
          setError(response.message);
          return [];
        }

        // Check if response has valid data
        if (response.data && Array.isArray(response.data)) {
          setData(response.data);
          return response.data;
        } else {
          console.warn('College search response is not an array');
          return [];
        }
      } catch (err) {
        console.error('Error searching colleges:', err);
        setIsError(true);
        setError(err instanceof Error ? err : String(err));
        return [];
      } finally {
        setIsLoading(false);
      }
    },
    [execute],
  );

  return {
    searchColleges,
    data,
    isLoading,
    isError,
    error,
  };
};
