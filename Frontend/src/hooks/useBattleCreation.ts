/**
 * @file useBattleCreation.ts
 * @description Custom React hook for battlecreation functionality
 */
import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { useToast } from '@/hooks/use-toast';

import { useAxiosPost } from './useAxios';

// Old interface kept for backward compatibility
export interface IBattleCreationData {
  title: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  tags: string[];
  maxParticipants: number;
  startTime: string;
  endTime: string;
}

// New interface that matches backend validation schema
export interface IBattleCreationRequest {
  title: string;
  description: string;
  topic_id: string;
  difficulty: string;
  length: string;
  type: string;
  max_participants?: number;
  start_time: string;
  end_time: string;
  points_per_question?: number;
  time_per_question?: number;
  total_questions?: number;
}

interface IBattleCreationResponse {
  id: string;
  success: boolean;
  message: string;
  data: {
    id: string;
  };
}

export const useBattleCreation = () => {
  const { toast } = useToast();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Array<{
    field: string;
    message: string;
  }> | null>(null);
  const [execute, state] = useAxiosPost<
    IBattleCreationResponse,
    IBattleCreationData | IBattleCreationRequest
  >('/battles');

  const createBattle = async (
    data: IBattleCreationData | IBattleCreationRequest,
  ) => {
    setIsSubmitting(true);
    setValidationErrors(null);

    try {
      const response = await execute(data);
      if (response?.data?.id) {
        toast({
          title: 'Success',
          description: 'Battle created successfully!',
        });
        router.push(`/battle-zone/${response.data.id}`);
        return response.data;
      }
      return null;
    } catch (error: unknown) {
      // Type guard for error object with validationErrors
      const errorWithValidation = error as {
        validationErrors?: Array<{ field: string; message: string }>;
        message?: string;
      };

      // Check if we have validation errors
      if (
        errorWithValidation.validationErrors &&
        Array.isArray(errorWithValidation.validationErrors) &&
        errorWithValidation.validationErrors.length > 0
      ) {
        const errors = errorWithValidation.validationErrors;
        setValidationErrors(errors);

        // Create a more descriptive error message
        const fieldErrors = errors
          .map((err) => `${err.field}: ${err.message}`)
          .join('\n');

        toast({
          title: 'Validation Error',
          description: `Failed to create battle. Please check the following fields:\n${fieldErrors}`,
          variant: 'destructive',
        });
      } else {
        // Generic error handling
        toast({
          title: 'Error',
          description:
            errorWithValidation.message ||
            'Failed to create battle. Please try again.',
          variant: 'destructive',
        });
      }
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    createBattle,
    isSubmitting,
    isLoading: state.isLoading,
    error: state.error,
    validationErrors,
  };
};
