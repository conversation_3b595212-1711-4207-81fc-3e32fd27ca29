/**
 * @file useForm.ts
 * @description Custom React hook for form state management with validation
 *
 * This hook provides a consistent way to handle form state, validation, and submission
 * across the application, reducing boilerplate code in form components.
 */

import { useState, useCallback, useEffect } from 'react';

export type ValidationRule<T> = {
  validate: (value: unknown, formValues: T) => boolean;
  message: string;
};

export type FieldValidation<T> = {
  [K in keyof T]?: ValidationRule<T>[];
};

export interface IFormOptions<T> {
  initialValues: T;
  validation?: FieldValidation<T>;
  onSubmit?: (values: T, isValid: boolean) => void | Promise<void>;
  onValuesChange?: (values: T) => void;
}

export interface IFormState<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  isValid: boolean;
  isDirty: boolean;
}

/**
 * Hook for managing form state, validation, and submission
 *
 * @param options - Form configuration options
 * @returns Form state and control functions
 */
export function useForm<T extends Record<string, unknown>>(
  options: IFormOptions<T>,
) {
  const {
    initialValues,
    validation = {} as FieldValidation<T>,
    onSubmit,
    onValuesChange,
  } = options;

  // Form state
  const [state, setState] = useState<IFormState<T>>({
    values: initialValues,
    errors: {},
    touched: {},
    isSubmitting: false,
    isValid: true,
    isDirty: false,
  });

  // Validate a single field
  const validateField = useCallback(
    (name: keyof T, value: unknown) => {
      const fieldValidation = validation[name];
      if (!fieldValidation) return '';

      for (const rule of fieldValidation) {
        if (!rule.validate(value, state.values)) {
          return rule.message;
        }
      }

      return '';
    },
    [validation, state.values],
  );

  // Validate all fields
  const validateForm = useCallback(() => {
    const errors: Partial<Record<keyof T, string>> = {};
    let isValid = true;

    // Check each field with validation rules
    Object.keys(validation).forEach((key) => {
      const fieldName = key as keyof T;
      const error = validateField(fieldName, state.values[fieldName]);

      if (error) {
        errors[fieldName] = error;
        isValid = false;
      }
    });

    return { errors, isValid };
  }, [state.values, validateField, validation]);

  // Update form validity whenever values change
  useEffect(() => {
    const { errors, isValid } = validateForm();

    setState((prev) => ({
      ...prev,
      errors,
      isValid,
    }));

    if (onValuesChange) {
      onValuesChange(state.values);
    }
  }, [state.values, validateForm, onValuesChange]);

  // Handle field change
  const handleChange = useCallback(
    (name: keyof T, value: unknown) => {
      setState((prev) => {
        const newValues = { ...prev.values, [name]: value };
        const error = validateField(name, value);

        return {
          ...prev,
          values: newValues,
          errors: { ...prev.errors, [name]: error },
          touched: { ...prev.touched, [name]: true },
          isDirty: true,
        };
      });
    },
    [validateField],
  );

  // Handle field blur (mark as touched)
  const handleBlur = useCallback((name: keyof T) => {
    setState((prev) => ({
      ...prev,
      touched: { ...prev.touched, [name]: true },
    }));
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault();
      }

      const { errors, isValid } = validateForm();

      // Mark all fields as touched
      const touched = Object.keys(state.values).reduce(
        (acc, key) => {
          acc[key as keyof T] = true;
          return acc;
        },
        {} as Record<keyof T, boolean>,
      );

      setState((prev) => ({
        ...prev,
        errors,
        touched,
        isSubmitting: true,
      }));

      if (onSubmit) {
        try {
          await onSubmit(state.values, isValid);
        } finally {
          setState((prev) => ({
            ...prev,
            isSubmitting: false,
          }));
        }
      }

      return isValid;
    },
    [state.values, validateForm, onSubmit],
  );

  // Reset form to initial values
  const resetForm = useCallback(() => {
    setState({
      values: initialValues,
      errors: {},
      touched: {},
      isSubmitting: false,
      isValid: true,
      isDirty: false,
    });
  }, [initialValues]);

  // Set form values programmatically
  const setValues = useCallback((values: Partial<T>) => {
    setState((prev) => ({
      ...prev,
      values: { ...prev.values, ...values },
      isDirty: true,
    }));
  }, []);

  // Set a specific field value
  const setValue = useCallback(
    (name: keyof T, value: unknown) => {
      handleChange(name, value);
    },
    [handleChange],
  );

  // Set form errors programmatically
  const setErrors = useCallback((errors: Partial<Record<keyof T, string>>) => {
    setState((prev) => ({
      ...prev,
      errors: { ...prev.errors, ...errors },
    }));
  }, []);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setState((prev) => ({
      ...prev,
      errors: {},
    }));
  }, []);

  return {
    // Form state
    ...state,

    // Form actions
    handleChange,
    handleBlur,
    handleSubmit,
    resetForm,
    setValues,
    setValue,
    setErrors,
    clearErrors,

    // Field helper
    getFieldProps: (name: keyof T) => ({
      name,
      value: state.values[name],
      onChange: (
        e:
          | React.ChangeEvent<
              HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
            >
          | unknown,
      ) => {
        if (
          e &&
          typeof e === 'object' &&
          'target' in e &&
          e.target &&
          'value' in e.target
        ) {
          handleChange(name, e.target.value);
        } else {
          handleChange(name, e);
        }
      },
      onBlur: () => handleBlur(name),
      error: state.errors[name],
      touched: state.touched[name] || false,
    }),
  };
}

// Common validation rules
export const validationRules = {
  required: <T extends Record<string, unknown>>(
    message = 'This field is required',
  ): ValidationRule<T> => ({
    validate: (value) => {
      if (value === undefined || value === null) return false;
      if (typeof value === 'string') return value.trim() !== '';
      if (Array.isArray(value)) return value.length > 0;
      return true;
    },
    message,
  }),

  email: <T extends Record<string, unknown>>(
    message = 'Please enter a valid email address',
  ): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true; // Only validate if value exists
      const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
      return emailRegex.test(value);
    },
    message,
  }),

  minLength: <T extends Record<string, unknown>>(
    length: number,
    message = `Must be at least ${length} characters`,
  ): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true; // Only validate if value exists
      return String(value).length >= length;
    },
    message,
  }),

  maxLength: <T extends Record<string, unknown>>(
    length: number,
    message = `Must be no more than ${length} characters`,
  ): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true; // Only validate if value exists
      return String(value).length <= length;
    },
    message,
  }),

  pattern: <T extends Record<string, unknown>>(
    regex: RegExp,
    message = 'Invalid format',
  ): ValidationRule<T> => ({
    validate: (value) => {
      if (!value) return true; // Only validate if value exists
      return regex.test(value);
    },
    message,
  }),

  match: <T extends Record<string, unknown>>(
    field: string,
    message = 'Fields do not match',
  ): ValidationRule<T> => ({
    validate: (value: unknown, formValues: T) => {
      return value === formValues[field];
    },
    message,
  }),
};

export default useForm;
