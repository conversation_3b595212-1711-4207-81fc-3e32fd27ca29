/**
 * @file useIntersection.ts
 * @description Custom React hook for intersection functionality
 */
import { useEffect, useState } from 'react';

export function useIntersection(element: React.RefObject<Element | null>) {
  const [isIntersecting, setIsIntersecting] = useState<boolean>(false);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
    });

    if (element.current) {
      observer.observe(element.current);
    }

    return () => {
      if (element.current) {
        observer.unobserve(element.current);
      }
    };
  }, [element]);

  return isIntersecting;
}
