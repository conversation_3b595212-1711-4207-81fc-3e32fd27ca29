/**
 * @file useRoadmapEnrollment.ts
 * @description Custom React hook for roadmap enrollment functionality
 */
import { useState } from 'react';
import { toast } from 'react-toastify';

import { useAxiosPost } from './useAxios';

/**
 * Hook for handling roadmap enrollment functionality
 *
 * @returns Object containing enrollment function and loading state
 */
export const useRoadmapEnrollment = () => {
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [enrollInRoadmap] = useAxiosPost<{ success: boolean; message: string }>(
    '/roadmaps/enroll',
  );

  /**
   * Enroll in a roadmap
   *
   * @param roadmapId - The ID of the roadmap to enroll in
   * @returns Promise resolving to a boolean indicating success
   */
  const enrollInRoadmapHandler = async (
    roadmapId: string,
  ): Promise<boolean> => {
    if (!roadmapId) {
      toast.error('Invalid roadmap ID');
      return false;
    }

    setIsEnrolling(true);
    try {
      const response = await enrollInRoadmap({ roadmapId });

      if (response.success) {
        toast.success('Successfully enrolled in roadmap!');
        return true;
      } else {
        throw new Error(response.message || 'Failed to enroll in roadmap');
      }
    } catch (error) {
      console.error('Error enrolling in roadmap:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to enroll in roadmap',
      );
      return false;
    } finally {
      setIsEnrolling(false);
    }
  };

  return {
    enrollInRoadmapHandler,
    isEnrolling,
  };
};
