/**
 * @file useAxios.ts
 * @description Custom React hooks for making API requests with axios
 *
 * This file provides a set of hooks for making HTTP requests (GET, POST, PUT, PATCH, DELETE)
 * with automatic authentication via Supabase, error handling, and loading state management.
 *
 * The hooks follow a consistent pattern:
 * - They return a tuple with an execute function and a state object
 * - The execute function makes the API request and returns the response
 * - The state object contains loading, success, error states and the response data
 *
 * @example
 * // Using the GET hook
 * const [fetchUsers, { data, isLoading, isError }] = useAxiosGet<User[]>('/users');
 *
 * // Fetch data when component mounts
 * useEffect(() => {
 *   fetchUsers();
 * }, [fetchUsers]);
 *
 * @example
 * // Using the POST hook
 * const [createUser, { isLoading, isSuccess }] = useAxiosPost<User, UserInput>('/users');
 *
 * // Submit form data
 * const handleSubmit = async (userData) => {
 *   const response = await createUser(userData);
 *   if (response.success) {
 *     // Handle success
 *   }
 * };
 */
import { useCallback, useState } from 'react';

import axios, { AxiosError, AxiosRequestConfig } from 'axios';

import { createClient } from '@/utils/supabase/client';
import { IPaginationMeta } from '@/types';

/**
 * Axios HTTP client instance with default configuration
 *
 * This client is used by all the hooks in this file to make API requests.
 * It includes default headers, timeout, and base URL configuration.
 */
const httpClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Normalize a relative API URL to work with a baseURL of '/api/v1'
// Strips any leading '/api/v1' or '/api' to avoid double-prefixing
const normalizeUrl = (u: string) => {
  let out = u || '';
  if (out.startsWith('/api/v1')) out = out.slice('/api/v1'.length);
  if (out.startsWith('/api')) out = out.slice('/api'.length);
  if (!out.startsWith('/')) out = `/${out}`;
  return out;
};

/**
 * Supabase authentication interceptor
 *
 * This interceptor automatically adds the Supabase access token to all requests
 * if the user is authenticated, enabling authenticated API requests.
 * It also logs the JWT claims for debugging purposes.
 */
httpClient.interceptors.request.use(async (config) => {
  try {
    // Get the current Supabase session
    const supabase = createClient();
    const { data, error } = await supabase.auth.getSession();

    // Check for errors from Supabase
    if (error) {
      console.warn('Supabase session error:', error.message);
      return config; // Continue with the request without auth header
    }

    const session = data.session;

    // Add the access token to the Authorization header if available
    if (session?.access_token) {
      config.headers.Authorization = `Bearer ${session.access_token}`;

      // For debugging: Log JWT claims to verify roles are included
      if (process.env.NODE_ENV === 'development') {
        try {
          // Decode the JWT to check if role claims are present
          const payload = JSON.parse(atob(session.access_token.split('.')[1]));
          console.debug('JWT claims:', payload);

          // Check if roles are present in the claims
          if (payload.claims?.roles) {
            console.debug('User roles from JWT:', payload.claims.roles);
          } else {
            console.warn('No roles found in JWT claims');
          }
        } catch (error) {
          console.error('Error decoding JWT:', error);
        }
      }
    }
  } catch (error) {
    // Handle any unexpected errors gracefully
    console.error('Error in auth interceptor:', error);
    // Continue with the request without auth header
  }

  return config;
});

/**
 * Base API response interface
 *
 * This interface defines the standard structure of API responses from the backend.
 * All API responses should follow this structure for consistency.
 *
 * @template T - The type of data contained in the response
 */
interface IBaseApiResponse<T = unknown> {
  /**
   * Whether the request was successful
   */
  success: boolean;

  /**
   * Message describing the result of the operation
   */
  message: string;

  /**
   * Whether an error occurred
   */
  error: boolean;

  /**
   * The main data payload
   */
  data: T;

  /**
   * Optional metadata about the response
   */
  meta?: {
    /**
     * Pagination information if the response is paginated
     */
    pagination?: {
      /**
       * Total number of items available
       */
      total: number;

      /**
       * Number of items per page
       */
      perPage: number;

      /**
       * Current page number
       */
      currentPage: number;

      /**
       * Total number of pages
       */
      lastPage: number;
    };

    /**
     * Additional metadata fields
     */
    [key: string]: unknown;
  };
}

/**
 * State object for API hooks
 *
 * This type represents the current state of an API request, including
 * loading state, success/error flags, and the response data.
 *
 * @template T - The type of data expected in the response
 */
interface IApiState<T> {
  /**
   * Whether the request is currently loading
   */
  isLoading: boolean;

  /**
   * Whether the request completed successfully
   */
  isSuccess: boolean;

  /**
   * Whether the request resulted in an error
   */
  isError: boolean;

  /**
   * The error object if the request failed
   */
  error: AxiosError | null;

  /**
   * The response data if the request succeeded
   */
  data: T | null;

  /**
   * Response message from the API
   */
  message: string;

  /**
   * Whether the API request was successful
   */
  success: boolean;

  /**
   * Optional metadata from the response
   */
  meta?: IBaseApiResponse<T>['meta'];
}

/**
 * Return type for GET and DELETE hooks
 *
 * @template T - The type of data expected in the response
 */
type ApiHookReturn<T> = [
  /**
   * Function to execute the API request
   *
   * @param config - Optional Axios request configuration
   * @param replacements - Optional URL parameter replacements
   * @returns Promise resolving to the API response
   */
  execute: (
    config?: AxiosRequestConfig,
    replacements?: { [key: string]: string },
  ) => Promise<IBaseApiResponse<T>>,

  /**
   * Current state of the API request
   */
  state: IApiState<T>,
];

/**
 * Return type for POST, PUT, and PATCH hooks
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 */
type ApiPostHookReturn<T, D> = [
  /**
   * Function to execute the API request
   *
   * @param data - Data to send in the request body
   * @param config - Optional Axios request configuration
   * @param replacements - Optional URL parameter replacements
   * @returns Promise resolving to the API response
   */
  execute: (
    data: D,
    config?: AxiosRequestConfig,
    replacements?: { [key: string]: string },
  ) => Promise<IBaseApiResponse<T>>,

  /**
   * Current state of the API request
   */
  state: IApiState<T>,
];

/**
 * Interface for validation error objects
 */
interface IValidationError {
  field: string;
  message: string;
  type?: string;
  context?: Record<string, unknown>;
}

/**
 * Handles API request errors and formats them into a consistent response
 *
 * @template T - The type of data expected in the response
 * @param error - The error object from the failed request
 * @returns A formatted error response
 */
const handleError = <T>(
  error: unknown,
): IBaseApiResponse<T> & { validationErrors?: IValidationError[] } => {
  const axiosError = error as AxiosError<
    IBaseApiResponse<T> & { validationErrors?: IValidationError[] }
  >;

  // Check if this is a validation error with field-specific errors
  const validationErrors = axiosError.response?.data?.validationErrors;

  // Create a more user-friendly error message for validation errors
  let errorMessage = axiosError.response?.data?.message || 'An error occurred';

  // If we have validation errors, create a more specific message
  if (
    validationErrors &&
    Array.isArray(validationErrors) &&
    validationErrors.length > 0
  ) {
    // For a single validation error, show the specific message
    if (validationErrors.length === 1) {
      errorMessage = `Validation error: ${validationErrors[0].message}`;
    } else {
      // For multiple errors, create a summary
      const fieldNames = validationErrors.map((err) => err.field).join(', ');
      errorMessage = `Validation failed for: ${fieldNames}`;
    }
  }

  return {
    success: false,
    error: true,
    data: null as T,
    message: errorMessage,
    meta: axiosError.response?.data?.meta,
    validationErrors: validationErrors || undefined,
  };
};

/**
 * Creates a request handler function for the specified HTTP method
 *
 * This is a higher-order function that creates request handlers for different
 * HTTP methods with consistent error handling.
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 * @param method - The HTTP method to use
 * @returns A function that executes the request
 */
const createExecuteHandler =
  <T, D = unknown>(method: 'get' | 'post' | 'put' | 'patch' | 'delete') =>
  async (url: string, data?: D, config?: AxiosRequestConfig) => {
    try {
      // Execute the request with the specified method
      const response = await httpClient[method]<IBaseApiResponse<T>>(
        url,
        data,
        config,
      );
      return response.data;
    } catch (error) {
      // Handle and format any errors
      return handleError<T>(error);
    }
  };

/**
 * Hook for making GET requests to the API
 *
 * This hook provides a function to make GET requests to the specified URL
 * and maintains the state of the request (loading, success, error, data).
 *
 * @example
 * const [fetchUsers, { data, isLoading, isError }] = useAxiosGet<User[]>('/users');
 *
 * useEffect(() => {
 *   fetchUsers();
 * }, [fetchUsers]);
 *
 * @template T - The type of data expected in the response
 * @param url - The URL to make the GET request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosGet = <T>(
  url: string,
): ApiHookReturn<T & { meta?: IPaginationMeta }> => {
  /**
   * State for tracking the request status and response data
   */
  const [state, setState] = useState<IApiState<T & { meta?: IPaginationMeta }>>(
    {
      data: null,
      message: '',
      success: false,
      isLoading: false,
      isSuccess: false,
      isError: false,
      error: null,
      meta: undefined,
    },
  );

  /**
   * Function to execute the GET request
   *
   * This function is memoized with useCallback to prevent unnecessary re-renders.
   *
   * @param config - Optional Axios request configuration
   * @param replacements - Optional URL parameter replacements
   * @returns Promise resolving to the API response
   */
  const execute = useCallback(
    async (
      config?: AxiosRequestConfig,
      replacements: { [key: string]: string } = {},
    ) => {
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true, isError: false }));

      try {
        // Process URL template replacements
        let modifiedUrl = url;
        for (const key in replacements) {
          modifiedUrl = modifiedUrl.replace(`{{${key}}}`, replacements[key]);
        }
        const finalUrl = normalizeUrl(modifiedUrl);

        // Make the GET request
        const response = await httpClient.get<
          IBaseApiResponse<T & { meta?: IPaginationMeta }>
        >(finalUrl, config);

        // Update state with successful response
        setState({
          data: response.data.data,
          message: response.data.message,
          success: response.data.success,
          meta: response.data.meta,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
        });

        return response.data;
      } catch (error) {
        // Check if it's an Axios error with a response
        if (axios.isAxiosError(error) && error.response) {
          console.error('API error status:', error.response.status);
          if (error.response.data) {
            console.error(
              'API error message:',
              typeof error.response.data === 'object' &&
                error.response.data.message
                ? error.response.data.message
                : 'No error message available',
            );
          }
        }

        // Handle error and update state
        const errorResponse = handleError<T & { meta?: IPaginationMeta }>(
          error,
        );
        setState({
          data: null,
          message: errorResponse.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: errorResponse.meta,
        });
        return errorResponse;
      }
    },
    [url], // Only re-create the function if the URL changes
  );

  return [execute, state];
};

/**
 * Hook for making POST requests to the API
 *
 * This hook provides a function to make POST requests to the specified URL
 * with a request body and maintains the state of the request.
 *
 * @example
 * const [createUser, { isLoading, isSuccess }] = useAxiosPost<User, UserInput>('/users');
 *
 * const handleSubmit = async (userData) => {
 *   const response = await createUser(userData);
 *   if (response.success) {
 *     // Handle success
 *   }
 * };
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 * @param url - The URL to make the POST request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosPost = <T, D = unknown>(
  url: string,
): ApiPostHookReturn<T, D> => {
  /**
   * State for tracking the request status and response data
   */
  const [state, setState] = useState<IApiState<T>>({
    data: null,
    message: '',
    success: false,
    isLoading: false,
    isSuccess: false,
    isError: false,
    error: null,
    meta: undefined,
  });

  /**
   * Function to execute the POST request
   *
   * @param data - Data to send in the request body
   * @param config - Optional Axios request configuration
   * @param replacements - Optional URL parameter replacements
   * @returns Promise resolving to the API response
   */
  const execute = useCallback(
    async (
      data: D,
      config?: AxiosRequestConfig,
      replacements: { [key: string]: string } = {},
    ) => {
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true }));

      try {
        // Process URL template replacements
        let modifiedUrl = url;
        for (const key in replacements) {
          modifiedUrl = modifiedUrl.replace(`{{${key}}}`, replacements[key]);
        }
        const finalUrl = normalizeUrl(modifiedUrl);

        // Make the POST request
        const response = await httpClient.post<IBaseApiResponse<T>>(
          finalUrl,
          data,
          config,
        );

        // Update state with successful response
        setState({
          data: response.data.data,
          message: response.data.message,
          success: response.data.success,
          meta: response.data.meta,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
        });

        return response.data;
      } catch (error) {
        // Handle error and update state
        const errorResponse = handleError<T>(error);
        setState({
          data: null,
          message: errorResponse.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: errorResponse.meta,
        });
        return errorResponse;
      }
    },
    [url], // Only re-create the function if the URL changes
  );

  return [execute, state];
};

/**
 * Hook for making PUT requests to the API
 *
 * This hook provides a function to make PUT requests to the specified URL
 * with a request body and maintains the state of the request.
 *
 * @example
 * const [updateUser, { isLoading }] = useAxiosPut<User, UserUpdateInput>('/users/123');
 *
 * const handleUpdate = async (userData) => {
 *   const response = await updateUser(userData);
 *   if (response.success) {
 *     // Handle success
 *   }
 * };
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 * @param url - The URL to make the PUT request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosPut = <T, D = unknown>(
  url: string,
): ApiPostHookReturn<T, D> => {
  /**
   * State for tracking the request status and response data
   */
  const [state, setState] = useState<IApiState<T>>({
    data: null,
    message: '',
    success: false,
    isLoading: false,
    isSuccess: false,
    isError: false,
    error: null,
    meta: undefined,
  });

  /**
   * Function to execute the PUT request
   *
   * @param data - Data to send in the request body
   * @param config - Optional Axios request configuration
   * @param replacements - Optional URL parameter replacements
   * @returns Promise resolving to the API response
   */
  const execute = useCallback(
    async (
      data: D,
      config?: AxiosRequestConfig,
      replacements: { [key: string]: string } = {},
    ) => {
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true }));

      try {
        // Process URL template replacements
        let modifiedUrl = url;
        for (const key in replacements) {
          modifiedUrl = modifiedUrl.replace(`{{${key}}}`, replacements[key]);
        }
        const finalUrl = normalizeUrl(modifiedUrl);

        // Make the PUT request using the execute handler
        const result = await createExecuteHandler<T, D>('put')(
          finalUrl,
          data,
          config,
        );

        // Update state with successful response
        setState({
          data: result.data,
          message: result.message,
          success: result.success,
          meta: result.meta,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
        });
        return result;
      } catch (error) {
        // Handle error and update state
        const result = handleError<T>(error);
        setState({
          data: null,
          message: result.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: result.meta,
        });
        return result;
      }
    },
    [url], // Only re-create the function if the URL changes
  );

  return [execute, state];
};

/**
 * Hook for making PATCH requests to the API
 *
 * This hook provides a function to make PATCH requests to the specified URL
 * with a request body and maintains the state of the request. PATCH is used
 * for partial updates to resources.
 *
 * @example
 * const [updateUserStatus, { isLoading }] = useAxiosPatch<User, StatusUpdate>('/users/123');
 *
 * const handleStatusUpdate = async (statusData) => {
 *   const response = await updateUserStatus(statusData);
 *   if (response.success) {
 *     // Handle success
 *   }
 * };
 *
 * @template T - The type of data expected in the response
 * @template D - The type of data to send in the request body
 * @param url - The URL to make the PATCH request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosPatch = <T, D = unknown>(
  url: string,
): ApiPostHookReturn<T, D> => {
  /**
   * State for tracking the request status and response data
   */
  const [state, setState] = useState<IApiState<T>>({
    data: null,
    message: '',
    success: false,
    isLoading: false,
    isSuccess: false,
    isError: false,
    error: null,
    meta: undefined,
  });

  /**
   * Function to execute the PATCH request
   *
   * @param data - Data to send in the request body
   * @param config - Optional Axios request configuration
   * @param replacements - Optional URL parameter replacements
   * @returns Promise resolving to the API response
   */
  const execute = useCallback(
    async (
      data: D,
      config?: AxiosRequestConfig,
      replacements: { [key: string]: string } = {},
    ) => {
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true }));

      try {
        // Process URL template replacements
        let modifiedUrl = url;
        for (const key in replacements) {
          modifiedUrl = modifiedUrl.replace(`{{${key}}}`, replacements[key]);
        }
        const finalUrl = normalizeUrl(modifiedUrl);

        // Make the PATCH request using the execute handler
        const result = await createExecuteHandler<T, D>('patch')(
          finalUrl,
          data,
          config,
        );

        // Update state with successful response
        setState({
          data: result.data,
          message: result.message,
          success: result.success,
          meta: result.meta,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
        });
        return result;
      } catch (error) {
        // Handle error and update state
        const result = handleError<T>(error);
        setState({
          data: null,
          message: result.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: result.meta,
        });
        return result;
      }
    },
    [url], // Only re-create the function if the URL changes
  );

  return [execute, state];
};

/**
 * Hook for making DELETE requests to the API
 *
 * This hook provides a function to make DELETE requests to the specified URL
 * and maintains the state of the request. DELETE is used to remove resources.
 *
 * @example
 * const [deleteUser, { isLoading, isSuccess }] = useAxiosDelete<void>('/users/123');
 *
 * const handleDelete = async () => {
 *   const response = await deleteUser();
 *   if (response.success) {
 *     // Handle successful deletion
 *   }
 * };
 *
 * @template T - The type of data expected in the response (often void for DELETE)
 * @param url - The URL to make the DELETE request to
 * @returns A tuple containing the execute function and the request state
 */
export const useAxiosDelete = <T = void>(url: string): ApiHookReturn<T> => {
  /**
   * State for tracking the request status and response data
   */
  const [state, setState] = useState<IApiState<T>>({
    data: null,
    message: '',
    success: false,
    isLoading: false,
    isSuccess: false,
    isError: false,
    error: null,
    meta: undefined,
  });

  /**
   * Function to execute the DELETE request
   *
   * @param config - Optional Axios request configuration
   * @param replacements - Optional URL parameter replacements
   * @returns Promise resolving to the API response
   */
  const execute = useCallback(
    async (
      config?: AxiosRequestConfig,
      replacements: { [key: string]: string } = {},
    ) => {
      // Set loading state
      setState((prev) => ({ ...prev, isLoading: true }));

      try {
        // Process URL template replacements
        let modifiedUrl = url;
        for (const key in replacements) {
          modifiedUrl = modifiedUrl.replace(`{{${key}}}`, replacements[key]);
        }
        const finalUrl = normalizeUrl(modifiedUrl);

        // Make the DELETE request using the execute handler
        const result = await createExecuteHandler<T>('delete')(
          finalUrl,
          undefined,
          config,
        );

        // Update state with successful response
        setState({
          data: result.data,
          message: result.message,
          success: result.success,
          meta: result.meta,
          isLoading: false,
          isSuccess: true,
          isError: false,
          error: null,
        });
        return result;
      } catch (error) {
        // Handle error and update state
        const result = handleError<T>(error);
        setState({
          data: null,
          message: result.message,
          success: false,
          isLoading: false,
          isSuccess: false,
          isError: true,
          error: error as AxiosError,
          meta: result.meta,
        });
        return result;
      }
    },
    [url], // Only re-create the function if the URL changes
  );

  return [execute, state];
};
