/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */
'use client';

import { useCallback, useState } from 'react';

import { toast } from 'sonner';

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

/**
 * @file useLoadingState.ts
 * @description Custom React hook for loadingstate functionality
 */

interface UseLoadingStateOptions<T> {
  initialData?: T;
  loadingMessage?: string;
  errorMessage?: string;
  successMessage?: string;
  showToasts?: boolean;
}

export function useLoadingState<T>(options: UseLoadingStateOptions<T> = {}) {
  const {
    initialData,
    loadingMessage,
    errorMessage = 'An error occurred. Please try again.',
    successMessage,
    showToasts = false,
  } = options;

  const [data, setData] = useState<T | undefined>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(
    async <R>(
      asyncFn: () => Promise<R>,
      {
        onSuccess,
        onError,
        customSuccessMessage,
        customErrorMessage,
        showSuccessToast = showToasts,
        showErrorToast = showToasts,
      }: {
        onSuccess?: (result: R) => void;
        onError?: (error: Error) => void;
        customSuccessMessage?: string;
        customErrorMessage?: string;
        showSuccessToast?: boolean;
        showErrorToast?: boolean;
      } = {},
    ): Promise<R | undefined> => {
      setIsLoading(true);
      setError(null);

      if (loadingMessage && showToasts) {
        toast.loading(loadingMessage);
      }

      try {
        const result = await asyncFn();

        setIsLoading(false);

        if (onSuccess) {
          onSuccess(result);
        }

        if ((successMessage || customSuccessMessage) && showSuccessToast) {
          toast.success(customSuccessMessage || successMessage);
        }

        return result;
      } catch (err) {
        setIsLoading(false);
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);

        if (onError) {
          onError(error);
        }

        if (showErrorToast) {
          toast.error(customErrorMessage || errorMessage || error.message);
        }

        return undefined;
      }
    },
    [loadingMessage, errorMessage, successMessage, showToasts],
  );

  const setLoadingState = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);

  const setErrorState = useCallback((err: Error | null) => {
    setError(err);
  }, []);

  const setDataState = useCallback((newData: T) => {
    setData(newData);
  }, []);

  const reset = useCallback(() => {
    setData(initialData);
    setIsLoading(false);
    setError(null);
  }, [initialData]);

  return {
    data,
    isLoading,
    error,
    execute,
    setLoadingState,
    setErrorState,
    setDataState,
    reset,
  };
}
