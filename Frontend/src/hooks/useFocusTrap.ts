/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */
'use client';

import { useEffect, useRef } from 'react';

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * @file useFocusTrap.ts
 * @description Custom React hook for focustrap functionality
 */

/**
 * Hook to trap focus within a container for accessibility
 * @param active - Whether the focus trap is active
 * @param initialFocusRef - Ref to the element that should receive initial focus
 * @returns Ref to the container element
 */
export function useFocusTrap(
  active: boolean = true,
  initialFocusRef?: React.RefObject<HTMLElement | null>,
) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!active) return;

    const container = containerRef.current;
    if (!container) return;

    // Save the element that had focus before the modal was opened
    const previouslyFocusedElement = document.activeElement as HTMLElement;

    // Set initial focus
    if (initialFocusRef && initialFocusRef.current) {
      initialFocusRef.current.focus();
    } else {
      // Find the first focusable element and focus it
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
      );
      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus();
      }
    }

    // Function to handle tab key to trap focus
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      const focusableElements = Array.from(
        container.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
        ),
      ).filter((el) => {
        const element = el as HTMLElement;
        return (
          !element.hasAttribute('disabled') && element.offsetParent !== null
        );
      });

      if (focusableElements.length === 0) return;

      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[
        focusableElements.length - 1
      ] as HTMLElement;

      // If shift+tab and on first element, move to last element
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
      // If tab and on last element, move to first element
      else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    };

    // Add event listener for tab key
    document.addEventListener('keydown', handleTabKey);

    // Cleanup function
    return () => {
      document.removeEventListener('keydown', handleTabKey);
      // Restore focus to the element that had it before the modal was opened
      if (previouslyFocusedElement) {
        previouslyFocusedElement.focus();
      }
    };
  }, [active, initialFocusRef]);

  return containerRef;
}
