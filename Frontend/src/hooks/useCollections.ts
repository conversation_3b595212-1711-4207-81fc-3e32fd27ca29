/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */
'use client';

import { useCallback, useEffect, useState } from 'react';

import { ICollection } from '@/app/coding-challenges/types';
import { useAxiosGet } from '@/hooks/useAxios';

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * @file useCollections.ts
 * @description Custom React hook for collections functionality
 */

/**
 * Hook to fetch and manage challenge collections
 */
export function useCollections() {
  const [collections, setCollections] = useState<ICollection[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getCollections] = useAxiosGet<{ collections: ICollection[] }>(
    '/challenges/collections',
  );

  // Fetch all collections
  const fetchCollections = useCallback(
    async (params?: Record<string, any>) => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await getCollections({ params });
        if (response.data && response.data.collections) {
          setCollections(response.data.collections);
        }
      } catch (err) {
        console.error('Error fetching collections:', err);
        setError('Failed to load collections');
      } finally {
        setIsLoading(false);
      }
    },
    [getCollections],
  );

  // Get collections by type
  const getCollectionsByType = useCallback(
    (type: 'company' | 'topic' | 'difficulty' | 'series') => {
      return collections.filter((collection) => collection.type === type);
    },
    [collections],
  );

  // Get company collections
  const getCompanyCollections = useCallback(() => {
    return getCollectionsByType('company');
  }, [getCollectionsByType]);

  // Get topic collections
  const getTopicCollections = useCallback(() => {
    return getCollectionsByType('topic');
  }, [getCollectionsByType]);

  // Get difficulty collections
  const getDifficultyCollections = useCallback(() => {
    return getCollectionsByType('difficulty');
  }, [getCollectionsByType]);

  // Get series collections
  const getSeriesCollections = useCallback(() => {
    return getCollectionsByType('series');
  }, [getCollectionsByType]);

  // Load collections on mount
  useEffect(() => {
    fetchCollections();
  }, [fetchCollections]);

  return {
    collections,
    isLoading,
    error,
    fetchCollections,
    getCollectionsByType,
    getCompanyCollections,
    getTopicCollections,
    getDifficultyCollections,
    getSeriesCollections,
  };
}

/**
 * Hook to fetch and manage a single collection
 */
export function useCollection(collectionId: string) {
  const [collection, setCollection] = useState<ICollection | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API hooks
  const [getCollection] = useAxiosGet<{ collection: ICollection }>(
    `/challenges/collections/${collectionId}`,
  );

  // Fetch collection
  const fetchCollection = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getCollection();
      if (response.data && response.data.collection) {
        setCollection(response.data.collection);
      }
    } catch (err) {
      console.error('Error fetching collection:', err);
      setError('Failed to load collection');
    } finally {
      setIsLoading(false);
    }
  }, [collectionId, getCollection]);

  // Load collection on mount
  useEffect(() => {
    fetchCollection();
  }, [fetchCollection]);

  return {
    collection,
    isLoading,
    error,
    fetchCollection,
  };
}
