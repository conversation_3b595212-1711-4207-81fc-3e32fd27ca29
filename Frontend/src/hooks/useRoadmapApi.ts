/**
 * @file useRoadmapApi.ts
 * @description Custom React hook for roadmapapi functionality
 */
import { useRef } from 'react';

import { AxiosError } from 'axios';
import { toast } from 'sonner';

import { useAxiosGet } from './useAxios';

export interface IRoadmapAuthor {
  id: string;
  name: string;
  profileImage?: string;
}

export interface INextTopic {
  id: string;
  title: string;
  estimatedTime: string;
}

export interface IRoadmapSocialData {
  likesCount: number;
  commentsCount: number;
  bookmarksCount: number;
  isLiked: boolean;
  isBookmarked: boolean;
}

export interface IBaseRoadmap extends IRoadmapSocialData {
  id: string;
  title: string;
  author: IRoadmapAuthor;
  thumbnail?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  createdAt: string;
  updatedAt: string;
}

export interface IEnrolledRoadmap extends IBaseRoadmap {
  progress: number;
  lastAccessed: string;
  totalTopics: number;
  completedTopics: number;
  nextTopic?: INextTopic;
  user?: {
    id: string;
    name: string;
    full_name: string | null;
    avatar_url: string | null;
    username: string;
    profileImage?: string;
  };
  description: string;
}

export interface IRecommendedRoadmap extends IBaseRoadmap {
  description: string;
  enrollmentCount: number;
  rating: number;
  topics: number;
  matchScore: number;
  matchReason: string;
}

export interface IRoadmapsResponse {
  enrolled?: IEnrolledRoadmap[];
  recommended?: IRecommendedRoadmap[];
  data?: (IEnrolledRoadmap | IRecommendedRoadmap)[];
  meta?: { hasNextPage?: boolean };
}

export interface IApiResponse<T> {
  data: T;
  message: string;
  status: number;
  meta?: {
    total?: number;
    currentPage?: number;
    totalPages?: number;
    limit?: number;
    hasNextPage?: boolean;
    hasPrevPage?: boolean;
  };
}

export interface IRoadmapFilters {
  categories?: string[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'all';
  sortBy?: 'popular' | 'recent' | 'rating' | 'enrolled';
  search?: string;
  page?: number;
  limit?: number;
  type?:
    | 'featured'
    | 'trending'
    | 'all'
    | 'bookmarked'
    | 'enrolled'
    | 'my-roadmaps';
}

export const useRoadmaps = (filters?: IRoadmapFilters) => {
  const [execute, state] =
    useAxiosGet<IApiResponse<IRoadmapsResponse>>('/roadmaps');

  // Use a ref to track the last request to prevent duplicate calls
  const lastRequestRef = useRef<string>('');

  const fetchRoadmaps = async () => {
    try {
      // Convert filters to URLSearchParams format
      const params = new URLSearchParams();

      if (filters) {
        if (filters.categories && filters.categories.length > 0) {
          filters.categories.forEach((category) => {
            params.append('categories', category);
          });
        }

        if (filters.difficulty && filters.difficulty !== 'all') {
          params.append('difficulty', filters.difficulty);
        }

        if (filters.sortBy) {
          params.append('sort', filters.sortBy);
        }

        if (filters.search) {
          params.append('search', filters.search);
        }

        if (filters.page) {
          params.append('page', filters.page.toString());
        }

        if (filters.limit) {
          params.append('limit', filters.limit.toString());
        }

        if (filters.type) {
          params.append('type', filters.type);
        }
      }

      // Create a request signature to compare with the last request
      const requestSignature = params.toString();

      // Skip the request if it's identical to the last one
      if (requestSignature === lastRequestRef.current && state.data) {
        return { ...state };
      }

      // Update the last request signature
      lastRequestRef.current = requestSignature;

      const response = await execute({ params });
      return response;
    } catch (error) {
      const axiosError = error as AxiosError<IApiResponse<unknown>>;
      toast.error(
        axiosError.response?.data?.message || 'Failed to fetch roadmaps',
      );
      throw error;
    }
  };

  return {
    data: state.data,
    isLoading: state.isLoading,
    error: state.error,
    refetch: fetchRoadmaps,
  };
};

export const useRoadmapById = (id: string) => {
  const [execute, state] = useAxiosGet<IApiResponse<IBaseRoadmap>>(
    `/roadmaps/${id}`,
  );

  const fetchRoadmap = async () => {
    try {
      const response = await execute();
      return response;
    } catch (error) {
      const axiosError = error as AxiosError<IApiResponse<unknown>>;
      toast.error(
        axiosError.response?.data?.message || 'Failed to fetch roadmap',
      );
      throw error;
    }
  };

  return {
    data: state.data,
    isLoading: state.isLoading,
    error: state.error,
    refetch: fetchRoadmap,
  };
};

export const useRoadmapCategories = () => {
  const [execute, state] = useAxiosGet<IApiResponse<string[]>>(
    '/roadmaps/categories',
  );

  const fetchCategories = async () => {
    try {
      const response = await execute();
      return response;
    } catch (error) {
      const axiosError = error as AxiosError<IApiResponse<unknown>>;
      toast.error(
        axiosError.response?.data?.message || 'Failed to fetch categories',
      );
      throw error;
    }
  };

  return {
    data: state.data,
    isLoading: state.isLoading,
    error: state.error,
    refetch: fetchCategories,
  };
};
