/**
 * @file usePagination.ts
 * @description Custom React hook for standardized pagination across the application
 *
 * This hook provides a consistent way to handle pagination state and logic,
 * including page navigation, items per page, and pagination metadata.
 */

import { useState, useCallback, useEffect } from 'react';
import { IPaginationMeta } from '@/types';

export interface IPaginationParams {
  page: number;
  limit: number;
}

export interface IPaginationState extends IPaginationParams {
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface IPaginationOptions {
  initialPage?: number;
  initialLimit?: number;
  onPageChange?: (params: IPaginationParams) => void;
}

/**
 * Hook for managing pagination state and operations
 *
 * @param options - Pagination configuration options
 * @returns Pagination state and control functions
 */
export function usePagination(options: IPaginationOptions = {}) {
  const { initialPage = 1, initialLimit = 10, onPageChange } = options;

  // Pagination state
  const [state, setState] = useState<IPaginationState>({
    page: initialPage,
    limit: initialLimit,
    total: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });

  // Update pagination state from API response metadata
  const updateFromMeta = useCallback((meta: IPaginationMeta) => {
    setState((prev) => ({
      ...prev,
      total: meta.total,
      totalPages: meta.last_page,
      hasNextPage: meta.current_page < meta.last_page,
      hasPreviousPage: meta.current_page > 1,
    }));
  }, []);

  // Go to a specific page
  const goToPage = useCallback(
    (page: number) => {
      if (page < 1 || (state.totalPages > 0 && page > state.totalPages)) {
        return;
      }

      setState((prev) => ({
        ...prev,
        page,
      }));
    },
    [state.totalPages],
  );

  // Go to the next page
  const nextPage = useCallback(() => {
    if (state.hasNextPage) {
      goToPage(state.page + 1);
    }
  }, [goToPage, state.hasNextPage, state.page]);

  // Go to the previous page
  const previousPage = useCallback(() => {
    if (state.hasPreviousPage) {
      goToPage(state.page - 1);
    }
  }, [goToPage, state.hasPreviousPage, state.page]);

  // Change the number of items per page
  const setItemsPerPage = useCallback((limit: number) => {
    setState((prev) => ({
      ...prev,
      limit,
      page: 1, // Reset to first page when changing items per page
    }));
  }, []);

  // Reset pagination to initial state
  const reset = useCallback(() => {
    setState((prev) => ({
      ...prev,
      page: initialPage,
      limit: initialLimit,
    }));
  }, [initialPage, initialLimit]);

  // Call onPageChange callback when page or limit changes
  useEffect(() => {
    if (onPageChange) {
      onPageChange({ page: state.page, limit: state.limit });
    }
  }, [state.page, state.limit, onPageChange]);

  return {
    // Pagination state
    ...state,

    // Pagination actions
    goToPage,
    nextPage,
    previousPage,
    setItemsPerPage,
    reset,
    updateFromMeta,

    // Pagination params for API requests
    params: {
      page: state.page,
      limit: state.limit,
    },
  };
}

export default usePagination;
