/**
 * @file use-media-query.tsx
 * @description Custom React hook for -media-query.tsx functionality
 */
import { useEffect, useState } from 'react';

export const useMediaQuery = (mediaQuery: string) => {
  const [matches, setMatches] = useState(window.matchMedia(mediaQuery).matches);

  useEffect(() => {
    const mediaQueryList = window.matchMedia(mediaQuery);
    const handleChange = (event: MediaQueryListEvent) =>
      setMatches(event.matches);

    mediaQueryList.addEventListener('change', handleChange);
    return () => mediaQueryList.removeEventListener('change', handleChange);
  }, [mediaQuery]);

  return matches;
};
