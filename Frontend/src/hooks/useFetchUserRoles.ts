/**
 * @file useFetchUserRoles.ts
 * @description Custom hook to fetch user roles from the backend
 */
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '@/lib/store';
import { setUser } from '@/lib/features/user/userSlice';
import { IRole } from '@/types';
import { createClient } from '@/utils/supabase/client';
import { useAxiosGet } from './useAxios';

/**
 * Custom hook to fetch user roles from the backend
 */
export const useFetchUserRoles = () => {
  const dispatch = useDispatch<AppDispatch>();
  const user = useSelector((state: RootState) => state.user.user);
  const isAuthenticated = useSelector(
    (state: RootState) => state.user.isAuthenticated,
  );
  const [getRoles] = useAxiosGet<IRole[]>('/users/{userId}/roles');

  useEffect(() => {
    const fetchUserRoles = async () => {
      if (!user || !isAuthenticated) return;

      try {
        // Get the Supabase client
        const supabase = createClient();

        // Get the current session
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session) return;

        // Fetch user roles from the backend
        const response = await getRoles(
          {},
          {
            userId: user.id,
          },
        );

        if (!response.success) {
          throw new Error('Failed to fetch user roles');
        }

        const roles = response.data;

        // Update the user in Redux with the roles
        dispatch(
          setUser({
            user: {
              ...user,
              roles,
            },
            detailsComplete: true,
          }),
        );
      } catch (error) {
        console.error('Error fetching user roles:', error);
      }
    };

    fetchUserRoles();
  }, [dispatch, user, isAuthenticated]);

  return { isLoading: !user?.roles && isAuthenticated };
};
