/**
 * @file roleService.ts
 * @description Service for role management API calls
 */

// Role interfaces
export interface IRole {
  id: string;
  name: string;
  type: string;
  description: string;
  userCount: number;
  permissions: number;
  createdAt?: string;
  updatedAt?: string;
  isSystem?: boolean;
}

export interface IRoleListResponse {
  roles: IRole[];
  total: number;
}

export interface IRoleResponse {
  role: IRole;
}

export interface IRolePermissionsResponse {
  permissionIds: string[];
}

export interface IPermission {
  id: string;
  name: string;
  code: string;
  description: string;
  category: string;
}

export interface IPermissionListResponse {
  permissions: IPermission[];
  total: number;
}

export interface IRoleCreateParams {
  name: string;
  type: string;
  description: string;
  permissionIds?: string[];
}

export interface IRoleUpdateParams {
  name?: string;
  description?: string;
  permissionIds?: string[];
}

export interface IRolePermissionParams {
  roleId: string;
  permissionIds: string[];
}

export interface IUserRoleAssignParams {
  userId: string;
  roleId: string;
}

// API endpoints
export const ROLE_API = {
  LIST: '/api/admin/roles',
  DETAIL: '/api/admin/roles/{{roleId}}',
  CREATE: '/api/admin/roles',
  UPDATE: '/api/admin/roles/{{roleId}}',
  DELETE: '/api/admin/roles/{{roleId}}',
  PERMISSIONS: '/api/admin/permissions',
  ROLE_PERMISSIONS: '/api/admin/roles/{{roleId}}/permissions',
  ASSIGN_USER_ROLE: '/api/admin/users/{{userId}}/role',
};
