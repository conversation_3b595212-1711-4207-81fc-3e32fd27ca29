/**
 * @file userService.ts
 * @description Service for user management API calls
 */

import { IApiResponse } from '@/types';

// User interfaces
export interface IUser {
  id: string;
  username: string;
  email: string;
  role: string;
  roles?: { name: string; id: string }[];
  status: string;
  lastLogin: string;
  registrationDate: string;
  // Additional fields that might come from the API
  firstName?: string;
  lastName?: string;
  profileImage?: string;
  bio?: string;
  isEmailVerified?: boolean;
  is_suspended?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Using type alias instead of empty interface to avoid lint errors
export interface IUserListResponse {
  users: IUser[];
  pagination: {
    total: number;
    perPage: number;
    currentPage: number;
    lastPage: number;
  };
}

export interface IUserListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  role?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export type IUserResponse = IApiResponse<{ user: IUser }>;
export type IUserActionResponse = IApiResponse<{
  success: boolean;
  message?: string;
}>;

export interface IUserSuspendParams {
  userId: string;
  reason: string;
  duration: string;
  notifyUser: boolean;
}

export interface IUserDeleteParams {
  userId: string;
  reason: string;
  exportData: boolean;
}

export interface IBulkActionParams {
  userIds: string[];
  action: 'suspend' | 'delete' | 'activate';
  reason?: string;
  duration?: string;
}

// API endpoints
export const USER_API = {
  LIST: '/admin/users',
  DETAIL: '/admin/users/{{userId}}',
  CREATE: '/admin/users',
  UPDATE: '/admin/users/{{userId}}',
  SUSPEND: '/admin/users/{{userId}}/suspend',
  DELETE: '/admin/users/{{userId}}',
  BULK_ACTION: '/admin/users/bulk-action',
  EXPORT_DATA: '/admin/users/{{userId}}/export',
  RESET_PASSWORD: '/admin/users/{{userId}}/reset-password',
  VERIFY_EMAIL: '/admin/users/{{userId}}/verify-email',
  SEND_VERIFICATION: '/admin/users/{{userId}}/send-verification',
  ASSIGN_ROLE: '/admin/users/{{userId}}/assign-role',
};

// User update interface
export interface IUserUpdateParams {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  role?: string;
  status?: string;
  phone?: string;
  location?: string;
  bio?: string;
  emailVerified?: boolean;
}

// Password reset interface
export interface IPasswordResetParams {
  userId: string;
  newPassword: string;
  sendNotification?: boolean;
}

// Email verification interface
export interface IEmailVerificationParams {
  userId: string;
  verify: boolean;
}

// Role assignment interface
export interface IRoleAssignmentParams {
  userId: string;
  roleId: string;
}
