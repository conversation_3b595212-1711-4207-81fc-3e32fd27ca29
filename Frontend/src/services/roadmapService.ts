/**
 * @file roadmapService.ts
 * @description Service for roadmap management API calls
 */

import { IApiResponse } from '@/types';

// Roadmap interfaces
export interface IRoadmap {
  id: string;
  title: string;
  description: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  created_at?: string;
  updated_at?: string;
  is_featured?: boolean;
  category?: {
    id: string;
    name: string;
  };
  authorName?: string;
  difficulty?: string;
  enrollmentCount?: number;
  completionRate?: number;
  averageRating?: number;
}

export interface IMainConcept {
  id: string;
  title: string;
  description: string;
  order: number;
  roadmap_id: string;
  created_at?: string;
  updated_at?: string;
}

export interface ISubject {
  id: string;
  title: string;
  description: string;
  order: number;
  main_concept_id: string;
  created_at?: string;
  updated_at?: string;
}

export interface ITopic {
  id: string;
  title: string;
  description: string;
  order: number;
  subject_id: string;
  created_at?: string;
  updated_at?: string;
}

export interface ITopicArticle {
  id: string;
  topic_id: string;
  article_id: string;
  is_primary: boolean;
  order: number;
  created_at?: string;
  updated_at?: string;
  article?: {
    id: string;
    title: string;
    slug: string;
  };
}

// List params interfaces
export interface IRoadmapListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface IMainConceptListParams {
  page?: number;
  limit?: number;
  search?: string;
  roadmap_id?: string;
}

export interface ISubjectListParams {
  page?: number;
  limit?: number;
  search?: string;
  main_concept_id?: string;
}

export interface ITopicListParams {
  page?: number;
  limit?: number;
  search?: string;
  subject_id?: string;
}

export interface IArticleListParams {
  page?: number;
  limit?: number;
  search?: string;
  topic_id?: string;
}

// Response types
export type IRoadmapResponse = IApiResponse<{ roadmap: IRoadmap }>;
export type IMainConceptResponse = IApiResponse<{ mainConcept: IMainConcept }>;
export type ISubjectResponse = IApiResponse<{ subject: ISubject }>;
export type ITopicResponse = IApiResponse<{ topic: ITopic }>;
export type ITopicArticleResponse = IApiResponse<{
  topicArticle: ITopicArticle;
}>;

export type IRoadmapActionResponse = IApiResponse<{
  success: boolean;
  message?: string;
}>;

// API endpoints
export const ROADMAP_API = {
  // Roadmap endpoints
  LIST: '/admin/roadmaps',
  DETAIL: '/admin/roadmaps/{{roadmapId}}',
  CREATE: '/admin/roadmaps',
  UPDATE: '/admin/roadmaps/{{roadmapId}}',
  DELETE: '/admin/roadmaps/{{roadmapId}}',
  TOGGLE_FEATURED: '/admin/roadmaps/{{roadmapId}}/featured',
  HIERARCHY: '/admin/roadmaps/{{roadmapId}}/hierarchy',
  BULK_STATUS_UPDATE: '/admin/roadmaps/bulk/status',

  // Main concept endpoints
  MAIN_CONCEPTS_LIST: '/admin/roadmaps/{{roadmapId}}/main-concepts',
  MAIN_CONCEPT_DETAIL: '/admin/main-concepts/{{mainConceptId}}',
  MAIN_CONCEPT_CREATE: '/admin/roadmaps/{{roadmapId}}/main-concepts',
  MAIN_CONCEPT_UPDATE: '/admin/main-concepts/{{mainConceptId}}',
  MAIN_CONCEPT_DELETE: '/admin/main-concepts/{{mainConceptId}}',
  MAIN_CONCEPT_REORDER: '/admin/roadmaps/{{roadmapId}}/main-concepts/reorder',

  // Subject endpoints
  SUBJECTS_LIST: '/admin/main-concepts/{{mainConceptId}}/subjects',
  SUBJECT_DETAIL: '/admin/subjects/{{subjectId}}',
  SUBJECT_CREATE: '/admin/main-concepts/{{mainConceptId}}/subjects',
  SUBJECT_UPDATE: '/admin/subjects/{{subjectId}}',
  SUBJECT_DELETE: '/admin/subjects/{{subjectId}}',
  SUBJECT_REORDER: '/admin/main-concepts/{{mainConceptId}}/subjects/reorder',

  // Topic endpoints
  TOPICS_LIST: '/admin/subjects/{{subjectId}}/topics',
  TOPIC_DETAIL: '/admin/topics/{{topicId}}',
  TOPIC_CREATE: '/admin/subjects/{{subjectId}}/topics',
  TOPIC_UPDATE: '/admin/topics/{{topicId}}',
  TOPIC_DELETE: '/admin/topics/{{topicId}}',
  TOPIC_REORDER: '/admin/subjects/{{subjectId}}/topics/reorder',

  // Topic article endpoints
  TOPIC_ARTICLES_LIST: '/admin/topics/{{topicId}}/articles',
  TOPIC_ARTICLE_LINK: '/admin/topics/{{topicId}}/articles',
  TOPIC_ARTICLE_UNLINK: '/admin/topics/{{topicId}}/articles/{{articleId}}',
  TOPIC_ARTICLE_SET_PRIMARY:
    '/admin/topics/{{topicId}}/articles/{{articleId}}/primary',
  TOPIC_ARTICLE_REORDER: '/admin/topics/{{topicId}}/articles/reorder',
};

// Roadmap create/update interface
export interface IRoadmapCreateParams {
  title: string;
  description: string;
  status: 'active' | 'inactive' | 'draft';
}

export interface IRoadmapUpdateParams {
  title?: string;
  description?: string;
  status?: 'active' | 'inactive' | 'draft';
}

// Main concept create/update interface
export interface IMainConceptCreateParams {
  title: string;
  description: string;
  order?: number;
}

export interface IMainConceptUpdateParams {
  title?: string;
  description?: string;
  order?: number;
}

// Subject create/update interface
export interface ISubjectCreateParams {
  title: string;
  description: string;
  order?: number;
}

export interface ISubjectUpdateParams {
  title?: string;
  description?: string;
  order?: number;
}

// Topic create/update interface
export interface ITopicCreateParams {
  title: string;
  description: string;
  order?: number;
}

export interface ITopicUpdateParams {
  title?: string;
  description?: string;
  order?: number;
}

// Topic article create/update interface
export interface ITopicArticleLinkParams {
  article_id: string;
  is_primary?: boolean;
  order?: number;
}

export interface ITopicArticleUpdateParams {
  is_primary?: boolean;
  order?: number;
}

// Reorder interfaces
export interface IReorderParams {
  ids: string[]; // Array of IDs in the desired order
}

// Bulk status update interface
export interface IBulkStatusUpdateParams {
  roadmapIds: string[];
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE';
}

export interface IBulkStatusUpdateResponse {
  updatedCount: number;
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE';
}
