/**
 * @file publicDataService.ts
 * @description Service for publicdataservice API operations
 */
// Fallback data and types for public data services

export interface College {
  id: string;
  name: string;
  location: string;
  logo_url: string;
  userCount: number;
}

export interface LeaderboardEntry {
  id: string;
  name: string;
  college: string;
  college_id?: string | null;
  college_logo?: string | null;
  college_location?: string | null;
  points: number;
  rank: number;
  avatar_url: string;
}

export interface PlatformStats {
  learningPaths: {
    stats: Array<{ label: string; value: string; icon: string }>;
  };
  community: {
    stats: Array<{ label: string; value: string; icon: string }>;
  };
  battleZone: {
    stats: Array<{ label: string; value: string; icon: string }>;
  };
}

// Fallback data for when API calls fail
const fallbackLeaderboardData: LeaderboardEntry[] = [
  {
    id: 'user-1',
    rank: 1,
    name: 'Shai<PERSON><PERSON>i Chaudhari',
    points: 2890,
    college: 'IIT Bombay',
    avatar_url: 'https://i.pravatar.cc/150?img=1',
  },
  {
    id: 'user-2',
    rank: 2,
    name: '<PERSON><PERSON><PERSON>',
    points: 2780,
    college: 'IIT Kharagpur',
    avatar_url: 'https://i.pravatar.cc/150?img=2',
  },
  {
    id: 'user-3',
    rank: 3,
    name: 'Shaileshbhai',
    points: 2690,
    college: 'BITS Pilani',
    avatar_url: 'https://i.pravatar.cc/150?img=3',
  },
  {
    id: 'user-4',
    rank: 4,
    name: 'Shailesh',
    points: 2610,
    college: 'IIT Delhi',
    avatar_url: 'https://i.pravatar.cc/150?img=4',
  },
  {
    id: 'user-5',
    rank: 5,
    name: 'Shailesh P. Chaudhari',
    points: 2580,
    college: 'VIT Vellore',
    avatar_url: 'https://i.pravatar.cc/150?img=5',
  },
];

const fallbackStats: PlatformStats = {
  learningPaths: {
    stats: [
      { label: 'Active Users', value: '10,000+', icon: 'FaUsers' },
      { label: 'Roadmaps', value: '50+', icon: 'FaRoad' },
      { label: 'Company Guides', value: '200+', icon: 'FaLaptopCode' },
      { label: 'Achievements', value: '100+', icon: 'FaTrophy' },
    ],
  },
  community: {
    stats: [
      { label: 'Active Users', value: '10,000+', icon: 'FaUsers' },
      { label: 'Challenges', value: '500+', icon: 'FaGamepad' },
      { label: 'Articles', value: '300+', icon: 'FaBook' },
      { label: 'Daily Streaks', value: '500+', icon: 'FaFire' },
    ],
  },
  battleZone: {
    stats: [
      { label: 'Battles', value: '1,000+', icon: 'FaGamepad' },
      { label: 'Participants', value: '5,000+', icon: 'FaUsers' },
      { label: 'Challenges', value: '500+', icon: 'FaCode' },
      { label: 'Winners', value: '300+', icon: 'FaTrophy' },
    ],
  },
};

// Fallback college data
const fallbackCollegeData: College[] = [
  {
    id: 'college-1',
    name: 'Indian Institute of Technology, Bombay',
    location: 'Mumbai, Maharashtra',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/1/1d/IIT_Bombay_Logo.svg/1200px-IIT_Bombay_Logo.svg.png',
    userCount: 1250,
  },
  {
    id: 'college-2',
    name: 'Indian Institute of Technology, Delhi',
    location: 'New Delhi, Delhi',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/f/fd/Indian_Institute_of_Technology_Delhi_Logo.svg/1200px-Indian_Institute_of_Technology_Delhi_Logo.svg.png',
    userCount: 1100,
  },
  {
    id: 'college-3',
    name: 'Indian Institute of Technology, Madras',
    location: 'Chennai, Tamil Nadu',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/6/69/IIT_Madras_Logo.svg/1200px-IIT_Madras_Logo.svg.png',
    userCount: 980,
  },
  {
    id: 'college-4',
    name: 'Birla Institute of Technology and Science, Pilani',
    location: 'Pilani, Rajasthan',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/d/d3/BITS_Pilani-Logo.svg/1200px-BITS_Pilani-Logo.svg.png',
    userCount: 850,
  },
  {
    id: 'college-5',
    name: 'Vellore Institute of Technology',
    location: 'Vellore, Tamil Nadu',
    logo_url:
      'https://upload.wikimedia.org/wikipedia/en/thumb/4/4e/Vellore_Institute_of_Technology_seal_2017.svg/1200px-Vellore_Institute_of_Technology_seal_2017.svg.png',
    userCount: 780,
  },
];

// Export fallback data for direct use in components
export const publicDataFallbacks = {
  leaderboardData: fallbackLeaderboardData,
  statsData: fallbackStats,
  collegeData: fallbackCollegeData,
};

// Legacy service for backward compatibility
export const publicDataService = {
  // These methods are kept for backward compatibility
  // New components should use the hooks directly
  async getWeeklyLeaderboard(): Promise<LeaderboardEntry[]> {
    console.warn(
      'publicDataService.getWeeklyLeaderboard is deprecated. Use usePublicLeaderboard hook instead.',
    );
    return fallbackLeaderboardData;
  },

  async getPlatformStats(): Promise<PlatformStats> {
    console.warn(
      'publicDataService.getPlatformStats is deprecated. Use usePublicStats hook instead.',
    );
    return fallbackStats;
  },

  async getTopColleges(): Promise<College[]> {
    console.warn(
      'publicDataService.getTopColleges is deprecated. Use useTopColleges hook instead.',
    );
    return fallbackCollegeData;
  },

  async searchColleges(query: string): Promise<College[]> {
    console.warn(
      'publicDataService.searchColleges is deprecated. Use useSearchColleges hook instead.',
    );
    return fallbackCollegeData.filter((college) =>
      college.name.toLowerCase().includes(query.toLowerCase()),
    );
  },
};
