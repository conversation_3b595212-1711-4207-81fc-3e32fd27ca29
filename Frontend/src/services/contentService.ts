/**
 * @file contentService.ts
 * @description Service for content management API operations using custom hooks
 */

import {
  useAxiosGet,
  useAxiosPost,
  useAxiosPut,
  useAxiosPatch,
  useAxiosDelete,
} from '@/hooks/useAxios';

// API endpoints
export const CONTENT_API = {
  LIST: '/content',
  DETAIL: '/content/{{id}}',
  CREATE: '/content',
  UPDATE: '/content/{{id}}',
  DELETE: '/content/{{id}}',
  STATUS: '/content/status/{{id}}',
};

// Content types
export type ContentType = 'Course' | 'Tutorial' | 'Article' | 'Guide';

// Content status
export type ContentStatus = 'draft' | 'review' | 'published' | 'archived';

// Content interface
export interface IContent {
  id: string;
  title: string;
  description: string;
  type: ContentType;
  status: ContentStatus;
  body: string;
  author: {
    id: string;
    name: string;
  };
  tags: string[];
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  views: number;
  likes: number;
  featured: boolean;
  thumbnail?: string;
}

// API response interface
export interface IApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

// Content list params interface
export interface IContentListParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: ContentType;
  status?: ContentStatus;
  author?: string;
  featured?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Hook for fetching content list with optional filters and pagination
 * @returns Custom hook for content list fetching
 */
export const useContentList = () => {
  return useAxiosGet<{ data: IContent[] }>(CONTENT_API.LIST);
};

/**
 * Hook for fetching content by ID
 * @returns Custom hook for content detail fetching
 */
export const useContentDetail = () => {
  return useAxiosGet<IContent>(CONTENT_API.DETAIL);
};

/**
 * Hook for creating new content
 * @returns Custom hook for content creation
 */
export const useCreateContent = () => {
  return useAxiosPost<
    IContent,
    Omit<IContent, 'id' | 'createdAt' | 'updatedAt' | 'views' | 'likes'>
  >(CONTENT_API.CREATE);
};

/**
 * Hook for updating existing content
 * @returns Custom hook for content updating
 */
export const useUpdateContent = () => {
  return useAxiosPut<IContent, Partial<IContent>>(CONTENT_API.UPDATE);
};

/**
 * Hook for deleting content
 * @returns Custom hook for content deletion
 */
export const useDeleteContent = () => {
  return useAxiosDelete<{ id: string }>(CONTENT_API.DELETE);
};

/**
 * Hook for updating content status
 * @returns Custom hook for content status updating
 */
export const useUpdateContentStatus = () => {
  return useAxiosPatch<IContent, { status: ContentStatus }>(CONTENT_API.STATUS);
};

/**
 * Usage examples:
 *
 * // Fetch content list
 * const [fetchContents, { data, isLoading }] = useContentList();
 * useEffect(() => {
 *   fetchContents({ params: { limit: 10, page: 1 } });
 * }, [fetchContents]);
 *
 * // Fetch content by ID
 * const [fetchContent, { data: content }] = useContentDetail();
 * useEffect(() => {
 *   fetchContent(undefined, { id: '123' });
 * }, [fetchContent]);
 *
 * // Create content
 * const [createContent, { isSuccess }] = useCreateContent();
 * const handleCreate = async (data) => {
 *   const response = await createContent(data);
 *   if (response.success) {
 *     // Handle success
 *   }
 * };
 *
 * // Update content
 * const [updateContent] = useUpdateContent();
 * const handleUpdate = async (id, data) => {
 *   await updateContent(data, undefined, { id });
 * };
 *
 * // Delete content
 * const [deleteContent] = useDeleteContent();
 * const handleDelete = async (id) => {
 *   await deleteContent(undefined, { id });
 * };
 *
 * // Update content status
 * const [updateStatus] = useUpdateContentStatus();
 * const handleStatusUpdate = async (id, status) => {
 *   await updateStatus({ status }, undefined, { id });
 * };
 */
