/**
 * @file adminDashboardService.ts
 * @description Types for admin dashboard data
 */

/**
 * Dashboard metrics interface
 */
export interface IDashboardMetrics {
  userStats: {
    totalUsers: number;
    newUsers: number;
    activeUsers: number;
    growthRate: number;
  };
  platformMetrics: {
    totalRoadmaps: number;
    totalChallenges: number;
    totalArticles: number;
    totalQuizzes: number;
    engagementRate: number;
  };
  activityMetrics: {
    dailyActiveUsers: number;
    weeklyActiveUsers: number;
    monthlyActiveUsers: number;
    averageSessionDuration: number;
    peakUsageTimes: string[];
  };
  systemHealth: {
    databaseStatus: string;
    cacheStatus: string;
    averageResponseTime: number;
    errorRate: number;
  };
}

/**
 * Recent activity interface
 */
export interface IRecentActivity {
  id: string;
  type:
    | 'user_registered'
    | 'content_published'
    | 'role_updated'
    | 'challenge_completed'
    | string;
  title: string;
  description: string;
  timestamp: string;
  user?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

/**
 * Audit log interface from API
 */
export interface IAuditLog {
  id: string;
  action: string;
  timestamp: string;
  details?: Record<string, unknown>;
  user?: {
    id: string;
    name: string;
    avatar?: string;
  };
}

/**
 * Transform audit logs to frontend-friendly activity format
 */
export function transformAuditLogsToActivities(
  logs: IAuditLog[],
): IRecentActivity[] {
  return logs.map((log) => {
    let type = 'other';
    let title = 'System event';
    let description = log.details ? String(log.details) : '';

    // Map audit log actions to frontend activity types
    switch (log.action) {
      case 'USER_CREATED':
        type = 'user_registered';
        title = 'New user registered';
        description = `${log.user?.name || 'A user'} joined the platform`;
        break;
      case 'CONTENT_CREATED':
        type = 'content_published';
        title = 'New content published';
        description =
          log.details && 'title' in log.details
            ? String(log.details.title)
            : 'New content was added';
        break;
      case 'ROLE_UPDATED':
        type = 'role_updated';
        title = 'Role updated';
        description =
          log.details && 'description' in log.details
            ? String(log.details.description)
            : 'A user role was updated';
        break;
      case 'CHALLENGE_COMPLETED':
        type = 'challenge_completed';
        title = 'Challenge completed';
        description =
          log.details && 'description' in log.details
            ? String(log.details.description)
            : 'A challenge was completed';
        break;
      default:
        type = 'other';
        title = log.action.replace(/_/g, ' ').toLowerCase();
        title = title.charAt(0).toUpperCase() + title.slice(1);
        description =
          log.details && 'description' in log.details
            ? String(log.details.description)
            : '';
    }

    return {
      id: log.id,
      type,
      title,
      description,
      timestamp: log.timestamp,
      user: log.user
        ? {
            id: log.user.id,
            name: log.user.name,
            avatar: log.user.avatar,
          }
        : undefined,
    };
  });
}
