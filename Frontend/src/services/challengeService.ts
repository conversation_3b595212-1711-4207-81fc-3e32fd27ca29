/**
 * @file challengeService.ts
 * @description Service for challenge-related API operations
 */
import axios from 'axios';
import { IApiResponse } from '@/types';

import { getDefaultCode } from '@/utils/codeTemplates';

// API endpoints
export const CHALLENGE_API = {
  LIST: '/api/challenges',
  DETAIL: '/api/challenges',
  CREATE: '/api/challenges',
  UPDATE: '/api/challenges',
  DELETE: '/api/challenges',
  FEATURE: '/api/challenges/feature',
  ANALYTICS: '/api/challenges/analytics',
  FEEDBACK: '/api/challenges/feedback',
  TAGS: '/api/challenges/tags',
  BOILERPLATES: '/api/challenges/boilerplates',
  TEST_CASES: '/api/challenges/test-cases',
  SUBMISSIONS: '/api/challenges/submissions',
};

// Types
export interface IChallengeBoilerplate {
  language: string;
  code: string;
  challenge_id: string;
}

export interface ITestCase {
  id: string;
  challenge_id: string;
  input: string;
  expected_output: string;
  is_hidden: boolean;
  name?: string;
}

// Challenge interface
export interface IChallenge {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'Easy' | 'Medium' | 'Hard' | 'Expert';
  type: 'Coding' | 'Quiz' | 'Project' | 'Algorithm';
  status: 'draft' | 'published' | 'archived';
  isFeatured: boolean;
  completionRate: number;
  averageAttempts: number;
  successRate: number;
  totalAttempts: number;
  createdAt: string;
  updatedAt: string;
  estimatedMinutes: number;
  authorId: string;
  authorName: string;
  tags: string[];
  content?: string;
  testCases?: Array<{
    input: string;
    expectedOutput: string;
    isHidden: boolean;
  }>;
  hints?: string[];
  solutionUrl?: string;
}

// Challenge list response
export interface IChallengeListResponse {
  challenges: IChallenge[];
  total: number;
}

// Challenge detail response
export interface IChallengeResponse {
  challenge: IChallenge;
}

// Challenge analytics response
export interface IChallengeAnalyticsResponse {
  analytics: {
    totalAttempts: number;
    completionRate: number;
    averageAttempts: number;
    successRate: number;
    averageTimeSpent: number;
    attemptsOverTime: Array<{
      date: string;
      attempts: number;
    }>;
    completionsByDifficulty: Record<string, number>;
    userDemographics: {
      experienceLevel: Record<string, number>;
      topCountries: Array<{
        country: string;
        percentage: number;
      }>;
    };
    commonErrors: Array<{
      error: string;
      count: number;
    }>;
    completionTime: Record<string, number>;
  };
}

// Challenge feedback response
export interface IChallengeFeedbackResponse {
  feedback: Array<{
    id: string;
    userId: string;
    userName: string;
    userAvatar?: string;
    rating: number;
    comment: string;
    createdAt: string;
    isResolved: boolean;
    adminResponse?: string;
    adminResponseDate?: string;
  }>;
  total: number;
}

export interface IChallengeSubmission {
  id: string;
  user_id: string;
  challenge_id: string;
  code: string;
  language: string;
  status: string;
  runtime_ms: number;
  memory_used_kb: number;
  feedback: string;
  score: number;
  created_at: string;
  test_results?: {
    passed: boolean;
    name?: string;
    input?: string;
    expected_output?: string;
    actual_output?: string;
    error?: string;
    is_hidden?: boolean;
  }[];
}

// Fetch boilerplate code for a specific challenge and language
export const fetchBoilerplate = async (
  challengeId: string,
  language: string,
): Promise<string> => {
  try {
    // TODO: Replace with useAxiosGet
    const response = await axios.get<{ boilerplate: IChallengeBoilerplate }>(
      `/api/challenges/${challengeId}/boilerplates/${language}`,
    );

    if (response.data && response.data.boilerplate) {
      return response.data.boilerplate.code;
    }

    // If no boilerplate found, return default code
    return getDefaultCode(language);
  } catch (error) {
    console.error('Error fetching boilerplate:', error);
    // Fallback to default code template
    return getDefaultCode(language);
  }
};

// Fetch all available boilerplates for a challenge
export const fetchAllBoilerplates = async (
  challengeId: string,
): Promise<Record<string, string>> => {
  try {
    // TODO: Replace with useAxiosGet
    const response = await axios.get<{ boilerplates: IChallengeBoilerplate[] }>(
      `/api/challenges/${challengeId}/boilerplates`,
    );

    if (response.data && response.data.boilerplates) {
      // Convert array to object with language as key
      return response.data.boilerplates.reduce(
        (acc, boilerplate) => {
          acc[boilerplate.language] = boilerplate.code;
          return acc;
        },
        {} as Record<string, string>,
      );
    }

    return {};
  } catch (error) {
    console.error('Error fetching all boilerplates:', error);
    return {};
  }
};

// Fetch test cases for a challenge
export const fetchTestCases = async (
  challengeId: string,
): Promise<ITestCase[]> => {
  try {
    // TODO: Replace with useAxiosGet
    const response = await axios.get<{ test_cases: ITestCase[] }>(
      `/api/challenges/${challengeId}/test-cases`,
    );

    if (response.data && response.data.test_cases) {
      return response.data.test_cases;
    }

    return [];
  } catch (error) {
    console.error('Error fetching test cases:', error);
    return [];
  }
};

// Fetch submission history for a challenge
export const fetchSubmissionHistory = async (
  challengeId: string,
): Promise<IChallengeSubmission[]> => {
  try {
    // TODO: Replace with useAxiosGet
    const response = await axios.get<{ submissions: IChallengeSubmission[] }>(
      `/api/challenges/${challengeId}/submissions`,
    );

    if (response.data && response.data.submissions) {
      return response.data.submissions;
    }

    return [];
  } catch (error) {
    console.error('Error fetching submission history:', error);
    return [];
  }
};

// Submit a solution
export const submitSolution = async (
  challengeId: string,
  code: string,
  language: string,
): Promise<IChallengeSubmission> => {
  try {
    // TODO: Replace with useAxiosPost
    const response = await axios.post<{ submission: IChallengeSubmission }>(
      `/api/challenges/${challengeId}/submit`,
      { code, language },
    );

    if (response.data && response.data.submission) {
      return response.data.submission;
    }

    throw new Error('No submission data returned');
  } catch (error) {
    console.error('Error submitting solution:', error);
    throw error;
  }
};

/**
 * Fetch a challenge by ID
 * @param id - The challenge ID
 * @returns Promise with the challenge data
 */
export const fetchChallengeById = async (
  id: string,
): Promise<IApiResponse<IChallengeResponse>> => {
  try {
    const response = await axios.get(`${CHALLENGE_API.DETAIL}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching challenge:', error);
    throw error;
  }
};

/**
 * Fetch challenge analytics
 * @param id - The challenge ID
 * @param timeRange - Optional time range for analytics
 * @returns Promise with the challenge analytics
 */
export const fetchChallengeAnalytics = async (
  id: string,
  timeRange?: string,
): Promise<IApiResponse<IChallengeAnalyticsResponse>> => {
  try {
    const params = timeRange ? { timeRange } : {};
    const response = await axios.get(`${CHALLENGE_API.ANALYTICS}/${id}`, {
      params,
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching challenge analytics:', error);
    throw error;
  }
};

/**
 * Fetch challenge feedback
 * @param id - The challenge ID
 * @param page - Optional page number
 * @param limit - Optional limit per page
 * @returns Promise with the challenge feedback
 */
export const fetchChallengeFeedback = async (
  id: string,
  page?: number,
  limit?: number,
): Promise<IApiResponse<IChallengeFeedbackResponse>> => {
  try {
    const params = { page, limit };
    const response = await axios.get(`${CHALLENGE_API.FEEDBACK}/${id}`, {
      params,
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching challenge feedback:', error);
    throw error;
  }
};

/**
 * Update challenge feature status
 * @param id - The challenge ID
 * @param isFeatured - Whether the challenge should be featured
 * @returns Promise with the updated challenge
 */
export const updateChallengeFeatureStatus = async (
  id: string,
  isFeatured: boolean,
): Promise<IApiResponse<IChallengeResponse>> => {
  try {
    const response = await axios.post(`${CHALLENGE_API.FEATURE}/${id}`, {
      isFeatured,
    });
    return response.data;
  } catch (error) {
    console.error('Error updating challenge feature status:', error);
    throw error;
  }
};

/**
 * Respond to challenge feedback
 * @param challengeId - The challenge ID
 * @param feedbackId - The feedback ID
 * @param response - The admin response
 * @returns Promise with the updated feedback
 */
export const respondToFeedback = async (
  challengeId: string,
  feedbackId: string,
  response: string,
): Promise<
  IApiResponse<{ feedback: IChallengeFeedbackResponse['feedback'][0] }>
> => {
  try {
    const responseData = await axios.post(
      `${CHALLENGE_API.FEEDBACK}/${challengeId}/${feedbackId}/respond`,
      {
        response,
      },
    );
    return responseData.data;
  } catch (error) {
    console.error('Error responding to feedback:', error);
    throw error;
  }
};

/**
 * Update challenge tags
 * @param id - The challenge ID
 * @param tags - The updated tags
 * @returns Promise with the updated challenge
 */
/**
 * Create a new challenge
 * @param challengeData - The challenge data to create
 * @returns Promise with the created challenge
 */
export const createChallenge = async (
  challengeData: Omit<IChallenge, 'id'>,
): Promise<IApiResponse<IChallengeResponse>> => {
  try {
    const response = await axios.post(CHALLENGE_API.CREATE, challengeData);
    return response.data;
  } catch (error) {
    console.error('Error creating challenge:', error);
    throw error;
  }
};

/**
 * Update an existing challenge
 * @param id - The challenge ID
 * @param challengeData - The challenge data to update
 * @returns Promise with the updated challenge
 */
export const updateChallenge = async (
  id: string,
  challengeData: Partial<IChallenge>,
): Promise<IApiResponse<IChallengeResponse>> => {
  try {
    const response = await axios.put(
      `${CHALLENGE_API.UPDATE}/${id}`,
      challengeData,
    );
    return response.data;
  } catch (error) {
    console.error('Error updating challenge:', error);
    throw error;
  }
};

export const updateChallengeTags = async (
  id: string,
  tags: string[],
): Promise<IApiResponse<IChallengeResponse>> => {
  try {
    const response = await axios.post(`${CHALLENGE_API.TAGS}/${id}`, { tags });
    return response.data;
  } catch (error) {
    console.error('Error updating challenge tags:', error);
    throw error;
  }
};
