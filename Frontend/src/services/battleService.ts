/**
 * @file battleService.ts
 * @description Service for battle-related API endpoints and types
 */

import { IBattle } from '@/app/admin/battles/types';
import { IApiResponse } from '@/types';
import axios from 'axios';

// API endpoints for battles
export const BATTLE_API = {
  LIST: '/battles',
  DETAIL: '/battles',
  CREATE: '/battles',
  UPDATE: '/battles',
  DELETE: '/battles',
  FEATURE: '/battles/feature',
  STATUS: '/battles/status',
  ANALYTICS: '/battles/analytics',
  PARTICIPANTS: '/battles/participants',
  UPDATE_PARTICIPANT_STATUS: '/battles/participants/status',
  CHALLENGES: '/battles/challenges',
  ADD_CHALLENGES: '/battles/challenges/add',
  REMOVE_CHALLENGE: '/battles/challenges/remove',
  UPDATE_CHALLENGE_ORDER: '/battles/challenges/order',
  UPDATE_CHALLENGE_REQUIRED: '/battles/challenges/required',
};

/**
 * Fetch a battle by ID
 * @param id - The battle ID
 * @returns Promise with the battle data
 */
export const fetchBattleById = async (
  id: string,
): Promise<IApiResponse<IBattleResponse>> => {
  try {
    const response = await axios.get(`${BATTLE_API.DETAIL}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching battle:', error);
    throw error;
  }
};

/**
 * Fetch challenges for a battle
 * @param battleId - The battle ID
 * @returns Promise with the battle challenges
 */
export const fetchBattleChallenges = async (
  battleId: string,
): Promise<IApiResponse<IBattleChallengesResponse>> => {
  try {
    const response = await axios.get(`${BATTLE_API.CHALLENGES}/${battleId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching battle challenges:', error);
    throw error;
  }
};

/**
 * Fetch available challenges for adding to a battle
 * @param params - Search and filter parameters
 * @returns Promise with available challenges
 */
export const fetchAvailableChallenges = async (params: {
  search?: string;
  type?: string;
  difficulty?: string;
  page?: number;
  limit?: number;
}): Promise<IApiResponse<IChallengeListResponse>> => {
  try {
    const response = await axios.get(BATTLE_API.CHALLENGES, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching available challenges:', error);
    throw error;
  }
};

/**
 * Create a new battle
 * @param battleData - The battle data to create
 * @returns Promise with the created battle
 */
export const createBattle = async (
  battleData: Omit<IBattle, 'id'>,
): Promise<IApiResponse<IBattleResponse>> => {
  try {
    const response = await axios.post(BATTLE_API.CREATE, battleData);
    return response.data;
  } catch (error) {
    console.error('Error creating battle:', error);
    throw error;
  }
};

/**
 * Update an existing battle
 * @param id - The battle ID
 * @param battleData - The updated battle data
 * @returns Promise with the updated battle
 */
export const updateBattle = async (
  id: string,
  battleData: Partial<IBattle>,
): Promise<IApiResponse<IBattleResponse>> => {
  try {
    const response = await axios.put(`${BATTLE_API.UPDATE}/${id}`, battleData);
    return response.data;
  } catch (error) {
    console.error('Error updating battle:', error);
    throw error;
  }
};

/**
 * Add challenges to a battle
 * @param params - Parameters for adding challenges
 * @returns Promise with the updated battle challenges
 */
export const addChallengesToBattle = async (
  params: IAddChallengesToBattleParams,
): Promise<IApiResponse<IBattleChallengesResponse>> => {
  try {
    const response = await axios.post(BATTLE_API.ADD_CHALLENGES, params);
    return response.data;
  } catch (error) {
    console.error('Error adding challenges to battle:', error);
    throw error;
  }
};

/**
 * Remove a challenge from a battle
 * @param params - Parameters for removing a challenge
 * @returns Promise with the updated battle challenges
 */
export const removeChallengeFromBattle = async (
  params: IRemoveChallengeFromBattleParams,
): Promise<IApiResponse<IBattleChallengesResponse>> => {
  try {
    const response = await axios.post(BATTLE_API.REMOVE_CHALLENGE, params);
    return response.data;
  } catch (error) {
    console.error('Error removing challenge from battle:', error);
    throw error;
  }
};

/**
 * Update the order of a challenge in a battle
 * @param params - Parameters for updating challenge order
 * @returns Promise with the updated battle challenges
 */
export const updateChallengeOrder = async (
  params: IUpdateChallengeOrderParams,
): Promise<IApiResponse<IBattleChallengesResponse>> => {
  try {
    const response = await axios.post(
      BATTLE_API.UPDATE_CHALLENGE_ORDER,
      params,
    );
    return response.data;
  } catch (error) {
    console.error('Error updating challenge order:', error);
    throw error;
  }
};

/**
 * Update whether a challenge is required in a battle
 * @param params - Parameters for updating challenge required status
 * @returns Promise with the updated battle challenges
 */
export const updateChallengeRequired = async (
  params: IUpdateChallengeRequiredParams,
): Promise<IApiResponse<IBattleChallengesResponse>> => {
  try {
    const response = await axios.post(
      BATTLE_API.UPDATE_CHALLENGE_REQUIRED,
      params,
    );
    return response.data;
  } catch (error) {
    console.error('Error updating challenge required status:', error);
    throw error;
  }
};

// Response type for battle list
export interface IBattleListResponse {
  battles: IBattle[];
  total: number;
}

// Response type for single battle
export interface IBattleResponse {
  battle: IBattle;
}

// Parameters for updating battle feature status
export interface IBattleFeatureParams {
  battleId: string;
  isFeatured: boolean;
}

// Parameters for updating battle status
export interface IBattleStatusParams {
  battleId: string;
  status: 'Scheduled' | 'Active' | 'Completed' | 'Cancelled';
}

// Parameters for bulk actions on battles
export interface IBulkBattleActionParams {
  action: 'delete' | 'feature' | 'unfeature';
  battleIds: string[];
}

// Parameters for updating participant status
export interface IParticipantStatusUpdateParams {
  participantId: string;
  status: 'active' | 'completed' | 'dropped';
}

// Battle analytics data types
export interface IBattleAnalyticsResponse {
  success: boolean;
  message?: string;
  data?: IBattleAnalytics;
}

export interface IBattleAnalytics {
  participationStats: {
    totalParticipants: number;
    activeParticipants: number;
    completionRate: number;
    dropoutRate: number;
  };
  performanceStats: {
    averageScore: number;
    highestScore: number;
    lowestScore: number;
    averageCompletionTime: number; // in minutes
  };
  engagementStats: {
    averageTimeSpent: number; // in minutes
    averageAttemptsPerChallenge: number;
    socialShares: number;
    commentsCount: number;
  };
  timeSeriesData: {
    dates: string[];
    participation: number[];
    completion: number[];
  };
}

// Challenge interfaces
export interface IChallenge {
  id: string;
  title: string;
  description: string;
  type: string;
  difficulty: string;
  points: number;
  timeLimit?: number; // in minutes
  order: number;
  isRequired: boolean;
}

// Response type for challenge list
export interface IChallengeListResponse {
  success: boolean;
  message?: string;
  data?: {
    challenges: IChallenge[];
    total: number;
  };
  meta?: {
    page: number;
    limit: number;
    totalPages: number;
    totalItems: number;
  };
}

// Response type for battle challenges
export interface IBattleChallengesResponse {
  success: boolean;
  message?: string;
  data?: {
    challenges: IChallenge[];
    total: number;
  };
}

// Parameters for adding challenges to a battle
export interface IAddChallengesToBattleParams {
  battleId: string;
  challengeIds: string[];
}

// Parameters for removing a challenge from a battle
export interface IRemoveChallengeFromBattleParams {
  battleId: string;
  challengeId: string;
}

// Parameters for updating challenge order
export interface IUpdateChallengeOrderParams {
  battleId: string;
  challengeId: string;
  newOrder: number;
}

// Parameters for updating challenge required status
export interface IUpdateChallengeRequiredParams {
  battleId: string;
  challengeId: string;
  isRequired: boolean;
}
