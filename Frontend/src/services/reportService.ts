/**
 * @file reportService.ts
 * @description Service for report-related API endpoints and types
 */

import {
  ExportFormat,
  IMetric,
  IDimension,
  IReport,
  IReportSchedule,
  IVisualization,
} from '@/types/reports';

// Base API response interface
export interface IBaseApiResponse<T = unknown> {
  success: boolean;
  message?: string;
  data?: T;
}

// API endpoints for reports
export const REPORT_API = {
  LIST: '/analytics/reports',
  CREATE: '/analytics/reports',
  DETAIL: '/analytics/reports',
  UPDATE: '/analytics/reports',
  DELETE: '/analytics/reports',
  GENERATE: '/analytics/reports/generate',
  SCHEDULE: '/analytics/reports/schedule',
  METRICS: '/analytics/metrics',
  DIMENSIONS: '/analytics/dimensions',
  RETENTION: '/analytics/settings/retention',
};

// Response type for metrics API
// This interface extends IBaseApiResponse with a specific data structure for metrics
export interface IMetricsResponse
  extends IBaseApiResponse<{ metrics: IMetric[] }> {
  // Extended properties specific to metrics response if needed
  timestamp?: string;
  count?: number;
}

// Response type for dimensions API
// This interface extends IBaseApiResponse with a specific data structure for dimensions
export interface IDimensionsResponse
  extends IBaseApiResponse<{ dimensions: IDimension[] }> {
  // Extended properties specific to dimensions response if needed
  timestamp?: string;
  count?: number;
}

// Request type for creating a report
export interface IReportCreateRequest {
  name: string;
  description: string;
  metrics: string[];
  dimensions: string[];
  visualizations: IVisualization[];
  schedule: IReportSchedule;
}

// Request type for updating a report
export interface IReportUpdateRequest extends Omit<IReportCreateRequest, 'id'> {
  id: string;
}

// Response type for a single report
// This interface extends IBaseApiResponse with a specific data structure for a single report
export interface IReportResponse extends IBaseApiResponse<{ report: IReport }> {
  // Extended properties specific to report response if needed
  timestamp?: string;
  version?: string;
}

// Response type for listing reports
// This interface extends IBaseApiResponse with a specific data structure for a list of reports
export interface IReportsListResponse
  extends IBaseApiResponse<{ reports: IReport[] }> {
  // Extended properties specific to reports list response if needed
  timestamp?: string;
  count?: number;
  totalPages?: number;
}

// Parameters for updating report schedule
export interface IScheduleUpdateParams {
  reportId: string;
  enabled?: boolean;
  frequency?: string;
  recipients?: string[];
  exportFormat?: ExportFormat;
}

// Parameters for generating a report
export interface IReportGenerateParams {
  reportId: string;
  format: ExportFormat;
}

// Response for generating a report
// This interface extends IBaseApiResponse with a specific data structure for report generation response
export interface IReportGenerateResponse
  extends IBaseApiResponse<{ url: string }> {
  // Extended properties specific to report generation response if needed
  generatedAt?: string;
  expiresAt?: string;
  fileSize?: number;
}

// Response for deleting a report
export interface IReportDeleteResponse
  extends IBaseApiResponse<{ id: string }> {
  // Extended properties specific to delete response if needed
  deletedAt?: string;
}

// Parameters for updating data retention period
export interface IRetentionUpdateParams {
  period: string;
}

// Response for updating data retention period
export interface IRetentionUpdateResponse {
  success: boolean;
  message?: string;
}
