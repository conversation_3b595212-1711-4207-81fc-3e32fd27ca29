/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */
'use client';

import { useEffect, useState } from 'react';

import {
  ArrowUpDown,
  Check,
  ChevronDown,
  ChevronUp,
  Clock,
  Code,
  Database,
  Eye,
  Filter,
  X,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

/**
 * @file index.tsx
 * @description React component for SolutionComparison
 */

export interface Solution {
  id: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
  };
  language: string;
  status:
    | 'accepted'
    | 'wrong_answer'
    | 'runtime_error'
    | 'time_limit_exceeded'
    | 'compilation_error';
  runtime_ms: number;
  memory_kb: number;
  code_length: number;
  submitted_at: string;
  is_current?: boolean;
}

interface SolutionComparisonProps {
  solutions: Solution[];
  currentSolutionId?: string;
  onViewSolution?: (solutionId: string) => void;
  className?: string;
}

type SortField = 'runtime_ms' | 'memory_kb' | 'code_length' | 'submitted_at';
type SortDirection = 'asc' | 'desc';

export default function SolutionComparison({
  solutions,
  currentSolutionId,
  onViewSolution,
  className = '',
}: SolutionComparisonProps) {
  const [languageFilter, setLanguageFilter] = useState<string>('all');
  const [sortField, setSortField] = useState<SortField>('runtime_ms');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [filteredSolutions, setFilteredSolutions] = useState<Solution[]>([]);

  // Get unique languages from solutions
  const languages = Array.from(new Set(solutions.map((s) => s.language)));

  // Filter and sort solutions
  useEffect(() => {
    let filtered = [...solutions];

    // Apply language filter
    if (languageFilter !== 'all') {
      filtered = filtered.filter((s) => s.language === languageFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (sortField === 'submitted_at') {
        const aDate = new Date(aValue as string).getTime();
        const bDate = new Date(bValue as string).getTime();
        return sortDirection === 'asc' ? aDate - bDate : bDate - aDate;
      }

      return sortDirection === 'asc'
        ? (aValue as number) - (bValue as number)
        : (bValue as number) - (aValue as number);
    });

    // Mark current solution
    if (currentSolutionId) {
      filtered = filtered.map((s) => ({
        ...s,
        is_current: s.id === currentSolutionId,
      }));
    }

    setFilteredSolutions(filtered);
  }, [solutions, languageFilter, sortField, sortDirection, currentSolutionId]);

  // Handle sort change
  const handleSort = (field: SortField) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default direction
      setSortField(field);
      setSortDirection(field === 'submitted_at' ? 'desc' : 'asc');
    }
  };

  // Get sort icon
  const getSortIcon = (field: SortField) => {
    if (field !== sortField)
      return <ArrowUpDown className="ml-1 h-3 w-3 text-muted-foreground" />;
    return sortDirection === 'asc' ? (
      <ChevronUp className="ml-1 h-3 w-3" />
    ) : (
      <ChevronDown className="ml-1 h-3 w-3" />
    );
  };

  // Format runtime
  const formatRuntime = (ms: number) => {
    return ms < 1000 ? `${ms} ms` : `${(ms / 1000).toFixed(2)} s`;
  };

  // Format memory
  const formatMemory = (kb: number) => {
    return kb < 1024 ? `${kb} KB` : `${(kb / 1024).toFixed(2)} MB`;
  };

  // Format code length
  const formatCodeLength = (length: number) => {
    return length < 1024 ? `${length} B` : `${(length / 1024).toFixed(2)} KB`;
  };

  // Get status badge
  const getStatusBadge = (status: Solution['status']) => {
    const variants = {
      accepted:
        'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400',
      wrong_answer:
        'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400',
      runtime_error:
        'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-400',
      time_limit_exceeded:
        'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400',
      compilation_error:
        'bg-purple-100 text-purple-800 border-purple-300 dark:bg-purple-900/30 dark:text-purple-400',
    };

    const labels = {
      accepted: 'Accepted',
      wrong_answer: 'Wrong Answer',
      runtime_error: 'Runtime Error',
      time_limit_exceeded: 'Time Limit',
      compilation_error: 'Compilation Error',
    };

    const icons = {
      accepted: <Check className="mr-1 h-3 w-3" />,
      wrong_answer: <X className="mr-1 h-3 w-3" />,
      runtime_error: <X className="mr-1 h-3 w-3" />,
      time_limit_exceeded: <Clock className="mr-1 h-3 w-3" />,
      compilation_error: <Code className="mr-1 h-3 w-3" />,
    };

    return (
      <Badge
        variant="outline"
        className={cn('flex items-center whitespace-nowrap', variants[status])}
      >
        {icons[status]}
        <span>{labels[status]}</span>
      </Badge>
    );
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h3 className="text-lg font-medium">Solution Comparison</h3>

        <div className="flex items-center gap-2">
          <Select value={languageFilter} onValueChange={setLanguageFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Languages</SelectItem>
              {languages.map((lang) => (
                <SelectItem key={lang} value={lang}>
                  {lang}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[180px]">User</TableHead>
              <TableHead>Language</TableHead>
              <TableHead>Status</TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('runtime_ms')}
              >
                <div className="flex items-center">
                  Runtime
                  {getSortIcon('runtime_ms')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('memory_kb')}
              >
                <div className="flex items-center">
                  Memory
                  {getSortIcon('memory_kb')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('code_length')}
              >
                <div className="flex items-center">
                  Code Length
                  {getSortIcon('code_length')}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer"
                onClick={() => handleSort('submitted_at')}
              >
                <div className="flex items-center">
                  Submitted
                  {getSortIcon('submitted_at')}
                </div>
              </TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSolutions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  No solutions found
                </TableCell>
              </TableRow>
            ) : (
              filteredSolutions.map((solution) => (
                <TableRow
                  key={solution.id}
                  className={cn(solution.is_current && 'bg-muted/50')}
                >
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      {solution.is_current && (
                        <Badge variant="secondary" className="mr-1">
                          You
                        </Badge>
                      )}
                      <span className="truncate">{solution.user.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{solution.language}</Badge>
                  </TableCell>
                  <TableCell>{getStatusBadge(solution.status)}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Clock className="mr-1 h-4 w-4 text-blue-500" />
                      <span>{formatRuntime(solution.runtime_ms)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Database className="mr-1 h-4 w-4 text-purple-500" />
                      <span>{formatMemory(solution.memory_kb)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Code className="mr-1 h-4 w-4 text-gray-500" />
                      <span>{formatCodeLength(solution.code_length)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {new Date(solution.submitted_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewSolution?.(solution.id)}
                      disabled={solution.is_current}
                    >
                      <Eye className="mr-1 h-4 w-4" />
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
