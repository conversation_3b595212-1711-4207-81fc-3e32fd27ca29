/**
 * @file index.tsx
 * @description React component for Icons
 */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { SVGProps } from 'react';

export function BookIcon(props: SVGProps<any>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
    >
      <path
        fillRule="evenodd"
        d="M6 2a1 1 0 00-1 1v18a1 1 0 001 1h13a1 1 0 001-1V3a1 1 0 00-1-1H6zm10.707 8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L13 10.414V17a1 1 0 102 0v-6.586l1.293 1.293a1 1 0 001.414-1.414z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function VideoIcon(props: SVGProps<any>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
    >
      <path
        fillRule="evenodd"
        d="M3 4a1 1 0 011-1h16a1 1 0 011 1v15a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm13 5a1 1 0 00-1.707-.707L8 13.586V7a1 1 0 10-2 0v10a1 1 0 002 0v-6.586l6.293 6.293A1 1 0 0016 16V9z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function FileIcon(props: SVGProps<any>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
    >
      <path
        fillRule="evenodd"
        d="M5 3a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V7.414a1 1 0 00-.293-.707l-5.414-5.414a1 1 0 00-.707-.293H5zM7 8h10a1 1 0 010 2H7a1 1 0 110-2zm0 4h10a1 1 0 010 2H7a1 1 0 110-2zm0 4h5a1 1 0 010 2H7a1 1 0 110-2z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function PuzzleIcon(props: SVGProps<any>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
    >
      <path
        fillRule="evenodd"
        d="M4.75 3.5a.75.75 0 000 1.5H8a1 1 0 001 1v2.25a.75.75 0 01-1.5 0V8a1 1 0 00-1-1H4.75a.75.75 0 01-.75-.75V4.75a.75.75 0 01.75-.75h1.5a1 1 0 00.75-.75V3a.75.75 0 011.5 0v.5a1 1 0 00.75.75h1.5a.75.75 0 010 1.5H10a1 1 0 00-1 1v2.25a.75.75 0 01-.75.75H7a1 1 0 00-1 1v1.5a.75.75 0 01-.75.75H3a1 1 0 00-1 1v4.75a.75.75 0 001.5 0V17a1 1 0 001-1h2.25a.75.75 0 010 1.5H5a1 1 0 00-1 1v1.25a.75.75 0 001.5 0V18a1 1 0 001-1v-1.5a.75.75 0 01.75-.75H10a1 1 0 001-1V12a1 1 0 001-1v-1.5a.75.75 0 01.75-.75H14a1 1 0 001-1V4.75a.75.75 0 01.75-.75h1.5a.75.75 0 010 1.5H15a1 1 0 00-1 1v2.25a.75.75 0 01-.75.75H12a1 1 0 00-1 1v1.5a.75.75 0 01-.75.75H9a1 1 0 00-1 1v2.25a.75.75 0 01-1.5 0V14a1 1 0 00-1-1H4.75a.75.75 0 01-.75-.75V11.5a1 1 0 00-1-1V9.75a.75.75 0 01.75-.75H6a1 1 0 001-1V5.25a.75.75 0 00-.75-.75h-1.5a1 1 0 00-1-1z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function BriefcaseIcon(props: SVGProps<any>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
    >
      <path
        fillRule="evenodd"
        d="M4 6a2 2 0 00-2 2v11a1 1 0 001 1h18a1 1 0 001-1V8a2 2 0 00-2-2H4zm0 2h16v11H4V8zM9 4a1 1 0 00-1 1v1h8V5a1 1 0 00-1-1H9zm1 1h4V5h-4v1z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function PresentationIcon(props: SVGProps<any>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
    >
      <path
        fillRule="evenodd"
        d="M3 2a1 1 0 000 2h18a1 1 0 000-2H3zm4.293 7.293a1 1 0 011.414 0L12 12.586l3.293-3.293a1 1 0 011.414 1.414L13.414 14l3.293 3.293a1 1 0 01-1.414 1.414L12 15.414l-3.293 3.293a1 1 0 01-1.414-1.414L10.586 14 7.293 10.707a1 1 0 010-1.414zM5.5 17a1 1 0 010 2h13a1 1 0 010-2h-13z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function GlobeIcon(props: SVGProps<any>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      className="h-6 w-6"
    >
      <path
        fillRule="evenodd"
        d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18a8 8 0 110-16 8 8 0 010 16zm3.5-9h-2.25V7.75a.75.75 0 10-1.5 0V11H8.5a.75.75 0 100 1.5h2.25V15.5a.75.75 0 101.5 0V12.5h2.25a.75.75 0 000-1.5z"
        clipRule="evenodd"
      />
    </svg>
  );
}
