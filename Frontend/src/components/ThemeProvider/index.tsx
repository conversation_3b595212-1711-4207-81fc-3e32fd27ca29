/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */
'use client';

import * as React from 'react';

import { Attribute, ThemeProvider as NextThemesProvider } from 'next-themes';

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

/**
 * @file index.tsx
 * @description React component for ThemeProvider
 */

export function ThemeProvider({
  children,
  ...props
}: {
  children: React.ReactNode;
  attribute?: Attribute;
  defaultTheme?: string;
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
}) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;
}
