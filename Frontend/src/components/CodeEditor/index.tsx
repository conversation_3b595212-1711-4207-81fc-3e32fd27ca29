/**
 * @file index.tsx
 * @description React component for CodeEditor
 */
'use client';

import { useEffect, useRef, useState } from 'react';

import Editor, { Monaco } from '@monaco-editor/react';
import { CheckCircle, Play } from 'lucide-react';
import { editor } from 'monaco-editor';

import { But<PERSON> } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

/**
 * @file index.tsx
 * @description React component for CodeEditor
 */

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: string;
  height?: string;
  onRun?: () => void;
  onSubmit?: () => void;
  isRunning?: boolean;
  isSubmitting?: boolean;
  readOnly?: boolean;
  autoSaveKey?: string;
}

export default function CodeEditor({
  value,
  onChange,
  language,
  height = '400px',
  onRun,
  onSubmit,
  isRunning = false,
  isSubmitting = false,
  readOnly = false,
  autoSaveKey,
}: CodeEditorProps) {
  const [isEditorReady, setIsEditorReady] = useState(false);
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);

  // Handle editor initialization
  const handleEditorDidMount = (
    editor: editor.IStandaloneCodeEditor,
    monaco: Monaco,
  ) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
    setIsEditorReady(true);

    // Focus the editor
    editor.focus();

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      if (onRun && !isRunning) {
        onRun();
      }
    });

    editor.addCommand(
      monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.Enter,
      () => {
        if (onSubmit && !isSubmitting) {
          onSubmit();
        }
      },
    );

    // Load saved code if autoSaveKey is provided
    if (autoSaveKey && typeof window !== 'undefined') {
      const savedCode = localStorage.getItem(autoSaveKey);
      if (savedCode && savedCode !== value) {
        onChange(savedCode);
      }
    }
  };

  // Auto-save code to localStorage
  useEffect(() => {
    if (autoSaveKey && value && typeof window !== 'undefined') {
      const saveTimer = setTimeout(() => {
        localStorage.setItem(autoSaveKey, value);
      }, 1000); // Debounce save to avoid excessive writes

      return () => clearTimeout(saveTimer);
    }
  }, [value, autoSaveKey]);

  // Detect if we're on a mobile device
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on mount
    checkMobile();

    // Add resize listener
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Set up editor options
  const editorOptions: editor.IStandaloneEditorConstructionOptions = {
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    fontSize: isMobile ? 12 : 14,
    fontFamily: 'JetBrains Mono, Menlo, Monaco, Consolas, monospace',
    lineNumbers: isMobile ? 'off' : 'on',
    folding: !isMobile,
    automaticLayout: true,
    tabSize: 2,
    wordWrap: 'on',
    readOnly,
    scrollbar: {
      verticalScrollbarSize: isMobile ? 8 : 10,
      horizontalScrollbarSize: isMobile ? 8 : 10,
      alwaysConsumeMouseWheel: false,
    },
    // Mobile-specific options
    ...(isMobile && {
      glyphMargin: false,
      lineDecorationsWidth: 5,
      lineNumbersMinChars: 2,
      padding: { top: 5, bottom: 5 },
      renderIndentGuides: false,
      renderLineHighlight: 'none',
      quickSuggestions: false,
    }),
  };

  // Map language to Monaco language
  const getMonacoLanguage = (lang: string): string => {
    const languageMap: Record<string, string> = {
      javascript: 'javascript',
      python: 'python',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      csharp: 'csharp',
      go: 'go',
      ruby: 'ruby',
      rust: 'rust',
      typescript: 'typescript',
      php: 'php',
      swift: 'swift',
      kotlin: 'kotlin',
    };

    return languageMap[lang.toLowerCase()] || 'plaintext';
  };

  return (
    <div className="flex h-full flex-col">
      <div className="relative flex-grow">
        {!isEditorReady && (
          <div className="absolute inset-0 flex items-center justify-center bg-muted/20">
            <Skeleton className="h-full w-full" />
          </div>
        )}
        <Editor
          height={isMobile ? '250px' : height}
          language={getMonacoLanguage(language)}
          value={value}
          onChange={(value) => onChange(value || '')}
          onMount={handleEditorDidMount}
          options={editorOptions}
          className="rounded-md border"
          loading={<Skeleton className="h-full w-full" />}
        />
      </div>

      {(onRun || onSubmit) && (
        <div className="mt-4 flex flex-col gap-3 sm:flex-row">
          {onRun && (
            <Button
              onClick={onRun}
              disabled={isRunning || !isEditorReady}
              variant="secondary"
              className="flex-1"
              size={isMobile ? 'sm' : 'default'}
            >
              {isRunning ? (
                <>
                  <span className="mr-2 animate-spin">⟳</span>
                  <span className="sm:inline">
                    {isMobile ? 'Running' : 'Running...'}
                  </span>
                </>
              ) : (
                <>
                  <Play className="mr-2 h-4 w-4" />
                  <span className="sm:inline">Run Code</span>
                  <span className="hidden sm:inline"> (Ctrl+Enter)</span>
                </>
              )}
            </Button>
          )}
          {onSubmit && (
            <Button
              onClick={onSubmit}
              disabled={isSubmitting || !isEditorReady}
              variant="default"
              className="to-primary/80 hover:from-primary/90 flex-1 bg-gradient-to-r from-primary hover:to-primary"
              size={isMobile ? 'sm' : 'default'}
            >
              {isSubmitting ? (
                <>
                  <span className="mr-2 animate-spin">⟳</span>
                  <span className="sm:inline">
                    {isMobile ? 'Submitting' : 'Submitting...'}
                  </span>
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  <span className="sm:inline">Submit</span>
                  <span className="hidden sm:inline"> (Ctrl+Shift+Enter)</span>
                </>
              )}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
