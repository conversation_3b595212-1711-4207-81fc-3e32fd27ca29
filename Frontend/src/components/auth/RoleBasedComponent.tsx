/**
 * @file RoleBasedComponent.tsx
 * @description Component that conditionally renders content based on user roles
 */
import { ReactNode } from 'react';

import { useRoles } from '@/hooks/useRoles';

type RoleType = 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'USER';

interface IRoleBasedComponentProps {
  /** The role(s) required to view the content */
  requiredRoles: RoleType | RoleType[];
  /** Whether all specified roles are required (true) or any of them (false) */
  requireAll?: boolean;
  /** Content to display if the user has the required role(s) */
  children: ReactNode;
  /** Optional content to display if the user doesn't have the required role(s) */
  fallback?: ReactNode;
}

/**
 * Component that conditionally renders content based on user roles
 */
export function RoleBasedComponent({
  requiredRoles,
  requireAll = false,
  children,
  fallback = null,
}: IRoleBasedComponentProps) {
  const { hasAnyRole, hasAllRoles } = useRoles();

  // Convert single role to array for consistent handling
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

  // Check if user has the required role(s)
  const hasRequiredRoles = requireAll ? hasAllRoles(roles) : hasAnyRole(roles);

  // Render children if user has required roles, otherwise render fallback
  return hasRequiredRoles ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component that only renders content for admin users
 */
export function AdminOnly({
  children,
  fallback = null,
}: Omit<IRoleBasedComponentProps, 'requiredRoles' | 'requireAll'>) {
  return (
    <RoleBasedComponent requiredRoles="ADMIN" fallback={fallback}>
      {children}
    </RoleBasedComponent>
  );
}

/**
 * Component that only renders content for moderator users
 */
export function ModeratorOnly({
  children,
  fallback = null,
}: Omit<IRoleBasedComponentProps, 'requiredRoles' | 'requireAll'>) {
  return (
    <RoleBasedComponent requiredRoles="MODERATOR" fallback={fallback}>
      {children}
    </RoleBasedComponent>
  );
}

/**
 * Component that only renders content for contributor users
 */
export function ContributorOnly({
  children,
  fallback = null,
}: Omit<IRoleBasedComponentProps, 'requiredRoles' | 'requireAll'>) {
  return (
    <RoleBasedComponent requiredRoles="CONTRIBUTOR" fallback={fallback}>
      {children}
    </RoleBasedComponent>
  );
}

/**
 * Component that renders content for users with staff privileges (admin or moderator)
 */
export function StaffOnly({
  children,
  fallback = null,
}: Omit<IRoleBasedComponentProps, 'requiredRoles' | 'requireAll'>) {
  return (
    <RoleBasedComponent
      requiredRoles={['ADMIN', 'MODERATOR']}
      fallback={fallback}
    >
      {children}
    </RoleBasedComponent>
  );
}
