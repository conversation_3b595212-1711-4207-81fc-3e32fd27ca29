/**
 * @file CommentSection.tsx
 * @description Component for displaying and managing comments on roadmaps
 */
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';
import { Comment } from '@/types';

import { CommentItem } from './CommentItem';

/**
 * Props for the CommentSection component
 */
interface ICommentSectionProps {
  /**
   * Unique identifier for the roadmap
   */
  roadmapId: string;

  /**
   * Whether the comment section is open and visible
   */
  isOpen: boolean;
}

/**
 * API response structure for comment-related requests
 * @template T - The type of data contained in the response
 */
interface IApiResponse<T> {
  /**
   * The main data payload
   */
  data: T;

  /**
   * Metadata for pagination
   */
  meta: {
    /**
     * Total number of items available
     */
    total: number;

    /**
     * Current page number
     */
    currentPage: number;

    /**
     * Total number of pages
     */
    totalPages: number;

    /**
     * Number of items per page
     */
    limit: number;

    /**
     * Whether there are more pages available
     */
    hasNextPage: boolean;
  };

  /**
   * Whether the request was successful
   */
  success: boolean;

  /**
   * Success or error message
   */
  message: string;

  /**
   * Error details if the request failed
   */
  error: null | string;
}

/**
 * Type guard to validate if an unknown object is a valid Comment
 *
 * @param comment - The object to validate
 * @returns True if the object is a valid Comment, false otherwise
 */
const isValidComment = (comment: unknown): comment is Comment => {
  return (
    comment !== null &&
    typeof comment === 'object' &&
    'id' in comment &&
    'content' in comment &&
    'user' in comment &&
    'created_at' in comment &&
    'updated_at' in comment &&
    'isLiked' in comment &&
    '_count' in comment
  );
};

/**
 * CommentSection - Component for displaying and managing comments on roadmaps
 *
 * This component provides a form for adding new comments and displays
 * a list of existing comments for a roadmap. It handles loading, submitting,
 * and updating comments.
 *
 * @example
 * <CommentSection roadmapId="123" isOpen={true} />
 *
 * @param props - Component props
 * @returns The rendered CommentSection component
 */
export const CommentSection: React.FC<ICommentSectionProps> = ({
  roadmapId,
  isOpen,
}) => {
  /**
   * State for storing the list of comments
   */
  const [comments, setComments] = useState<Comment[]>([]);

  /**
   * State for the new comment input field
   */
  const [newComment, setNewComment] = useState('');

  /**
   * State for tracking comment submission status
   */
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Hook for fetching comments from the API
   */
  const [fetchComments, { data: fetchedComments, isLoading }] = useAxiosGet<
    IApiResponse<Comment[]>
  >(`/roadmaps/${roadmapId}/comments`);

  /**
   * Hook for posting new comments to the API
   */
  const [postComment] = useAxiosPost<{
    data: {
      data: Comment;
    };
    success: boolean;
    message: string;
    error: null | string;
  }>(`/roadmaps/${roadmapId}/comments`);

  /**
   * Fetch comments when the comment section is opened
   */
  useEffect(() => {
    if (isOpen) {
      fetchComments();
    }
  }, [isOpen, fetchComments]);

  /**
   * Update local comments state when fetched comments are received
   */
  useEffect(() => {
    if (fetchedComments?.success && fetchedComments?.data) {
      setComments(fetchedComments.data);
    }
  }, [fetchedComments]);

  /**
   * Updates a comment in the local state when it's modified
   *
   * @param updatedComment - The updated comment object
   */
  const handleCommentUpdate = (updatedComment: Comment) => {
    setComments((prevComments) =>
      prevComments.map((comment) =>
        comment.id === updatedComment.id ? updatedComment : comment,
      ),
    );
  };

  /**
   * Handles the submission of a new comment
   *
   * @param e - The form submission event
   */
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    // Don't submit empty comments
    if (!newComment.trim()) return;

    setIsSubmitting(true);
    try {
      // Send the comment to the API
      const response = await postComment({ content: newComment });

      // Process successful response
      if (response?.success && response?.data?.data) {
        const newCommentData = response.data.data;
        if (isValidComment(newCommentData)) {
          // Clear the input field
          setNewComment('');

          // Add the new comment to the top of the list
          setComments((prevComments) => [newCommentData, ...prevComments]);

          // Show success message
          toast.success('Comment added successfully');
        }
      }
    } catch (error) {
      // Handle error
      console.error('Error posting comment:', error);
      toast.error('Failed to add comment');
    } finally {
      // Reset submission state
      setIsSubmitting(false);
    }
  };

  /**
   * Don't render anything if the comment section is closed
   */
  if (!isOpen) return null;

  return (
    <div className="space-y-6 border-t pt-6">
      <h3 className="text-lg font-semibold">Comments</h3>

      {/* Comment Form - Allows users to submit new comments */}
      <form onSubmit={handleSubmitComment} className="space-y-4">
        <Textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment..."
          className="min-h-[100px]"
          aria-label="Comment text"
        />
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting || !newComment.trim()}
            className="hover:bg-primary/90 bg-primary"
            aria-busy={isSubmitting}
          >
            {/* Show loading spinner when submitting */}
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Post Comment
          </Button>
        </div>
      </form>

      {/* Comments List - Displays existing comments or loading/empty states */}
      <div className="space-y-6">
        {/* Loading state */}
        {isLoading ? (
          <div
            className="flex justify-center py-8"
            aria-live="polite"
            aria-busy="true"
          >
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : comments && comments.length > 0 ? (
          /* Comments exist - render the list */
          comments.map((comment) => {
            return (
              <CommentItem
                key={comment.id}
                comment={comment}
                roadmapId={roadmapId}
                onCommentUpdate={handleCommentUpdate}
              />
            );
          })
        ) : (
          /* Empty state - no comments yet */
          <p className="text-center text-muted-foreground" aria-live="polite">
            No comments yet. Be the first to comment!
          </p>
        )}
      </div>
    </div>
  );
};
