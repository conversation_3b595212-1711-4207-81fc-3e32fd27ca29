/**
 * @file RoadmapCard.tsx
 * @description React component for RoadmapCard
 */
import { useState } from 'react';
import { toast } from 'react-toastify';

import { useRouter } from 'next/navigation';

import { motion } from 'framer-motion';
import {
  Bookmark,
  ChevronRight,
  Clock,
  Heart,
  Map,
  MessageCircle,
  Route,
  Users,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { BaseRoadmap, RoadmapAuthor } from '@/hooks/useRoadmapApi';
import { useRoadmapSocial } from '@/hooks/useRoadmapSocial';
import { cn } from '@/lib/utils';

export type RoadmapType = BaseRoadmap & {
  description: string;
  enrollmentCount?: number;
  rating?: number;
  steps?: number;
  estimatedTime?: string;
  progress?: number;
  isEnrolled?: boolean;
  isFeatured?: boolean;
  tags?: string;
  difficulty?: string;
  estimatedHours?: number;
  popularity?: number;
  version?: string;
  user?: {
    username: string;
    full_name: string | null;
    avatar_url: string | null;
  } & RoadmapAuthor;
};

// Create a skeleton loader component for RoadmapCard
export const RoadmapCardSkeleton = () => (
  <div className="flex h-full flex-col rounded-xl border bg-card shadow-sm">
    {/* Header */}
    <div className="relative h-40 animate-pulse rounded-t-xl bg-gray-200"></div>

    {/* Content */}
    <div className="flex flex-1 flex-col p-5">
      {/* Title */}
      <div className="mb-2 h-6 w-3/4 animate-pulse rounded bg-gray-200"></div>

      {/* Description */}
      <div className="space-y-1.5">
        <div className="h-4 w-full animate-pulse rounded bg-gray-200"></div>
        <div className="h-4 w-5/6 animate-pulse rounded bg-gray-200"></div>
      </div>

      {/* Categories */}
      <div className="mt-3 flex gap-2">
        <div className="h-6 w-16 animate-pulse rounded-full bg-gray-200"></div>
        <div className="h-6 w-20 animate-pulse rounded-full bg-gray-200"></div>
      </div>

      {/* Metadata */}
      <div className="mt-4 flex items-center gap-3">
        <div className="h-8 w-8 animate-pulse rounded-full bg-gray-200"></div>
        <div className="flex-1">
          <div className="h-4 w-24 animate-pulse rounded bg-gray-200"></div>
          <div className="mt-1 h-3 w-32 animate-pulse rounded bg-gray-200"></div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-4 flex items-center justify-between border-t pt-4">
        <div className="flex gap-3">
          <div className="h-5 w-14 animate-pulse rounded bg-gray-200"></div>
          <div className="h-5 w-14 animate-pulse rounded bg-gray-200"></div>
          <div className="h-5 w-14 animate-pulse rounded bg-gray-200"></div>
        </div>
        <div className="h-8 w-16 animate-pulse rounded bg-gray-200"></div>
      </div>
    </div>
  </div>
);

export interface RoadmapCardProps {
  roadmap: RoadmapType;
  index: number;
  onLike?: (roadmapId: string) => Promise<void>;
  onBookmark?: (roadmapId: string) => Promise<void>;
  onCommentClick?: () => void;
  showViewButton?: boolean;
}

export const getDifficultyColor = (level?: string) => {
  switch (level?.toLowerCase()) {
    case 'easy':
      return 'bg-green-100 text-green-800';
    case 'medium':
      return 'bg-blue-100 text-blue-800';
    case 'hard':
      return 'bg-purple-100 text-purple-800';
    case 'beginner':
      return 'bg-green-100 text-green-800';
    case 'intermediate':
      return 'bg-blue-100 text-blue-800';
    case 'advanced':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const RoadmapCard = ({
  roadmap,
  index = 0,
  onLike,
  onBookmark,
  onCommentClick,
  showViewButton = true,
}: RoadmapCardProps) => {
  const router = useRouter();
  const [isLiked, setIsLiked] = useState(roadmap.isLiked);
  const [isBookmarked, setIsBookmarked] = useState(roadmap.isBookmarked);
  const [likeCount, setLikeCount] = useState(roadmap.likesCount || 0);
  const [bookmarkCount, setBookmarkCount] = useState(
    roadmap.bookmarksCount || 0,
  );
  const [isLoading, setIsLoading] = useState(false);
  const { handleLike, handleBookmark } = useRoadmapSocial();

  const handleLikeClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isLoading) return;
    setIsLoading(true);

    // Store current state to avoid closure issues
    const currentIsLiked = isLiked;
    const currentLikeCount = likeCount;

    // Optimistic update
    setIsLiked(!currentIsLiked);
    setLikeCount(currentIsLiked ? currentLikeCount - 1 : currentLikeCount + 1);

    try {
      await handleLike(roadmap.id);
      // The state is already updated optimistically
      onLike?.(roadmap.id);
    } catch (err) {
      console.error('Failed to update like status:', err);
      toast.error('Failed to update like status');
      // Revert on error
      setIsLiked(currentIsLiked);
      setLikeCount(currentLikeCount);
    } finally {
      setIsLoading(false);
    }
  };

  const handleComment = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onCommentClick) {
      onCommentClick();
    } else {
      router.push(`/career-roadmap/${roadmap.id}?comments=open`);
    }
  };

  const handleBookmarkClick = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isLoading) return;
    setIsLoading(true);

    // Store current state to avoid closure issues
    const currentIsBookmarked = isBookmarked;
    const currentBookmarkCount = bookmarkCount;

    // Optimistic update
    setIsBookmarked(!currentIsBookmarked);
    setBookmarkCount(
      currentIsBookmarked ? currentBookmarkCount - 1 : currentBookmarkCount + 1,
    );

    try {
      await handleBookmark(roadmap.id);
      // The state is already updated optimistically
      onBookmark?.(roadmap.id);
    } catch (err) {
      console.error('Failed to update bookmark status:', err);
      toast.error('Failed to update bookmark status');
      // Revert on error
      setIsBookmarked(currentIsBookmarked);
      setBookmarkCount(currentBookmarkCount);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewRoadmap = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/career-roadmap/${roadmap.id}`);
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: index * 0.1,
        duration: 0.5,
      },
    },
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={cardVariants}
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      className="group relative flex h-full flex-col overflow-hidden rounded-xl border bg-card shadow-sm transition-all hover:shadow-lg"
    >
      {/* Card header with difficulty badge and icon */}
      <div className="relative p-6 pb-4">
        <div className="to-primary/5 absolute inset-0 bg-gradient-to-br from-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

        {roadmap.difficulty ? (
          <Badge
            variant="outline"
            className={`absolute right-6 top-6 ${getDifficultyColor(roadmap.difficulty)}`}
          >
            {roadmap.difficulty}
          </Badge>
        ) : null}

        <div className="flex items-start justify-between">
          <div className="bg-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full text-primary">
            <Route size={24} />
          </div>

          <div className="flex gap-2">
            {roadmap.isEnrolled ? (
              <Badge className="bg-primary/80">Enrolled</Badge>
            ) : null}

            {roadmap.isFeatured ? (
              <Badge
                variant="secondary"
                className="border-yellow-200 bg-yellow-100 text-yellow-800"
              >
                Featured
              </Badge>
            ) : null}

            {roadmap.popularity && roadmap.popularity > 100 ? (
              <Badge
                variant="secondary"
                className="border-orange-200 bg-orange-100 text-orange-800"
              >
                Popular
              </Badge>
            ) : null}
          </div>
        </div>

        <h3 className="mb-3 text-lg font-semibold leading-tight tracking-tight">
          {roadmap.title}
        </h3>

        <p className="line-clamp-2 text-sm text-muted-foreground">
          {roadmap.description}
        </p>
      </div>

      {/* Metadata section */}
      <div className="flex-grow px-6 py-2">
        <div className="mb-3 flex flex-wrap items-center gap-3">
          {roadmap.steps && roadmap.steps > 0 ? (
            <Badge
              variant="outline"
              className="flex items-center gap-1 font-normal"
            >
              <Map className="h-3 w-3" />
              {roadmap.steps} steps
            </Badge>
          ) : null}

          {roadmap.estimatedTime ? (
            <Badge
              variant="outline"
              className="flex items-center gap-1 font-normal"
            >
              <Clock className="h-3 w-3" />
              {roadmap.estimatedTime}
            </Badge>
          ) : null}

          {roadmap.estimatedHours && roadmap.estimatedHours > 0 ? (
            <Badge
              variant="outline"
              className="flex items-center gap-1 font-normal"
            >
              <Clock className="h-3 w-3" />
              {roadmap.estimatedHours} hours
            </Badge>
          ) : null}

          {roadmap.enrollmentCount && roadmap.enrollmentCount > 0 ? (
            <Badge
              variant="outline"
              className="flex items-center gap-1 font-normal"
            >
              <Users className="h-3 w-3" />
              {roadmap.enrollmentCount.toLocaleString()} enrolled
            </Badge>
          ) : null}

          {roadmap.version ? (
            <Badge
              variant="outline"
              className="flex items-center gap-1 font-normal"
            >
              v{roadmap.version}
            </Badge>
          ) : null}
        </div>

        {roadmap.progress !== undefined && roadmap.progress > 0 ? (
          <div className="mb-4">
            <div className="mb-1 flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Progress</span>
              <span className="text-xs font-medium">{roadmap.progress}%</span>
            </div>
            <Progress value={roadmap.progress} className="h-1.5" />
          </div>
        ) : null}
      </div>

      {/* Card footer with author info and actions */}
      <div className="border-t px-6 py-4">
        <div className="flex flex-col space-y-3">
          {/* Tags Section */}
          {roadmap.tags && roadmap.tags.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {roadmap.tags.split(',').map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="bg-primary/10 hover:bg-primary/20 text-primary"
                >
                  {tag.trim()}
                </Badge>
              ))}
            </div>
          ) : null}

          {/* Author and Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                {roadmap?.user?.avatar_url ? (
                  <AvatarImage
                    src={roadmap?.user?.avatar_url}
                    alt={roadmap?.user?.full_name || roadmap?.user?.username}
                  />
                ) : (
                  <AvatarFallback className="bg-primary/10 text-primary">
                    {(roadmap?.user?.full_name || roadmap?.user?.username)
                      ?.charAt(0)
                      ?.toUpperCase()}
                  </AvatarFallback>
                )}
              </Avatar>
              <span className="text-xs text-muted-foreground">
                {roadmap?.user?.full_name || roadmap?.user?.username}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLikeClick}
                disabled={isLoading}
                className={cn(
                  'flex items-center gap-1.5 rounded-full px-3 transition-colors',
                  isLiked
                    ? 'bg-rose-50 text-rose-500 hover:bg-rose-100 dark:bg-rose-900/20 dark:hover:bg-rose-900/30'
                    : '',
                )}
              >
                <Heart
                  className={cn('h-4 w-4', isLiked ? 'fill-current' : '')}
                />
                <span className="text-xs font-medium">{likeCount}</span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleComment}
                className="flex items-center gap-1.5 rounded-full px-3 hover:bg-muted"
              >
                <MessageCircle className="h-4 w-4" />
                <span className="text-xs font-medium">
                  {roadmap.commentsCount > 0 ? roadmap.commentsCount : ''}
                </span>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleBookmarkClick}
                disabled={isLoading}
                className={cn(
                  'flex items-center gap-1.5 rounded-full px-3 transition-colors',
                  isBookmarked
                    ? 'bg-blue-50 text-blue-500 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30'
                    : '',
                )}
              >
                <Bookmark
                  className={cn('h-4 w-4', isBookmarked ? 'fill-current' : '')}
                />
                <span className="text-xs font-medium">{bookmarkCount}</span>
              </Button>
            </div>
          </div>
        </div>

        {showViewButton ? (
          <div className="mt-3 flex justify-end">
            <Button
              size="sm"
              className="hover:bg-primary/90 bg-primary"
              onClick={handleViewRoadmap}
            >
              View Roadmap <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
        ) : null}
      </div>
    </motion.div>
  );
};

export default RoadmapCard;
