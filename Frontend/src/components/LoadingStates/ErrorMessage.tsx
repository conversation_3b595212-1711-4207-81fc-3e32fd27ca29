/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */
'use client';

import { AlertCircle, RefreshCw } from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { <PERSON><PERSON> } from '@/components/ui/button';

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

/**
 * @file ErrorMessage.tsx
 * @description React component for ErrorMessage
 */

interface ErrorMessageProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  className?: string;
}

export default function ErrorMessage({
  title = 'Something went wrong',
  message = 'An error occurred while loading the data. Please try again.',
  onRetry,
  className = '',
}: ErrorMessageProps) {
  return (
    <Alert variant="destructive" className={`${className}`}>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription className="mt-2">
        <p>{message}</p>
        {onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="mt-2 bg-background"
          >
            <RefreshCw className="mr-2 h-3 w-3" />
            Try Again
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
}
