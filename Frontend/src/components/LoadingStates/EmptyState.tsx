/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */
'use client';

import { LucideIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

/**
 * @file EmptyState.tsx
 * @description React component for EmptyState
 */

interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
  actionHref?: string;
  className?: string;
}

export default function EmptyState({
  icon: Icon,
  title,
  description,
  actionText,
  onAction,
  actionHref,
  className = '',
}: EmptyStateProps) {
  return (
    <div
      className={`flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center ${className}`}
    >
      {Icon && (
        <div className="rounded-full bg-muted p-3">
          <Icon className="h-6 w-6 text-muted-foreground" />
        </div>
      )}
      <h3 className="mt-4 text-lg font-medium">{title}</h3>
      {description && (
        <p className="mt-2 max-w-sm text-sm text-muted-foreground">
          {description}
        </p>
      )}
      {(actionText && onAction) || actionHref ? (
        <Button
          variant="outline"
          className="mt-4"
          onClick={onAction}
          asChild={!!actionHref}
        >
          {actionHref ? (
            <a href={actionHref}>{actionText}</a>
          ) : (
            <>{actionText}</>
          )}
        </Button>
      ) : null}
    </div>
  );
}
