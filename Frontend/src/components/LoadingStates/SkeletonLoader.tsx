/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */
'use client';

import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

/**
 * @file SkeletonLoader.tsx
 * @description React component for SkeletonLoader
 */

type SkeletonType = 'card' | 'list' | 'detail' | 'table' | 'stats';

interface SkeletonLoaderProps {
  type: SkeletonType;
  count?: number;
  className?: string;
}

export default function SkeletonLoader({
  type,
  count = 1,
  className = '',
}: SkeletonLoaderProps) {
  const renderSkeleton = () => {
    switch (type) {
      case 'card':
        return (
          <Card className={`h-full ${className}`}>
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-5 w-16" />
              </div>
            </CardHeader>
            <CardContent className="flex-grow">
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              <div className="mt-4 flex flex-wrap gap-2">
                <Skeleton className="h-5 w-16 rounded-full" />
                <Skeleton className="h-5 w-16 rounded-full" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Skeleton className="h-9 w-full" />
            </CardFooter>
          </Card>
        );

      case 'list':
        return (
          <div className={`space-y-2 ${className}`}>
            <div className="flex items-center gap-2">
              <Skeleton className="h-6 w-6 rounded-full" />
              <Skeleton className="h-4 w-48" />
            </div>
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-5/6" />
            <Skeleton className="h-3 w-4/6" />
          </div>
        );

      case 'detail':
        return (
          <div className={`space-y-6 ${className}`}>
            <div className="space-y-2">
              <Skeleton className="h-8 w-3/4" />
              <div className="flex gap-2">
                <Skeleton className="h-5 w-20 rounded-full" />
                <Skeleton className="h-5 w-20 rounded-full" />
              </div>
            </div>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <Skeleton className="h-4 w-4/6" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-6 w-1/4" />
              <Skeleton className="h-32 w-full rounded-md" />
            </div>
          </div>
        );

      case 'table':
        return (
          <div className={`space-y-2 ${className}`}>
            <div className="flex items-center justify-between rounded-md bg-muted/40 p-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-24" />
            </div>
            {Array(3)
              .fill(0)
              .map((_, i) => (
                <div
                  key={i}
                  className="flex items-center justify-between rounded-md p-2"
                >
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-24" />
                </div>
              ))}
          </div>
        );

      case 'stats':
        return (
          <Card className={className}>
            <CardHeader className="pb-2">
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-2 w-full" />
              </div>
              <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
                {Array(4)
                  .fill(0)
                  .map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        );

      default:
        return <Skeleton className={`h-24 w-full ${className}`} />;
    }
  };

  return (
    <>
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <div key={index}>{renderSkeleton()}</div>
        ))}
    </>
  );
}
