/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */
'use client';

import React from 'react';

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * @file index.tsx
 * @description React component for ProgressWidget
 */

/**
 * Interface for progress data from backend
 */
interface IProgressData {
  /**
   * Total number of chapters in the course
   */
  chapters: number;

  /**
   * Total number of practice items in the course
   */
  items: number;

  /**
   * Number of completed chapters
   */
  completed_chapters: number;

  /**
   * Number of completed practice items
   */
  completed_items: number;
}

/**
 * Props for the ProgressWidget component
 */
interface IProgressWidgetProps {
  /**
   * Initial progress data to display
   */
  initialData: IProgressData;
}

/**
 * ProgressWidget - Displays a user's learning progress
 *
 * This component shows the user's progress through chapters and practice items,
 * including a visual progress bar and percentage completion.
 *
 * @example
 * // Basic usage
 * <ProgressWidget
 *   initialData={{
 *     chapters: 10,
 *     items: 50,
 *     completed_chapters: 3,
 *     completed_items: 15
 *   }}
 * />
 */
function ProgressWidget({
  initialData,
}: IProgressWidgetProps): React.ReactElement {
  /**
   * Calculate the overall progress percentage
   * @returns The percentage of completed items (0-100)
   */
  const calculateProgress = (): number => {
    const totalCompleted =
      initialData.completed_chapters + initialData.completed_items;
    const totalItems = initialData.chapters + initialData.items;
    return Math.round((totalCompleted / totalItems) * 100);
  };

  return (
    <div className="rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
      <h3 className="mb-4 text-xl font-semibold text-gray-800 dark:text-gray-200">
        Learning Progress
      </h3>

      <div className="mb-6 space-y-4">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Chapters</span>
          <span className="text-gray-800 dark:text-gray-200">
            {initialData.completed_chapters}/{initialData.chapters}
          </span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">
            Practice Items
          </span>
          <span className="text-gray-800 dark:text-gray-200">
            {initialData.completed_items}/{initialData.items}
          </span>
        </div>
      </div>

      <div className="relative pt-1">
        <div className="mb-2 flex items-center justify-between">
          <div className="text-right">
            <span className="inline-block text-xs font-semibold text-blue-600 dark:text-blue-400">
              {calculateProgress()}%
            </span>
          </div>
        </div>
        <div className="mb-4 flex h-2 overflow-hidden rounded bg-gray-200 text-xs dark:bg-gray-700">
          <div
            style={{ width: `${calculateProgress()}%` }}
            className="flex flex-col justify-center whitespace-nowrap bg-blue-500 text-center text-white shadow-none transition-all duration-300 dark:bg-blue-400"
          />
        </div>
      </div>
    </div>
  );
}

export default ProgressWidget;
