#app {
  height: 100%;
}
html,
body {
  position: relative;
  height: 100%;
}

body {
  background: #eee;
  font-family:
    Helvetica Neue,
    Helvetica,
    Arial,
    sans-serif;
  font-size: 14px;
  color: #000;
  margin: 0;
  padding: 0;
}

body {
  background: #fff;
  font-family:
    Helvetica Neue,
    Helvetica,
    Arial,
    sans-serif;
  font-size: 14px;
  color: #000;
  margin: 0;
  padding: 0;
}

html,
body {
  position: relative;
  height: 100%;
}

#app {
  display: flex;
  justify-content: center;
  align-items: center;
}

.banner .swiper {
  width: 240px;
  height: 320px;
}

.banner .swiper-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
  font-size: 22px;
  font-weight: bold;
  color: #fff;
}

.banner .swiper-slide:nth-child(1n) {
  background-image: url('../../../assets/banner-book/book1.png');
}

.banner .swiper-slide:nth-child(2n) {
  background-image: url('../../../assets/banner-book/book2.png');
}

.banner .swiper-slide:nth-child(3n) {
  background-image: url('../../../assets/banner-book/book3.png');
}

.banner .swiper-slide:nth-child(4n) {
  background-image: url('../../../assets/banner-book/book4.png');
}

.banner .swiper-slide:nth-child(5n) {
  background-image: url('../../../assets/banner-book/book5.png');
}

.banner .swiper-slide:nth-child(6n) {
  background-color: rgb(180, 10, 47);
}

.banner .swiper-slide:nth-child(7n) {
  background-color: rgb(35, 99, 19);
}

.banner .swiper-slide:nth-child(8n) {
  background-color: rgb(0, 68, 255);
}

.banner .swiper-slide:nth-child(9n) {
  background-color: rgb(218, 12, 218);
}

.banner .swiper-slide:nth-child(10n) {
  background-color: rgb(54, 94, 77);
}
