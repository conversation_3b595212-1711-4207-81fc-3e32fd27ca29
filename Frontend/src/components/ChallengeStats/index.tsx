/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */
'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Clock, Code, Flame } from 'lucide-react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { UserChallengeStats } from '@/hooks/useChallengeProgress';

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

/**
 * @file index.tsx
 * @description React component for ChallengeStats
 */

interface ChallengeStatsProps {
  stats: UserChallengeStats | null;
  isLoading?: boolean;
}

export default function ChallengeStats({
  stats,
  isLoading = false,
}: ChallengeStatsProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <Skeleton className="h-7 w-40" />
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-2 w-full" />
          </div>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-10 w-10 rounded-full" />
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-3 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Challenge Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            No statistics available yet. Start solving challenges to see your
            progress!
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle>Your Challenge Progress</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>
              {stats.total_completed} of {stats.total_challenges} completed
            </span>
            <span className="font-medium">
              {Math.round(stats.completion_percentage)}%
            </span>
          </div>
          <Progress value={stats.completion_percentage} className="h-2" />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
          <div className="flex flex-col items-start gap-1">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
              <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <h4 className="text-base font-medium">{stats.total_completed}</h4>
            <p className="text-sm text-muted-foreground">
              Challenges Completed
            </p>
          </div>

          <div className="flex flex-col items-start gap-1">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/20">
              <Clock className="h-5 w-5 text-amber-600 dark:text-amber-400" />
            </div>
            <h4 className="text-base font-medium">{stats.total_in_progress}</h4>
            <p className="text-sm text-muted-foreground">In Progress</p>
          </div>

          <div className="flex flex-col items-start gap-1">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
              <Code className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <h4 className="text-base font-medium">
              {stats.favorite_language || 'N/A'}
            </h4>
            <p className="text-sm text-muted-foreground">Favorite Language</p>
          </div>

          <div className="flex flex-col items-start gap-1">
            <div className="bg-red-100 dark:bg-red-900/20 flex h-10 w-10 items-center justify-center rounded-full">
              <Flame className="text-red-600 dark:text-red-400 h-5 w-5" />
            </div>
            <h4 className="text-base font-medium">{stats.streak_days} days</h4>
            <p className="text-sm text-muted-foreground">Current Streak</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
