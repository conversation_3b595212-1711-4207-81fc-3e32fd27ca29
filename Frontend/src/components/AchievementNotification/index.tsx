/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */
'use client';

import { useEffect, useState } from 'react';

import confetti from 'canvas-confetti';
import { AnimatePresence, motion } from 'framer-motion';
import { Share2, Trophy, X } from 'lucide-react';

import {
  achievementTierColors,
  IAchievement,
} from '@/app/coding-challenges/types/achievements';
import AchievementBadge from '@/components/AchievementBadge';
import ShareButton from '@/components/ShareButton';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotification
 */

interface AchievementNotificationProps {
  achievement: IAchievement;
  onClose: () => void;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

export default function AchievementNotification({
  achievement,
  onClose,
  autoClose = true,
  autoCloseDelay = 8000,
}: AchievementNotificationProps) {
  const [isVisible, setIsVisible] = useState(true);

  // Auto close notification after delay
  useEffect(() => {
    if (autoClose) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(onClose, 500); // Wait for exit animation to complete
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [autoClose, autoCloseDelay, onClose]);

  // Trigger confetti effect when notification appears
  useEffect(() => {
    if (isVisible) {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
        colors: ['#FFD700', '#FFC0CB', '#00FFFF', '#FF00FF', '#FFFF00'],
      });
    }
  }, [isVisible]);

  // Handle close button click
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 500); // Wait for exit animation to complete
  };

  // Get tier colors for styling
  const tierColors = achievementTierColors[achievement.tier];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.9 }}
          transition={{ duration: 0.5, type: 'spring', bounce: 0.3 }}
          className="fixed bottom-4 left-1/2 z-50 w-full max-w-md -translate-x-1/2 px-4"
        >
          <div
            className={cn(
              'flex overflow-hidden rounded-lg border shadow-lg',
              tierColors.border,
              tierColors.bg,
            )}
          >
            <div className="flex flex-1 items-center p-4">
              <div className="mr-4">
                <AchievementBadge
                  achievement={achievement}
                  size="md"
                  showTooltip={false}
                  showDetails={false}
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Trophy className={cn('h-4 w-4', tierColors.icon)} />
                  <h3 className={cn('font-bold', tierColors.text)}>
                    Achievement Unlocked!
                  </h3>
                </div>
                <h4 className="text-lg font-semibold">{achievement.title}</h4>
                <p className="text-sm text-muted-foreground">
                  {achievement.description}
                </p>
                <div className="mt-2 flex items-center gap-2">
                  <Trophy className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium">
                    {achievement.points} points
                  </span>
                </div>
              </div>
            </div>

            <div className="flex flex-col justify-between border-l bg-background p-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleClose}
              >
                <X className="h-4 w-4" />
              </Button>

              <ShareButton
                url={`/profile/achievements/${achievement.id}`}
                title={`I earned the "${achievement.title}" achievement!`}
                description={achievement.description}
                variant="ghost"
                size="icon"
                showText={false}
                className="h-8 w-8"
              />
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
