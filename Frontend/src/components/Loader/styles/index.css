/***************************************************
 * Generated by SVG Artista on 2/21/2025, 9:29:03 AM
 * MIT license (https://opensource.org/licenses/MIT)
 * W. https://svgartista.net
 **************************************************/

@-webkit-keyframes animate-svg-fill-1 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(0, 0, 0);
  }
}

@keyframes animate-svg-fill-1 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(0, 0, 0);
  }
}

.svg-elem-1 {
  -webkit-animation: animate-svg-fill-1 3s ease-in-out 0.2s both;
  animation: animate-svg-fill-1 3s ease-in-out 0.2s both;
}

@-webkit-keyframes animate-svg-fill-2 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(0, 0, 0);
  }
}

@keyframes animate-svg-fill-2 {
  0% {
    fill: transparent;
  }

  100% {
    fill: rgb(0, 0, 0);
  }
}

.svg-elem-2 {
  -webkit-animation: animate-svg-fill-2 3s ease-in-out 0.4s both;
  animation: animate-svg-fill-2 3s ease-in-out 0.4s both;
}
