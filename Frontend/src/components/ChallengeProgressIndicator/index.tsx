/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */
'use client';

import { <PERSON><PERSON><PERSON><PERSON>, CircleDashed, Clock } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  ChallengeProgress,
  ChallengeStatus,
} from '@/hooks/useChallengeProgress';

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

/**
 * @file index.tsx
 * @description React component for ChallengeProgressIndicator
 */

interface ChallengeProgressIndicatorProps {
  status: ChallengeStatus;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
  progress?: ChallengeProgress;
}

export default function ChallengeProgressIndicator({
  status,
  size = 'md',
  showLabel = false,
  className = '',
  progress,
}: ChallengeProgressIndicatorProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  };

  const statusConfig = {
    not_started: {
      icon: (
        <CircleDashed
          className={`${sizeClasses[size]} text-muted-foreground`}
        />
      ),
      label: 'Not Started',
      color: 'text-muted-foreground',
      tooltip: "You haven't started this challenge yet",
    },
    in_progress: {
      icon: <Clock className={`${sizeClasses[size]} text-amber-500`} />,
      label: 'In Progress',
      color: 'text-amber-500',
      tooltip: progress
        ? `Last attempted ${new Date(progress.last_attempted_at || '').toLocaleDateString()} • ${progress.attempts_count} attempt${progress.attempts_count !== 1 ? 's' : ''}`
        : "You've started working on this challenge",
    },
    completed: {
      icon: <CheckCircle className={`${sizeClasses[size]} text-green-500`} />,
      label: 'Completed',
      color: 'text-green-500',
      tooltip: progress
        ? `Completed on ${new Date(progress.completed_at || '').toLocaleDateString()} • Best score: ${progress.best_score || 0}`
        : "You've successfully completed this challenge",
    },
  };

  const currentStatus = statusConfig[status];

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center gap-1.5 ${className}`}>
            {currentStatus.icon}
            {showLabel && (
              <span className={`text-sm font-medium ${currentStatus.color}`}>
                {currentStatus.label}
              </span>
            )}
            {progress?.best_score && showLabel && (
              <Badge variant="outline" className="ml-1 text-xs">
                {progress.best_score} pts
              </Badge>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{currentStatus.tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
