'use client';

import { AlertCircle, LucideIcon, RefreshCw } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

interface IErrorMessageProps {
  title: string;
  message: string;
  onRetry?: () => void;
  className?: string;
}

export function ErrorMessage({
  title,
  message,
  onRetry,
  className = '',
}: IErrorMessageProps) {
  return (
    <div
      className={`border-red-200 bg-red-50 dark:border-red-900/50 dark:bg-red-950/30 flex flex-col items-center justify-center rounded-lg border p-6 text-center ${className}`}
      role="alert"
    >
      <AlertCircle className="text-red-500 mb-2 h-8 w-8" aria-hidden="true" />
      <h3 className="text-red-800 dark:text-red-300 mb-2 text-lg font-medium">
        {title}
      </h3>
      <p className="text-red-600 dark:text-red-400 mb-4 text-sm">{message}</p>
      {onRetry && (
        <Button
          variant="outline"
          size="sm"
          onClick={onRetry}
          className="border-red-300 text-red-600 hover:bg-red-50 hover:text-red-700 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-950/50 dark:hover:text-red-300"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      )}
    </div>
  );
}

interface IEmptyStateProps {
  icon: LucideIcon;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
}: IEmptyStateProps) {
  return (
    <div className="flex min-h-[200px] flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
      <div className="bg-primary/10 mb-4 rounded-full p-3">
        <Icon className="h-6 w-6 text-primary" aria-hidden="true" />
      </div>
      <h3 className="mb-2 text-lg font-medium">{title}</h3>
      <p className="mb-4 max-w-md text-sm text-muted-foreground">
        {description}
      </p>
      {action && (
        <Button variant="outline" size="sm" onClick={action.onClick}>
          {action.label}
        </Button>
      )}
    </div>
  );
}

interface ISkeletonLoaderProps {
  type: 'card' | 'table' | 'list';
  count?: number;
}

export function SkeletonLoader({ type, count = 3 }: ISkeletonLoaderProps) {
  if (type === 'table') {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-[200px]" />
          <Skeleton className="h-10 w-[150px]" />
        </div>
        <div className="rounded-md border">
          <div className="border-b bg-muted/40 p-4">
            <div className="flex">
              <Skeleton className="h-5 w-8" />
              <Skeleton className="ml-4 h-5 w-32" />
              <Skeleton className="ml-auto h-5 w-24" />
              <Skeleton className="ml-4 h-5 w-24" />
            </div>
          </div>
          <div className="divide-y">
            {Array(count)
              .fill(0)
              .map((_, i) => (
                <div key={i} className="p-4">
                  <div className="flex items-center">
                    <Skeleton className="h-5 w-8" />
                    <div className="ml-4 flex items-center space-x-2">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <Skeleton className="h-5 w-32" />
                    </div>
                    <Skeleton className="ml-auto h-5 w-16" />
                    <Skeleton className="ml-4 h-5 w-24" />
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    );
  }

  if (type === 'list') {
    return (
      <div className="space-y-4">
        {Array(count)
          .fill(0)
          .map((_, i) => (
            <div key={i} className="rounded-lg border p-4">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-16" />
                </div>
              </div>
              <div className="mt-3 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </div>
          ))}
      </div>
    );
  }

  // Default card skeleton
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {Array(count)
        .fill(0)
        .map((_, i) => (
          <div
            key={i}
            className="flex h-full flex-col rounded-lg border bg-card shadow-sm"
          >
            <div className="p-6">
              <div className="flex items-start justify-between">
                <Skeleton className="h-7 w-3/4" />
                <Skeleton className="h-6 w-16 rounded-full" />
              </div>
              <div className="mt-4 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>
              <div className="mt-4 flex flex-wrap gap-2">
                <Skeleton className="h-6 w-16 rounded-full" />
                <Skeleton className="h-6 w-16 rounded-full" />
                <Skeleton className="h-6 w-16 rounded-full" />
              </div>
            </div>
            <div className="mt-auto border-t p-4">
              <Skeleton className="h-9 w-full rounded-md" />
            </div>
          </div>
        ))}
    </div>
  );
}
