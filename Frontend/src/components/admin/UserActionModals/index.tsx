/**
 * @file index.tsx
 * @description Modal components for user suspension and deletion actions
 */
'use client';

import { useState } from 'react';
import {
  RiAlertLine,
  RiDownloadLine,
  RiErrorWarningLine,
} from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

interface IUserActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  userName: string;
  onConfirm: (data: any) => void;
}

interface ISuspensionData {
  reason: string;
  duration: string;
  notifyUser: boolean;
}

interface IDeletionData {
  reason: string;
  exportData: boolean;
  confirmDelete: boolean;
}

// User Suspension Modal
function SuspendUserModal({
  isOpen,
  onClose,
  userId,
  userName,
  onConfirm,
}: IUserActionModalProps) {
  const [formData, setFormData] = useState<ISuspensionData>({
    reason: '',
    duration: '7days',
    notifyUser: true,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = () => {
    setIsSubmitting(true);

    // TODO: Replace with actual API call to suspend user
    setTimeout(() => {
      onConfirm(formData);
      setIsSubmitting(false);
      onClose();
    }, 1000);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RiAlertLine className="text-warning" />
            Suspend User Account
          </DialogTitle>
          <DialogDescription>
            Temporarily suspend access for user <strong>{userName}</strong>.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="duration">Suspension Duration</Label>
            <Select
              value={formData.duration}
              onValueChange={(value) =>
                setFormData({ ...formData, duration: value })
              }
            >
              <SelectTrigger id="duration">
                <SelectValue placeholder="Select duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1day">1 Day</SelectItem>
                <SelectItem value="3days">3 Days</SelectItem>
                <SelectItem value="7days">7 Days</SelectItem>
                <SelectItem value="14days">14 Days</SelectItem>
                <SelectItem value="30days">30 Days</SelectItem>
                <SelectItem value="indefinite">Indefinite</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason for Suspension</Label>
            <Textarea
              id="reason"
              value={formData.reason}
              onChange={(e) =>
                setFormData({ ...formData, reason: e.target.value })
              }
              placeholder="Explain why this user is being suspended..."
              rows={3}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="notifyUser">Notify User</Label>
            <Switch
              id="notifyUser"
              checked={formData.notifyUser}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, notifyUser: checked })
              }
            />
          </div>
          <p className="text-xs text-muted-foreground">
            If enabled, the user will receive an email notification about their
            account suspension.
          </p>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="outline"
            className="border-amber-300 bg-amber-100 text-amber-700 hover:bg-amber-200"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Suspending...' : 'Suspend Account'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// User Deletion Modal
function DeleteUserModal({
  isOpen,
  onClose,
  userId,
  userName,
  onConfirm,
}: IUserActionModalProps) {
  const [formData, setFormData] = useState<IDeletionData>({
    reason: '',
    exportData: true,
    confirmDelete: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleExportData = () => {
    // TODO: Replace with actual API call to export user data
    setTimeout(() => {
      // Simulate data export
      const userData = {
        id: userId,
        name: userName,
        exportDate: new Date().toISOString(),
        data: {
          // Mock user data for export
          profile: {
            /* user profile data */
          },
          activities: [
            /* user activities */
          ],
          content: [
            /* user content */
          ],
        },
      };

      // Create and download a JSON file
      const dataStr = JSON.stringify(userData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `user-${userId}-export.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }, 1000);
  };

  const handleSubmit = () => {
    if (!formData.confirmDelete) {
      return;
    }

    setIsSubmitting(true);

    // TODO: Replace with actual API call to delete user
    setTimeout(() => {
      onConfirm(formData);
      setIsSubmitting(false);
      onClose();
    }, 1000);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RiErrorWarningLine className="text-destructive" />
            Delete User Account
          </DialogTitle>
          <DialogDescription>
            Permanently delete user <strong>{userName}</strong>. This action
            cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="rounded-md bg-destructive/10 p-4 text-sm text-destructive">
            <p className="font-medium">Warning: This action is permanent</p>
            <p className="mt-1">
              Deleting this user will remove all of their data from the system.
              This action cannot be reversed.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason for Deletion</Label>
            <Textarea
              id="reason"
              value={formData.reason}
              onChange={(e) =>
                setFormData({ ...formData, reason: e.target.value })
              }
              placeholder="Explain why this user is being deleted..."
              rows={3}
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="exportData">Export User Data</Label>
              <Switch
                id="exportData"
                checked={formData.exportData}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, exportData: checked })
                }
              />
            </div>
            <p className="text-xs text-muted-foreground">
              Export the user&apos;s data before deletion for record-keeping
              purposes.
            </p>

            {formData.exportData && (
              <Button
                type="button"
                variant="outline"
                className="flex w-full items-center justify-center gap-1"
                onClick={handleExportData}
              >
                <RiDownloadLine /> Export User Data
              </Button>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label
                htmlFor="confirmDelete"
                className="font-medium text-destructive"
              >
                Confirm Deletion
              </Label>
              <Switch
                id="confirmDelete"
                checked={formData.confirmDelete}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, confirmDelete: checked })
                }
              />
            </div>
            <p className="text-xs text-muted-foreground">
              I understand that this action is permanent and cannot be undone.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleSubmit}
            disabled={isSubmitting || !formData.confirmDelete}
          >
            {isSubmitting ? 'Deleting...' : 'Delete Account'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export { SuspendUserModal, DeleteUserModal };
