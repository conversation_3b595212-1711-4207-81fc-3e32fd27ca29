/**
 * @file DashboardOverview.tsx
 * @description Dashboard overview component with key metrics and stats
 */
'use client';

import { useState, useEffect, useRef } from 'react';
import {
  RiUser3Line,
  RiFileList3Line,
  RiTimeLine,
  RiBarChartBoxLine,
  RiShieldUserLine,
  RiAwardLine,
  RiAlertLine,
  RiRefreshLine,
} from 'react-icons/ri';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAxiosGet } from '@/hooks/useAxios';
import {
  IDashboardMetrics,
  IRecentActivity,
  IAuditLog,
  transformAuditLogsToActivities,
} from '@/services/adminDashboardService';

interface IMetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  change?: string;
  isPositive?: boolean;
  isLoading?: boolean;
}

function MetricCard({
  title,
  value,
  icon,
  change,
  isPositive,
  isLoading = false,
}: IMetricCardProps) {
  return (
    <div className="rounded-lg border border-border bg-background p-6 shadow-sm">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
          {isLoading ? (
            <Skeleton className="mt-2 h-9 w-24" />
          ) : (
            <p className="mt-2 text-3xl font-semibold text-foreground">
              {value}
            </p>
          )}
          {change && !isLoading && (
            <p
              className={`mt-2 text-xs ${isPositive ? 'text-success' : 'text-destructive'}`}
            >
              {isPositive ? '↑' : '↓'} {change} from last month
            </p>
          )}
        </div>
        <div className="bg-primary/10 rounded-full p-3 text-primary">
          {icon}
        </div>
      </div>
    </div>
  );
}

export default function DashboardOverview() {
  const [metrics, setMetrics] = useState<IDashboardMetrics | null>(null);
  const [prevMetrics, setPrevMetrics] = useState<IDashboardMetrics | null>(
    null,
  );
  const [activities, setActivities] = useState<IRecentActivity[]>([]);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState<boolean>(false);
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Use the existing useAxiosGet hooks
  const [fetchMetrics, metricsState] = useAxiosGet<IDashboardMetrics>(
    '/admin/dashboard/metrics',
  );
  const [fetchActivities, activitiesState] =
    useAxiosGet<IAuditLog[]>('/admin/audit/logs');

  // Determine overall loading state
  const isLoading = metricsState.isLoading || activitiesState.isLoading;

  // Function to fetch dashboard data
  const fetchDashboardData = async () => {
    setError(null);

    try {
      // Fetch metrics
      const metricsResponse = await fetchMetrics();
      if (metricsResponse.success && metricsResponse.data) {
        setMetrics(metricsResponse.data);
      } else if (metricsResponse.error) {
        console.error('Metrics fetch error:', metricsResponse.message);
        throw new Error(metricsResponse.message || 'Failed to fetch metrics');
      }

      // Fetch activities with limit parameter
      const activitiesResponse = await fetchActivities({
        params: { limit: 5 },
      });

      if (activitiesResponse.success && activitiesResponse.data) {
        // Transform the audit logs to activities format
        const transformedActivities = transformAuditLogsToActivities(
          activitiesResponse.data,
        );
        setActivities(transformedActivities);
      } else if (activitiesResponse.error) {
        console.error('Activities fetch error:', activitiesResponse.message);
        // Don't throw here to allow partial data display
      }

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');
    }
  };

  // Initial data fetch - using empty dependency array to prevent infinite API calls
  useEffect(() => {
    let isMounted = true;
    
    // Initial data fetch function
    async function fetchInitialData() {
      try {
        if (!isMounted) return;
        
        // Fetch current metrics and activities
        await fetchDashboardData();
        
        if (!isMounted) return;
        
        // Fetch previous period metrics for comparison
        const prevResponse = await fetchMetrics({
          params: { period: 'previous' },
        });
        
        if (!isMounted) return;
        
        if (prevResponse.success && prevResponse.data) {
          setPrevMetrics(prevResponse.data);
        } else if (prevResponse.error) {
          console.warn('Failed to load previous metrics:', prevResponse.message);
          // Don't set error state for this - it's not critical
        }
      } catch (error) {
        console.error('Error in initial data fetch:', error);
        if (isMounted) {
          setError('Failed to load dashboard data. Please try again.');
        }
      }
    }
    
    // Only fetch once on mount
    fetchInitialData();
    
    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
    
    return () => {
      // Clean up timer on component unmount
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, []); // Empty dependency array - only run on mount
  
  // TODO: When useAxios hook is updated with stableFunction, use it here to prevent infinite API calls

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh) {
      // Set up a timer to refresh data every 30 seconds
      refreshTimerRef.current = setInterval(() => {
        // Refresh function
        const refreshAllData = async () => {
          try {
            // Fetch current metrics and activities
            await fetchDashboardData();
            
            // Fetch previous period metrics for comparison
            const prevResponse = await fetchMetrics({
              params: { period: 'previous' },
            });
            if (prevResponse.success && prevResponse.data) {
              setPrevMetrics(prevResponse.data);
            }
          } catch (error) {
            console.error('Error refreshing dashboard data:', error);
          }
        };
        
        refreshAllData();
      }, 30000); // 30 seconds

      return () => {
        if (refreshTimerRef.current) {
          clearInterval(refreshTimerRef.current);
          refreshTimerRef.current = null;
        }
      };
    } else if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
      refreshTimerRef.current = null;
    }
  }, [autoRefresh]); // Only depend on autoRefresh state

  // Toggle auto-refresh
  const toggleAutoRefresh = () => {
    setAutoRefresh((prev) => !prev);
  };

  // Format date for display
  const formatDateTime = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // Helper function to format relative time
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return date.toLocaleDateString();
  };

  // Get icon for activity type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registered':
        return <RiUser3Line />;
      case 'content_published':
        return <RiFileList3Line />;
      case 'role_updated':
        return <RiShieldUserLine />;
      case 'challenge_completed':
        return <RiAwardLine />;
      default:
        return <RiTimeLine />;
    }
  };

  // Get background color for activity icon
  const getActivityIconBg = (type: string) => {
    switch (type) {
      case 'user_registered':
        return 'bg-primary/10';
      case 'content_published':
        return 'bg-success/10';
      case 'role_updated':
        return 'bg-warning/10';
      case 'challenge_completed':
        return 'bg-accent/10';
      default:
        return 'bg-muted';
    }
  };

  // Calculate percentage change between current and previous values
  const calculateChange = (
    current: number,
    previous: number,
  ): { value: string; isPositive: boolean } => {
    if (!previous) return { value: '0%', isPositive: false };

    const change = ((current - previous) / previous) * 100;
    return {
      value: `${Math.abs(change).toFixed(1)}%`,
      isPositive: change >= 0,
    };
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive" className="mb-4">
          <RiAlertLine className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div className="ml-auto flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Last updated: {formatDateTime(lastUpdated)}
          </div>
          <Button variant="outline" size="sm" onClick={toggleAutoRefresh}>
            <span
              className={`mr-2 h-2 w-2 rounded-full ${autoRefresh ? 'bg-success animate-pulse' : 'bg-muted-foreground'}`}
            />
            {autoRefresh ? 'Auto-refresh on' : 'Auto-refresh off'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchDashboardData}
            disabled={isLoading}
          >
            <RiRefreshLine className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <MetricCard
          title="Total Users"
          value={metrics?.userStats.totalUsers || 0}
          icon={<RiUser3Line className="text-2xl" />}
          change={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.userStats.totalUsers,
                  prevMetrics.userStats.totalUsers,
                ).value
              : undefined
          }
          isPositive={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.userStats.totalUsers,
                  prevMetrics.userStats.totalUsers,
                ).isPositive
              : true
          }
          isLoading={isLoading}
        />
        <MetricCard
          title="New Users (This Month)"
          value={metrics?.userStats.newUsers || 0}
          icon={<RiUser3Line className="text-2xl" />}
          change={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.userStats.newUsers,
                  prevMetrics.userStats.newUsers,
                ).value
              : undefined
          }
          isPositive={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.userStats.newUsers,
                  prevMetrics.userStats.newUsers,
                ).isPositive
              : true
          }
          isLoading={isLoading}
        />
        <MetricCard
          title="Total Content"
          value={
            metrics
              ? metrics.platformMetrics.totalArticles +
                metrics.platformMetrics.totalChallenges +
                metrics.platformMetrics.totalQuizzes
              : 0
          }
          icon={<RiFileList3Line className="text-2xl" />}
          change={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.platformMetrics.totalArticles +
                    metrics.platformMetrics.totalChallenges +
                    metrics.platformMetrics.totalQuizzes,
                  prevMetrics.platformMetrics.totalArticles +
                    prevMetrics.platformMetrics.totalChallenges +
                    prevMetrics.platformMetrics.totalQuizzes,
                ).value
              : undefined
          }
          isPositive={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.platformMetrics.totalArticles +
                    metrics.platformMetrics.totalChallenges +
                    metrics.platformMetrics.totalQuizzes,
                  prevMetrics.platformMetrics.totalArticles +
                    prevMetrics.platformMetrics.totalChallenges +
                    prevMetrics.platformMetrics.totalQuizzes,
                ).isPositive
              : true
          }
          isLoading={isLoading}
        />
        <MetricCard
          title="Active Users"
          value={metrics?.userStats.activeUsers || 0}
          icon={<RiTimeLine className="text-2xl" />}
          change={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.userStats.activeUsers,
                  prevMetrics.userStats.activeUsers,
                ).value
              : undefined
          }
          isPositive={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.userStats.activeUsers,
                  prevMetrics.userStats.activeUsers,
                ).isPositive
              : true
          }
          isLoading={isLoading}
        />
        <MetricCard
          title="Engagement Rate"
          value={
            metrics && metrics.platformMetrics?.engagementRate
              ? `${metrics.platformMetrics.engagementRate.toFixed(1)}%`
              : '0%'
          }
          icon={<RiBarChartBoxLine className="text-2xl" />}
          change={
            metrics &&
            prevMetrics &&
            metrics.platformMetrics?.engagementRate &&
            prevMetrics.platformMetrics?.engagementRate
              ? calculateChange(
                  metrics.platformMetrics.engagementRate,
                  prevMetrics.platformMetrics.engagementRate,
                ).value
              : undefined
          }
          isPositive={
            metrics &&
            prevMetrics &&
            metrics.platformMetrics?.engagementRate &&
            prevMetrics.platformMetrics?.engagementRate
              ? calculateChange(
                  metrics.platformMetrics.engagementRate,
                  prevMetrics.platformMetrics.engagementRate,
                ).isPositive
              : false
          }
          isLoading={isLoading}
        />
        <MetricCard
          title="Avg. Session (min/user)"
          value={
            metrics
              ? Math.round(metrics.activityMetrics.averageSessionDuration / 60)
              : 0
          }
          icon={<RiAwardLine className="text-2xl" />}
          change={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.activityMetrics.averageSessionDuration,
                  prevMetrics.activityMetrics.averageSessionDuration,
                ).value
              : undefined
          }
          isPositive={
            metrics && prevMetrics
              ? calculateChange(
                  metrics.activityMetrics.averageSessionDuration,
                  prevMetrics.activityMetrics.averageSessionDuration,
                ).isPositive
              : true
          }
          isLoading={isLoading}
        />
      </div>

      {/* Recent Activity */}
      <div className="mt-6 rounded-lg border border-border bg-background p-6 shadow-sm">
        <h3 className="mb-4 text-xl font-semibold">Recent Activity</h3>
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div
                key={index}
                className="flex items-start space-x-4 border-b border-border pb-4"
              >
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-3 w-2/3" />
                  <Skeleton className="h-3 w-1/4" />
                </div>
              </div>
            ))}
          </div>
        ) : activities.length > 0 ? (
          <div className="space-y-4">
            {activities.map((activity) => (
              <div
                key={activity.id}
                className="flex items-start space-x-4 border-b border-border pb-4"
              >
                <div
                  className={`rounded-full p-2 ${getActivityIconBg(activity.type)}`}
                >
                  {getActivityIcon(activity.type)}
                </div>
                <div>
                  <p className="font-medium text-foreground">
                    {activity.title}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {activity.description}
                  </p>
                  <p className="mt-1 text-xs text-muted-foreground">
                    {formatRelativeTime(activity.timestamp)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground">
            No recent activity to display.
          </p>
        )}
        <Button
          variant="link"
          className="mt-4 h-auto p-0 text-sm font-medium text-primary"
        >
          View all activity
        </Button>
      </div>
    </div>
  );
}
