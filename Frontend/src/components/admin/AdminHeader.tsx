/**
 * @file AdminHeader.tsx
 * @description Header component for the admin dashboard with profile dropdown and notifications
 */
'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  RiNotification3Line,
  RiUser3Line,
  RiSettings4Line,
  RiLogoutBoxLine,
  RiArrowDownSLine,
} from 'react-icons/ri';

import { IUser } from '@/types';
import { createClient } from '@/utils/supabase/client';

interface IAdminHeaderProps {
  user: IUser | null;
}

export default function AdminHeader({ user }: IAdminHeaderProps) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [notifications, setNotifications] = useState<
    Array<{
      id: string;
      title: string;
      message: string;
      time: string;
      read: boolean;
    }>
  >([]);

  const router = useRouter();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const notificationRef = useRef<HTMLDivElement>(null);

  // Sample notifications - in a real app, these would come from an API
  useEffect(() => {
    // Simulating fetching notifications
    setNotifications([
      {
        id: '1',
        title: 'New User Registration',
        message: 'A new user has registered and requires approval',
        time: '5 minutes ago',
        read: false,
      },
      {
        id: '2',
        title: 'System Update',
        message: 'The system will undergo maintenance in 24 hours',
        time: '1 hour ago',
        read: false,
      },
      {
        id: '3',
        title: 'Content Reported',
        message: 'A user has reported inappropriate content',
        time: '3 hours ago',
        read: true,
      },
    ]);
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target as Node)
      ) {
        setNotificationsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();
      router.push('/auth/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
    if (notificationsOpen) setNotificationsOpen(false);
  };

  const toggleNotifications = () => {
    setNotificationsOpen(!notificationsOpen);
    if (dropdownOpen) setDropdownOpen(false);
  };

  const markAsRead = (id: string) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification,
      ),
    );
  };

  const unreadCount = notifications.filter(
    (notification) => !notification.read,
  ).length;

  return (
    <header className="flex h-16 items-center justify-between bg-white px-6 shadow-sm">
      <h1 className="text-2xl font-semibold text-gray-800">Admin Dashboard</h1>

      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <div className="relative" ref={notificationRef}>
          <button
            onClick={toggleNotifications}
            className="relative rounded-full p-2 hover:bg-gray-100"
            aria-label="Notifications"
          >
            <RiNotification3Line className="text-xl" />
            {unreadCount > 0 && (
              <span className="bg-red-500 absolute right-0 top-0 flex h-5 w-5 items-center justify-center rounded-full text-xs text-white">
                {unreadCount}
              </span>
            )}
          </button>

          {notificationsOpen && (
            <div className="absolute right-0 z-10 mt-2 w-80 rounded-md bg-white py-1 shadow-lg">
              <div className="border-b px-4 py-2">
                <h3 className="font-semibold">Notifications</h3>
              </div>

              <div className="max-h-96 overflow-y-auto">
                {notifications.length > 0 ? (
                  notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`border-b px-4 py-3 hover:bg-gray-50 ${!notification.read ? 'bg-blue-50' : ''}`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start justify-between">
                        <h4 className="text-sm font-medium">
                          {notification.title}
                        </h4>
                        <span className="text-xs text-gray-500">
                          {notification.time}
                        </span>
                      </div>
                      <p className="mt-1 text-sm text-gray-600">
                        {notification.message}
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="px-4 py-3 text-center text-gray-500">
                    No notifications
                  </div>
                )}
              </div>

              <div className="border-t px-4 py-2 text-center">
                <button className="hover:text-primary-dark text-sm text-primary">
                  Mark all as read
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Profile Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={toggleDropdown}
            className="flex items-center space-x-2 rounded-md p-2 hover:bg-gray-100"
            aria-label="User menu"
          >
            <div className="flex h-8 w-8 items-center justify-center overflow-hidden rounded-full bg-gray-300">
              {user?.avatarUrl ? (
                <Image
                  src={user.avatarUrl}
                  alt={user.username || 'User'}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
              ) : (
                <span className="text-sm font-medium text-gray-700">
                  {user?.username?.charAt(0).toUpperCase() || 'U'}
                </span>
              )}
            </div>
            <span className="hidden text-sm font-medium md:block">
              {user?.username || 'User'}
            </span>
            <RiArrowDownSLine className="hidden md:block" />
          </button>

          {dropdownOpen && (
            <div className="absolute right-0 z-10 mt-2 w-48 rounded-md bg-white py-1 shadow-lg">
              <button
                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={() => router.push(`/profile/${user?.username}`)}
              >
                <RiUser3Line className="mr-2" />
                Profile
              </button>
              <button
                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={() => router.push('/admin/settings')}
              >
                <RiSettings4Line className="mr-2" />
                Settings
              </button>
              <hr className="my-1" />
              <button
                className="text-red-600 flex w-full items-center px-4 py-2 text-sm hover:bg-gray-100"
                onClick={handleLogout}
              >
                <RiLogoutBoxLine className="mr-2" />
                Logout
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
