/**
 * @file AdminLayout.tsx
 * @description Main layout component for the admin dashboard
 */
'use client';

import { ReactNode, useState, useEffect, useCallback, JSX } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import Link from 'next/link';
import { Bell, ChevronRight, Map, Swords, Users, FileText, BarChart2, Settings } from 'lucide-react';
import Image from 'next/image';

import { ModeToggle } from '@/components/ui/ModeToggle';
import { UserProfileMenu } from '@/components/ui/UserProfileMenu';

import { createClient } from '@/utils/supabase/client';
import { RootState } from '@/lib/store';
import { useRoles } from '@/hooks/useRoles';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// RBAC bypass flag
// TODO: Remove this bypass when RBAC implementation is ready for production
const BYPASS_RBAC = true;

// Define menu item interface
interface IMenuItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  access: 'all' | 'admin' | 'moderator';
}

// Define breadcrumb interface
interface IBreadcrumb {
  name: string;
  path: string;
}

// Define props interface
interface IAdminLayoutProps {
  children: ReactNode;
}

function AdminLayout({ children }: IAdminLayoutProps): JSX.Element {
  const router = useRouter();
  const pathname = usePathname();
  const { isAdmin, isModerator } = useRoles();

  // Get user from Redux store
  const user = useSelector((state: RootState) => state.user.user);

  // State for sidebar, loading, and errors
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [authError, setAuthError] = useState<string | null>(null);
  const [errorCount, setErrorCount] = useState<number>(0);

  // Toggle sidebar function with stable reference
  const toggleSidebar = useCallback(() => {
    setSidebarCollapsed((prev) => !prev);
  }, []);

  // Check authentication and authorization
  useEffect(() => {
    const checkAuth = async (): Promise<void> => {
      try {
        const supabase = createClient();
        const {
          data: { session },
        } = await supabase.auth.getSession();

        // If no session, redirect to login
        if (!session) {
          router.push('/login?redirect=/admin');
          return;
        }

        // Check if user has admin or moderator role
        if (!isAdmin() && !isModerator() && !BYPASS_RBAC) {
          // TODO: Remove BYPASS_RBAC condition when RBAC implementation is ready for production
          setAuthError('You do not have permission to access the admin panel');
          setErrorCount((prev) => prev + 1);
          // Optionally redirect to unauthorized page
          // router.push('/unauthorized');
        }
      } catch (error) {
        console.error('Error checking authentication:', error);
        setAuthError('Authentication error');
        setErrorCount((prev) => prev + 1);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router, isAdmin, isModerator]);

  // Define menu items
  const menuItems: IMenuItem[] = [
    {
      name: 'Dashboard',
      path: '/admin',
      icon: <BarChart2 className="h-5 w-5" />,
      access: 'all', // All admin users can access
    },
    {
      name: 'User Management',
      path: '/admin/users',
      icon: <Users className="h-5 w-5" />,
      access: 'admin', // Only ADMIN role can access
    },
    {
      name: 'Content Management',
      path: '/admin/content',
      icon: <FileText className="h-5 w-5" />,
      access: 'all', // All admin users can access
    },
    {
      name: 'Roadmaps',
      path: '/admin/roadmaps',
      icon: <Map className="h-5 w-5" />,
      access: 'all', // All admin users can access
    },
    {
      name: 'Battles',
      path: '/admin/battles',
      icon: <Swords className="h-5 w-5" />,
      access: 'all', // All admin users can access
    },
    {
      name: 'Analytics',
      path: '/admin/analytics',
      icon: <BarChart2 className="h-5 w-5" />,
      access: 'all', // All admin users can access
    },
    {
      name: 'Settings',
      path: '/admin/settings',
      icon: <Settings className="h-5 w-5" />,
      access: 'admin', // Only ADMIN role can access
    },
  ];

  // Generate breadcrumbs based on current path
  const generateBreadcrumbs = useCallback((): IBreadcrumb[] => {
    if (!pathname) return [];

    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs: IBreadcrumb[] = [{ name: 'Admin', path: '/admin' }];

    let currentPath = '';

    pathSegments.forEach((segment, index) => {
      if (index === 0 && segment === 'admin') return; // Skip the first 'admin' segment

      currentPath += `/${segment}`;
      const fullPath = `/admin${currentPath}`;

      // Format the segment name (capitalize first letter, replace hyphens with spaces)
      const name = segment
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      breadcrumbs.push({ name, path: fullPath });
    });

    return breadcrumbs;
  }, [pathname]);

  const breadcrumbs = generateBreadcrumbs();

  // Show loading state while checking permissions
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <aside
        className={`bg-card shadow-md transition-all duration-300 border-r border-border ${sidebarCollapsed ? 'w-20' : 'w-64'}`}
      >
        {/* Sidebar Header */}
        <div className="flex h-16 items-center justify-between border-b border-border px-4">
          {!sidebarCollapsed && (
            <h1 className="text-xl font-bold text-primary">Admin Panel</h1>
          )}
          <button
            onClick={toggleSidebar}
            className="rounded-md p-2 hover:bg-accent"
            aria-label={
              sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'
            }
          >
            {sidebarCollapsed ? (
              <ChevronRight />
            ) : (
              <ChevronRight className="rotate-180" />
            )}
          </button>
        </div>

        {/* Sidebar Menu */}
        <nav className="p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              // Check if this menu item is active (either exact match or child route)
              const isActive = pathname === item.path || 
                (pathname?.startsWith(item.path) && item.path !== '/admin');

              // Skip admin-only items for moderators
              if (item.access === 'admin' && !isAdmin() && !BYPASS_RBAC) {
                // TODO: Remove BYPASS_RBAC condition when RBAC implementation is ready for production
                return null;
              }

              return (
                <li key={item.path}>
                  <Link
                    href={item.path}
                    className={`flex items-center ${!sidebarCollapsed ? 'px-4' : 'justify-center'} rounded-md py-3 transition-colors ${isActive ? 'bg-primary text-white dark:text-white' : 'hover:bg-accent'}`}
                  >
                    <span className="flex-shrink-0">{item.icon}</span>
                    {!sidebarCollapsed && (
                      <span className="ml-3">{item.name}</span>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* User Info */}
        <div className="absolute bottom-0 w-full border-t border-border p-4">
          <div className="flex items-center">
            <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-muted">
              {user?.avatarUrl ? (
                <Image
                  src={user.avatarUrl}
                  alt={user.username || 'User'}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
              ) : (
                <span className="text-lg font-medium text-foreground">
                  {user?.username?.charAt(0).toUpperCase() || 'U'}
                </span>
              )}
            </div>
            {!sidebarCollapsed && (
              <div className="ml-3">
                <p className="text-sm font-medium text-foreground">{user?.username}</p>
                <p className="truncate text-xs text-muted-foreground">{user?.email}</p>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 flex flex-col overflow-hidden relative">
        {/* Header */}
        <header className="flex h-16 items-center justify-between border-b border-border bg-card px-6">
          {/* Breadcrumbs */}
          <div className="flex items-center">
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="inline-flex items-center space-x-1 md:space-x-3">
                {breadcrumbs.map((crumb, index) => {
                  const isLast = index === breadcrumbs.length - 1;

                  return (
                    <li key={crumb.path} className="inline-flex items-center">
                      {index > 0 && (
                        <ChevronRight className="mx-1 h-4 w-4 text-muted-foreground" />
                      )}
                      {isLast ? (
                        <span className="text-muted-foreground">{crumb.name}</span>
                      ) : (
                        <Link
                          href={crumb.path}
                          className="text-primary hover:text-primary/80"
                        >
                          {crumb.name}
                        </Link>
                      )}
                    </li>
                  );
                })}
              </ol>
            </nav>
          </div>

          {/* Header Actions */}
          <div className="flex items-center space-x-2">
            {/* Import ModeToggle */}
            <div className="mr-1">
              <ModeToggle />
            </div>
            
            {/* Notification Bell */}
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              {errorCount > 0 && (
                <Badge
                  className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full p-0"
                  variant="destructive"
                >
                  {errorCount}
                </Badge>
              )}
            </Button>
            
            {/* User Profile Menu */}
            <UserProfileMenu />
          </div>
        </header>

        {/* Page content */}
        <div className="flex-1 overflow-y-auto bg-background p-4 lg:p-6 min-h-[calc(100vh-4rem)] pb-24 flex flex-col">
          <div className="flex-grow">{children}</div>
          <div className="h-10"></div> {/* Extra space at the bottom */}
        </div>
      </main>
    </div>
  );
}

export default AdminLayout;
