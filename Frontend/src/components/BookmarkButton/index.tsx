/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */
'use client';

import { useState } from 'react';

import { Bookmark, BookmarkX } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useChallengeBookmark } from '@/hooks/useBookmarks';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

/**
 * @file index.tsx
 * @description React component for BookmarkButton
 */

interface BookmarkButtonProps {
  challengeId: string;
  variant?: 'default' | 'outline' | 'ghost' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

export default function BookmarkButton({
  challengeId,
  variant = 'ghost',
  size = 'md',
  showText = false,
  className = '',
}: BookmarkButtonProps) {
  const { isBookmarked, toggleBookmark, isLoading } =
    useChallengeBookmark(challengeId);
  const [isHovering, setIsHovering] = useState(false);

  // Size classes for the icon
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  };

  // Button size classes
  const buttonSizeClasses = {
    sm: showText ? 'h-8 px-3' : 'h-8 w-8 p-0',
    md: showText ? 'h-9 px-4' : 'h-9 w-9 p-0',
    lg: showText ? 'h-10 px-5' : 'h-10 w-10 p-0',
  };

  // Determine which icon to show
  const IconComponent = isBookmarked && isHovering ? BookmarkX : Bookmark;

  // Determine button variant
  const buttonVariant = variant === 'icon' ? 'ghost' : variant;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={buttonVariant}
            size={variant === 'icon' ? 'icon' : undefined}
            className={cn(
              buttonSizeClasses[size],
              isBookmarked ? 'text-yellow-500 hover:text-yellow-600' : '',
              className,
            )}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              toggleBookmark();
            }}
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            disabled={isLoading}
            aria-label={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}
            aria-pressed={isBookmarked}
          >
            <IconComponent
              className={cn(
                sizeClasses[size],
                isBookmarked ? 'fill-current' : '',
              )}
            />
            {showText && (
              <span className="ml-2">
                {isBookmarked ? 'Bookmarked' : 'Bookmark'}
              </span>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          {isBookmarked ? 'Remove bookmark' : 'Add to bookmarks'}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
