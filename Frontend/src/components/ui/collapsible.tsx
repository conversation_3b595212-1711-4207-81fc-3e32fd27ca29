/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */
'use client';

import * as CollapsiblePrimitive from '@radix-ui/react-collapsible';

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

/**
 * @file collapsible.tsx
 * @description React component for collapsible
 */

const Collapsible = CollapsiblePrimitive.Root;

const CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger;

const CollapsibleContent = CollapsiblePrimitive.CollapsibleContent;

export { Collapsible, CollapsibleTrigger, CollapsibleContent };
