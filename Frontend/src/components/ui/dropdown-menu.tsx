/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */
'use client';

import * as React from 'react';

import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';
import { Check, ChevronRight, Circle } from 'lucide-react';

import { cn } from '@/lib/utils';

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * @file dropdown-menu.tsx
 * @description A versatile dropdown menu component for displaying contextual options and commands
 *
 * This component is built on top of Radix UI's DropdownMenu primitive and provides
 * a styled, accessible dropdown menu with support for items, checkboxes, radio items,
 * sub-menus, labels, separators, and keyboard shortcuts.
 *
 * Dropdown menus are commonly used for:
 * - Navigation menus
 * - Action menus (e.g., "More" options)
 * - Selection menus
 * - Context menus (right-click menus)
 * - Command palettes
 *
 * The dropdown menu consists of several sub-components:
 * - DropdownMenu: The root container
 * - DropdownMenuTrigger: The element that triggers the dropdown
 * - DropdownMenuContent: The main content container
 * - DropdownMenuItem: A standard menu item
 * - DropdownMenuCheckboxItem: A menu item with a checkbox
 * - DropdownMenuRadioItem: A menu item with a radio button
 * - DropdownMenuLabel: A non-interactive label
 * - DropdownMenuSeparator: A visual separator between items
 * - DropdownMenuShortcut: A keyboard shortcut indicator
 * - DropdownMenuGroup: A group of related menu items
 * - DropdownMenuSub: A nested sub-menu
 * - DropdownMenuSubTrigger: The element that triggers a sub-menu
 * - DropdownMenuSubContent: The content of a sub-menu
 * - DropdownMenuRadioGroup: A group of radio items
 *
 * @example
 * <DropdownMenu>
 *   <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *   <DropdownMenuContent>
 *     <DropdownMenuItem>Profile</DropdownMenuItem>
 *     <DropdownMenuItem>Settings</DropdownMenuItem>
 *     <DropdownMenuSeparator />
 *     <DropdownMenuItem>Logout</DropdownMenuItem>
 *   </DropdownMenuContent>
 * </DropdownMenu>
 */

/**
 * The root DropdownMenu component
 *
 * This is the container for all dropdown menu components. It manages the state of the dropdown.
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#root}
 */
const DropdownMenu = DropdownMenuPrimitive.Root;

/**
 * The button that triggers the dropdown menu
 *
 * This component is typically a button that, when clicked, opens the dropdown menu.
 *
 * @example
 * <DropdownMenuTrigger>Open Menu</DropdownMenuTrigger>
 *
 * @example
 * <DropdownMenuTrigger asChild>
 *   <Button>Options</Button>
 * </DropdownMenuTrigger>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#trigger}
 */
const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;

/**
 * A group of related dropdown menu items
 *
 * This component is used to group related menu items together.
 *
 * @example
 * <DropdownMenuGroup>
 *   <DropdownMenuItem>Profile</DropdownMenuItem>
 *   <DropdownMenuItem>Settings</DropdownMenuItem>
 * </DropdownMenuGroup>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#group}
 */
const DropdownMenuGroup = DropdownMenuPrimitive.Group;

/**
 * The portal component that renders the dropdown menu in a portal
 *
 * This component renders its children in a portal, which is useful for
 * rendering content outside the DOM hierarchy of the parent component.
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#portal}
 */
const DropdownMenuPortal = DropdownMenuPrimitive.Portal;

/**
 * A nested sub-menu within a dropdown menu
 *
 * This component is used to create nested dropdown menus.
 *
 * @example
 * <DropdownMenuSub>
 *   <DropdownMenuSubTrigger>More Options</DropdownMenuSubTrigger>
 *   <DropdownMenuSubContent>
 *     <DropdownMenuItem>Option 1</DropdownMenuItem>
 *     <DropdownMenuItem>Option 2</DropdownMenuItem>
 *   </DropdownMenuSubContent>
 * </DropdownMenuSub>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#sub}
 */
const DropdownMenuSub = DropdownMenuPrimitive.Sub;

/**
 * A group of radio items within a dropdown menu
 *
 * This component is used to group radio items together, ensuring that only one
 * item in the group can be selected at a time.
 *
 * @example
 * <DropdownMenuRadioGroup value={theme} onValueChange={setTheme}>
 *   <DropdownMenuRadioItem value="light">Light</DropdownMenuRadioItem>
 *   <DropdownMenuRadioItem value="dark">Dark</DropdownMenuRadioItem>
 *   <DropdownMenuRadioItem value="system">System</DropdownMenuRadioItem>
 * </DropdownMenuRadioGroup>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#radiogroup}
 */
const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;

/**
 * The trigger component for a sub-menu
 *
 * This component is used to trigger a nested sub-menu. It displays a chevron icon
 * on the right to indicate that it opens a sub-menu.
 *
 * @example
 * <DropdownMenuSubTrigger>More Options</DropdownMenuSubTrigger>
 *
 * @example
 * <DropdownMenuSubTrigger inset>
 *   More Options (with inset padding)
 * </DropdownMenuSubTrigger>
 *
 * @param props - Component props
 * @param props.inset - Whether to add left padding to align with checkbox and radio items
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#subtrigger}
 */
const DropdownMenuSubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
    inset?: boolean;
  }
>(({ className, inset, children, ...props }, ref) => (
  <DropdownMenuPrimitive.SubTrigger
    ref={ref}
    className={cn(
      'flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
      inset && 'pl-8', // Add left padding if inset is true
      className,
    )}
    {...props}
  >
    {children}
    {/* Chevron icon indicating a sub-menu */}
    <ChevronRight className="ml-auto" />
  </DropdownMenuPrimitive.SubTrigger>
));
DropdownMenuSubTrigger.displayName =
  DropdownMenuPrimitive.SubTrigger.displayName;

/**
 * The content component for a sub-menu
 *
 * This component renders the content of a nested sub-menu. It has similar styling
 * to the main dropdown menu content but is positioned relative to its trigger.
 *
 * @example
 * <DropdownMenuSubContent>
 *   <DropdownMenuItem>Sub-menu Item 1</DropdownMenuItem>
 *   <DropdownMenuItem>Sub-menu Item 2</DropdownMenuItem>
 * </DropdownMenuSubContent>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#subcontent}
 */
const DropdownMenuSubContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.SubContent
    ref={ref}
    className={cn(
      'z-50 min-w-[8rem] origin-[--radix-dropdown-menu-content-transform-origin] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
      className,
    )}
    {...props}
  />
));
DropdownMenuSubContent.displayName =
  DropdownMenuPrimitive.SubContent.displayName;

/**
 * The main content container for the dropdown menu
 *
 * This component renders the content of the dropdown menu. It includes styling for
 * positioning, animations, and scrolling behavior.
 *
 * @example
 * <DropdownMenuContent>
 *   <DropdownMenuItem>Item 1</DropdownMenuItem>
 *   <DropdownMenuItem>Item 2</DropdownMenuItem>
 * </DropdownMenuContent>
 *
 * @example
 * <DropdownMenuContent className="w-56">
 *   <DropdownMenuItem>Custom width content</DropdownMenuItem>
 * </DropdownMenuContent>
 *
 * @param props - Component props
 * @param props.sideOffset - Distance from the trigger (default: 4)
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#content}
 */
const DropdownMenuContent = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <DropdownMenuPrimitive.Portal>
    <DropdownMenuPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        'z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md',
        'origin-[--radix-dropdown-menu-content-transform-origin] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
        className,
      )}
      {...props}
    />
  </DropdownMenuPrimitive.Portal>
));
DropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;

/**
 * A standard menu item in the dropdown menu
 *
 * This component renders a clickable item in the dropdown menu. It can contain
 * text, icons, or other elements.
 *
 * @example
 * <DropdownMenuItem>Profile</DropdownMenuItem>
 *
 * @example
 * <DropdownMenuItem inset>
 *   Settings (with inset padding)
 * </DropdownMenuItem>
 *
 * @example
 * <DropdownMenuItem>
 *   <User className="mr-2 h-4 w-4" />
 *   Profile
 *   <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
 * </DropdownMenuItem>
 *
 * @param props - Component props
 * @param props.inset - Whether to add left padding to align with checkbox and radio items
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#item}
 */
const DropdownMenuItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
    inset?: boolean;
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Item
    ref={ref}
    className={cn(
      'relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0',
      inset && 'pl-8', // Add left padding if inset is true
      className,
    )}
    {...props}
  />
));
DropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;

/**
 * A checkbox item in the dropdown menu
 *
 * This component renders a menu item with a checkbox that can be toggled on and off.
 * It displays a checkmark icon when checked.
 *
 * @example
 * const [showStatusBar, setShowStatusBar] = React.useState(true)
 *
 * <DropdownMenuCheckboxItem
 *   checked={showStatusBar}
 *   onCheckedChange={setShowStatusBar}
 * >
 *   Show Status Bar
 * </DropdownMenuCheckboxItem>
 *
 * @param props - Component props
 * @param props.checked - Whether the checkbox is checked
 * @param props.onCheckedChange - Function called when the checked state changes
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#checkboxitem}
 */
const DropdownMenuCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
  <DropdownMenuPrimitive.CheckboxItem
    ref={ref}
    className={cn(
      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    checked={checked}
    {...props}
  >
    {/* Checkbox indicator container */}
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.CheckboxItem>
));
DropdownMenuCheckboxItem.displayName =
  DropdownMenuPrimitive.CheckboxItem.displayName;

/**
 * A radio item in the dropdown menu
 *
 * This component renders a menu item with a radio button that can be selected.
 * It displays a filled circle icon when selected. Radio items should be used
 * within a DropdownMenuRadioGroup to ensure only one item can be selected at a time.
 *
 * @example
 * const [position, setPosition] = React.useState("top")
 *
 * <DropdownMenuRadioGroup value={position} onValueChange={setPosition}>
 *   <DropdownMenuRadioItem value="top">Top</DropdownMenuRadioItem>
 *   <DropdownMenuRadioItem value="bottom">Bottom</DropdownMenuRadioItem>
 * </DropdownMenuRadioGroup>
 *
 * @param props - Component props
 * @param props.value - The value of the radio item
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#radioitem}
 */
const DropdownMenuRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
  <DropdownMenuPrimitive.RadioItem
    ref={ref}
    className={cn(
      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    {...props}
  >
    {/* Radio indicator container */}
    <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <DropdownMenuPrimitive.ItemIndicator>
        <Circle className="h-2 w-2 fill-current" />
      </DropdownMenuPrimitive.ItemIndicator>
    </span>
    {children}
  </DropdownMenuPrimitive.RadioItem>
));
DropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;

/**
 * A label in the dropdown menu
 *
 * This component renders a non-interactive label that can be used to group or
 * describe menu items. It's typically used as a header for a group of related items.
 *
 * @example
 * <DropdownMenuLabel>Account</DropdownMenuLabel>
 * <DropdownMenuItem>Profile</DropdownMenuItem>
 * <DropdownMenuItem>Settings</DropdownMenuItem>
 *
 * @example
 * <DropdownMenuLabel inset>Account</DropdownMenuLabel>
 *
 * @param props - Component props
 * @param props.inset - Whether to add left padding to align with checkbox and radio items
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#label}
 */
const DropdownMenuLabel = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
    inset?: boolean;
  }
>(({ className, inset, ...props }, ref) => (
  <DropdownMenuPrimitive.Label
    ref={ref}
    className={cn(
      'px-2 py-1.5 text-sm font-semibold',
      inset && 'pl-8', // Add left padding if inset is true
      className,
    )}
    {...props}
  />
));
DropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;

/**
 * A separator in the dropdown menu
 *
 * This component renders a horizontal line that can be used to separate groups
 * of menu items visually.
 *
 * @example
 * <DropdownMenuItem>Profile</DropdownMenuItem>
 * <DropdownMenuItem>Settings</DropdownMenuItem>
 * <DropdownMenuSeparator />
 * <DropdownMenuItem>Logout</DropdownMenuItem>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dropdown-menu#separator}
 */
const DropdownMenuSeparator = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <DropdownMenuPrimitive.Separator
    ref={ref}
    className={cn('-mx-1 my-1 h-px bg-muted', className)}
    {...props}
  />
));
DropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;

/**
 * A keyboard shortcut indicator for dropdown menu items
 *
 * This component renders a keyboard shortcut hint at the right side of a menu item.
 * It's not part of Radix UI's primitives but a custom addition for better UX.
 *
 * @example
 * <DropdownMenuItem>
 *   New File
 *   <DropdownMenuShortcut>⌘N</DropdownMenuShortcut>
 * </DropdownMenuItem>
 *
 * @example
 * <DropdownMenuItem>
 *   Save
 *   <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
 * </DropdownMenuItem>
 */
const DropdownMenuShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}
      {...props}
    />
  );
};
DropdownMenuShortcut.displayName = 'DropdownMenuShortcut';

export {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuRadioGroup,
};
