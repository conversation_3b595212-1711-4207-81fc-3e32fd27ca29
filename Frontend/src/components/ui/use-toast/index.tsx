/**
 * @file index.tsx
 * @description Toast notification component for the UI
 */
'use client';

import { useState, useEffect, ReactNode } from 'react';
import { createContext, useContext } from 'react';

interface IToastProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
  duration?: number;
}

interface IToastContextType {
  toast: (props: IToastProps) => void;
  dismiss: () => void;
}

const ToastContext = createContext<IToastContextType | null>(null);

function ToastProvider({ children }: { children: ReactNode }) {
  const [toast, setToast] = useState<IToastProps | null>(null);
  const [visible, setVisible] = useState(false);

  const showToast = (props: IToastProps) => {
    setToast(props);
    setVisible(true);
  };

  const dismissToast = () => {
    setVisible(false);
  };

  useEffect(() => {
    if (visible && toast?.duration) {
      const timer = setTimeout(() => {
        setVisible(false);
      }, toast.duration);

      return () => clearTimeout(timer);
    }
  }, [visible, toast]);

  return (
    <ToastContext.Provider value={{ toast: showToast, dismiss: dismissToast }}>
      {children}

      {/* Toast UI */}
      {visible && toast && (
        <div
          className={`fixed bottom-4 right-4 z-50 max-w-md rounded-md shadow-lg transition-all duration-300 ${visible ? 'opacity-100' : 'opacity-0'} ${
            toast.variant === 'destructive'
              ? 'bg-destructive text-destructive-foreground'
              : toast.variant === 'success'
                ? 'bg-success text-success-foreground'
                : toast.variant === 'warning'
                  ? 'bg-warning text-warning-foreground'
                  : 'border border-border bg-background text-foreground'
          }`}
        >
          <div className="flex p-4">
            <div className="flex-1">
              {toast.title && <h3 className="font-medium">{toast.title}</h3>}
              {toast.description && (
                <p className="text-sm">{toast.description}</p>
              )}
            </div>
            <button
              onClick={dismissToast}
              className="ml-4 text-sm opacity-70 hover:opacity-100"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </ToastContext.Provider>
  );
}

function useToast() {
  const context = useContext(ToastContext);

  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }

  return context;
}

const toast = (props: IToastProps) => {
  // This is a placeholder for direct imports
  // In a real implementation, this would use a global context or event system
  console.warn(
    'Toast was called outside of a component. For proper functionality, use the useToast hook within a component.',
  );
  console.info('Toast props:', props);
};

export { ToastProvider, useToast, toast };
