/**
 * @file index.tsx
 * @description Advanced data table component with pagination, filtering, sorting, and row selection
 */
'use client';

import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  VisibilityState,
  RowSelectionState,
  Table,
  Row,
  Updater
} from "@tanstack/react-table";
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface IDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  searchKey?: string;
  showRowSelection?: boolean;
  onRowSelectionChange?: (selectedRows: TData[]) => void;
  onPaginationChange?: (pageIndex: number, pageSize: number) => void;
  initialPageIndex?: number;
  initialPageSize?: number;
  pageCount?: number;
  isLoading?: boolean; // Added loading state prop
  serverSidePagination?: boolean; // Flag to indicate if pagination is handled by server
  onRowClick?: (row: TData) => void; // Added row click handler
  columnVisibility?: Record<string, boolean>; // Control column visibility
  onColumnVisibilityChange?: (visibility: Record<string, boolean>) => void; // Handle column visibility changes
  onHeaderCheckboxChange?: (table: Table<TData>) => void; // Custom header checkbox behavior
}

// Memoized checkbox component to prevent unnecessary re-renders
// Simple header checkbox component
const HeaderCheckbox = memo(({ checked, onChange }: { 
  checked: boolean | 'indeterminate'; 
  onChange: (value: boolean | 'indeterminate') => void; 
}) => {
  return (
    <Checkbox
      checked={checked === true}
      className="translate-y-[2px]"
      onCheckedChange={(value) => {
        // Stop propagation to prevent navigation
        onChange(!!value);
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
      {...(checked === 'indeterminate' ? { 'data-state': 'indeterminate' } : {})}
    />
  );
});

HeaderCheckbox.displayName = 'HeaderCheckbox';

// Simple row checkbox component
const RowCheckbox = memo(({ checked, onChange }: { 
  checked: boolean; 
  onChange: (value: boolean | 'indeterminate') => void; 
}) => {
  return (
    <Checkbox
      checked={checked}
      className="translate-y-[2px]"
      onCheckedChange={(value) => {
        // Stop propagation to prevent navigation
        onChange(!!value);
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
    />
  );
});

RowCheckbox.displayName = 'RowCheckbox';

function DataTable<TData, TValue>({
  columns,
  data,
  searchKey,
  showRowSelection = false,
  onRowSelectionChange,
  onPaginationChange,
  initialPageIndex = 0,
  initialPageSize = 10,
  pageCount: externalPageCount,
  isLoading = false,
  serverSidePagination = false,
  onRowClick,
  columnVisibility: externalColumnVisibility,
  onColumnVisibilityChange,
  onHeaderCheckboxChange,
}: IDataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(externalColumnVisibility || {});
  
  // Update internal state when external visibility changes
  useEffect(() => {
    if (externalColumnVisibility) {
      setColumnVisibility(externalColumnVisibility);
    }
  }, [externalColumnVisibility]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pageIndex, setPageIndex] = useState(initialPageIndex);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [pageCount, setPageCount] = useState(externalPageCount || 0);
  const [gotoPage, setGotoPage] = useState('');
  const [prevSelectedRows, setPrevSelectedRows] = useState<TData[]>([]);

  // Memoize row selection handler to prevent infinite loops
  const handleRowSelectionChange = useCallback((updater: RowSelectionState | ((old: RowSelectionState) => RowSelectionState)) => {
    setRowSelection(updater);
  }, []);

  // Memoize the selection column to prevent re-creation on every render
  const selectionColumn = useMemo(() => ({
    id: 'select',
    header: ({ table }: { table: Table<TData> }) => {
      const isAllSelected = table.getIsAllPageRowsSelected();
      const isSomeSelected = table.getIsSomePageRowsSelected();
      const checked = isAllSelected || (isSomeSelected && 'indeterminate');
      
      return (
        <HeaderCheckbox 
          checked={checked} 
          onChange={() => {
            if (onHeaderCheckboxChange) {
              onHeaderCheckboxChange(table);
            } else {
              table.toggleAllPageRowsSelected(!!isAllSelected === false);
            }
          }} 
        />
      );
    },
    cell: ({ row }: { row: Row<TData> }) => {
      return (
        <RowCheckbox 
          checked={row.getIsSelected()} 
          onChange={(value) => row.toggleSelected(!!value)} 
        />
      );
    },
    enableSorting: false,
    enableHiding: false,
  }), []);

  // Memoize table columns to prevent re-creation on every render
  const tableColumns = useMemo(() => {
    // Using type assertion to avoid 'any' type warning
    return showRowSelection ? [selectionColumn, ...columns] : columns;
  }, [columns, selectionColumn, showRowSelection]);

  // Memoize table options to prevent re-creation on every render
  const tableOptions = useMemo(() => ({
    data,
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
    // Only use client-side pagination model if not using server-side pagination
    ...(serverSidePagination ? {} : { getPaginationRowModel: getPaginationRowModel() }),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: (updater: Updater<VisibilityState>) => {
      // Handle both function and direct value updates
      const newVisibility = typeof updater === 'function' 
        ? updater(columnVisibility)
        : updater;
      
      setColumnVisibility(newVisibility);
      
      // If external handler is provided, call it
      if (onColumnVisibilityChange) {
        onColumnVisibilityChange(newVisibility);
      }
    },
    onRowSelectionChange: (updater: Updater<RowSelectionState>) => {
      // Handle both function and direct value updates
      const newSelection = typeof updater === 'function'
        ? updater(rowSelection)
        : updater;
      
      setRowSelection(newSelection);
      
      // If external handler is provided, call it with the selected rows
      if (onRowSelectionChange) {
        const selectedRows = Object.keys(newSelection)
          .filter(key => newSelection[key])
          .map(key => {
            const rowIndex = parseInt(key, 10);
            return data[rowIndex];
          });
        
        onRowSelectionChange(selectedRows);
      }
    },
    onGlobalFilterChange: setGlobalFilter,
    // For server-side pagination, we need to manually set the page count
    ...(serverSidePagination && externalPageCount !== undefined ? { manualPagination: true, pageCount: externalPageCount } : {}),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
      pagination: {
        pageIndex,
        pageSize,
      },
    },
  }), [
    data, 
    tableColumns, 
    sorting, 
    columnFilters, 
    columnVisibility, 
    rowSelection, 
    globalFilter, 
    pageIndex, 
    pageSize, 
    handleRowSelectionChange
  ]);

  // Create table instance with memoized options
  const table = useReactTable(tableOptions);

  // Update page count when data changes or external pageCount changes
  useEffect(() => {
    if (externalPageCount !== undefined) {
      setPageCount(externalPageCount);
    } else if (!serverSidePagination) {
      // Only calculate page count from data length if not using server-side pagination
      const count = Math.ceil(data.length / pageSize);
      setPageCount(count);
    }
  }, [data, pageSize, externalPageCount, serverSidePagination]);

  // Memoize selected rows calculation to prevent unnecessary recalculations
  const selectedRows = useMemo(() => {
    if (!showRowSelection) return [];
    
    const selectedRowsCount = Object.keys(rowSelection).length;
    if (selectedRowsCount === 0) return [];
    
    return table
      .getFilteredRowModel()
      .rows.filter((row) => row.getIsSelected())
      .map((row) => row.original as TData);
  }, [rowSelection, table, showRowSelection]);
  
  // Notify parent component about selected rows only when they actually change
  useEffect(() => {
    if (!onRowSelectionChange) return;
    
    // Compare current selection with previous selection using a type-safe approach
    const getRowIdentifier = (row: TData): string => {
      const record = row as Record<string, any>;
      return (record.id as string) || (record._id as string) || JSON.stringify(record);
    };
    
    const currentIds = selectedRows.map(getRowIdentifier).sort();
    const prevIds = prevSelectedRows.map(getRowIdentifier).sort();
    
    // Only update if the selection has actually changed
    if (currentIds.length !== prevIds.length || 
        currentIds.some((id, i) => id !== prevIds[i])) {
      onRowSelectionChange(selectedRows);
      setPrevSelectedRows(selectedRows);
    }
  }, [selectedRows, prevSelectedRows, onRowSelectionChange]);

  // Handle row click events
  const handleRowClick = useCallback((row: Row<TData>) => {
    if (onRowClick) {
      onRowClick(row.original);
    }
  }, [onRowClick]);

  // Handle goto page input
  const handleGotoPage = useCallback(() => {
    const page = parseInt(gotoPage);
    if (isNaN(page)) return;
    
    // Ensure page is within valid range
    const validPage = Math.max(1, Math.min(page, pageCount));
    const newPageIndex = validPage - 1;
    
    // Only trigger navigation if the page is different
    if (newPageIndex !== pageIndex) {
      table.setPageIndex(newPageIndex);
      setPageIndex(newPageIndex);
      
      if (onPaginationChange) {
        onPaginationChange(newPageIndex, pageSize);
      }
    }
    
    // Clear input after navigation
    setGotoPage('');
  }, [gotoPage, pageCount, table, onPaginationChange, pageSize, pageIndex]);

  return (
    <div className="space-y-4">
      {/* Table Controls */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4 py-4 px-2 border-b">
        {searchKey && (
          <div className="w-full sm:flex-1">
            <Input
              placeholder={`Search ${searchKey}...`}
              value={globalFilter ?? ''}
              onChange={(event) => setGlobalFilter(event.target.value)}
              className="max-w-sm w-full"
              disabled={isLoading}
            />
          </div>
        )}
        <div className="flex items-center gap-2">
          <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto cursor-pointer" disabled={isLoading}>
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuItem key={column.id} className="capitalize">
                      <Checkbox
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                        className="mr-2"
                      />
                      {column.id}
                    </DropdownMenuItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <table className="w-full divide-y divide-border">
            <thead className="bg-muted/50">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <th
                        key={header.id}
                        colSpan={header.colSpan}
                        className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground"
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                      </th>
                    );
                  })}
                </tr>
              ))}
            </thead>
            <tbody className="divide-y divide-border bg-card">
              {isLoading ? (
                // Show loading state
                <tr>
                  <td
                    colSpan={columns.length + (showRowSelection ? 1 : 0)}
                    className="px-4 py-10 text-center text-sm text-muted-foreground"
                  >
                    <div className="flex items-center justify-center space-x-2">
                      <svg className="h-5 w-5 animate-spin text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Loading...</span>
                    </div>
                  </td>
                </tr>
              ) : table.getRowModel().rows.length > 0 ? (
                table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    className={`${row.getIsSelected() ? 'bg-primary/5' : ''} hover:bg-accent/50 ${onRowClick ? 'cursor-pointer' : ''}`}
                    onClick={() => handleRowClick(row)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td key={cell.id} className="px-4 py-3">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={columns.length + (showRowSelection ? 1 : 0)}
                    className="px-4 py-10 text-center text-sm text-muted-foreground"
                  >
                    No results found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col md:flex-row items-center justify-between gap-4 py-4 px-2 border-t">
        <div className="flex items-center">
          <p className="text-sm text-muted-foreground font-medium">
            {serverSidePagination ? (
              // For server-side pagination, we use the meta information
              <>
                Showing entries {pageIndex * pageSize + 1} to{' '}
                {Math.min((pageIndex + 1) * pageSize, data.length ? (pageIndex * pageSize) + data.length : 0)}{' '}
                of {pageCount * pageSize > 0 ? pageCount * pageSize : 0}
              </>
            ) : (
              // For client-side pagination, we use the table's filtered row model
              <>
                Showing{' '}
                <span className="font-medium">
                  {table.getState().pagination.pageIndex *
                    table.getState().pagination.pageSize +
                    1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(
                    (table.getState().pagination.pageIndex + 1) *
                      table.getState().pagination.pageSize,
                    table.getFilteredRowModel().rows.length,
                  )}
                </span>{' '}
                of{' '}
                <span className="font-medium">
                  {table.getFilteredRowModel().rows.length}
                </span>{' '}
                entries
              </>
            )}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row items-center gap-4">
          <div className="flex items-center gap-2">
            <p className="text-sm text-muted-foreground font-medium">Rows per page</p>
            <select
              value={pageSize}
              onChange={(e) => {
                const newPageSize = Number(e.target.value);
                setPageSize(newPageSize);
                table.setPageSize(newPageSize);
                // Reset to page 0 when changing page size
                setPageIndex(0);
                if (onPaginationChange) {
                  // Always go back to first page when changing page size
                  onPaginationChange(0, newPageSize);
                }
              }}
              className="h-8 w-16 rounded-md border border-input bg-background px-2 py-1 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 cursor-pointer"
              disabled={isLoading}
            >
              {[5, 10, 20, 30, 40, 50]
                // Always show 5 and 10 as options, and any larger options that make sense
                .filter(size => size <= Math.max(10, table.getFilteredRowModel().rows.length) || size === 5 || size === 10)
                .map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
            </select>
          </div>

          <div className="flex items-center gap-2">
            <p className="text-sm text-muted-foreground font-medium">Go to page</p>
            <div className="flex items-center gap-1">
              <Input
                type="number"
                min={1}
                max={pageCount}
                value={gotoPage}
                onChange={(e) => setGotoPage(e.target.value)}
                className="h-8 w-16 cursor-text"
                onKeyDown={(e) => e.key === 'Enter' && handleGotoPage()}
                disabled={isLoading}
              />
              <Button
                variant="outline"
                size="sm"
                className="cursor-pointer"
                onClick={handleGotoPage}
                disabled={isLoading || !gotoPage}
              >
                Go
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2 mt-2 sm:mt-0">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => {
                table.setPageIndex(0);
                setPageIndex(0);
                if (onPaginationChange) {
                  onPaginationChange(0, pageSize);
                }
              }}
              disabled={isLoading || pageIndex === 0}
            >
              <span className="sr-only">Go to first page</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0 cursor-pointer"
              onClick={() => {
                const newPageIndex = pageIndex - 1;
                table.setPageIndex(newPageIndex);
                setPageIndex(newPageIndex);
                if (onPaginationChange) {
                  onPaginationChange(newPageIndex, pageSize);
                }
              }}
              disabled={isLoading || pageIndex === 0}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-1 min-w-[5rem] justify-center bg-muted/30 px-2 py-1 rounded-md">
              <span className="text-sm font-medium">
                {pageIndex + 1}
              </span>
              <span className="text-sm text-muted-foreground">of</span>
              <span className="text-sm font-medium">{pageCount || 1}</span>
            </div>
            <Button
              variant="outline"
              className="h-8 w-8 p-0 cursor-pointer"
              onClick={() => {
                const newPageIndex = pageIndex + 1;
                table.setPageIndex(newPageIndex);
                setPageIndex(newPageIndex);
                if (onPaginationChange) {
                  onPaginationChange(newPageIndex, pageSize);
                }
              }}
              disabled={isLoading || pageIndex >= pageCount - 1}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => {
                const newPageIndex = pageCount - 1;
                table.setPageIndex(newPageIndex);
                setPageIndex(newPageIndex);
                if (onPaginationChange) {
                  onPaginationChange(newPageIndex, pageSize);
                }
              }}
              disabled={isLoading || pageIndex >= pageCount - 1}
            >
              <span className="sr-only">Go to last page</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export { DataTable };
export type { IDataTableProps };
