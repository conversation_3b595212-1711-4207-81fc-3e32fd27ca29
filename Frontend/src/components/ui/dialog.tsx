/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */
'use client';

import * as React from 'react';

import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X } from 'lucide-react';

import { cn } from '@/lib/utils';

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * @file dialog.tsx
 * @description A modal dialog component for displaying content that requires user interaction
 *
 * This component is built on top of Radix UI's Dialog primitive and provides
 * a styled, accessible dialog with animation effects. It's designed for displaying
 * content that requires user attention or interaction, such as forms, details,
 * or confirmation messages.
 *
 * Unlike AlertDialog, the Dialog component is more general-purpose and can be
 * dismissed by clicking outside or pressing the escape key.
 *
 * The dialog consists of several sub-components:
 * - Dialog: The root container
 * - DialogTrigger: The element that triggers the dialog
 * - DialogContent: The main content container
 * - DialogHeader: Container for the dialog's header content
 * - DialogTitle: The dialog's title
 * - DialogDescription: A description or subtitle for the dialog
 * - DialogFooter: Container for actions at the bottom of the dialog
 * - DialogClose: A button to close the dialog
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open Dialog</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Dialog Title</DialogTitle>
 *       <DialogDescription>Dialog Description</DialogDescription>
 *     </DialogHeader>
 *     <p>Dialog content goes here.</p>
 *     <DialogFooter>
 *       <Button>Save</Button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */

/**
 * The root Dialog component
 *
 * This is the container for all dialog components. It manages the state of the dialog.
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dialog#root}
 */
const Dialog = DialogPrimitive.Root;

/**
 * The button that triggers the dialog
 *
 * This component is typically a button that, when clicked, opens the dialog.
 *
 * @example
 * <DialogTrigger>Open Dialog</DialogTrigger>
 *
 * @example
 * <DialogTrigger asChild>
 *   <Button>Open Dialog</Button>
 * </DialogTrigger>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dialog#trigger}
 */
const DialogTrigger = DialogPrimitive.Trigger;

/**
 * The portal component that renders the dialog in a portal
 *
 * This component renders its children in a portal, which is useful for
 * rendering content outside the DOM hierarchy of the parent component.
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dialog#portal}
 */
const DialogPortal = DialogPrimitive.Portal;

/**
 * The button that closes the dialog
 *
 * This component is typically a button that, when clicked, closes the dialog.
 *
 * @example
 * <DialogClose>Close</DialogClose>
 *
 * @example
 * <DialogClose asChild>
 *   <Button variant="outline">Cancel</Button>
 * </DialogClose>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dialog#close}
 */
const DialogClose = DialogPrimitive.Close;

/**
 * The overlay component that covers the page behind the dialog
 *
 * This component renders a semi-transparent overlay that covers the entire viewport
 * when the dialog is open. It helps focus attention on the dialog by dimming
 * the content behind it.
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dialog#overlay}
 */
const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      'fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className,
    )}
    {...props}
  />
));
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

/**
 * The main content container for the dialog
 *
 * This component renders the dialog content with proper positioning and styling.
 * It includes animations for opening and closing the dialog, and a close button
 * in the top-right corner.
 *
 * @example
 * <DialogContent>
 *   <DialogHeader>
 *     <DialogTitle>Dialog Title</DialogTitle>
 *     <DialogDescription>Dialog Description</DialogDescription>
 *   </DialogHeader>
 *   <p>Dialog content goes here.</p>
 *   <DialogFooter>
 *     <Button>Save</Button>
 *   </DialogFooter>
 * </DialogContent>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dialog#content}
 */
const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPortal>
    <DialogOverlay />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',
        className,
      )}
      {...props}
    >
      {children}
      {/* Close button in the top-right corner */}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </DialogPortal>
));
DialogContent.displayName = DialogPrimitive.Content.displayName;

/**
 * A container for the dialog header content
 *
 * This component provides consistent styling for the header section of the dialog.
 * It typically contains the title and description.
 *
 * @example
 * <DialogHeader>
 *   <DialogTitle>Dialog Title</DialogTitle>
 *   <DialogDescription>Dialog Description</DialogDescription>
 * </DialogHeader>
 */
const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col space-y-1.5 text-center sm:text-left',
      className,
    )}
    {...props}
  />
);
DialogHeader.displayName = 'DialogHeader';

/**
 * A container for the dialog footer content
 *
 * This component provides consistent styling for the footer section of the dialog.
 * It typically contains action buttons.
 *
 * On mobile, it stacks the buttons vertically with the primary action on top.
 * On desktop, it displays the buttons horizontally with the primary action on the right.
 *
 * @example
 * <DialogFooter>
 *   <Button variant="outline">Cancel</Button>
 *   <Button>Save</Button>
 * </DialogFooter>
 */
const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',
      className,
    )}
    {...props}
  />
);
DialogFooter.displayName = 'DialogFooter';

/**
 * The title component for the dialog
 *
 * This component renders the main heading of the dialog.
 * It should clearly state the purpose of the dialog.
 *
 * @example
 * <DialogTitle>Create New Project</DialogTitle>
 *
 * @example
 * <DialogTitle className="text-xl text-primary">
 *   Custom Styled Title
 * </DialogTitle>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dialog#title}
 */
const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      'text-lg font-semibold leading-none tracking-tight',
      className,
    )}
    {...props}
  />
));
DialogTitle.displayName = DialogPrimitive.Title.displayName;

/**
 * The description component for the dialog
 *
 * This component renders additional information about the dialog's purpose.
 * It should provide context and explain what the user can do in the dialog.
 *
 * @example
 * <DialogDescription>
 *   Fill out the form below to create a new project. All fields are required.
 * </DialogDescription>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/dialog#description}
 */
const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));
DialogDescription.displayName = DialogPrimitive.Description.displayName;

export {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogTrigger,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
};
