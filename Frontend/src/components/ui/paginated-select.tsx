import { AsyncPaginate, LoadOptions } from 'react-select-async-paginate';
import { GroupBase, StylesConfig } from 'react-select';

import { cn } from '@/lib/utils';

export interface IOption {
  value: string;
  label: string;
}

export interface IPaginatedSelectProps {
  getOptions: (params: { page: number; limit: number }) => Promise<{
    data: Array<{ id: string; title: string }>;
    meta: {
      total: number;
      currentPage: number;
      totalPages: number;
      limit: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  }>;
  value: IOption | null;
  onChange: (option: IOption | null) => void;
  placeholder?: string;
  isDisabled?: boolean;
}

export function PaginatedSelect({
  getOptions,
  value,
  onChange,
  placeholder = 'Select...',
  isDisabled = false,
}: IPaginatedSelectProps) {
  // Handle the case where additional might be undefined
  const loadOptions: LoadOptions<
    IOption,
    GroupBase<IOption>,
    { page: number }
  > = async (
    _search: string,
    _loadedOptions: unknown,
    additional?: { page: number },
  ) => {
    // Default to page 1 if additional is undefined
    const page = additional?.page || 1;
    try {
      const response = await getOptions({ page, limit: 10 });
      const options = response.data.map((item) => ({
        value: item.id,
        label: item.title,
      }));

      return {
        options,
        hasMore: response.meta.hasNextPage,
        additional: {
          page: page + 1,
        },
      };
    } catch (error) {
      console.error('Error loading options:', error);
      return {
        options: [],
        hasMore: false,
        additional: {
          page: page,
        },
      };
    }
  };

  // Custom styles to match the theme with proper typing
  // Use the StylesConfig type from react-select for proper typing
  const customStyles: StylesConfig<IOption, false, GroupBase<IOption>> = {
    control: (
      provided: Record<string, unknown>,
      state: { isFocused: boolean },
    ) => ({
      ...provided,
      backgroundColor: 'var(--background)',
      color: 'var(--foreground)',
      borderColor: state.isFocused ? 'var(--primary)' : 'var(--border)',
      borderWidth: '1px',
      borderRadius: '0.5rem',
      boxShadow: state.isFocused ? '0 0 0 2px var(--primary-50)' : 'none',
      '&:hover': {
        borderColor: state.isFocused ? 'var(--primary)' : 'var(--primary-50)',
      },
      padding: '8px 12px',
      minHeight: '44px',
      transition: 'all 200ms ease',
      fontSize: '0.95rem',
      cursor: 'pointer',
    }),
    menu: (provided: Record<string, unknown>) => ({
      ...provided,
      backgroundColor: 'var(--background)',
      border: '1px solid var(--border)',
      borderRadius: '0.5rem',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)',
      marginTop: '8px',
      padding: '8px',
      overflow: 'visible',
      zIndex: 9999, // Ensure it's above everything else
      // Use 'absolute' as a valid CSS position value
      position: 'absolute' as const,
      width: '100%', // Full width
      color: 'var(--foreground)', // Ensure text is visible
    }),
    menuList: (provided: Record<string, unknown>) => ({
      ...provided,
      backgroundColor: 'var(--background)',
      padding: '4px',
    }),
    option: (
      provided: Record<string, unknown>,
      state: { isSelected: boolean; isFocused: boolean },
    ) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? 'var(--primary)'
        : state.isFocused
          ? 'var(--accent)'
          : 'var(--background)',
      color: state.isSelected ? 'white' : 'var(--foreground)',
      padding: '12px 14px',
      fontSize: '0.95rem',
      borderRadius: '0.3rem',
      margin: '3px 0',
      '&:active': {
        backgroundColor: 'var(--primary-50)',
      },
      cursor: 'pointer',
      display: 'block', // Ensure options are displayed
      transition: 'all 150ms ease',
      opacity: 1, // Ensure full opacity
    }),
    input: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--foreground)',
    }),
    placeholder: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--muted-foreground)',
    }),
    singleValue: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--foreground)',
    }),
    multiValue: (provided: Record<string, unknown>) => ({
      ...provided,
      backgroundColor: 'var(--accent)',
    }),
    multiValueLabel: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--foreground)',
    }),
    multiValueRemove: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--foreground-muted)',
      '&:hover': {
        backgroundColor: 'var(--destructive-50)',
        color: 'var(--destructive)',
      },
    }),
    indicatorSeparator: (provided: Record<string, unknown>) => ({
      ...provided,
      backgroundColor: 'var(--border)',
    }),
    dropdownIndicator: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--muted-foreground)',
      '&:hover': {
        color: 'var(--foreground)',
      },
    }),
    clearIndicator: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--muted-foreground)',
      '&:hover': {
        color: 'var(--destructive)',
      },
    }),
    noOptionsMessage: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--muted-foreground)',
    }),
    loadingMessage: (provided: Record<string, unknown>) => ({
      ...provided,
      color: 'var(--muted-foreground)',
    }),
  };

  return (
    <div className="relative w-full">
      <style jsx global>{`
        /* Animation for dropdown menu */
        @keyframes selectMenuFadeIn {
          from {
            opacity: 0;
            transform: translateY(-4px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* Styling for dropdown menu */
        .paginated-select__menu {
          animation: selectMenuFadeIn 150ms ease;
          background-color: var(--background) !important;
          border: 1px solid var(--border) !important;
        }

        /* Focus state styling */
        .paginated-select__control--is-focused {
          border-color: var(--primary) !important;
          box-shadow: 0 0 0 2px var(--primary-50) !important;
        }

        /* Improve option hover state */
        .paginated-select__option:hover {
          background-color: var(--accent) !important;
          cursor: pointer;
        }

        /* Selected option styling */
        .paginated-select__option--is-selected {
          background-color: var(--primary) !important;
          color: white !important;
        }

        /* Ensure all options have solid background */
        .paginated-select__option {
          background-color: var(--background) !important;
          opacity: 1 !important;
          color: var(--foreground) !important;
          padding: 10px 12px !important;
          border-radius: 4px !important;
        }

        /* Fix for menu list container */
        .paginated-select__menu-list {
          background-color: var(--background) !important;
          color: var(--foreground) !important;
          padding: 8px !important;
          max-height: 300px !important;
        }

        /* Fix for dark mode */
        html.dark .paginated-select__menu,
        html.dark .paginated-select__menu-list {
          background-color: hsl(var(--card)) !important;
          border-color: hsl(var(--border)) !important;
        }

        html.dark .paginated-select__option {
          background-color: hsl(var(--card)) !important;
          color: hsl(var(--card-foreground)) !important;
        }

        html.dark .paginated-select__option--is-focused,
        html.dark .paginated-select__option:hover {
          background-color: hsl(var(--accent)) !important;
        }

        html.dark .paginated-select__option--is-selected {
          background-color: hsl(var(--primary)) !important;
          color: white !important;
        }

        /* Fix for light mode */
        html:not(.dark) .paginated-select__menu,
        html:not(.dark) .paginated-select__menu-list {
          background-color: white !important;
          border-color: #e2e8f0 !important;
        }

        html:not(.dark) .paginated-select__option {
          background-color: white !important;
          color: #1e293b !important;
        }

        html:not(.dark) .paginated-select__option--is-focused,
        html:not(.dark) .paginated-select__option:hover {
          background-color: #f1f5f9 !important;
        }
      `}</style>
      <AsyncPaginate<IOption, GroupBase<IOption>, { page: number }>
        loadOptions={loadOptions}
        value={value}
        onChange={onChange}
        isDisabled={isDisabled}
        placeholder={placeholder}
        additional={{ page: 1 }}
        debounceTimeout={300}
        isClearable
        styles={customStyles}
        className={cn(
          'w-full border-0',
          'focus:outline-none focus:ring-0',
          isDisabled && 'cursor-not-allowed opacity-70',
        )}
        classNamePrefix="paginated-select"
        loadingMessage={() => (
          <div className="flex items-center justify-center py-2 text-sm">
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            <span className="text-foreground">Loading options...</span>
          </div>
        )}
        noOptionsMessage={({ inputValue }) => (
          <div className="px-4 py-2 text-center text-sm text-muted-foreground">
            {inputValue
              ? `No results found for "${inputValue}"`
              : 'No options available'}
          </div>
        )}
        components={{
          IndicatorSeparator: () => null, // Remove the separator for cleaner UI
        }}
      />
    </div>
  );
}
