/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */
'use client';

import * as React from 'react';

import * as AccordionPrimitive from '@radix-ui/react-accordion';
import { ChevronDown } from 'lucide-react';

import { cn } from '@/lib/utils';

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * @file accordion.tsx
 * @description An accessible accordion component for toggling content visibility
 *
 * This component is built on top of Radix UI's Accordion primitive and provides
 * a styled, accessible accordion with animation effects. It can be used for FAQs,
 * collapsible sections, and other expandable content.
 *
 * The accordion consists of four main parts:
 * - Accordion: The root container
 * - AccordionItem: Individual accordion items
 * - AccordionTrigger: The clickable header that toggles content visibility
 * - AccordionContent: The content that is shown/hidden
 *
 * @example
 * <Accordion type="single" collapsible>
 *   <AccordionItem value="item-1">
 *     <AccordionTrigger>Is it accessible?</AccordionTrigger>
 *     <AccordionContent>Yes. It adheres to the WAI-ARIA design pattern.</AccordionContent>
 *   </AccordionItem>
 * </Accordion>
 */

/**
 * The root Accordion component
 *
 * This is the container for all accordion items. It manages the state of the accordion.
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/accordion#root}
 */
const Accordion = AccordionPrimitive.Root;

/**
 * Individual accordion item component
 *
 * Each AccordionItem represents a collapsible section with a trigger and content.
 * It must have a unique `value` prop to identify it.
 *
 * @example
 * <AccordionItem value="item-1" className="border-t">
 *   <AccordionTrigger>Section Title</AccordionTrigger>
 *   <AccordionContent>Section Content</AccordionContent>
 * </AccordionItem>
 */
const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn('border-b', className)}
    {...props}
  />
));
AccordionItem.displayName = 'AccordionItem';

/**
 * The clickable header component that toggles the accordion content
 *
 * This component renders a button that users can click to expand or collapse
 * the associated accordion content. It includes a chevron icon that rotates
 * to indicate the current state.
 *
 * @example
 * <AccordionTrigger>Click to expand</AccordionTrigger>
 *
 * @example
 * <AccordionTrigger className="text-lg font-bold">
 *   Custom styled trigger
 * </AccordionTrigger>
 */
const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        'flex flex-1 items-center justify-between py-4 text-left text-sm font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180',
        className,
      )}
      {...props}
    >
      {children}
      {/* Chevron icon that rotates when the accordion is open */}
      <ChevronDown className="h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200" />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

/**
 * The content component that is shown/hidden when the accordion is toggled
 *
 * This component contains the content that is revealed when the accordion is expanded.
 * It includes smooth animation effects for opening and closing.
 *
 * @example
 * <AccordionContent>
 *   <p>This content is revealed when the accordion is expanded.</p>
 * </AccordionContent>
 *
 * @example
 * <AccordionContent className="text-gray-600">
 *   Custom styled content
 * </AccordionContent>
 */
const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    // Animation classes for smooth opening and closing transitions
    className="overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
    {...props}
  >
    <div className={cn('pb-4 pt-0', className)}>{children}</div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
