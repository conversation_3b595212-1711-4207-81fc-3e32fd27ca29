/**
 * @file card.tsx
 * @description A versatile card component with header, content, and footer sections
 *
 * This component provides a styled card container with various sub-components for
 * structuring content. Cards are used to group related information and actions,
 * providing a clear visual hierarchy and separation from other content.
 *
 * The card consists of several sub-components:
 * - Card: The main container
 * - CardHeader: Container for the card's header content (typically title and description)
 * - CardTitle: The card's title
 * - CardDescription: A description or subtitle for the card
 * - CardContent: The main content area of the card
 * - CardFooter: Container for actions or additional information at the bottom of the card
 *
 * @example
 * <Card>
 *   <CardHeader>
 *     <CardTitle>Card Title</CardTitle>
 *     <CardDescription>Card Description</CardDescription>
 *   </CardHeader>
 *   <CardContent>
 *     <p>Card Content</p>
 *   </CardContent>
 *   <CardFooter>
 *     <Button>Action</Button>
 *   </CardFooter>
 * </Card>
 */
import * as React from 'react';

import { cn } from '@/lib/utils';

/**
 * The main Card component
 *
 * This is the container for all card content. It provides the card's outer styling,
 * including border, background, and shadow.
 *
 * @example
 * <Card>Card content goes here</Card>
 *
 * @example
 * <Card className="max-w-md mx-auto">
 *   Custom styled card
 * </Card>
 */
const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'rounded-xl border bg-card text-card-foreground shadow',
      className,
    )}
    {...props}
  />
));
Card.displayName = 'Card';

/**
 * The header section of the Card
 *
 * This component is used for the top section of a card, typically containing
 * the card's title and description. It provides appropriate spacing and layout.
 *
 * @example
 * <CardHeader>
 *   <CardTitle>Card Title</CardTitle>
 *   <CardDescription>Card Description</CardDescription>
 * </CardHeader>
 */
const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-col space-y-1.5 p-6', className)}
    {...props}
  />
));
CardHeader.displayName = 'CardHeader';

/**
 * The title component for the Card
 *
 * This component is used for the main heading of a card. It's typically placed
 * within the CardHeader component and provides appropriate typography styling.
 *
 * @example
 * <CardTitle>Card Title</CardTitle>
 *
 * @example
 * <CardTitle className="text-xl text-primary">
 *   Custom Styled Title
 * </CardTitle>
 */
const CardTitle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('font-semibold leading-none tracking-tight', className)}
    {...props}
  />
));
CardTitle.displayName = 'CardTitle';

/**
 * The description component for the Card
 *
 * This component is used for secondary text or a subtitle within a card.
 * It's typically placed within the CardHeader component, below the CardTitle,
 * and provides appropriate typography styling with muted color.
 *
 * @example
 * <CardDescription>This is a description of the card content.</CardDescription>
 *
 * @example
 * <CardDescription className="italic">
 *   Custom styled description
 * </CardDescription>
 */
const CardDescription = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));
CardDescription.displayName = 'CardDescription';

/**
 * The content component for the Card
 *
 * This component is used for the main content area of a card. It provides
 * appropriate padding and spacing, with no top padding to align properly
 * with the CardHeader component.
 *
 * @example
 * <CardContent>
 *   <p>This is the main content of the card.</p>
 * </CardContent>
 *
 * @example
 * <CardContent className="bg-muted rounded-md">
 *   <p>Custom styled content area</p>
 * </CardContent>
 */
const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />
));
CardContent.displayName = 'CardContent';

/**
 * The footer component for the Card
 *
 * This component is used for the bottom section of a card, typically containing
 * actions like buttons or additional information. It provides appropriate padding
 * and spacing, with no top padding to align properly with the CardContent component.
 *
 * By default, it uses a flex layout to align items horizontally.
 *
 * @example
 * <CardFooter>
 *   <Button>Save</Button>
 *   <Button variant="outline">Cancel</Button>
 * </CardFooter>
 *
 * @example
 * <CardFooter className="justify-between">
 *   <Button variant="ghost">Previous</Button>
 *   <Button>Next</Button>
 * </CardFooter>
 */
const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center p-6 pt-0', className)}
    {...props}
  />
));
CardFooter.displayName = 'CardFooter';

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
};
