/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */
'use client';

import * as React from 'react';

import * as SelectPrimitive from '@radix-ui/react-select';
import { Check, ChevronDown, ChevronUp } from 'lucide-react';

import { cn } from '@/lib/utils';

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * @file select.tsx
 * @description A customizable select component for choosing from a list of options
 *
 * This component is built on top of Radix UI's Select primitive and provides
 * a styled, accessible dropdown select with support for groups, labels, and
 * custom styling. It handles keyboard navigation, focus management, and
 * accessibility requirements.
 *
 * The select component consists of several sub-components:
 * - Select: The root container
 * - SelectTrigger: The button that opens the select dropdown
 * - SelectValue: The displayed value of the select
 * - SelectContent: The dropdown content container
 * - SelectItem: An individual select option
 * - SelectGroup: A group of select options
 * - SelectLabel: A label for a group of options
 * - SelectSeparator: A visual separator between items
 * - SelectScrollUpButton: Button to scroll up when content overflows
 * - SelectScrollDownButton: Button to scroll down when content overflows
 *
 * @example
 * <Select>
 *   <SelectTrigger className="w-[180px]">
 *     <SelectValue placeholder="Select a fruit" />
 *   </SelectTrigger>
 *   <SelectContent>
 *     <SelectGroup>
 *       <SelectLabel>Fruits</SelectLabel>
 *       <SelectItem value="apple">Apple</SelectItem>
 *       <SelectItem value="banana">Banana</SelectItem>
 *       <SelectItem value="orange">Orange</SelectItem>
 *     </SelectGroup>
 *   </SelectContent>
 * </Select>
 */

/**
 * The root Select component
 *
 * This is the container for all select components. It manages the state of the select.
 *
 * @example
 * <Select onValueChange={setValue} defaultValue="default">
 *   {/* Select components
 * </Select>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#root}
 */
const Select = SelectPrimitive.Root;

/**
 * A group of select options
 *
 * This component is used to group related select options together.
 * It's typically used with a SelectLabel to provide a heading for the group.
 *
 * @example
 * <SelectGroup>
 *   <SelectLabel>Fruits</SelectLabel>
 *   <SelectItem value="apple">Apple</SelectItem>
 *   <SelectItem value="banana">Banana</SelectItem>
 * </SelectGroup>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#group}
 */
const SelectGroup = SelectPrimitive.Group;

/**
 * The displayed value of the select
 *
 * This component displays the currently selected value in the select trigger.
 * If no value is selected, it displays the placeholder text.
 *
 * @example
 * <SelectTrigger>
 *   <SelectValue placeholder="Select an option" />
 * </SelectTrigger>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#value}
 */
const SelectValue = SelectPrimitive.Value;

/**
 * The button that opens the select dropdown
 *
 * This component renders a button that, when clicked, opens the select dropdown.
 * It displays the currently selected value and a chevron icon.
 *
 * @example
 * <SelectTrigger className="w-[200px]">
 *   <SelectValue placeholder="Select a language" />
 * </SelectTrigger>
 *
 * @example
 * <SelectTrigger disabled>
 *   <SelectValue placeholder="Disabled select" />
 * </SelectTrigger>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#trigger}
 */
const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      // Base styles
      'flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-border bg-background px-3 py-2 text-foreground',
      // Hover and focus styles
      'hover:bg-accent/50 focus:outline-none focus:ring-1 focus:ring-ring',
      // Disabled styles
      'disabled:cursor-not-allowed disabled:opacity-50',
      // Ensure text doesn't overflow
      '[&>span]:line-clamp-1',
      className,
    )}
    {...props}
  >
    {children}
    {/* Chevron down icon */}
    <SelectPrimitive.Icon asChild>
      <ChevronDown className="h-4 w-4 opacity-50" />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
));
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName;

/**
 * Button to scroll up when content overflows
 *
 * This component renders a button that appears at the top of the select dropdown
 * when there are more options above the current viewport. Clicking it scrolls
 * the content up.
 *
 * @example
 * <SelectContent>
 *   <SelectScrollUpButton />
 *   <SelectViewport>{/* Many items </SelectViewport>
 *   <SelectScrollDownButton />
 * </SelectContent>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#scrollupbutton}
 */
const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      'flex cursor-default items-center justify-center py-1',
      className,
    )}
    {...props}
  >
    <ChevronUp className="h-4 w-4" />
  </SelectPrimitive.ScrollUpButton>
));
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;

/**
 * Button to scroll down when content overflows
 *
 * This component renders a button that appears at the bottom of the select dropdown
 * when there are more options below the current viewport. Clicking it scrolls
 * the content down.
 *
 * @example
 * <SelectContent>
 *   <SelectScrollUpButton />
 *   <SelectViewport>{/* Many items </SelectViewport>
 *   <SelectScrollDownButton />
 * </SelectContent>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#scrolldownbutton}
 */
const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      'flex cursor-default items-center justify-center py-1',
      className,
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
));
SelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName;

/**
 * The dropdown content container for the select
 *
 * This component renders the dropdown content of the select, including the options,
 * scroll buttons, and viewport. It handles positioning, animations, and scrolling.
 *
 * @example
 * <SelectContent>
 *   <SelectItem value="option1">Option 1</SelectItem>
 *   <SelectItem value="option2">Option 2</SelectItem>
 * </SelectContent>
 *
 * @example
 * <SelectContent position="item-aligned">
 *   <SelectItem value="option1">Option 1</SelectItem>
 * </SelectContent>
 *
 * @param props - Component props
 * @param props.position - The positioning strategy ('popper' or 'item-aligned')
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#content}
 */
const SelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = 'popper', ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        // Base styles
        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-border bg-background text-foreground shadow-md',
        // Animation styles
        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
        // Positioning styles based on side
        'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
        // Additional positioning for popper
        position === 'popper' &&
          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',
        className,
      )}
      position={position}
      {...props}
    >
      {/* Button to scroll up when content overflows */}
      <SelectScrollUpButton />

      {/* Viewport containing the select options */}
      <SelectPrimitive.Viewport
        className={cn(
          'p-1',
          position === 'popper' &&
            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]',
        )}
      >
        {children}
      </SelectPrimitive.Viewport>

      {/* Button to scroll down when content overflows */}
      <SelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
));
SelectContent.displayName = SelectPrimitive.Content.displayName;

/**
 * A label for a group of select options
 *
 * This component renders a label for a group of select options. It's typically
 * used within a SelectGroup to provide a heading for the group.
 *
 * @example
 * <SelectGroup>
 *   <SelectLabel>Fruits</SelectLabel>
 *   <SelectItem value="apple">Apple</SelectItem>
 *   <SelectItem value="banana">Banana</SelectItem>
 * </SelectGroup>
 *
 * @example
 * <SelectLabel className="text-blue-500">Categories</SelectLabel>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#label}
 */
const SelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn('px-2 py-1.5 text-sm font-semibold', className)}
    {...props}
  />
));
SelectLabel.displayName = SelectPrimitive.Label.displayName;

/**
 * An individual select option
 *
 * This component renders an individual option in the select dropdown.
 * It displays a checkmark icon when the option is selected.
 *
 * @example
 * <SelectItem value="apple">Apple</SelectItem>
 *
 * @example
 * <SelectItem value="banana" disabled>Banana (Unavailable)</SelectItem>
 *
 * @example
 * <SelectItem value="orange" className="text-orange-500">
 *   Orange
 * </SelectItem>
 *
 * @param props - Component props
 * @param props.value - The value of the option (required)
 * @param props.disabled - Whether the option is disabled
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#item}
 */
const SelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      // Base styles
      'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none',
      // Focus styles
      'focus:bg-accent focus:text-accent-foreground',
      // Disabled styles
      'data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className,
    )}
    {...props}
  >
    {/* Checkmark indicator for selected item */}
    <span className="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <Check className="h-4 w-4" />
      </SelectPrimitive.ItemIndicator>
    </span>

    {/* Item text content */}
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
));
SelectItem.displayName = SelectPrimitive.Item.displayName;

/**
 * A visual separator between select items
 *
 * This component renders a horizontal line that can be used to separate groups
 * of select items visually.
 *
 * @example
 * <SelectContent>
 *   <SelectItem value="apple">Apple</SelectItem>
 *   <SelectItem value="banana">Banana</SelectItem>
 *   <SelectSeparator />
 *   <SelectItem value="orange">Orange</SelectItem>
 * </SelectContent>
 *
 * @example
 * <SelectSeparator className="bg-red-200" />
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/select#separator}
 */
const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn('-mx-1 my-1 h-px bg-muted', className)}
    {...props}
  />
));
SelectSeparator.displayName = SelectPrimitive.Separator.displayName;

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
};
