/**
 * @file input.tsx
 * @description A versatile input component for collecting user input
 *
 * This component provides a styled, accessible input element that can be used
 * for various types of user input. It supports all standard HTML input types
 * and attributes, with consistent styling that matches the design system.
 *
 * The input component is designed to work seamlessly with the Form components
 * and can be used for text, email, password, number, search, and other input types.
 *
 * Features:
 * - Consistent styling across all input types
 * - Support for disabled state
 * - Focus styles with keyboard navigation
 * - File input styling
 * - Placeholder styling
 * - Full width by default
 *
 * @example
 * // Basic text input
 * <Input type="text" placeholder="Enter your name" />
 *
 * @example
 * // Email input with custom styling
 * <Input
 *   type="email"
 *   placeholder="Enter your email"
 *   className="max-w-sm"
 * />
 *
 * @example
 * // Password input
 * <Input type="password" placeholder="Enter your password" />
 *
 * @example
 * // Number input
 * <Input type="number" min={1} max={100} defaultValue={50} />
 *
 * @example
 * // Disabled input
 * <Input disabled type="text" placeholder="Disabled input" />
 *
 * @example
 * // With react-hook-form
 * <FormField
 *   control={form.control}
 *   name="email"
 *   render={({ field }) => (
 *     <FormItem>
 *       <FormLabel>Email</FormLabel>
 *       <FormControl>
 *         <Input placeholder="<EMAIL>" {...field} />
 *       </FormControl>
 *       <FormMessage />
 *     </FormItem>
 *   )}
 * />
 */
import * as React from 'react';

import { cn } from '@/lib/utils';

/**
 * Input component for collecting user input
 *
 * This component renders a styled HTML input element with consistent design
 * and behavior. It supports all standard HTML input attributes and types.
 *
 * @example
 * // Basic usage
 * <Input placeholder="Type here..." />
 *
 * @example
 * // With type
 * <Input type="email" placeholder="Email address" />
 *
 * @example
 * // With event handler
 * <Input
 *   type="text"
 *   placeholder="Search..."
 *   onChange={(e) => setSearch(e.target.value)}
 * />
 *
 * @param props - Component props
 * @param props.className - Additional CSS classes to apply
 * @param props.type - The type of input (text, email, password, etc.)
 * @param props.ref - Forwarded ref to the underlying input element
 * @returns The rendered Input component
 */
const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(
  ({ className, type = 'text', ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          // Base styles
          'flex h-10 w-full rounded-md border border-border bg-background px-3 py-2 text-sm text-foreground',
          // Focus styles
          'ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
          // File input styles
          'file:border-0 file:bg-transparent file:text-sm file:font-medium',
          // Placeholder styles
          'placeholder:text-muted-foreground',
          // Disabled styles
          'disabled:cursor-not-allowed disabled:opacity-50',
          // Additional custom classes
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export { Input };
