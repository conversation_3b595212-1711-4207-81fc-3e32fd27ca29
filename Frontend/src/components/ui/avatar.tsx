/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */
'use client';

import * as React from 'react';

import * as AvatarPrimitive from '@radix-ui/react-avatar';

import { cn } from '@/lib/utils';

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * @file avatar.tsx
 * @description A visual representation of a user or entity with image support and fallback
 *
 * This component is built on top of Radix UI's Avatar primitive and provides
 * a styled, accessible avatar with support for images and fallback content.
 * It's commonly used in user interfaces to represent users, and can display
 * either an image or fallback content (like initials or an icon) when the image
 * is not available or loading.
 *
 * The avatar consists of three main parts:
 * - Avatar: The root container
 * - AvatarImage: The image to display
 * - AvatarFallback: The content to display when the image is not available
 *
 * @example
 * <Avatar>
 *   <AvatarImage src="https://example.com/avatar.jpg" alt="User's avatar" />
 *   <AvatarFallback>JD</AvatarFallback>
 * </Avatar>
 */

/**
 * The root Avatar component
 *
 * This is the container for the avatar image and fallback content.
 * It provides the overall shape and size of the avatar.
 *
 * @example
 * <Avatar className="h-12 w-12">
 *   Avatar content
 * </Avatar>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/avatar#root}
 */
const Avatar = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Root
    ref={ref}
    className={cn(
      'relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full',
      className,
    )}
    {...props}
  />
));
Avatar.displayName = AvatarPrimitive.Root.displayName;

/**
 * The image component for the avatar
 *
 * This component displays an image within the avatar.
 * If the image fails to load, the AvatarFallback will be displayed instead.
 *
 * @example
 * <AvatarImage
 *   src="/path/to/image.jpg"
 *   alt="User's profile picture"
 * />
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/avatar#image}
 */
const AvatarImage = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Image>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Image
    ref={ref}
    className={cn('aspect-square h-full w-full', className)}
    {...props}
  />
));
AvatarImage.displayName = AvatarPrimitive.Image.displayName;

/**
 * The fallback component for the avatar
 *
 * This component is displayed when the avatar image is not available or fails to load.
 * It typically contains initials, an icon, or any other fallback content.
 *
 * @example
 * <AvatarFallback>JD</AvatarFallback>
 *
 * @example
 * <AvatarFallback delayMs={600}>
 *   <UserIcon className="h-6 w-6" />
 * </AvatarFallback>
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/avatar#fallback}
 */
const AvatarFallback = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Fallback>,
  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>
>(({ className, ...props }, ref) => (
  <AvatarPrimitive.Fallback
    ref={ref}
    className={cn(
      'flex h-full w-full items-center justify-center rounded-full bg-muted',
      className,
    )}
    {...props}
  />
));
AvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;

export { Avatar, AvatarImage, AvatarFallback };
