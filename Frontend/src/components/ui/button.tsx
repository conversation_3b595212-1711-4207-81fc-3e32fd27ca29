/**
 * @file button.tsx
 * @description A versatile button component with multiple variants and sizes
 *
 * This component provides a customizable button with different visual styles,
 * sizes, and the ability to render as any element via the asChild prop.
 * It's built on top of Radix UI's Slot primitive for composition.
 */
import * as React from 'react';

import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

/**
 * Button variant and size configuration using class-variance-authority
 *
 * This defines all the visual variants and size options for the Button component
 * using Tailwind CSS classes.
 */
const buttonVariants = cva(
  // Base styles applied to all buttons
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
  {
    variants: {
      /**
       * Visual style variants
       */
      variant: {
        // Primary button - Used for primary actions
        default: 'bg-primary text-white shadow hover:bg-primary/90',
        // Destructive button - Used for destructive actions like delete
        destructive:
          'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
        // Outline button - Used for secondary actions
        outline:
          'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',
        // Secondary button - Alternative to primary
        secondary:
          'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
        // Ghost button - No background until hovered
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        // Link button - Appears as a link but maintains button semantics
        link: 'text-primary underline-offset-4 hover:underline',
        // Action buttons - Used for view, edit, delete actions in tables
        'action-primary': 'text-primary hover:bg-primary/10 border border-border',
        'action-success': 'text-success hover:bg-success/10 border border-border',
        'action-destructive': 'text-destructive hover:bg-destructive/10 border border-border',
        'action-info': 'text-info hover:bg-info/10 border border-border',
        'action-warning': 'text-warning hover:bg-warning/10 border border-border',
      },
      /**
       * Size variants
       */
      size: {
        // Default size
        default: 'h-9 px-4 py-2',
        // Small size
        sm: 'h-8 rounded-md px-3 text-xs',
        // Large size
        lg: 'h-10 rounded-md px-8',
        // Icon button (square)
        icon: 'h-9 w-9',
      },
    },
    // Default variant and size if not specified
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

/**
 * Props for the Button component
 *
 * @extends React.ButtonHTMLAttributes<HTMLButtonElement> - Inherits all standard button attributes
 * @extends VariantProps<typeof buttonVariants> - Adds variant and size props
 */
export interface IButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  /**
   * When true, the button will render its children directly without wrapping them
   * Useful for composition with other components like Link
   * @default false
   */
  asChild?: boolean;
  
  /**
   * When true, the button will be styled as an action button for tables
   * This is a convenience prop that applies specific styling for action buttons
   * @default false
   */
  isActionButton?: boolean;
  
  /**
   * The action type for the button, used with isActionButton
   * @default 'primary'
   */
  actionType?: 'primary' | 'success' | 'destructive' | 'info' | 'warning';
}

/**
 * Button component
 *
 * A versatile button component that can be styled with different variants and sizes.
 * It can also render as any element via the asChild prop.
 *
 * @example
 * // Default button
 * <Button>Click me</Button>
 *
 * @example
 * // Destructive button with large size
 * <Button variant="destructive" size="lg">Delete</Button>
 *
 * @example
 * // Using as a link with Next.js Link component
 * <Button asChild variant="link">
 *   <Link href="/about">About us</Link>
 * </Button>
 */
const Button = React.forwardRef<HTMLButtonElement, IButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    asChild = false, 
    isActionButton = false,
    actionType = 'primary',
    ...props 
  }, ref) => {
    // Use Slot from Radix UI when asChild is true to render children directly
    const Comp = asChild ? Slot : 'button';
    
    // If isActionButton is true, override variant with the action variant
    if (isActionButton) {
      // Create the action variant string and properly type it
      const actionVariant = `action-${actionType}` as
        | 'action-primary'
        | 'action-success'
        | 'action-destructive'
        | 'action-info'
        | 'action-warning';
      variant = actionVariant;
      size = size || 'sm';
    }
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
Button.displayName = 'Button';

export { Button, buttonVariants };
