/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */
'use client';

import * as React from 'react';
import {
  Controller,
  ControllerProps,
  FieldPath,
  FieldValues,
  FormProvider,
  useFormContext,
} from 'react-hook-form';

import * as LabelPrimitive from '@radix-ui/react-label';
import { Slot } from '@radix-ui/react-slot';

import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * @file form.tsx
 * @description A comprehensive form component built on top of react-hook-form
 *
 * This component provides a set of accessible, reusable form components that
 * integrate seamlessly with react-hook-form. It handles form state management,
 * validation, error messages, and accessibility.
 *
 * The form system consists of several components:
 * - Form: The root form provider that wraps the entire form
 * - FormField: Connects a form field to the form state
 * - FormItem: Container for a form field and its related components
 * - FormLabel: Label for a form field
 * - FormControl: Connects a form control to the form state
 * - FormDescription: Description text for a form field
 * - FormMessage: Error message for a form field
 *
 * This implementation follows best practices for form accessibility, including
 * proper labeling, error states, and ARIA attributes.
 *
 * @example
 * // Basic usage with react-hook-form
 * const form = useForm<FormValues>();
 *
 * return (
 *   <Form {...form}>
 *     <form onSubmit={form.handleSubmit(onSubmit)}>
 *       <FormField
 *         control={form.control}
 *         name="username"
 *         render={({ field }) => (
 *           <FormItem>
 *             <FormLabel>Username</FormLabel>
 *             <FormControl>
 *               <Input {...field} />
 *             </FormControl>
 *             <FormDescription>Enter your username.</FormDescription>
 *             <FormMessage />
 *           </FormItem>
 *         )}
 *       />
 *       <Button type="submit">Submit</Button>
 *     </form>
 *   </Form>
 * )
 */

/**
 * The root Form component
 *
 * This component is a direct re-export of react-hook-form's FormProvider.
 * It provides the form context to all child components.
 *
 * @example
 * const form = useForm();
 *
 * <Form {...form}>
 *   <form onSubmit={form.handleSubmit(onSubmit)}>
 *      Form fields
 *   </form>
 * </Form>
 */
const Form = FormProvider;

/**
 * Type definition for the FormField context
 *
 * This context holds the name of the current field, which is used by
 * other form components to access the field's state.
 */
type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  name: TName;
};

/**
 * Context for sharing field information between form components
 *
 * This context allows child components to access the current field's name
 * without prop drilling.
 */
const FormFieldContext = React.createContext<FormFieldContextValue>(
  {} as FormFieldContextValue,
);

/**
 * Connects a form field to react-hook-form
 *
 * This component wraps react-hook-form's Controller and provides the field's
 * name to child components through context.
 *
 * @example
 * <FormField
 *   control={form.control}
 *   name="email"
 *   render={({ field }) => (
 *     <FormItem>
 *       <FormLabel>Email</FormLabel>
 *       <FormControl>
 *         <Input {...field} type="email" />
 *       </FormControl>
 *       <FormMessage />
 *     </FormItem>
 *   )}
 * />
 *
 * @template TFieldValues - The type of the form values
 * @template TName - The type of the field name
 */
function FormField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({ ...props }: ControllerProps<TFieldValues, TName>) {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
}

/**
 * Hook for accessing form field state and metadata
 *
 * This hook provides access to the current field's state (error, touched, etc.)
 * and metadata (IDs for accessibility). It must be used within a FormField component.
 *
 * @returns An object containing field state and metadata
 * @throws Error if used outside of a FormField component
 */
const useFormField = () => {
  const fieldContext = React.useContext(FormFieldContext);
  const itemContext = React.useContext(FormItemContext);
  const { getFieldState, formState } = useFormContext();

  const fieldState = getFieldState(fieldContext.name, formState);

  if (!fieldContext) {
    throw new Error('useFormField should be used within <FormField>');
  }

  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  };
};

/**
 * Type definition for the FormItem context
 *
 * This context holds the unique ID for the current form item,
 * which is used for accessibility attributes.
 */
type FormItemContextValue = {
  id: string;
};

/**
 * Context for sharing form item information between components
 *
 * This context allows child components to access the current form item's ID
 * without prop drilling.
 */
const FormItemContext = React.createContext<FormItemContextValue>(
  {} as FormItemContextValue,
);

/**
 * Container component for a form field and its related components
 *
 * This component provides a container for a form field and its related components
 * (label, control, description, message). It generates a unique ID for the field
 * and provides it to child components through context.
 *
 * @example
 * <FormItem>
 *   <FormLabel>Username</FormLabel>
 *   <FormControl>
 *     <Input />
 *   </FormControl>
 *   <FormDescription>Enter your username.</FormDescription>
 *   <FormMessage />
 * </FormItem>
 */
const FormItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  // Generate a unique ID for this form item
  const id = React.useId();

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} className={cn('space-y-2', className)} {...props} />
    </FormItemContext.Provider>
  );
});
FormItem.displayName = 'FormItem';

/**
 * Label component for a form field
 *
 * This component renders a label for a form field. It automatically connects
 * to the field's ID and shows an error state when the field has an error.
 *
 * @example
 * <FormLabel>Username</FormLabel>
 *
 * @example
 * <FormLabel className="text-lg">Email Address</FormLabel>
 */
const FormLabel = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>
>(({ className, ...props }, ref) => {
  const { error, formItemId } = useFormField();

  return (
    <Label
      ref={ref}
      className={cn(error && 'text-destructive', className)}
      htmlFor={formItemId}
      {...props}
    />
  );
});
FormLabel.displayName = 'FormLabel';

/**
 * Control component for a form field
 *
 * This component connects a form control (like Input, Select, etc.) to the form field.
 * It adds the necessary accessibility attributes (ID, aria-describedby, aria-invalid)
 * based on the field's state.
 *
 * The Slot component allows it to pass props to its children without adding an extra DOM element.
 *
 * @example
 * <FormControl>
 *   <Input />
 * </FormControl>
 *
 * @example
 * <FormControl>
 *   <Select>
 *     <SelectTrigger>
 *       <SelectValue placeholder="Select an option" />
 *     </SelectTrigger>
 *     <SelectContent>
 *       <SelectItem value="option1">Option 1</SelectItem>
 *       <SelectItem value="option2">Option 2</SelectItem>
 *     </SelectContent>
 *   </Select>
 * </FormControl>
 */
const FormControl = React.forwardRef<
  React.ElementRef<typeof Slot>,
  React.ComponentPropsWithoutRef<typeof Slot>
>(({ ...props }, ref) => {
  const { error, formItemId, formDescriptionId, formMessageId } =
    useFormField();

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-describedby={
        !error
          ? `${formDescriptionId}`
          : `${formDescriptionId} ${formMessageId}`
      }
      aria-invalid={!!error}
      {...props}
    />
  );
});
FormControl.displayName = 'FormControl';

/**
 * Description component for a form field
 *
 * This component renders a description for a form field. It provides additional
 * information or instructions about the field. It automatically connects to the
 * field's ID for accessibility.
 *
 * @example
 * <FormDescription>
 *   Your username must be at least 4 characters long.
 * </FormDescription>
 *
 * @example
 * <FormDescription className="italic">
 *   This information will be publicly visible.
 * </FormDescription>
 */
const FormDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormField();

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn('text-[0.8rem] text-muted-foreground', className)}
      {...props}
    />
  );
});
FormDescription.displayName = 'FormDescription';

/**
 * Error message component for a form field
 *
 * This component renders an error message for a form field. It automatically
 * displays the error message from react-hook-form if the field has an error,
 * or it can display custom content.
 *
 * If there's no error and no children are provided, it renders nothing.
 *
 * @example
 * <FormMessage />  Displays the field's error message if there is one
 *
 * @example
 * <FormMessage>
 *   This field is required.  /* Custom error message
 * </FormMessage>
 *
 * @example
 * <FormMessage className="text-red-600">
 *   {/* Custom styled error message
 * </FormMessage>
 */
const FormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const { error, formMessageId } = useFormField();
  const body = error ? String(error?.message) : children;

  // If there's no error message and no children, render nothing
  if (!body) {
    return null;
  }

  return (
    <p
      ref={ref}
      id={formMessageId}
      className={cn('text-[0.8rem] font-medium text-destructive', className)}
      {...props}
    >
      {body}
    </p>
  );
});
FormMessage.displayName = 'FormMessage';

export {
  useFormField,
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormField,
};
