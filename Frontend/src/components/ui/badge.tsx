/**
 * @file badge.tsx
 * @description A small visual indicator component for statuses, labels, and categories
 *
 * This component provides a styled badge that can be used to display short pieces of
 * information like statuses, counts, or categories. It comes in multiple variants
 * for different visual styles and can be customized with additional classes.
 *
 * Badges are typically used to:
 * - Show status (active, pending, etc.)
 * - Display counts (notifications, items, etc.)
 * - Categorize content (tags, labels, etc.)
 * - Highlight new or important information
 *
 * @example
 * // Default badge
 * <Badge>New</Badge>
 *
 * @example
 * // Variant examples
 * <Badge variant="default">Default</Badge>
 * <Badge variant="secondary">Secondary</Badge>
 * <Badge variant="destructive">Destructive</Badge>
 * <Badge variant="outline">Outline</Badge>
 */
import * as React from 'react';

import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

/**
 * Badge variant configuration using class-variance-authority
 *
 * This defines the base styles and variants for the Badge component.
 * The base styles apply to all badges, while the variants provide different
 * visual styles for different use cases.
 */
const badgeVariants = cva(
  // Base styles applied to all badges
  'inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      /**
       * Visual style variants
       */
      variant: {
        /**
         * Primary badge - Used for primary indicators
         * Blue background with white text by default
         */
        default:
          'border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80',

        /**
         * Secondary badge - Used for secondary indicators
         * Gray background with dark text by default
         */
        secondary:
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',

        /**
         * Destructive badge - Used for error or warning indicators
         * Red background with white text by default
         */
        destructive:
          'border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80',

        /**
         * Outline badge - Used for subtle indicators
         * Transparent background with border and text color
         */
        outline: 'text-foreground',
      },
    },
    // Default variant if not specified
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Props for the Badge component
 *
 * @extends React.HTMLAttributes<HTMLDivElement> - Inherits all standard div attributes
 * @extends VariantProps<typeof badgeVariants> - Adds variant prop from badgeVariants
 */
export interface IBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

/**
 * Badge component
 *
 * A small visual indicator component for statuses, labels, and categories.
 * Renders as a div element with the appropriate styling based on the variant.
 *
 * @example
 * // Basic usage
 * <Badge>New</Badge>
 *
 * @example
 * // With custom styling
 * <Badge className="px-4 py-1">Custom Size</Badge>
 *
 * @example
 * // With different variants
 * <Badge variant="secondary">Secondary</Badge>
 * <Badge variant="destructive">Error</Badge>
 * <Badge variant="outline">Outline</Badge>
 *
 * @param props - Component props
 * @param props.className - Additional CSS classes to apply
 * @param props.variant - Visual style variant to use
 * @returns The rendered Badge component
 */
function Badge({ className, variant, ...props }: IBadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  );
}

export { Badge, badgeVariants };
