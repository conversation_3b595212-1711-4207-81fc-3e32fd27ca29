/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */
'use client';

import React from 'react';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RefreshCw } from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
} from '@/components/ui/dialog';

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

/**
 * @file network-error-modal.tsx
 * @description React component for network-error-modal
 */

interface NetworkErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRetry?: () => void;
  title?: string;
  description?: string;
}

export function NetworkErrorModal({
  isOpen,
  onClose,
  onRetry,
  title = 'Network Error',
  description = 'There was a problem connecting to the server. Please check your internet connection and try again.',
}: NetworkErrorModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <p className="text-sm text-muted-foreground">
            If the problem persists, please try refreshing the page or contact
            support.
          </p>
        </div>
        <DialogFooter className="flex flex-row items-center justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          {onRetry && (
            <Button onClick={onRetry} className="gap-1">
              <RefreshCw className="h-4 w-4" />
              Retry
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
