/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */
'use client';

import * as React from 'react';

import * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';

import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * @file alert-dialog.tsx
 * @description A modal dialog that interrupts the user with important content and requires a decision
 *
 * This component is built on top of Radix UI's AlertDialog primitive and provides
 * a styled, accessible alert dialog with animation effects. It's designed for
 * important interruptions that require user confirmation or acknowledgment.
 *
 * Unlike a regular Dialog, an AlertDialog is modal and requires a user action
 * to dismiss. It's ideal for confirmations, warnings, or critical information.
 *
 * @example
 * <AlertDialog>
 *   <AlertDialogTrigger>Delete Account</AlertDialogTrigger>
 *   <AlertDialogContent>
 *     <AlertDialogHeader>
 *       <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *       <AlertDialogDescription>
 *         This action cannot be undone. This will permanently delete your account.
 *       </AlertDialogDescription>
 *     </AlertDialogHeader>
 *     <AlertDialogFooter>
 *       <AlertDialogCancel>Cancel</AlertDialogCancel>
 *       <AlertDialogAction>Delete</AlertDialogAction>
 *     </AlertDialogFooter>
 *   </AlertDialogContent>
 * </AlertDialog>
 */

/**
 * The root AlertDialog component
 *
 * This is the container for all alert dialog components. It manages the state of the dialog.
 *
 * @see {@link https://www.radix-ui.com/primitives/docs/components/alert-dialog#root}
 */
const AlertDialog = AlertDialogPrimitive.Root;

/**
 * The button that triggers the alert dialog
 *
 * This component is typically a button that, when clicked, opens the alert dialog.
 *
 * @example
 * <AlertDialogTrigger>Open Alert</AlertDialogTrigger>
 *
 * @example
 * <AlertDialogTrigger asChild>
 *   <Button variant="destructive">Delete Account</Button>
 * </AlertDialogTrigger>
 */
const AlertDialogTrigger = AlertDialogPrimitive.Trigger;

/**
 * The portal component that renders the alert dialog in a portal
 *
 * This component renders its children in a portal, which is useful for
 * rendering content outside the DOM hierarchy of the parent component.
 */
const AlertDialogPortal = AlertDialogPrimitive.Portal;

/**
 * The overlay component that covers the page behind the alert dialog
 *
 * This component renders a semi-transparent overlay that covers the entire viewport
 * when the alert dialog is open. It helps focus attention on the dialog by dimming
 * the content behind it.
 */
const AlertDialogOverlay = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Overlay
    className={cn(
      'fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
      className,
    )}
    {...props}
    ref={ref}
  />
));
AlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName;

/**
 * The main content container for the alert dialog
 *
 * This component renders the alert dialog content with proper positioning and styling.
 * It includes animations for opening and closing the dialog.
 *
 * @example
 * <AlertDialogContent>
 *   <AlertDialogHeader>
 *     <AlertDialogTitle>Are you sure?</AlertDialogTitle>
 *     <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
 *   </AlertDialogHeader>
 *   <AlertDialogFooter>
 *     <AlertDialogCancel>Cancel</AlertDialogCancel>
 *     <AlertDialogAction>Continue</AlertDialogAction>
 *   </AlertDialogFooter>
 * </AlertDialogContent>
 */
const AlertDialogContent = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>
>(({ className, ...props }, ref) => (
  <AlertDialogPortal>
    <AlertDialogOverlay />
    <AlertDialogPrimitive.Content
      ref={ref}
      className={cn(
        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',
        className,
      )}
      {...props}
    />
  </AlertDialogPortal>
));
AlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName;

/**
 * A container for the alert dialog header content
 *
 * This component provides consistent styling for the header section of the alert dialog.
 * It typically contains the title and description.
 *
 * @example
 * <AlertDialogHeader>
 *   <AlertDialogTitle>Delete Account</AlertDialogTitle>
 *   <AlertDialogDescription>
 *     This will permanently delete your account and all associated data.
 *   </AlertDialogDescription>
 * </AlertDialogHeader>
 */
const AlertDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col space-y-2 text-center sm:text-left',
      className,
    )}
    {...props}
  />
);
AlertDialogHeader.displayName = 'AlertDialogHeader';

/**
 * A container for the alert dialog footer content
 *
 * This component provides consistent styling for the footer section of the alert dialog.
 * It typically contains the action and cancel buttons.
 *
 * On mobile, it stacks the buttons vertically with the primary action on top.
 * On desktop, it displays the buttons horizontally with the primary action on the right.
 *
 * @example
 * <AlertDialogFooter>
 *   <AlertDialogCancel>Cancel</AlertDialogCancel>
 *   <AlertDialogAction>Delete</AlertDialogAction>
 * </AlertDialogFooter>
 */
const AlertDialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',
      className,
    )}
    {...props}
  />
);
AlertDialogFooter.displayName = 'AlertDialogFooter';

/**
 * The title component for the alert dialog
 *
 * This component renders the main heading of the alert dialog.
 * It should clearly state the purpose of the dialog or the decision being requested.
 *
 * @example
 * <AlertDialogTitle>Delete Account</AlertDialogTitle>
 *
 * @example
 * <AlertDialogTitle className="text-red-500">Warning</AlertDialogTitle>
 */
const AlertDialogTitle = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Title
    ref={ref}
    className={cn('text-lg font-semibold', className)}
    {...props}
  />
));
AlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName;

/**
 * The description component for the alert dialog
 *
 * This component renders additional information about the alert dialog's purpose.
 * It should provide context and explain the consequences of the user's decision.
 *
 * @example
 * <AlertDialogDescription>
 *   This will permanently delete your account and all of your data.
 *   This action cannot be undone.
 * </AlertDialogDescription>
 */
const AlertDialogDescription = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Description
    ref={ref}
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));
AlertDialogDescription.displayName =
  AlertDialogPrimitive.Description.displayName;

/**
 * The primary action button for the alert dialog
 *
 * This component renders a button that represents the primary action in the alert dialog.
 * It's typically used for confirming the action described in the dialog.
 *
 * By default, it uses the primary button style from the Button component.
 *
 * @example
 * <AlertDialogAction>Delete</AlertDialogAction>
 *
 * @example
 * <AlertDialogAction className="bg-red-500 hover:bg-red-600">
 *   Permanently Delete
 * </AlertDialogAction>
 */
const AlertDialogAction = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Action>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Action
    ref={ref}
    className={cn(buttonVariants(), className)}
    {...props}
  />
));
AlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName;

/**
 * The cancel button for the alert dialog
 *
 * This component renders a button that allows users to dismiss the alert dialog
 * without taking the primary action. It's typically used for canceling or declining
 * the action described in the dialog.
 *
 * By default, it uses the outline button style from the Button component.
 *
 * @example
 * <AlertDialogCancel>Cancel</AlertDialogCancel>
 *
 * @example
 * <AlertDialogCancel className="text-gray-500">
 *   Not Now
 * </AlertDialogCancel>
 */
const AlertDialogCancel = React.forwardRef<
  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,
  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>
>(({ className, ...props }, ref) => (
  <AlertDialogPrimitive.Cancel
    ref={ref}
    className={cn(
      buttonVariants({ variant: 'outline' }),
      'mt-2 sm:mt-0', // Adds margin on mobile, removes it on desktop
      className,
    )}
    {...props}
  />
));
AlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName;

export {
  AlertDialog,
  AlertDialogPortal,
  AlertDialogOverlay,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
};
