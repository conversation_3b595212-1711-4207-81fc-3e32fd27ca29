/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */
'use client';

import { useState } from 'react';

import {
  AlertTriangle,
  Award,
  BarChart,
  ChevronDown,
  ChevronUp,
  Clock,
  Cpu,
  Database,
  Info,
  Zap,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Progress } from '@/components/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

/**
 * @file index.tsx
 * @description React component for ExecutionMetrics
 */

export interface ExecutionMetric {
  value: number;
  unit: string;
  percentile?: number;
  ranking?: string; // 'excellent', 'good', 'average', 'poor'
}

export interface ExecutionMetricsProps {
  runtime: ExecutionMetric;
  memory: ExecutionMetric;
  language: string;
  showComparison?: boolean;
  showOptimizationTips?: boolean;
  className?: string;
}

export default function ExecutionMetrics({
  runtime,
  memory,
  language,
  showComparison = true,
  showOptimizationTips = true,
  className = '',
}: ExecutionMetricsProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Get color based on percentile
  const getPercentileColor = (percentile?: number) => {
    if (!percentile) return 'text-muted-foreground';
    if (percentile <= 25) return 'text-green-500';
    if (percentile <= 50) return 'text-blue-500';
    if (percentile <= 75) return 'text-yellow-500';
    return 'text-red-500';
  };

  // Get ranking badge
  const getRankingBadge = (ranking?: string) => {
    if (!ranking) return null;

    const variants = {
      excellent:
        'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400',
      good: 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400',
      average:
        'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400',
      poor: 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400',
    };

    return (
      <Badge
        variant="outline"
        className={cn(
          'ml-2 capitalize',
          variants[ranking as keyof typeof variants] || 'bg-gray-100',
        )}
      >
        {ranking}
      </Badge>
    );
  };

  // Get optimization tips based on metrics
  const getOptimizationTips = () => {
    const tips = [];

    // Runtime optimization tips
    if (runtime.percentile && runtime.percentile > 75) {
      if (language === 'python') {
        tips.push({
          title: 'Consider using more efficient data structures',
          description:
            'Replace lists with sets or dictionaries for lookups, or use collections.Counter for counting elements.',
          icon: <Zap className="h-4 w-4 text-blue-500" />,
        });
        tips.push({
          title: 'Avoid unnecessary list operations',
          description:
            'Operations like slicing or copying large lists can be expensive. Consider using iterators or generators.',
          icon: <Cpu className="h-4 w-4 text-blue-500" />,
        });
      } else if (language === 'javascript') {
        tips.push({
          title: 'Use appropriate array methods',
          description:
            'Methods like map(), filter(), and reduce() can be more efficient than for loops for certain operations.',
          icon: <Zap className="h-4 w-4 text-blue-500" />,
        });
        tips.push({
          title: 'Consider algorithmic improvements',
          description:
            'Your solution might benefit from a different algorithm. Look for O(n log n) or O(n) alternatives.',
          icon: <Cpu className="h-4 w-4 text-blue-500" />,
        });
      } else if (language === 'java') {
        tips.push({
          title: 'Use appropriate collections',
          description:
            'Choose the right collection type (ArrayList, LinkedList, HashSet, etc.) based on your operations.',
          icon: <Zap className="h-4 w-4 text-blue-500" />,
        });
        tips.push({
          title: 'Avoid unnecessary object creation',
          description:
            'Creating objects inside loops can be expensive due to garbage collection overhead.',
          icon: <Cpu className="h-4 w-4 text-blue-500" />,
        });
      } else {
        tips.push({
          title: 'Optimize your algorithm',
          description:
            'Your solution might benefit from a more efficient algorithm or data structure.',
          icon: <Zap className="h-4 w-4 text-blue-500" />,
        });
      }
    }

    // Memory optimization tips
    if (memory.percentile && memory.percentile > 75) {
      if (language === 'python') {
        tips.push({
          title: 'Reduce memory usage',
          description:
            'Consider using generators instead of lists, or use array.array for numeric data.',
          icon: <Database className="h-4 w-4 text-purple-500" />,
        });
      } else if (language === 'javascript') {
        tips.push({
          title: 'Minimize object creation',
          description:
            'Reuse objects when possible instead of creating new ones, especially in loops.',
          icon: <Database className="h-4 w-4 text-purple-500" />,
        });
      } else if (language === 'java') {
        tips.push({
          title: 'Use primitive types when possible',
          description:
            'Prefer int over Integer, boolean over Boolean, etc. to reduce memory overhead.',
          icon: <Database className="h-4 w-4 text-purple-500" />,
        });
      } else {
        tips.push({
          title: 'Reduce memory allocation',
          description:
            'Look for ways to reduce temporary object creation or use more memory-efficient data structures.',
          icon: <Database className="h-4 w-4 text-purple-500" />,
        });
      }
    }

    // General tips
    if (tips.length === 0) {
      tips.push({
        title: 'Great job!',
        description:
          'Your solution is already performing well. Keep up the good work!',
        icon: <Award className="h-4 w-4 text-yellow-500" />,
      });
    }

    return tips;
  };

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className={cn(
        'w-full rounded-md border bg-card p-4 shadow-sm',
        className,
      )}
    >
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Execution Metrics</h3>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
      </div>

      <div className="mt-2 grid grid-cols-1 gap-4 sm:grid-cols-2">
        {/* Runtime Metric */}
        <div className="flex items-center space-x-4">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
            <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1">
            <div className="flex items-center">
              <h4 className="font-medium">Runtime</h4>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">
                      Time taken to execute your code on the server.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {getRankingBadge(runtime.ranking)}
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold">{runtime.value}</span>
              <span className="ml-1 text-sm text-muted-foreground">
                {runtime.unit}
              </span>
              {runtime.percentile && (
                <span
                  className={cn(
                    'ml-2 text-sm',
                    getPercentileColor(runtime.percentile),
                  )}
                >
                  (Beats {100 - runtime.percentile}%)
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Memory Metric */}
        <div className="flex items-center space-x-4">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/30">
            <Database className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div className="flex-1">
            <div className="flex items-center">
              <h4 className="font-medium">Memory</h4>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">
                      Memory used by your code during execution.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {getRankingBadge(memory.ranking)}
            </div>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold">{memory.value}</span>
              <span className="ml-1 text-sm text-muted-foreground">
                {memory.unit}
              </span>
              {memory.percentile && (
                <span
                  className={cn(
                    'ml-2 text-sm',
                    getPercentileColor(memory.percentile),
                  )}
                >
                  (Beats {100 - memory.percentile}%)
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      <CollapsibleContent className="mt-4 space-y-4">
        {/* Performance Comparison */}
        {showComparison && (
          <div className="space-y-3 rounded-md border bg-card/50 p-4">
            <h4 className="flex items-center font-medium">
              <BarChart className="mr-2 h-4 w-4 text-muted-foreground" />
              Performance Comparison
            </h4>

            <div className="space-y-3">
              {/* Runtime Comparison */}
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span>Runtime (faster is better)</span>
                  {runtime.percentile && (
                    <span className={getPercentileColor(runtime.percentile)}>
                      {100 - runtime.percentile}th percentile
                    </span>
                  )}
                </div>
                <div className="relative h-2 overflow-hidden rounded-full bg-muted">
                  {runtime.percentile && (
                    <>
                      <div
                        className="to-red-500 absolute h-full bg-gradient-to-r from-green-500 via-yellow-500"
                        style={{ width: '100%' }}
                      />
                      <div
                        className="absolute h-full w-1 bg-black dark:bg-white"
                        style={{ left: `${runtime.percentile}%` }}
                      />
                    </>
                  )}
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Fastest</span>
                  <span>Median</span>
                  <span>Slowest</span>
                </div>
              </div>

              {/* Memory Comparison */}
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span>Memory (less is better)</span>
                  {memory.percentile && (
                    <span className={getPercentileColor(memory.percentile)}>
                      {100 - memory.percentile}th percentile
                    </span>
                  )}
                </div>
                <div className="relative h-2 overflow-hidden rounded-full bg-muted">
                  {memory.percentile && (
                    <>
                      <div
                        className="to-red-500 absolute h-full bg-gradient-to-r from-green-500 via-yellow-500"
                        style={{ width: '100%' }}
                      />
                      <div
                        className="absolute h-full w-1 bg-black dark:bg-white"
                        style={{ left: `${memory.percentile}%` }}
                      />
                    </>
                  )}
                </div>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Least</span>
                  <span>Median</span>
                  <span>Most</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Optimization Tips */}
        {showOptimizationTips && (
          <div className="space-y-3 rounded-md border bg-card/50 p-4">
            <h4 className="flex items-center font-medium">
              <Zap className="mr-2 h-4 w-4 text-muted-foreground" />
              Optimization Tips
            </h4>

            <div className="space-y-3">
              {getOptimizationTips().map((tip, index) => (
                <div key={index} className="rounded-md border bg-card p-3">
                  <div className="flex items-center">
                    {tip.icon}
                    <h5 className="ml-2 font-medium">{tip.title}</h5>
                  </div>
                  <p className="mt-1 text-sm text-muted-foreground">
                    {tip.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Language-specific notes */}
        <div className="flex items-start rounded-md border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-900/50 dark:bg-yellow-900/20">
          <AlertTriangle className="mr-2 h-4 w-4 flex-shrink-0 text-yellow-600 dark:text-yellow-400" />
          <div className="text-sm text-yellow-800 dark:text-yellow-300">
            <p>
              <strong>Note:</strong> Performance metrics can vary between runs
              due to server load and other factors. Use these metrics as a
              general guide rather than absolute values.
            </p>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}
