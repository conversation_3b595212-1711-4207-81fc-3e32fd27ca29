/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */
'use client';

import dynamic from 'next/dynamic';

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

/**
 * @file ClientAchievementManager.tsx
 * @description React component for ClientAchievementManager
 */

// Dynamically import the AchievementNotificationManager to avoid SSR issues
const AchievementNotificationManager = dynamic(
  () => import('@/components/AchievementNotificationManager'),
  { ssr: false },
);

export default function ClientAchievementManager() {
  return <AchievementNotificationManager />;
}
