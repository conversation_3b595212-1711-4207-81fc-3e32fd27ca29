/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */
'use client';

import { useState } from 'react';

import {
  Award,
  Brain,
  Calendar,
  CheckCircle,
  Clock,
  Code,
  Cpu,
  Database,
  Lock,
  MessageCircle,
  Rocket,
  Share2,
  Trophy,
  Zap,
} from 'lucide-react';

import {
  achievementTierColors,
  IAchievement,
} from '@/app/coding-challenges/types/achievements';
import ShareButton from '@/components/ShareButton';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

/**
 * @file index.tsx
 * @description React component for AchievementBadge
 */

interface AchievementBadgeProps {
  achievement: IAchievement;
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  showDetails?: boolean;
  className?: string;
}

export default function AchievementBadge({
  achievement,
  size = 'md',
  showTooltip = true,
  showDetails = true,
  className = '',
}: AchievementBadgeProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Get icon based on achievement icon name
  const getIcon = () => {
    const iconSize =
      size === 'sm' ? 'h-4 w-4' : size === 'md' ? 'h-6 w-6' : 'h-8 w-8';
    const iconClass = `${iconSize} ${achievement.unlocked ? achievementTierColors[achievement.tier].icon : 'text-muted-foreground'}`;

    switch (achievement.icon) {
      case 'rocket':
        return <Rocket className={iconClass} />;
      case 'brain':
        return <Brain className={iconClass} />;
      case 'trophy':
        return <Trophy className={iconClass} />;
      case 'code':
        return <Code className={iconClass} />;
      case 'database':
        return <Database className={iconClass} />;
      case 'zap':
        return <Zap className={iconClass} />;
      case 'cpu':
        return <Cpu className={iconClass} />;
      case 'calendar':
        return <Calendar className={iconClass} />;
      case 'message-circle':
        return <MessageCircle className={iconClass} />;
      default:
        return <Award className={iconClass} />;
    }
  };

  // Get badge size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-10 w-10';
      case 'md':
        return 'h-16 w-16';
      case 'lg':
        return 'h-24 w-24';
      default:
        return 'h-16 w-16';
    }
  };

  // Get badge classes based on achievement tier and unlock status
  const getBadgeClasses = () => {
    const sizeClass = getSizeClasses();
    const baseClasses =
      'flex items-center justify-center rounded-full border-2';

    if (!achievement.unlocked) {
      return `${baseClasses} ${sizeClass} bg-muted/50 border-muted text-muted-foreground`;
    }

    const tierColors = achievementTierColors[achievement.tier];
    return `${baseClasses} ${sizeClass} ${tierColors.bg} ${tierColors.border}`;
  };

  // Render badge content
  const renderBadgeContent = () => {
    return (
      <div className={getBadgeClasses()}>
        {achievement.unlocked ? (
          getIcon()
        ) : (
          <Lock
            className={
              size === 'sm' ? 'h-4 w-4' : size === 'md' ? 'h-6 w-6' : 'h-8 w-8'
            }
          />
        )}
      </div>
    );
  };

  // Render tooltip content
  const renderTooltipContent = () => {
    return (
      <div className="space-y-2 p-1">
        <div className="flex items-center gap-2">
          <span className="font-semibold">{achievement.title}</span>
          <Badge
            variant="outline"
            className={cn(
              'capitalize',
              achievement.unlocked
                ? achievementTierColors[achievement.tier].bg
                : 'bg-muted',
              achievement.unlocked
                ? achievementTierColors[achievement.tier].text
                : 'text-muted-foreground',
            )}
          >
            {achievement.tier}
          </Badge>
        </div>
        <p className="text-sm">{achievement.description}</p>
        {achievement.progress && !achievement.unlocked && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span>Progress</span>
              <span>
                {achievement.progress.current}/{achievement.progress.target}
              </span>
            </div>
            <Progress
              value={achievement.progress.percentage}
              className="h-1.5"
            />
          </div>
        )}
        {achievement.unlocked && achievement.unlocked_at && (
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <CheckCircle className="h-3 w-3" />
            <span>
              Unlocked {new Date(achievement.unlocked_at).toLocaleDateString()}
            </span>
          </div>
        )}
        {showDetails && (
          <div className="pt-1 text-xs text-muted-foreground">
            <span>Click for details</span>
          </div>
        )}
      </div>
    );
  };

  // Render achievement details dialog
  const renderDetailsDialog = () => {
    return (
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span>{achievement.title}</span>
            <Badge
              variant="outline"
              className={cn(
                'capitalize',
                achievement.unlocked
                  ? achievementTierColors[achievement.tier].bg
                  : 'bg-muted',
                achievement.unlocked
                  ? achievementTierColors[achievement.tier].text
                  : 'text-muted-foreground',
              )}
            >
              {achievement.tier}
            </Badge>
          </DialogTitle>
          <DialogDescription>{achievement.description}</DialogDescription>
        </DialogHeader>

        <div className="flex flex-col items-center space-y-4 py-4">
          <div className={cn(getBadgeClasses(), 'h-24 w-24')}>
            {achievement.unlocked ? getIcon() : <Lock className="h-8 w-8" />}
          </div>

          <div className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            <span className="font-medium">{achievement.points} points</span>
          </div>

          {achievement.progress && !achievement.unlocked && (
            <div className="w-full space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>
                  {achievement.progress.current}/{achievement.progress.target}
                </span>
              </div>
              <Progress
                value={achievement.progress.percentage}
                className="h-2"
              />
              <p className="text-center text-sm text-muted-foreground">
                {achievement.progress.percentage}% complete
              </p>
            </div>
          )}

          {achievement.unlocked && achievement.unlocked_at && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>
                Unlocked on{' '}
                {new Date(achievement.unlocked_at).toLocaleDateString()}
              </span>
            </div>
          )}

          {!achievement.unlocked && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>Keep working to unlock this achievement!</span>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsDialogOpen(false)}
          >
            Close
          </Button>

          {achievement.unlocked && (
            <ShareButton
              url={`/profile/achievements/${achievement.id}`}
              title={`I earned the "${achievement.title}" achievement!`}
              description={achievement.description}
              variant="default"
              size="sm"
              showText={true}
            />
          )}
        </div>
      </DialogContent>
    );
  };

  // If showing tooltip, wrap badge in tooltip
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {showDetails ? (
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <button className={cn('focus:outline-none', className)}>
                    {renderBadgeContent()}
                  </button>
                </DialogTrigger>
                {renderDetailsDialog()}
              </Dialog>
            ) : (
              <div className={className}>{renderBadgeContent()}</div>
            )}
          </TooltipTrigger>
          <TooltipContent side="top" align="center">
            {renderTooltipContent()}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Otherwise, just show badge with optional details dialog
  return showDetails ? (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <button className={cn('focus:outline-none', className)}>
          {renderBadgeContent()}
        </button>
      </DialogTrigger>
      {renderDetailsDialog()}
    </Dialog>
  ) : (
    <div className={className}>{renderBadgeContent()}</div>
  );
}
