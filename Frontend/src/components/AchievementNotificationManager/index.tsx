/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */
'use client';

import { useEffect, useState } from 'react';

import AchievementNotification from '@/components/AchievementNotification';
import { useAchievementNotifications } from '@/hooks/useAchievements';

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

/**
 * @file index.tsx
 * @description React component for AchievementNotificationManager
 */

export default function AchievementNotificationManager() {
  const { notifications, markAsRead } = useAchievementNotifications();
  const [currentNotification, setCurrentNotification] = useState<string | null>(
    null,
  );
  const [queue, setQueue] = useState<string[]>([]);

  // Process notifications and build queue
  useEffect(() => {
    // Filter for unread notifications
    const unreadNotifications = notifications
      .filter((notification) => !notification.read)
      .map((notification) => notification.id);

    // Update queue with new unread notifications
    setQueue((prevQueue) => {
      const newQueue = [...prevQueue];

      // Add any new notifications that aren't already in the queue
      unreadNotifications.forEach((id) => {
        if (!newQueue.includes(id) && id !== currentNotification) {
          newQueue.push(id);
        }
      });

      return newQueue;
    });
  }, [notifications, currentNotification]);

  // Process the notification queue
  useEffect(() => {
    // If there's no current notification and the queue isn't empty, show the next one
    if (!currentNotification && queue.length > 0) {
      const nextNotification = queue[0];
      setCurrentNotification(nextNotification);
      setQueue((prevQueue) => prevQueue.slice(1)); // Remove from queue
    }
  }, [currentNotification, queue]);

  // Handle notification close
  const handleClose = () => {
    if (currentNotification) {
      markAsRead(currentNotification);
      setCurrentNotification(null);
    }
  };

  // Find the current notification object
  const notificationToShow = currentNotification
    ? notifications.find((n) => n.id === currentNotification)
    : null;

  if (!notificationToShow) {
    return null;
  }

  return (
    <AchievementNotification
      achievement={notificationToShow.achievement}
      onClose={handleClose}
      autoClose={true}
      autoCloseDelay={8000}
    />
  );
}
