/**
 * @file index.tsx
 * @description React component for ShareButton
 */
'use client';

import { useState } from 'react';

import {
  Check,
  Co<PERSON>,
  Facebook,
  Linkedin,
  Link as LinkIcon,
  Mail,
  Share2,
  Twitter,
} from 'lucide-react';
import { toast } from 'sonner';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

/**
 * @file index.tsx
 * @description React component for ShareButton
 */

interface ShareButtonProps {
  url: string;
  title: string;
  description?: string;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showText?: boolean;
}

export default function ShareButton({
  url,
  title,
  description = '',
  className = '',
  variant = 'outline',
  size = 'default',
  showText = true,
}: ShareButtonProps) {
  const [copied, setCopied] = useState(false);

  // Ensure we have the full URL
  const fullUrl = url.startsWith('http')
    ? url
    : `${window.location.origin}${url}`;

  // Handle copy to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(fullUrl);
      setCopied(true);
      toast.success('Link copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error('Failed to copy link');
      console.error('Failed to copy:', err);
    }
  };

  // Share via Twitter
  const shareOnTwitter = () => {
    const text = `${title}${description ? ` - ${description}` : ''}`;
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(fullUrl)}`;
    window.open(twitterUrl, '_blank');
  };

  // Share via Facebook
  const shareOnFacebook = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(fullUrl)}`;
    window.open(facebookUrl, '_blank');
  };

  // Share via LinkedIn
  const shareOnLinkedIn = () => {
    const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent(title)}${description ? `&summary=${encodeURIComponent(description)}` : ''}`;
    window.open(linkedInUrl, '_blank');
  };

  // Share via Email
  const shareViaEmail = () => {
    const subject = encodeURIComponent(title);
    const body = encodeURIComponent(`${description || title}\n\n${fullUrl}`);
    window.location.href = `mailto:?subject=${subject}&body=${body}`;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn('gap-2', className)}
        >
          <Share2 className="h-4 w-4" />
          {showText && <span>Share</span>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem onClick={copyToClipboard} className="cursor-pointer">
          {copied ? (
            <Check className="mr-2 h-4 w-4 text-green-500" />
          ) : (
            <Copy className="mr-2 h-4 w-4" />
          )}
          <span>Copy Link</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={shareOnTwitter} className="cursor-pointer">
          <Twitter className="mr-2 h-4 w-4 text-[#1DA1F2]" />
          <span>Twitter</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={shareOnFacebook} className="cursor-pointer">
          <Facebook className="mr-2 h-4 w-4 text-[#4267B2]" />
          <span>Facebook</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={shareOnLinkedIn} className="cursor-pointer">
          <Linkedin className="mr-2 h-4 w-4 text-[#0077B5]" />
          <span>LinkedIn</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={shareViaEmail} className="cursor-pointer">
          <Mail className="mr-2 h-4 w-4" />
          <span>Email</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
