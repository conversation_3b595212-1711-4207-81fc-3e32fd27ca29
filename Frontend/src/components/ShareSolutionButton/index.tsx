/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */
'use client';

import { useState } from 'react';

import { Check, Copy, QrCode, Share2 } from 'lucide-react';
import { toast } from 'sonner';

import ShareButton from '@/components/ShareButton';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

/**
 * @file index.tsx
 * @description React component for ShareSolutionButton
 */

interface ShareSolutionButtonProps {
  challengeId: string;
  submissionId: string;
  language: string;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showText?: boolean;
}

export default function ShareSolutionButton({
  challengeId,
  submissionId,
  language,
  className = '',
  variant = 'outline',
  size = 'default',
  showText = true,
}: ShareSolutionButtonProps) {
  const [open, setOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const [includeCode, setIncludeCode] = useState(true);
  const [includeStats, setIncludeStats] = useState(true);

  // Generate the shareable URL
  const generateShareableUrl = () => {
    const baseUrl = `${window.location.origin}/coding-challenges/${challengeId}`;
    const params = new URLSearchParams();
    params.set('submission', submissionId);
    params.set('language', language);

    if (includeCode) {
      params.set('code', 'true');
    }

    if (includeStats) {
      params.set('stats', 'true');
    }

    return `${baseUrl}?${params.toString()}`;
  };

  // Handle copy to clipboard
  const copyToClipboard = async () => {
    try {
      const url = generateShareableUrl();
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success('Solution link copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error('Failed to copy link');
      console.error('Failed to copy:', err);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn('gap-2', className)}
        >
          <Share2 className="h-4 w-4" />
          {showText && <span>Share Solution</span>}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share Your Solution</DialogTitle>
          <DialogDescription>
            Create a shareable link to your solution that you can share with
            others.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="flex items-center justify-between space-x-2">
            <Label htmlFor="include-code" className="flex flex-col space-y-1">
              <span>Include Code</span>
              <span className="text-xs text-muted-foreground">
                Allow others to view your solution code
              </span>
            </Label>
            <Switch
              id="include-code"
              checked={includeCode}
              onCheckedChange={setIncludeCode}
            />
          </div>
          <div className="flex items-center justify-between space-x-2">
            <Label htmlFor="include-stats" className="flex flex-col space-y-1">
              <span>Include Stats</span>
              <span className="text-xs text-muted-foreground">
                Show runtime and memory usage statistics
              </span>
            </Label>
            <Switch
              id="include-stats"
              checked={includeStats}
              onCheckedChange={setIncludeStats}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="share-link">Shareable Link</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="share-link"
                value={generateShareableUrl()}
                readOnly
                className="flex-1"
              />
              <Button
                size="sm"
                variant="outline"
                onClick={copyToClipboard}
                className="flex-shrink-0"
              >
                {copied ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>
        <DialogFooter className="flex flex-col sm:flex-row sm:justify-between sm:space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setOpen(false)}
            className="mt-2 sm:mt-0"
          >
            Close
          </Button>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Generate QR code for the solution
                const url = encodeURIComponent(generateShareableUrl());
                window.open(
                  `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${url}`,
                  '_blank',
                );
              }}
            >
              <QrCode className="mr-2 h-4 w-4" />
              <span>QR Code</span>
            </Button>
            <ShareButton
              url={generateShareableUrl()}
              title="Check out my coding challenge solution!"
              description={`I solved this coding challenge in ${language}. Take a look!`}
              size="sm"
              showText={false}
            />
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
