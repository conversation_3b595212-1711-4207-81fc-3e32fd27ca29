/**
 * @file index.tsx
 * @description React component for SimpleWeeklyLeaderboard
 */
import React, { useEffect, useState } from 'react';
import { FaAward, FaMedal, FaTrophy, FaUniversity } from 'react-icons/fa';

import Image from 'next/image';
import Link from 'next/link';

import { Skeleton } from '@/components/ui/skeleton';
import { ctaLinks } from '@/constants';
// Types are imported through the hooks
import { usePublicLeaderboard, useTopColleges } from '@/hooks/usePublicData';
import {
  LeaderboardEntry,
  publicDataFallbacks,
} from '@/services/publicDataService';

// Fallback data is now handled in the publicDataService

// Medal icon component
function MedalIcon({ rank }: { rank: number }) {
  if (rank === 1) {
    return <FaTrophy className="h-8 w-8 text-yellow-500" />;
  } else if (rank === 2) {
    return <FaMedal className="h-8 w-8 text-gray-400" />;
  } else if (rank === 3) {
    return <FaAward className="h-8 w-8 text-amber-600" />;
  }
  return null;
}

export default function SimpleWeeklyLeaderboard() {
  const {
    data: leaderboardData,
    isLoading: isLoadingLeaderboard,
    isError: isErrorLeaderboard,
  } = usePublicLeaderboard(10);

  const {
    data: collegeData,
    isLoading: isLoadingColleges,
    isError: isErrorColleges,
  } = useTopColleges(10);

  const [error, setError] = useState<string | null>(null);
  const isLoading = isLoadingLeaderboard || isLoadingColleges;
  const isError = isErrorLeaderboard || isErrorColleges;

  // Set error message if there's an error from the hooks
  useEffect(() => {
    if (isError) {
      setError('Failed to load data');
    } else {
      setError(null);
    }
  }, [isError]);

  // Find college details for each leaderboard entry
  const getCollegeDetails = (
    entry: LeaderboardEntry,
  ): { name: string; logo?: string; location?: string } => {
    if (entry.college_id && collegeData) {
      const college = collegeData.find((c) => c.id === entry.college_id);
      if (college) {
        return {
          name: college.name,
          logo: college.logo_url,
          location: college.location,
        };
      }
    }

    // Use the college name from the entry if no match is found
    return {
      name: entry.college,
      logo: entry.college_logo || undefined,
      location: entry.college_location || undefined,
    };
  };

  // Make sure leaderboardData is an array
  const leaderboardEntries = Array.isArray(leaderboardData)
    ? leaderboardData
    : publicDataFallbacks.leaderboardData;

  // Top 3 users for podium
  const toppers = leaderboardEntries.slice(0, 3) ?? [];

  // Correct order: 2nd left, 1st center, 3rd right
  const podiumOrder = [1, 0, 2];

  // Render loading state
  if (isLoading) {
    return (
      <div className="relative overflow-hidden rounded-xl bg-card px-4 py-6 text-card-foreground dark:bg-[#1e293b] dark:text-white">
        {/* Header */}
        <div className="relative z-10 mb-6 text-center">
          <h2 className="text-2xl font-bold">Weekly Leaderboard</h2>
          <p className="mt-1 text-center text-sm text-muted-foreground dark:text-gray-300">
            Compete, climb, and claim your spot among the best engineers.
          </p>
        </div>

        {/* Loading podium */}
        <div className="mb-8 grid grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex flex-col items-center">
              <Skeleton className="mb-2 h-14 w-14 rounded-full" />
              <Skeleton className="mb-2 h-8 w-8" />
              <Skeleton className="h-24 w-full rounded-t-md" />
              <Skeleton className="mt-2 h-20 w-full rounded-md" />
            </div>
          ))}
        </div>

        {/* Loading additional entries */}
        <div className="relative z-10 space-y-2">
          {[1, 2].map((i) => (
            <div key={i} className="flex items-center rounded-lg p-2">
              <Skeleton className="mr-2 h-6 w-6 rounded-full" />
              <Skeleton className="mr-2 h-7 w-7 rounded-full" />
              <div className="flex-1">
                <Skeleton className="h-3 w-24" />
                <Skeleton className="mt-1 h-2 w-16" />
              </div>
              <Skeleton className="h-3 w-12" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="relative overflow-hidden rounded-xl bg-card px-4 py-6 text-card-foreground dark:bg-[#1e293b] dark:text-white">
      {/* Background decorative elements */}
      <div className="absolute inset-0 z-0 overflow-hidden opacity-10">
        <div className="absolute -left-20 top-20 h-64 w-64 rounded-full bg-primary blur-3xl"></div>
        <div className="absolute -right-20 bottom-20 h-64 w-64 rounded-full bg-primary2 blur-3xl"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 mb-6 text-center">
        <h2 className="text-2xl font-bold">Weekly Leaderboard</h2>
        <p className="mt-1 text-center text-sm text-muted-foreground dark:text-gray-300">
          Compete, climb, and claim your spot among the best engineers.
        </p>
        {error && (
          <p className="text-red-500 mt-2 text-sm">
            {error} - Showing sample data instead.
          </p>
        )}
      </div>

      {/* Improved Podium display */}
      {toppers?.length > 0 ? (
        <div className="relative mb-8 flex h-64 items-end justify-center gap-4">
          {podiumOrder.map((podiumIdx) => {
            const user = toppers[podiumIdx];

            // Determine colors based on rank
            const bgColor =
              user.rank === 1
                ? 'bg-yellow-500'
                : user.rank === 2
                  ? 'bg-gray-400'
                  : 'bg-amber-600';

            const textColor =
              user.rank === 1
                ? 'text-yellow-500 dark:text-yellow-300'
                : user.rank === 2
                  ? 'text-gray-500 dark:text-gray-300'
                  : 'text-amber-600 dark:text-amber-400';

            // Determine height based on rank - make 1st place tallest
            const podiumHeight =
              user.rank === 1 ? 'h-40' : user.rank === 2 ? 'h-32' : 'h-24';

            // Position elements based on rank
            const podiumPosition =
              user.rank === 1
                ? 'order-2'
                : user.rank === 2
                  ? 'order-1'
                  : 'order-3';

            // Adjust width based on rank
            const podiumWidth = user.rank === 1 ? 'w-28' : 'w-24';

            return (
              <div
                key={user.id}
                className={`flex flex-col items-center ${podiumPosition}`}
              >
                {/* Avatar - positioned above the podium */}
                <div className="relative mb-2">
                  <div className="h-14 w-14 overflow-hidden rounded-full border-2 border-gray-200/50 shadow-md dark:border-white/50">
                    <Image
                      src={user.avatar_url}
                      alt={user.name}
                      width={56}
                      height={56}
                      className="h-full w-full object-cover"
                      unoptimized={user.avatar_url.startsWith('http')}
                      onError={(e) => {
                        // Fallback to default avatar if image fails to load
                        (e.target as HTMLImageElement).src =
                          '/avatars/default-0.png';
                      }}
                    />
                  </div>

                  {/* Medal positioned on top of avatar */}
                  <div className="absolute -right-2 -top-2 rounded-full bg-white p-1 shadow-md dark:bg-gray-800">
                    <MedalIcon rank={user.rank} />
                  </div>
                </div>

                {/* Podium */}
                <div className={`relative ${podiumWidth}`}>
                  <div
                    className={`w-full ${podiumHeight} rounded-t-md ${bgColor} flex items-center justify-center shadow-md transition-all duration-300 hover:brightness-110`}
                  >
                    <span className="text-3xl font-bold text-white">
                      {user.rank}
                    </span>
                  </div>
                </div>

                {/* User info */}
                <div
                  className={`mt-2 ${podiumWidth} rounded-md bg-gray-100/90 p-2 text-center shadow-sm dark:bg-white/10`}
                >
                  <div className="truncate text-sm font-semibold text-gray-900 dark:text-white">
                    {user.name}
                  </div>
                  <div className={`text-sm font-medium ${textColor}`}>
                    {user.points.toLocaleString()} pts
                  </div>
                  <div className="flex items-center justify-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                    {getCollegeDetails(user).logo ? (
                      <Image
                        src={getCollegeDetails(user).logo!}
                        alt={getCollegeDetails(user).name}
                        width={12}
                        height={12}
                        className="rounded-full"
                        unoptimized
                      />
                    ) : (
                      <FaUniversity className="text-xs text-primary" />
                    )}
                    {getCollegeDetails(user).name}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <></>
      )}

      {/* Additional leaderboard entries */}
      <div className="relative z-10 space-y-2">
        {leaderboardEntries.slice(3, 5).map((user) => (
          <div
            key={user.id}
            className="flex items-center rounded-lg bg-gray-100/80 p-2 shadow-sm dark:bg-white/5"
          >
            <div className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-gray-200 text-xs font-bold text-gray-700 shadow-sm dark:bg-gray-800 dark:text-gray-300">
              {user.rank}
            </div>
            <div className="mr-2">
              <Image
                src={user.avatar_url}
                alt={user.name}
                width={28}
                height={28}
                className="h-7 w-7 rounded-full border border-gray-300 object-cover shadow-sm dark:border-gray-700"
                unoptimized={user.avatar_url.startsWith('http')}
                onError={(e) => {
                  // Fallback to default avatar if image fails to load
                  (e.target as HTMLImageElement).src = '/avatars/default-0.png';
                }}
              />
            </div>
            <div className="flex-1">
              <div className="text-xs font-medium text-gray-900 dark:text-white">
                {user.name}
              </div>
              <div className="flex items-center gap-1 text-[10px] text-gray-500 dark:text-gray-400">
                {getCollegeDetails(user).logo ? (
                  <Image
                    src={getCollegeDetails(user).logo!}
                    alt={getCollegeDetails(user).name}
                    width={10}
                    height={10}
                    className="rounded-full"
                    unoptimized
                  />
                ) : (
                  <FaUniversity className="text-[8px] text-primary" />
                )}
                {getCollegeDetails(user).name}
              </div>
            </div>
            <div className="text-right text-xs font-semibold text-gray-700 dark:text-gray-300">
              {user.points.toLocaleString()} pts
            </div>
          </div>
        ))}
      </div>

      {/* Call to action */}
      <div className="relative z-10 mt-4 text-center">
        <Link
          href={ctaLinks.battleZone.href}
          className="dark:text-primary-light text-sm font-medium text-primary transition-colors duration-300 hover:text-primary2 dark:hover:text-primary"
        >
          {ctaLinks.battleZone.name} →
        </Link>
      </div>
    </div>
  );
}
