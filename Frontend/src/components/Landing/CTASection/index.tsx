/**
 * @file index.tsx
 * @description React component for CTASection
 */
import React from 'react';
import { FaArrowRight } from 'react-icons/fa';

import Link from 'next/link';

import { motion } from 'framer-motion';

import { companyInfo, ctaLinks } from '@/constants';

const CTASection: React.FC = () => (
  <section className="relative z-10 bg-background py-20 dark:bg-background">
    <div className="container mx-auto px-4">
      <motion.div
        className="relative"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
      >
        {/* Background elements */}
        <div className="absolute inset-0 z-0 overflow-hidden opacity-10">
          <div className="absolute -left-20 top-20 h-64 w-64 rounded-full bg-primary blur-3xl"></div>
          <div className="absolute -right-20 bottom-20 h-64 w-64 rounded-full bg-primary2 blur-3xl"></div>
        </div>

        <div className="relative z-10 flex flex-col items-center justify-center px-6 py-16 md:px-12">
          <motion.h2
            className="mb-6 text-center text-4xl font-extrabold text-foreground md:text-5xl"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Ready to Transform Your <br />
            <span className="text-primary">Engineering Journey?</span>
          </motion.h2>
          <motion.p
            className="mx-auto mb-10 max-w-2xl text-center text-lg text-muted-foreground"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            Join thousands of engineering students who are already using{' '}
            {companyInfo.name}
            to accelerate their learning, prepare for placements, and build a
            successful career.
          </motion.p>
          <motion.div
            className="flex flex-col items-center justify-center gap-4 sm:flex-row"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Link
              href={ctaLinks.getStarted.href}
              className="group relative inline-flex items-center overflow-hidden rounded-full bg-primary px-8 py-4 text-lg font-bold text-white transition-all duration-300 hover:bg-primary2"
            >
              <span className="relative z-10">{ctaLinks.getStarted.name}</span>
            </Link>
            <Link
              href={ctaLinks.learnMore.href}
              className="flex items-center rounded-full border border-foreground/20 px-6 py-4 text-lg font-medium text-foreground transition-all duration-300 hover:bg-foreground/5"
            >
              {ctaLinks.learnMore.name}
              <motion.span
                animate={{ translateX: [0, 5, 0] }}
                transition={{ repeat: Infinity, duration: 1.5 }}
                className="ml-2 text-primary"
              >
                <FaArrowRight />
              </motion.span>
            </Link>
          </motion.div>
        </div>
      </motion.div>
    </div>
  </section>
);

export default CTASection;
