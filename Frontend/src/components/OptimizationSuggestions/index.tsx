/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */
'use client';

import { useState } from 'react';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  BookOpen,
  Clock,
  Code,
  Cpu,
  Database,
  Lightbulb,
  Zap,
} from 'lucide-react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

/**
 * @file index.tsx
 * @description React component for OptimizationSuggestions
 */

export interface OptimizationTip {
  id: string;
  title: string;
  description: string;
  category: 'algorithm' | 'data_structure' | 'language' | 'memory' | 'time';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  code_example?: string;
  language: string;
  impact: 'low' | 'medium' | 'high';
}

interface OptimizationSuggestionsProps {
  tips: OptimizationTip[];
  language: string;
  className?: string;
}

export default function OptimizationSuggestions({
  tips,
  language,
  className = '',
}: OptimizationSuggestionsProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Filter tips by category
  const filteredTips =
    selectedCategory === 'all'
      ? tips
      : tips.filter((tip) => tip.category === selectedCategory);

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'algorithm':
        return <Zap className="h-4 w-4" />;
      case 'data_structure':
        return <Database className="h-4 w-4" />;
      case 'language':
        return <Code className="h-4 w-4" />;
      case 'memory':
        return <Cpu className="h-4 w-4" />;
      case 'time':
        return <Clock className="h-4 w-4" />;
      default:
        return <Lightbulb className="h-4 w-4" />;
    }
  };

  // Get impact badge
  const getImpactBadge = (impact: string) => {
    const variants = {
      low: 'bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-400',
      medium:
        'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400',
      high: 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400',
    };

    return (
      <Badge
        variant="outline"
        className={cn(
          'capitalize',
          variants[impact as keyof typeof variants] || 'bg-gray-100',
        )}
      >
        {impact} impact
      </Badge>
    );
  };

  // Get difficulty badge
  const getDifficultyBadge = (difficulty: string) => {
    const variants = {
      beginner:
        'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/30 dark:text-green-400',
      intermediate:
        'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-400',
      advanced:
        'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-400',
    };

    return (
      <Badge
        variant="outline"
        className={cn(
          'capitalize',
          variants[difficulty as keyof typeof variants] || 'bg-gray-100',
        )}
      >
        {difficulty}
      </Badge>
    );
  };

  // Get category label
  const getCategoryLabel = (category: string) => {
    const labels = {
      algorithm: 'Algorithm',
      data_structure: 'Data Structure',
      language: 'Language-Specific',
      memory: 'Memory Optimization',
      time: 'Time Optimization',
    };

    return labels[category as keyof typeof labels] || category;
  };

  return (
    <div className={cn('space-y-6', className)}>
      <div className="flex items-center justify-between">
        <h3 className="flex items-center text-lg font-medium">
          <Lightbulb className="mr-2 h-5 w-5 text-yellow-500" />
          Optimization Suggestions
        </h3>

        <div className="flex flex-wrap gap-2">
          <Badge
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            className="cursor-pointer"
            onClick={() => setSelectedCategory('all')}
          >
            All
          </Badge>
          {Array.from(new Set(tips.map((tip) => tip.category))).map(
            (category) => (
              <Badge
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                className="flex cursor-pointer items-center gap-1"
                onClick={() => setSelectedCategory(category)}
              >
                {getCategoryIcon(category)}
                <span>{getCategoryLabel(category)}</span>
              </Badge>
            ),
          )}
        </div>
      </div>

      {filteredTips.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No suggestions available</CardTitle>
            <CardDescription>
              There are no optimization suggestions for the selected category.
            </CardDescription>
          </CardHeader>
        </Card>
      ) : (
        <Accordion type="single" collapsible className="space-y-4">
          {filteredTips.map((tip) => (
            <AccordionItem
              key={tip.id}
              value={tip.id}
              className="rounded-lg border bg-card px-6"
            >
              <AccordionTrigger className="py-4 hover:no-underline">
                <div className="flex flex-1 flex-col items-start gap-2 text-left sm:flex-row sm:items-center">
                  <div className="flex items-center gap-2">
                    {getCategoryIcon(tip.category)}
                    <span className="font-medium">{tip.title}</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {getImpactBadge(tip.impact)}
                    {getDifficultyBadge(tip.difficulty)}
                    <Badge variant="outline" className="capitalize">
                      {getCategoryLabel(tip.category)}
                    </Badge>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pb-4 pt-2">
                <div className="space-y-4">
                  <p className="text-muted-foreground">{tip.description}</p>

                  {tip.code_example && (
                    <div className="rounded-md bg-muted p-4">
                      <div className="mb-2 flex items-center">
                        <Code className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          Example ({tip.language})
                        </span>
                      </div>
                      <pre className="overflow-x-auto text-sm">
                        <code>{tip.code_example}</code>
                      </pre>
                    </div>
                  )}

                  <div className="flex items-center rounded-md bg-blue-50 p-3 dark:bg-blue-950/30">
                    <BookOpen className="mr-2 h-4 w-4 text-blue-500" />
                    <span className="text-sm text-blue-800 dark:text-blue-300">
                      Learn more about{' '}
                      {tip.category === 'algorithm'
                        ? 'algorithms'
                        : tip.category === 'data_structure'
                          ? 'data structures'
                          : tip.category === 'language'
                            ? `${language} optimization`
                            : tip.category === 'memory'
                              ? 'memory optimization'
                              : 'time complexity'}{' '}
                      in our learning resources.
                    </span>
                    <ArrowRight className="ml-2 h-4 w-4 text-blue-500" />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </div>
  );
}
