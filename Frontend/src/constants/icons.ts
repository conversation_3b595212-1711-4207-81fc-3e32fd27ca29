/**
 * This file contains standardized icon imports to ensure consistency across the platform
 * Always import icons from this file instead of directly from icon libraries
 */

// Import from lucide-react for primary icons
import { 
  Pencil, 
  Trash2, 
  Eye, 
  MoreHorizontal,
  Plus,
  X,
  Check,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Search,
  Settings,
  User,
  Users,
  Bell,
  LogOut,
  Menu,
  Home,
  Calendar,
  FileText,
  Filter,
  HelpCircle,
  Info,
  AlertTriangle,
  AlertCircle,
  ExternalLink,
  Download,
  Upload,
  Copy,
  Edit,
  Save,
  Loader,
  RefreshCw,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
} from 'lucide-react';

// Import from react-icons for additional icons
import { 
  RiAddLine, 
  RiRoadMapLine,
  RiDashboardLine,
  RiUserLine,
  RiSettings4Line,
  RiNotification3Line,
  RiLogoutBoxLine,
  RiMenuLine,
  RiCloseLine,
  RiSearchLine,
  RiFilterLine,
  RiInformationLine,
  RiErrorWarningLine,
  <PERSON><PERSON><PERSON><PERSON>ckLine,
  <PERSON><PERSON><PERSON>lose<PERSON>ill,
} from 'react-icons/ri';

/**
 * Standard UI Icons
 * Use these for consistent UI elements across the platform
 */
export const UIIcons = {
  // Action icons
  Edit: Pencil,
  Delete: Trash2,
  View: Eye,
  Add: Plus,
  Close: X,
  Check: Check,
  Search: Search,
  Filter: Filter,
  Settings: Settings,
  More: MoreHorizontal,
  
  // Navigation icons
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  ExternalLink,
  
  // User related
  User,
  Users,
  Notification: Bell,
  Logout: LogOut,
  
  // Layout
  Menu,
  Home,
  
  // Content
  Calendar,
  FileText,
  
  // Feedback
  Help: HelpCircle,
  Info,
  Warning: AlertTriangle,
  Error: AlertCircle,
  
  // Data
  Download,
  Upload,
  Copy,
  Save,
  Refresh: RefreshCw,
  Loading: Loader,
};

/**
 * Feature specific icons
 * Use these for specific features or sections
 */
export const FeatureIcons = {
  Roadmap: RiRoadMapLine,
  Dashboard: RiDashboardLine,
  AddItem: RiAddLine,
};

export default {
  UIIcons,
  FeatureIcons
};
