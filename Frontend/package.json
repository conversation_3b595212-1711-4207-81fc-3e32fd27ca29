{"name": "mrengin<PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "start": "next start", "lint": "next lint", "build": "prettier --config .prettierrc.json --check . && next build --no-lint", "format": "prettier --config .prettierrc.json --write .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.2.3", "@reduxjs/toolkit": "^2.5.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "@types/react-datepicker": "^7.0.0", "@types/react-select": "^5.0.1", "@types/react-syntax-highlighter": "^15.5.13", "axios": "^1.7.9", "axios-mock-adapter": "^2.1.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "dompurify": "^3.2.3", "framer-motion": "^11.18.1", "js-cookie": "^3.0.5", "lottie-react": "^2.4.1", "lucide-react": "^0.473.0", "next": "15.1.5", "next-themes": "^0.4.4", "phone": "^3.1.58", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-intersection-observer": "^9.15.0", "react-redux": "^9.2.0", "react-scroll-parallax": "^3.4.5", "react-select": "^5.10.1", "react-select-async-paginate": "^0.7.10", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^11.0.3", "react-tsparticles": "^2.12.2", "recharts": "^2.15.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "swiper": "^11.2.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tsparticles": "^2.12.0", "yup": "^1.6.1", "zustand": "^5.0.3", "zxcvbn-typescript": "^5.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@storybook/addon-essentials": "^8.5.6", "@storybook/addon-interactions": "^8.5.6", "@storybook/addon-links": "^8.5.6", "@storybook/nextjs": "^8.5.6", "@storybook/test-runner": "^0.21.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/canvas-confetti": "^1.9.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/node": "20.17.14", "@types/react": "19.0.7", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.5", "postcss": "^8", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "5.7.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}