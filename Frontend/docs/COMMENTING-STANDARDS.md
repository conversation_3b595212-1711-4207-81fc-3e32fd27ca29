# Code Commenting Standards

This document outlines the code commenting standards implemented in our frontend codebase. Following these standards ensures consistent, readable, and maintainable code.

## Table of Contents

1. [Introduction](#introduction)
2. [File Headers](#file-headers)
3. [Components](#components)
4. [Hooks](#hooks)
5. [Types and Interfaces](#types-and-interfaces)
6. [Utility Functions](#utility-functions)
7. [JSX Comments](#jsx-comments)
8. [Inline Comments](#inline-comments)
9. [Tools and Scripts](#tools-and-scripts)
10. [Examples](#examples)

## Introduction

Good code comments explain **why** code exists rather than **what** it does. The code itself should be clear enough to understand what it's doing, while comments provide context, reasoning, and additional information that isn't obvious from the code.

## File Headers

Every file should start with a header comment that describes the purpose of the file:

```tsx
/**
 * @file ComponentName.tsx
 * @description Brief description of what this file contains and its purpose
 */
```

## Components

Components should be documented with JSDoc comments that include:

1. Description of the component's purpose
2. Props documentation
3. Usage examples for complex components
4. Return value description

```tsx
/**
 * ComponentName - Brief description of the component
 *
 * Detailed description of what the component does, when to use it,
 * and any important considerations.
 *
 * @example
 * <ComponentName prop1="value" prop2={value} />
 */

/**
 * Props for the ComponentName component
 */
interface IComponentNameProps {
  /**
   * Description of prop1
   */
  prop1: string;

  /**
   * Description of prop2
   * @default false
   */
  prop2?: boolean;
}

/**
 * ComponentName component
 *
 * @param props - The component props
 * @returns The rendered component
 */
function ComponentName({
  prop1,
  prop2 = false,
}: IComponentNameProps): React.ReactElement {
  // Implementation...
}
```

## Hooks

Custom hooks should be documented with:

1. Description of the hook's purpose
2. Parameters documentation
3. Return value documentation
4. Usage examples

```tsx
/**
 * useHookName - Brief description of the hook
 *
 * Detailed description of what the hook does, when to use it,
 * and any important considerations.
 *
 * @example
 * const result = useHookName(param1, param2);
 *
 * @param param1 - Description of param1
 * @param param2 - Description of param2
 * @returns The hook result
 */
function useHookName(param1: string, param2: number): ReturnType {
  // Implementation...
}
```

## Types and Interfaces

Types and interfaces should be documented with:

1. Description of what the type/interface represents
2. Documentation for each property

```tsx
/**
 * Represents a user in the system
 */
interface IUser {
  /**
   * Unique identifier for the user
   */
  id: string;

  /**
   * User's display name
   */
  name: string;

  /**
   * User's email address
   */
  email: string;
}

/**
 * Possible states of a battle
 *
 * @remarks
 * - 'pending': Battle is created but not yet started
 * - 'active': Battle is currently in progress
 * - 'completed': Battle has finished
 */
export type BattleStatus = 'pending' | 'active' | 'completed';
```

## Utility Functions

Utility functions should be documented with:

1. Description of the function's purpose
2. Parameters documentation
3. Return value documentation
4. Examples for complex utilities

```tsx
/**
 * Merges multiple class values into a single className string
 *
 * This utility combines the functionality of clsx (for conditional classes)
 * with tailwind-merge (for handling Tailwind CSS class conflicts).
 *
 * @example
 * cn('text-red-500', 'bg-blue-500') // => 'text-red-500 bg-blue-500'
 *
 * @param inputs - Class values to merge
 * @returns Merged className string with conflicts resolved
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}
```

## JSX Comments

Use JSX comments to explain complex UI structures or conditional rendering:

```tsx
{
  /* User information section */
}
<div className="user-info">
  {/* Only show admin controls for admin users */}
  {user.role === 'admin' && <AdminControls user={user} />}
</div>;
```

## Inline Comments

Use inline comments sparingly and only when necessary to explain complex logic or non-obvious decisions:

```tsx
// This is necessary to prevent race conditions when multiple requests are in flight
const requestId = generateUniqueId();

// Using a timeout to simulate network latency for testing
setTimeout(() => {
  fetchData();
}, 500);
```

## Tools and Scripts

We've created several tools to help maintain our commenting standards:

1. **comment-analyzer.js** - Analyzes the codebase for files that need better comments
2. **add-file-headers.js** - Adds file header comments to files that don't have them

Run these scripts from the project root:

```bash
node scripts/comment-analyzer.js
node scripts/add-file-headers.js
```

## Examples

### Component Example

```tsx
/**
 * @file CommentSection.tsx
 * @description Component for displaying and managing comments on roadmaps
 */
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useAxiosGet, useAxiosPost } from '@/hooks/useAxios';
import { Comment } from '@/types';

import { CommentItem } from './CommentItem';

/**
 * Props for the CommentSection component
 */
interface ICommentSectionProps {
  /**
   * Unique identifier for the roadmap
   */
  roadmapId: string;

  /**
   * Whether the comment section is open and visible
   */
  isOpen: boolean;
}

/**
 * CommentSection - Component for displaying and managing comments on roadmaps
 *
 * This component provides a form for adding new comments and displays
 * a list of existing comments for a roadmap. It handles loading, submitting,
 * and updating comments.
 *
 * @example
 * <CommentSection roadmapId="123" isOpen={true} />
 *
 * @param props - Component props
 * @returns The rendered CommentSection component
 */
export const CommentSection: React.FC<ICommentSectionProps> = ({
  roadmapId,
  isOpen,
}) => {
  /**
   * State for storing the list of comments
   */
  const [comments, setComments] = useState<Comment[]>([]);

  // Rest of the component implementation...
};
```

### Hook Example

```tsx
/**
 * @file useDebounce.ts
 * @description Custom hook for debouncing rapidly changing values
 */
import { useEffect, useState } from 'react';

/**
 * useDebounce - Debounces a value by delaying updates
 *
 * This hook is useful for delaying the processing of a rapidly changing value,
 * such as search input, to avoid excessive operations like API calls.
 *
 * @example
 * // Basic usage with a search input
 * const [searchTerm, setSearchTerm] = useState('');
 * const debouncedSearchTerm = useDebounce(searchTerm, 500);
 *
 * @template T - The type of the value being debounced
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds
 * @returns The debounced value
 */
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set a timeout to update the debounced value after the specified delay
    const timer = setTimeout(() => setDebouncedValue(value), delay);

    // Clean up the timeout if the value or delay changes before the timeout completes
    return () => clearTimeout(timer);
  }, [value, delay]);

  return debouncedValue;
}

export default useDebounce;
```

### Utility Function Example

```tsx
/**
 * @file cn.ts
 * @description Utility for merging Tailwind CSS classes with proper conflict resolution
 */
import { ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Merges multiple class values into a single className string
 *
 * This utility combines the functionality of clsx (for conditional classes)
 * with tailwind-merge (for handling Tailwind CSS class conflicts).
 *
 * @example
 * // Basic usage
 * cn('text-red-500', 'bg-blue-500') // => 'text-red-500 bg-blue-500'
 *
 * @param inputs - Class values to merge
 * @returns Merged className string with conflicts resolved
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}
```
