# Frontend Code Standards

## Overview

This document outlines the coding standards and best practices for the frontend codebase. Following these standards ensures consistency, maintainability, and quality across the project.

## File and Folder Structure

### Project Structure

```
src/
├── app/              # Next.js app directory (pages and layouts)
├── assets/           # Static assets (images, fonts, etc.)
├── components/       # Reusable React components
├── constants/        # Application constants
├── contexts/         # React contexts
├── data/             # Static data files
├── hooks/            # Custom React hooks
├── lib/              # Third-party library configurations
├── services/         # API services and data fetching
├── store/            # State management (Redux, Zustand, etc.)
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

### Component Structure

Components should follow this structure:

```
ComponentName/
├── index.tsx                 # Main component file
├── ComponentName.module.css  # (Optional) Component-specific styles
├── ComponentName.test.tsx    # (Optional) Component tests
└── ComponentName.stories.tsx # (Optional) Storybook stories
```

## Naming Conventions

### Files and Folders

- **Component folders**: PascalCase (e.g., `<PERSON><PERSON>`, `UserProfile`)
- **Component files**: PascalCase (e.g., `Button.tsx`, `UserProfile.tsx`)
- **Hook files**: camelCase, prefixed with "use" (e.g., `useAuth.ts`, `useLocalStorage.ts`)
- **Utility files**: camelCase (e.g., `formatDate.ts`, `validation.ts`)
- **Test files**: Same name as the file being tested with `.test` or `.spec` suffix (e.g., `Button.test.tsx`)

### Variables and Functions

- **Variables**: camelCase (e.g., `userName`, `isLoading`)
- **Functions**: camelCase (e.g., `handleSubmit`, `fetchData`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_URL`, `MAX_RETRIES`)
- **React components**: PascalCase (e.g., `Button`, `UserProfile`)
- **React hooks**: camelCase, prefixed with "use" (e.g., `useAuth`, `useLocalStorage`)

### Backend Data

- Variables and props that represent data from the backend should use snake_case to match the backend naming convention (e.g., `user_id`, `created_at`)

### TypeScript Types and Interfaces

- **Interfaces**: PascalCase with "I" prefix (e.g., `IUserProps`, `IAuthState`)
- **Types**: PascalCase (e.g., `UserRole`, `ApiResponse`)
- **Enums**: PascalCase (e.g., `UserStatus`, `PaymentMethod`)

## Code Style

### General

- Use 2 spaces for indentation
- Use semicolons at the end of statements
- Use single quotes for strings
- Add trailing commas in multi-line objects and arrays
- Keep lines under 80 characters when possible

### TypeScript

- Enable strict mode in TypeScript configuration
- Avoid using `any` type when possible
- Use explicit return types for functions
- Use type inference when the type is obvious
- Use interfaces for object shapes and types for unions/intersections

### React

- Use functional components with hooks instead of class components
- Use destructuring for props and state
- Use the React.FC type sparingly (prefer explicit prop interfaces)
- Keep components small and focused on a single responsibility
- Extract complex logic into custom hooks

### CSS

- Use CSS modules for component-specific styles
- Use Tailwind CSS for utility classes
- Avoid inline styles except for dynamic values
- Use CSS variables for theme colors and values

## Documentation

### Components

- Document components using JSDoc comments
- Include a brief description of what the component does
- Document all props, including types and default values
- Include usage examples for complex components

### Hooks

- Document hooks using JSDoc comments
- Include a brief description of what the hook does
- Document all parameters and return values
- Include usage examples

### Utilities

- Document utility functions using JSDoc comments
- Include a brief description of what the function does
- Document all parameters and return values
- Include usage examples for complex functions

## Testing

- Write tests for all components and hooks
- Use React Testing Library for component tests
- Test user interactions and accessibility
- Mock API calls and external dependencies
- Aim for high test coverage of critical paths

## Performance

- Use React.memo for expensive components
- Use useMemo and useCallback for expensive calculations and callbacks
- Avoid unnecessary re-renders
- Optimize images and assets
- Use code splitting and lazy loading for large components

## Accessibility

- Use semantic HTML elements
- Include proper ARIA attributes
- Ensure keyboard navigation works
- Maintain sufficient color contrast
- Test with screen readers

## Version Control

- Write clear and descriptive commit messages
- Use feature branches for new features
- Create pull requests for code reviews
- Keep pull requests focused on a single feature or fix
- Squash commits before merging

## Code Review

- Review code for adherence to these standards
- Check for potential bugs and edge cases
- Verify that tests cover the changes
- Ensure documentation is updated
- Look for opportunities to improve performance and accessibility
