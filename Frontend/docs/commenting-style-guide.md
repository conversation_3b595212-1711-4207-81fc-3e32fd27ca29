# Code Commenting Style Guide

This guide outlines the standards for commenting code in our frontend codebase. Consistent commenting improves code readability, maintainability, and helps new developers understand the codebase more quickly.

## General Principles

1. **Be Clear and Concise**: Write comments that are easy to understand and to the point.
2. **Comment Why, Not What**: Focus on explaining why code exists rather than what it does (the code itself should be self-explanatory).
3. **Keep Comments Updated**: Outdated comments are worse than no comments at all.
4. **Use JSDoc Format**: Follow JSDoc conventions for documenting functions, components, and types.
5. **Document Edge Cases**: Explain any non-obvious edge cases or limitations.

## File Headers

Every file should start with a header comment that describes the purpose of the file:

```tsx
/**
 * @file ComponentName.tsx
 * @description Brief description of what this file contains and its purpose
 * <AUTHOR> Original author's name
 */
```

## React Components

Components should be documented with JSDoc comments that include:

1. Description of the component's purpose
2. Props documentation
3. Usage examples for complex components
4. Return value description

```tsx
/**
 * ComponentName - Brief description of the component
 *
 * Detailed description of what the component does, when to use it,
 * and any important considerations.
 *
 * @example
 * // Basic usage
 * <ComponentName prop1="value" prop2={value} />
 *
 * // With optional props
 * <ComponentName
 *   prop1="value"
 *   optionalProp="value"
 * />
 */

/**
 * Props for the ComponentName component
 */
interface IComponentNameProps {
  /**
   * Description of prop1
   */
  prop1: string;

  /**
   * Description of prop2
   */
  prop2: number;

  /**
   * Description of optionalProp
   * @default false
   */
  optionalProp?: boolean;
}

/**
 * ComponentName component
 *
 * @param props - The component props
 * @returns The rendered component
 */
function ComponentName({
  prop1,
  prop2,
  optionalProp = false,
}: IComponentNameProps): React.ReactElement {
  // Implementation...
}
```

## React Hooks

Custom hooks should be documented with:

1. Description of the hook's purpose
2. Parameters documentation
3. Return value documentation
4. Usage examples

```tsx
/**
 * useHookName - Brief description of the hook
 *
 * Detailed description of what the hook does, when to use it,
 * and any important considerations.
 *
 * @example
 * // Basic usage
 * const result = useHookName(param1, param2);
 *
 * // With optional parameters
 * const result = useHookName(param1, param2, { optionalParam: value });
 *
 * @param param1 - Description of param1
 * @param param2 - Description of param2
 * @param options - Optional configuration
 * @returns The hook result object containing { data, loading, error }
 */
function useHookName(
  param1: string,
  param2: number,
  options?: IUseHookNameOptions,
): IUseHookNameResult {
  // Implementation...
}
```

## Functions and Methods

Functions and methods should be documented with:

1. Description of the function's purpose
2. Parameters documentation
3. Return value documentation
4. Any side effects or exceptions

```tsx
/**
 * Brief description of what the function does
 *
 * Detailed description if needed
 *
 * @param param1 - Description of param1
 * @param param2 - Description of param2
 * @returns Description of the return value
 * @throws Description of potential errors that might be thrown
 */
function functionName(param1: string, param2: number): ReturnType {
  // Implementation...
}
```

## TypeScript Interfaces and Types

Interfaces and types should be documented with:

1. Description of what the interface/type represents
2. Documentation for each property

```tsx
/**
 * Represents a user in the system
 */
interface IUser {
  /**
   * Unique identifier for the user
   */
  id: string;

  /**
   * User's display name
   */
  name: string;

  /**
   * User's email address
   */
  email: string;

  /**
   * User's role in the system
   * @default "user"
   */
  role?: UserRole;
}
```

## Constants and Enums

Constants and enums should be documented with:

1. Description of what the constant/enum represents
2. Documentation for each value (for enums)

```tsx
/**
 * Available user roles in the system
 */
enum UserRole {
  /**
   * Regular user with basic permissions
   */
  USER = 'user',

  /**
   * Administrator with full system access
   */
  ADMIN = 'admin',

  /**
   * Moderator with content management permissions
   */
  MODERATOR = 'moderator',
}

/**
 * Maximum number of login attempts before account lockout
 */
const MAX_LOGIN_ATTEMPTS = 5;
```

## Inline Comments

Use inline comments sparingly and only when necessary to explain complex logic or non-obvious decisions:

```tsx
// This is necessary to prevent race conditions when multiple requests are in flight
const requestId = generateUniqueId();

// Using a timeout to simulate network latency for testing
setTimeout(() => {
  fetchData();
}, 500);
```

## JSX Comments

Use JSX comments to explain complex UI structures or conditional rendering:

```tsx
{
  /* User information section */
}
<div className="user-info">
  {/* Only show admin controls for admin users */}
  {user.role === 'admin' && <AdminControls user={user} />}

  {/* Show different content based on subscription status */}
  {user.isSubscribed ? <SubscriberContent /> : <FreeTierContent />}
</div>;
```

## TODO Comments

Use TODO comments to mark areas that need future improvement, but follow these guidelines:

1. Include a brief description of what needs to be done
2. If possible, include a ticket/issue number for reference
3. Consider adding a date or deadline if applicable

```tsx
// TODO: Refactor this to use the new API endpoint (TICKET-123)
// TODO: (2023-06-30) Improve performance of this calculation
// TODO: Add proper error handling here
```

## Commenting Context Providers

Context providers should be documented with:

1. Description of what the context provides
2. Documentation for the context value
3. Usage examples

```tsx
/**
 * AuthContext - Provides authentication state and functions
 *
 * This context provides user authentication state and functions to login,
 * logout, and manage the user session throughout the application.
 *
 * @example
 * // To consume the context
 * const { user, login, logout } = useAuth();
 *
 * // To check if user is authenticated
 * if (user) {
 *   // User is logged in
 * }
 */

/**
 * Authentication context value
 */
interface IAuthContextValue {
  /**
   * Current authenticated user or null if not authenticated
   */
  user: IUser | null;

  /**
   * Function to log in a user
   * @param credentials - User credentials
   * @returns Promise that resolves when login is complete
   */
  login: (credentials: ILoginCredentials) => Promise<void>;

  /**
   * Function to log out the current user
   * @returns Promise that resolves when logout is complete
   */
  logout: () => Promise<void>;

  /**
   * Whether authentication is currently in progress
   */
  isLoading: boolean;

  /**
   * Any authentication error that occurred
   */
  error: Error | null;
}
```

## Commenting API Services

API service files should be documented with:

1. Description of the service's purpose
2. Documentation for each API function
3. Parameters and return types

```tsx
/**
 * @file userService.ts
 * @description Service for user-related API operations
 */

/**
 * Fetches a user by ID
 *
 * @param userId - The ID of the user to fetch
 * @returns Promise resolving to the user data
 * @throws Error if the user is not found or request fails
 */
export async function getUserById(userId: string): Promise<IUser> {
  // Implementation...
}

/**
 * Updates a user's profile information
 *
 * @param userId - The ID of the user to update
 * @param data - The user data to update
 * @returns Promise resolving to the updated user data
 * @throws Error if the update fails
 */
export async function updateUserProfile(
  userId: string,
  data: IUserUpdateData,
): Promise<IUser> {
  // Implementation...
}
```

## Commenting Utility Functions

Utility functions should be documented with:

1. Description of the utility's purpose
2. Documentation for parameters and return values
3. Examples for complex utilities

```tsx
/**
 * Formats a date string into a human-readable format
 *
 * @example
 * // Returns "January 1, 2023"
 * formatDate("2023-01-01T00:00:00Z", "long");
 *
 * // Returns "Jan 1, 2023"
 * formatDate("2023-01-01T00:00:00Z", "medium");
 *
 * // Returns "01/01/2023"
 * formatDate("2023-01-01T00:00:00Z", "short");
 *
 * @param dateString - ISO date string to format
 * @param format - Format style (long, medium, short)
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string,
  format: 'long' | 'medium' | 'short' = 'medium',
): string {
  // Implementation...
}
```

## Best Practices

1. **Keep Comments Updated**: When you change code, update the related comments.
2. **Don't Comment Obvious Code**: Avoid comments that just repeat what the code clearly does.
3. **Use Proper Grammar**: Write comments with proper spelling, grammar, and punctuation.
4. **Be Consistent**: Follow the same commenting style throughout the codebase.
5. **Document Workarounds**: If you're implementing a workaround or hack, explain why it's necessary.
6. **Explain Complex Algorithms**: For complex algorithms, explain the approach and any references.
7. **Document External Dependencies**: When using external libraries in non-obvious ways, add explanatory comments.
