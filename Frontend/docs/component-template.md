# Component Documentation Template

## Overview

This document provides a template for documenting React components in the codebase. All components should follow this documentation pattern to ensure consistency and maintainability.

## Component Documentation Template

```tsx
/**
 * ComponentName - Brief description of the component
 *
 * Detailed description of what the component does, when to use it,
 * and any important considerations.
 *
 * @example
 * // Basic usage
 * <ComponentName prop1="value" prop2={value} />
 *
 * // With optional props
 * <ComponentName
 *   prop1="value"
 *   optionalProp="value"
 * />
 */
import React from 'react';

/**
 * Props for the ComponentName component
 */
interface IComponentNameProps {
  /**
   * Description of prop1
   */
  prop1: string;

  /**
   * Description of prop2
   */
  prop2: number;

  /**
   * Description of optionalProp
   */
  optionalProp?: boolean;

  /**
   * Description of children
   */
  children?: React.ReactNode;
}

/**
 * ComponentName component
 *
 * @param props - The component props
 * @returns The rendered component
 */
function ComponentName({
  prop1,
  prop2,
  optionalProp = false,
  children,
}: IComponentNameProps): React.ReactElement {
  // Component implementation

  return <div>{/* Component JSX */}</div>;
}

export default ComponentName;
```

## File Structure

Components should follow this file structure:

```
ComponentName/
├── index.tsx       # Main component file
├── ComponentName.module.css  # (Optional) Component-specific styles
├── ComponentName.test.tsx    # (Optional) Component tests
└── ComponentName.stories.tsx # (Optional) Storybook stories
```

## Naming Conventions

- Component folders should use PascalCase (e.g., `Button`, `UserProfile`)
- Component files should use PascalCase (e.g., `Button.tsx`, `UserProfile.tsx`)
- Component props interfaces should use PascalCase with an "I" prefix (e.g., `IButtonProps`)
- CSS module files should use PascalCase with `.module.css` suffix (e.g., `Button.module.css`)

## Props Naming

- Props should use camelCase (e.g., `onClick`, `isDisabled`)
- Props that represent data from the backend should use snake_case to match the backend naming convention (e.g., `user_id`, `created_at`)
