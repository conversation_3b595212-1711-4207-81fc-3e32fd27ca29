---
trigger: always_on
---

1. use the css variables that we have assigned in the tailwind file and avoid using dark: prefix everywhere
2. Review similar files before writing any code like if you are creating a ui then review the files for eample login form or details form to understand existing format and sturcture like if you want to creat a route in be review the user routes file to udnerstand the things
3. Always use function keywords for the compoents in fe
4. Always use class based structure in the backend
5. Follow typscript strictly and avoid giving any type
6. Add // TODO: for anything that is static or that need to modified in future
7. use ComponentName/index.tsx structure for the naming
8. use camel case for variable names assignment
9. use underscore pattern for defining any props
10. use - dash naming for the routes
11. Use I prefix for interfaces
12. Use Image tag from next.js
13. Utilize common components from shadcn or from compoents/ui directory
14. Add types and interfaces to the dedicated types/index files in necessary directories
15. Use the hooks like useAxiosGet from useAxios file to call the apis in FE
