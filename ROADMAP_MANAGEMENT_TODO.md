# Roadmap Management Module - Implementation Tasks

## Overview
This document outlines the tasks required to implement the Roadmap Management module with the following hierarchy:
- Roadmap
  - Main Concepts
    - Subjects
      - Topics
        - Articles (linked to topics)

## Database Schema Tasks

### 1. Create Database Models
- [ ] Create `Roadmap` model with fields:
  - id (UUID)
  - title
  - description
  - status (active/inactive)
  - created_at
  - updated_at

- [ ] Create `MainConcept` model with fields:
  - id (UUID)
  - title
  - description
  - order (for sorting)
  - roadmap_id (foreign key)
  - created_at
  - updated_at

- [ ] Create `Subject` model with fields:
  - id (UUID)
  - title
  - description
  - order (for sorting)
  - main_concept_id (foreign key)
  - created_at
  - updated_at

- [ ] Create `Topic` model with fields:
  - id (UUID)
  - title
  - description
  - order (for sorting)
  - subject_id (foreign key)
  - created_at
  - updated_at

- [ ] Create `TopicArticle` model for linking topics to articles:
  - id (UUID)
  - topic_id (foreign key)
  - article_id (foreign key)
  - is_primary (boolean - to mark which article is the primary one for the topic)
  - order (for sorting)
  - created_at
  - updated_at

### 2. Create Database Migrations
- [ ] Write migration scripts for all the above models
- [ ] Add appropriate indexes for performance
- [ ] Set up foreign key constraints and cascading deletes/updates

## Backend Tasks

### 1. Create Repositories
- [ ] Create `RoadmapRepository` with methods:
  - [ ] `createRoadmap(data)`
  - [ ] `updateRoadmap(id, data)`
  - [ ] `deleteRoadmap(id)`
  - [ ] `getRoadmapById(id)`
  - [ ] `listRoadmaps(params)` - with pagination, search, filters
  - [ ] `getRoadmapWithHierarchy(id)` - to get full roadmap structure

- [ ] Create `MainConceptRepository` with methods:
  - [ ] `createMainConcept(data)`
  - [ ] `updateMainConcept(id, data)`
  - [ ] `deleteMainConcept(id)`
  - [ ] `getMainConceptById(id)`
  - [ ] `listMainConceptsByRoadmapId(roadmapId, params)`
  - [ ] `reorderMainConcepts(roadmapId, orderedIds)`

- [ ] Create `SubjectRepository` with methods:
  - [ ] `createSubject(data)`
  - [ ] `updateSubject(id, data)`
  - [ ] `deleteSubject(id)`
  - [ ] `getSubjectById(id)`
  - [ ] `listSubjectsByMainConceptId(mainConceptId, params)`
  - [ ] `reorderSubjects(mainConceptId, orderedIds)`

- [ ] Create `TopicRepository` with methods:
  - [ ] `createTopic(data)`
  - [ ] `updateTopic(id, data)`
  - [ ] `deleteTopic(id)`
  - [ ] `getTopicById(id)`
  - [ ] `listTopicsBySubjectId(subjectId, params)`
  - [ ] `reorderTopics(subjectId, orderedIds)`

- [ ] Create `TopicArticleRepository` with methods:
  - [ ] `linkArticleToTopic(topicId, articleId, isPrimary, order)`
  - [ ] `unlinkArticleFromTopic(topicId, articleId)`
  - [ ] `setPrimaryArticle(topicId, articleId)`
  - [ ] `listArticlesByTopicId(topicId)`
  - [ ] `reorderTopicArticles(topicId, orderedIds)`

### 2. Create Services
- [ ] Create `RoadmapService` to handle business logic for roadmaps
- [ ] Create `MainConceptService` to handle business logic for main concepts
- [ ] Create `SubjectService` to handle business logic for subjects
- [ ] Create `TopicService` to handle business logic for topics
- [ ] Create `TopicArticleService` to handle business logic for topic-article relationships

### 3. Create Controllers
- [ ] Create `RoadmapController` with endpoints:
  - [ ] `POST /api/admin/roadmaps` - Create roadmap
  - [ ] `GET /api/admin/roadmaps` - List roadmaps (with pagination, search, filters)
  - [ ] `GET /api/admin/roadmaps/:id` - Get roadmap details
  - [ ] `PUT /api/admin/roadmaps/:id` - Update roadmap
  - [ ] `DELETE /api/admin/roadmaps/:id` - Delete roadmap
  - [ ] `GET /api/admin/roadmaps/:id/hierarchy` - Get full roadmap hierarchy

- [ ] Create `MainConceptController` with endpoints:
  - [ ] `POST /api/admin/roadmaps/:roadmapId/main-concepts` - Create main concept
  - [ ] `GET /api/admin/roadmaps/:roadmapId/main-concepts` - List main concepts
  - [ ] `GET /api/admin/main-concepts/:id` - Get main concept details
  - [ ] `PUT /api/admin/main-concepts/:id` - Update main concept
  - [ ] `DELETE /api/admin/main-concepts/:id` - Delete main concept
  - [ ] `POST /api/admin/roadmaps/:roadmapId/main-concepts/reorder` - Reorder main concepts

- [ ] Create `SubjectController` with endpoints:
  - [ ] `POST /api/admin/main-concepts/:mainConceptId/subjects` - Create subject
  - [ ] `GET /api/admin/main-concepts/:mainConceptId/subjects` - List subjects
  - [ ] `GET /api/admin/subjects/:id` - Get subject details
  - [ ] `PUT /api/admin/subjects/:id` - Update subject
  - [ ] `DELETE /api/admin/subjects/:id` - Delete subject
  - [ ] `POST /api/admin/main-concepts/:mainConceptId/subjects/reorder` - Reorder subjects

- [ ] Create `TopicController` with endpoints:
  - [ ] `POST /api/admin/subjects/:subjectId/topics` - Create topic
  - [ ] `GET /api/admin/subjects/:subjectId/topics` - List topics
  - [ ] `GET /api/admin/topics/:id` - Get topic details
  - [ ] `PUT /api/admin/topics/:id` - Update topic
  - [ ] `DELETE /api/admin/topics/:id` - Delete topic
  - [ ] `POST /api/admin/subjects/:subjectId/topics/reorder` - Reorder topics

- [ ] Create `TopicArticleController` with endpoints:
  - [ ] `POST /api/admin/topics/:topicId/articles` - Link article to topic
  - [ ] `GET /api/admin/topics/:topicId/articles` - List articles for topic
  - [ ] `DELETE /api/admin/topics/:topicId/articles/:articleId` - Unlink article from topic
  - [ ] `PUT /api/admin/topics/:topicId/articles/:articleId/primary` - Set primary article
  - [ ] `POST /api/admin/topics/:topicId/articles/reorder` - Reorder topic articles

### 4. Create Routes
- [ ] Add all controller routes to the admin routes file
- [ ] Set up proper middleware for authentication and authorization
- [ ] Add validation middleware for request data

### 5. Implement Validation
- [ ] Create validation schemas for all endpoints
- [ ] Implement request validation middleware

## Frontend Tasks

### 1. Create State Management
- [ ] Create roadmap slice in Redux store
- [ ] Implement API service functions for all backend endpoints
- [ ] Create custom hooks for roadmap management operations

### 2. Create Roadmap List Page
- [ ] Create roadmap listing page with:
  - [ ] Search functionality
  - [ ] Pagination
  - [ ] Filters (status, date created, etc.)
  - [ ] Action buttons (view, edit, delete)
  - [ ] "Create New Roadmap" button
- [ ] Implement roadmap deletion confirmation modal

### 3. Create Roadmap Form Components
- [ ] Create reusable form components for roadmap data
- [ ] Implement form validation
- [ ] Create success/error notifications

### 4. Create Roadmap Creation Page
- [ ] Create "New Roadmap" page with form
- [ ] Implement form submission and API integration
- [ ] Add validation and error handling

### 5. Create Roadmap Edit Page
- [ ] Create "Edit Roadmap" page with pre-filled form
- [ ] Implement form submission and API integration
- [ ] Add validation and error handling

### 6. Create Roadmap Detail/View Page
- [ ] Create roadmap detail page showing full hierarchy
- [ ] Implement collapsible sections for main concepts, subjects, and topics
- [ ] Add action buttons for each level (add, edit, delete)

### 7. Create Main Concept Management
- [ ] Create main concept list component within roadmap detail page
- [ ] Implement main concept creation modal/form
- [ ] Implement main concept editing functionality
- [ ] Implement main concept deletion with confirmation
- [ ] Add drag-and-drop reordering of main concepts

### 8. Create Subject Management
- [ ] Create subject list component within main concept section
- [ ] Implement subject creation modal/form
- [ ] Implement subject editing functionality
- [ ] Implement subject deletion with confirmation
- [ ] Add drag-and-drop reordering of subjects

### 9. Create Topic Management
- [ ] Create topic list component within subject section
- [ ] Implement topic creation modal/form
- [ ] Implement topic editing functionality
- [ ] Implement topic deletion with confirmation
- [ ] Add drag-and-drop reordering of topics

### 10. Create Topic-Article Management
- [ ] Create article selection component for topics
- [ ] Implement article search and filtering
- [ ] Create functionality to link/unlink articles to/from topics
- [ ] Implement primary article selection
- [ ] Add drag-and-drop reordering of articles

### 11. Implement UI Components
- [ ] Create reusable card components for each entity type
- [ ] Implement collapsible tree view for the hierarchy
- [ ] Create drag-and-drop interface for reordering
- [ ] Design and implement responsive layouts for all pages

### 12. Add Loading States and Error Handling
- [ ] Add loading indicators for all async operations
- [ ] Implement error handling and user feedback
- [ ] Add empty states for lists with no data

## Testing Tasks

### 1. Backend Tests
- [ ] Write unit tests for repositories
- [ ] Write unit tests for services
- [ ] Write integration tests for controllers
- [ ] Write end-to-end API tests

### 2. Frontend Tests
- [ ] Write unit tests for Redux slices and reducers
- [ ] Write unit tests for API service functions
- [ ] Write component tests for form components
- [ ] Write integration tests for pages

## Documentation Tasks

### 1. API Documentation
- [ ] Document all API endpoints
- [ ] Create Swagger/OpenAPI specification
- [ ] Add example requests and responses

### 2. Frontend Documentation
- [ ] Document component structure
- [ ] Document state management approach
- [ ] Add JSDoc comments to key functions

## Deployment Tasks
- [ ] Update database migration scripts
- [ ] Update deployment pipeline
- [ ] Configure feature flags if needed

## Additional Considerations
- [ ] Implement proper error logging
- [ ] Add analytics tracking for user interactions
- [ ] Consider performance optimizations for large roadmaps
- [ ] Implement caching strategies for frequently accessed data
