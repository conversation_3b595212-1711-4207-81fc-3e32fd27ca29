1. -- 1. Function to get user roles
CREATE OR R<PERSON>LACE FUNCTION public.get_user_roles(user_id UUID)
RETURNS TABLE (role_name TEXT, role_type TEXT) AS $$
BEGIN
  RETURN QUERY 
  SELECT r.name, r.type::TEXT
  FROM user_roles ur
  JOIN roles r ON ur.role_id = r.id
  WHERE ur.user_id = user_id::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Function to check if user has specific role
CREATE OR REPLACE FUNCTION public.has_role(user_id UUID, role_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  has_role BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM user_roles ur
    JOIN roles r ON ur.role_id = r.id
    WHERE ur.user_id = user_id::TEXT AND r.name = role_name
  ) INTO has_role;
  RETURN has_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Function to get user roles as J<PERSON><PERSON> array for <PERSON>W<PERSON> claims
CREATE OR REPLACE FUNCTION public.get_user_role_claims(uid uuid)
RETURNS jsonb
LANGUAGE plpgsql SECURITY DEFINER SET search_path = public
AS $$
DECLARE
  result jsonb;
BEGIN
  -- Get user roles as JSON array
  SELECT
    COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'role', r.name,
          'type', r.type
        )
      ),
      '[]'::jsonb
    ) INTO result
  FROM user_roles ur
  JOIN roles r ON ur.role_id = r.id
  WHERE ur.user_id = uid::TEXT;
  
  RETURN result;
END;
$$;