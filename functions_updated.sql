-- Updated function based on the working example
CREATE OR REPLACE FUNCTION public.get_user_role_claims(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_roles jsonb;
BEGIN
  -- Get user roles as JSON array
  SELECT
    COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'role', r.name,
          'type', r.type
        )
      ),
      '[]'::jsonb
    ) INTO user_roles
  FROM user_roles ur
  JOIN roles r ON ur.role_id = r.id
  WHERE ur.user_id = (event->>'user_id')::TEXT;
  
  -- Set the roles in the JWT claims
  RETURN jsonb_set(
    event,
    '{claims,roles}',
    user_roles
  );
END;
$$;
